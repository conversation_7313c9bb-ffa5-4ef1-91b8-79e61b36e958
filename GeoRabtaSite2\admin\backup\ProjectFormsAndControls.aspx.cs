using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

namespace GeoRabtaSite.admin
{
	/// <summary>
	/// Summary description for ProjectFormsAndControls.
	/// </summary>
	public class ProjectFormsAndControls : System.Web.UI.Page
	{
		protected System.Web.UI.WebControls.Label Label1;
		protected System.Web.UI.WebControls.LinkButton lnkProjects;
		protected System.Web.UI.WebControls.LinkButton pnlWebForms;
		protected System.Web.UI.WebControls.LinkButton pnlContols;
		protected System.Web.UI.WebControls.Panel pnlProjects;
		protected System.Web.UI.WebControls.Panel Panel2;
		protected System.Web.UI.WebControls.Button cmdAddNewProject;
		protected System.Web.UI.WebControls.Button cmdSaveProject;
		protected System.Web.UI.WebControls.Button cmdDeleteProject;
		protected System.Web.UI.WebControls.Label lblProjectID;
		protected eWorld.UI.CollapsablePanel CollapsablePanel1;
		protected System.Web.UI.WebControls.CheckBox chkIsActive;
		protected System.Web.UI.WebControls.TextBox txtProjectDescription;
		protected eWorld.UI.CalendarPopup dtpProjectDate;
		protected System.Web.UI.WebControls.TextBox txtProjectName;
		protected System.Web.UI.WebControls.TextBox txtDatabaseName;
		protected System.Web.UI.WebControls.DataGrid dgPro;
		protected System.Web.UI.WebControls.Button Button1;
		protected System.Web.UI.WebControls.Button cmdCancelProject;
		protected System.Web.UI.WebControls.Panel Panel3;
	
		private void Page_Load(object sender, System.EventArgs e)
		{
			// Put user code to initialize the page here
			// btnDelete.Attributes.Add("onclick", "if(confirm('Are you sure to delete?')){}else{return false});
			cmdDeleteProject.Attributes.Add("onclick","if(confirm('Are you sure to delete this record?')){return true}else{return false}");
			UpdateGrid();
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.cmdAddNewProject.Click += new System.EventHandler(this.cmdAddNewProject_Click);
			this.cmdSaveProject.Click += new System.EventHandler(this.cmdSaveProject_Click);
			this.cmdDeleteProject.Click += new System.EventHandler(this.cmdDeleteProject_Click);
			this.cmdCancelProject.Click += new System.EventHandler(this.cmdCancelProject_Click);
			this.dgPro.EditCommand += new System.Web.UI.WebControls.DataGridCommandEventHandler(this.dgPro_EditCommand);
			this.dgPro.DeleteCommand += new System.Web.UI.WebControls.DataGridCommandEventHandler(this.dgPro_DeleteCommand);
			this.dgPro.SelectedIndexChanged += new System.EventHandler(this.dgPro_SelectedIndexChanged);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		private void UpdateGrid()
		{
			try
			{
				SqlConnection Sconn = new SqlConnection(Connection.SecurityConnectionString);
				Sconn.Open();
				SqlCommand cmd=new SqlCommand("Select * from t_Projects Order by ProjectName",Sconn);
				SqlDataReader dr=cmd.ExecuteReader();
				dgPro.DataSource=dr;
				dgPro.DataBind();
			}
			catch
			{

			}
		}
		private void ClearForm()
		{
			lblProjectID.Text="";
			txtProjectName.Text="";
			txtProjectDescription.Text="";
			txtDatabaseName.Text="";
			chkIsActive.Checked=false;
			cmdSaveProject.Text="Save";
			cmdSaveProject.Enabled=true;
			cmdDeleteProject.Enabled=false;
			cmdCancelProject.Enabled=false;
		}
		private void cmdAddNewProject_Click(object sender, System.EventArgs e)
		{
			ClearForm();
		}

		private void cmdSaveProject_Click(object sender, System.EventArgs e)
		{
			if (cmdSaveProject.Text=="Save")
			{
				try
				{
					SqlConnection Sconn = new SqlConnection(Connection.SecurityConnectionString);
					Sconn.Open();
					SqlCommand cmd=new SqlCommand("insert into t_projects(projectName,projectDate,isActive,databaseName,description) values(@p_projectName,@p_projectDate,@p_isActive,@p_databaseName,@p_description)",Sconn);
					SqlParameter p_projectName=new SqlParameter("@p_projectName",SqlDbType.VarChar,500);    p_projectName.Value=txtProjectName.Text.Trim();
					SqlParameter p_projectDate=new SqlParameter("@p_projectDate",SqlDbType.DateTime);       p_projectDate.Value=dtpProjectDate.SelectedDate;
					SqlParameter p_isActive=new SqlParameter("@p_isActive",SqlDbType.TinyInt);              
					if (chkIsActive.Checked)
					{
						p_isActive.Value=1;
					}
					else
					{
						p_isActive.Value=0;
					}
					SqlParameter p_databaseName=new SqlParameter("@p_databaseName",SqlDbType.VarChar,500);  p_databaseName.Value=txtDatabaseName.Text.Trim();
					SqlParameter p_description=new SqlParameter("@p_description",SqlDbType.VarChar,5000);   p_description.Value=txtProjectDescription.Text.Trim();
					cmd.Parameters.Add(p_databaseName);
					cmd.Parameters.Add(p_description);
					cmd.Parameters.Add(p_isActive);
					cmd.Parameters.Add(p_projectDate);
					cmd.Parameters.Add(p_projectName);
					cmd.ExecuteNonQuery();
					Sconn.Close();
					ClearForm();
					UpdateGrid();

				}
				catch (Exception ex)
				{
					string s=ex.Message;
				}

			}
			else if (cmdSaveProject.Text=="Update")
			{
				try
				{
					SqlConnection Sconn = new SqlConnection(Connection.SecurityConnectionString);
					Sconn.Open();
					//SqlCommand cmd=new SqlCommand("insert into t_projects(projectName,projectDate,isActive,databaseName,description) values(@p_projectName,@p_projectDate,@p_isActive,@p_databaseName,@p_description)",Sconn);
					SqlCommand cmd=new SqlCommand("update t_projects set projectName=@p_projectName,projectDate=@p_projectDate,isActive=@p_isActive,databaseName=@p_databaseName,description=@p_description where projectID="+lblProjectID.Text,Sconn);
					SqlParameter p_projectName=new SqlParameter("@p_projectName",SqlDbType.VarChar,500);    p_projectName.Value=txtProjectName.Text.Trim();
					SqlParameter p_projectDate=new SqlParameter("@p_projectDate",SqlDbType.DateTime);       p_projectDate.Value=dtpProjectDate.SelectedDate;
					SqlParameter p_isActive=new SqlParameter("@p_isActive",SqlDbType.TinyInt);              
					if (chkIsActive.Checked)
					{
						p_isActive.Value=1;
					}
					else
					{
						p_isActive.Value=0;
					}
					SqlParameter p_databaseName=new SqlParameter("@p_databaseName",SqlDbType.VarChar,500);  p_databaseName.Value=txtDatabaseName.Text.Trim();
					SqlParameter p_description=new SqlParameter("@p_description",SqlDbType.VarChar,5000);   p_description.Value=txtProjectDescription.Text.Trim();
					cmd.Parameters.Add(p_databaseName);
					cmd.Parameters.Add(p_description);
					cmd.Parameters.Add(p_isActive);
					cmd.Parameters.Add(p_projectDate);
					cmd.Parameters.Add(p_projectName);
					cmd.ExecuteNonQuery();
					Sconn.Close();
					ClearForm();
					UpdateGrid();
				}
				catch (Exception ex)
				{
					string s=ex.Message;
				}
			}
		}

		private void dgPro_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			if (dgPro.SelectedIndex>-1 )
			{
				pnlWebForms.Enabled=true;
			}
		}

		private void dgPro_EditCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{
			SqlConnection cnn=new SqlConnection(Connection.SecurityConnectionString);
			cnn.Open();
			string projectID=dgPro.DataKeys[e.Item.ItemIndex].ToString();
			lblProjectID.Text=projectID;
			SqlCommand cmd=new SqlCommand("select * from t_projects where projectid=" + projectID,cnn);
			SqlDataReader dr=cmd.ExecuteReader();
			dr.Read();
			if (dr.HasRows)
			{
				txtProjectDescription.Text=dr["description"].ToString();
				txtDatabaseName.Text=dr["databasename"].ToString();
				txtProjectName.Text=dr["projectName"].ToString();
				dtpProjectDate.SelectedDate=Convert.ToDateTime(dr["ProjectDate"].ToString());
				chkIsActive.Checked=(dr["isActive"].ToString()=="1");
				dr.Close();			
				cmdSaveProject.Text="Update";
				cmdDeleteProject.Enabled=false;
				cmdCancelProject.Enabled=true;
				
			}
			else
			{
				ClearForm();
				dr.Close();
			}
		}

		private void cmdCancelProject_Click(object sender, System.EventArgs e)
		{
			ClearForm();
			cmdSaveProject.Text="Save";
			cmdDeleteProject.Enabled=false;
			cmdCancelProject.Enabled=false;
		}

		private void btnDelete_Click(object sender, System.EventArgs e)
		{
		
		}

		private void dgPro_DeleteCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{
			SqlConnection cnn=new SqlConnection(Connection.SecurityConnectionString);
			cnn.Open();
			string projectID=dgPro.DataKeys[e.Item.ItemIndex].ToString();
			lblProjectID.Text=projectID;
			SqlCommand cmd=new SqlCommand("select * from t_projects where projectid=" + projectID,cnn);
			SqlDataReader dr=cmd.ExecuteReader();
			dr.Read();
			if (dr.HasRows)
			{
				txtProjectDescription.Text=dr["description"].ToString();
				txtDatabaseName.Text=dr["databasename"].ToString();
				txtProjectName.Text=dr["projectName"].ToString();
				dtpProjectDate.SelectedDate=Convert.ToDateTime(dr["ProjectDate"].ToString());
				chkIsActive.Checked=(dr["isActive"].ToString()=="1");
				dr.Close();			
				cmdSaveProject.Text="Save";
				cmdSaveProject.Enabled=false;
				cmdDeleteProject.Enabled=true;
				cmdCancelProject.Enabled=true;
				
			}
			else
			{
				ClearForm();
				dr.Close();
			}
		
		}

		private void cmdDeleteProject_Click(object sender, System.EventArgs e)
		{
			try
			{
				SqlConnection cnn=new SqlConnection(Connection.SecurityConnectionString);
				cnn.Open();
				SqlCommand cmd=new SqlCommand("delete from t_projects where projectID="+lblProjectID.Text,cnn);
				cmd.ExecuteNonQuery();
				UpdateGrid();
				ClearForm();
			}
			catch (Exception ex)
			{
				string s=ex.Message;
			}


		}


	}
}
