using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;
using System.IO;
using System.DirectoryServices;
using System.Web.Mail;
namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for EmployeInfo.
	/// </summary>
	public class NewEmployeInfo : System.Web.UI.Page
	{
		protected System.Web.UI.WebControls.Label lblExtension;
		protected System.Web.UI.WebControls.Label lblDepartment;
		protected System.Web.UI.WebControls.Label lblDesignation;
		protected System.Web.UI.WebControls.Label lblDob;
		protected System.Web.UI.WebControls.Label lblDoJ;
		protected System.Web.UI.WebControls.Label lblCity;
		protected System.Web.UI.WebControls.Label lblEmailPersonal;
		protected System.Web.UI.WebControls.TextBox txtExtension;
		protected System.Web.UI.WebControls.TextBox txtDepartment;
		protected System.Web.UI.WebControls.TextBox txtDesignation;
		protected System.Web.UI.WebControls.TextBox txtDOB;
		protected System.Web.UI.WebControls.TextBox txtDOJ;
		protected System.Web.UI.WebControls.TextBox txtEmailComp;
		protected System.Web.UI.WebControls.TextBox txtCity;
		protected System.Web.UI.WebControls.TextBox txtEmailPersonal;
		protected System.Web.UI.WebControls.TextBox TextBox25;
		protected System.Web.UI.WebControls.TextBox TextBox26;
		protected System.Web.UI.WebControls.TextBox TextBox27;
		protected System.Web.UI.WebControls.TextBox TextBox28;
		protected System.Web.UI.WebControls.TextBox TextBox29;
		protected System.Web.UI.WebControls.TextBox TextBox30;
		SqlConnection con;
		public string filename="";
		public string path="";
		//public static int FamilyId=0;
		//public static int EducationId=0;
		int WebFormID=32;
		int ProjectID=2;
		protected System.Web.UI.WebControls.RangeValidator RangeValidator1;
		protected Random RandomNumber = new Random();
		protected System.Web.UI.WebControls.Panel pnlPersonal;
		protected System.Web.UI.WebControls.Panel row3;
		protected System.Web.UI.WebControls.Panel row2;
		protected System.Web.UI.WebControls.Panel row4;
		protected System.Web.UI.WebControls.ImageButton ImageButton1;
		protected System.Web.UI.WebControls.ImageButton ImageButton2;
		protected System.Web.UI.WebControls.ImageButton ImageButton3;
		protected System.Web.UI.WebControls.ImageButton ImageButton4;
		protected System.Web.UI.WebControls.ImageButton ImageButton5;
		protected System.Web.UI.WebControls.ImageButton ImageButton6;
		protected System.Web.UI.WebControls.ImageButton ImageButton7;
		protected System.Web.UI.WebControls.Panel Panel1;
		protected System.Web.UI.WebControls.Image Image1;
		protected System.Web.UI.WebControls.Panel pnlPersonalUPdate;
		protected System.Web.UI.WebControls.Label Label9;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator2;
		protected System.Web.UI.WebControls.CompareValidator CompareValidator2;
		protected System.Web.UI.WebControls.Label Label10;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator3;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator6;
		protected eWorld.UI.CalendarPopup calendarDOB;
		protected System.Web.UI.WebControls.TextBox txtFName;
		protected System.Web.UI.WebControls.RadioButtonList RadioButtonList1;
		protected System.Web.UI.WebControls.DropDownList ddBloodGrp;
		protected System.Web.UI.WebControls.DropDownList ddReligion;
		protected System.Web.UI.WebControls.TextBox txtNick;
		protected System.Web.UI.WebControls.Label Label11;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator6;
		protected System.Web.UI.WebControls.TextBox txtAddress;
		protected System.Web.UI.WebControls.DropDownList ddStatus;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator9;
		protected System.Web.UI.WebControls.TextBox txtEmail;
		protected System.Web.UI.WebControls.TextBox txtNTN;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator7;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator8;
		protected System.Web.UI.WebControls.TextBox txtContactNo;
		protected System.Web.UI.WebControls.TextBox txtMobile;
		protected System.Web.UI.WebControls.CustomValidator CustomValidator1;
		protected System.Web.UI.WebControls.DropDownList ddNationality;
		protected System.Web.UI.WebControls.DropDownList ddNationality2;
		protected System.Web.UI.WebControls.TextBox txtPassport;
		protected System.Web.UI.WebControls.TextBox txtKin;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator2;
		protected System.Web.UI.WebControls.TextBox txtOldNic;
		protected System.Web.UI.WebControls.TextBox txtBankAcctNo;
		protected System.Web.UI.WebControls.TextBox txtAccountDetails;
		protected System.Web.UI.WebControls.DataGrid dgNIC;
		protected System.Web.UI.WebControls.Label lblEmp;
		protected System.Web.UI.WebControls.Label lblVacancy;
		protected System.Web.UI.WebControls.Button btnUpdate;
		protected System.Web.UI.WebControls.TextBox txtServerDate;
		protected System.Web.UI.WebControls.LinkButton lnkNewEmployee;
		protected System.Web.UI.WebControls.Panel pnlEmployeeInfo;
		protected System.Web.UI.WebControls.Label Label1;
		protected System.Web.UI.WebControls.Label Label2;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator8;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator3;
		protected System.Web.UI.WebControls.TextBox txtEmpInfoCode;
		protected System.Web.UI.WebControls.TextBox txtEmpInfoName;
		protected System.Web.UI.WebControls.Label Label3;
		protected System.Web.UI.WebControls.CustomValidator CustomValidator4;
		protected System.Web.UI.WebControls.Label Label4;
		protected System.Web.UI.WebControls.CustomValidator CustomValidator2;
		protected System.Web.UI.WebControls.DropDownList ddDesignation;
		protected System.Web.UI.WebControls.Label Label5;
		protected System.Web.UI.WebControls.CustomValidator CustomValidator3;
		protected System.Web.UI.WebControls.Label Label6;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator9;
		protected System.Web.UI.WebControls.DropDownList ddOpertingCity;
		protected eWorld.UI.CalendarPopup CalenderDOJ;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator4;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator5;
		protected System.Web.UI.WebControls.TextBox txtEmpInfoEmail;
		protected System.Web.UI.WebControls.TextBox txtEmpInfoExtension;
		protected System.Web.UI.WebControls.TextBox txtEmpInfoSESSI;
		protected System.Web.UI.WebControls.TextBox txtEmpInfoEOBI;
		protected System.Web.UI.WebControls.Label Label8;
		protected System.Web.UI.WebControls.CustomValidator CustomValidator5;
		protected System.Web.UI.WebControls.CompareValidator CompareValidator1;
		protected System.Web.UI.WebControls.DropDownList txtEmpInfoTOE;
		protected eWorld.UI.CalendarPopup CalenderCOD;
		protected System.Web.UI.WebControls.TextBox txtEmpInfoInsurance;
		protected System.Web.UI.WebControls.DropDownList ddNICcity;
		protected eWorld.UI.CalendarPopup InterimCommitment;
		protected System.Web.UI.WebControls.Panel pnlFamilyInfo;
		protected System.Web.UI.WebControls.DataGrid DataGrid1;
		protected System.Web.UI.WebControls.Label lblRecord;
		protected System.Web.UI.WebControls.Panel Panel2;
		protected System.Web.UI.WebControls.Label lblSpouse;
		protected System.Web.UI.WebControls.TextBox txtFamilyName;
		protected eWorld.UI.CalendarPopup CalendarPopup1;
		protected System.Web.UI.WebControls.CustomValidator CustomValidator8;
		protected System.Web.UI.WebControls.CustomValidator CustomValidator6;
		protected System.Web.UI.WebControls.DropDownList rdRelation;
		protected System.Web.UI.WebControls.DropDownList rdStatus;
		protected System.Web.UI.WebControls.CustomValidator CustomValidator7;
		protected System.Web.UI.WebControls.DropDownList ddDependent;
		protected System.Web.UI.WebControls.DropDownList rdFamilyGender;
		protected System.Web.UI.WebControls.TextBox txtOccupation;
		protected System.Web.UI.WebControls.Button btnNew;
		protected System.Web.UI.WebControls.Button btnFupdate;
		protected System.Web.UI.WebControls.Panel pnlEducation;
		protected System.Web.UI.WebControls.DataGrid DataGrid3;
		protected System.Web.UI.WebControls.Label lblEdBoRecord;
		protected System.Web.UI.WebControls.CustomValidator CustomValidator9;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator14;
		protected System.Web.UI.WebControls.CustomValidator CustomValidator10;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator15;
		protected System.Web.UI.WebControls.DropDownList txtDegree;
		protected System.Web.UI.WebControls.DropDownList txtInstitute;
		protected System.Web.UI.WebControls.TextBox txtOtherDegree;
		protected System.Web.UI.WebControls.TextBox txtOtherInstitute;
		protected System.Web.UI.WebControls.CustomValidator CustomValidator11;
		protected System.Web.UI.WebControls.DropDownList txtResult;
		protected System.Web.UI.WebControls.TextBox txtOtherMajors;
		protected System.Web.UI.WebControls.DropDownList DurationFrm;
		protected System.Web.UI.WebControls.DropDownList DurationTo;
		protected System.Web.UI.WebControls.TextBox txtAchievements;
		protected System.Web.UI.WebControls.Button btnEdNew;
		protected System.Web.UI.WebControls.Button btnEdUpdate;
		protected System.Web.UI.WebControls.Button btnECancel;
		protected System.Web.UI.WebControls.Panel Panel3;
		protected System.Web.UI.WebControls.Panel pnlBondPaper;
		protected eWorld.UI.CalendarPopup SignDate;
		protected eWorld.UI.CalendarPopup EndDate;
		protected System.Web.UI.WebControls.Button btnInsertBond;
		protected System.Web.UI.WebControls.Panel pnlFunctionalDesignation;
		protected System.Web.UI.WebControls.Label lblEmpDesignation;
		protected System.Web.UI.WebControls.Label lblFuncPcode;
		protected System.Web.UI.WebControls.CustomValidator CustomValidator12;
		protected System.Web.UI.WebControls.DropDownList ddFunctionalDesignation;
		protected System.Web.UI.WebControls.Label lblFunctionalVacancy;
		protected eWorld.UI.CalendarPopup EffectiveDate;
		protected System.Web.UI.WebControls.Button btnAddFunctional;
		protected System.Web.UI.WebControls.Button btnFunctionalCancel;
		protected System.Web.UI.WebControls.Label lblFunMsg;
		protected System.Web.UI.WebControls.ValidationSummary ValidationSummary1;
		protected System.Web.UI.HtmlControls.HtmlInputFile FileToUpload;
		protected System.Web.UI.HtmlControls.HtmlTableCell rowNIC;
		protected System.Web.UI.WebControls.Label lblBond;
		protected System.Web.UI.WebControls.DropDownList ddDepartment;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator5;
		protected System.Web.UI.WebControls.CompareValidator CompareValidator3;
		protected System.Web.UI.WebControls.CustomValidator CustomValidator13;
		protected System.Web.UI.WebControls.CustomValidator CustomValidator14;
		protected System.Web.UI.WebControls.TextBox txtEmpLocation;
		string userId="";
		private bool _refreshState;
		protected System.Web.UI.WebControls.Label lblPCode_2;
		protected System.Web.UI.WebControls.Label lblName_2;
		protected System.Web.UI.WebControls.Label lblDesg_2;
		protected System.Web.UI.WebControls.Label lblDept_2;
		protected System.Web.UI.WebControls.Label lblEmailOfficial_2;
		protected System.Web.UI.WebControls.Label lblAvailable;
		protected System.Web.UI.WebControls.LinkButton lbCheckAvailability;
		protected System.Web.UI.WebControls.RequiredFieldValidator Requiredfieldvalidator1;
		protected System.Web.UI.WebControls.Label lblADExist_2;
		protected System.Web.UI.WebControls.TextBox txtPassword;
		protected System.Web.UI.WebControls.TextBox txtEmpInfoEmail2;
		protected System.Web.UI.WebControls.TextBox txtUserID;
		protected System.Web.UI.WebControls.CheckBox chkActiveAccount;
		protected System.Web.UI.WebControls.Button cmdCreate;
		protected System.Web.UI.WebControls.Label lblError;
		protected System.Web.UI.WebControls.Label lblAccountMessage_2;
		protected System.Web.UI.WebControls.DataGrid dgRoles;
		protected System.Web.UI.WebControls.Button cmdUpdate;
		protected System.Web.UI.WebControls.Label lblRoleMessage_2;
		protected System.Web.UI.WebControls.Panel pnlCompensation;
		protected System.Web.UI.WebControls.TextBox txtMobileEntitlement;
		protected System.Web.UI.WebControls.TextBox txtPetrolEntitlement;
		protected System.Web.UI.WebControls.TextBox txtGrossSal;
		protected System.Web.UI.WebControls.TextBox txtCarDescription;
		protected System.Web.UI.WebControls.TextBox txtBasicSal;
		protected System.Web.UI.WebControls.TextBox txtHouseRent;
		protected System.Web.UI.WebControls.TextBox txtUtility;
		protected System.Web.UI.WebControls.DropDownList ddCarConveyance;
		protected System.Web.UI.WebControls.Button btnCancelCompensation;
		protected System.Web.UI.WebControls.Button btnAddCompensation;
		protected System.Web.UI.WebControls.Label lblComMsg;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator10;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator11;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator12;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator13;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator14;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator15;
		protected System.Web.UI.WebControls.ImageButton ImgCompensation;
		protected System.Web.UI.WebControls.TextBox txtEmpInfoOthers;
		protected System.Web.UI.WebControls.TextBox txtY;
		protected System.Web.UI.WebControls.TextBox txtX;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator7;
		protected System.Web.UI.WebControls.Label Label7;
		protected System.Web.UI.WebControls.DropDownList rdCompansatory;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator10;
		protected System.Web.UI.WebControls.Label Label12;
		protected System.Web.UI.WebControls.TextBox txtProbabtionOther;
		protected System.Web.UI.WebControls.DropDownList ddProbabtion;
		protected eWorld.UI.CalendarPopup calenderFinalConfirmation;
		protected System.Web.UI.WebControls.DropDownList ddNewJoiner;
		protected eWorld.UI.MaskedTextBox txtNewNic;
		protected System.Web.UI.WebControls.Panel row1;
		protected System.Web.UI.WebControls.Label Label16;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator4;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator1;
		protected System.Web.UI.WebControls.TextBox txtHidden;
		protected System.Web.UI.WebControls.Label Label13;
		protected System.Web.UI.WebControls.DropDownList ddFDesignation;
		protected System.Web.UI.WebControls.Label lblFV;
		protected System.Web.UI.WebControls.DropDownList ddeStatus;
		protected eWorld.UI.CalendarPopup CTDOJ;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator11;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator18;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator19;
		protected System.Web.UI.WebControls.ImageButton ImgRelative;
		protected System.Web.UI.WebControls.Label Label14;
		protected System.Web.UI.WebControls.DropDownList ddNetwork;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator12;
		protected System.Web.UI.WebControls.ImageButton imgContract;
		protected System.Web.UI.WebControls.DataGrid dgContractHistory;
		protected System.Web.UI.WebControls.Button btnNotifyCancel;
		protected System.Web.UI.WebControls.Button btnOK;
		protected System.Web.UI.WebControls.Label lblWarning;
		protected System.Web.UI.WebControls.Panel pnlNotification;
		protected System.Web.UI.WebControls.Button btnCRenew;
		protected System.Web.UI.WebControls.Button btnCNew;
		protected System.Web.UI.WebControls.DropDownList ddServingStatus;
		protected System.Web.UI.WebControls.TextBox txtCRemarks;
		protected eWorld.UI.CalendarPopup cpContractExpiry;
		protected System.Web.UI.WebControls.DropDownList ddDuration;
		protected eWorld.UI.CalendarPopup cpContarctStart;
		protected System.Web.UI.WebControls.Label lblLastContractId;
		protected System.Web.UI.WebControls.Label lblStatus;
		protected System.Web.UI.WebControls.Label lblLastExpiry;
		protected System.Web.UI.WebControls.Panel pnlContract;
		protected System.Web.UI.HtmlControls.HtmlTableCell tdContract;
		protected System.Web.UI.WebControls.Label lblRefProfile;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator21;
		protected System.Web.UI.WebControls.TextBox txtRefCode;
		protected System.Web.UI.WebControls.DropDownList ddPosType;
		protected System.Web.UI.WebControls.TextBox txtName;
		protected System.Web.UI.WebControls.RadioButtonList rdoSalType;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator22;
		protected System.Web.UI.WebControls.DropDownList ddCurrency;
		private bool _isRefresh;

		public bool IsRefresh
		{
			get
			{
				return _isRefresh;
			}
		}
		private bool IsPageAccessAllowed()
		{
					
			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception) 
			{
				Response.Redirect("../notavailable.htm");
			}
			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"View")==true)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("../notavailable.htm");
				return false;
			}
		}
		private void Page_Load(object sender, System.EventArgs e)
		{
			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			if(IsPageAccessAllowed())
			{
			 
			}
			else
			{
				Response.Redirect("../notavailable.htm");
			}
			con=new SqlConnection(Connection.ConnectionString);
			//Page.SmartNavigation=true;
			if(this.ddeStatus.SelectedValue.ToString()=="1")
			{
				this.CTDOJ.Enabled=true;
			}
			else
			{
				this.CTDOJ.Enabled=false;
			}
			if(!IsPostBack)
			{
				EmployeeInfo();
				PersonalInfo();
				GetNetwork();
				rowNIC.Visible=false;
				this.ValidationSummary1.Visible=true;
				this.txtServerDate.Text=Count();
				this.btnUpdate.Attributes.Add("onclick","return checkValue();");
				this.btnCNew.Attributes.Add("onclick","return validateContract();");
				this.btnCRenew.Attributes.Add("onclick","return validateContract();");
				txtName.Attributes.Add("onkeyup","return GetExEmployee(event);");
				txtName.Attributes.Add("onkeypress","return disableEnterKey(event);");
				cmdCreate.Attributes.Add("onclick","return CheckOfficalEmail();");
				imgContract.Attributes.Add("onclick","return checkEmpType();");
				this.btnAddCompensation.Attributes.Add("onclick","return VerifyComp();");
				getYears();
				this.txtProbabtionOther.Attributes.Add("onKeyPress","return checkNum();");
				this.ImageButton2.Attributes.Add("onclick","return CheckVacancy();");
				this.CTDOJ.LowerBoundDate=DateTime.Now;
				RedirectToMenu();
				GetCurrency();
				//==================Tentative Date======================================//
				if(this.ddeStatus.SelectedValue.ToString()=="1")
				{
					this.RequiredFieldValidator7.Enabled=false;
					RequiredFieldValidator9.Enabled=false;
					RequiredFieldValidator11.Enabled=true;
					this.CalenderDOJ.Enabled=false;
					Label6.Visible=false;
					Label7.Visible=false;
					this.ddNewJoiner.Enabled=false;
					this.CTDOJ.Enabled=true;
				}
				else
				{
				
					this.RequiredFieldValidator7.Enabled=true;
					RequiredFieldValidator9.Enabled=true;
					RequiredFieldValidator11.Enabled=false;
					this.CalenderDOJ.Enabled=true;

					Label6.Visible=true;
					Label7.Visible=true;
					this.ddNewJoiner.Enabled=true;
					this.CTDOJ.Enabled=false;
				}
				//======================================================================//
			}
			//=======================Check Buttons=================================//
			if(this.btnUpdate.Enabled==false)
			{
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Family")==true)
				{
					this.btnNew.Visible=true;
					this.btnFupdate.Visible=true;
				}
				else
				{
					this.btnNew.Visible=false;
					this.btnFupdate.Visible=false;
					this.ImageButton3.Visible=false;
				}
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Educationalinfo")==true)
				{
					this.btnEdNew.Visible=true;
					this.btnEdUpdate.Visible=true;
				}
				else
				{
					this.btnEdNew.Visible=false;
					this.btnEdUpdate.Visible=false;
					this.ImageButton4.Visible=false;
				}
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Bond")==true)
				{
					ImageButton6.Visible=true;
					btnInsertBond.Visible=true;
					btnInsertBond.Visible=true;
					//btnBondCancel.Visible=true;
				}
				else
				{
					ImageButton6.Visible=false;
					btnInsertBond.Visible=false;
					btnInsertBond.Visible=false;
					//btnBondCancel.Visible=false;
				}
				/*if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Functional")==true)
				{
					ImageButton7.Visible=true;
					btnAddFunctional.Visible=true;
					btnFunctionalCancel.Visible=true; 
				}
				else
				{
					ImageButton7.Visible=false;
					btnAddFunctional.Visible=false;
					btnFunctionalCancel.Visible=false; 
				}*/
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Account")==true)
				{
					ImageButton5.Visible=true;
					cmdCreate.Visible=true;
				}
				else
				{
					ImageButton5.Visible=false;
					cmdCreate.Visible=false;
				}
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Compensation")==true)
				{
					ImgCompensation.Visible=true;
					//pnlCompensation.Visible=true;
				}
				else
				{
					ImgCompensation.Visible=false;
					//pnlCompensation.Visible=false;
				}
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Contract")==true)
				{
					imgContract.Visible=true;
					//pnlCompensation.Visible=true;
				}
				else
				{
					imgContract.Visible=false;
					//pnlCompensation.Visible=false;
				}
			}
			//=====================================================================//
			
			
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{ 
			this.ImageButton1.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton1_Click);
			this.ImageButton2.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton2_Click);
			this.ImageButton3.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton3_Click);
			this.ImageButton4.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton4_Click);
			this.ImageButton5.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton5_Click);
			this.ImageButton6.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton6_Click);
			this.ImageButton7.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton7_Click);
			this.ImgCompensation.Click += new System.Web.UI.ImageClickEventHandler(this.ImgCompensation_Click);
			this.ImgRelative.Click += new System.Web.UI.ImageClickEventHandler(this.ImgRelative_Click);
			this.imgContract.Click += new System.Web.UI.ImageClickEventHandler(this.imgContract_Click);
			this.ddReligion.SelectedIndexChanged += new System.EventHandler(this.ddReligion_SelectedIndexChanged);
			this.CustomValidator1.ServerValidate += new System.Web.UI.WebControls.ServerValidateEventHandler(this.TextValidate);
			this.ddNationality.SelectedIndexChanged += new System.EventHandler(this.ddNationality_SelectedIndexChanged);
			this.ddNationality2.SelectedIndexChanged += new System.EventHandler(this.ddNationality2_SelectedIndexChanged);
			this.btnUpdate.Click += new System.EventHandler(this.btnUpdate_Click);
			this.lnkNewEmployee.Click += new System.EventHandler(this.lnkNewEmployee_Click);
			this.txtRefCode.TextChanged += new System.EventHandler(this.txtRefCode_TextChanged);
			this.ddPosType.SelectedIndexChanged += new System.EventHandler(this.ddPosType_SelectedIndexChanged);
			this.ddNetwork.SelectedIndexChanged += new System.EventHandler(this.ddNetwork_SelectedIndexChanged);
			this.CustomValidator4.ServerValidate += new System.Web.UI.WebControls.ServerValidateEventHandler(this.TextValidate);
			this.CustomValidator2.ServerValidate += new System.Web.UI.WebControls.ServerValidateEventHandler(this.TextValidate);
			this.ddDepartment.SelectedIndexChanged += new System.EventHandler(this.ddDepartment_SelectedIndexChanged);
			this.ddDesignation.SelectedIndexChanged += new System.EventHandler(this.ddDesignation_SelectedIndexChanged_1);
			this.CustomValidator3.ServerValidate += new System.Web.UI.WebControls.ServerValidateEventHandler(this.TextValidate);
			this.ddFDesignation.SelectedIndexChanged += new System.EventHandler(this.ddFDesignation_SelectedIndexChanged);
			this.ddeStatus.SelectedIndexChanged += new System.EventHandler(this.ddeStatus_SelectedIndexChanged);
			this.CalenderDOJ.DateChanged += new eWorld.UI.DateChangedEventHandler(this.CalenderDOJ_DateChanged);
			this.ddProbabtion.SelectedIndexChanged += new System.EventHandler(this.ddProbabtion_SelectedIndexChanged);
			this.txtProbabtionOther.TextChanged += new System.EventHandler(this.txtProbabtionOther_TextChanged);
			this.CustomValidator5.ServerValidate += new System.Web.UI.WebControls.ServerValidateEventHandler(this.TextValidate);
			this.DataGrid1.SelectedIndexChanged += new System.EventHandler(this.DataGrid1_SelectedIndexChanged_2);
			this.CustomValidator8.ServerValidate += new System.Web.UI.WebControls.ServerValidateEventHandler(this.TextValidate);
			this.CustomValidator6.ServerValidate += new System.Web.UI.WebControls.ServerValidateEventHandler(this.TextValidate);
			this.CustomValidator7.ServerValidate += new System.Web.UI.WebControls.ServerValidateEventHandler(this.TextValidate);
			this.btnNew.Click += new System.EventHandler(this.btnNew_Click);
			this.btnFupdate.Click += new System.EventHandler(this.btnFupdate_Click);
			this.DataGrid3.SelectedIndexChanged += new System.EventHandler(this.DataGrid3_SelectedIndexChanged_3);
			this.CustomValidator9.ServerValidate += new System.Web.UI.WebControls.ServerValidateEventHandler(this.TextValidate);
			this.CustomValidator10.ServerValidate += new System.Web.UI.WebControls.ServerValidateEventHandler(this.TextValidate);
			this.txtDegree.SelectedIndexChanged += new System.EventHandler(this.txtDegree_SelectedIndexChanged);
			this.txtInstitute.SelectedIndexChanged += new System.EventHandler(this.txtInstitute_SelectedIndexChanged);
			this.CustomValidator11.ServerValidate += new System.Web.UI.WebControls.ServerValidateEventHandler(this.TextValidate);
			this.CustomValidator13.ServerValidate += new System.Web.UI.WebControls.ServerValidateEventHandler(this.TextValidate);
			this.CustomValidator14.ServerValidate += new System.Web.UI.WebControls.ServerValidateEventHandler(this.TextValidate);
			this.btnEdNew.Click += new System.EventHandler(this.btnEdNew_Click);
			this.btnEdUpdate.Click += new System.EventHandler(this.btnEdUpdate_Click);
			this.btnECancel.Click += new System.EventHandler(this.btnECancel_Click);
			this.lbCheckAvailability.Click += new System.EventHandler(this.lbCheckAvailability_Click);
			this.cmdCreate.Click += new System.EventHandler(this.cmdCreate_Click);
			this.cmdUpdate.Click += new System.EventHandler(this.cmdUpdate_Click);
			this.btnInsertBond.Click += new System.EventHandler(this.btnInsertBond_Click);
			this.CustomValidator12.ServerValidate += new System.Web.UI.WebControls.ServerValidateEventHandler(this.TextValidate);
			this.ddFunctionalDesignation.SelectedIndexChanged += new System.EventHandler(this.ddFunctionalDesignation_SelectedIndexChanged);
			this.btnAddFunctional.Click += new System.EventHandler(this.btnAddFunctional_Click);
			this.btnFunctionalCancel.Click += new System.EventHandler(this.btnFunctionalCancel_Click);
			this.txtGrossSal.TextChanged += new System.EventHandler(this.txtGrossSal_TextChanged);
			this.btnAddCompensation.Click += new System.EventHandler(this.btnAddCompensation_Click);
			this.btnCancelCompensation.Click += new System.EventHandler(this.btnCancelCompensation_Click);
			this.cpContarctStart.DateChanged += new eWorld.UI.DateChangedEventHandler(this.cpContarctStart_DateChanged);
			this.ddDuration.SelectedIndexChanged += new System.EventHandler(this.ddDuration_SelectedIndexChanged);
			this.btnCNew.Click += new System.EventHandler(this.btnCNew_Click);
			this.btnCRenew.Click += new System.EventHandler(this.btnCRenew_Click);
			this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
			this.btnNotifyCancel.Click += new System.EventHandler(this.btnNotifyCancel_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion
		public void GetNetwork()
		{
			if(con.State!=ConnectionState.Open)
			{
				con.Open();
			}
			string userID=GeoSecurity.GetUserNetwork(Session["user_id"].ToString());
			SqlCommand cmd=new SqlCommand("select nID,name from t_network where del=1 and nID in ("+userID+") order by name",con);
			SqlDataReader rd=cmd.ExecuteReader();
			ddNetwork.Items.Clear();
			ListItem _item=new ListItem("Select--Network","0");
			ddNetwork.Items.Add(_item);
			while(rd.Read())
			{
				ListItem item=new ListItem(rd[1].ToString(),rd[0].ToString());
				ddNetwork.Items.Add(item);
			}
			rd.Close();
		}
		private void ImageButton2_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Requiredfieldvalidator1.Enabled=false;
			this.lblSpouse.Visible=false;		
			CustomValidator8.Enabled=false;
			CustomValidator6.Enabled=false;
			CustomValidator7.Enabled=false; 
 
			CustomValidator9.Enabled=false;
			RequiredFieldValidator14.Enabled=false;
			CustomValidator11.Enabled=false;
			CustomValidator10.Enabled=false;
			CustomValidator13.Enabled=false;
			CustomValidator14.Enabled=false; 
			RequiredFieldValidator15.Enabled=false;
		
			CustomValidator12.Enabled=false;
			Page.Validate();
			this.ValidationSummary1.Visible=true;
			
			lblVacancy.Visible=false;
			lblEmp.Visible=false;
			dgNIC.Visible=false;

			lblError.Visible=false;
			lblBond.Visible=false;
			lblFunMsg.Visible=false;

			RegularExpressionValidator10.Enabled=false;
			RegularExpressionValidator11.Enabled=false;
			RegularExpressionValidator12.Enabled=false;
			RequiredFieldValidator22.Enabled=false;
			RegularExpressionValidator14.Enabled=false;
			RegularExpressionValidator13.Enabled=false;
			RegularExpressionValidator15.Enabled=false;
			RequiredFieldValidator10.Enabled=false;
			this.lblComMsg.Visible=false; 
			if(Page.IsValid)
			{
				this.ValidationSummary1.Visible=false;
				pnlPersonal.Visible=true;
				pnlEmployeeInfo.Visible=false;
				pnlFamilyInfo.Visible=false;
				pnlEducation.Visible=false;
				btnUpdate.Visible=true;
				Panel3.Visible=false;
				this.pnlBondPaper.Visible=false;
				this.pnlFunctionalDesignation.Visible=false;
				this.pnlCompensation.Visible=false;
				pnlContract.Visible=false;
				this.txtHidden.Text=this.txtEmpInfoTOE.SelectedValue.ToString();
				//PersonalInfo();
			}
			string errorString="";
			errorString+=PerVerification();
			errorString+=EmpVerification();
			if(errorString.Length>0)
			{
				errorString=errorString.Remove(errorString.Length-1,1);
				Response.Write("<script>window.alert('"+ errorString +"')</script>");
			}
			
		}

		private void ImageButton1_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Requiredfieldvalidator1.Enabled=false;
			this.lblSpouse.Visible=false;
			CustomValidator8.Enabled=false;
			CustomValidator6.Enabled=false;
			CustomValidator7.Enabled=false; 

			CustomValidator9.Enabled=false;
			RequiredFieldValidator14.Enabled=false;
			CustomValidator11.Enabled=false;
			CustomValidator10.Enabled=false;
			CustomValidator13.Enabled=false;
			CustomValidator14.Enabled=false; 
			RequiredFieldValidator15.Enabled=false;
			CustomValidator12.Enabled=false;
			lblVacancy.Visible=false;
			lblEmp.Visible=false;
			dgNIC.Visible=false;

			lblError.Visible=false;
			lblBond.Visible=false;
			lblFunMsg.Visible=false;
 
			RegularExpressionValidator10.Enabled=false;
			RegularExpressionValidator11.Enabled=false;
			RegularExpressionValidator12.Enabled=false;
			RequiredFieldValidator22.Enabled=false;
			RegularExpressionValidator14.Enabled=false;
			RegularExpressionValidator13.Enabled=false;
			RegularExpressionValidator15.Enabled=false;
			RequiredFieldValidator10.Enabled=false;
			this.lblComMsg.Visible=false;
			Page.Validate();
			this.ValidationSummary1.Visible=true;
			if(Page.IsValid)
			{ 
				pnlPersonal.Visible=false;
				pnlEmployeeInfo.Visible=true;
				pnlFamilyInfo.Visible=false;
				pnlEducation.Visible=false;
				Panel3.Visible=false;
				this.pnlBondPaper.Visible=false;
				this.pnlFunctionalDesignation.Visible=false;
				this.pnlCompensation.Visible=false;
				pnlContract.Visible=false;
				//btnUpdate.Visible=true;
				//EmployeeInfo();
			}
			string errorString="";
			errorString+=PerVerification();
			errorString+=EmpVerification();
			if(errorString.Length>0)
			{
				errorString=errorString.Remove(errorString.Length-1,1);
				Response.Write("<script>window.alert('"+ errorString +"')</script>");
			}
		}

		private void ImageButton3_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Requiredfieldvalidator1.Enabled=false;
			this.lblSpouse.Visible=false;
			CustomValidator9.Enabled=false;
			RequiredFieldValidator14.Enabled=false;
			CustomValidator11.Enabled=false;
			CustomValidator10.Enabled=false;
			CustomValidator13.Enabled=false;
			CustomValidator14.Enabled=false; 
			RequiredFieldValidator15.Enabled=false;
			
			CustomValidator12.Enabled=false;
			Page.Validate();
			
			lblVacancy.Visible=false;
			lblEmp.Visible=false;
			dgNIC.Visible=false;

			lblError.Visible=false;
			lblBond.Visible=false;
			lblFunMsg.Visible=false;
 
			RegularExpressionValidator10.Enabled=false;
			RegularExpressionValidator11.Enabled=false;
			RegularExpressionValidator12.Enabled=false;
			RequiredFieldValidator22.Enabled=false;
			RegularExpressionValidator14.Enabled=false;
			RegularExpressionValidator13.Enabled=false;
			RegularExpressionValidator15.Enabled=false;
			RequiredFieldValidator10.Enabled=false;
			this.lblComMsg.Visible=false;
			if(Page.IsValid)
			{
				this.ValidationSummary1.Visible=false;
				pnlFamilyInfo.Visible=true;
				DataGrid1.Visible=true;
				pnlPersonal.Visible=false;
				pnlEmployeeInfo.Visible=false;
				pnlEducation.Visible=false;
				btnUpdate.Visible=false;
				Panel3.Visible=false;
				this.pnlBondPaper.Visible=false;
				this.pnlFunctionalDesignation.Visible=false;
				this.pnlCompensation.Visible=false;
				pnlContract.Visible=false;
				FamilyInfo();
			}
		}

		public void PersonalInfo()
		{
			ddBloodGrp.Visible=true;
			con.Open();
			string getCity2="select cityid,cityname from t_city where status='1' order by cityname";
			SqlCommand cCity2=new SqlCommand(getCity2,con);
			SqlDataReader rCity2=cCity2.ExecuteReader();
			int b2=1;
			while(rCity2.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rCity2[0].ToString();
				itm.Text=rCity2[1].ToString();
				ddNICcity.Items.Insert(b2,itm);
				b2++ ; 
			}
			rCity2.Close();
			

			string str="select * from view_Nationality";
			SqlCommand cmd=new SqlCommand(str,con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem(rd[0].ToString(),rd[1].ToString());
				this.ddNationality.Items.Add(itm);
			}
			rd.Close();
			con.Close();
		 			
		}

		public void EmployeeInfo()
		{
			con.Open();
			if(GeoSecurity.isAllAllow("Department",Session["user_id"].ToString()))
			{
				//string getDept="select deptid,deptname from t_department where status='" + 1 + "' order by deptname";
				string getDept="select dept.deptid,dept.deptname from t_department dept,t_sbu s,t_network n where dept.sbu=s.sbuid and s.isactive=1 and dept.status=1 and s.networkid=n.nid and n.nId = "+ddNetwork.SelectedItem.Value+" order by deptname";
				SqlCommand cm=new SqlCommand(getDept,con);
				SqlDataReader r=cm.ExecuteReader();
				ddDepartment.Items.Clear();
				ListItem _itm=new ListItem("Select--Department","0");
				ddDepartment.Items.Add(_itm);
				int x=1;	
				while(r.Read())
				{
					ListItem itm=new ListItem();
					itm.Value=r[0].ToString();
					itm.Text=r[1].ToString();
					ddDepartment.Items.Insert(x,itm);
					x++;
				}
				r.Close();
			}
			else
			{
				string deptid=GeoSecurity.GetRights("Department",Session["user_id"].ToString().Trim());
				if(deptid.Length>0)
				{
					deptid=deptid.Remove(deptid.Length-1,1);
					//string getDept="select deptid,deptname from t_department where status=1 and deptid in ("+deptid+") order by deptname";
				    string getDept="select dept.deptid,dept.deptname from t_department dept,t_sbu s,t_network n where dept.sbu=s.sbuid and s.isactive=1 and dept.status=1 and s.networkid=n.nid and n.nId = "+ddNetwork.SelectedItem.Value+" and deptid in ("+deptid+") order by deptname";
					SqlCommand cm=new SqlCommand(getDept,con);
					SqlDataReader r=cm.ExecuteReader();
					ddDepartment.Items.Clear();
					ListItem _itm=new ListItem("Select--Department","0");
					ddDepartment.Items.Add(_itm);
					int x=1;	
					while(r.Read())
					{
						ListItem itm=new ListItem();
						itm.Value=r[0].ToString();
						itm.Text=r[1].ToString();
						ddDepartment.Items.Insert(x,itm);
						x++;
					}
					r.Close();
				}
			}
			con.Close();

			//====================Check for All Station=============================//
			con.Open();
			if(GeoSecurity.isAllAllow("Station",Session["user_id"].ToString()))
			{
				string getCity3="select cityid,cityname from t_city where status='1' order by cityname";
				SqlCommand cCity3=new SqlCommand(getCity3,con);
				SqlDataReader rCity3=cCity3.ExecuteReader();
				int d=1;
				while(rCity3.Read())
				{
					ListItem itm=new ListItem();
					itm.Value=rCity3[0].ToString();
					itm.Text=rCity3[1].ToString();
					ddOpertingCity.Items.Insert(d,itm);
					d++; 
				}
				rCity3.Close();
			}
			else
			{
			    string cityid=GeoSecurity.GetRights("Station",Session["user_id"].ToString().Trim());
				if(cityid.Length>0)
				{
					cityid=cityid.Remove(cityid.Length-1,1);
					string getCity3="select cityid,cityname from t_city where status='1' and cityid in ("+cityid+") order by cityname";
					SqlCommand cCity3=new SqlCommand(getCity3,con);
					SqlDataReader rCity3=cCity3.ExecuteReader();
					int d=1;
					while(rCity3.Read())
					{
						ListItem itm=new ListItem();
						itm.Value=rCity3[0].ToString();
						itm.Text=rCity3[1].ToString();
						ddOpertingCity.Items.Insert(d,itm);
						d++; 
					}
					rCity3.Close();
				}
			}
			con.Close();
		}

		public void FamilyInfo()
		{
				
			con.Open();
			//string getFamily="select name,relationship,maritialstatus,gender,occupation,pcode,fdid from t_familydetails where isactive='1'";
			string empId=txtEmpInfoCode.Text;
			//Session["user_id"].ToString();
			//Request.QueryString["id"].ToString();
			string getFamily=
				"select name,relationship = case relationship when '2' then 'Husband' " + 
				" when '3' then 'Wife' " + 
				" when '4' then 'Son' " + 
				" when '5' then 'Daughter' " + 
				" END, " + 
				" maritialstatus=case maritialstatus when '1' then 'Single' " + 
				" when '2' then 'Married' " + 
				" when '3' then 'Divorced' " +
				" when '4' then 'Widow' " + 
				" when '5' then 'Separated' " + 
				" END, " + 
				" gender=case gender WHEN '1' THEN 'Male' " + 
				" when '2' then 'Female' " + 
				" END ,occupation,DOB,dependent=case dependent when '1' then 'Yes' when '2' then 'No' end,pcode,fdid " + 
				" from t_familydetails where isactive='1' and pcode='" + empId + "' And relationship in(2,3,4,5)";
			SqlDataAdapter rd=new SqlDataAdapter(getFamily,con);
			DataSet dsFamily=new DataSet();
			
			rd.Fill(dsFamily,"FamilyInfo");
			DataGrid1.DataSource=dsFamily;
			DataGrid1.DataBind();
			int getItems=DataGrid1.Items.Count;
			DataGrid1.Columns[8].Visible=false;
			DataGrid1.Columns[9].Visible=false;
			clickEvents();
			if(getItems<=0)
			{
				lblRecord.Visible=true;
			}
			else
			{
				lblRecord.Visible=false;
			}
			con.Close();
			
		}

		public void EducationalInfo()
		{
			this.txtDegree.Items.Clear();
			this.txtInstitute.Items.Clear();
			this.txtOtherMajors.Visible=true;
			con.Open();
			string empId=txtEmpInfoCode.Text;
			DataGrid3.Visible=true; 
			//string getInfo="select e.eduinfoid,degree=case e.degree when '0' then (select temp.otherdegree from t_educationinfo temp where temp.eduinfoid=e.eduinfoid ) else (select d.degree from t_degree d where d.id=e.degree) end , institute=case e.institute when '0' then (select temp.otherinstitute from t_educationinfo temp where temp.eduinfoid=e.eduinfoid) else (select temp.institute_name from t_institute temp where temp.id=e.institute)end ,e.durationto,e.result,e.durationfrom,majors=case e.majors when '0' then (select temp.othermajors from t_educationinfo temp where temp.eduinfoid=e.eduinfoid) else (select major from t_majors where id=e.majors) end ,e.achievements,type from t_educationinfo e where e.isactive='1' And e.pcode='"+empId+"'";
			string getInfo="select e.eduinfoid,degree=case e.degree when '-1' then (select temp.otherdegree from t_educationinfo temp where temp.eduinfoid=e.eduinfoid ) else (select d.degree from t_degree d where d.id=e.degree) end , institute=case e.institute when '-1' then (select temp.otherinstitute from t_educationinfo temp where temp.eduinfoid=e.eduinfoid) else (select temp.institute_name from t_institute temp where temp.id=e.institute)end ,e.durationto,e.result,e.durationfrom,e.majors,e.achievements,type from t_educationinfo e where e.isactive='1' And e.pcode='"+empId+"'";
			SqlDataAdapter rd=new SqlDataAdapter(getInfo,con);
			DataSet rSet=new DataSet();
			rd.Fill(rSet,"EdutionalInfo");
			DataView dv=new DataView(rSet.Tables["EdutionalInfo"]);
			//dv.Sort=sort2; 
			this.DataGrid3.DataSource=dv;;
			this.DataGrid3.DataBind();
			clickEvents2();
			int getItems=this.DataGrid3.Items.Count;
			if(getItems<=0)
			{
				this.lblEdBoRecord.Visible=true;
			}
			else
			{
				this.lblEdBoRecord.Visible=false;
			}
			this.DataGrid3.Columns[9].Visible=false;
			this.DataGrid3.Columns[8].Visible=false;
			 
			string getDegree="select id,degree from t_degree where isactive='1' order by degree";
			SqlCommand cmDegree=new SqlCommand(getDegree,con);
			SqlDataReader rdDegree=cmDegree.ExecuteReader();
		
			ListItem it=new ListItem();
			it.Value="0";
			it.Text="Select--Degree";
			this.txtDegree.Items.Insert(0,it);
			int i=1;
			while(rdDegree.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rdDegree[0].ToString();
				itm.Text=rdDegree[1].ToString();
				this.txtDegree.Items.Insert(i,itm);
				i++;
			}
			rdDegree.Close();
			it=new ListItem("Other","-1");
			this.txtDegree.Items.Add(it);

			string getInstitute="select id,institute_name from t_institute where isactive='1' order by institute_name";
			SqlCommand cmInstitute=new SqlCommand(getInstitute,con);
			SqlDataReader rdInstitute=cmInstitute.ExecuteReader();
			
			ListItem item2=new ListItem();
			item2.Value="0";
			item2.Text="Select--Institute";
			this.txtInstitute.Items.Insert(0,item2);
			int j=1;
			while(rdInstitute.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rdInstitute[0].ToString();
				itm.Text=rdInstitute[1].ToString();
				this.txtInstitute.Items.Insert(j,itm);
				j++;
			}
			rdInstitute.Close();
			item2=new ListItem("Other","-1");
			this.txtInstitute.Items.Add(item2);
			con.Close();
			
		}

		private void ddDepartment_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			getDes();
		}
		public void getDes()
		{
			this.lblFV.Text="";
			this.ddDesignation.Items.Clear();
			ListItem item=new ListItem();
			item.Value="0";
			item.Text="Select--Designation";
			this.ddDesignation.Items.Add(item);
			con.Open();
			string getDesignation="select d.desigid,d.designation,c.cat_name from t_designation d,t_categorization c where d.status=1 And c.isactive=1 And d.category=c.cat_id And d.deptid='"+this.ddDepartment.SelectedValue.ToString()+"' order by designation";
			SqlCommand cmd=new SqlCommand(getDesignation,con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rd[0].ToString();
				itm.Text=rd[1].ToString();
				//+":"+"Category "+rd[2].ToString();
				this.ddDesignation.Items.Add(itm);
			}
			rd.Close(); 
			con.Close();

		}

		private void ImageButton4_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Requiredfieldvalidator1.Enabled=false;
			this.lblSpouse.Visible=false;
			CustomValidator8.Enabled=false;
			CustomValidator6.Enabled=false;
			CustomValidator7.Enabled=false; 
			CustomValidator12.Enabled=false;
			
			lblVacancy.Visible=false;
			lblEmp.Visible=false;
			dgNIC.Visible=false;
			
			lblError.Visible=false;
			lblBond.Visible=false;
			lblFunMsg.Visible=false;
			
			RegularExpressionValidator10.Enabled=false;
			RegularExpressionValidator11.Enabled=false;
			RegularExpressionValidator12.Enabled=false;
			RequiredFieldValidator22.Enabled=false;
			RegularExpressionValidator14.Enabled=false;
			RegularExpressionValidator13.Enabled=false;
			RegularExpressionValidator15.Enabled=false;
			RequiredFieldValidator10.Enabled=false;
			this.lblComMsg.Visible=false;
 
			Page.Validate();
			if(Page.IsValid)
			{
				this.ValidationSummary1.Visible=false;
				pnlEducation.Visible=true;
				pnlEmployeeInfo.Visible=false;
				pnlFamilyInfo.Visible=false;
				pnlPersonal.Visible=false;
				btnUpdate.Visible=false;
				EducationalInfo();
				this.pnlBondPaper.Visible=false;
				this.pnlFunctionalDesignation.Visible=false;
				Panel3.Visible=false;
				this.pnlCompensation.Visible=false;
				pnlContract.Visible=false;
			}
		}

		private void btnUpdate_Click(object sender, System.EventArgs e)
		{
			if(!_isRefresh)
			{
				lblVacancy.Visible=false;
				this.dgNIC.Visible=false;
				lblEmp.Visible=false;
				con.Open();
				string IsPcode="select pcode from t_employee where pcode='"+ this.txtEmpInfoCode.Text +"'";
				SqlCommand cmd=new SqlCommand(IsPcode,con);
				SqlDataReader rd=cmd.ExecuteReader();
				rd.Read();
				if(rd.HasRows)
				{
					this.lblVacancy.Text="Given employee pcode have already entered.Please enter other pcode ";
					this.lblVacancy.Visible=true; 
				}
				else
				{
					rd.Close();
					con.Close();
					lblVacancy.Visible=false;
					lblVacancy.Text="Record Insert Successfully.Now You Can Use Family ,Education ,User Account & Other Information Tabs";
					if(Page.IsValid)
					{
						bool IsValid=false;
						if(this.ddNationality.SelectedValue=="163")
						{
							IsValid=verifyNIC();
							this.lblEmp.Text="The Given NIC # Already Assigned To Above Employee.Please Verify Above Record Using Select Option ";
						}
						else
						{
							IsValid=verifyNIC(true);
							this.lblEmp.Text="The Given Passport # Already Assigned To Above Employee.Please Verify Above Record Using Select Option ";
						}
						if(IsValid)
						{
							rowNIC.Visible=true;
							this.dgNIC.Visible=true; 
							this.lblEmp.Visible=true; 
				
						}
						else
						{
							if(this.CompareValidator2.IsValid)
							{
								rowNIC.Visible=false;
								this.dgNIC.Visible=false; 
								this.lblEmp.Visible=false; 
								string var="NULL";
								UpdatePersonalInfo(var);
								updateJDStatus(this.txtEmpInfoCode.Text.Trim(),"","");
							}
						}
			
					}
					else
					{
						this.ValidationSummary1.Visible=true;
					}
				}
				string errorString="";
				errorString+=PerVerification();
				errorString+=EmpVerification();
				if(errorString.Length>0)
				{
					errorString=errorString.Remove(errorString.Length-1,1);
					Response.Write("<script>window.alert('"+ errorString +"')</script>");
				}
			}
		}
		public void updateJDStatus(string pCode,string eMail,string Val)
		{
			SqlTransaction trans=null;
			if (con.State==ConnectionState.Closed)
			{
				con.Open();
			}
			trans=con.BeginTransaction();
			string CheckRecord="select * from t_JDStatusLog where pcode='"+pCode+"'";
			SqlCommand cmd=new SqlCommand(CheckRecord,con);
			cmd.Transaction=trans;
			SqlDataReader rd2=cmd.ExecuteReader();
			rd2.Read();
			if(!rd2.HasRows)
			{
				rd2.Close();
				string getEmployeeInfo="select e.pcode,e.name,dept.deptname,d.designation, f.functionaltitle,[JD Status]='Available',[Zameer Status]='Not Available',JDCounter=0,ZameerCounter=0,EmailStatus='Send',RecordAddDate=getdate(),EmailSentDate=getdate() from t_employee e,t_designation d,t_department dept,t_functionaldesignation f,t_designationhistory dh where e.del=1 and d.status=1 and dept.status=1 and f.isactive=1 and e.desigid=d.desigid and dept.deptid=d.deptid and d.desigid=f.functionaldesignation and dh.pcode=e.pcode and dh.isactive=1 and dh.fdesigid=f.fdesigid and e.pcode='"+pCode+"'";
				SqlDataAdapter dr=new SqlDataAdapter(getEmployeeInfo,con);
				dr.SelectCommand.Transaction=trans;
				DataSet ds=new DataSet();
				dr.Fill(ds,"Records");
				try
				{
					string InsertEmployee="insert into t_JDStatusLog (pcode,name,department,designation,functionaltitle,Email,JDStatus,ZameerStatus,JDCounter,ZameerCounter,EmailStatus,RecordAddDate,EmailSentDate) values (@pcode,@name,@department,@designation,@functionaltitle,@Email,@JDStatus,@ZameerStatus,0,0,@EmailStatus,getdate(),@EmailSentDate)";
					cmd=new SqlCommand(InsertEmployee,con);
					cmd.Transaction=trans;
					SqlParameter _pcode=new SqlParameter("@pcode",SqlDbType.VarChar);
					SqlParameter _name=new SqlParameter("@name",SqlDbType.VarChar);
					SqlParameter _department=new SqlParameter("@department",SqlDbType.VarChar);
					SqlParameter _designation=new SqlParameter("@designation",SqlDbType.VarChar);
					SqlParameter _functionaltitle=new SqlParameter("@functionaltitle",SqlDbType.VarChar);
					SqlParameter _email=new SqlParameter("@Email",SqlDbType.VarChar);
					SqlParameter _JDStatus=new SqlParameter("@JDStatus",SqlDbType.VarChar);
					SqlParameter _ZameerStatus=new SqlParameter("@ZameerStatus",SqlDbType.VarChar);
					SqlParameter _emailStatus=new SqlParameter("@EmailStatus",SqlDbType.VarChar);
					SqlParameter _EmailSentDate=new SqlParameter("@EmailSentDate",SqlDbType.DateTime);
					_pcode.Value=ds.Tables[0].Rows[0]["pcode"].ToString();
					_name.Value=ds.Tables[0].Rows[0]["name"].ToString();
					_department.Value=ds.Tables[0].Rows[0]["deptname"].ToString();
					_designation.Value=ds.Tables[0].Rows[0]["designation"].ToString();
					_functionaltitle.Value=ds.Tables[0].Rows[0]["functionaltitle"].ToString();
					_JDStatus.Value=ds.Tables[0].Rows[0]["JD Status"].ToString();
					_ZameerStatus.Value=ds.Tables[0].Rows[0]["Zameer Status"].ToString();
					if(eMail.Length>0)
					{
						_email.Value=eMail;
					}
					else
					{
						_email.Value="N/A";
					}
					if(Val=="ok")
					{
						_emailStatus.Value="Send";
						_EmailSentDate.Value=DateTime.Now;
					}
					else
					{
						_emailStatus.Value="Un-Send";
						_EmailSentDate.Value=DBNull.Value;
					}
					cmd.Parameters.Add(_pcode);
					cmd.Parameters.Add(_name);
					cmd.Parameters.Add(_department);
					cmd.Parameters.Add(_designation);
					cmd.Parameters.Add(_functionaltitle);
					cmd.Parameters.Add(_email);
					cmd.Parameters.Add(_JDStatus);
					cmd.Parameters.Add(_ZameerStatus);
					cmd.Parameters.Add(_emailStatus);
					cmd.Parameters.Add(_EmailSentDate);
					cmd.ExecuteNonQuery();
				}
				catch(Exception ex)
				{
						
					Response.Write(ex.Message);
				}
			}
			else
			{
				rd2.Close();
				cmd=new SqlCommand("update t_jdstatuslog set JDStatus='Available' where pcode='"+pCode+"'",con);
				cmd.Transaction=trans;
				cmd.ExecuteNonQuery();
				if(Val=="ok")
				{
					cmd=new SqlCommand("update t_jdstatuslog set emailstatus='Send',emailsentdate=getdate() where pcode='"+pCode+"'",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
				}
				if(Val.Length<=0)
				{
					if(eMail.Length>0)
					{
						cmd=new SqlCommand("update t_jdstatuslog set emailstatus='Un-Send',Email='"+eMail+"' where pcode='"+pCode+"'",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
					}
				}
			}
			trans.Commit();
			con.Close();
		}
		public bool verifyNIC()
		{
			con.Open();
			string verify="select pcode from t_employee where nic_new='"+ this.txtNewNic.Text +"'";
			SqlCommand cmd=new SqlCommand(verify,con);
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			if(rd.HasRows)
			{
				rd.Close();
				string getRecord="select e.pcode pcode,e.name name,d.designation designation,department=case pcode when pcode then(select dept.deptname from t_department dept where dept.deptid=d.deptid)end,active=case e.del when '1' then 'Active' when '0' then 'Exited' when 2 then 'Hold/InActive'end,e.del del from t_employee e,t_designation d where e.desigid=d.desigid And e.pcode=Any(select ee.pcode from t_employee ee where ee.nic_new='"+ this.txtNewNic.Text +"')";
				SqlDataAdapter dr=new SqlDataAdapter(getRecord,con);
				DataSet dm=new DataSet();
				dr.Fill(dm,"Record");
				this.dgNIC.DataSource=dm;
				this.dgNIC.DataBind();
				con.Close();
				for(int i=0;i<dgNIC.Items.Count;i++)
				{
				 Button btn=(Button)this.dgNIC.Items[i].FindControl("btnNIC");
				 //btn.Attributes.Clear();
				 btn.Attributes.Add("onclick","return checkValue2();");
				}
				confirmation();	 
				return true; 
			}
			else
			{
				rd.Close();
				con.Close();
				return false; 
			}
		}
		public bool verifyNIC(bool flag)
		{
			con.Open();
			string verify="select pcode from t_employee where passportno='"+ this.txtPassport.Text +"'";
			SqlCommand cmd=new SqlCommand(verify,con);
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			if(rd.HasRows)
			{
				rd.Close();
				string getRecord="select e.pcode pcode,e.name name,d.designation designation,department=case pcode when pcode then(select dept.deptname from t_department dept where dept.deptid=d.deptid)end,active=case e.del when '1' then 'Active' when '0' then 'De-Active'end,e.del del from t_employee e,t_designation d where e.desigid=d.desigid And e.pcode=Any(select ee.pcode from t_employee ee where ee.passportno='"+ this.txtPassport.Text +"')";
				SqlDataAdapter dr=new SqlDataAdapter(getRecord,con);
				DataSet dm=new DataSet();
				dr.Fill(dm,"Record");
				this.dgNIC.DataSource=dm;
				this.dgNIC.DataBind();
				con.Close();
				for(int i=0;i<dgNIC.Items.Count;i++)
				{
					Button btn=(Button)this.dgNIC.Items[i].FindControl("btnNIC");
					//btn.Attributes.Clear();
					btn.Attributes.Add("onclick","return checkValue2();");
				}
				//confirmation();	 
				return true; 
			}
			else
			{
				rd.Close();
				con.Close();
				return false; 
			}
		}
		public void Proceed(object sender,EventArgs e)
		{
			if(!_isRefresh)
			{
				Button req=(Button)sender;
				for(int i=0;i<this.dgNIC.Items.Count;i++)
				{
					Button lnk=(Button)this.dgNIC.Items[i].FindControl("btnNIC");
					if(lnk.Equals(req))
					{
 
						/*if(this.dgNIC.Items[i].Cells[6].Text=="1")
						{
						Response.Redirect("UpdateEmployee.aspx?id="+ this.dgNIC.Items[i].Cells[0].Text +"");
						//string var=this.dgNIC.Items[i].Cells[0].Text;
						//Response.Write("<script>window.open(' UpdateEmployee.aspx?id="+ var +" ');</script>");
						}*/
						if(this.dgNIC.Items[i].Cells[6].Text=="0")
						{
							if(Page.IsValid)
							{
								this.lblEmp.Visible=true;
								UpdatePersonalInfo(this.dgNIC.Items[i].Cells[0].Text);
								updateJDStatus(this.txtEmpInfoCode.Text.Trim(),"","");
							}
						}
						else
						{
							this.lblEmp.Text="Only Exited Employee Can Referred";
							this.lblEmp.Visible=true;
						}
						break;
					}
				}
			}
		}

		public void confirmation()
		{
			for(int i=0;i<this.dgNIC.Items.Count;i++)
			{
				if(this.dgNIC.Items[i].Cells[6].Text=="0")
				{
					Button btn=(Button)this.dgNIC.Items[i].FindControl("btnNIC");
					btn.Attributes.Add("onclick","return checkValue2();"); 
				}
				if(this.dgNIC.Items[i].Cells[6].Text=="1")
				{
					Button btn=(Button)this.dgNIC.Items[i].FindControl("btnNIC");
					btn.Attributes.Add("onclick","window.open('UpdateEmployee.aspx?id="+ this.dgNIC.Items[i].Cells[0].Text +"');return false;");
				}
			}
		}
		private string NewEmployeeID(int typeofemployment)
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			string [] etype={"P", "C", "R", "H","T"};
			string et="";
			
			if (typeofemployment>=1 && typeofemployment<=5)
			{
				typeofemployment--;
				et=etype[typeofemployment];
				/*string sql="SELECT CASE MAX(CAST(REPLACE(pcode, '"+et+"', '') AS int)) WHEN NULL THEN 1 ELSE MAX(CAST(REPLACE(pcode, '"+et+"', '') AS int)) + 1 END AS NewID " +
					" FROM dbo.t_Employee " +
					" WHERE (CHARINDEX('"+et+"', pcode) > 0)";*/
				string sql="SELECT CASE WHEN (SELECT MAX(CAST(REPLACE(pcode, '"+et+"', '') AS int)) "+
					"FROM dbo.t_Employee WHERE (pcode LIKE '"+et+"%')) IS NULL THEN 1 ELSE "+
					"((SELECT MAX(CAST(REPLACE(pcode, '"+et+"', '') AS int)) "+
					"FROM dbo.t_Employee "+
					"WHERE (pcode LIKE '"+et+"%'))+1) END AS NewID";
				SqlDataAdapter da =new SqlDataAdapter(sql,conn);
				DataSet ds=new DataSet();
				da.Fill(ds,"ids");
				if(ds.Tables["ids"].Rows.Count>0)
				{
					if(ds.Tables["ids"].Rows[0]["newid"].ToString()=="")
					{
						//txtCode.Text=ddlEmploymentType.SelectedValue+"1";
						return et+"1";
					}
					else
					{
						//txtCode.Text=ddlEmploymentType.SelectedValue+ds.Tables["ids"].Rows[0]["newid"].ToString();
						return et+ds.Tables["ids"].Rows[0]["newid"].ToString();
					}
				}
				else
				{
					//txtCode.Text=ddlEmploymentType.SelectedValue+"1";
					return et+"1";
				}
			}
			else
			{
				//txtCode.Text="";
				return "";
			}
		}
		private void CreateThumbnail(string file)
		{
			System.Drawing.Image ObjImage;
			System.Drawing.Image Objthumbnail;
			string Path=Server.MapPath(@"..\employee\"+file); 
			ObjImage=System.Drawing.Image.FromFile(Path);
			Objthumbnail=ObjImage.GetThumbnailImage(100,120,null,System.IntPtr.Zero);
			Response.ContentType="image/jpeg";
			//Objthumbnail.Save(Response.OutputStream,System.Drawing.Imaging.ImageFormat.Jpeg);
			Path=Server.MapPath(@"..\");
			try
			{
				Objthumbnail.Save(Path+"\\EmpThumbnail\\"+file,System.Drawing.Imaging.ImageFormat.Jpeg);
			}
			catch(Exception ex)
			{
				
				Response.Write(ex.Message);
			}
			ObjImage.Dispose();
			Objthumbnail.Dispose();
            Response.ContentType="text/html";
		}

		public void UpdatePersonalInfo(string _pcodes)
		{
			string output="";
			
			con.Open();
			SqlTransaction trans=con.BeginTransaction();
			try
			{								
				string InsertEmp="INSERT INTO dbo.t_Employee "+
					"(pcode, desigid, supervisor, station, name, dateofjoin, dateofconfirmation, dateofexit, category, del, dateofbirth, gender, bloodgroup, passportno, "+
					"nic_old, nic_new, address, extension, typeofemployment, fathername, maritialstatus, religion, niccity, telephone, mobile, email_personal, kin, "+
					"admincity, operatingcity, bondpaper, email_official, bankaccountno, bankaccountdetails, ntnno, eobino, sessino, insurancecode, pic, levels, "+ 
					"compansatoryoff, basicsalary, grosssalary, houserent, utilities, lastappraisaldate, lastappper, lasthrgrade, lastposition, evaluationmonths, "+
					"appremarks,Nationality,Nick,p_id,interim,nationality2,profilecreateby,probabtionPeriod,probabtionPeriodOther,finalconfirmationdate,isnew,tentativedate,istobejoined,positiontype,refprofilecode,refprofile) "+
					"VALUES (@pcode, @desigid, NULL, @station, @name, @dateofjoin, @dateofconfirmation, NULL, NULL, 1, @dateofbirth, @gender, @bloodgroup, "+
					"@passportno, @nic_old, @nic_new, @address, @extension, @typeofemployment, @fathername, @maritialstatus, @religion, NULL, @telephone, "+
					"@mobile, @email_personal, @kin, @admincity, @operatingcity, NULL, @email_official, @bankaccountno, @bankaccountdetails, @ntnno, "+
					"@eobino, @sessino, @insurancecode, @pic, NULL, @compansatoryoff, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL,@nationality,@nick,@p_id,@interim,@nationality2,@profilecreateby,@probabtionPeriod,@probabtionPeriodOther,@finalconfirmationdate,@isnew,@tentativedate,@istobejoined,@posType,@refCode,@refProfile)";
				SqlCommand cmd=new SqlCommand(InsertEmp,con);
				SqlParameter _pcode=new SqlParameter("@pcode",SqlDbType.Char);
				SqlParameter _desigid=new SqlParameter("@desigid",SqlDbType.Int);
				SqlParameter _station=new SqlParameter("@station",SqlDbType.Int);
				SqlParameter _name=new SqlParameter("@name",SqlDbType.VarChar);
				SqlParameter _dateofjoin=new SqlParameter("@dateofjoin",SqlDbType.SmallDateTime);
				SqlParameter _dateofconfirmation=new SqlParameter("@dateofconfirmation",SqlDbType.DateTime);
				SqlParameter _dateofbirth=new SqlParameter("@dateofbirth",SqlDbType.DateTime);
				SqlParameter _gender=new SqlParameter("@gender",SqlDbType.Int);
				SqlParameter _bloodgroup=new SqlParameter("@bloodgroup",SqlDbType.Int);
				SqlParameter _passport=new SqlParameter("@passportno",SqlDbType.VarChar);
				SqlParameter _nic_old=new SqlParameter("@nic_old",SqlDbType.VarChar);
				SqlParameter _nic_new=new SqlParameter("@nic_new",SqlDbType.VarChar);
				SqlParameter _address=new SqlParameter("@address",SqlDbType.VarChar);
				SqlParameter _extension=new SqlParameter("@extension",SqlDbType.VarChar);
				SqlParameter _typeofemployment=new SqlParameter("@typeofemployment",SqlDbType.Int);
				SqlParameter _fathername=new SqlParameter("@fathername",SqlDbType.VarChar);
				SqlParameter _maritialstatus=new SqlParameter("@maritialstatus",SqlDbType.TinyInt);
				SqlParameter _religion=new SqlParameter("@religion",SqlDbType.Int);
				SqlParameter _telephone=new SqlParameter("@telephone",SqlDbType.VarChar);
				SqlParameter _mobile=new SqlParameter("@mobile",SqlDbType.VarChar);
				SqlParameter _email_personal=new SqlParameter("@email_personal",SqlDbType.VarChar);
				SqlParameter _kin=new SqlParameter("@kin",SqlDbType.VarChar);
				SqlParameter _admincity=new SqlParameter("@admincity",SqlDbType.Int);
				SqlParameter _operatingcity=new SqlParameter("@operatingcity",SqlDbType.Text);
				SqlParameter _email_official=new SqlParameter("@email_official",SqlDbType.VarChar);
				SqlParameter _bankaccountno=new SqlParameter("@bankaccountno",SqlDbType.VarChar);
				SqlParameter _bankaccountdetails=new SqlParameter("@bankaccountdetails",SqlDbType.VarChar);
				SqlParameter _ntnno=new SqlParameter("@ntnno",SqlDbType.VarChar);
				SqlParameter _eobino=new SqlParameter("@eobino",SqlDbType.VarChar);
				SqlParameter _sessino=new SqlParameter("@sessino",SqlDbType.VarChar);
				SqlParameter _insurancecode=new SqlParameter("@insurancecode",SqlDbType.VarChar);
				SqlParameter _pic=new SqlParameter("@pic",SqlDbType.VarChar);
				SqlParameter _compensatoryoff=new SqlParameter("@compansatoryoff",SqlDbType.Int);
				SqlParameter _nationality=new SqlParameter("@nationality",SqlDbType.Int);
				SqlParameter _nick=new SqlParameter("@nick",SqlDbType.VarChar);
				SqlParameter _p_id=new SqlParameter("@p_id",SqlDbType.Char,10);
				SqlParameter _interim=new SqlParameter("@interim",SqlDbType.SmallDateTime);
				SqlParameter _nationality2=new SqlParameter("@nationality2",SqlDbType.Int);
				SqlParameter _profilecreateby=new SqlParameter("@profilecreateby",SqlDbType.Char);
				SqlParameter _probabtionPeriod=new SqlParameter("@probabtionPeriod",SqlDbType.Int);
				SqlParameter _probabtionPeriodOther=new SqlParameter("@probabtionPeriodOther",SqlDbType.Int);
				SqlParameter _finalconfirmationdate=new SqlParameter("@finalconfirmationdate",SqlDbType.DateTime);
				SqlParameter _isnew=new SqlParameter("@isnew",SqlDbType.TinyInt);
				SqlParameter _tentativedate=new SqlParameter("@tentativedate",SqlDbType.SmallDateTime);
				SqlParameter _istobejoined=new SqlParameter("@istobejoined",SqlDbType.TinyInt);
				SqlParameter _posType=new SqlParameter("@posType",SqlDbType.Int);
				SqlParameter _refCode=new SqlParameter("@refCode",SqlDbType.VarChar);
				SqlParameter _refProfile=new SqlParameter("@refProfile",SqlDbType.VarChar);
				if(txtEmpInfoTOE.SelectedValue.ToString() !="0")
				{
					this.txtEmpInfoCode.Text=NewEmployeeID(Int32.Parse(this.txtEmpInfoTOE.SelectedValue.ToString())); 
					_pcode.Value=this.txtEmpInfoCode.Text;
				}
				_desigid.Value=this.ddDesignation.SelectedValue.ToString();
				_station.Value=ddOpertingCity.SelectedValue.ToString();
				_name.Value=txtEmpInfoName.Text;
				if(this.CalenderDOJ.ValidDateEntered)
				{
					_dateofjoin.Value=CalenderDOJ.SelectedDate.ToString();
				}
				else
				{
					_dateofjoin.Value=DBNull.Value;
				}
				if(this.CalenderCOD.SelectedDate.ToString()=="1/1/0001 12:00:00 AM")
				{
					_dateofconfirmation.Value=DBNull.Value;
				}
				else
				{
					_dateofconfirmation.Value=CalenderCOD.SelectedDate.ToString();
				}
				_dateofbirth.Value=this.calendarDOB.SelectedDate.ToString();
				_gender.Value=this.RadioButtonList1.SelectedValue.ToString();
				_bloodgroup.Value=this.ddBloodGrp.SelectedValue.ToString();
				_passport.Value=this.txtPassport.Text;
				_nic_old.Value=this.txtOldNic.Text;
				_nic_new.Value=this.txtNewNic.Text;
				_address.Value=this.txtAddress.Text;
				_extension.Value=this.txtEmpInfoExtension.Text;
				_typeofemployment.Value=this.txtEmpInfoTOE.SelectedValue.ToString();
				_fathername.Value=this.txtFName.Text;
				_maritialstatus.Value=this.ddStatus.SelectedValue.ToString();
				_religion.Value=this.ddReligion.SelectedValue.ToString();
				_telephone.Value=this.txtContactNo.Text;
				_mobile.Value=this.txtMobile.Text;
				_email_personal.Value=txtEmail.Text;
				_kin.Value=txtKin.Text;
				_admincity.Value=ddNICcity.SelectedValue.ToString();
				_operatingcity.Value=txtEmpLocation.Text;
				_email_official.Value=txtEmpInfoEmail.Text;
				_bankaccountno.Value=txtBankAcctNo.Text;
				_bankaccountdetails.Value=txtAccountDetails.Text;
				_ntnno.Value=txtNTN.Text;
				_eobino.Value=txtEmpInfoEOBI.Text ;
				_sessino.Value=txtEmpInfoSESSI.Text;
				_insurancecode.Value=txtEmpInfoInsurance.Text;
				string filepath=Server.MapPath(@"..\employee\");
				filename=txtEmpInfoCode.Text.Trim() + ".jpg";
				if((FileToUpload.PostedFile != null) && (FileToUpload.PostedFile.ContentLength > 0))
				{
					FileToUpload.PostedFile.SaveAs(filepath + filename);
					path=filepath + filename;
					CreateThumbnail(filename);
					this.Image1.ImageUrl=path;
					this.Image1.Visible=true;
					//===================Email Picture=====================//
					/*MailMessage mm=new MailMessage();
					mm.From="<EMAIL>";
					mm.To=System.Configuration.ConfigurationSettings.AppSettings["PictureEmail"].ToString();
					mm.Subject="Employee Picture of "+txtEmpInfoCode.Text;
					mm.Body="Employee Picture of "+txtEmpInfoCode.Text;
					mm.BodyFormat=MailFormat.Html;
					MailAttachment mi=new MailAttachment(path);
					mm.Attachments.Add(mi);
					SmtpMail.Send(mm);*/
					//=====================================================//
				} 
				_pic.Value=filename;
				_compensatoryoff.Value=rdCompansatory.SelectedValue.ToString();
				_nationality.Value=this.ddNationality.SelectedValue.ToString();
				_nick.Value=this.txtNick.Text;
				_pcodes=_pcodes.Trim();
				if(_pcodes!="NULL")
				{
					_p_id.Value=_pcodes.Trim();
				}
				else
				{
					_p_id.Value=DBNull.Value;
				}
				if(this.InterimCommitment.SelectedDate.ToString()!="1/1/0001 12:00:00 AM")
				{
					_interim.Value=this.InterimCommitment.SelectedDate.ToString();
				}
				else
				{
					_interim.Value=DBNull.Value;
				}
				if(this.ddNationality2.SelectedValue !="0")
				{
					_nationality2.Value=this.ddNationality2.SelectedValue.ToString();
				}
				else
				{
					_nationality2.Value=DBNull.Value;
				}
				_profilecreateby.Value=Session["user_id"].ToString();
				_probabtionPeriod.Value=this.ddProbabtion.SelectedValue.ToString();
				if(this.txtProbabtionOther.Text.Length>0)
				{
					_probabtionPeriodOther.Value=this.txtProbabtionOther.Text;
				}
				else
				{
					_probabtionPeriodOther.Value=DBNull.Value;
				}
				if(calenderFinalConfirmation.ValidDateEntered)
				{
					_finalconfirmationdate.Value=calenderFinalConfirmation.SelectedDate.ToString("d MMM,yyyy");
				}
				else
				{
					_finalconfirmationdate.Value=DBNull.Value;
				}
				if(ddNewJoiner.Enabled)
				{
					_isnew.Value=this.ddNewJoiner.SelectedValue.ToString();
				}
				else
				{
					_isnew.Value=DBNull.Value;
				}
				if(this.CTDOJ.ValidDateEntered)
				{
					_tentativedate.Value=this.CTDOJ.SelectedDate.ToString("d MMM,yyyy");
				}
				else
				{
					_tentativedate.Value=DBNull.Value;
				}
				_istobejoined.Value=this.ddeStatus.SelectedValue.ToString();
				_posType.Value=ddPosType.SelectedValue.ToString();
				if(txtName.Text.Length>0)
				{
					string []refProfileCode=txtName.Text.Split('-');
					_refCode.Value=refProfileCode[0];
					_refProfile.Value=txtName.Text;
				}
				else
				{
					_refCode.Value=DBNull.Value;
					_refProfile.Value=DBNull.Value;
				}
				cmd.Parameters.Add(_pcode);
				cmd.Parameters.Add(_desigid);
				cmd.Parameters.Add(_station);
				cmd.Parameters.Add(_name);
				cmd.Parameters.Add(_dateofjoin);
				cmd.Parameters.Add(_dateofconfirmation);
				cmd.Parameters.Add(_dateofbirth);
				cmd.Parameters.Add(_gender);
				cmd.Parameters.Add(_bloodgroup);
				cmd.Parameters.Add(_passport);
				cmd.Parameters.Add(_nic_old);
				cmd.Parameters.Add(_nic_new);
				cmd.Parameters.Add(_address);
				cmd.Parameters.Add(_extension);
				cmd.Parameters.Add(_typeofemployment);
				cmd.Parameters.Add(_fathername);
				cmd.Parameters.Add(_maritialstatus);
				cmd.Parameters.Add(_religion);
				cmd.Parameters.Add(_telephone);
				cmd.Parameters.Add(_mobile);
				cmd.Parameters.Add(_email_personal);
				cmd.Parameters.Add(_kin);
				cmd.Parameters.Add(_admincity);
				cmd.Parameters.Add(_operatingcity);
				cmd.Parameters.Add(_email_official);
				cmd.Parameters.Add(_bankaccountno);
				cmd.Parameters.Add(_bankaccountdetails);
				cmd.Parameters.Add(_ntnno);
				cmd.Parameters.Add(_eobino);
				cmd.Parameters.Add(_sessino);
				cmd.Parameters.Add(_insurancecode);
				cmd.Parameters.Add(_pic);
				cmd.Parameters.Add(_compensatoryoff); 
				cmd.Parameters.Add(_nationality);
				cmd.Parameters.Add(_nick);
				cmd.Parameters.Add(_p_id);
				cmd.Parameters.Add(_interim);
				cmd.Parameters.Add(_nationality2);
				cmd.Parameters.Add(_profilecreateby);
				cmd.Parameters.Add(_probabtionPeriod);
				cmd.Parameters.Add(_probabtionPeriodOther);
				cmd.Parameters.Add(_finalconfirmationdate);
				cmd.Parameters.Add(_isnew);
				cmd.Parameters.Add(_tentativedate);
				cmd.Parameters.Add(_istobejoined);
				cmd.Parameters.Add(_posType);
				cmd.Parameters.Add(_refCode);
				cmd.Parameters.Add(_refProfile);
				cmd.Transaction=trans;
				int i=cmd.ExecuteNonQuery();
				//int i=1;
				if(this.ddDesignation.SelectedValue !="0" && this.ddFDesignation.SelectedValue !="0" && i>0)
				{
					string var="Null";
					string InsertHistory=" insert into t_designationhistory values('"+ this.txtEmpInfoCode.Text +"','"+ this.ddFDesignation.SelectedValue.ToString() +"','"+ DateTime.Now.ToString() +"',"+ var +",'"+ 1 +"','"+Session["user_id"].ToString()+"',"+var+","+var+")";
					SqlCommand hCom=new SqlCommand(InsertHistory,con);
					hCom.Transaction=trans;
					hCom.ExecuteNonQuery();
				}
				//=======================Insert into HRIS=================================//
				string Mstatus="";
				string Religion="";
				
				if(this.ddStatus.SelectedValue.ToString()!="0")
				{
					Mstatus=this.ddStatus.SelectedItem.Text;
				}
				if(this.ddReligion.SelectedValue.ToString()!="0")
				{
					Religion=this.ddReligion.SelectedItem.Text;
				}
				if(this.CalenderDOJ.ValidDateEntered)
				{
					HRISService.Employee service=new GeoRabtaSite.HRISService.Employee();
					output=service.AddEmployee(_name.Value.ToString(),_fathername.Value.ToString(),_dateofbirth.Value.ToString(),this.RadioButtonList1.SelectedItem.Text,Mstatus,Religion,_nic_new.Value.ToString(),_nic_old.Value.ToString(),_address.Value.ToString(),this.ddOpertingCity.SelectedItem.Text,_telephone.Value.ToString(),_mobile.Value.ToString(),_email_personal.Value.ToString(),_kin.Value.ToString(),getCategory(Int32.Parse(this.ddDesignation.SelectedValue.ToString()),trans),getDeptNo(Int32.Parse(this.ddDepartment.SelectedValue.ToString()),trans),this.txtEmpInfoCode.Text.Substring(0,1),this.txtEmpInfoCode.Text.Substring(1,this.txtEmpInfoCode.Text.Length-1),this.ddDesignation.SelectedItem.Text,this.ddOpertingCity.SelectedItem.Text,this.ddOpertingCity.SelectedItem.Text,_dateofjoin.Value.ToString(),_dateofconfirmation.Value.ToString(),_finalconfirmationdate.Value.ToString(),"",this.rdCompansatory.SelectedValue.ToString(),_email_official.Value.ToString(),_bankaccountno.Value.ToString(),_bankaccountdetails.Value.ToString(),_ntnno.Value.ToString(),_eobino.Value.ToString(),_sessino.Value.ToString(),_insurancecode.Value.ToString());
				}
						
				//========================================================================// 
				//output="Ok";
				if(!this.CTDOJ.ValidDateEntered)
				{
					if(output!="Ok")
					{
						trans.Rollback();
						this.lblVacancy.Visible=true;
						this.lblVacancy.Text="System failed to Add record. Error Occured: "+output;
						return;
					}
					else
					{
						GeoRabtaSite.admin.TISIntegrator tis=new GeoRabtaSite.admin.TISIntegrator();
						string mStatus="";
						if(this.ddStatus.SelectedItem.Value!="0")
						{
							mStatus=this.ddStatus.SelectedItem.Text;
						}
						if(tis.AddEmployee(this.txtEmpInfoCode.Text,this.txtEmpInfoName.Text,this.RadioButtonList1.SelectedItem.Text,mStatus,1,this.CalenderDOJ.SelectedDate.ToShortDateString(),this.CalenderCOD.SelectedDate.ToShortDateString(),"",Int32.Parse(this.ddOpertingCity.SelectedItem.Value),Int32.Parse(this.ddDepartment.SelectedItem.Value),this.ddDesignation.SelectedItem.Text,0,ddNetwork.SelectedItem.Value))
						{
						 
						}
						else
						{
							trans.Rollback();
							this.lblVacancy.Visible=true;
							this.lblVacancy.Text="System failed to Add record";
							return;
						} 
					}
				}
				trans.Commit();
				con.Close();
				if (i>0)
				{
					if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Family")==true)
					{
						ImageButton3.Visible=true;
					}
					if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Educationalinfo")==true)
					{
						ImageButton4.Visible=true;
					}
					if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Bond")==true)
					{
						ImageButton6.Visible=true;
					}
					//					if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Functional")==true)
					//					{
					//						ImageButton7.Visible=false;
					//					}
					if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Account")==true)
					{
						ImageButton5.Visible=true;
					}
					if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Compensation")==true)
					{
						ImgCompensation.Visible=true;
					}
					if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Contract")==true)
					{
						imgContract.Visible=true;
					}
					this.lblVacancy.Visible=true;
					this.btnUpdate.Enabled=false;
					ImgRelative.Visible=true;
				}
			}
			catch(Exception ex)
			{
				Response.Write(ex.Message+"HRIS->"+output);
				trans.Rollback();
			}
		}
		public string getCategory(int DesID,SqlTransaction tran)
		{
			//con.Open();
			string Val="";
			SqlCommand cmd=new SqlCommand("select c.cat_name from t_categorization c,t_designation d where d.category=c.cat_id and d.desigid="+DesID+"",con);
			cmd.Transaction=tran;
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			//con.Close();
			Val=rd[0].ToString();
			rd.Close();
			return Val;
		}
		public string getDeptNo(int DepID,SqlTransaction tran)
		{
			//con.Open();
			string Val="";
			SqlCommand cmd=new SqlCommand("select deptno from t_HrisDeptMapping where raabtaid="+DepID+"",con);
			cmd.Transaction=tran;
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			//con.Close();
			Val=rd[0].ToString();
			rd.Close();
			return Val;
		}
		private void btnFupdate_Click(object sender, System.EventArgs e)
		{
			if(!_isRefresh)
			{
				this.ValidationSummary1.Visible=true;
				CustomValidator8.Enabled=true;
				CustomValidator6.Enabled=true;
				CustomValidator7.Enabled=true; 
				Page.Validate();
			
				if(Page.IsValid)
				{ 
					if(this.txtFamilyName.Text.Length>0)
					{
						this.lblSpouse.Visible=false;
						this.ValidationSummary1.Visible=false;
						string var="NULL";
						if(CalendarPopup1.SelectedDate.ToString()!="1/1/0001 12:00:00 AM")
						{
							var="'"+DateTime.Parse(this.CalendarPopup1.SelectedDate.ToString())+"'";
						}
						con.Open();
						string empId=txtEmpInfoCode.Text;
						Session["user_id"].ToString();
						//string updateFamily="update t_familydetails set name='" + txtFamilyName.Text + "',relationship=" + rdRelation.SelectedValue.ToString() + ",maritialstatus=" + rdStatus.SelectedValue.ToString() + ",gender=" + rdFamilyGender.SelectedValue.ToString() + ",dependent='"+ this.ddDependent.SelectedValue.ToString() +"',dob="+ var +",occupation='"+this.txtOccupation.Text+"' where fdid=" + Session["familyId"].ToString() + "";
						string updateFamily="update t_familydetails set name=@p_name,relationship=@p_relationship,maritialstatus=@p_maritialstatus,gender=@p_gender,dependent=@p_dependent,dob=@p_dob,occupation=@p_occupation,modifiedby=@modifiedby where fdid=@fdid";
						SqlCommand cmd=new SqlCommand(updateFamily,con);
						SqlParameter p_name= new SqlParameter("@p_name",SqlDbType.VarChar, 1000); 
						p_name.Value=this.txtFamilyName.Text;
						SqlParameter p_relationship= new SqlParameter("@p_relationship",SqlDbType.Int, 4); 
						p_relationship.Value=rdRelation.SelectedValue.ToString();
						SqlParameter p_maritialstatus= new SqlParameter("@p_maritialstatus",SqlDbType.TinyInt, 1); 
						p_maritialstatus.Value=rdStatus.SelectedValue.ToString();
						SqlParameter p_gender= new SqlParameter("@p_gender",SqlDbType.TinyInt, 1); 
						p_gender.Value=rdFamilyGender.SelectedValue.ToString();
						SqlParameter p_occupation= new SqlParameter("@p_occupation",SqlDbType.VarChar, 1000); 
						p_occupation.Value=txtOccupation.Text;
						SqlParameter p_dob= new SqlParameter("@p_dob",SqlDbType.SmallDateTime, 8); 
						p_dob.Value=var;
						SqlParameter p_Dependent= new SqlParameter("@p_Dependent",SqlDbType.TinyInt, 1); 
						p_Dependent.Value=this.ddDependent.SelectedValue.ToString();
						SqlParameter _fdid=new SqlParameter("@fdid",SqlDbType.Int);
						_fdid.Value=Session["familyId"].ToString();
						SqlParameter p_modifiedby= new SqlParameter("@modifiedby",SqlDbType.Char); 
						p_modifiedby.Value=Session["user_id"].ToString();
						cmd.Parameters.Add(p_name);
						cmd.Parameters.Add(p_relationship);
						cmd.Parameters.Add(p_maritialstatus);
						cmd.Parameters.Add(p_gender);
						cmd.Parameters.Add(p_occupation);
						cmd.Parameters.Add(p_dob);
						cmd.Parameters.Add(p_Dependent);
						cmd.Parameters.Add(_fdid);
						cmd.Parameters.Add(p_modifiedby);
						int k=cmd.ExecuteNonQuery();
						con.Close();
						FamilyInfo();
						btnFupdate.Enabled=false;
						btnNew.Enabled=true;
						txtFamilyName.Text="";
						txtOccupation.Text="";
						for(int i=0;i<rdFamilyGender.Items.Count;i++)
						{
							rdFamilyGender.Items[i].Selected=false;
						}
						for(int i=0;i<rdRelation.Items.Count;i++)
						{
							rdRelation.Items[i].Selected=false;
						}
						for(int i=0;i<rdStatus.Items.Count;i++)
						{
							rdStatus.Items[i].Selected=false;
						}
						this.ddDependent.SelectedIndex=0;
						this.CalendarPopup1.Reset();
					}
					else
					{
						this.lblSpouse.Visible=true;
					}
				}
				string errorString="";
				errorString=FamVerification();
				if(errorString.Length>0)
				{
					errorString=errorString.Remove(errorString.Length-1,1);
					Response.Write("<script>window.alert('"+ errorString +"');</script>"); 
				}
			}
			else
			{
				FamilyInfo();
				btnFupdate.Enabled=false;
				btnNew.Enabled=true;
				txtFamilyName.Text="";
				txtOccupation.Text="";
				for(int i=0;i<rdFamilyGender.Items.Count;i++)
				{
					rdFamilyGender.Items[i].Selected=false;
				}
				for(int i=0;i<rdRelation.Items.Count;i++)
				{
					rdRelation.Items[i].Selected=false;
				}
				for(int i=0;i<rdStatus.Items.Count;i++)
				{
					rdStatus.Items[i].Selected=false;
				}
				this.ddDependent.SelectedIndex=0;
				this.CalendarPopup1.Reset();
			}
		}

		private void btnNew_Click(object sender, System.EventArgs e)
		{
			if(!_isRefresh)
			{
				this.ValidationSummary1.Visible=true;
				CustomValidator8.Enabled=true;
				CustomValidator6.Enabled=true;
				CustomValidator7.Enabled=true; 
				Page.Validate();
				if(Page.IsValid)
				{ 
					if(this.txtFamilyName.Text.Length>0)
					{
						this.lblSpouse.Visible=false;
						this.ValidationSummary1.Visible=false;
						string var="NULL";
						if(CalendarPopup1.SelectedDate.ToString()!="1/1/0001 12:00:00 AM")
						{
							var="'"+DateTime.Parse(this.CalendarPopup1.SelectedDate.ToString())+"'";
						}
						con.Open();
						string empId=txtEmpInfoCode.Text;
						//string updateFamily="insert into t_familydetails values('" + empId + "','" + txtFamilyName.Text + "'," + rdRelation.SelectedValue.ToString() + "," + rdStatus.SelectedValue.ToString() + "," + rdFamilyGender.SelectedValue.ToString() + ",'" + txtOccupation.Text + "',"+ var +",'" + 1 + "','"+ this.ddDependent.SelectedValue.ToString() +"')";
						string updateFamily="INSERT INTO dbo.t_FamilyDetails "+
							"(pcode, name, relationship, maritialstatus, gender, occupation, dob, isactive, Dependent,createdby) "+
							"VALUES (@p_pcode, @p_name, @p_relationship, @p_maritialstatus, @p_gender, @p_occupation, @p_dob, @p_isactive, @p_Dependent,@createdby)";
						SqlCommand cmd=new SqlCommand(updateFamily,con);
						SqlParameter p_pcode= new SqlParameter("@p_pcode",SqlDbType.Char, 10); 
						p_pcode.Value=empId;
						SqlParameter p_name= new SqlParameter("@p_name",SqlDbType.VarChar, 1000); 
						p_name.Value=this.txtFamilyName.Text;
						SqlParameter p_relationship= new SqlParameter("@p_relationship",SqlDbType.Int, 4); 
						p_relationship.Value=rdRelation.SelectedValue.ToString();
						SqlParameter p_maritialstatus= new SqlParameter("@p_maritialstatus",SqlDbType.TinyInt, 1); 
						p_maritialstatus.Value=rdStatus.SelectedValue.ToString();
						SqlParameter p_gender= new SqlParameter("@p_gender",SqlDbType.TinyInt, 1); 
						p_gender.Value=rdFamilyGender.SelectedValue.ToString();
						SqlParameter p_occupation= new SqlParameter("@p_occupation",SqlDbType.VarChar, 1000); 
						p_occupation.Value=txtOccupation.Text;
						SqlParameter p_dob= new SqlParameter("@p_dob",SqlDbType.SmallDateTime, 8); 
						p_dob.Value=var;
						SqlParameter p_isactive= new SqlParameter("@p_isactive",SqlDbType.TinyInt, 1); 
						p_isactive.Value=1;
						SqlParameter p_Dependent= new SqlParameter("@p_Dependent",SqlDbType.TinyInt, 1); 
						p_Dependent.Value=this.ddDependent.SelectedValue.ToString();
						SqlParameter p_createdby= new SqlParameter("@createdby",SqlDbType.Char, 10); 
						p_createdby.Value=Session["user_id"].ToString();

						cmd.Parameters.Add(p_pcode);
						cmd.Parameters.Add(p_name);
						cmd.Parameters.Add(p_relationship);
						cmd.Parameters.Add(p_maritialstatus);
						cmd.Parameters.Add(p_gender);
						cmd.Parameters.Add(p_occupation);
						cmd.Parameters.Add(p_dob);
						cmd.Parameters.Add(p_isactive);
						cmd.Parameters.Add(p_Dependent);
						cmd.Parameters.Add(p_createdby);
						int k=cmd.ExecuteNonQuery();
						con.Close();
						FamilyInfo(); 
 
						txtFamilyName.Text="";
						txtOccupation.Text="";
						for(int i=0;i<rdFamilyGender.Items.Count;i++)
						{
							rdFamilyGender.Items[i].Selected=false;
						}
						for(int i=0;i<rdRelation.Items.Count;i++)
						{
							rdRelation.Items[i].Selected=false;
						}
						for(int i=0;i<rdStatus.Items.Count;i++)
						{
							rdStatus.Items[i].Selected=false;
						}
						this.CalendarPopup1.Reset();
						this.ddDependent.SelectedIndex=0;
					}
					else
					{
						this.lblSpouse.Visible=true;
					}
				}
				string errorString="";
				errorString=FamVerification();
				if(errorString.Length>0)
				{
					errorString=errorString.Remove(errorString.Length-1,1);
					Response.Write("<script>window.alert('"+ errorString +"');</script>"); 
				}
			}
			else
			{
				FamilyInfo(); 
				txtFamilyName.Text="";
				txtOccupation.Text="";
				for(int i=0;i<rdFamilyGender.Items.Count;i++)
				{
					rdFamilyGender.Items[i].Selected=false;
				}
				for(int i=0;i<rdRelation.Items.Count;i++)
				{
					rdRelation.Items[i].Selected=false;
				}
				for(int i=0;i<rdStatus.Items.Count;i++)
				{
					rdStatus.Items[i].Selected=false;
				}
				this.CalendarPopup1.Reset();
				this.ddDependent.SelectedIndex=0;
			}
		}

		private void btnEdUpdate_Click(object sender, System.EventArgs e)
		{
			if(!_isRefresh)
			{
				CustomValidator9.Enabled=true;
				CustomValidator11.Enabled=true;
				CustomValidator10.Enabled=true;
				CustomValidator13.Enabled=true;
				CustomValidator14.Enabled=true; 
				Page.Validate();
				this.ValidationSummary1.Visible=true;
				if(Page.IsValid)
				{
					this.ValidationSummary1.Visible=false;
					con.Open();
					string empId=txtEmpInfoCode.Text;
					//string updateInfo="update t_educationinfo set pcode='"+empId+"',degree='"+this.txtDegree.SelectedValue.ToString()+"',institute='"+this.txtInstitute.SelectedValue.ToString()+"',durationto='"+this.DurationTo.SelectedValue.ToString()+"',result='"+this.txtResult.SelectedValue.ToString()+"',durationfrom='"+this.DurationFrm.SelectedValue.ToString()+"',majors='"+this.txtOtherMajors.Text+"',achievements='"+this.txtAchievements.Text+"',isactive='"+1+"',otherdegree='"+this.txtOtherDegree.Text+"',otherinstitute='"+this.txtOtherInstitute.Text+"',othermajors='"+this.txtOtherMajors.Text+"' where eduinfoid='"+Convert.ToInt32(Session["educationId"].ToString())+"'";
					string updateInfo="update t_educationinfo set pcode=@p_code,degree=@p_degree,institute=@p_institute,durationto=@p_DurationTo,result=@p_result,durationfrom=@p_DurationFrom,majors=@p_majors,achievements=@p_achievement,otherdegree=@p_OtherDegree,otherinstitute=@p_OtherInstitute,othermajors=@p_OtherMajors,modifiedby=@modifiedby where eduinfoid=@eduinfoid";
					SqlCommand cmd=new SqlCommand(updateInfo,con);
					SqlParameter p_pcode= new SqlParameter("@p_code",SqlDbType.Char, 10); 
					p_pcode.Value=empId;
					SqlParameter p_degree= new SqlParameter("@p_degree",SqlDbType.Int, 4); 
					p_degree.Value=this.txtDegree.SelectedValue.ToString().Trim();
					SqlParameter p_institute= new SqlParameter("@p_institute",SqlDbType.Int, 4); 
					p_institute.Value=this.txtInstitute.SelectedValue.ToString().Trim();
					SqlParameter p_DurationFrom= new SqlParameter("@p_DurationFrom",SqlDbType.Int, 4); 
					p_DurationFrom.Value=DurationFrm.SelectedValue.ToString();
					SqlParameter p_DurationTo= new SqlParameter("@p_DurationTo",SqlDbType.Int, 4); 
					p_DurationTo.Value=DurationTo.SelectedValue.ToString(); 
					SqlParameter p_result= new SqlParameter("@p_result",SqlDbType.VarChar, 100); 
					p_result.Value=this.txtResult.SelectedValue.ToString().Trim();
					SqlParameter p_majors= new SqlParameter("@p_majors",SqlDbType.VarChar, 50); 
					p_majors.Value=this.txtOtherMajors.Text;
					SqlParameter p_achievements= new SqlParameter("@p_achievement",SqlDbType.VarChar,1000); 
					p_achievements.Value=this.txtAchievements.Text;
					SqlParameter p_OtherDegree= new SqlParameter("@p_OtherDegree",SqlDbType.VarChar, 50); 
					p_OtherDegree.Value=this.txtOtherDegree.Text;
					SqlParameter p_OtherInstitute= new SqlParameter("@p_OtherInstitute",SqlDbType.VarChar, 50); 
					p_OtherInstitute.Value=this.txtOtherInstitute.Text;
					SqlParameter p_OtherMajors= new SqlParameter("@p_OtherMajors",SqlDbType.VarChar, 50); 
					p_OtherMajors.Value=this.txtOtherMajors.Text;
					SqlParameter _eduinfoid=new SqlParameter("@eduinfoid",SqlDbType.Int);
					_eduinfoid.Value=Session["educationId"].ToString();
					SqlParameter p_modifiedby= new SqlParameter("@modifiedby",SqlDbType.Char, 10); 
					p_modifiedby.Value=Session["user_id"].ToString();
					cmd.Parameters.Add(p_pcode);
					cmd.Parameters.Add(p_degree);
					cmd.Parameters.Add(p_institute);
					cmd.Parameters.Add(p_DurationFrom);
					cmd.Parameters.Add(p_DurationTo);
					cmd.Parameters.Add(p_result);
					cmd.Parameters.Add(p_majors);
					cmd.Parameters.Add(p_achievements);
					cmd.Parameters.Add(p_OtherDegree);
					cmd.Parameters.Add(p_OtherInstitute);
					cmd.Parameters.Add(p_OtherMajors);
					cmd.Parameters.Add(_eduinfoid);
					cmd.Parameters.Add(p_modifiedby);
					int i=cmd.ExecuteNonQuery();
					con.Close();
					EducationalInfo();
					this.btnEdUpdate.Enabled=false;
					this.btnEdNew.Enabled=true;
					this.txtDegree.SelectedValue="0";
					this.txtInstitute.SelectedValue="0";
					this.txtResult.SelectedIndex=0;
					this.txtAchievements.Text="";
					this.DurationTo.SelectedIndex=0;
					this.DurationFrm.SelectedIndex=0;
					this.txtOtherDegree.Text="";
					this.txtOtherInstitute.Text="";
					this.txtOtherMajors.Text="";
					this.txtOtherDegree.Visible=false;
					this.txtOtherInstitute.Visible=false;
					this.txtDegree.Visible=true;
					this.txtInstitute.Visible=true;

					CustomValidator9.Enabled=false;
					RequiredFieldValidator14.Enabled=false;
					CustomValidator11.Enabled=false;
					CustomValidator10.Enabled=false;
					CustomValidator13.Enabled=false;
					CustomValidator14.Enabled=false; 
					RequiredFieldValidator15.Enabled=false;
				}
				string errorString="";
				errorString=EdVerification();
				if(errorString.Length>0)
				{
					errorString=errorString.Remove(errorString.Length-1,1);
					Response.Write("<script>window.alert('"+ errorString +"');</script>"); 
				}
			}
			else
			{
				EducationalInfo();
				this.btnEdUpdate.Enabled=false;
				this.btnEdNew.Enabled=true;
				this.txtDegree.SelectedValue="0";
				this.txtInstitute.SelectedValue="0";
				this.txtResult.SelectedIndex=0;
				this.txtAchievements.Text="";
				this.DurationTo.SelectedIndex=0;
				this.DurationFrm.SelectedIndex=0;
				this.txtOtherDegree.Text="";
				this.txtOtherInstitute.Text="";
				this.txtOtherMajors.Text="";
				this.txtOtherDegree.Visible=false;
				this.txtOtherInstitute.Visible=false;
				this.txtDegree.Visible=true;
				this.txtInstitute.Visible=true;

				CustomValidator9.Enabled=false;
				RequiredFieldValidator14.Enabled=false;
				CustomValidator11.Enabled=false;
				CustomValidator10.Enabled=false;
				CustomValidator13.Enabled=false;
				CustomValidator14.Enabled=false; 
				RequiredFieldValidator15.Enabled=false;
			}
		}

		private void btnEdNew_Click(object sender, System.EventArgs e)
		{
			if(!_isRefresh)
			{
				CustomValidator9.Enabled=true;
				CustomValidator11.Enabled=true;
				CustomValidator10.Enabled=true;
				CustomValidator13.Enabled=true;
				CustomValidator14.Enabled=true; 
			
				Page.Validate();
				this.ValidationSummary1.Visible=true;
				if(Page.IsValid)
				{
					this.ValidationSummary1.Visible=false;
					con.Open();
					string empId=txtEmpInfoCode.Text;
					//string var="NULL";
					//string updateInfo="insert into t_educationinfo values('"+empId.Trim()+"','"+this.txtDegree.SelectedValue.ToString().Trim()+"','"+this.txtInstitute.SelectedValue.ToString().Trim()+"','"+ DurationFrm.SelectedValue.ToString() +"','"+ DurationTo.SelectedValue.ToString() +"','"+this.txtResult.SelectedValue.ToString().Trim()+"','"+this.txtOtherMajors.Text+"','"+this.txtAchievements.Text+"','"+1+"','"+this.txtOtherDegree.Text+"','"+this.txtOtherInstitute.Text+"','"+this.txtOtherMajors.Text+"',"+ var +")";
					string updateInfo="INSERT INTO dbo.t_educationInfo (pcode, degree, institute, DurationFrom, DurationTo, result, majors, achievements, isactive, OtherDegree, OtherInstitute, OtherMajors,createdby) "+
						"VALUES (@p_pcode, @p_degree, @p_institute, @p_DurationFrom, @p_DurationTo, @p_result, @p_majors, @p_achievements, @p_isactive, @p_OtherDegree, @p_OtherInstitute, @p_OtherMajors,@createdby)";
					SqlCommand cmd=new SqlCommand(updateInfo,con);
					SqlParameter p_pcode= new SqlParameter("@p_pcode",SqlDbType.Char, 10); 
					p_pcode.Value=empId;
					SqlParameter p_degree= new SqlParameter("@p_degree",SqlDbType.Int, 4); 
					p_degree.Value=this.txtDegree.SelectedValue.ToString().Trim();
					SqlParameter p_institute= new SqlParameter("@p_institute",SqlDbType.Int, 4); 
					p_institute.Value=this.txtInstitute.SelectedValue.ToString().Trim();
					SqlParameter p_DurationFrom= new SqlParameter("@p_DurationFrom",SqlDbType.Int, 4); 
					p_DurationFrom.Value=DurationFrm.SelectedValue.ToString();
					SqlParameter p_DurationTo= new SqlParameter("@p_DurationTo",SqlDbType.Int, 4); 
					p_DurationTo.Value=DurationTo.SelectedValue.ToString(); 
					SqlParameter p_result= new SqlParameter("@p_result",SqlDbType.VarChar, 100); 
					p_result.Value=this.txtResult.SelectedValue.ToString().Trim();
					SqlParameter p_majors= new SqlParameter("@p_majors",SqlDbType.VarChar, 50); 
					p_majors.Value=this.txtOtherMajors.Text;
					SqlParameter p_achievements= new SqlParameter("@p_achievements",SqlDbType.VarChar, 1000); 
					p_achievements.Value=this.txtAchievements.Text;
					SqlParameter p_isactive= new SqlParameter("@p_isactive",SqlDbType.Int, 4); 
					p_isactive.Value=1;
					SqlParameter p_OtherDegree= new SqlParameter("@p_OtherDegree",SqlDbType.VarChar, 50); 
					p_OtherDegree.Value=this.txtOtherDegree.Text;
					SqlParameter p_OtherInstitute= new SqlParameter("@p_OtherInstitute",SqlDbType.VarChar, 50); 
					p_OtherInstitute.Value=this.txtOtherInstitute.Text;
					SqlParameter p_OtherMajors= new SqlParameter("@p_OtherMajors",SqlDbType.VarChar, 50); 
					p_OtherMajors.Value=this.txtOtherMajors.Text;
					SqlParameter p_createdby= new SqlParameter("@createdby",SqlDbType.Char, 10); 
					p_createdby.Value=Session["user_id"].ToString();

					cmd.Parameters.Add(p_pcode);
					cmd.Parameters.Add(p_degree);
					cmd.Parameters.Add(p_institute);
					cmd.Parameters.Add(p_DurationFrom);
					cmd.Parameters.Add(p_DurationTo);
					cmd.Parameters.Add(p_result);
					cmd.Parameters.Add(p_majors);
					cmd.Parameters.Add(p_achievements);
					cmd.Parameters.Add(p_isactive);
					cmd.Parameters.Add(p_OtherDegree);
					cmd.Parameters.Add(p_OtherInstitute);
					cmd.Parameters.Add(p_OtherMajors);
					cmd.Parameters.Add(p_createdby);
					int i=cmd.ExecuteNonQuery();
					con.Close();
					EducationalInfo();
					this.txtDegree.SelectedValue="0";
					this.txtInstitute.SelectedValue="0";
					this.txtResult.SelectedIndex=0;
					this.txtAchievements.Text="";
					this.DurationTo.SelectedIndex=0;
					this.DurationFrm.SelectedIndex=0; 
					this.txtOtherDegree.Text="";
					this.txtOtherInstitute.Text="";
					this.txtOtherMajors.Text="";
					this.txtOtherDegree.Visible=false;
					this.txtOtherInstitute.Visible=false;
					this.txtDegree.Visible=true;
					this.txtInstitute.Visible=true;

					CustomValidator9.Enabled=false;
					RequiredFieldValidator14.Enabled=false;
					CustomValidator11.Enabled=false;
					CustomValidator10.Enabled=false;
					CustomValidator13.Enabled=false;
					CustomValidator14.Enabled=false; 
					RequiredFieldValidator15.Enabled=false;
					this.ValidationSummary1.Visible=false;
				}
				string errorString="";
				errorString=EdVerification();
				if(errorString.Length>0)
				{
					errorString=errorString.Remove(errorString.Length-1,1);
					Response.Write("<script>window.alert('"+ errorString +"');</script>"); 
				}
			}
			else
			{
				EducationalInfo();
				this.txtDegree.SelectedValue="0";
				this.txtInstitute.SelectedValue="0";
				this.txtResult.SelectedIndex=0;
				this.txtAchievements.Text="";
				this.DurationTo.SelectedIndex=0;
				this.DurationFrm.SelectedIndex=0; 
				this.txtOtherDegree.Text="";
				this.txtOtherInstitute.Text="";
				this.txtOtherMajors.Text="";
				this.txtOtherDegree.Visible=false;
				this.txtOtherInstitute.Visible=false;
				this.txtDegree.Visible=true;
				this.txtInstitute.Visible=true;

				CustomValidator9.Enabled=false;
				RequiredFieldValidator14.Enabled=false;
				CustomValidator11.Enabled=false;
				CustomValidator10.Enabled=false;
				CustomValidator13.Enabled=false;
				CustomValidator14.Enabled=false; 
				RequiredFieldValidator15.Enabled=false;
				this.ValidationSummary1.Visible=false;
			}
		}

		private void DataGrid2_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			
		}

		private void DataGrid4_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			
		}
		
		private void FillRoles()
		{
			//		SqlConnection con=new SqlConnection(Connection.ConnectionString);
			if (con.State==ConnectionState.Closed)
				con.Open();
			string str="select * from t_roles where isactive=1 order by rolename";
			SqlDataAdapter dr=new SqlDataAdapter(str,con);
			DataSet ds=new DataSet();
			dr.Fill(ds,"Record");
			DataView dv=new DataView(ds.Tables["Record"]);
			
			dgRoles.DataSource=dv;
			dgRoles.DataBind();
			
			foreach( DataGridItem di in dgRoles.Items )
			{
				CheckBox cb = (CheckBox)di.FindControl("chkSelectRole") ;
				String RoleID = di.Cells[0].Text;
				//SqlCommand cmd2=new SqlCommand("select isActive from t_usersroles where roleid=" + RoleID + " and LoginID='" + txtUserID.Text + "'",con);
				SqlCommand cmd2=new SqlCommand("select isactive from t_usersroles where pcode='"+ txtEmpInfoCode.Text +"' and roleid=" + RoleID,con);
				SqlDataReader dd=cmd2.ExecuteReader();
				dd.Read();
				if (dd.HasRows)
				{
					if(dd["isActive"].ToString()=="1")
					{
						cb.Checked=true;
					}
					else
					{
						cb.Checked=false;
					}
				}
				else
				{
					cb.Checked=false;
				}
				dd.Close();
			}
		}


		//		private void FillRoles()
		//		{
		//			//		SqlConnection con=new SqlConnection(Connection.ConnectionString);
		//			if (con.State==ConnectionState.Closed)
		//				con.Open();
		//			string str="select * from t_roles ";
		//			SqlDataAdapter dr=new SqlDataAdapter(str,con);
		//			DataSet ds=new DataSet();
		//			dr.Fill(ds,"Record");
		//			DataView dv=new DataView(ds.Tables["Record"]);
		//			
		//			dgRoles.DataSource=dv;
		//			dgRoles.DataBind();
		//			
		//			foreach( DataGridItem di in dgRoles.Items )
		//			{
		//				CheckBox cb = (CheckBox)di.FindControl("chkSelectRole") ;
		//				String RoleID = di.Cells[0].Text;
		//				SqlCommand cmd2=new SqlCommand("select isActive from t_usersroles where roleid=" + RoleID + " and LoginID='" + txtUserID.Text + "'",con);
		//				SqlDataReader dd=cmd2.ExecuteReader();
		//				dd.Read();
		//				if (dd.HasRows)
		//				{
		//					if(dd["isActive"].ToString()=="1")
		//					{
		//						cb.Checked=true;
		//					}
		//					else
		//					{
		//						cb.Checked=false;
		//					}
		//				}
		//				else
		//				{
		//					cb.Checked=false;
		//				}
		//				dd.Close();
		//			}
		//		}
		//

		private void GetEmployeeInfo()
		{
			string pcode="";
			try
			{
				pcode=Request.QueryString["id"];
				try
				{
					SqlDataAdapter da =new SqlDataAdapter("SELECT emp.name, dsg.designation, dept.deptname, sbu.sbuname, emp.email_official " +
						" FROM dbo.t_Department dept INNER JOIN " +
						" dbo.t_Designation dsg ON dept.deptid = dsg.deptid INNER JOIN " +
						" dbo.t_Employee emp ON dsg.desigid = emp.desigid INNER JOIN "+
						" dbo.t_Sbu sbu ON dept.sbu = sbu.sbuid " +
						" WHERE (emp.pcode = '" + pcode + "') ",con);
					DataSet ds=new DataSet();
					
					da.Fill(ds,"emp");

					if (ds.Tables[0].Rows.Count>0)
					{
						lblDept_2.Text=ds.Tables[0].Rows[0]["deptname"].ToString();
						lblDesg_2.Text=ds.Tables[0].Rows[0]["designation"].ToString();
						lblName_2.Text=ds.Tables[0].Rows[0]["name"].ToString();
						lblPCode_2.Text=pcode;
						//lblEmailOfficial_2.Text=ds.Tables[0].Rows[0]["email_official"].ToString();
						lblEmailOfficial_2.Text=ADUsers.GetLoginIDbyPCode(lblPCode_2.Text);
						txtEmpInfoEmail2.Text=ds.Tables[0].Rows[0]["email_official"].ToString();
						
					}

					da.Dispose();
					da=new SqlDataAdapter("SELECT user_id, isActive,password FROM dbo.t_systemuser WHERE (pcode = '" + pcode + "') ",con);
					da.Fill(ds,"account");

					if (ds.Tables["account"].Rows.Count>0)
					{
						cmdCreate.Text="Update Account";
						//-->txtUserID.ReadOnly=true;
						//-->lbCheckAvailability.Visible=false;
						//-->lblAvailable.Visible=false;
						cmdCreate.Enabled=true;
						//						txtPassword1.Text=dr["password"].ToString();
						//							txtPassword2.Text=dr["password"].ToString();
						txtUserID.Text=ds.Tables["account"].Rows[0]["User_id"].ToString();
							
						if (ds.Tables["account"].Rows[0]["isActive"].ToString()=="1")
							chkActiveAccount.Checked=true;
						else
							chkActiveAccount.Checked=false;
						cmdUpdate.Enabled=true;
					}
					else
					{
						txtUserID.ReadOnly=false;
						lbCheckAvailability.Visible=true;
						cmdCreate.Enabled=false;

						string [] s = lblEmailOfficial_2.Text.Split('@');

						txtUserID.Text=s[0];

						cmdCreate.Text="Create Account";
						cmdCreate.Enabled=false;
						cmdUpdate.Enabled=false;

					}

				}
				catch (Exception ex)
				{
					Response.Write(ex.Message);
				}
			}
			catch (Exception ex)
			{
				Response.Redirect("SelectUser.aspx");
				string s=ex.Message;
			}
		}



		//		private void GetEmployeeInfo()
		//		{
		//			string pcode="";
		//			try
		//			{
		//				pcode=Request.QueryString["id"];
		//				try
		//				{
		//					SqlDataAdapter da =new SqlDataAdapter("SELECT emp.name, dsg.designation, dept.deptname, sbu.sbuname, emp.email_official " +
		//						" FROM dbo.t_Department dept INNER JOIN " +
		//						" dbo.t_Designation dsg ON dept.deptid = dsg.deptid INNER JOIN " +
		//						" dbo.t_Employee emp ON dsg.desigid = emp.desigid INNER JOIN "+
		//						" dbo.t_Sbu sbu ON dept.sbu = sbu.sbuid " +
		//						" WHERE (emp.pcode = '" + pcode + "') ",con);
		//					DataSet ds=new DataSet();
		//					
		//					da.Fill(ds,"emp");
		//
		//					if (ds.Tables[0].Rows.Count>0)
		//					{
		//						lblDept_2.Text=ds.Tables[0].Rows[0]["deptname"].ToString();
		//						lblDesg_2.Text=ds.Tables[0].Rows[0]["designation"].ToString();
		//						lblName_2.Text=ds.Tables[0].Rows[0]["name"].ToString();
		//						lblPCode_2.Text=pcode;
		//						lblEmailOfficial_2.Text=ds.Tables[0].Rows[0]["email_official"].ToString();
		//						txtEmpInfoEmail2.Text=ds.Tables[0].Rows[0]["email_official"].ToString();
		//					}
		//
		//					da.Dispose();
		//					da=new SqlDataAdapter("SELECT user_id, isActive,password FROM dbo.t_systemuser WHERE (pcode = '" + pcode + "') ",con);
		//					da.Fill(ds,"account");
		//
		//					if (ds.Tables["account"].Rows.Count>0)
		//					{
		//						cmdCreate.Text="Update Account";
		//						txtUserID.ReadOnly=true;
		//						lbCheckAvailability.Visible=false;
		//						lblAvailable.Visible=false;
		//						cmdCreate.Enabled=true;
		//						//						txtPassword1.Text=dr["password"].ToString();
		//						//							txtPassword2.Text=dr["password"].ToString();
		//						txtUserID.Text=ds.Tables["account"].Rows[0]["User_id"].ToString();
		//							
		//						if (ds.Tables["account"].Rows[0]["isActive"].ToString()=="1")
		//							chkActiveAccount.Checked=true;
		//						else
		//							chkActiveAccount.Checked=false;
		//						cmdUpdate.Enabled=true;
		//					}
		//					else
		//					{
		//						txtUserID.ReadOnly=false;
		//						lbCheckAvailability.Visible=true;
		//						cmdCreate.Enabled=false;
		//
		//						string [] s = lblEmailOfficial_2.Text.Split('@');
		//
		//						txtUserID.Text=s[0];
		//
		//						cmdCreate.Text="Create Account";
		//						cmdCreate.Enabled=false;
		//						cmdUpdate.Enabled=false;
		//
		//					}
		//
		//				}
		//				catch (Exception ex)
		//				{
		//					Response.Write(ex.Message);
		//				}
		//			}
		//			catch (Exception ex)
		//			{
		//				Response.Redirect("SelectUser.aspx");
		//				string s=ex.Message;
		//			}
		//		}
		//


		/// <summary>
		/// Account system
		/// </summary>
		/// <param name="sender"></param>
		/// <param name="e"></param>
		private void ImageButton5_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Requiredfieldvalidator1.Enabled=true;
			this.lblSpouse.Visible=false;		
			CustomValidator8.Enabled=false;
			CustomValidator6.Enabled=false;
			CustomValidator7.Enabled=false; 

			CustomValidator9.Enabled=false;
			RequiredFieldValidator14.Enabled=false;
			CustomValidator11.Enabled=false;
			CustomValidator10.Enabled=false;
			CustomValidator13.Enabled=false;
			CustomValidator14.Enabled=false; 
			RequiredFieldValidator15.Enabled=false;
			
			CustomValidator12.Enabled=false;
			Page.Validate();
			lblVacancy.Visible=false;
			lblEmp.Visible=false;
			dgNIC.Visible=false;

			lblError.Visible=false;
			lblBond.Visible=false;
			lblFunMsg.Visible=false;
 
			RegularExpressionValidator10.Enabled=false;
			RegularExpressionValidator11.Enabled=false;
			RegularExpressionValidator12.Enabled=false;
			RequiredFieldValidator22.Enabled=false;
			RegularExpressionValidator14.Enabled=false;
			RegularExpressionValidator13.Enabled=false;
			RegularExpressionValidator15.Enabled=false;
			RequiredFieldValidator10.Enabled=false;
			this.lblComMsg.Visible=false;

			if(Page.IsValid)
			{
				this.ValidationSummary1.Visible=false;
				this.pnlBondPaper.Visible=false;
				pnlPersonal.Visible=false;
				pnlEmployeeInfo.Visible=false;
				pnlFamilyInfo.Visible=false;
				pnlEducation.Visible=false;
				Panel3.Visible=true;
				pnlCompensation.Visible=false;
				pnlContract.Visible=false;
				lblPCode_2.Text=txtEmpInfoCode.Text;
				lblName_2.Text=txtEmpInfoName.Text;
				lblDept_2.Text=ddDepartment.SelectedItem.Text;
				//txtLoginID.Text=txtEmployeeName2.Text.Replace(" ",".");
				//txtLoginID.Text=txtLoginID.Text.ToLower();
				//txtEmpInfoEmail2.Text=txtEmpInfoEmail.Text;
				//txtDesignation2.Text=ddDesignation.SelectedItem.Text;
				//GetEmployeeAccountInfo();
				//FillRoles();
				txtUserID.Text=txtUserID.Text.ToLower();
				txtEmpInfoEmail2.Text=txtEmpInfoEmail.Text;
				lblDesg_2.Text=ddDesignation.SelectedItem.Text;
				pnlFunctionalDesignation.Visible=false;

				//GetEmployeeAccountInfo();
				lblAvailable.Text="";
				//lblNewPassword.Text="";
				//FillRoles();
				GetEmployeeInfo();
				lblAvailable.Text="";
				//lblNewPassword.Text="";
				FillRoles();
			}
		}

		////		private void Button1_Click(object sender, System.EventArgs e)
		////		{
		////			if (txtLoginID.Text=="")
		////			{
		////				lblError.Text="Password is required";
		////				lblError.Visible=true;
		////				return;
		////			}
		////			else if (txtPassword.Text=="")
		////			{
		////				lblError.Text="Password is not assigned. Please click Generate New Password Link to assigne a new password";
		////				lblError.Visible=true;
		////				return;
		////			}
		////			else if (!AccountAvailable())
		////			{
		////				lblError.Text="Please Check account for availablity";
		////				lblError.Visible=true;
		////				return;
		////			}
		////			
		////			lblError.Visible=true;
		////			txtLoginID.Text=txtLoginID.Text.ToLower();
		////			lblError.Text="";
		////			if (txtLoginID.Text.Trim()=="")
		////			{
		////				lblError.Text="* User ID is required<br>";
		////			}
		////			if (txtPassword.Text=="")
		////			{
		////				lblError.Text += "* Password is required. Click Generate New Password Link to generate a new password<br>";
		////			}
		////			if (lblError.Text=="")
		////			{
		////				lblError.Visible=false;
		////				if (Button1.Text=="Create Account")
		////				{
		////					try
		////					{
		////						SqlConnection conn=new SqlConnection(Connection.ConnectionString);
		////						conn.Open();
		////						int isActive;
		////						isActive=1;
		////						
		////						string uid=Session["user_id"].ToString();
		////						SqlCommand cmd=new SqlCommand("Insert Into t_SystemUser(user_id,pcode,password,created_by,create_date, isActive,isExpire) " + 
		////							"values ('" + txtLoginID.Text.Trim() + "','" + txtEmployeeCode2.Text + "','" + txtPassword.Text + "','" + uid + "',getdate(), " + isActive + ",0)",conn);
		////						cmd.ExecuteNonQuery();
		////						conn.Close();
		////						//Response.Redirect("SelectUser.aspx");
		////						Button1.Text="Update Account";
		////						cmdUpdateRoles.Enabled=true;
		////
		////						if (conn.State==ConnectionState.Closed)
		////							conn.Open();
		////
		////						cmd=new SqlCommand("Insert into t_usersroles (loginid,roleid,isActive) values('" + txtLoginID.Text + "',1,1)",conn);
		////						cmd.ExecuteNonQuery();
		////						FillRoles();
		////						lblAvailable.Text="";
		////						LinkButton1.Visible=false;
		////						txtLoginID.ReadOnly=true;
		////						lblNewPassword.Text="";
		////
		////
		////					}
		////					catch (SqlException ex)
		////					{
		////						Response.Write("Error Number: " + ex.Number +"<br><br>Message: " + ex.Message);
		////						if (ex.Number==2627)
		////						{
		////							lblError.Visible=true;
		////							lblError.Text="The login id '" + txtLoginID.Text + "' already exist please enter new login id";
		////							txtLoginID.Text = "";
		////						}
		////						else if (ex.Number==547)
		////						{
		////							lblError.Visible=true;
		////							lblError.Text="Employee profile is not created, Please First Create Employee Profile.";
		////							txtLoginID.Text="";
		////						}
		////
		////					}
		////				}
		////				else if (Button1.Text=="Update Account")
		////				{
		////					try
		////					{
		////						SqlConnection conn=new SqlConnection(Connection.ConnectionString);
		////						conn.Open();
		////						int isActive;
		////						isActive=1;
		////						//Response.Write("udate t_systemuser set user_id='" + txtUserID.Text + "', password='" + txtPassword1.Text + "', created_by='P991', isActive=" + isActive + " isExpire=0 wher pcode='" + lblEmployeeCode.Text + "'");
		////						SqlCommand cmd=new SqlCommand("update t_systemuser set user_id='" + txtLoginID.Text + "', password='" + txtPassword.Text + "', created_by='P991', isActive=" + isActive + ", isExpire=0 where pcode='" + txtEmployeeCode2.Text + "'",conn);
		////						cmd.ExecuteNonQuery();
		////						conn.Close();
		////						lblPassword.Text="";
		////					}
		////					catch (SqlException ex)
		////					{
		////						Response.Write("Error Number: " + ex.Number +"<br><br>Message: " + ex.Message);
		////					}
		////
		////				}
		////
		////			}
		////			else
		////			{
		////				lblError.Visible=true;
		////			}
		////		}
		////
		//		private void Button1_Click(object sender, System.EventArgs e)
		//		{
		//			lblError.Visible=true;
		//			txtLoginID.Text=txtLoginID.Text.ToLower();
		//			lblError.Text="";
		//			if (txtLoginID.Text.Trim()=="")
		//			{
		//				lblError.Text="* User ID is required<br>";
		//			}
		//			if (txtPassword.Text=="")
		//			{
		//				lblError.Text +="* Password is required<br>";
		//			}
		//			else if (txtPassword.Text!=txtPassword2.Text)
		//			{
		//				lblError.Text+="* Password mismatch";
		//			}
		//			if (lblError.Text=="")
		//			{
		//				lblError.Visible=false;
		//				if (Button1.Text=="Create Account")
		//				{
		//					try
		//					{
		//						SqlConnection conn=new SqlConnection(Connection.ConnectionString);
		//						conn.Open();
		//						int isActive;
		//						if (cbisActvie.Checked==true)
		//							isActive=1;
		//						else
		//							isActive=0;
		//						string uid=Session["user_id"].ToString();
		//						SqlCommand cmd=new SqlCommand("Insert Into t_SystemUser(user_id,pcode,password,created_by,create_date, isActive,isExpire) " + 
		//							"values ('" + txtLoginID.Text.Trim() + "','" + txtEmployeeCode2.Text + "','" + txtPassword.Text + "','" + uid + "',getdate(), " + isActive + ",0)",conn);
		//						cmd.ExecuteNonQuery();
		//						conn.Close();
		//						//Response.Redirect("SelectUser.aspx");
		//						Button1.Text="Update Account";
		//						cmdUpdateRoles.Enabled=true;
		//					}
		//					catch (Exception ex)
		//					{
		//						Response.Write(ex.Message);
		//					}
		//				}
		//				else if (Button1.Text=="Update Account")
		//				{
		//					try
		//					{
		//						SqlConnection conn=new SqlConnection(Connection.ConnectionString);
		//						conn.Open();
		//						int isActive;
		//						if (cbisActvie.Checked==true)
		//							isActive=1;
		//						else
		//							isActive=0;
		//						//Response.Write("udate t_systemuser set user_id='" + txtUserID.Text + "', password='" + txtPassword1.Text + "', created_by='P991', isActive=" + isActive + " isExpire=0 wher pcode='" + lblEmployeeCode.Text + "'");
		//						SqlCommand cmd=new SqlCommand("update t_systemuser set user_id='" + txtLoginID.Text + "', password='" + txtPassword2.Text + "', created_by='P991', isActive=" + isActive + ", isExpire=0 where pcode='" + txtEmployeeCode2.Text + "'",conn);
		//						cmd.ExecuteNonQuery();
		//						conn.Close();
		//						
		//					}
		//					catch (Exception ex)
		//					{
		//						Response.Write(ex.Message);
		//					}
		//
		//				}
		//
		//			}
		//			else
		//			{
		//				lblError.Visible=true;
		//			}
		//		}
		//

		//		private void cmdUpdateRoles_Click(object sender, System.EventArgs e)
		//		{
		//			
		//			if(con.State==ConnectionState.Closed)
		//				con.Open();
		//			SqlCommand cmd = new SqlCommand("delete from t_usersroles where loginid='" + txtLoginID.Text + "'",con);
		//			cmd.ExecuteNonQuery();
		//
		//			foreach( DataGridItem di in Datagrid2.Items )
		//			{
		//				CheckBox cb = (CheckBox)di.FindControl("chkSelectRole") ;
		//				if( cb.Checked==true )
		//				{
		//					//Label1.Text + ="Checked...<bt>";
		//					//Label lb = (Label)di.FindControl("lblRoleID");
		//					//Response.Write( lb.Text + "<br>" );
		//					String RoleID = di.Cells[0].Text;
		//					cmd=new SqlCommand("Insert into t_usersroles (loginid,roleid,isActive) values('" + txtLoginID.Text + "'," + RoleID + ",1)",con);
		//					cmd.ExecuteNonQuery();
		//				}
		//				else
		//				{
		//					//Label1.Text + ="Un Checked...<bt> " + cb.Checked.ToString();
		//				}
		//			}		
		//		}
		//

		//		private void Button2_Click(object sender, System.EventArgs e)
		//		{
		//			txtLoginID.Text=txtLoginID.Text.ToLower();
		//			if(con.State==ConnectionState.Closed)
		//				con.Open();
		//			SqlCommand cmd = new SqlCommand("SELECT user_id FROM dbo.t_systemuser WHERE (user_id = '" + txtLoginID.Text + "')",con);
		//			SqlDataReader dr=cmd.ExecuteReader();
		//			dr.Read();
		//			if (dr.HasRows)
		//			{
		//				lblAvailable.Text="(Not Available)";
		//			}
		//			else
		//			{
		//				lblAvailable.Text="(<b>Available</b>)";
		//			}
		//			lblAvailable.Visible=true;
		//		}
		//
		private void ddDesignation_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			/*this.ddFunctionalDesignation.Items.Clear();
			ListItem item=new ListItem();
			item.Value="0";
			item.Text="Select--Functional Designation";
			this.ddFunctionalDesignation.Items.Add(item);
			con.Open();
			string getFunDes="select fdesigid,functionaltitle from t_functionaldesignation where functionaldesignation='"+ this.ddDesignation.SelectedValue.ToString() +"'";
			SqlCommand cmd=new SqlCommand(getFunDes,con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
			 ListItem itm=new ListItem();
			 itm.Value=rd[0].ToString();
			 itm.Text=rd[1].ToString();
			 this.ddFunctionalDesignation.Items.Add(itm);
			}
			rd.Close();
			con.Close();*/
		}
		protected void TextValidate(object source, ServerValidateEventArgs args)
		{
			args.IsValid = (args.Value !="0");
		}


		private void txtDegree_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			if(this.txtDegree.SelectedValue=="-1")
			{
				this.txtOtherDegree.Visible=true;
				RequiredFieldValidator14.Enabled=true;
			}
			else
			{
				this.txtOtherDegree.Visible=false;
				this.txtOtherDegree.Text="";
				RequiredFieldValidator14.Enabled=false;
			}
		}

		
		private void DataGrid3_SelectedIndexChanged_3(object sender, System.EventArgs e)
		{
			//EducationId=0;
			Session["educationId"]="0";
			this.btnEdUpdate.Enabled=true;
			this.btnEdNew.Enabled=false;
			Session["educationId"]=Convert.ToInt32(this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[8].Text);

			string getDegree=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[0].Text.Trim();
			string getInstitute=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[1].Text.Trim();
			
			string getYear=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[3].Text;
			string getDuration=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[2].Text;
			
			string getResult=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[4].Text.Trim();
			string getMajors=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[5].Text.Trim();
			string getAchievements="";
			if(this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[6].Text=="&nbsp;")
			{}
			else
			{
				getAchievements=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[6].Text;
			}
			//string getType=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[9].Text; 
			
			//if(getType=="1")
			
			//this.ddType.SelectedIndex=Convert.ToInt32(getType);
			this.txtDegree.Visible=true;
			this.txtInstitute.Visible=true;
						

			this.txtOtherDegree.Visible=false;
			this.txtOtherInstitute.Visible=false;
			this.txtOtherMajors.Visible=true;
				
			this.txtOtherDegree.Text="";
			this.txtOtherInstitute.Text="";
			this.txtOtherMajors.Text="";
			try
			{
				int countDegree=0;
				for(int i=0;i<this.txtDegree.Items.Count;i++)
				{
					if(this.txtDegree.Items[i].Text==getDegree)
					{
						this.txtDegree.SelectedIndex=i;
						break;
					}
					else
					{
						countDegree++;
					}
				}
				if(countDegree==this.txtDegree.Items.Count)
				{
					this.txtOtherDegree.Visible=true;
					this.txtOtherDegree.Text=getDegree;
					this.txtDegree.SelectedIndex=countDegree-1;
				}
				int countInstitute=0;
				for(int i=0;i<this.txtInstitute.Items.Count;i++)
				{
					if(this.txtInstitute.Items[i].Text==getInstitute)
					{
						this.txtInstitute.SelectedIndex=i;
						break;
					}
					else
					{
						countInstitute++;
					}
				}
				if(countInstitute==this.txtInstitute.Items.Count)
				{
					this.txtOtherInstitute.Visible=true;
					this.txtOtherInstitute.Text=getInstitute;
					this.txtInstitute.SelectedIndex=countInstitute-1;
				}
				for(int i=0;i<this.txtResult.Items.Count;i++)
				{
					if(this.txtResult.Items[i].Text==getResult)
					{
						//this.txtDegree.SelectedValue=this.txtDegree.Items[i].Value;
						this.txtResult.SelectedIndex=i;
						break;
					}
				}
			}
			catch(Exception ){}
			
			this.DurationTo.SelectedValue=getYear;
			this.DurationFrm.SelectedValue=getDuration;
			this.txtAchievements.Text=getAchievements;
			this.txtOtherMajors.Text=getMajors;
			/*if(getType !="1")
			{
				this.txtDegree.Visible=false;
				this.txtInstitute.Visible=false; 
			}*/
		}
				
						
		

		private void DataGrid1_SelectedIndexChanged_2(object sender, System.EventArgs e)
		{
			Session["familyId"]="0";
			btnFupdate.Enabled=true;
			btnNew.Enabled=false;
			Session["familyId"]=Convert.ToInt32(DataGrid1.Items[DataGrid1.SelectedIndex].Cells[9].Text);
			txtFamilyName.Text=DataGrid1.Items[DataGrid1.SelectedIndex].Cells[0].Text;
			if(DataGrid1.Items[DataGrid1.SelectedIndex].Cells[4].Text=="&nbsp;")
			{
				txtOccupation.Text="";
			}
			else
			{
				txtOccupation.Text=DataGrid1.Items[DataGrid1.SelectedIndex].Cells[4].Text;
			}
			string getRelation=DataGrid1.Items[DataGrid1.SelectedIndex].Cells[1].Text;
			this.CalendarPopup1.SelectedDate=DateTime.Parse(DataGrid1.Items[DataGrid1.SelectedIndex].Cells[5].Text);
			
			if(DataGrid1.Items[DataGrid1.SelectedIndex].Cells[6].Text=="Yes")
			{
				this.ddDependent.SelectedIndex=1;
			}
			else if(DataGrid1.Items[DataGrid1.SelectedIndex].Cells[6].Text=="No")
			{
				this.ddDependent.SelectedIndex=2;
			}
			else
			{
				this.ddDependent.SelectedIndex=0;
			}
			try
			{
				for(int i=0;i<rdRelation.Items.Count;i++)
				{
					ListItem itm=rdRelation.Items[i];
					if(itm.Text==getRelation.Trim())
					{
						rdRelation.SelectedIndex=i;
						break;
					}
				}
				string getStatus=DataGrid1.Items[DataGrid1.SelectedIndex].Cells[2].Text;
				for(int i=0;i<rdStatus.Items.Count;i++)
				{
					ListItem itm=rdStatus.Items[i];
					if(itm.Text==getStatus.Trim())
					{
						rdStatus.SelectedIndex=i;
						break;
					}
				}
				string getGender=DataGrid1.Items[DataGrid1.SelectedIndex].Cells[3].Text;
				for(int i=0;i<rdFamilyGender.Items.Count;i++)
				{
					ListItem itm=rdFamilyGender.Items[i];
					if(itm.Text==getGender.Trim())
					{
						rdFamilyGender.SelectedIndex=i;
						break;
					}
				}
			}
			catch(Exception exc){Response.Write(exc.Message+exc.StackTrace);Response.End();}
		}

		private void btnECancel_Click(object sender, System.EventArgs e)
		{
			this.btnEdUpdate.Enabled=false;
			this.btnEdNew.Enabled=true;
			this.txtDegree.SelectedValue="0";
			this.txtInstitute.SelectedValue="0";
			this.txtResult.SelectedIndex=0;
			this.txtAchievements.Text="";
			this.DurationTo.SelectedIndex=0;
			this.DurationFrm.SelectedIndex=0;
			this.txtOtherDegree.Text="";
			this.txtOtherInstitute.Text="";
			this.txtOtherMajors.Text="";
			this.txtOtherDegree.Visible=false;
			this.txtOtherInstitute.Visible=false;
			this.txtDegree.Visible=true;
			this.txtInstitute.Visible=true;

			CustomValidator9.Enabled=false;
			RequiredFieldValidator14.Enabled=false;
			CustomValidator11.Enabled=false;
			CustomValidator10.Enabled=false;
			CustomValidator13.Enabled=false;
			CustomValidator14.Enabled=false; 
			RequiredFieldValidator15.Enabled=false;
			
		}

		private void txtInstitute_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			if(this.txtInstitute.SelectedValue=="-1")
			{
				this.txtOtherInstitute.Visible=true;
				RequiredFieldValidator15.Enabled=true;
			}
			else
			{
				this.txtOtherInstitute.Visible=false;
				this.txtOtherInstitute.Text="";
				RequiredFieldValidator15.Enabled=false;
			}
		}

		private void calendarDOB_DateChanged(object sender, System.EventArgs e)
		{
			 	
		}

		private void btnInsertBond_Click(object sender, System.EventArgs e)
		{
			con.Open();
			//string Inst="insert into t_bondpaper values('"+ this.txtEmpInfoCode.Text +"','"+ this.SignDate.SelectedDate.ToString() +"','"+ this.EndDate.SelectedDate.ToString() +"','1')";
			string Inst="insert into t_bondpaper values('"+ this.txtEmpInfoCode.Text +"','"+ this.SignDate.SelectedDate.ToString() +"','"+ this.EndDate.SelectedDate.ToString() +"','1','"+Session["user_id"].ToString()+"','"+DateTime.Now+"',null)";
			SqlCommand cmd=new SqlCommand(Inst,con);
			int i=cmd.ExecuteNonQuery();
			if(i>0)
			{
				string setEmp="update t_employee set bondpaper='1' where pcode='"+ this.txtEmpInfoCode.Text+"' And del=1";
				cmd=new SqlCommand(setEmp,con);
				cmd.ExecuteNonQuery();
				this.lblBond.Visible=true;
				this.lblBond.Text="Employee's Bond Paper Information Has Been Successfully Inserted"; 
				this.SignDate.Enabled=false;
				this.EndDate.Enabled=false;
				this.btnInsertBond.Enabled=false;
			}
			con.Close();
		}

		private void ImageButton6_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Requiredfieldvalidator1.Enabled=false;			
			this.lblSpouse.Visible=false;
			CustomValidator8.Enabled=false;
			CustomValidator6.Enabled=false;
			CustomValidator7.Enabled=false;
 
			CustomValidator9.Enabled=false;
			RequiredFieldValidator14.Enabled=false;
			CustomValidator11.Enabled=false;
			CustomValidator10.Enabled=false;
			CustomValidator13.Enabled=false;
			CustomValidator14.Enabled=false; 
			RequiredFieldValidator15.Enabled=false;
			
			CustomValidator12.Enabled=false;
			Page.Validate();
			lblVacancy.Visible=false;
			lblEmp.Visible=false;
			dgNIC.Visible=false;

			lblError.Visible=false;
			lblBond.Visible=false;
			lblFunMsg.Visible=false;
		
			RegularExpressionValidator10.Enabled=false;
			RegularExpressionValidator11.Enabled=false;
			RegularExpressionValidator12.Enabled=false;
			RequiredFieldValidator22.Enabled=false;
			RegularExpressionValidator14.Enabled=false;
			RegularExpressionValidator13.Enabled=false;
			RegularExpressionValidator15.Enabled=false;
			RequiredFieldValidator10.Enabled=false;
			this.lblComMsg.Visible=false;
			if(Page.IsValid)
			{
				this.ValidationSummary1.Visible=false;
				this.pnlFunctionalDesignation.Visible=false;
				pnlPersonal.Visible=false;
				pnlEmployeeInfo.Visible=false;
				pnlFamilyInfo.Visible=false;
				pnlEducation.Visible=false;
				Panel3.Visible=false;
				pnlCompensation.Visible=false;
				pnlContract.Visible=false;
				this.pnlBondPaper.Visible=true;
			}
		}

		private void ddNationality_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			setNationality();
			
		}
		public void setNationality()
		{
			
			if(this.ddNationality.SelectedValue !="0")
			{
				this.ddNationality2.Items.Clear();
				ListItem item=new ListItem("Select--Nationality","0");
				this.ddNationality2.Items.Add(item);
				for(int i=0;i<this.ddNationality.Items.Count;i++)
				{
					ListItem itm=new ListItem();
					itm.Text=this.ddNationality.Items[i].Text;
					itm.Value=this.ddNationality.Items[i].Value;
					if(itm.Value !=this.ddNationality.SelectedValue && this.ddNationality.Items[i].Value !="0")
					{
						this.ddNationality2.Items.Add(itm);
					}
				}
				this.ddNationality2.Enabled=true;
			}
			else
			{
				this.ddNationality2.Items.Clear();
				ListItem item=new ListItem("Select--Nationality","0");
				this.ddNationality2.Items.Add(item);
				this.ddNationality2.SelectedIndex=0;
				this.ddNationality2.Enabled=false;
			}
			
			settings();
	
		}
		public void settings()
		{
			if(this.ddNationality.SelectedValue !="0" && this.ddNationality.SelectedValue=="163")
			{
				RequiredFieldValidator4.Enabled=true;
			}
			if(this.ddNationality.SelectedValue !="0" && this.ddNationality.SelectedValue !="163")
			{
				RequiredFieldValidator5.Enabled=true;
			}
			if(this.ddNationality.SelectedValue !="0" && this.ddNationality.SelectedValue=="163" && this.ddNationality2.SelectedValue=="0")
			{
				RequiredFieldValidator5.Enabled=false;
			}
			if(this.ddNationality.SelectedValue !="0" && this.ddNationality.SelectedValue !="163" && this.ddNationality2.SelectedValue=="0")
			{
				RequiredFieldValidator4.Enabled=false;
			}
			 
			if(this.ddNationality2.SelectedValue !="0" && this.ddNationality2.SelectedValue=="163")
			{
				RequiredFieldValidator4.Enabled=true;
			}
			if(this.ddNationality2.SelectedValue !="0" && this.ddNationality2.SelectedValue !="163")
			{
				RequiredFieldValidator5.Enabled=true;
			}
			if(this.ddNationality2.SelectedValue !="0" && this.ddNationality2.SelectedValue=="163" && this.ddNationality.SelectedValue=="0")
			{
				RequiredFieldValidator5.Enabled=false;
			}
			if(this.ddNationality2.SelectedValue !="0" && this.ddNationality2.SelectedValue !="163" && this.ddNationality.SelectedValue=="0")
			{
				RequiredFieldValidator4.Enabled=false;
			}
			if(this.ddNationality.SelectedValue =="0" && this.ddNationality2.SelectedValue=="0")
			{
				RequiredFieldValidator4.Enabled=false;
				RequiredFieldValidator5.Enabled=false;
			}
			
		}
		public string Count()
		{
			return DateTime.Today.Date.ToString();
		}

		private void ImageButton7_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Requiredfieldvalidator1.Enabled=false;
			this.lblSpouse.Visible=false;		
			CustomValidator8.Enabled=false;
			CustomValidator6.Enabled=false;
			CustomValidator7.Enabled=false; 
			CustomValidator9.Enabled=false;
			RequiredFieldValidator14.Enabled=false;
			CustomValidator11.Enabled=false;
			CustomValidator10.Enabled=false;
			CustomValidator13.Enabled=false;
			CustomValidator14.Enabled=false; 
			RequiredFieldValidator15.Enabled=false;
			
			CustomValidator12.Enabled=false;
			Page.Validate();
			lblVacancy.Visible=false;
			lblEmp.Visible=false;
			dgNIC.Visible=false;

			lblError.Visible=false;
			lblBond.Visible=false;
			lblFunMsg.Visible=false;
 
			RegularExpressionValidator10.Enabled=false;
			RegularExpressionValidator11.Enabled=false;
			RegularExpressionValidator12.Enabled=false;
			RequiredFieldValidator22.Enabled=false;
			RegularExpressionValidator14.Enabled=false;
			RegularExpressionValidator13.Enabled=false;
			RegularExpressionValidator15.Enabled=false;
			RequiredFieldValidator10.Enabled=false;
			this.lblComMsg.Visible=false;
			if(Page.IsValid)
			{
				this.ValidationSummary1.Visible=false;
				pnlPersonal.Visible=false;
				pnlEmployeeInfo.Visible=false;
				pnlFamilyInfo.Visible=false;
				pnlEducation.Visible=false;
				btnUpdate.Visible=true;
				Panel3.Visible=false;
				this.pnlBondPaper.Visible=false;
				this.pnlFunctionalDesignation.Visible=true;
				pnlCompensation.Visible=false;
				pnlContract.Visible=false;

				con.Open();
				this.lblEmpDesignation.Text=this.ddDesignation.SelectedItem.Text;
				this.lblFuncPcode.Text=this.txtEmpInfoCode.Text;
				this.ddFunctionalDesignation.Items.Clear();
				ListItem item=new ListItem("Select--Functional Designation","0");
				this.ddFunctionalDesignation.Items.Add(item);
				string getVal="select f.fdesigid,f.functionaltitle from t_functionaldesignation f,t_designation d where f.functionaldesignation=d.desigid And d.status=1 And f.isactive=1 And d.desigid =Any(select desigid from t_employee where pcode='"+ this.txtEmpInfoCode.Text +"' And del=1) order by f.functionaltitle";
				SqlCommand cmd=new SqlCommand(getVal,con);
				SqlDataReader rd=cmd.ExecuteReader();
				while(rd.Read())
				{
					ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
					this.ddFunctionalDesignation.Items.Add(itm);
				}
				rd.Close();
				con.Close();
			}

		}

		private void ddFunctionalDesignation_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			con.Open();
			string getVacancy="select noofvacancies from t_functionaldesignation where fdesigid='"+ this.ddFunctionalDesignation.SelectedValue.ToString() +"'";
			SqlCommand cmd=new SqlCommand(getVacancy,con);
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			if(rd.HasRows)
			{
				this.lblFunctionalVacancy.Text=rd[0].ToString();
			}
			else
			{
				this.lblFunctionalVacancy.Text="";
			}
			rd.Close();
			con.Close(); 
		}

		private void btnAddFunctional_Click(object sender, System.EventArgs e)
		{
			if(!_isRefresh)
			{
				this.ValidationSummary1.Visible=true;
				CustomValidator12.Enabled=true;
				Page.Validate();
				if(Page.IsValid)
				{
					this.ValidationSummary1.Visible=false;
					con.Open();
					string getVacancy="select sum(isactive) from t_designationhistory where fdesigid='"+this.ddFunctionalDesignation.SelectedValue.ToString()+"' and isactive=1";
					SqlCommand cVacancy=new SqlCommand(getVacancy,con);
					SqlDataReader rVacancy=cVacancy.ExecuteReader();
					int allotedVac=0;
					int totalVac=0;
						
					while(rVacancy.Read())
					{
						if(rVacancy[0].ToString().Length>0)
						{
							allotedVac=Convert.ToInt32(rVacancy[0].ToString());
						}
					}
					rVacancy.Close();
 
					string gettVacancy="select noofvacancies from t_functionaldesignation where fdesigid='"+this.ddFunctionalDesignation.SelectedValue.ToString()+"'";
					SqlCommand ctVacancy=new SqlCommand(gettVacancy,con);
					SqlDataReader rtVacancy=ctVacancy.ExecuteReader();
					while(rtVacancy.Read())
					{
						if(rtVacancy[0].ToString().Length>0)
						{
							totalVac=Convert.ToInt32(rtVacancy[0].ToString());
						}
					}
					rtVacancy.Close();
					totalVac=totalVac-(allotedVac);
					if(totalVac<=0)
					{
						this.lblFunMsg.Visible=true; 
						this.lblFunMsg.Text="You cannot assign selected functional designation because vacancies exceed the limit";	 
					}
					else
					{
						string var="NULL";
						string InsertRec="insert into t_designationhistory values('"+this.txtEmpInfoCode.Text+"','"+this.ddFunctionalDesignation.SelectedValue.ToString()+"','"+ this.EffectiveDate.SelectedDate.ToString() +"',"+ var +",'"+1+"')";
						SqlCommand cmd=new SqlCommand(InsertRec,con);
						int z=cmd.ExecuteNonQuery();
						if(z>0)
						{
							this.btnAddFunctional.Enabled=false;
							this.lblFunMsg.Visible=true;
							this.lblFunMsg.Text="Functional Designation has been successfully assigned to the employee";
						}
					}
					con.Close();
				}
				string errorString="";
				errorString=FuncVerification();
				if(errorString.Length>0)
				{
					errorString=errorString.Remove(errorString.Length-1,1);
					Response.Write("<script>window.alert('"+ errorString +"');</script>"); 
				}
			}
		}

		private bool AccountAvailable()
		{
			txtUserID.Text=txtUserID.Text.ToLower().ToLower();
			if (cmdCreate.Text=="Create Account" && txtUserID.Text!="")
			{
				//txtUserID.Text=txtUserID.Text.ToLower();
				if(con.State==ConnectionState.Closed)
					con.Open();
				SqlCommand cmd = new SqlCommand("SELECT user_id FROM dbo.t_systemuser WHERE (user_id = '" + txtUserID.Text + "')",con);
				SqlDataReader dr=cmd.ExecuteReader();
				dr.Read();
				lblAvailable.Visible=true;
				if (dr.HasRows)
				{
					lblAvailable.Text="(Not Available)";
					cmdCreate.Enabled=false;
					return false;
				}
				else
				{
					lblAvailable.Text="(<b>Available</b>)";
					cmdCreate.Enabled=true;
					return true;
				}
			}
			else
			{
				lblAvailable.Text="";
				cmdCreate.Enabled=false;
				return false;
			}
		}
		private void LinkButton1_Click(object sender, System.EventArgs e)
		{
			AccountAvailable();
		}

		//		private void LinkButton2_Click(object sender, System.EventArgs e)
		//		{
		//			char [] digits = {'0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z'};
		//			int digitsLength = digits.Length;
		//			//Random RandomNumber = new Random();
		//			string temp="";
		//			for(int k=0; k < 10; k++)
		//			{
		//				int j = RandomNumber.Next(digitsLength);
		//				temp+=digits[j].ToString();
		//			}
		//			txtPassword.Text=temp;
		//			lblNewPassword.Text="A new password is Assigned";
		//		}
		//
		private void ddNationality2_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			settings();
		}
		public void getYears()
		{
			int startYear=1900;
			int endYear=DateTime.Today.Year;
			for(int i=endYear;i>=startYear;i--)
			{
				ListItem itm=new ListItem(i.ToString(),i.ToString());
				ListItem item=new ListItem(i.ToString(),i.ToString());
				this.DurationFrm.Items.Add(itm);
				this.DurationTo.Items.Add(item); 
			}
			ListItem itm2=new ListItem("In Progress","-1");
			this.DurationTo.Items.Insert(1,itm2);
		}

		public void clickEvents()
		{
			for(int i=0;i<this.DataGrid1.Items.Count;i++)
			{
				LinkButton lnk=(LinkButton)this.DataGrid1.Items[i].FindControl("delFamily");
				lnk.Attributes.Add("onclick","return confirm('Are you sure to delete selected record ');"); 
			}
		}

		public void dFamily(object sender ,EventArgs e)
		{
			LinkButton req=(LinkButton)sender;
			for(int i=0;i<this.DataGrid1.Items.Count;i++)
			{
				LinkButton lnk=(LinkButton)this.DataGrid1.Items[i].FindControl("delFamily");
				if(lnk.Equals(req))
				{
					con.Open();
					SqlCommand cmd=new SqlCommand("update t_familydetails set isactive=0 where fdid='"+ this.DataGrid1.Items[i].Cells[9].Text +"' ",con);
					cmd.ExecuteNonQuery(); 
					con.Close();
					FamilyInfo(); 
					break;
				}
			}
		}

		public void clickEvents2()
		{
			for(int i=0;i<this.DataGrid3.Items.Count;i++)
			{
				LinkButton lnk=(LinkButton)this.DataGrid3.Items[i].FindControl("delEducation");
				lnk.Attributes.Add("onclick","return confirm('Are you sure to delete selected record ');"); 
			}
		}

		public void dEducation(object sender ,EventArgs e)
		{
			LinkButton req=(LinkButton)sender;
			for(int i=0;i<this.DataGrid3.Items.Count;i++)
			{
				LinkButton lnk=(LinkButton)this.DataGrid3.Items[i].FindControl("delEducation");
				if(lnk.Equals(req))
				{
					con.Open();
					SqlCommand cmd=new SqlCommand("update t_educationinfo set isactive=0 where eduinfoid='"+ this.DataGrid3.Items[i].Cells[8].Text +"' ",con);
					cmd.ExecuteNonQuery(); 
					con.Close();
					EducationalInfo();
					break;
				}
			}
		}

		private void btnFunctionalCancel_Click(object sender, System.EventArgs e)
		{
			this.ddFunctionalDesignation.SelectedIndex=0;
			this.lblFunctionalVacancy.Text="";
			CustomValidator12.Enabled=false;
		}

		private void lnkNewEmployee_Click(object sender, System.EventArgs e)
		{
			Response.Redirect("NewEmployee.aspx");
		}

		private void ddReligion_SelectedIndexChanged(object sender, System.EventArgs e)
		{
 
		}

		public string PerVerification()
		{
			string errorString="";
			if(!RequiredFieldValidator4.IsValid)
			{
				//errorString+=RequiredFieldValidator1.ErrorMessage+" ,";
			}
			if(!CompareValidator2.IsValid)
			{
				errorString+=CompareValidator2.ErrorMessage+" ,";
			}
			if(!RequiredFieldValidator2.IsValid)
			{
				errorString+=RequiredFieldValidator2.ErrorMessage+" ,";
			}
			if(!RegularExpressionValidator6.IsValid)
			{
				errorString+=RegularExpressionValidator6.ErrorMessage+" ,";
			}
			if(!RequiredFieldValidator3.IsValid)
			{
				errorString+=RequiredFieldValidator3.ErrorMessage+" ,";
			}
			if(!RegularExpressionValidator9.IsValid)
			{
				errorString+=RegularExpressionValidator9.ErrorMessage+" ,";
			}
			if(!RegularExpressionValidator7.IsValid)
			{
				errorString+=RegularExpressionValidator7.ErrorMessage+" ,";
			}
			if(!RegularExpressionValidator8.IsValid)
			{
				errorString+=RegularExpressionValidator8.ErrorMessage+" ,";
			}
			if(!CustomValidator1.IsValid)
			{
				errorString+=CustomValidator1.ErrorMessage+" ,";
			}
			if(!RequiredFieldValidator6.IsValid)
			{
				errorString+=RequiredFieldValidator6.ErrorMessage+" ,";
			}
			if(!RequiredFieldValidator5.IsValid)
			{
				errorString+=RequiredFieldValidator5.ErrorMessage+" ,";
			}
			if(!RegularExpressionValidator1.IsValid)
			{
				errorString+=RegularExpressionValidator1.ErrorMessage+" ,";
			}
			if(!RegularExpressionValidator2.IsValid)
			{
				errorString+=RegularExpressionValidator2.ErrorMessage+" ,";
			}
			return errorString;
		}
		public string EmpVerification()
		{
			string errorString="";
			//			if(!RequiredFieldValidator1.IsValid)
			//			{
			//				errorString+=RequiredFieldValidator1.ErrorMessage+" ,";
			//			}
			
			if(!RegularExpressionValidator3.IsValid)
			{
				errorString+=RegularExpressionValidator3.ErrorMessage+" ,";
			}
			if(!RequiredFieldValidator21.IsValid)
			{
				errorString+=RequiredFieldValidator21.ErrorMessage+" ,";
			}
			if(!CustomValidator4.IsValid)
			{
				errorString+=CustomValidator4.ErrorMessage+" ,";
			}
			if(!CustomValidator2.IsValid)
			{
				errorString+=CustomValidator2.ErrorMessage+" ,";
			}
			if(!CustomValidator3.IsValid)
			{
				errorString+=CustomValidator3.ErrorMessage+" ,";
			}
			if(!RequiredFieldValidator8.IsValid)
			{
				errorString+=RequiredFieldValidator8.ErrorMessage+" ,";
			}
			if(!RegularExpressionValidator4.IsValid)
			{
				errorString+=RegularExpressionValidator4.ErrorMessage+" ,";
			}
			if(!RegularExpressionValidator5.IsValid)
			{
				errorString+=RegularExpressionValidator5.ErrorMessage+" ,";
			}
			if(!CustomValidator5.IsValid)
			{
				errorString+=CustomValidator5.ErrorMessage+" ,";
			}
			if(!CompareValidator1.IsValid)
			{
				errorString+=CompareValidator1.ErrorMessage+" ,";
			}
			if(!RequiredFieldValidator9.IsValid)
			{
				errorString+=RequiredFieldValidator9.ErrorMessage+" ,";
			}
			if(!RequiredFieldValidator7.IsValid)
			{
				errorString+=RequiredFieldValidator7.ErrorMessage+" ,";
			}
			if(!RequiredFieldValidator11.IsValid)
			{
				errorString+=RequiredFieldValidator11.ErrorMessage+" ,";
			}
			return errorString;
		}
		public string FamVerification()
		{
			string errorString="";
			if(lblSpouse.Visible)
			{
				errorString+=lblSpouse.Text+" ,";
			}
			if(!CustomValidator8.IsValid)
			{
				errorString+=CustomValidator8.ErrorMessage+" ,";
			}
			if(!CustomValidator6.IsValid)
			{
				errorString+=CustomValidator6.ErrorMessage+" ,";
			}
			if(!CustomValidator7.IsValid)
			{
				errorString+=CustomValidator7.ErrorMessage+" ,";
			}
			return errorString;
		}
		public string EdVerification()
		{
			string errorString="";
			if(!CustomValidator9.IsValid)
			{
				errorString+=CustomValidator9.ErrorMessage+" ,";
			}
			if(!RequiredFieldValidator14.IsValid)
			{
				errorString+=RequiredFieldValidator14.ErrorMessage+" ,";
			}
			if(!CustomValidator10.IsValid)
			{
				errorString+=CustomValidator10.ErrorMessage+" ,";
			}
			if(!RequiredFieldValidator15.IsValid)
			{
				errorString+=RequiredFieldValidator15.ErrorMessage+" ,";
			}
			if(!CustomValidator11.IsValid)
			{
				errorString+=CustomValidator11.ErrorMessage+" ,";
			}
			if(!CustomValidator13.IsValid)
			{
				errorString+=CustomValidator13.ErrorMessage+" ,";
			}
			if(!CustomValidator14.IsValid)
			{
				errorString+=CustomValidator14.ErrorMessage+" ,";
			}
			return errorString;
		}

		public string FuncVerification()
		{
			string errorString="";
			if(!CustomValidator12.IsValid)
			{
				errorString+=CustomValidator12.ErrorMessage+" ,";
			}
			if(lblFunMsg.Visible)
			{
				errorString+=lblFunMsg.Text+" ,";
			}
			return errorString;
		}
		protected override void LoadViewState(object savedState)
		{
			object[] allStates = (object[]) savedState;
			base.LoadViewState(allStates[0]);
			_refreshState = (bool) allStates[1];
			try
			{
				_isRefresh = _refreshState == (bool) Session["__ISREFRESH"];
			}
			catch(Exception)
			{
				Response.Redirect("../Login.aspx");
			}

		}

		protected override object SaveViewState()
		{
			Session["__ISREFRESH"] = _refreshState;
			object[] allStates = new object[2];
			allStates[0] = base.SaveViewState();
			allStates[1] = !_refreshState;
			return allStates;
		}

		private void AssignEmployeeRole()
		{
			SqlConnection conn2 = new SqlConnection(Connection.ConnectionString);
			conn2.Open();
			SqlCommand cmd = new SqlCommand("DELETE FROM dbo.t_usersroles WHERE (PCode = '"+lblPCode_2.Text+"')",conn2);
			cmd.ExecuteNonQuery();
			//String RoleID = "1";
			cmd=new SqlCommand("INSERT INTO dbo.t_usersroles (LoginID, roleID, isActive, PCode) " +
				" VALUES ('"+txtUserID.Text+"', 1, 1, '"+lblPCode_2.Text+"')",conn2);
			cmd.ExecuteNonQuery();
		}

		public UserInfo GetUser(string userAccount)
		{
			UserInfo user=null;
			try
			{				
				DirectoryEntry cAccount=new DirectoryEntry("LDAP://10.1.10.5","raabta","rabta");
				DirectorySearcher uSearch=new DirectorySearcher(cAccount);
				uSearch.Filter=("(&(objectclass=user)(objectcategory=person)(sAMAccountName=" + userAccount + "))");
				SearchResult uResult=uSearch.FindOne();
				if(uResult !=null)
				{
					
					user=new UserInfo(userAccount);
					if(uResult.Properties.Contains("givenname"))
					{
						user.FirstName=(String)uResult.Properties["givenname"][0];
					}
					if(uResult.Properties.Contains("displayname"))
					{
						user.FullName=(String)uResult.Properties["displayname"][0];
					}
					if(uResult.Properties.Contains("department"))
					{
						user.Department=(String)uResult.Properties["department"][0];
					}
					if(uResult.Properties.Contains("title"))
					{
						user.Designation=(String)uResult.Properties["title"][0];
					}
					if(uResult.Properties.Contains("sAMAccountName"))
					{
						user.UID=(String)uResult.Properties["sAMAccountName"][0];
					}
					if(uResult.Properties.Contains("employeeid"))
					{
						user.EmployeeID=(String)uResult.Properties["employeeid"][0];
						user.EmployeeID=user.EmployeeID.Replace("-","");
					}
					return user;
				}
				else
				{
					return user;
				}
			}
			catch(Exception )
			{
				return user;
			}
	 
		}

		private void CheckAvailability()
		{
			if (txtUserID.Text.Trim()=="")
			{
				lblAvailable.Text="Please enter User ID";
				cmdCreate.Enabled=false;
			}
			else
			{
				string pc=ADUsers.GetPCodeByLoginID(txtUserID.Text.Trim());
				txtEmpInfoCode.Text=txtEmpInfoCode.Text.Trim();
				if(pc=="")
				{
					lblAvailable.Text="<br>This User ID does not exist or Employee code is not assigned to it<br>Please contact network administrator";
					cmdCreate.Enabled=false;
				}
				else if (pc!=txtEmpInfoCode.Text)
				{
					lblAvailable.Text="<br>This User ID already assigned to "+pc;
					cmdCreate.Enabled=false;
				}
				else
				{
					lblAvailable.Text="(<b>Available</b>)";
					cmdCreate.Enabled=true;
				}
			}
		}

		//		private void CheckAvailability()
		//		{
		//			lblADExist_2.Text="0";
		//			if (txtUserID.Text.Trim()=="")
		//			{
		//				lblAvailable.Text="Please enter User ID";
		//				cmdCreate.Enabled=false;
		//			}
		//			else
		//			{
		//				UserInfo usr=GetUser(txtUserID.Text);
		//				if(usr==null)
		//				{
		//					lblAvailable.Text="<br>This User ID does not exist.<br>Please contact network administrator";
		//					cmdCreate.Enabled=false; 
		//					///	 in future this will be enabled to create an account in
		//					///	 active directory as well
		//				}
		//				else
		//				{
		//					lblADExist_2.Text="1";
		//					if (usr.EmployeeID==null)
		//					{
		//						lblAvailable.Text="Employee code is not assigned to "+lblName_2.Text+"<br>Please contact network administrator";
		//						cmdCreate.Enabled=false;
		//						return;
		//					}
		//
		//					string pc=usr.EmployeeID.Replace("-","").Trim();
		//					string pc2=lblPCode_2.Text.Trim();
		//
		//					if (pc==pc2)
		//					{
		//						/// Check Account in Raabta Database it is available or not
		//						AccountAvailable();
		//					}
		//					else
		//					{
		//						/// selected id is already assigned to another employee
		//						lblAvailable.Text=txtUserID.Text+ " is already assigned to another user"; 
		//						cmdCreate.Enabled=false;
		//					}
		//				}
		//			}
		//		}
		//

		private void cmdCreate_Click(object sender, System.EventArgs e)
		{
			if(con.State==ConnectionState.Closed)
				con.Open();
			if (cmdCreate.Text=="Create Account")
			{
				CheckAvailability();
				if(lblAvailable.Text=="(<b>Available</b>)")
				{
					/// if account exist in active directory then just create 
					/// account in geo raabta with employee role
					//->if (lblADExist_2.Text=="1")
					//->{
					SqlCommand cmd=new SqlCommand("INSERT INTO dbo.t_systemuser " +
						" (user_id, pcode, password, created_by, create_date, isActive, isExpire, counter, musicid) " +
						" VALUES (@p_user_id, @p_pcode, NULL, @p_created_by, GETDATE(), @p_isActive, 0, 0, 1)",con);
					SqlParameter p_user_id=new SqlParameter("@p_user_id",SqlDbType.VarChar,100);
					p_user_id.Value=txtUserID.Text;

					SqlParameter p_pcode=new SqlParameter("@p_pcode",SqlDbType.Char,10);
					p_pcode.Value=lblPCode_2.Text;

					SqlParameter p_created_by=new SqlParameter("@p_created_by",SqlDbType.Char,10);
					p_created_by.Value=Session["user_id"].ToString();

					SqlParameter p_isActive=new SqlParameter("@p_isActive",SqlDbType.TinyInt);
					if (chkActiveAccount.Checked)
						p_isActive.Value=1;
					else
						p_isActive.Value=0;

					cmd.Parameters.Add(p_created_by);
					cmd.Parameters.Add(p_pcode);
					cmd.Parameters.Add(p_user_id);
					cmd.Parameters.Add(p_isActive);

					cmd.ExecuteNonQuery();
					AssignEmployeeRole();
					FillRoles();
					cmdUpdate.Enabled=true;
					cmdCreate.Text="Update Account";
					//->lbCheckAvailability.Visible=false;
					lblAvailable.Text="";
					lblAccountMessage_2.Text="Raabta Account has been created successfully";
					cmdUpdate.Enabled=true;
					
					//->}
					//->else if (lblADExist_2.Text=="0")
					//->{
					//->}
				}
			}
			else if (cmdCreate.Text=="Update Account")
			{
				CheckAvailability();
				if(lblAvailable.Text=="(<b>Available</b>)")
				{
					if (con.State==ConnectionState.Closed)
						con.Open();
					string st="";
					if (chkActiveAccount.Checked)
						st="1";
					else
						st="0";
					//SqlCommand cmd=new SqlCommand("UPDATE dbo.t_systemuser SET isActive = "+st+" and WHERE (pcode = '"+lblPCode_2.Text+"')",con);
					SqlCommand cmd=new SqlCommand("UPDATE t_systemuser SET user_id = '" + txtUserID.Text.Trim() + "' ,isActive = " + st + " " +
						" WHERE PCode='" + lblPCode_2.Text + "'",con);
					cmd.ExecuteNonQuery();
					lblAccountMessage_2.Text="Account information has been updated successfully";
					cmdUpdate.Enabled=true;
				}
			}
			if(con.State==ConnectionState.Open)
				con.Close();
		}



		//		private void cmdCreate_Click(object sender, System.EventArgs e)
		//		{
		//			if (cmdCreate.Text=="Create Account")
		//			{
		//				if (_isRefresh)
		//				{
		//					FillRoles();
		//					cmdUpdate.Enabled=true;
		//					cmdCreate.Text="Update Account";
		//					return;
		//				}
		//
		//				CheckAvailability();
		//				if(lblAvailable.Text=="(<b>Available</b>)")
		//				{
		//					/// if account exist in active directory then just create 
		//					/// account in geo raabta with employee role
		//					if (lblADExist_2.Text=="1")
		//					{
		//						SqlCommand cmd=new SqlCommand("INSERT INTO dbo.t_systemuser " +
		//							" (user_id, pcode, password, created_by, create_date, isActive, isExpire, counter, musicid) " +
		//							" VALUES (@p_user_id, @p_pcode, NULL, @p_created_by, GETDATE(), @p_isActive, 0, 0, 1)",con);
		//						SqlParameter p_user_id=new SqlParameter("@p_user_id",SqlDbType.VarChar,100);
		//						p_user_id.Value=txtUserID.Text;
		//
		//						SqlParameter p_pcode=new SqlParameter("@p_pcode",SqlDbType.Char,10);
		//						p_pcode.Value=lblPCode_2.Text;
		//
		//						SqlParameter p_created_by=new SqlParameter("@p_created_by",SqlDbType.Char,10);
		//						p_created_by.Value=Session["user_id"].ToString();
		//
		//						SqlParameter p_isActive=new SqlParameter("@p_isActive",SqlDbType.TinyInt);
		//						if (chkActiveAccount.Checked)
		//							p_isActive.Value=1;
		//						else
		//							p_isActive.Value=0;
		//
		//						cmd.Parameters.Add(p_created_by);
		//						cmd.Parameters.Add(p_pcode);
		//						cmd.Parameters.Add(p_user_id);
		//						cmd.Parameters.Add(p_isActive);
		//
		//						cmd.ExecuteNonQuery();
		//						AssignEmployeeRole();
		//						FillRoles();
		//						cmdUpdate.Enabled=true;
		//						cmdCreate.Text="Update Account";
		//					
		//					}
		//					else if (lblADExist_2.Text=="0")
		//					{
		//					}
		//				}
		//			}
		//			else if (cmdCreate.Text=="Update Account")
		//			{
		//				if (con.State==ConnectionState.Closed)
		//					con.Open();
		//				string st="";
		//				if (chkActiveAccount.Checked)
		//					st="1";
		//				else
		//					st="0";
		//				SqlCommand cmd=new SqlCommand("UPDATE dbo.t_systemuser SET isActive = "+st+" WHERE (pcode = '"+lblPCode_2.Text+"')",con);
		//				cmd.ExecuteNonQuery();
		//			}
		//		}
		//
		private void cmdUpdate_Click(object sender, System.EventArgs e)
		{
			if (_isRefresh)
			{
				FillRoles();
				return;
			}
			if (con.State==ConnectionState.Closed)
				con.Open();
			//Label1.Text="";
			SqlCommand cmd = new SqlCommand("delete from t_usersroles where pcode='"+lblPCode_2.Text+"'",con);
			cmd.ExecuteNonQuery();

			foreach( DataGridItem di in dgRoles.Items )
			{
				CheckBox cb = (CheckBox)di.FindControl("chkSelectRole") ;
				if( cb.Checked==true )
				{
					//Label1.Text+="Checked...<bt>";
					//Label lb = (Label)di.FindControl("lblRoleID");
					//Response.Write( lb.Text +"<br>" );
					String RoleID = di.Cells[0].Text;
					cmd=new SqlCommand("Insert into t_usersroles (LoginID, roleID, isActive, PCode) values('"+txtUserID.Text+"', "+RoleID+", 1, '"+lblPCode_2.Text+"')",con);
					//cmd=new SqlCommand("Insert into t_usersroles (LoginID, roleID, isActive, PCode) values('" + txtUserID.Text + "'," + RoleID + ",1)",con);
					cmd.ExecuteNonQuery();
				}
				else
				{
					//Label1.Text+="Un Checked...<bt> "+cb.Checked.ToString();
				}
			}
			//lblMessage_2.Text="Roles assignment has completed successfully";
			lblRoleMessage_2.Text="Role assignment process has been completed.";
		}

		private void lbCheckAvailability_Click(object sender, System.EventArgs e)
		{
			CheckAvailability();
		}

		private void ImgCompensation_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Requiredfieldvalidator1.Enabled=false;
			this.lblSpouse.Visible=false;
			CustomValidator8.Enabled=false;
			CustomValidator6.Enabled=false;
			CustomValidator7.Enabled=false; 

			CustomValidator9.Enabled=false;
			RequiredFieldValidator14.Enabled=false;
			CustomValidator11.Enabled=false;
			CustomValidator10.Enabled=false;
			CustomValidator13.Enabled=false;
			CustomValidator14.Enabled=false; 
			RequiredFieldValidator15.Enabled=false;
			CustomValidator12.Enabled=false;
			lblVacancy.Visible=false;
			lblEmp.Visible=false;
			dgNIC.Visible=false;

			lblError.Visible=false;
			lblBond.Visible=false;
			lblFunMsg.Visible=false;
 
			RegularExpressionValidator10.Enabled=true;
			RegularExpressionValidator11.Enabled=true;
			RegularExpressionValidator12.Enabled=true;
			RequiredFieldValidator22.Enabled=true;
			RegularExpressionValidator14.Enabled=true;
			RegularExpressionValidator13.Enabled=true;
			RegularExpressionValidator15.Enabled=true;
			RequiredFieldValidator10.Enabled=true;
			this.lblComMsg.Visible=true;
			Page.Validate();
			this.ValidationSummary1.Visible=true;
			if(Page.IsValid)
			{ 
				pnlPersonal.Visible=false;
				pnlEmployeeInfo.Visible=false;
				pnlFamilyInfo.Visible=false;
				pnlEducation.Visible=false;
				Panel3.Visible=false;
				this.pnlBondPaper.Visible=false;
				this.pnlFunctionalDesignation.Visible=false;
				this.pnlCompensation.Visible=true;
				pnlContract.Visible=false;
			}
			//string errorString="";
		}

		private void btnAddCompensation_Click(object sender, System.EventArgs e)
		{
			con.Open();
			GeoRabtaSite.admin.Convertor c=new GeoRabtaSite.admin.Convertor();
			bool state=c.updateCom(this.txtGrossSal.Text,txtBasicSal.Text,this.txtHouseRent.Text,this.txtUtility.Text,this.txtMobileEntitlement.Text,this.txtPetrolEntitlement.Text,this.ddCarConveyance.SelectedValue.ToString(),this.txtCarDescription.Text,this.txtEmpInfoOthers.Text,this.txtEmpInfoCode.Text,Session["user_id"].ToString().Trim(),con,rdoSalType.SelectedValue.ToString(),ddCurrency.SelectedValue.ToString());
			
			/*string InsertCompensation="update t_employee set mobileentitle=@mobileentitle,petrolentitle=@petrolentitle,carconveyance=@carconveyance,cardescription=@cardescription,grosssalary=@grosssalary,basicsalary=@basicsalary,houserent=@houserent,utilities=@utilities,comcreateby=@comcreateby,others=@others,commodifyby=@commodifyby where pcode=@pcode";
			SqlCommand cmd=new SqlCommand(InsertCompensation,con);
			SqlParameter _mobileentitle=new SqlParameter("@mobileentitle",SqlDbType.Int);
			SqlParameter _petrolentitle=new SqlParameter("@petrolentitle",SqlDbType.Int);
			SqlParameter _car=new SqlParameter("@carconveyance",SqlDbType.TinyInt);
			SqlParameter _cardescription=new SqlParameter("@cardescription",SqlDbType.VarChar,1000);
			SqlParameter _grosssalary=new SqlParameter("@grosssalary",SqlDbType.Int);
			SqlParameter _basicsalary=new SqlParameter("@basicsalary",SqlDbType.Int);
			SqlParameter _houserent=new SqlParameter("@houserent",SqlDbType.Int);
			SqlParameter _utilities=new SqlParameter("@utilities",SqlDbType.Int);
			SqlParameter _createby=new SqlParameter("@comcreateby",SqlDbType.VarChar);
			SqlParameter _pcode=new SqlParameter("@pcode",SqlDbType.VarChar);
			SqlParameter _others=new SqlParameter("@others",SqlDbType.VarChar,1000);
			SqlParameter _modifyby=new SqlParameter("@commodifyby",SqlDbType.VarChar);

			_mobileentitle.Value=this.txtMobileEntitlement.Text;
			_petrolentitle.Value=this.txtPetrolEntitlement.Text;
			_car.Value=this.ddCarConveyance.SelectedValue.ToString();
			_cardescription.Value=this.txtCarDescription.Text;
			_grosssalary.Value=this.txtGrossSal.Text;
			_basicsalary.Value=this.txtBasicSal.Text;
			_houserent.Value=this.txtHouseRent.Text;
			_utilities.Value=this.txtUtility.Text;
			_createby.Value=Session["user_id"].ToString();
			_pcode.Value=this.txtEmpInfoCode.Text;
			_others.Value=this.txtEmpInfoOthers.Text;
			_modifyby.Value=Session["user_id"].ToString();
			cmd.Parameters.Add(_mobileentitle);
			cmd.Parameters.Add(_petrolentitle);
			cmd.Parameters.Add(_car);
			cmd.Parameters.Add(_cardescription);
			cmd.Parameters.Add(_grosssalary);
			cmd.Parameters.Add(_basicsalary);
			cmd.Parameters.Add(_houserent);
			cmd.Parameters.Add(_utilities);
			cmd.Parameters.Add(_createby);
			cmd.Parameters.Add(_pcode);
			cmd.Parameters.Add(_others);
			cmd.Parameters.Add(_modifyby);
			cmd.ExecuteNonQuery();*/
			this.btnAddCompensation.Enabled=false;
			this.lblComMsg.Text="Employee Compensation Details Have Been Entered";
			this.lblComMsg.Visible=true;
			con.Close();

			con.Open();
			SqlCommand cmd=new SqlCommand("insert into t_userlog(userid,action,description,accessdate) values(@userid,@action,@description,@accessdate)",con);
			SqlParameter _userid=new SqlParameter("@userid",SqlDbType.Char);
			SqlParameter _action=new SqlParameter("@action",SqlDbType.VarChar);
			SqlParameter _description=new SqlParameter("@description",SqlDbType.VarChar);
			SqlParameter _accessdate=new SqlParameter("@accessdate",SqlDbType.DateTime);
			_userid.Value=Session["user_id"].ToString().Trim();
			_action.Value="Add Compensation";
			_description.Value="Added Profile Details:Pcode="+this.txtEmpInfoCode.Text.Trim()+",Name="+this.txtEmpInfoName.Text;
			_accessdate.Value=DateTime.Now;
			cmd.Parameters.Add(_userid);
			cmd.Parameters.Add(_action);
			cmd.Parameters.Add(_description);
			cmd.Parameters.Add(_accessdate);
			cmd.ExecuteNonQuery();
			con.Close();
			con.Dispose();
		}

		private void txtGrossSal_TextChanged(object sender, System.EventArgs e)
		{
			RegularExpressionValidator12.Validate();
			RequiredFieldValidator10.Validate();
			if(RegularExpressionValidator12.IsValid && this.txtGrossSal.Text.Length >0 && RequiredFieldValidator10.IsValid)
			{
				double GrossSal=double.Parse(this.txtGrossSal.Text);
				double BasicSal=Math.Round(GrossSal/1.55);
				double HouseRent=Math.Round(GrossSal/1.55*0.45);
				double Utility=Math.Round(GrossSal/1.55*0.1);
				this.txtBasicSal.Text=BasicSal.ToString();
				this.txtHouseRent.Text=HouseRent.ToString();
				this.txtUtility.Text=Utility.ToString();
			}
			else
			{
			 
			}
		}

		private void btnCancelCompensation_Click(object sender, System.EventArgs e)
		{
			this.txtMobileEntitlement.Text="0";
			this.txtPetrolEntitlement.Text="0";
			this.ddCarConveyance.SelectedIndex=0;
			this.txtCarDescription.Text="";
			this.txtGrossSal.Text="0";
			this.txtBasicSal.Text="0";
			this.txtHouseRent.Text="0";
			this.txtUtility.Text="0";
			this.txtEmpInfoOthers.Text="";
			rdoSalType.SelectedIndex=-1;
			ddCurrency.SelectedValue="204";
		}

		private void ddDesignation_SelectedIndexChanged_1(object sender, System.EventArgs e)
		{
			GetFDesignations();
		}
		public void GetFDesignations()
		{
			this.lblFV.Text="";
			con.Open();
			SqlCommand cmd=new SqlCommand("select d.desigid,d.designation,c.cat_name,compensatoryoff=case c.compensatoryoff when '1' then '1' when '0' then '0' else '-1'end from t_categorization c,t_designation d where d.category=c.cat_id and d.status=1 and d.desigid="+this.ddDesignation.SelectedValue.ToString()+"",con);
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			this.rdCompansatory.SelectedValue=rd[3].ToString();
			rd.Close();
			
			this.ddFDesignation.Items.Clear();
			ListItem item=new ListItem("Select--Functional Designation","0");
			this.ddFDesignation.Items.Add(item);
			//string getVal="select f.fdesigid,f.functionaltitle from t_functionaldesignation f,t_designation d where f.functionaldesignation=d.desigid And d.status=1 And f.isactive=1 And d.desigid =Any(select desigid from t_employee where pcode='"+ this.txtEmpInfoCode.Text +"' And del=1) order by f.functionaltitle";
			//string getVal="select distinct f.fdesigid,f.functionaltitle,d.desigid from t_functionaldesignation f,t_designation d,t_directreporting dr where f.functionaldesignation=d.desigid And d.status=1 And f.isactive=1 And dr.fdesigid=f.fdesigid and dr.isactive=1 And d.desigid ="+this.ddDesignation.SelectedValue.ToString()+" order by f.functionaltitle";
			string getVal="select distinct f.fdesigid,f.functionaltitle,d.desigid from t_functionaldesignation f,t_designation d,t_directreporting dr where f.functionaldesignation=d.desigid And d.status=1 And f.isactive=1 And (dr.fdesigid=f.fdesigid or dr.sfdesigid=f.fdesigid) and dr.isactive=1 And d.desigid ="+this.ddDesignation.SelectedValue.ToString()+" order by f.functionaltitle";
			cmd=new SqlCommand(getVal,con);
			rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
				this.ddFDesignation.Items.Add(itm);
			}
			rd.Close();
			con.Close();
		}

		private void ddProbabtion_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			if(this.CalenderDOJ.ValidDateEntered)
			{
				if(this.ddProbabtion.SelectedValue !="-1" && this.ddProbabtion.SelectedValue !="-2")
				{
					//this.CalenderCOD.Enabled=true;
					this.txtProbabtionOther.Visible=false;
					this.txtProbabtionOther.Text="";
					if(this.ddProbabtion.SelectedValue =="0")
					{
						this.CalenderCOD.SelectedDate=this.CalenderDOJ.SelectedDate;
					}
					else
					{
						this.CalenderCOD.SelectedDate=this.CalenderDOJ.SelectedDate.AddMonths(Int32.Parse(this.ddProbabtion.SelectedValue.ToString()));
					}
				}
				else if(this.ddProbabtion.SelectedValue=="-2")
				{
					this.txtProbabtionOther.Visible=true;
					this.CalenderCOD.Clear();
					this.CalenderCOD.Enabled=false;
				}
				else
				{
					this.CalenderCOD.Clear();
					this.CalenderCOD.Enabled=false;
					this.txtProbabtionOther.Visible=false;
					this.txtProbabtionOther.Text="";
				}
			}
			else
			{
				this.CalenderCOD.Clear();
				this.CalenderCOD.Enabled=false;
			}
		}

		private void txtProbabtionOther_TextChanged(object sender, System.EventArgs e)
		{
			if(this.CalenderDOJ.ValidDateEntered)
			{
				if(this.txtProbabtionOther.Text.Length <=0)
				{
					this.CalenderCOD.Clear();
					this.CalenderCOD.Enabled=false;
				}
				else
				{
					this.CalenderCOD.SelectedDate=this.CalenderDOJ.SelectedDate.AddMonths(Int32.Parse(this.txtProbabtionOther.Text));
					//this.CalenderCOD.Enabled=true;
				}
			}
			else
			{
				this.CalenderCOD.Clear();
				this.CalenderCOD.Enabled=false;
			}
		}

		private void ddFDesignation_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			con.Open();
			string VerifyInDH="select fdesigid from t_designationhistory where fdesigid="+this.ddFDesignation.SelectedValue.ToString()+"";
			SqlCommand cmd=new SqlCommand(VerifyInDH,con);
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			if(rd.HasRows)
			{
				rd.Close();
				//string getVacancy="select f.noofvacancies-sum(h.isactive) from t_designationhistory h,t_functionaldesignation f where f.fdesigid="+ this.ddFDesignation.SelectedValue.ToString() +" and f.fdesigid=h.fdesigid group by f.noofvacancies";
				string getVacancy="SELECT noofvacancies -(SELECT COUNT(pcode) AS COE FROM dbo.t_DesignationHistory WHERE (isactive = 1) AND (fdesigid = f.fdesigid)) AS Vac FROM dbo.t_FunctionalDesignation AS f WHERE (fdesigid = "+this.ddFDesignation.SelectedValue.ToString()+") AND (isactive = 1)";
				cmd=new SqlCommand(getVacancy,con);
				rd=cmd.ExecuteReader();
				rd.Read();
				if(rd.HasRows)
				{
					this.lblFV.Text=rd[0].ToString();
				}
				else
				{
					this.lblFV.Text="";
				}
				rd.Close();
			}
			else
			{
				rd.Close();
				string getVacancy="select f.noofvacancies from t_functionaldesignation f where f.fdesigid="+ this.ddFDesignation.SelectedValue.ToString() +" group by f.noofvacancies";
				cmd=new SqlCommand(getVacancy,con);
				rd=cmd.ExecuteReader();
				rd.Read();
				if(rd.HasRows)
				{
					this.lblFV.Text=rd[0].ToString();
				}
			}
			con.Close();
		}

		private void CalenderDOJ_DateChanged(object sender, System.EventArgs e)
		{
			if(this.CalenderDOJ.ValidDateEntered)
			{
				this.ddeStatus.SelectedValue="0";
				if(this.ddProbabtion.SelectedValue !="-1" && this.ddProbabtion.SelectedValue !="-2")
				{
					//this.CalenderCOD.Enabled=true;
					this.txtProbabtionOther.Visible=false;
					if(this.ddProbabtion.SelectedValue =="0")
					{
						this.CalenderCOD.SelectedDate=this.CalenderDOJ.SelectedDate;
					}
					else
					{
						this.CalenderCOD.SelectedDate=this.CalenderDOJ.SelectedDate.AddMonths(Int32.Parse(this.ddProbabtion.SelectedValue.ToString()));
					}
				}
				else if(this.txtProbabtionOther.Text.Length !=0)
				{
					this.CalenderCOD.SelectedDate=this.CalenderDOJ.SelectedDate.AddMonths(Int32.Parse(this.txtProbabtionOther.Text));
					//this.CalenderCOD.Enabled=true;
				}
			}
			else
			{
				this.ddeStatus.SelectedValue="1";
				this.CalenderCOD.Clear();
				this.CalenderCOD.Enabled=false;
			}
		}

		private void ddeStatus_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			if(this.ddeStatus.SelectedValue.ToString()=="1")
			{
				this.RequiredFieldValidator7.Enabled=false;
				RequiredFieldValidator9.Enabled=false;
				RequiredFieldValidator11.Enabled=true;
				this.CalenderDOJ.Enabled=false; 
				Label6.Visible=false;
				Label7.Visible=false;
				this.ddNewJoiner.Enabled=false;
				this.CTDOJ.Enabled=true;
				this.CalenderDOJ.Clear();
				this.CalenderCOD.Clear();
			}
			else
			{
				
				this.RequiredFieldValidator7.Enabled=true;
				RequiredFieldValidator9.Enabled=true;
				RequiredFieldValidator11.Enabled=false;
				this.CalenderDOJ.Enabled=true;

				Label6.Visible=true;
				Label7.Visible=true;
				this.ddNewJoiner.Enabled=true;
				this.CTDOJ.Enabled=false;
				this.CTDOJ.Clear();
				
			}
		}

		private void ImgRelative_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("RelativeInfo.aspx?id="+this.txtEmpInfoCode.Text.Trim()+"&name="+this.txtEmpInfoName.Text+"&page=1");
		}

		private void ddNetwork_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			GetNetworkDepts();
		}
		public void GetNetworkDepts()
		{
			ddDesignation.Items.Clear();
			ddFDesignation.Items.Clear();
			ddOpertingCity.Items.Clear();
			ListItem itm=new ListItem("Select--Designation","0");
			ddDesignation.Items.Add(itm);
			itm=new ListItem("Select--Functional Designation","0");
			ddFDesignation.Items.Add(itm);
			itm=new ListItem("Select--Operating Station","0");
			ddOpertingCity.Items.Add(itm);
			EmployeeInfo();
		}

		private void imgContract_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			pnlPersonal.Visible=false;
			pnlEmployeeInfo.Visible=false;
			pnlFamilyInfo.Visible=false;
			pnlEducation.Visible=false;
			this.pnlBondPaper.Visible=false;
			this.pnlFunctionalDesignation.Visible=false;
			this.pnlCompensation.Visible=false;
			pnlContract.Visible=true;
			SetContractStatus();
			GetContractDetail();
		}
		public void SetContractStatus()
		{
			tdContract.Visible=false;
			ContractController c= ContractController.getInstance();
			ddDuration.Items.Clear();
			ListItem defaultDuration=new ListItem("Select--Duration","0");
			ddDuration.Items.Add(defaultDuration);
			//=====================Set Duration ==================//
			DataSet ds=c.GetStatus("1");
			foreach(DataRow r in ds.Tables[0].Rows)
			{
				ListItem durationItem=new ListItem();
				durationItem.Value= r["cas_Id"].ToString();
				durationItem.Text= r["cas_Status"].ToString();
				ddDuration.Items.Add(durationItem);
			}
			//===================Set Serving Status============//
			ddServingStatus.Items.Clear();
			ds=c.GetStatus("3");
			foreach(DataRow r in ds.Tables[0].Rows)
			{
				ListItem servingItem=new ListItem();
				servingItem.Value= r["cas_Id"].ToString();
				servingItem.Text= r["cas_Status"].ToString();
				ddServingStatus.Items.Add(servingItem);
			}
			//=================== Set Button ======================//
			DataSet dsStatus=c.GetContractStatus(txtEmpInfoCode.Text.Trim());
			if(dsStatus.Tables[0].Rows[0][0].ToString()=="New")
			{
				btnCNew.Enabled=true;
				btnCRenew.Enabled=false;
			}
			if(dsStatus.Tables[0].Rows[0][0].ToString()=="Renew")
			{
				btnCNew.Enabled=false;
				btnCRenew.Enabled=true;
			}
			lblLastExpiry.Text=dsStatus.Tables[0].Rows[0][1].ToString();
			lblStatus.Text=dsStatus.Tables[0].Rows[0][2].ToString();
			lblLastContractId.Text=dsStatus.Tables[0].Rows[0][3].ToString();
			//=====================================================//
		}

		public void GetContractDetail()
		{
			ContractController c=ContractController.getInstance();
			DataSet ds=c.GetContractDetail(txtEmpInfoCode.Text.Trim());
			dgContractHistory.DataSource=ds;
			dgContractHistory.DataBind();
			string temp="ContractChange.aspx?id=";
			for(int i=0;i<dgContractHistory.Items.Count;i++)
			{
				LinkButton lnk=(LinkButton)this.dgContractHistory.Items[i].FindControl("lnkDisable");
				if(this.dgContractHistory.Items[i].Cells[10].Text=="8")
				{
					temp+=this.dgContractHistory.Items[i].Cells[0].Text;
					//lnk.Attributes.Add("onclick","window.open('"+temp+"','myPage','status=no,toolbar=no,scrollbars=yes,resizable=yes');return false;");
					lnk.Attributes.Add("onclick","PopupCenter('"+temp+"','myNewPage','600','300');return false;");
				}
				else
				{
					lnk.Enabled=false;
				}
				
			}
		 
		}
		public void RedirectToMenu()
		{
			int paramCount=Request.QueryString.Count;
			if(paramCount >0)
			{
				pnlPersonal.Visible=false;
				pnlEmployeeInfo.Visible=false;
				pnlFamilyInfo.Visible=false;
				pnlEducation.Visible=false;
				this.pnlBondPaper.Visible=false;
				this.pnlFunctionalDesignation.Visible=false;
				this.pnlCompensation.Visible=false;
				SetContractStatus();
				GetContractDetail();
				pnlContract.Visible=true;
			}
		}
		public void GetCurrency()
		{
			GeoRabtaSite.admin.Convertor c=new GeoRabtaSite.admin.Convertor();
			DataSet ds=c.GetCurrency();
			foreach(DataRow r in ds.Tables[0].Rows)
			{
				ListItem currencyItem=new ListItem();
				currencyItem.Value= r["ID"].ToString();
				currencyItem.Text= r["Title"].ToString();
				ddCurrency.Items.Add(currencyItem);
			}
			ddCurrency.SelectedValue="204";
		}
		public void SetExpiry(object sender)
		{
			if(ddDuration.SelectedItem.Value=="0" )
			{
				if(sender.ToString()=="System.Web.UI.WebControls.DropDownList")
				{
					cpContarctStart.SelectedDate=DateTime.Now;
				}
				cpContractExpiry.Nullable=true;
			}
			else if(ddDuration.SelectedItem.Value=="1" )
			{
				cpContractExpiry.SelectedDate=DateTime.Now;
			}
			else
			{
				ContractController c=ContractController.getInstance();
				cpContractExpiry.SelectedDate=DateTime.Parse(c.CalculateExpiry(cpContarctStart.SelectedDate.ToString(),ddDuration.SelectedItem.Text.ToString()));
			}
		}

		public void AddNewContract(string servingStatus)
		{
			ContractController c=ContractController.getInstance();
			c.AddNewContract(txtEmpInfoCode.Text,cpContarctStart.SelectedDate,cpContractExpiry.SelectedDate,ddDuration.SelectedValue.ToString(),"8",servingStatus,txtCRemarks.Text.Trim(),Session["user_id"].ToString(),Session["user_id"].ToString());
			DataSet dsStatus=c.GetContractStatus(txtEmpInfoCode.Text);
			if(dsStatus.Tables[0].Rows[0][0].ToString()=="New")
			{
				btnCNew.Enabled=true;
				btnCRenew.Enabled=false;
			}
			if(dsStatus.Tables[0].Rows[0][0].ToString()=="Renew")
			{
				btnCNew.Enabled=false;
				btnCRenew.Enabled=true;
			}
			lblLastExpiry.Text=dsStatus.Tables[0].Rows[0][1].ToString();
			lblStatus.Text=dsStatus.Tables[0].Rows[0][2].ToString();
			lblLastContractId.Text=dsStatus.Tables[0].Rows[0][3].ToString();
			ddDuration.SelectedIndex=0;
			GetContractDetail();
		}
		public void RenewContract(string statusCode)
		{
			ContractController c=ContractController.getInstance();
			string lastContractId=lblLastContractId.Text;
			string statusId=statusCode;
			string remarks="";
			string servingStatus="15";
			if(tdContract.Visible)
				servingStatus=ddServingStatus.SelectedValue.ToString();
			c.UpdateContract(lastContractId,statusId,remarks,Session["user_id"].ToString());
			AddNewContract(servingStatus);
		}
		private void ddDuration_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			string text=ddDuration.SelectedItem.Value;
			string val=ddDuration.SelectedValue.ToString();
			SetExpiry(sender);
		}

		private void cpContarctStart_DateChanged(object sender, System.EventArgs e)
		{
			SetExpiry(sender);
		}

		private void btnCNew_Click(object sender, System.EventArgs e)
		{
			AddNewContract("15");
		}

		private void btnCRenew_Click(object sender, System.EventArgs e)
		{
			DateTime ed=DateTime.Parse(lblLastExpiry.Text);
			TimeSpan sp;
			if(lblStatus.Text=="8")
			{
				sp=ed-DateTime.Now;
				if(sp.Days>=1)
				{
					lblWarning.Text="Day(s) still remain in last contract expiry. Use de-active option to close the last active contract to create new contract";
					pnlNotification.Visible=true;
					btnCRenew.Enabled=false;
					btnOK.Enabled=false;
					return;
				}
				else
				{ 
					DateTime newContract = cpContarctStart.SelectedDate;
					sp=ed-newContract;
					if(newContract < ed)
					{
						//Response.Write("<script>window.alert('Renewing contract start date must be greater than last contract expiry');</script>");
						lblWarning.Text="Renewing contract start date must be greater than last contract expiry";
						pnlNotification.Visible=true;
						btnCRenew.Enabled=false;
						btnOK.Enabled=false;
						return;
				
					}
					else if(sp.Days <-1)
					{
						lblWarning.Text="Renewing contract start date is not equal to last contract expiry. Are you sure to renew? Provide employee serving status and Click OK to proceed or Cancel to rollback your action.";
						pnlNotification.Visible=true;
						btnCRenew.Enabled=false;
						tdContract.Visible=true;
						btnOK.Enabled=true;
						return;
					}
					else
					{
						RenewContract("9");
					}
				}
			}
			else
			{
				DateTime newContract = cpContarctStart.SelectedDate;
				sp=ed-newContract;
				if(newContract < ed)
				{
					//Response.Write("<script>window.alert('Renewing contract start date must be greater than last contract expiry');</script>");
					lblWarning.Text="Renewing contract start date must be greater than last contract expiry";
					pnlNotification.Visible=true;
					btnCRenew.Enabled=false;
					btnOK.Enabled=false;
					return;
				
				}
				else if(sp.Days <-1)
				{
					lblWarning.Text="Renewing contract start date is not equal to last contract expiry. Are you sure to renew? Click OK to proceed or Cancel to rollback your action.";
					pnlNotification.Visible=true;
					btnCRenew.Enabled=false;
					tdContract.Visible=true;
					btnOK.Enabled=true;
					return;
				}
				else
				{
					RenewContract("9");
				}
			}
		}

		private void btnOK_Click(object sender, System.EventArgs e)
		{
			RenewContract("9");
			pnlNotification.Visible=false;
			btnCRenew.Enabled=true;
		}

		private void btnNotifyCancel_Click(object sender, System.EventArgs e)
		{
			pnlNotification.Visible=false;
			btnCRenew.Text="Renew";
			btnCRenew.Enabled=true;
		}

		private void ddPosType_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			txtName.Text="";
			txtRefCode.Text="";
			txtName.Enabled=false;
			RequiredFieldValidator21.Enabled=false;
			ddNetwork.Enabled=false;
			ddDepartment.Enabled=false;
			ddDesignation.Enabled=false;
			ddNetwork.SelectedIndex=0;
			ddDepartment.SelectedIndex=0;
			ddDesignation.SelectedIndex=0;
			ddDesignation.Items.Clear();
			ddFDesignation.Items.Clear();
			ddDepartment.Items.Clear();
			ListItem itm=new ListItem("Select--Designation","0");
			ddDesignation.Items.Add(itm);
			itm=new ListItem("Select--Functional Designation","0");
			ddFDesignation.Items.Add(itm);
			itm=new ListItem("Select--Department","0");
			ddDepartment.Items.Add(itm);
			lblFV.Text="";
			if(ddPosType.SelectedValue!="0")
			{
				if(ddPosType.SelectedValue=="1")
				{
					txtName.Enabled=false;
					RequiredFieldValidator21.Enabled=false;
					
					ddNetwork.Enabled=true;
					ddDepartment.Enabled=true;
					ddDesignation.Enabled=true;
				}
				else
				{
					txtName.Enabled=true;
					RequiredFieldValidator21.Enabled=true;
					ddNetwork.SelectedIndex=0;
					ddNetwork.Enabled=false;
					ddDepartment.Enabled=false;
					ddDesignation.Enabled=false;
				}
			}
		}

		private void txtRefCode_TextChanged(object sender, System.EventArgs e)
		{
			if(txtRefCode.Text.Length>0)
			{
				string []ProfileIds=txtRefCode.Text.Split('#');
				bool isExist=false;
				for(int i=0;i<ddNetwork.Items.Count;i++)
				{
					if(ddNetwork.Items[i].Value.Equals(ProfileIds[0]))
					{
						ddNetwork.SelectedIndex=i;
						isExist=true;
						break;
					}
				}
				if(isExist)
				{
					bool isDeptExist=false;
					ddDesignation.Items.Clear();
					ddFDesignation.Items.Clear();
					ddDepartment.Items.Clear();
					ListItem itm=new ListItem("Select--Designation","0");
					ddDesignation.Items.Add(itm);
					itm=new ListItem("Select--Functional Designation","0");
					ddFDesignation.Items.Add(itm);
					itm=new ListItem("Select--Department","0");
					ddDepartment.Items.Add(itm);
					GetNetworkDepts();
					for(int i=0;i<ddDepartment.Items.Count;i++)
					{
						if(ddDepartment.Items[i].Value.Equals(ProfileIds[1]))
						{
							ddDepartment.SelectedIndex=i;
							isDeptExist=true;
							break;
						}
					}
					if(isDeptExist)
					{
						lblFV.Text="";
						getDes();
						for(int i=0;i<ddDesignation.Items.Count;i++)
						{
							if(ddDesignation.Items[i].Value.Equals(ProfileIds[2]))
							{
								ddDesignation.SelectedIndex=i;
								GetFDesignations();
								break;
							}
						}
					}
				}
				
			}
		}
	}
}
