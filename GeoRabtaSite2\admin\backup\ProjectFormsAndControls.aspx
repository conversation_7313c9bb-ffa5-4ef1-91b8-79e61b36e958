<%@ Register TagPrefix="ew" Namespace="eWorld.UI" Assembly="eWorld.UI" %>
<%@ Page language="c#" Codebehind="ProjectFormsAndControls.aspx.cs" AutoEventWireup="false" Inherits="GeoRabtaSite.admin.ProjectFormsAndControls" smartNavigation="True"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Rabta ::</title>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="../StyleSheet1.css" type="text/css" rel="stylesheet">
		<style type="text/css">.style1 { FONT-WEIGHT: bold; FONT-SIZE: 8px }
	.style2 { FONT-WEIGHT: bold; FONT-SIZE: 9pt }
		</style>
	</HEAD>
	<body bottomMargin="0" bgProperties="fixed" leftMargin="0" background="../images/bg.jpg"
		topMargin="0" rightMargin="0">
		<form id="myForm" name="MyForm" runat="server">
			<TABLE id="Table1" height="100%" cellSpacing="0" cellPadding="0" width="1004" border="0">
				<TR>
					<TD height="80"></TD>
				</TR>
				<TR>
					<TD vAlign="top" align="center">
						<TABLE id="Table2" cellSpacing="0" cellPadding="0" width="100%" border="0">
							<TR>
								<TD vAlign="top" align="center" width="150" height="175"></TD>
								<TD vAlign="top" align="left" width="650">
									<table cellSpacing="0" cellPadding="2" width="100%" align="center" bgColor="white" border="0">
										<tr>
											<td background="../images/PanelTop.jpg" bgColor="#004477" height="63">
												<table cellSpacing="0" cellPadding="0" width="100%" border="0">
													<tr>
														<td width="5%" height="36">&nbsp;</td>
														<td width="95%">&nbsp;
															<asp:label id="Label1" runat="server" Height="32px" Font-Bold="True" Font-Size="Medium" ForeColor="Transparent"
																BackColor="Transparent" BorderColor="Transparent"></asp:label></td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><asp:linkbutton id="lnkProjects" runat="server" Width="90px" Height="24px" CssClass="Button">Projects</asp:linkbutton><asp:linkbutton id="pnlWebForms" runat="server" Width="90px" Height="24px" CssClass="Button" Enabled="False">Web Forms</asp:linkbutton><asp:linkbutton id="pnlContols" runat="server" Width="90px" Height="24px" CssClass="Button" Enabled="False">Controls</asp:linkbutton></td>
										</tr>
										<TR>
											<TD><asp:panel id="pnlProjects" runat="server" Width="100%">
													<TABLE id="Table3" cellSpacing="0" cellPadding="3" width="100%" border="0">
														<TR>
															<TD width="300"><STRONG>Project Name:</STRONG>
																<asp:Label id="lblProjectID" runat="server" Visible="False">Label</asp:Label><BR>
																<asp:TextBox id="txtProjectName" runat="server" Width="100%" CssClass="TextBox"></asp:TextBox></TD>
															<TD></TD>
															<TD width="300"><STRONG>Project Date:<BR>
																</STRONG>
																<ew:CalendarPopup id="dtpProjectDate" runat="server">
																	<TextboxLabelStyle CssClass="TextBox"></TextboxLabelStyle>
																	<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></WeekdayStyle>
																	<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="Yellow"></MonthHeaderStyle>
																	<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																		BackColor="AntiqueWhite"></OffMonthStyle>
																	<ButtonStyle Height="20px" CssClass="Button"></ButtonStyle>
																	<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></GoToTodayStyle>
																	<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="LightGoldenrodYellow"></TodayDayStyle>
																	<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="Orange"></DayHeaderStyle>
																	<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="LightGray"></WeekendStyle>
																	<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="Yellow"></SelectedDateStyle>
																	<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></ClearDateStyle>
																	<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></HolidayStyle>
																</ew:CalendarPopup></TD>
														</TR>
														<TR>
															<TD colSpan="3"><STRONG>Project Desctiption:</STRONG><BR>
																<asp:TextBox id="txtProjectDescription" runat="server" Width="100%" TextMode="MultiLine" Rows="5"
																	CssClass="TextBox"></asp:TextBox></TD>
														</TR>
														<TR>
															<TD colSpan="3">Database:<BR>
																<asp:TextBox id="txtDatabaseName" runat="server" Width="100%" CssClass="TextBox"></asp:TextBox></TD>
														</TR>
														<TR>
															<TD colSpan="3">
																<asp:CheckBox id="chkIsActive" runat="server" Text="Active Project"></asp:CheckBox></TD>
														</TR>
														<TR>
															<TD colSpan="3">
																<asp:Button id="cmdAddNewProject" runat="server" Width="80px" Text="New" CssClass="Button"></asp:Button>&nbsp;
																<asp:Button id="cmdSaveProject" runat="server" Width="80px" Text="Save" CssClass="Button"></asp:Button>&nbsp;
																<asp:Button id="cmdDeleteProject" runat="server" Width="80px" Text="Delete" CssClass="Button"
																	Enabled="False"></asp:Button>&nbsp;
																<asp:Button id="cmdCancelProject" runat="server" Width="80px" Text="Cancel" CssClass="Button"
																	Enabled="False"></asp:Button></TD>
														</TR>
														<TR>
															<TD colSpan="3">
																<asp:DataGrid id="dgPro" runat="server" Width="100%" BackColor="White" BorderColor="#CC9966" BorderStyle="None"
																	BorderWidth="1px" CellPadding="4" AutoGenerateColumns="False" DataKeyField="projectID">
																	<SelectedItemStyle Font-Bold="True" ForeColor="#663399" BackColor="#FFCC66"></SelectedItemStyle>
																	<ItemStyle ForeColor="#330099" BackColor="White"></ItemStyle>
																	<HeaderStyle Font-Bold="True" ForeColor="#FFFFCC" BackColor="#990000"></HeaderStyle>
																	<FooterStyle ForeColor="#330099" BackColor="#FFFFCC"></FooterStyle>
																	<Columns>
																		<asp:BoundColumn DataField="projectName" HeaderText="Project Name"></asp:BoundColumn>
																		<asp:BoundColumn DataField="projectDate" HeaderText="Project Date"></asp:BoundColumn>
																		<asp:BoundColumn Visible="False" DataField="projectID" HeaderText="projectID"></asp:BoundColumn>
																		<asp:ButtonColumn Text="Select" CommandName="Select"></asp:ButtonColumn>
																		<asp:EditCommandColumn ButtonType="LinkButton" UpdateText="Update" CancelText="Cancel" EditText="Edit"></asp:EditCommandColumn>
																		<asp:ButtonColumn Text="Delete" CommandName="Delete"></asp:ButtonColumn>
																	</Columns>
																	<PagerStyle HorizontalAlign="Center" ForeColor="#330099" BackColor="#FFFFCC"></PagerStyle>
																</asp:DataGrid></TD>
														</TR>
													</TABLE>
												</asp:panel><asp:panel id="Panel2" runat="server" Width="100%">Panel</asp:panel>
												<asp:panel id="Panel3" runat="server" Width="100%">Panel</asp:panel>
												<P><ew:collapsablepanel id="CollapsablePanel1" runat="server" AllowSliding="True" AllowTitleExpandCollapse="True"
														AllowTitleRowExpandCollapse="True">
														<P>testand test</P>
													</ew:collapsablepanel>
												<P></P>
												<P>&nbsp;</P>
											</TD>
										</TR>
									</table>
								</TD>
								<TD vAlign="top" align="center" width="180"></TD>
							</TR>
						</TABLE>
					</TD>
				</TR>
				<TR>
					<TD class="Fotter" align="center" height="20">Copyright © 2005 Independent Media 
						Corporation <A href="http://www.geo.tv">www.geo.tv</A></TD>
				</TR>
			</TABLE>
		</form>
	</body>
</HTML>
