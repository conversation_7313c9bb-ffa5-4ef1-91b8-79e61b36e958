using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;
namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for GeoIdeas.
	/// </summary>
	public class PulseQueueList: System.Web.UI.Page
	{
		
		protected System.Web.UI.HtmlControls.HtmlTable tabForm;
		protected System.Web.UI.WebControls.ImageButton ImageButton1;
		protected System.Web.UI.WebControls.ImageButton ImageButton2;
		protected System.Web.UI.WebControls.DataGrid dgLivePulse;
		protected System.Web.UI.WebControls.Panel pnlMain1;
		protected System.Web.UI.WebControls.Panel pnlSubMain1;
		protected System.Web.UI.WebControls.Label lblPulseID;
		protected System.Web.UI.WebControls.Label lblPcode;
		protected System.Web.UI.WebControls.Label lblName;
		protected System.Web.UI.WebControls.Label lblDepartment;
		protected System.Web.UI.WebControls.Label lblDesignation;
		protected System.Web.UI.WebControls.Label lblpDepartment;
		protected System.Web.UI.WebControls.Label lblDescription;
		protected System.Web.UI.WebControls.Label lblCreateon;
		protected System.Web.UI.WebControls.Label lblType;
		protected System.Web.UI.WebControls.Label lblTitle;
		protected System.Web.UI.WebControls.Label lblpStation;
		protected System.Web.UI.WebControls.Panel pnlSubMain2;
		protected System.Web.UI.WebControls.DataGrid dgPulseBlog;
		protected System.Web.UI.WebControls.Label lblhPulseID;
		protected System.Web.UI.WebControls.TextBox txtBlog;
		protected System.Web.UI.WebControls.Panel pnlSubMain3;
		protected System.Web.UI.WebControls.DropDownList ddActionCode;
		protected System.Web.UI.HtmlControls.HtmlTableCell r1;
		protected eWorld.UI.CalendarPopup calInterimDate;
		protected System.Web.UI.HtmlControls.HtmlTableCell r2;
		protected System.Web.UI.WebControls.CheckBoxList chkReportingList;
		protected System.Web.UI.WebControls.Label Label1;
		protected System.Web.UI.WebControls.Button btnSend;
		protected System.Web.UI.WebControls.Image Image1;
        SqlConnection con;  
		
		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		private void InitializeComponent()
		{
			this.dgLivePulse.SelectedIndexChanged += new System.EventHandler(this.dgLivePulse_SelectedIndexChanged);
			this.ddActionCode.SelectedIndexChanged += new System.EventHandler(this.ddActionCode_SelectedIndexChanged);
			this.Load += new System.EventHandler(this.Page_Load);

		}
#endregion
		private void Page_Load(object sender, System.EventArgs e)
		{
			if(!IsPostBack)
			{
				GetMyPulse(Session["user_id"].ToString());
				GetActionCode();
				r1.Visible=false;
				r2.Visible=false;
			}
		}

		public void GetMyPulse(string pcode)
		{
		 con=new SqlConnection(Connection.ConnectionString);
		 con.Open();
		 SqlDataAdapter dr=new SqlDataAdapter("select '<img src=../empthumbnail/' + e.pic + ' width=55>' as pic,e.name as [Pulse Initiate By],p.pid as [Pulse ID],p.title,p.description,p.pulsecreateon,[Current Status]=case p.currentstatus when 0 then 'InActive' when 1 then 'In Process' when 2 then 'Completed' when 3 then 'Re-Open' when 4 then 'Interim Closed'end,[TaskSheet]=case (case p.pid when p.pid then(select count(pulseid) from t_pulsetasksheet where addto='P2284' and pulseid=p.pid) end) when 0 then 'No' else 'Yes' end,Blog=case p.pid when p.pid then(select '('+cast(count(*) as varchar)+')' from t_pulseprogressblog where p.pid=pulseid)end,c.pulsecategory as pulsetype,e.pcode,ides=case p.sendto when p.sendto then(select d.designation from t_designation d where d.desigid=e.desigid)end,idept=case p.sendto when p.sendto then(select dept.deptname from t_department dept,t_designation d where e.desigid=d.desigid and dept.deptid=d.deptid)end,pdept=case p.deptid when p.deptid then(select dept.deptname from t_department dept where dept.deptid=p.deptid)end,pcity=case p.station when p.station then(select c.cityname from t_city c where c.cityid=p.station)end from t_pulse p,t_pulsehandler h,t_employee e,t_pulsecategory c where h.psendto='P2284'and e.pcode=p.pcode and (p.currentstatus=1 or p.currentstatus=3) and h.pulseid=p.pid and p.pulsecategory=c.pulseid order by p.pulsecreateon",con);
		 DataSet ds=new DataSet();
		 dr.Fill(ds,"Records");
		 this.dgLivePulse.DataSource=ds;
		 this.dgLivePulse.DataBind();
         con.Close();
		 con.Dispose();
		}
		public void GetActionCode()
		{
		 con=new SqlConnection(Connection.ConnectionString);
		 con.Open();
		 SqlCommand cmd=new SqlCommand("select actionid,actioncode from t_pulseactioncode where isactive=1 order by actioncode",con);
		 SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
			 ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
			 this.ddActionCode.Items.Add(itm);
			}
		 rd.Close();
		 con.Close();
		}
		public void GetPulseBlog(string ID)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlDataAdapter dr=new SqlDataAdapter("select '<img src=../empthumbnail/' + rtrim(b.psendby) + '.jpg width=55>' as pic,b.progressblog,[Send By]=case b.psendby when 'Raabta' then 'Raabta' else (select e.name from t_employee e where e.pcode=b.psendby)end,b.pdate,[Action Code]=case COALESCE (b.actioncode,0) when 0 then '' else (select a.actioncode from t_pulseactioncode a where a.actionid=b.actioncode) end from t_pulseprogressblog b where b.pulseid='"+ID+"' and isactive=1 order by pdate desc",con);
			DataSet ds=new DataSet();
			dr.Fill(ds,"Records");
			this.dgPulseBlog.DataSource=ds;
			this.dgPulseBlog.DataBind();
			this.dgPulseBlog.Visible=true;
			con.Close();
			con.Dispose();
		}

		private void dgLivePulse_SelectedIndexChanged(object sender, System.EventArgs e)
		{
		 Reset();
		 this.lblPulseID.Text=this.dgLivePulse.Items[this.dgLivePulse.SelectedIndex].Cells[1].Text;
		 this.lblhPulseID.Text=lblPulseID.Text;
		 this.lblName.Text=this.dgLivePulse.Items[this.dgLivePulse.SelectedIndex].Cells[3].Text;
		 this.lblTitle.Text=this.dgLivePulse.Items[this.dgLivePulse.SelectedIndex].Cells[4].Text;
		 this.lblType.Text=this.dgLivePulse.Items[this.dgLivePulse.SelectedIndex].Cells[5].Text;
		 this.lblDescription.Text=this.dgLivePulse.Items[this.dgLivePulse.SelectedIndex].Cells[6].Text;
		 this.lblCreateon.Text=this.dgLivePulse.Items[this.dgLivePulse.SelectedIndex].Cells[7].Text;
		 this.lblPcode.Text=this.dgLivePulse.Items[this.dgLivePulse.SelectedIndex].Cells[11].Text;
		 this.lblDesignation.Text=this.dgLivePulse.Items[this.dgLivePulse.SelectedIndex].Cells[12].Text;
		 this.lblDepartment.Text=this.dgLivePulse.Items[this.dgLivePulse.SelectedIndex].Cells[13].Text;
		 this.lblpDepartment.Text=this.dgLivePulse.Items[this.dgLivePulse.SelectedIndex].Cells[14].Text;
		 this.lblpStation.Text=this.dgLivePulse.Items[this.dgLivePulse.SelectedIndex].Cells[15].Text;
		 this.pnlSubMain1.Visible=true;
		 GetPulseBlog(this.lblPulseID.Text);
		 this.pnlSubMain2.Visible=true;
		 this.pnlSubMain3.Visible=true;
		}
		public void Reset()
		{
			this.lblCreateon.Text="";
			this.lblDepartment.Text="";
			this.lblDescription.Text="";
			this.lblDesignation.Text="";
			this.lblName.Text="";
			this.lblPcode.Text="";
			this.lblpDepartment.Text="";
			this.lblpStation.Text="";
			this.lblPulseID.Text="";
			this.lblhPulseID.Text="";
			this.lblTitle.Text="";
			this.lblType.Text="";
			r1.Visible=false;
			r2.Visible=false;
		}

		private void ddActionCode_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			if(this.ddActionCode.SelectedItem.Value=="7")
			{
				this.chkReportingList.Items.Clear();
				MyTeam team=new MyTeam();
				ArrayList _list=team.getDirectTeam(Session["user_id"].ToString());
				ArrayList tempList=new ArrayList();
				for(int j=0;j<_list.Count;j++)
				{
					string member=(string)_list[j];
					string []memberStr=member.Split('^');
					tempList.Add(memberStr[2]);
				}
				tempList.Sort();
				for(int k=0;k<tempList.Count;k++)
				{
					string members=(string)tempList[k];
					for(int z=0;z<_list.Count;z++)
					{
						string member=(string)_list[z];
						string []memberStr=member.Split('^');
						if(memberStr[2].Equals(members))
						{
							ListItem itm=new ListItem(members,memberStr[0]);
							this.chkReportingList.Items.Add(itm);
						}
					}			  
				}
				this.Label1.Text="My Team";
				r2.Visible=true;
			}
			else if(this.ddActionCode.SelectedItem.Value=="8")
			{
				this.chkReportingList.Items.Clear();
				MyTeam team=new MyTeam();
				ArrayList _list=team.getBoss(Session["user_id"].ToString(),1);
				ArrayList tempList=new ArrayList();
				for(int j=0;j<_list.Count;j++)
				{
					string member=(string)_list[j];
					string []memberStr=member.Split('^');
					tempList.Add(memberStr[2]);
				}
				tempList.Sort();
				for(int k=0;k<tempList.Count;k++)
				{
					string members=(string)tempList[k];
					for(int z=0;z<_list.Count;z++)
					{
						string member=(string)_list[z];
						string []memberStr=member.Split('^');
						if(memberStr[2].Equals(members))
						{
							ListItem itm=new ListItem(members,memberStr[0]);
							this.chkReportingList.Items.Add(itm);
						}
					}			  
				}
				r2.Visible=true;
				this.Label1.Text="My Line Manager(s)";
			}
		}
	}
}
