<%@ Register TagPrefix="uc1" TagName="myMenus" Src="myMenus.ascx" %>
<%@ Page CodeBehind="ManageEmployeeRequest.aspx.cs" Language="c#" AutoEventWireup="false" Inherits="GeoRabtaSite.ManageEmployeeRequest" smartNavigation="False"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Rabta :: Manage Queue List Specialist </title>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="RaabtaAdmin.css" type="text/css" rel="stylesheet">
		<script language="javascript" id="clientEventHandlersJS">
<!--

//-->
		</script>
	</HEAD>
	<body language="javascript" dir="ltr" bottomMargin="0" bgProperties="fixed" leftMargin="0"
		topMargin="0" onLoad="return window_onload()" rightMargin="0" onscroll="return window_onscroll()">
		<form id="myForm" name="MyForm" runat="server">
			<table height="100%" cellSpacing="0" cellPadding="0" width="750" align="center" bgColor="#ffffff"
				border="0">
				<TR>
					<TD vAlign="top" align="left" height="69">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="69" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="1826">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top1.swf">
							<PARAM NAME="Src" VALUE="flash/Top1.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top1.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="69"> </embed>
						</OBJECT>
					</TD>
				</TR>
				<tr>
					<td height="10">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="10" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="265">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top2.swf">
							<PARAM NAME="Src" VALUE="flash/Top2.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top2.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="10"> </embed>
						</OBJECT>
					</td>
				</tr>
				<TR>
					<TD height="20" class="PageTitle">Geo Raabta Admin :: Station Setup</TD>
				</TR>
				<tr>
					<td height="20" background="../images/menu-off-bg.gif"><uc1:mymenus id="MyMenus1" runat="server"></uc1:mymenus></td>
				</tr>
				<tr>
					<td vAlign="top" align="left" class="MainBG"><STRONG><FONT size="4"><BR>
								<TABLE class="MainFormColor" id="tdFrom2" cellSpacing="0" cellPadding="3" width="90%" align="center"
									border="0" runat="server">
									<TR>
										<TD class="OrangeFormTitle" colSpan="3">Manage Employee Request&nbsp;</TD>
									</TR>
									<TR>
										<TD><STRONG>Field Name:<BR>
											</STRONG>
											<asp:dropdownlist id="ddFeilds" runat="server" CssClass="textbox" Width="100%" AutoPostBack="True">
												<asp:ListItem Value="0">Select--Field</asp:ListItem>
												<asp:ListItem Value="2">Designation</asp:ListItem>
												<asp:ListItem Value="3">Name</asp:ListItem>
												<asp:ListItem Value="4">Station</asp:ListItem>
												<asp:ListItem Value="5">Date of Join</asp:ListItem>
												<asp:ListItem Value="Department">Department</asp:ListItem>
												<asp:ListItem Value="9">Date of Birth</asp:ListItem>
												<asp:ListItem Value="10">Gender</asp:ListItem>
												<asp:ListItem Value="11">Blood Group</asp:ListItem>
												<asp:ListItem Value="12">Passport No</asp:ListItem>
												<asp:ListItem Value="13">NIC (new)</asp:ListItem>
												<asp:ListItem Value="14">NIC (old)</asp:ListItem>
												<asp:ListItem Value="15">Address</asp:ListItem>
												<asp:ListItem Value="16">Extension</asp:ListItem>
												<asp:ListItem Value="17">Employment Type</asp:ListItem>
												<asp:ListItem Value="18">Father Name</asp:ListItem>
												<asp:ListItem Value="19">Marital Status</asp:ListItem>
												<asp:ListItem Value="20">Religion</asp:ListItem>
												<asp:ListItem Value="21">Telephone (Home)</asp:ListItem>
												<asp:ListItem Value="22">Mobile</asp:ListItem>
												<asp:ListItem Value="23">Email (Personal)</asp:ListItem>
												<asp:ListItem Value="24">Next of Kin</asp:ListItem>
												<asp:ListItem Value="26">Email (Official)</asp:ListItem>
												<asp:ListItem Value="27">Bank Account#</asp:ListItem>
												<asp:ListItem Value="28">Bank Account Detail</asp:ListItem>
												<asp:ListItem Value="29">NTN#</asp:ListItem>
												<asp:ListItem Value="30">EOBI#</asp:ListItem>
												<asp:ListItem Value="31">SESSI#</asp:ListItem>
												<asp:ListItem Value="34">Nationality</asp:ListItem>
												<asp:ListItem Value="35">Nationality (Secondary)</asp:ListItem>
												<asp:ListItem Value="41">Confirmation Date</asp:ListItem>
												<asp:ListItem Value="Nick">Nick</asp:ListItem>
												<asp:ListItem Value="Workstation Address">Workstation Address</asp:ListItem>
												<asp:ListItem Value="Education">Education</asp:ListItem>
												<asp:ListItem Value="Training">Training</asp:ListItem>
												<asp:ListItem Value="Spouse Addition">Spouse Addition</asp:ListItem>
												<asp:ListItem Value="Child Addition">Child Addition</asp:ListItem>
											</asp:dropdownlist></TD>
										<TD></TD>
										<TD width="320"><STRONG>Editable by Employee:<BR>
											</STRONG>
											<asp:DropDownList id="ddIsEditable" runat="server" CssClass="textbox" Width="100%">
												<asp:ListItem Value="1">Yes</asp:ListItem>
												<asp:ListItem Value="2">No</asp:ListItem>
											</asp:DropDownList></TD>
									</TR>
									<TR>
										<TD width="320"><STRONG>Specialist:<BR>
											</STRONG>
											<DIV style="OVERFLOW: auto; WIDTH: 312px; HEIGHT: 80px" ms_positioning="FlowLayout">
												<asp:CheckBoxList id="chkSpecialist" runat="server"></asp:CheckBoxList></DIV>
										</TD>
										<TD></TD>
										<TD width="320">
											<P><STRONG>Verification Required by HR:</STRONG><BR>
												<asp:DropDownList id="ddIsVerificationRequired" runat="server" CssClass="textbox" Width="100%">
													<asp:ListItem Value="1">Yes</asp:ListItem>
													<asp:ListItem Value="2">No</asp:ListItem>
												</asp:DropDownList></P>
										</TD>
									</TR>
									<TR>
										<TD style="HEIGHT: 61px" width="320"><STRONG>Verification Criteria:<BR>
											</STRONG>
											<asp:TextBox id="txtVerifyCriteria" runat="server" CssClass="textbox" Width="100%" Height="50px"
												TextMode="MultiLine"></asp:TextBox></TD>
										<TD style="HEIGHT: 61px"></TD>
										<TD style="HEIGHT: 61px" width="320"><STRONG>Documents Required for Verification:</STRONG><BR>
											<DIV style="OVERFLOW: auto; WIDTH: 312px; HEIGHT: 80px" ms_positioning="FlowLayout">
												<asp:CheckBoxList id="chkvDcouments" runat="server"></asp:CheckBoxList></DIV>
										</TD>
									</TR>
									<TR>
										<TD width="320"><STRONG>Documents Required from TeamGeo Member:<BR>
											</STRONG>
											<DIV style="OVERFLOW: auto; WIDTH: 312px; HEIGHT: 80px" ms_positioning="FlowLayout">
												<asp:CheckBoxList id="chkeDocuments" runat="server"></asp:CheckBoxList></DIV>
										</TD>
										<TD></TD>
										<TD width="320"><STRONG>Timeline to accept or reject:<BR>
											</STRONG>
											<asp:DropDownList id="ddTimeline" runat="server" CssClass="textbox" Width="100%">
												<asp:ListItem Value="-1">Select--Time</asp:ListItem>
												<asp:ListItem Value="0">Immediate</asp:ListItem>
												<asp:ListItem Value="1">1 working day</asp:ListItem>
												<asp:ListItem Value="2">2 working days</asp:ListItem>
												<asp:ListItem Value="3">3 working days</asp:ListItem>
												<asp:ListItem Value="4">4 working days</asp:ListItem>
												<asp:ListItem Value="5">5 working days</asp:ListItem>
												<asp:ListItem Value="6">6 working days</asp:ListItem>
												<asp:ListItem Value="7">7 working days</asp:ListItem>
												<asp:ListItem Value="8">8 working days</asp:ListItem>
												<asp:ListItem Value="9">9 working days</asp:ListItem>
												<asp:ListItem Value="10">10 working days</asp:ListItem>
											</asp:DropDownList></TD>
									</TR>
									<TR>
										<TD width="320"><STRONG>Reply at the time of Requisition:</STRONG><BR>
											<asp:TextBox id="txtReplyatRequest" runat="server" CssClass="textbox" Width="100%" Height="50px"
												TextMode="MultiLine" MaxLength="1000"></asp:TextBox></TD>
										<TD></TD>
										<TD width="320"><STRONG>Message to Employee on Acceptance:<BR>
											</STRONG>
											<asp:TextBox id="txtReplyAccept" runat="server" CssClass="textbox" Width="100%" Height="50px"
												TextMode="MultiLine" MaxLength="1000"></asp:TextBox></TD>
									</TR>
									<TR>
										<TD width="320"><STRONG>Message to Employee on Rejection:<BR>
											</STRONG>
											<asp:TextBox id="txteplyReject" runat="server" CssClass="textbox" Width="100%" Height="50px"
												TextMode="MultiLine" MaxLength="1000"></asp:TextBox><BR>
										</TD>
										<TD></TD>
										<TD width="320"><STRONG>ToolTip Message:<BR>
												<asp:TextBox id="txtTooltipMsg" runat="server" Width="100%" CssClass="textbox" TextMode="MultiLine"
													Height="50px" MaxLength="1000"></asp:TextBox><BR>
											</STRONG>
										</TD>
									</TR>
									<TR>
										<TD width="320" colSpan="3">
											<asp:Label id="lblmessage" runat="server" Visible="False" Font-Bold="True" ForeColor="Blue"></asp:Label></TD>
									</TR>
									<TR>
										<TD colSpan="3">
											<asp:Button id="btnReset" runat="server" Text="Reset"></asp:Button>
											<asp:Button id="btnSave" runat="server" Text="Save"></asp:Button></TD>
									</TR>
								</TABLE>
							</FONT></STRONG>
						<BR>
						<TABLE id="Table2" cellSpacing="0" cellPadding="3" width="90%" align="center" border="0"
							runat="server">
							<TR>
								<TD width="320"></TD>
								<TD width="120"></TD>
								<TD width="320"></TD>
							</TR>
							<TR>
								<TD colSpan="3"></TD>
							</TR>
						</TABLE>
					</td>
				</tr>
				<tr>
					<td vAlign="middle" align="center" height="15">Copyright © 2005 Independent Media 
						Corporation <A href="http://www.geo.tv">www.geo.tv</A></td>
				</tr>
			</table>
		</form>
	</body>
</HTML>
