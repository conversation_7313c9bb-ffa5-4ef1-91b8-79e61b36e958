using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;
namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for GeoIdeas.
	/// </summary>
	public class GeoCategory : System.Web.UI.Page
	{
		SqlConnection con;
		private static DataSet ds;
		protected System.Web.UI.WebControls.Label Label2;
		protected System.Web.UI.WebControls.TextBox txtCatName;
		protected System.Web.UI.WebControls.Label Label4;
		protected System.Web.UI.WebControls.TextBox txtCatCriteria;
		protected System.Web.UI.WebControls.Label Label3;
		protected System.Web.UI.WebControls.TextBox txtCatLevel;
		protected System.Web.UI.WebControls.Label Label5;
		protected System.Web.UI.WebControls.TextBox txtCatColor;
		protected System.Web.UI.WebControls.Label Label8;
		protected System.Web.UI.WebControls.DropDownList ddParentCat;
		protected System.Web.UI.WebControls.Label Label6;
		protected System.Web.UI.WebControls.TextBox txtPositive;
		protected System.Web.UI.WebControls.Label Label9;
		protected System.Web.UI.WebControls.DropDownList ddSRole;
		protected System.Web.UI.WebControls.DataGrid DataGrid1;
		protected eWorld.UI.NumericBox txtLevel;
		protected System.Web.UI.WebControls.Button btnCancel;
		protected System.Web.UI.WebControls.Button cmdDelete;
		protected ZmodemControls.ColourPicker ColourPicker1;
		protected System.Web.UI.WebControls.Label Label10;
		protected System.Web.UI.WebControls.RadioButtonList rblCategory;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator1;
		protected System.Web.UI.WebControls.Panel Panel1;
		protected System.Web.UI.WebControls.Label Label1;
		protected System.Web.UI.WebControls.TextBox txtCatId;
		protected System.Web.UI.WebControls.TextBox txtColor;
		protected System.Web.UI.WebControls.Button btnQueryButton;
		protected System.Web.UI.WebControls.Label Label7;
		static int WebFormID=3;
		static int ProjectID=2;
		static string userId="";
		protected System.Web.UI.WebControls.Label Label11;
		protected System.Web.UI.HtmlControls.HtmlTable pnlCategoryForm;
		protected eWorld.UI.CollapsablePanel CollapsablePanel1;
		protected System.Web.UI.WebControls.LinkButton Button1;
		protected System.Web.UI.WebControls.Label Label12;
		public static string sort="Id";
		private bool IsPageAccessAllowed()
		{

			
			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception ex)
			{
				Response.Redirect("../Login.aspx");
			}
		
			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"View")==true)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("../Login.aspx");
				return false;
			}
		}

		private void Page_Load(object sender, System.EventArgs e)
		{
			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			con = new SqlConnection(Connection.ConnectionString);
			Label12.Visible=false;
			con.Open();

			if(IsPageAccessAllowed())
			{
				Label7.Visible=false;
				
				
				Page.SmartNavigation=true;
				if(!IsPostBack)
				{
					//ColourPicker1.Visible=false;
					FillParentCategoryDDL();
					BindGrid();
					cmdDelete.Attributes.Add("onclick","if(confirm('Are you sure to delete this Record?')){return true}else{return false}");
					ResetForm();
					pnlCategoryForm.Visible=false;
				}
				
				Button1.Visible=GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Add");
				DataGrid1.Columns[0].Visible=GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Edit");
				cmdDelete.Visible=GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Delete");
				cmdDelete.Enabled=GeoSecurity.isControlEnable(ProjectID,WebFormID,userId,"Delete");
			}
			else
			{
				Response.Redirect("ErrorPage.aspx");
			}

		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.ddParentCat.SelectedIndexChanged += new System.EventHandler(this.ddParentCat_SelectedIndexChanged);
			this.rblCategory.SelectedIndexChanged += new System.EventHandler(this.rblCategory_SelectedIndexChanged);
			this.btnQueryButton.Click += new System.EventHandler(this.btnQueryButton_Click);
			this.cmdDelete.Click += new System.EventHandler(this.cmdDelete_Click);
			this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
			this.Button1.Click += new System.EventHandler(this.Button1_Click);
			this.DataGrid1.EditCommand += new System.Web.UI.WebControls.DataGridCommandEventHandler(this.DataGrid1_EditCommand);
			this.DataGrid1.SortCommand += new System.Web.UI.WebControls.DataGridSortCommandEventHandler(this.DataGrid1_SortCommand);
			this.DataGrid1.DeleteCommand += new System.Web.UI.WebControls.DataGridCommandEventHandler(this.DataGrid1_DeleteCommand);
			this.DataGrid1.SelectedIndexChanged += new System.EventHandler(this.DataGrid1_SelectedIndexChanged);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		private string GetNewLevel()
		{
			SqlCommand cmd = new SqlCommand("SELECT MAX(CLevel) + 1 AS NewCLevel FROM dbo.t_Categorization",con);
			SqlDataReader dr=cmd.ExecuteReader();
			dr.Read();
			string temp="";
			if (dr.HasRows)
			{
				temp=dr["NewCLevel"].ToString();
			}
			else
			{
				temp="1";
			}
			dr.Close();
			return temp;
		}
		private void btnQueryButton_Click(object sender, System.EventArgs e)
		{
			
			if (btnQueryButton.Text=="Save")
			{
				
				if(!this.txtCatId.Visible && !this.Label1.Visible)
				{
					
					if (txtLevel.Text!="0")
					{
						SqlCommand cmd = new SqlCommand("select cLevel from t_Categorization where cLevel="+txtLevel.Text,con);
						SqlDataReader dr=cmd.ExecuteReader();
						dr.Read();
						if (dr.HasRows)
						{
							dr.Close();
							Label10.Visible=true;
							return;
						}
						else
						{
							if(ddSRole.Enabled && ddSRole.SelectedIndex==0)
							{
								Label7.Visible=true;
								dr.Close();
								return;
							}
							dr.Close();
							txtColor.Text=ColourPicker1.SelectedColour.ToString();
							string str="insert into t_categorization values('"+this.txtCatName.Text+"','"+this.txtCatLevel.Text+"','"+this.txtCatCriteria.Text+"','"+this.txtCatColor.Text+"','"+this.txtPositive.Text+"','"+Convert.ToInt32(this.ddParentCat.SelectedValue.ToString())+"','"+Convert.ToInt32(this.ddSRole.SelectedValue.ToString())+"',"+ rblCategory.SelectedValue.ToString()  +"," + txtLevel.Text + ",'" + txtColor.Text + "')"; 
							SqlCommand c=new SqlCommand(str,con);
							int i=c.ExecuteNonQuery();
							BindGrid();
							Label10.Visible=false;
							ResetForm();
							FillParentCategoryDDL();
							this.btnQueryButton.Text="New Category";
							pnlCategoryForm.Visible=false;
							Button1.Visible=true;

						}

					}
					else
					{
						if (ddSRole.Enabled && ddSRole.SelectedIndex==0)
						{
							Label7.Visible=true;
							return;
						}
						
						txtColor.Text=ColourPicker1.SelectedColour.ToString();
						string str="insert into t_categorization values('"+this.txtCatName.Text+"','"+this.txtCatLevel.Text+"','"+this.txtCatCriteria.Text+"','"+this.txtCatColor.Text+"','"+this.txtPositive.Text+"','"+Convert.ToInt32(this.ddParentCat.SelectedValue.ToString())+"','"+Convert.ToInt32(this.ddSRole.SelectedValue.ToString())+"',"+ rblCategory.SelectedValue.ToString()  +"," + txtLevel.Text + ",'" + txtColor.Text + "')"; 
						SqlCommand c=new SqlCommand(str,con);
						int i=c.ExecuteNonQuery();
						BindGrid();
						Label10.Visible=false;
						ResetForm();
						FillParentCategoryDDL();
						this.btnQueryButton.Text="New Category";
						pnlCategoryForm.Visible=false;
						Button1.Visible=true;


					}
				}
					//if(this.txtCatId.Visible)
				else if (btnQueryButton.Text=="Update")
				{
					if (txtLevel.Text!="0")
					{
						SqlCommand cmd = new SqlCommand("select cLevel from t_Categorization where cLevel="+txtLevel.Text + " and cat_id<>"+txtCatId.Text ,con);
						SqlDataReader dr=cmd.ExecuteReader();
						dr.Read();
						if (dr.HasRows)
						{
							dr.Close();
							Label10.Visible=true;
							return;
						}
						else
						{
							dr.Close();

							if (ddSRole.Enabled && ddSRole.SelectedIndex==0)
							{
								Label7.Visible=true;
								return;
							}
							txtColor.Text=ColourPicker1.SelectedColour.ToString();
							//string str="update t_categorization set cat_name='"+this.txtCatName.Text+"',cat_level='"+this.txtCatLevel.Text+"',cat_criteria='"+this.txtCatCriteria.Text+"',cat_color='"+this.txtCatColor.Text+"',cat_positive='"+this.txtPositive.Text+"',cat_parent='"+Convert.ToInt32(this.ddParentCat.SelectedValue.ToString())+"',supervisory_role='"+Convert.ToInt32(this.ddSRole.SelectedValue.ToString())+"',isactive="+  rblCategory.SelectedValue.ToString() +", CLevel=" + txtLevel.Text + ", Color='" + txtColor.Text + "'   where cat_id='"+Int32.Parse(this.txtCatId.Text)+"'"; 


							string p="";
						
							if (ddParentCat.SelectedIndex==0) 
								p=" NULL ";
							else
								p=ddParentCat.SelectedValue.ToString();

							string str="UPDATE dbo.t_Categorization " +
								"SET cat_name = '" + txtCatName.Text + "', cat_level = '" + txtCatLevel.Text + "', cat_criteria = '" + txtCatCriteria.Text.Trim() + "', cat_color = '" + txtCatColor.Text + "', cat_positive = '" + txtPositive.Text + "', cat_parent = " + p + ", supervisory_role = " + ddSRole.SelectedValue + ", " +
								" isactive = " + rblCategory.SelectedValue.ToString() + ", CLevel = " + txtLevel.Text.Trim() + ", Color = '" + ColourPicker1.SelectedColour.ToString() + "' " +
								"WHERE (cat_id = " + txtCatId.Text + ")";

							SqlCommand c=new SqlCommand(str,con);
							int i=c.ExecuteNonQuery();
							BindGrid();
							Label10.Visible=false;
							ResetForm();
							FillParentCategoryDDL();
							pnlCategoryForm.Visible=false;
							Button1.Visible=true;

						}
						

					}
					else
					{
						
						//txtColor.Text=ColourPicker1.SelectedColour.ToString();
						//string str="update t_categorization set " + " cat_name='"+this.txtCatName.Text+"',cat_level='"+this.txtCatLevel.Text+"',cat_criteria='"+this.txtCatCriteria.Text+"',cat_color='"+this.txtCatColor.Text+"',cat_positive='"+this.txtPositive.Text+"',cat_parent='"+Convert.ToInt32(this.ddParentCat.SelectedValue.ToString())+"',supervisory_role='"+Convert.ToInt32(this.ddSRole.SelectedValue.ToString())+"',isactive="+  rblCategory.SelectedValue.ToString() +", CLevel=" + txtLevel.Text + ", Color='" + txtColor.Text + "'   where cat_id='"+Int32.Parse(this.txtCatId.Text)+"'"; 
						if (ddSRole.Enabled && ddSRole.SelectedIndex==0)
						{
							Label7.Visible=true;
							return;
						}
						string p="";
						
						if (ddParentCat.SelectedIndex==0) 
							p=" NULL ";
						else
							p=ddParentCat.SelectedValue.ToString();

						string str="UPDATE dbo.t_Categorization " +
							"SET cat_name = '" + txtCatName.Text + "', cat_level = '" + txtCatLevel.Text + "', cat_criteria = '" + txtCatCriteria.Text.Trim() + "', cat_color = '" + txtCatColor.Text + "', cat_positive = '" + txtPositive.Text + "', cat_parent = " + p + ", supervisory_role = " + ddSRole.SelectedValue + ", " +
							" isactive = " + rblCategory.SelectedValue.ToString() + ", CLevel = " + txtLevel.Text.Trim() + ", Color = '" + ColourPicker1.SelectedColour.ToString() + "' " +
							"WHERE (cat_id = " + txtCatId.Text + ")";
						//Response.Write(str);
						//Response.End();
						SqlCommand c=new SqlCommand(str,con);
						int i=c.ExecuteNonQuery();
						BindGrid();
						Label10.Visible=false;
						ResetForm();
						FillParentCategoryDDL();
						pnlCategoryForm.Visible=false;
						Button1.Visible=true;

					}

					this.btnQueryButton.Text="New Category";
					this.btnCancel.Enabled=false;
					this.txtCatId.Visible=false;
					this.Label1.Visible=false;
					
					this.txtCatId.Text="";
					this.txtCatColor.Text="";
					this.txtCatCriteria.Text="";
					this.txtCatLevel.Text="";
					this.txtCatName.Text="";
					this.txtPositive.Text="";
					this.ddParentCat.SelectedIndex=0;
					this.ddSRole.SelectedIndex=0;
					txtColor.Text="";
					txtLevel.Text="";
				}
			}
			//
		}

		private void ResetForm()
		{
			this.btnQueryButton.Text="New Category";
			this.btnCancel.Enabled=false;
			this.txtCatId.Visible=false;
			this.Label1.Visible=false;
			this.txtCatId.Text="";
			this.txtCatColor.Text="";
			this.txtCatCriteria.Text="";
			this.txtCatLevel.Text="";
			this.txtCatName.Text="";
			this.txtPositive.Text="";
			this.ddParentCat.SelectedIndex=0;
			this.ddSRole.SelectedValue="0";
			txtLevel.Text="";
			txtColor.Text="";
			cmdDelete.Enabled=false;

		}
		private void btnCancel_Click(object sender, System.EventArgs e)
		{
			ResetForm();
			pnlCategoryForm.Visible=false;
			Button1.Visible=true;
		}

		public void FillParentCategoryDDL()
		{

			string getCat="SELECT TOP 100 PERCENT cat_id, cat_name " +
				" FROM dbo.t_Categorization " + 
				" WHERE (isactive = 2) " +
				" ORDER BY cat_name " ;

			//string getCat="select cat_id,cat_name from t_categorization where isactive='"+1+"'";
			//string getCat="select cat_id,cat_name from t_categorization where isactive=2";
			SqlCommand cmd=new SqlCommand(getCat,con);
			SqlDataReader rd=cmd.ExecuteReader();
			ddParentCat.Items.Clear();
			ddParentCat.Items.Add(new ListItem("Select Parent Category","0"));
			while(rd.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rd[0].ToString();
				itm.Text=rd[1].ToString();
				this.ddParentCat.Items.Add(itm);
			}
			rd.Close();
		}

		public void BindGrid()
		{
			//string getData="select c.cat_id Id,c.cat_name Name,c.cat_level Level,SupervisoryRole=case c.supervisory_role when '1' then 'Yes' when 2 then 'No' End from t_categorization c,t_categorization c2 where c.isactive<>0 And c.cat_parent=c2.cat_id order by "+sort;
			string getData="SELECT case when clevel=0 then 'Super Category' else cast(clevel as varchar) end as clevel1, cat_id as id, cat_name as Name, cat_criteria as Level,  SupervisoryRole=case supervisory_role when '1' then 'Yes' when 2 then 'No' End  " + 
				" FROM dbo.t_Categorization WHERE (isactive <> 0) order by clevel";
			Response.Write(getData);
			Response.End();

				SqlDataAdapter rd=new SqlDataAdapter(getData,con);
			ds=new DataSet();
			rd.Fill(ds,"Category");
			this.DataGrid1.DataSource=ds;
			this.DataBind(); 
		}

		private void DataGrid1_EditCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{
			ddSRole.Enabled=true;
			
			this.btnQueryButton.Text="Save";
			this.btnCancel.Enabled=true;
			this.txtCatId.Visible=true;
			this.txtCatId.Enabled=false;
			this.Label1.Visible=true;
			Session["IsTrue"]="true";
 
			string str="select cat_id,cat_name,cat_level,cat_criteria,cat_color,cat_positive,cat_parent,supervisory_role,CLevel, Color,isactive from t_categorization where cat_id=@Id";
			SqlCommand cmd=new SqlCommand(str,con);
			cmd.Parameters.Add(new SqlParameter("@Id",SqlDbType.VarChar,20));
			cmd.Parameters["@Id"].Value=this.DataGrid1.DataKeys[e.Item.ItemIndex];
			SqlDataReader rd=cmd.ExecuteReader();
			
			while(rd.Read())
			{
				this.txtCatId.Text=rd[0].ToString();
				this.txtCatName.Text=rd[1].ToString();
				this.txtCatLevel.Text=rd[2].ToString();
				this.txtCatCriteria.Text=rd[3].ToString();
				this.txtCatColor.Text=rd[4].ToString();
				this.txtPositive.Text=rd[5].ToString();

				if (rd["isactive"].ToString()=="1")
				{
					rblCategory.SelectedIndex=0;
					txtLevel.Enabled=true;
					ddParentCat.Enabled=true;
				}
				else if (rd["isactive"].ToString()=="2")
				{
					rblCategory.SelectedIndex=1;
					txtLevel.Text="0";
					txtLevel.Enabled=false;
					ddParentCat.SelectedIndex=0;
					ddParentCat.Enabled=false;

				}
				else
				{
					rblCategory.SelectedIndex=0;
					txtLevel.Enabled=true;
					ddParentCat.Enabled=true;
				}
				int i=0;
				ddParentCat.SelectedIndex=0;
				for(i=1;i<ddParentCat.Items.Count;i++)
				{
					if(ddParentCat.Items[i].Value==rd[6].ToString())
					{
						ddParentCat.SelectedIndex=i;
						break;
					}
				}
				
				//this.ddParentCat.SelectedValue=;
				this.ddSRole.SelectedValue=rd[7].ToString();
				txtLevel.Text=rd[8].ToString();
				txtColor.Text=rd[9].ToString();
				ColourPicker1.SelectedColour=txtColor.Text;
				cmdDelete.Enabled=true;
				btnCancel.Enabled=true;
				pnlCategoryForm.Visible=true;
				btnQueryButton.Text="Update";
				Button1.Visible=false;
				if (ddSRole.SelectedIndex==0)
				{
					ddSRole.Enabled=false;
				}
			}
			rd.Close();
			
		}

		private void DataGrid1_DeleteCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{

		}

		private void DataGrid1_SortCommand(object source, System.Web.UI.WebControls.DataGridSortCommandEventArgs e)
		{
			sort=e.SortExpression.ToString();
			BindGrid(); 
		}

		private void ddParentCat_SelectedIndexChanged(object sender, System.EventArgs e)
		{
		
		}

		private void cmdDelete_Click(object sender, System.EventArgs e)
		{
				
			SqlDataAdapter da = new SqlDataAdapter("SELECT     COUNT(dbo.t_Designation.desigid) AS NoOfDesg " +
				" FROM         dbo.t_Categorization INNER JOIN " +
				" dbo.t_Designation ON dbo.t_Categorization.cat_id = dbo.t_Designation.category " +
				" WHERE     (dbo.t_Categorization.cat_id = " + txtCatId.Text + ") AND (dbo.t_Categorization.isactive = 1) AND (dbo.t_Designation.status = 1) ",con);
			
			DataSet ds = new DataSet();
			da.Fill(ds,"NoOfDesg");
			if (ds.Tables[0].Rows[0][0].ToString()=="0")
			{
				string str="update t_categorization set isactive='"+0+"' where cat_id=@Id";
				SqlCommand cm=new SqlCommand(str,con);
				cm.Parameters.Add(new SqlParameter("@Id",SqlDbType.VarChar,20));
				cm.Parameters["@Id"].Value= txtCatId.Text; //this.DataGrid1.DataKeys[e.Item.ItemIndex]; 
				cm.ExecuteNonQuery();
				this.DataGrid1.EditItemIndex=-1;
				BindGrid();
			
				ResetForm();
				pnlCategoryForm.Visible=false;
				Button1.Visible=true;
			}
			else
			{
				Label12.Visible=true;
			}
		}

		private void LinkButton1_Click(object sender, System.EventArgs e)
		{
			if(ColourPicker1.Visible)
				ColourPicker1.Visible=false;
			else
				ColourPicker1.Visible=true;
		}

		private void rblCategory_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			if (rblCategory.SelectedValue=="2")
			{
				txtLevel.Text="0";
				txtLevel.Enabled=false;
				ddParentCat.SelectedIndex=0;
				ddParentCat.Enabled=false;
				ddSRole.Enabled=false;
			}
			else
			{
				txtLevel.Text=GetNewLevel();
				txtLevel.Enabled=true;
				ddParentCat.Enabled=true;
				ddSRole.Enabled=true;
			}
		}

		private void Button1_Click(object sender, System.EventArgs e)
		{
			Session["IsTrue"]="false";
			this.btnQueryButton.Text="Save";
			this.btnCancel.Enabled=true;
			this.txtCatId.Text="";
			this.txtCatColor.Text="";
			this.txtCatCriteria.Text="";
			this.txtCatLevel.Text="";
			this.txtCatName.Text="";
			this.txtPositive.Text="";
			this.ddParentCat.SelectedIndex=0;
			this.ddSRole.SelectedIndex=0;
			ColourPicker1.SelectedColour="#FFFFFF";
			txtLevel.Text="";
			btnCancel.Enabled=true;
			cmdDelete.Enabled=false;
			txtColor.Text="";
			txtLevel.Text=GetNewLevel();
				
			
			pnlCategoryForm.Visible=true;
			Button1.Visible=false;
			btnQueryButton.Text="Save";
			btnCancel.Enabled=true;
		}

		private void DataGrid1_SelectedIndexChanged(object sender, System.EventArgs e)
		{
		
		}
	}
}
