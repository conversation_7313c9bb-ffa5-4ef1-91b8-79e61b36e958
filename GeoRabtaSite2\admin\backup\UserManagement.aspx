<%@ Register TagPrefix="uc1" TagName="myMenus" Src="myMenus.ascx" %>
<%@ Page language="c#" Codebehind="UserManagement.aspx.cs" AutoEventWireup="false" Inherits="GeoRabtaSite.admin.UserManagement" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Raabta Admin :: Account Management</title>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="RaabtaAdmin.css" type="text/css" rel="stylesheet">
		<LINK href="../Styles5.css" type="text/css" rel="stylesheet">
		<script language="javascript">
	function CheckOfficalEmail()
	{
		txtEmailOffical2 = window.document.getElementById("txtEmpInfoEmail2");
		txtLoginID = window.document.getElementById("txtUserID");
		
		if(txtEmailOffical2.value=="" && txtLoginID.value=="" )
		{
			if (confirm("Offical email is not provided.\n\nAre you sure to continue to create Login ID without offical email address?"))
			{
				var a = prompt("Please enter new login id","");
				if (a=="")
				{
					event.returnValue=false;
					return false;
					
				}
				else
				{
					txtLoginID.value = a;
					event.returnValue=true;
					return true;
				}
			}
			else
			{
				event.returnValue=false;
				return false;
			}
		}
		else
		{
			event.returnValue=true;
			return true;
		}
	}
		</script>
	</HEAD>
	<body bottomMargin="0" bgProperties="fixed" leftMargin="0" topMargin="0" rightMargin="0">
		<form id="myForm" name="MyForm" runat="server">
			<table height="100%" cellSpacing="0" cellPadding="0" width="780" align="center" bgColor="#ffffff"
				border="0">
				<tr>
					<td vAlign="middle" align="left" height="69">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="69" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="1826">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top1.swf">
							<PARAM NAME="Src" VALUE="flash/Top1.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<embed src="flash/Top1.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="69"> </embed>
						</OBJECT>
					</td>
				</tr>
				<tr>
					<td height="10">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="10" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="265">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top2.swf">
							<PARAM NAME="Src" VALUE="flash/Top2.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<embed src="flash/Top2.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="10"> </embed>
						</OBJECT>
					</td>
				</tr>
				<TR>
					<TD class="PageTitle" height="20">Geo Raabta Admin :: Account Management</TD>
				</TR>
				<tr>
					<td height="20" background="../images/menu-off-bg.gif"><uc1:mymenus id="MyMenus1" runat="server"></uc1:mymenus></td>
				</tr>
				<TR>
					<TD class="mainbg" vAlign="top"><BR>
						<TABLE class="MainFormColor" id="Table2" cellSpacing="0" cellPadding="3" width="750" align="center"
							border="0">
							<TR>
								<TD class="OrangeFormTitle" colSpan="3">Account mangement:</TD>
							</TR>
							<TR>
								<TD class="menubar" width="320"><STRONG>Employee Code:</STRONG></TD>
								<TD class="menubar" width="90"></TD>
								<TD class="menubar" width="320"></TD>
							</TR>
							<TR>
								<TD width="320">
									<asp:label id="lblEmployeeCode" runat="server"></asp:label></TD>
								<TD width="90"></TD>
								<TD width="320"></TD>
							</TR>
							<TR>
								<TD class="menubar" width="320"><STRONG>Name:</STRONG></TD>
								<TD class="menubar" width="90"></TD>
								<TD class="menubar" width="320"><STRONG>Designation:</STRONG></TD>
							</TR>
							<TR>
								<TD width="320">
									<asp:label id="lblName" runat="server">Label</asp:label></TD>
								<TD width="90"></TD>
								<TD width="320">
									<asp:label id="lblDesignation" runat="server"></asp:label></TD>
							</TR>
							<TR>
								<TD class="menubar" width="320"><STRONG>Department:</STRONG></TD>
								<TD class="menubar" width="90"></TD>
								<TD class="menubar" width="320"><STRONG>Official Email:</STRONG></TD>
							</TR>
							<TR>
								<TD width="320">
									<asp:label id="lblDepartment" runat="server"></asp:label></TD>
								<TD width="90"></TD>
								<TD width="320">
									<asp:label id="lblEmailOfficial" runat="server"></asp:label></TD>
							</TR>
							<TR>
								<TD class="menubar" width="320">
									<asp:label id="lblUserID" runat="server">User ID:</asp:label>
									<asp:requiredfieldvalidator id="RequiredFieldValidator1" runat="server" ErrorMessage="* Required" Display="Dynamic"
										ControlToValidate="txtUserID"></asp:requiredfieldvalidator>
									<asp:LinkButton id="LinkButton1" runat="server" CausesValidation="False">Check Availability</asp:LinkButton>
									<asp:Label id="lblAvailable" runat="server"></asp:Label></TD>
								<TD class="menubar" width="90"></TD>
								<TD class="menubar" width="320">
									<asp:LinkButton id="lnkNewPassword" runat="server" CausesValidation="False">Generate New Password</asp:LinkButton>&nbsp;</TD>
							</TR>
							<TR>
								<TD width="320">
									<asp:textbox id="txtUserID" runat="server" CssClass="TextBox" Width="100%"></asp:textbox></TD>
								<TD width="90"></TD>
								<TD width="320">
									<asp:Label id="Label2" runat="server"></asp:Label></TD>
							</TR>
							<TR>
								<TD class="menubar" width="320">
									<asp:checkbox id="chkActiveAccount" runat="server" Text="Active Account"></asp:checkbox></TD>
								<TD class="menubar" width="90"></TD>
								<TD class="menubar" width="320">
									<asp:textbox id="txtPassword1" runat="server" Width="100%" CssClass="TextBox" Visible="False"
										ReadOnly="True"></asp:textbox></TD>
							</TR>
							<TR>
								<TD width="320">
									<asp:Label id="Label1" runat="server" Font-Bold="True"></asp:Label></TD>
								<TD width="90"></TD>
								<TD width="320"></TD>
							</TR>
							<TR>
								<TD width="320"><asp:button id="cmdCreate" runat="server" Text="Create Account" Width="120px"></asp:button></TD>
								<TD width="90"></TD>
								<TD width="320">
									<asp:TextBox id="txtEmpInfoEmail2" runat="server" style="VISIBILITY:hidden"></asp:TextBox></TD>
							</TR>
							<TR>
								<TD width="320">
									<asp:HyperLink id="HyperLink1" runat="server" NavigateUrl="SelectUser.aspx">Select Another User</asp:HyperLink></TD>
								<TD width="90"></TD>
								<TD width="320"></TD>
							</TR>
						</TABLE>
						<BR>
						<TABLE class="MainFormColor" id="Table1" cellSpacing="0" cellPadding="3" width="750" align="center"
							border="0">
							<TR>
								<TD class="OrangeFormTitle" colSpan="3">User's Roles</TD>
							</TR>
							<TR>
								<TD colSpan="3">
									<asp:datagrid id="DataGrid1" runat="server" Width="100%" BorderColor="Navy" AutoGenerateColumns="False"
										CellPadding="3" BorderWidth="1px" BorderStyle="Solid" AllowSorting="True" GridLines="Horizontal">
										<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
										<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
										<AlternatingItemStyle CssClass="alternetitem"></AlternatingItemStyle>
										<ItemStyle CssClass="item"></ItemStyle>
										<HeaderStyle Font-Bold="True" ForeColor="Black" CssClass="header"></HeaderStyle>
										<Columns>
											<asp:BoundColumn Visible="False" DataField="RoleID" SortExpression="RoleID" HeaderText="RoleID"></asp:BoundColumn>
											<asp:BoundColumn DataField="roleName" SortExpression="roleName" HeaderText="Role Name">
												<ItemStyle HorizontalAlign="Left" VerticalAlign="Bottom"></ItemStyle>
											</asp:BoundColumn>
											<asp:TemplateColumn>
												<ItemStyle HorizontalAlign="Center" Width="40px" VerticalAlign="Middle"></ItemStyle>
												<ItemTemplate>
													<asp:CheckBox id="chkSelectRole" runat="server"></asp:CheckBox>
												</ItemTemplate>
											</asp:TemplateColumn>
										</Columns>
										<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
									</asp:datagrid></TD>
							</TR>
							<TR>
								<TD width="320" colSpan="3">
									<asp:button id="Button1" runat="server" Width="120px" Text="Update"></asp:button></TD>
							</TR>
						</TABLE>
						<BR>
					</TD>
				</TR>
				<tr>
					<td height="20" vAlign="middle" align="center">Copyright © 2005 Independent Media 
						Corporation <A href="http://www.geo.tv">www.geo.tv</A></td>
				</tr>
			</table>
		</form>
	</body>
</HTML>
