<%@ Register TagPrefix="uc1" TagName="myMenus" Src="myMenus.ascx" %>
<%@ Register TagPrefix="ew" Namespace="eWorld.UI" Assembly="eWorld.UI" %>
<%@ Page CodeBehind="Department.aspx.cs" Language="c#" AutoEventWireup="false" Inherits="GeoRabtaSite.GeoDepartment" smartNavigation="False" enableViewStateMac="False"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Raabata Admin :: Departments</title>
		<meta name="vs_showGrid" content="False">
		<meta name="vs_snapToGrid" content="False">
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="RaabtaAdmin.css" type="text/css" rel="stylesheet">
		<LINK href="../Styles4.css" type="text/css" rel="stylesheet">
		<script language="javascript" id="clientEventHandlersJS">
<!--

function window_onload() {
	t=document.getElementById('txtName');
	if(t)
		t.focus();
}

//-->
		</script>
	</HEAD>
	<body language="javascript" dir="ltr" bottomMargin="0" leftMargin="0" topMargin="0" onLoad="return window_onload()"
		rightMargin="0">
		<form id="myForm" name="MyForm" runat="server">
			<table height="100%" cellSpacing="0" cellPadding="0" width="780" align="center" bgColor="#ffffff"
				border="0">
				<tr>
					<td vAlign="middle" align="left" height="69">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="69" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="1826">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top1.swf">
							<PARAM NAME="Src" VALUE="flash/Top1.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<embed src="flash/Top1.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="69"> </embed>
						</OBJECT>
					</td>
				</tr>
				<tr>
					<td height="10">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="10" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="265">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top2.swf">
							<PARAM NAME="Src" VALUE="flash/Top2.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<embed src="flash/Top2.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="10"> </embed>
						</OBJECT>
					</td>
				</tr>
				<TR>
					<TD class="PageTitle" height="20">Geo Raabata Admin :: Departments</TD>
				</TR>
				<tr>
					<td class="MenuBar" height="20"><uc1:mymenus id="MyMenus1" runat="server"></uc1:mymenus></td>
				</tr>
				<tr>
					<td class="MainBG" vAlign="top" align="center">
						<asp:Label id="lblMessage" runat="server" Font-Bold="True"></asp:Label><BR>
						<TABLE class="MainFormColor" id="tabNewForm" cellSpacing="0" cellPadding="3" width="750"
							align="center" border="0" runat="server">
							<TR>
								<TD class="OrangeFormTitle" colSpan="3">Department Information Form</TD>
							</TR>
							<TR>
								<TD class="MenuBar" width="330" vAlign="top"><asp:label id="Label2" runat="server" Font-Bold="True">Department Name</asp:label><asp:label id="txtDeptId" runat="server" Visible="False"></asp:label>
									<asp:RequiredFieldValidator id="RequiredFieldValidator1" runat="server" ControlToValidate="txtName" Display="Dynamic"
										ErrorMessage="* Required"></asp:RequiredFieldValidator></TD>
								<TD class="MenuBar" width="70" vAlign="top"></TD>
								<TD class="MenuBar" width="330" vAlign="top"><asp:label id="Label4" runat="server" Font-Bold="True">Strategic Business Unit</asp:label>
									<asp:CustomValidator id="CustomValidator1" runat="server" ControlToValidate="txtBusinessUnit" Display="Dynamic"
										ErrorMessage="* Required"></asp:CustomValidator></TD>
							</TR>
							<TR>
								<TD width="330"><asp:textbox id="txtName" tabIndex="1" runat="server" CssClass="textbox" Width="100%"></asp:textbox></TD>
								<TD width="70"></TD>
								<TD width="330"><asp:dropdownlist id="txtBusinessUnit" runat="server" CssClass="textbox" Width="100%"></asp:dropdownlist></TD>
							</TR>
							<TR>
								<TD class="MenuBar" id="tdError" colSpan="3" runat="server"><asp:label id="lblError" runat="server" CssClass="ErrorLabel" Font-Bold="True"></asp:label></TD>
							</TR>
							<TR>
								<TD colSpan="3"><asp:button id="btnQueryButton" tabIndex="4" runat="server" Width="76px" Text="Save"></asp:button><asp:button id="cmdDelete" runat="server" Width="76px" Text="Delete" CausesValidation="False"></asp:button><asp:button id="btnCancel" tabIndex="5" runat="server" Width="76px" CausesValidation="False"
										Text="Cancel"></asp:button></TD>
							</TR>
						</TABLE>
						<BR>
						<TABLE id="Table9" cellSpacing="0" cellPadding="3" width="750" align="center" border="0">
							<TR>
								<TD><asp:linkbutton id="LinkButton1" runat="server" Visible="False" CausesValidation="False">Add New Department</asp:linkbutton></TD>
							</TR>
							<TR>
								<TD><asp:datagrid id="DataGrid1" runat="server" Width="100%" DataKeyField="deptId" AutoGenerateColumns="False"
										AllowSorting="True" GridLines="Horizontal" BorderColor="Navy" BorderWidth="1px" CellPadding="3"
										BorderStyle="Solid" DESIGNTIMEDRAGDROP="735" PageSize="5">
										<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
										<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
										<AlternatingItemStyle CssClass="alternetitem"></AlternatingItemStyle>
										<ItemStyle CssClass="item"></ItemStyle>
										<HeaderStyle Font-Bold="True" CssClass="header"></HeaderStyle>
										<Columns>
											<asp:BoundColumn Visible="False" DataField="DeptId" SortExpression="DeptId" HeaderText="Dept ID"></asp:BoundColumn>
											<asp:BoundColumn DataField="DeptName" SortExpression="DeptName" HeaderText="Department Name"></asp:BoundColumn>
											<asp:BoundColumn DataField="SBU" SortExpression="SBU" HeaderText="SBU"></asp:BoundColumn>
											<asp:BoundColumn Visible="False" DataField="Division" SortExpression="Division" HeaderText="Division"></asp:BoundColumn>
											<asp:EditCommandColumn ButtonType="LinkButton" UpdateText="Update" CancelText="Cancel" EditText="Detail">
												<ItemStyle HorizontalAlign="Center" Width="80px" VerticalAlign="Middle"></ItemStyle>
											</asp:EditCommandColumn>
										</Columns>
										<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
									</asp:datagrid></TD>
							</TR>
							<TR>
								<TD><asp:panel id="pnlBUManager" runat="server">
										<TABLE id="Table4" cellSpacing="0" cellPadding="3" width="100%" bgColor="aliceblue" border="0"
											runat="server">
											<TR>
												<TD class="OrangeFormTitle" colSpan="3"><STRONG>HR Business Unit Managers:</STRONG></TD>
											</TR>
											<TR>
												<TD colSpan="3">
													<asp:DataGrid id="DataGrid2" runat="server" Width="100%" BorderStyle="Solid" CellPadding="3" BorderWidth="1px"
														BorderColor="Navy" GridLines="Horizontal" AllowSorting="True" AutoGenerateColumns="False"
														DataKeyField="bumangerid">
														<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
														<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
														<AlternatingItemStyle CssClass="alternetitem"></AlternatingItemStyle>
														<ItemStyle ForeColor="#4A3C8C" CssClass="item"></ItemStyle>
														<HeaderStyle Font-Bold="True" ForeColor="Black" CssClass="header"></HeaderStyle>
														<Columns>
															<asp:BoundColumn DataField="Name" SortExpression="Name" HeaderText="Name"></asp:BoundColumn>
															<asp:BoundColumn DataField="DateFrom" SortExpression="datefrom" HeaderText="From" DataFormatString="{0:d}"></asp:BoundColumn>
															<asp:BoundColumn DataField="dateto" SortExpression="dateto" HeaderText="To" DataFormatString="{0:d}"></asp:BoundColumn>
															<asp:BoundColumn DataField="IsActive" SortExpression="IsActive" HeaderText="Is Active"></asp:BoundColumn>
															<asp:EditCommandColumn ButtonType="LinkButton" UpdateText="Update" CancelText="Cancel" EditText="Edit"></asp:EditCommandColumn>
															<asp:BoundColumn Visible="False" DataField="bumangerid" HeaderText="bumangerid"></asp:BoundColumn>
														</Columns>
														<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
													</asp:DataGrid>
													<asp:Label id="Label5" runat="server" Visible="False">Record Not Found</asp:Label></TD>
											</TR>
											<TR>
												<TD width="350" colSpan="3">
													<asp:Button id="btnChange" runat="server" Width="180px" Text="Change HR BU Manager"></asp:Button></TD>
											</TR>
											<TR>
												<TD colSpan="3">
													<asp:Panel id="pnlEndDateBU" runat="server" Visible="False" Width="100%" CssClass="MainFormColor">
														<TABLE class="MainFormColor" id="Table1" cellSpacing="0" cellPadding="3" width="100%" border="0">
															<TR>
																<TD class="OrangeFormTitle">End Date is Required</TD>
															</TR>
															<TR>
																<TD><FONT color="red" size="2"><STRONG>An Active HR BU Manager's end date is not given. 
																			Please Enter End Date to&nbsp;add a new HR BU Manager's record.</STRONG></FONT></TD>
															</TR>
															<TR>
																<TD><STRONG>End Date:</STRONG><BR>
																	<ew:CalendarPopup id="doEndTo" runat="server">
																		<TextboxLabelStyle CssClass="textbox"></TextboxLabelStyle>
																		<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="White"></WeekdayStyle>
																		<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="Yellow"></MonthHeaderStyle>
																		<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																			BackColor="AntiqueWhite"></OffMonthStyle>
																		<ButtonStyle CssClass="button"></ButtonStyle>
																		<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="White"></GoToTodayStyle>
																		<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="LightGoldenrodYellow"></TodayDayStyle>
																		<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="Orange"></DayHeaderStyle>
																		<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="LightGray"></WeekendStyle>
																		<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="Yellow"></SelectedDateStyle>
																		<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="White"></ClearDateStyle>
																		<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="White"></HolidayStyle>
																	</ew:CalendarPopup></TD>
															</TR>
															<TR>
																<TD>
																	<asp:Button id="btnUpdateBUDate" runat="server" Width="76px" Text="Update"></asp:Button>
																	<asp:Button id="Button7" runat="server" Width="76px" Text="Cancel"></asp:Button></TD>
															</TR>
														</TABLE>
													</asp:Panel></TD>
											</TR>
											<TR>
												<TD colSpan="3">
													<asp:Panel id="pnlBUManagerForm" runat="server">
														<P>
															<TABLE class="MainFormColor" id="Table2" cellSpacing="0" cellPadding="3" width="100%" border="0">
																<TR>
																	<TD class="OrangeFormTitle" colSpan="3">HR BU Manger Information:</TD>
																</TR>
																<TR>
																	<TD class="MenuBar" width="330"><STRONG>HR BU Manager:</STRONG></TD>
																	<TD class="MenuBar" width="58"></TD>
																	<TD class="MenuBar" width="330"></TD>
																</TR>
																<TR>
																	<TD width="330">
																		<asp:DropDownList id="ddlBUManager" runat="server" Width="100%" CssClass="textbox"></asp:DropDownList></TD>
																	<TD width="58"></TD>
																	<TD width="330">
																		<asp:Label id="lblbumangerid" runat="server" Visible="False"></asp:Label></TD>
																</TR>
																<TR>
																	<TD class="MenuBar" width="330"><STRONG>From:</STRONG></TD>
																	<TD class="MenuBar" width="58"></TD>
																	<TD class="MenuBar" width="330">
																		<asp:CheckBox id="CheckBox1" runat="server" Text="To" AutoPostBack="True"></asp:CheckBox></TD>
																</TR>
																<TR>
																	<TD width="330"><WEEKDAYSTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small">
																			<ew:CalendarPopup id="dpFrom" runat="server">
																				<TextboxLabelStyle CssClass="textbox"></TextboxLabelStyle>
																				<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></WeekdayStyle>
																				<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="Yellow"></MonthHeaderStyle>
																				<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																					BackColor="AntiqueWhite"></OffMonthStyle>
																				<ButtonStyle CssClass="button"></ButtonStyle>
																				<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></GoToTodayStyle>
																				<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="LightGoldenrodYellow"></TodayDayStyle>
																				<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="Orange"></DayHeaderStyle>
																				<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="LightGray"></WeekendStyle>
																				<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="Yellow"></SelectedDateStyle>
																				<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></ClearDateStyle>
																				<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></HolidayStyle>
																			</ew:CalendarPopup>
																		</WEEKDAYSTYLE><MONTHHEADERSTYLE BackColor="Yellow" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></MONTHHEADERSTYLE><OFFMONTHSTYLE BackColor="AntiqueWhite" ForeColor="Gray" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></OFFMONTHSTYLE><GOTOTODAYSTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></GOTOTODAYSTYLE><TODAYDAYSTYLE BackColor="LightGoldenrodYellow" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></TODAYDAYSTYLE><DAYHEADERSTYLE BackColor="Orange" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></DAYHEADERSTYLE><WEEKENDSTYLE BackColor="LightGray" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></WEEKENDSTYLE><SELECTEDDATESTYLE BackColor="Yellow" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></SELECTEDDATESTYLE><CLEARDATESTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></CLEARDATESTYLE><HOLIDAYSTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></HOLIDAYSTYLE></TD>
																	<TD width="58"></TD>
																	<TD width="330"><WEEKDAYSTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small">
																			<ew:CalendarPopup id="dpTo" runat="server" Visible="False">
																				<TextboxLabelStyle CssClass="textbox"></TextboxLabelStyle>
																				<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></WeekdayStyle>
																				<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="Yellow"></MonthHeaderStyle>
																				<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																					BackColor="AntiqueWhite"></OffMonthStyle>
																				<ButtonStyle CssClass="button"></ButtonStyle>
																				<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></GoToTodayStyle>
																				<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="LightGoldenrodYellow"></TodayDayStyle>
																				<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="Orange"></DayHeaderStyle>
																				<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="LightGray"></WeekendStyle>
																				<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="Yellow"></SelectedDateStyle>
																				<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></ClearDateStyle>
																				<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></HolidayStyle>
																			</ew:CalendarPopup>
																		</WEEKDAYSTYLE><MONTHHEADERSTYLE BackColor="Yellow" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></MONTHHEADERSTYLE><OFFMONTHSTYLE BackColor="AntiqueWhite" ForeColor="Gray" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></OFFMONTHSTYLE><GOTOTODAYSTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></GOTOTODAYSTYLE><TODAYDAYSTYLE BackColor="LightGoldenrodYellow" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></TODAYDAYSTYLE><DAYHEADERSTYLE BackColor="Orange" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></DAYHEADERSTYLE><WEEKENDSTYLE BackColor="LightGray" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></WEEKENDSTYLE><SELECTEDDATESTYLE BackColor="Yellow" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></SELECTEDDATESTYLE><CLEARDATESTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></CLEARDATESTYLE><HOLIDAYSTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></HOLIDAYSTYLE></TD>
																</TR>
																<TR>
																	<TD colSpan="3">
																		<asp:Button id="cmdSave" runat="server" Width="76px" Text="Save"></asp:Button>
																		<asp:Button id="cmdCancel" runat="server" Width="76px" Text="Cancel"></asp:Button></TD>
																</TR>
															</TABLE>
														</P>
													</asp:Panel></TD>
											</TR>
										</TABLE>
									</asp:panel></TD>
							</TR>
							<TR>
								<TD><asp:panel id="pnlHOD" runat="server">
										<TABLE id="Table5" cellSpacing="0" cellPadding="3" width="100%" bgColor="aliceblue" border="0"
											runat="server">
											<TR>
												<TD class="OrangeFormTitle" colSpan="3"><STRONG>HOD</STRONG></TD>
											</TR>
											<TR>
												<TD colSpan="3">
													<asp:DataGrid id="DataGrid5" runat="server" Width="100%" BorderStyle="Solid" CellPadding="3" BorderWidth="1px"
														BorderColor="Navy" GridLines="Horizontal" AllowSorting="True" AutoGenerateColumns="False"
														DataKeyField="h_id">
														<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
														<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
														<AlternatingItemStyle CssClass="alternetitem"></AlternatingItemStyle>
														<ItemStyle CssClass="item"></ItemStyle>
														<HeaderStyle Font-Bold="True" ForeColor="Black" CssClass="header"></HeaderStyle>
														<Columns>
															<asp:BoundColumn DataField="Name" SortExpression="Name" HeaderText="Name"></asp:BoundColumn>
															<asp:BoundColumn DataField="DateFrom" SortExpression="datefrom" HeaderText="From" DataFormatString="{0:d}"></asp:BoundColumn>
															<asp:BoundColumn DataField="dateto" SortExpression="dateto" HeaderText="To" DataFormatString="{0:d}"></asp:BoundColumn>
															<asp:BoundColumn DataField="IsActive" SortExpression="IsActive" HeaderText="Is Active"></asp:BoundColumn>
															<asp:EditCommandColumn ButtonType="LinkButton" UpdateText="Update" CancelText="Cancel" EditText="Edit"></asp:EditCommandColumn>
															<asp:BoundColumn Visible="False" DataField="h_id" SortExpression="h_id" HeaderText="h_id"></asp:BoundColumn>
														</Columns>
														<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
													</asp:DataGrid>
													<asp:Label id="Label8" runat="server" Visible="False">Record Not Found</asp:Label></TD>
											</TR>
											<TR>
												<TD width="350" colSpan="3">
													<asp:Button id="cmdChangeHOD" runat="server" Width="180px" Text="Change HOD"></asp:Button></TD>
											</TR>
											<TR>
												<TD colSpan="3">
													<asp:Panel id="pnlEndHOD" runat="server" Visible="False" Width="100%" CssClass="MainFormColor">
														<TABLE class="MainFormColor" id="Table8" cellSpacing="0" cellPadding="3" width="100%" border="0">
															<TR>
																<TD class="OrangeFormTitle">End Date is Required</TD>
															</TR>
															<TR>
																<TD><FONT color="red" size="2"><STRONG>An Active HR BU Manager's end date is not given. 
																			Please Enter End Date to&nbsp;add a new HR BU Manager's record.</STRONG></FONT></TD>
															</TR>
															<TR>
																<TD><STRONG>End Date:</STRONG><BR>
																	<ew:CalendarPopup id="dpEndHOD" runat="server">
																		<TextboxLabelStyle CssClass="textbox"></TextboxLabelStyle>
																		<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="White"></WeekdayStyle>
																		<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="Yellow"></MonthHeaderStyle>
																		<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																			BackColor="AntiqueWhite"></OffMonthStyle>
																		<ButtonStyle CssClass="button"></ButtonStyle>
																		<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="White"></GoToTodayStyle>
																		<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="LightGoldenrodYellow"></TodayDayStyle>
																		<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="Orange"></DayHeaderStyle>
																		<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="LightGray"></WeekendStyle>
																		<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="Yellow"></SelectedDateStyle>
																		<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="White"></ClearDateStyle>
																		<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="White"></HolidayStyle>
																	</ew:CalendarPopup></TD>
															</TR>
															<TR>
																<TD>
																	<asp:Button id="Button9" runat="server" Width="76px" Text="Update"></asp:Button>
																	<asp:Button id="Button8" runat="server" Width="76px" Text="Cancel"></asp:Button></TD>
															</TR>
														</TABLE>
													</asp:Panel></TD>
											</TR>
											<TR>
												<TD colSpan="3">
													<asp:Panel id="pnlHODForm" runat="server">
														<P>
															<TABLE class="MainFormColor" id="Table7" cellSpacing="0" cellPadding="3" width="100%" border="0">
																<TR>
																	<TD class="OrangeFormTitle" colSpan="3">HOD Information:</TD>
																</TR>
																<TR>
																	<TD class="MenuBar" width="330"><STRONG>Head Of Department:</STRONG></TD>
																	<TD class="MenuBar" width="58"></TD>
																	<TD class="MenuBar" width="330">
																		<asp:Label id="lblHODID" runat="server" Visible="False"></asp:Label></TD>
																</TR>
																<TR>
																	<TD width="330">
																		<asp:DropDownList id="ddlHOD2" runat="server" Width="100%" CssClass="textbox"></asp:DropDownList></TD>
																	<TD width="58"></TD>
																	<TD width="330"></TD>
																</TR>
																<TR>
																	<TD class="MenuBar" width="330"><STRONG>From:</STRONG></TD>
																	<TD class="MenuBar" width="58"></TD>
																	<TD class="MenuBar" width="330">
																		<asp:CheckBox id="cbTo2" runat="server" Text="To" AutoPostBack="True"></asp:CheckBox></TD>
																</TR>
																<TR>
																	<TD width="330"><WEEKDAYSTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small">
																			<ew:CalendarPopup id="dpFrom2" runat="server">
																				<TextboxLabelStyle CssClass="textbox"></TextboxLabelStyle>
																				<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></WeekdayStyle>
																				<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="Yellow"></MonthHeaderStyle>
																				<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																					BackColor="AntiqueWhite"></OffMonthStyle>
																				<ButtonStyle CssClass="button"></ButtonStyle>
																				<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></GoToTodayStyle>
																				<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="LightGoldenrodYellow"></TodayDayStyle>
																				<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="Orange"></DayHeaderStyle>
																				<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="LightGray"></WeekendStyle>
																				<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="Yellow"></SelectedDateStyle>
																				<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></ClearDateStyle>
																				<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></HolidayStyle>
																			</ew:CalendarPopup>
																		</WEEKDAYSTYLE><MONTHHEADERSTYLE BackColor="Yellow" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></MONTHHEADERSTYLE><OFFMONTHSTYLE BackColor="AntiqueWhite" ForeColor="Gray" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></OFFMONTHSTYLE><GOTOTODAYSTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></GOTOTODAYSTYLE><TODAYDAYSTYLE BackColor="LightGoldenrodYellow" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></TODAYDAYSTYLE><DAYHEADERSTYLE BackColor="Orange" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></DAYHEADERSTYLE><WEEKENDSTYLE BackColor="LightGray" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></WEEKENDSTYLE><SELECTEDDATESTYLE BackColor="Yellow" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></SELECTEDDATESTYLE><CLEARDATESTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></CLEARDATESTYLE><HOLIDAYSTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></HOLIDAYSTYLE></TD>
																	<TD width="58"></TD>
																	<TD width="330"><WEEKDAYSTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small">
																			<ew:CalendarPopup id="dpTo2" runat="server" Visible="False">
																				<TextboxLabelStyle CssClass="textbox"></TextboxLabelStyle>
																				<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></WeekdayStyle>
																				<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="Yellow"></MonthHeaderStyle>
																				<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																					BackColor="AntiqueWhite"></OffMonthStyle>
																				<ButtonStyle CssClass="button"></ButtonStyle>
																				<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></GoToTodayStyle>
																				<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="LightGoldenrodYellow"></TodayDayStyle>
																				<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="Orange"></DayHeaderStyle>
																				<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="LightGray"></WeekendStyle>
																				<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="Yellow"></SelectedDateStyle>
																				<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></ClearDateStyle>
																				<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></HolidayStyle>
																			</ew:CalendarPopup>
																		</WEEKDAYSTYLE><MONTHHEADERSTYLE BackColor="Yellow" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></MONTHHEADERSTYLE><OFFMONTHSTYLE BackColor="AntiqueWhite" ForeColor="Gray" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></OFFMONTHSTYLE><GOTOTODAYSTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></GOTOTODAYSTYLE><TODAYDAYSTYLE BackColor="LightGoldenrodYellow" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></TODAYDAYSTYLE><DAYHEADERSTYLE BackColor="Orange" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></DAYHEADERSTYLE><WEEKENDSTYLE BackColor="LightGray" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></WEEKENDSTYLE><SELECTEDDATESTYLE BackColor="Yellow" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></SELECTEDDATESTYLE><CLEARDATESTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></CLEARDATESTYLE><HOLIDAYSTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></HOLIDAYSTYLE></TD>
																</TR>
																<TR>
																	<TD colSpan="3">
																		<asp:Button id="Button5" runat="server" Width="76px" Text="Save"></asp:Button>
																		<asp:Button id="Button4" runat="server" Width="76px" Text="Cancel"></asp:Button></TD>
																</TR>
															</TABLE>
														</P>
													</asp:Panel></TD>
											</TR>
										</TABLE>
									</asp:panel></TD>
							</TR>
							<TR>
								<TD></TD>
							</TR>
						</TABLE>
						</FONT></STRONG></td>
				</tr>
				<tr>
					<td vAlign="middle" align="center" height="20">Copyright © 2005 Independent Media 
						Corporation <A href="http://www.geo.tv">www.geo.tv</A></td>
				</tr>
			</table>
		</form>
	</body>
</HTML>
