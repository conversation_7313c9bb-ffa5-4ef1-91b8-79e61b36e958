<%@ Page CodeBehind="ManageRequest.aspx.cs" Language="c#" AutoEventWireup="false" Inherits="GeoRabtaSite.ManageRequest" smartNavigation="True" %>
<%@ Register TagPrefix="ew" Namespace="eWorld.UI" Assembly="eWorld.UI" %>
<%@ Register TagPrefix="uc1" TagName="myMenus" Src="myMenus.ascx" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Raabat Admin :: Manage Request</title>
		<meta content="False" name="vs_showGrid">
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="RaabtaAdmin.css" type="text/css" rel="stylesheet">
		<LINK href="../Styles4.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body dir="ltr" bottomMargin="0" bgProperties="fixed" leftMargin="0" topMargin="0" rightMargin="0">
		<form id="ideas" runat="server">
			&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			<table height="100%" cellSpacing="0" cellPadding="0" width="780" align="center" bgColor="#ffffff"
				border="0">
				<tr>
					<td vAlign="middle" align="left" height="69"><STRONG>
							<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
								height="69" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
								<PARAM NAME="_cx" VALUE="20638">
								<PARAM NAME="_cy" VALUE="1826">
								<PARAM NAME="FlashVars" VALUE="">
								<PARAM NAME="Movie" VALUE="flash/Top1.swf">
								<PARAM NAME="Src" VALUE="flash/Top1.swf">
								<PARAM NAME="WMode" VALUE="Window">
								<PARAM NAME="Play" VALUE="-1">
								<PARAM NAME="Loop" VALUE="-1">
								<PARAM NAME="Quality" VALUE="High">
								<PARAM NAME="SAlign" VALUE="">
								<PARAM NAME="Menu" VALUE="-1">
								<PARAM NAME="Base" VALUE="">
								<PARAM NAME="AllowScriptAccess" VALUE="">
								<PARAM NAME="Scale" VALUE="ShowAll">
								<PARAM NAME="DeviceFont" VALUE="0">
								<PARAM NAME="EmbedMovie" VALUE="0">
								<PARAM NAME="BGColor" VALUE="">
								<PARAM NAME="SWRemote" VALUE="">
								<PARAM NAME="MovieData" VALUE="">
								<PARAM NAME="SeamlessTabbing" VALUE="1">
								<PARAM NAME="Profile" VALUE="0">
								<PARAM NAME="ProfileAddress" VALUE="">
								<PARAM NAME="ProfilePort" VALUE="0">
								<PARAM NAME="AllowNetworking" VALUE="all">
								<PARAM NAME="AllowFullScreen" VALUE="false">
								<embed src="flash/Top1.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
									type="application/x-shockwave-flash" width="780" height="69"> </embed>
							</OBJECT>
						</STRONG>
					</td>
				</tr>
				<tr>
					<td height="10">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="10" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="265">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top2.swf">
							<PARAM NAME="Src" VALUE="flash/Top2.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top2.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="10"> </embed>
						</OBJECT>
					</td>
				</tr>
				<TR>
					<TD class="PageTitle" height="20">Geo Raabat Admin :: Degrees</TD>
				</TR>
				<tr>
					<td class="MenuBar" height="20"><uc1:mymenus id="MyMenus1" runat="server"></uc1:mymenus></td>
				</tr>
				<tr>
					<td class="MainBG" vAlign="top" align="left"><asp:imagebutton id="imgRequest" runat="server" ImageUrl="../images/myqueuelist.gif"></asp:imagebutton><asp:imagebutton id="imgReopen" runat="server" ImageUrl="../images/reopengrievance.gif"></asp:imagebutton>
						<asp:imagebutton id="imgcCGrievance" runat="server" ImageUrl="../images/ccgrievances.gif"></asp:imagebutton><BR>
						<asp:panel id="pnlMyReq" Runat="server">
							<TABLE class="MainFormColor" id="tabForm" cellSpacing="0" cellPadding="3" width="750" align="center"
								border="0" runat="server">
								<TBODY>
									<TR>
										<TD class="OrangeFormTitle" width="320" colSpan="3">My Queue List:: Data 
											Verification</TD>
									</TR>
									<TR>
										<TD colSpan="3">
											<asp:datagrid id="dgMyRequest" runat="server" GridLines="Vertical" CellPadding="3" BackColor="White"
												BorderWidth="1px" BorderStyle="None" BorderColor="#999999" AutoGenerateColumns="False" Width="100%">
												<FooterStyle ForeColor="Black" BackColor="#CCCCCC"></FooterStyle>
												<SelectedItemStyle Font-Bold="True" ForeColor="White" BackColor="#008A8C"></SelectedItemStyle>
												<AlternatingItemStyle BackColor="Gainsboro"></AlternatingItemStyle>
												<ItemStyle ForeColor="Black" BackColor="#EEEEEE"></ItemStyle>
												<HeaderStyle Font-Bold="True" ForeColor="White" BackColor="#000084"></HeaderStyle>
												<Columns>
													<asp:ButtonColumn Text="Select" CommandName="Select">
														<HeaderStyle Width="40px"></HeaderStyle>
													</asp:ButtonColumn>
													<asp:BoundColumn DataField="rID" HeaderText="Request #">
														<HeaderStyle Width="60px"></HeaderStyle>
													</asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="fieldno" HeaderText="Fieldno"></asp:BoundColumn>
													<asp:BoundColumn DataField="fieldname" HeaderText="Request Detail">
														<HeaderStyle Width="197px"></HeaderStyle>
													</asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="requestforchange" HeaderText="RequestForChange"></asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="requestforchangeid" HeaderText="requestforchangeid"></asp:BoundColumn>
													<asp:BoundColumn DataField="requisitecode" HeaderText="Requestor">
														<HeaderStyle Width="100px"></HeaderStyle>
													</asp:BoundColumn>
													<asp:BoundColumn DataField="requestdate" HeaderText="Request Date">
														<HeaderStyle Width="160px"></HeaderStyle>
													</asp:BoundColumn>
													<asp:BoundColumn DataField="requesttimeline" HeaderText="Timeline to Complete">
														<HeaderStyle Width="70px"></HeaderStyle>
													</asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="documentid" HeaderText="documentid"></asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="sp_id" HeaderText="spid"></asp:BoundColumn>
													<asp:TemplateColumn>
														<HeaderStyle Width="70px"></HeaderStyle>
														<HeaderTemplate>
															Status
														</HeaderTemplate>
														<ItemTemplate>
															<asp:Label id="lblStatus" runat="server"></asp:Label>
														</ItemTemplate>
													</asp:TemplateColumn>
													<asp:BoundColumn Visible="False" DataField="code">
														<HeaderStyle Width="70px"></HeaderStyle>
													</asp:BoundColumn>
												</Columns>
												<PagerStyle HorizontalAlign="Center" ForeColor="Black" BackColor="#999999" Mode="NumericPages"></PagerStyle>
											</asp:datagrid></TD>
									</TR>
									<TR>
										<TD colSpan="3">
											<asp:panel id="pnlrView" runat="server" Visible="False">
												<STRONG><FONT size="3">Request Details<BR>
														<TABLE id="Table1" cellSpacing="1" cellPadding="1" width="100%" border="1">
															<TR>
																<TD style="WIDTH: 265px"><STRONG>Employee Code:</STRONG></TD>
																<TD>
																	<asp:Label id="lbleCode" runat="server"></asp:Label></TD>
															</TR>
															<TR>
																<TD style="WIDTH: 265px"><STRONG>Change Request of:</STRONG></TD>
																<TD>
																	<asp:Label id="lblFieldName" runat="server" Font-Bold="True"></asp:Label></TD>
															</TR>
															<TR>
																<TD style="WIDTH: 265px"><STRONG>Current Information:</STRONG></TD>
																<TD>
																	<asp:Label id="lblcInformation" runat="server"></asp:Label>
																	<asp:Label id="lnkvPicture" runat="server" ForeColor="Blue" CssClass="hh">View</asp:Label></TD>
															</TR>
															<TR>
																<TD style="WIDTH: 265px"><STRONG>Request Information:</STRONG></TD>
																<TD>
																	<asp:Label id="lblRequestInformation" runat="server"></asp:Label></TD>
															</TR>
															<TR>
																<TD style="WIDTH: 265px"><STRONG>Request Posted on:</STRONG></TD>
																<TD>
																	<asp:Label id="lblRequestDate" runat="server"></asp:Label></TD>
															</TR>
															<TR>
																<TD style="WIDTH: 265px"><STRONG>Timeline to Approved / Reject:</STRONG></TD>
																<TD>
																	<asp:Label id="lblTimeline" runat="server"></asp:Label></TD>
															</TR>
															<TR>
																<TD style="WIDTH: 265px"><STRONG>Verification Criteria:</STRONG></TD>
																<TD>
																	<asp:Label id="lblVerificationCriteria" runat="server"></asp:Label></TD>
															</TR>
															<TR>
																<TD style="WIDTH: 265px"><STRONG>Documents Required for Verification by HR:</STRONG></TD>
																<TD>
																	<asp:Label id="lblRequiredByHR" runat="server"></asp:Label></TD>
															</TR>
															<TR>
																<TD style="WIDTH: 265px"><STRONG>Documents Required from TeamGeo member</STRONG></TD>
																<TD>
																	<asp:Label id="lblRequiredByEmp" runat="server"></asp:Label></TD>
															</TR>
															<TR>
																<TD style="WIDTH: 265px"><STRONG id="col1" runat="server">View Attached Document:</STRONG></TD>
																<TD id="col2" runat="server">
																	<asp:Label id="lnkView" runat="server" ForeColor="Blue" CssClass="hh">View</asp:Label></TD>
															</TR>
															<TR>
																<TD colSpan="2">
																	<asp:Button id="btnAccept" runat="server" CssClass="button" Text="Accept"></asp:Button>
																	<asp:Button id="btnReject" runat="server" CssClass="button" Text="Reject"></asp:Button>
																	<asp:Label id="Label7" runat="server"></asp:Label></TD>
															</TR>
															<TR>
																<TD id="TD1" colSpan="2" runat="server">
																	<asp:Label id="lblAction" runat="server"></asp:Label><BR>
																	<asp:TextBox id="txtMessage" runat="server" Width="100%" CssClass="textbox" TextMode="MultiLine"
																		Height="48px"></asp:TextBox><BR>
																	<asp:Button id="btnSend" runat="server" CssClass="button" Text="Submit"></asp:Button>
																	<asp:Button id="btnCancel" runat="server" CssClass="button" Text="Cancel"></asp:Button></TD>
															</TR>
														</TABLE>
													</FONT></STRONG>
											</asp:panel></TD>
									</TR>
									<TR>
										<TD class="OrangeFormTitle" width="320" colSpan="3">My Queue List:: Employee 
											Grievances</TD>
									</TR>
									<TR>
										<TD class="MenuBar" width="320" colSpan="3"></TD>
									</TR>
									<TR>
										<TD colSpan="3">
											<asp:datagrid id="dgMyGrievance" runat="server" GridLines="Vertical" CellPadding="3" BackColor="White"
												BorderWidth="1px" BorderStyle="None" BorderColor="#999999" AutoGenerateColumns="False" Width="100%">
												<FooterStyle ForeColor="Black" BackColor="#CCCCCC"></FooterStyle>
												<SelectedItemStyle Font-Bold="True" ForeColor="White" BackColor="#008A8C"></SelectedItemStyle>
												<AlternatingItemStyle BackColor="Gainsboro"></AlternatingItemStyle>
												<ItemStyle ForeColor="Black" BackColor="#EEEEEE"></ItemStyle>
												<HeaderStyle Font-Bold="True" ForeColor="White" BackColor="#000084"></HeaderStyle>
												<Columns>
													<asp:ButtonColumn Text="Select" CommandName="Select">
														<HeaderStyle Width="40px"></HeaderStyle>
													</asp:ButtonColumn>
													<asp:BoundColumn DataField="rid" HeaderText="Request No">
														<HeaderStyle Width="60px"></HeaderStyle>
													</asp:BoundColumn>
													<asp:BoundColumn DataField="grievancetitle" HeaderText="Title"></asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="grievancedetail" HeaderText="Detail"></asp:BoundColumn>
													<asp:BoundColumn DataField="requestby" HeaderText="Requestor"></asp:BoundColumn>
													<asp:BoundColumn DataField="requestdate" HeaderText="Request Date"></asp:BoundColumn>
													<asp:BoundColumn DataField="requesttimeline" HeaderText="Timeline to Complete"></asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="sgid"></asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="requisitecode"></asp:BoundColumn>
													<asp:TemplateColumn HeaderText="Status">
														<ItemTemplate>
															<asp:Label id="lblgStatus" runat="server"></asp:Label>
														</ItemTemplate>
													</asp:TemplateColumn>
													<asp:BoundColumn DataField="status"></asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="sgrievance"></asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="grievance"></asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="requeststatus" HeaderText="Request Status"></asp:BoundColumn>
												</Columns>
												<PagerStyle HorizontalAlign="Center" ForeColor="Black" BackColor="#999999" Mode="NumericPages"></PagerStyle>
											</asp:datagrid></TD>
									</TR>
									<TR>
										<TD colSpan="3">
											<asp:panel id="pnlgView" runat="server" Visible="False">
												<P><STRONG>
														<TABLE id="Table2" cellSpacing="1" cellPadding="1" width="300" border="0">
															<TR>
																<TD><STRONG>Request ID:</STRONG></TD>
																<TD>
																	<asp:Label id="lblRID" runat="server"></asp:Label></TD>
															</TR>
															<TR>
																<TD><STRONG>Grievance Subject:&nbsp;</STRONG></TD>
																<TD>
																	<asp:Label id="lblTitle" runat="server"></asp:Label></TD>
															</TR>
															<TR>
																<TD><STRONG>Grievance Type:&nbsp;&nbsp;</STRONG></TD>
																<TD>
																	<asp:Label id="lblGrievanceType" runat="server"></asp:Label></TD>
															</TR>
															<TR>
																<TD><STRONG>Grievance Sub Type:</STRONG></TD>
																<TD>
																	<asp:Label id="lblGrievanceSubType" runat="server"></asp:Label></TD>
															</TR>
															<TR>
																<TD><STRONG>Grievance Detail:&nbsp;</STRONG></TD>
																<TD>
																	<asp:Label id="txtDetail" runat="server"></asp:Label></TD>
															</TR>
														</TABLE>
													</STRONG>
													<BR>
													<asp:Panel id="pnlbView" runat="server" Visible="False">
														<STRONG>Grievance Blog:<BR>
														</STRONG>
														<asp:DataGrid id="dgBlog" runat="server" GridLines="Vertical" CellPadding="3" BackColor="White"
															BorderWidth="1px" BorderStyle="None" BorderColor="#999999" AutoGenerateColumns="False" Width="100%"
															Visible="False">
															<FooterStyle ForeColor="Black" BackColor="#CCCCCC"></FooterStyle>
															<SelectedItemStyle Font-Bold="True" ForeColor="White" BackColor="#008A8C"></SelectedItemStyle>
															<AlternatingItemStyle BackColor="#DCDCDC"></AlternatingItemStyle>
															<ItemStyle ForeColor="Black" BackColor="#EEEEEE"></ItemStyle>
															<HeaderStyle Font-Bold="True" ForeColor="White" BackColor="#000084"></HeaderStyle>
															<Columns>
																<asp:BoundColumn DataField="comment" HeaderText="Comments"></asp:BoundColumn>
																<asp:BoundColumn DataField="sendon" HeaderText="Post on"></asp:BoundColumn>
																<asp:BoundColumn DataField="pcode" HeaderText="Pcode"></asp:BoundColumn>
																<asp:BoundColumn DataField="sendby" HeaderText="Posted by"></asp:BoundColumn>
																<asp:BoundColumn DataField="senderdesignation" HeaderText="Designation"></asp:BoundColumn>
																<asp:BoundColumn DataField="senderdepartment" HeaderText="Department"></asp:BoundColumn>
															</Columns>
															<PagerStyle HorizontalAlign="Center" ForeColor="Black" BackColor="#999999" Mode="NumericPages"></PagerStyle>
														</asp:DataGrid></P>
												<P><STRONG></STRONG>
											</asp:panel></P>
											<P><STRONG>Comments:<BR>
												</STRONG>
												<asp:textbox id="txtComments" runat="server" Width="720px" CssClass="textbox" TextMode="MultiLine"
													Height="74px"></asp:textbox><BR>
												<STRONG>Closing Code:<BR>
												</STRONG>
												<asp:dropdownlist id="ddClosingCode" runat="server" Width="592px" CssClass="textbox" AutoPostBack="True"></asp:dropdownlist>
												<asp:linkbutton id="lnkRedirect" runat="server">Re-direct Grievance</asp:linkbutton><BR>
												<asp:panel id="pnlInterimDate" runat="server">
													<STRONG>Select Interim Closing Date:<BR>
														<ew:CalendarPopup id="cpInterim" runat="server">
															<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="White"></WeekdayStyle>
															<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Yellow"></MonthHeaderStyle>
															<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																BackColor="AntiqueWhite"></OffMonthStyle>
															<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="White"></GoToTodayStyle>
															<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="LightGoldenrodYellow"></TodayDayStyle>
															<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Orange"></DayHeaderStyle>
															<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="LightGray"></WeekendStyle>
															<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Yellow"></SelectedDateStyle>
															<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="White"></ClearDateStyle>
															<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="White"></HolidayStyle>
														</ew:CalendarPopup></STRONG></asp:panel>
												<asp:panel id="pnlRedirect" runat="server">
													<STRONG>Grievance Type:<BR>
													</STRONG>
													<asp:dropdownlist id="ddGrievance" runat="server" Width="304px" CssClass="textbox" AutoPostBack="True"></asp:dropdownlist>
													<BR>
													<STRONG>Grievance Sub Type:<BR>
													</STRONG>
													<asp:DropDownList id="ddSubGrivance" runat="server" Width="304px" CssClass="textbox"></asp:DropDownList>
												</asp:panel>
												<asp:button id="btnGrievanceSubmit" runat="server" CssClass="button" Text="Submit"></asp:button><BR>
												<STRONG></STRONG>
											</P>
						</asp:panel></td>
				</tr>
			</table>
			</asp:panel><asp:panel id="pnlEdu" Runat="server">&nbsp; 
      <TABLE id="Table4" cellSpacing="0" cellPadding="2" width="750" align="center" border="0">
					<TR>
						<TD class="OrangeFormTitle">Educational Queue List:</TD>
					</TR>
					<TR>
						<TD class="FormColor">
							<asp:datagrid id="Datagrid2" runat="server" GridLines="Vertical" CellPadding="3" BackColor="White"
								BorderWidth="1px" BorderStyle="None" BorderColor="#999999" AutoGenerateColumns="False" Width="100%">
								<FooterStyle ForeColor="Black" BackColor="#CCCCCC"></FooterStyle>
								<SelectedItemStyle Font-Bold="True" ForeColor="White" BackColor="#008A8C"></SelectedItemStyle>
								<AlternatingItemStyle BackColor="Gainsboro"></AlternatingItemStyle>
								<ItemStyle ForeColor="Black" BackColor="#EEEEEE"></ItemStyle>
								<HeaderStyle Font-Bold="True" ForeColor="White" BackColor="#000084"></HeaderStyle>
								<Columns>
									<asp:ButtonColumn Text="Select" CommandName="Select">
										<HeaderStyle Width="40px"></HeaderStyle>
									</asp:ButtonColumn>
									<asp:BoundColumn DataField="reqid" HeaderText="Request #">
										<HeaderStyle Width="60px"></HeaderStyle>
									</asp:BoundColumn>
									<asp:BoundColumn DataField="addflag" HeaderText="Request Detail">
										<HeaderStyle Width="197px"></HeaderStyle>
									</asp:BoundColumn>
									<asp:BoundColumn DataField="Name" HeaderText="Requester">
										<HeaderStyle Width="100px"></HeaderStyle>
									</asp:BoundColumn>
									<asp:BoundColumn DataField="c_at" HeaderText="Requested Date" DataFormatString="{0:d MMM, yyyy h:m:s}">
										<HeaderStyle Width="160px"></HeaderStyle>
									</asp:BoundColumn>
									<asp:BoundColumn DataField="timeline" HeaderText="Timeline to Complete" DataFormatString="{0:d MMM,yyyy}">
										<HeaderStyle Width="85px"></HeaderStyle>
									</asp:BoundColumn>
									<asp:BoundColumn HeaderText="Status">
										<HeaderStyle Width="70px"></HeaderStyle>
									</asp:BoundColumn>
								</Columns>
								<PagerStyle HorizontalAlign="Center" ForeColor="Black" BackColor="#999999" Mode="NumericPages"></PagerStyle>
							</asp:datagrid></TD>
					</TR>
				</TABLE>
      <TABLE id="tabEduDetail" cellSpacing="0" cellPadding="3" width="750" align="center" border="1"
					runat="server">
					<TR>
						<TD class="OrangeFormTitle" colSpan="2">Educational Queue List Request Detail</TD>
					</TR>
					<TR>
						<TD style="WIDTH: 269px; HEIGHT: 20px" vAlign="top" align="left"><STRONG>Request No.</STRONG></TD>
						<TD style="HEIGHT: 20px">
							<asp:Label id="Label13" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 269px" vAlign="top" align="left"><STRONG>Requested For:</STRONG></TD>
						<TD>
							<asp:Label id="Label14" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 269px" vAlign="top" align="left"><STRONG>Employee Code:</STRONG></TD>
						<TD>
							<asp:Label id="Label15" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 269px" vAlign="top" align="left"><STRONG>Employee Name:</STRONG></TD>
						<TD>
							<asp:Label id="Label16" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 269px" vAlign="top" align="left"><STRONG>Designation </STRONG>
						</TD>
						<TD>
							<asp:Label id="Label17" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 269px" vAlign="top" align="left"><STRONG>Department:</STRONG></TD>
						<TD>
							<asp:Label id="Label3" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 269px" vAlign="top" align="left"><STRONG>Degree:</STRONG></TD>
						<TD>
							<asp:Label id="Label18" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 269px" vAlign="top" align="left"><STRONG>Institute:</STRONG></TD>
						<TD>
							<asp:Label id="Label19" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 269px" vAlign="top" align="left"><STRONG>Major:</STRONG></TD>
						<TD>
							<asp:Label id="Label20" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 269px" vAlign="top" align="left"><STRONG>Result:</STRONG></TD>
						<TD>
							<asp:Label id="Label4" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 269px" vAlign="top" align="left"><STRONG>Duration:</STRONG></TD>
						<TD>
							<asp:Label id="Label21" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 269px" vAlign="top" align="left"><STRONG>Distinction:</STRONG></TD>
						<TD>
							<asp:Label id="Label22" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 269px" vAlign="top" align="left"><STRONG>Timeline to Approve/Reject:</STRONG></TD>
						<TD>
							<asp:Label id="Label23" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 269px" vAlign="top" align="left"><STRONG>Verification Criteria:</STRONG></TD>
						<TD>
							<asp:Label id="Label24" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 269px" vAlign="top" align="left"><STRONG>Documents Required for 
								Verification by HR:</STRONG></TD>
						<TD>
							<asp:Label id="Label25" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 269px" vAlign="top" align="left"><STRONG>Documents Required from 
								TeamGeo member</STRONG></TD>
						<TD>
							<asp:Label id="Label26" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 269px" vAlign="top" align="left"><STRONG>View Attached Document:</STRONG></TD>
						<TD id="Td4" runat="server">
							<asp:Label id="Label12" runat="server" ForeColor="Blue" CssClass="hh">View</asp:Label></TD>
					</TR>
					<TR>
						<TD colSpan="2">
							<asp:Button id="Button8" runat="server" CssClass="button" Text="Accept"></asp:Button>
							<asp:Button id="Button7" runat="server" CssClass="button" Text="Reject"></asp:Button></TD>
					</TR>
					<TR>
						<TD id="Td5" colSpan="2" runat="server">
							<asp:Label id="Label11" runat="server"></asp:Label><BR>
							<asp:TextBox id="TextBox2" runat="server" Width="100%" CssClass="textbox" TextMode="MultiLine"
								Rows="4"></asp:TextBox><BR>
							<asp:Button id="Button6" runat="server" CssClass="button" Text="Submit" Enabled="False"></asp:Button>
							<asp:Button id="Button5" runat="server" CssClass="button" Text="Cancel"></asp:Button></TD>
					</TR>
				</TABLE></asp:panel><asp:panel id="pnlFamily" Runat="server">
				<TABLE id="Table2" cellSpacing="0" cellPadding="2" width="750" align="center" border="0">
					<TR>
						<TD class="OrangeFormTitle">Family Queue List:</TD>
					</TR>
					<TR>
						<TD class="FormColor">
							<asp:datagrid id="Datagrid1" runat="server" GridLines="Vertical" CellPadding="3" BackColor="White"
								BorderWidth="1px" BorderStyle="None" BorderColor="#999999" AutoGenerateColumns="False" Width="100%">
								<FooterStyle ForeColor="Black" BackColor="#CCCCCC"></FooterStyle>
								<SelectedItemStyle Font-Bold="True" ForeColor="White" BackColor="#008A8C"></SelectedItemStyle>
								<AlternatingItemStyle BackColor="Gainsboro"></AlternatingItemStyle>
								<ItemStyle ForeColor="Black" BackColor="#EEEEEE"></ItemStyle>
								<HeaderStyle Font-Bold="True" ForeColor="White" BackColor="#000084"></HeaderStyle>
								<Columns>
									<asp:ButtonColumn Text="Select" CommandName="Select">
										<HeaderStyle Width="40px"></HeaderStyle>
									</asp:ButtonColumn>
									<asp:BoundColumn DataField="reqid" HeaderText="Request #">
										<HeaderStyle Width="60px"></HeaderStyle>
									</asp:BoundColumn>
									<asp:BoundColumn DataField="addflag" HeaderText="Request Detail">
										<HeaderStyle Width="197px"></HeaderStyle>
									</asp:BoundColumn>
									<asp:BoundColumn DataField="Name" HeaderText="Requester">
										<HeaderStyle Width="100px"></HeaderStyle>
									</asp:BoundColumn>
									<asp:BoundColumn DataField="c_at" HeaderText="Request Date" DataFormatString="{0:d MMM, yyyy h:m:s}">
										<HeaderStyle Width="160px"></HeaderStyle>
									</asp:BoundColumn>
									<asp:BoundColumn DataField="timeline" HeaderText="Timeline to Complete" DataFormatString="{0:d MMM,yyyy}">
										<HeaderStyle Width="85px"></HeaderStyle>
									</asp:BoundColumn>
									<asp:BoundColumn HeaderText="Status">
										<HeaderStyle Width="70px"></HeaderStyle>
										<ItemStyle HorizontalAlign="Center"></ItemStyle>
									</asp:BoundColumn>
								</Columns>
								<PagerStyle HorizontalAlign="Center" ForeColor="Black" BackColor="#999999" Mode="NumericPages"></PagerStyle>
							</asp:datagrid></TD>
					</TR>
				</TABLE>
				<TABLE id="tabFamilyDetail" cellSpacing="0" cellPadding="3" width="750" align="center"
					border="1" runat="server">
					<TR>
						<TD class="OrangeFormTitle" colSpan="2">Family Queue List Request Detail</TD>
					</TR>
					<TR>
						<TD style="WIDTH: 261px" align="left"><STRONG>Request No.</STRONG></TD>
						<TD>
							<asp:Label id="Label27" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 261px" align="left"><STRONG>Request For:</STRONG></TD>
						<TD>
							<asp:Label id="Label28" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 261px" align="left"><STRONG>Employee Code:</STRONG></TD>
						<TD>
							<asp:Label id="Label29" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 261px" align="left"><STRONG>Employee Name:</STRONG></TD>
						<TD>
							<asp:Label id="Label30" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 261px" align="left"><STRONG>Designation :</STRONG></TD>
						<TD>
							<asp:Label id="Label31" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 261px" align="left"><STRONG>Department:</STRONG></TD>
						<TD>
							<asp:Label id="Label32" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 261px" align="left"><STRONG>Family Member Name:</STRONG></TD>
						<TD>
							<asp:Label id="Label33" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 261px" align="left"><STRONG>Relationship:</STRONG></TD>
						<TD>
							<asp:Label id="Label34" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 261px" align="left">
							<P class="MsoNormal"><STRONG>Marital status:</STRONG></P>
						</TD>
						<TD>
							<asp:Label id="Label35" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 261px" align="left">
							<P class="MsoNormal"><STRONG>Occupation</STRONG></P>
						</TD>
						<TD>
							<asp:Label id="Label36" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 261px" align="left"><STRONG>Dependant:</STRONG></TD>
						<TD>
							<asp:Label id="Label37" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 261px" align="left"><STRONG>Request Posted on:</STRONG></TD>
						<TD>
							<asp:Label id="Label40" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 261px" align="left"><STRONG>Timeline to Approved / Reject:</STRONG></TD>
						<TD>
							<asp:Label id="Label41" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 261px" align="left"><STRONG>Verification Criteria:</STRONG></TD>
						<TD>
							<asp:Label id="Label42" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 261px" align="left"><STRONG>Documents Required for Verification by 
								HR:</STRONG></TD>
						<TD>
							<asp:Label id="Label43" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 261px" align="left"><STRONG>Documents Required from TeamGeo member</STRONG></TD>
						<TD>
							<asp:Label id="Label44" runat="server"></asp:Label></TD>
					</TR>
					<TR>
						<TD style="WIDTH: 261px" align="left"><STRONG>View Attached Document:</STRONG></TD>
						<TD id="Td2" runat="server">
							<asp:Label id="Label2" runat="server" ForeColor="Blue" CssClass="hh">View</asp:Label></TD>
					</TR>
					<TR>
						<TD colSpan="2">
							<asp:Button id="Button4" runat="server" CssClass="button" Text="Accept"></asp:Button>
							<asp:Button id="Button3" runat="server" CssClass="button" Text="Reject"></asp:Button></TD>
					</TR>
					<TR>
						<TD id="Td3" colSpan="2" runat="server">
							<asp:Label id="Label1" runat="server"></asp:Label><BR>
							<asp:TextBox id="TextBox1" runat="server" Width="100%" CssClass="textbox" TextMode="MultiLine"
								Rows="4"></asp:TextBox><BR>
							<asp:Button id="Button2" runat="server" CssClass="button" Text="Submit" Enabled="False"></asp:Button>
							<asp:Button id="Button1" runat="server" CssClass="button" Text="Cancel"></asp:Button></TD>
					</TR>
				</TABLE>
			</asp:panel><asp:panel id="pnlTraining" Runat="server">
				<TABLE id="Table3" cellSpacing="0" cellPadding="2" width="750" align="center" border="0">
					<TR>
						<TD class="OrangeFormTitle">Training Queue List:</TD>
					</TR>
					<TR>
						<TD class="FormColor">
							<asp:datagrid id="dgTraining" runat="server" CellPadding="3" BackColor="White" BorderWidth="1px"
								BorderStyle="None" BorderColor="#999999" AutoGenerateColumns="False" Width="100%">
								<FooterStyle ForeColor="Black" BackColor="#CCCCCC"></FooterStyle>
								<SelectedItemStyle Font-Bold="True" ForeColor="White" BackColor="#008A8C"></SelectedItemStyle>
								<AlternatingItemStyle BackColor="Gainsboro"></AlternatingItemStyle>
								<ItemStyle ForeColor="Black" BackColor="#EEEEEE"></ItemStyle>
								<HeaderStyle Font-Bold="True" ForeColor="White" BackColor="#000084"></HeaderStyle>
								<Columns>
									<asp:ButtonColumn Text="Select" CommandName="Select">
										<HeaderStyle Width="40px"></HeaderStyle>
									</asp:ButtonColumn>
									<asp:BoundColumn DataField="reqid" HeaderText="Request #">
										<HeaderStyle Width="60px"></HeaderStyle>
									</asp:BoundColumn>
									<asp:BoundColumn DataField="addflag" HeaderText="Request Detail">
										<HeaderStyle Width="197px"></HeaderStyle>
									</asp:BoundColumn>
									<asp:BoundColumn DataField="Name" HeaderText="Requester">
										<HeaderStyle Width="100px"></HeaderStyle>
									</asp:BoundColumn>
									<asp:BoundColumn DataField="c_at" HeaderText="Request Date" DataFormatString="{0:d MMM, yyyy h:m:s}">
										<HeaderStyle Width="160px"></HeaderStyle>
									</asp:BoundColumn>
									<asp:BoundColumn DataField="timeline" HeaderText="Timeline to Complete" DataFormatString="{0:d MMM,yyyy}">
										<HeaderStyle Width="85px"></HeaderStyle>
									</asp:BoundColumn>
									<asp:BoundColumn HeaderText="Status">
										<HeaderStyle Width="70px"></HeaderStyle>
										<ItemStyle HorizontalAlign="Center"></ItemStyle>
									</asp:BoundColumn>
								</Columns>
								<PagerStyle HorizontalAlign="Center" ForeColor="Black" BackColor="#999999" Mode="NumericPages"></PagerStyle>
							</asp:datagrid></TD>
					</TR>
				</TABLE>
				<asp:panel id="pnlTrainingDetail" runat="server" Visible="False">
					<TABLE id="Table5" cellSpacing="0" cellPadding="3" width="750" align="center" border="1"
						runat="server">
						<TR>
							<TD class="OrangeFormTitle" colSpan="2">Trining Request Detail
								<asp:Label id="Label5" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" align="left"><STRONG>Request No.</STRONG></TD>
							<TD>
								<asp:Label id="Label54" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" align="left"><STRONG>Request For:</STRONG></TD>
							<TD>
								<asp:Label id="Label53" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" align="left"><STRONG>Employee Code:</STRONG></TD>
							<TD>
								<asp:Label id="Label52" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" align="left"><STRONG>Employee Name:</STRONG></TD>
							<TD>
								<asp:Label id="Label51" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" align="left"><STRONG>Designation :</STRONG></TD>
							<TD>
								<asp:Label id="Label50" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" align="left"><STRONG>Department:</STRONG></TD>
							<TD>
								<asp:Label id="Label49" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" align="left"><STRONG>Training Title</STRONG></TD>
							<TD>
								<asp:Label id="Label48" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" align="left"><STRONG>Training Field</STRONG></TD>
							<TD>
								<asp:Label id="Label47" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" align="left">
								<P class="MsoNormal"><STRONG>Training (Month Year)</STRONG></P>
							</TD>
							<TD>
								<asp:Label id="Label46" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" align="left">
								<P class="MsoNormal"><STRONG>Training Duration:</STRONG></P>
							</TD>
							<TD>
								<asp:Label id="Label45" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" align="left"><STRONG>Institute</STRONG></TD>
							<TD>
								<asp:Label id="Label39" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" align="left"><STRONG>Country</STRONG></TD>
							<TD>
								<asp:Label id="Label38" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" align="left"><STRONG>City</STRONG></TD>
							<TD>
								<asp:Label id="Label10" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" align="left"><STRONG>View Attached Document:</STRONG></TD>
							<TD id="Td6" runat="server">
								<asp:Label id="Label6" runat="server" ForeColor="Blue" CssClass="hh">View</asp:Label></TD>
						</TR>
						<TR>
							<TD colSpan="2">
								<asp:Button id="cmdAccTraining" runat="server" CssClass="button" Text="Accept"></asp:Button>
								<asp:Button id="cmdRejTraining" runat="server" CssClass="button" Text="Reject"></asp:Button></TD>
						</TR>
						<TR>
							<TD id="Td7" colSpan="2" runat="server">
								<asp:Label id="lblTrainingMsg" runat="server"></asp:Label><BR>
								<asp:TextBox id="txtTrainingMsg" runat="server" Width="100%" CssClass="textbox" TextMode="MultiLine"
									Rows="4"></asp:TextBox><BR>
								<asp:Button id="cmdSubmitTraining" runat="server" CssClass="button" Text="Submit" Enabled="False"></asp:Button>
								<asp:Button id="cmdCancelTraining" runat="server" CssClass="button" Text="Cancel"></asp:Button></TD>
						</TR>
					</TABLE>
				</asp:panel>
			</asp:panel><asp:panel id="pnlExp" runat="server">
				<TABLE id="Table6" cellSpacing="0" cellPadding="2" width="750" align="center" border="0">
					<TR>
						<TD class="OrangeFormTitle">Experience Queue List:</TD>
					</TR>
					<TR>
						<TD class="FormColor">
							<asp:datagrid id="dgExp" runat="server" CellPadding="3" BackColor="White" BorderWidth="1px" BorderStyle="None"
								BorderColor="#999999" AutoGenerateColumns="False" Width="100%">
								<FooterStyle ForeColor="Black" BackColor="#CCCCCC"></FooterStyle>
								<SelectedItemStyle Font-Bold="True" ForeColor="White" BackColor="#008A8C"></SelectedItemStyle>
								<AlternatingItemStyle BackColor="Gainsboro"></AlternatingItemStyle>
								<ItemStyle ForeColor="Black" BackColor="#EEEEEE"></ItemStyle>
								<HeaderStyle Font-Bold="True" ForeColor="White" BackColor="#000084"></HeaderStyle>
								<Columns>
									<asp:ButtonColumn Text="Select" CommandName="Select">
										<HeaderStyle Width="40px"></HeaderStyle>
									</asp:ButtonColumn>
									<asp:BoundColumn DataField="reqid" HeaderText="Request #">
										<HeaderStyle Width="60px"></HeaderStyle>
									</asp:BoundColumn>
									<asp:BoundColumn DataField="addflag" HeaderText="Request Detail">
										<HeaderStyle Width="197px"></HeaderStyle>
									</asp:BoundColumn>
									<asp:BoundColumn DataField="Name" HeaderText="Requester">
										<HeaderStyle Width="100px"></HeaderStyle>
									</asp:BoundColumn>
									<asp:BoundColumn DataField="c_at" HeaderText="Request Date" DataFormatString="{0:d MMM, yyyy h:m:s}">
										<HeaderStyle Width="160px"></HeaderStyle>
									</asp:BoundColumn>
									<asp:BoundColumn DataField="timeline" HeaderText="Timeline to Complete" DataFormatString="{0:d MMM,yyyy}">
										<HeaderStyle Width="85px"></HeaderStyle>
									</asp:BoundColumn>
									<asp:BoundColumn HeaderText="Status">
										<HeaderStyle Width="70px"></HeaderStyle>
										<ItemStyle HorizontalAlign="Center"></ItemStyle>
									</asp:BoundColumn>
								</Columns>
								<PagerStyle HorizontalAlign="Center" ForeColor="Black" BackColor="#999999" Mode="NumericPages"></PagerStyle>
							</asp:datagrid></TD>
					</TR>
				</TABLE>
				<asp:panel id="pnlExpDetail" runat="server" Visible="False">
					<TABLE id="Table7" cellSpacing="0" cellPadding="3" width="750" align="center" border="1"
						runat="server">
						<TR>
							<TD class="OrangeFormTitle" colSpan="2">Experience Request Detail
								<asp:Label id="lblExpID" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left"><STRONG>Request No:</STRONG></TD>
							<TD vAlign="top">
								<asp:Label id="lblReqID" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left"><STRONG>Request For:</STRONG></TD>
							<TD vAlign="top">
								<asp:Label id="Label68" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left"><STRONG>Employee Code:</STRONG></TD>
							<TD vAlign="top">
								<asp:Label id="Label67" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left"><STRONG>Employee Name:</STRONG></TD>
							<TD vAlign="top">
								<asp:Label id="Label66" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left"><STRONG>Designation :</STRONG></TD>
							<TD vAlign="top">
								<asp:Label id="Label65" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left"><STRONG>Department:</STRONG></TD>
							<TD vAlign="top">
								<asp:Label id="Label64" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left"><STRONG>Area of Experience:</STRONG></TD>
							<TD vAlign="top">
								<asp:Label id="Label63" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left"><STRONG>Company Name:</STRONG></TD>
							<TD vAlign="top">
								<asp:Label id="Label62" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left">
								<P class="MsoNormal"><STRONG>Position Held:</STRONG></P>
							</TD>
							<TD vAlign="top">
								<asp:Label id="Label61" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left">
								<P class="MsoNormal"><STRONG>Department:</STRONG></P>
							</TD>
							<TD vAlign="top">
								<asp:Label id="Label60" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left"><STRONG>Served From:</STRONG></TD>
							<TD vAlign="top">
								<asp:Label id="Label59" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left"><STRONG>Served Till:</STRONG></TD>
							<TD vAlign="top">
								<asp:Label id="Label58" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left"><STRONG>Reporting To:</STRONG></TD>
							<TD vAlign="top">
								<asp:Label id="Label57" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left"><STRONG>Number of Subordinate:</STRONG></TD>
							<TD vAlign="top">
								<asp:Label id="Label69" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left"><STRONG>Company Contact Person:</STRONG></TD>
							<TD vAlign="top">
								<asp:Label id="Label70" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left"><STRONG>Company Email Address:</STRONG></TD>
							<TD vAlign="top">
								<asp:Label id="Label71" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left"><STRONG>Company Address:</STRONG></TD>
							<TD vAlign="top">
								<asp:Label id="Label72" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left"><STRONG>Company Phone:</STRONG></TD>
							<TD vAlign="top">
								<asp:Label id="Label73" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left"><STRONG>Major Responsibilities:</STRONG></TD>
							<TD vAlign="top">
								<asp:Label id="Label74" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left"><STRONG>Achievements/Contribution 
									during employment:</STRONG></TD>
							<TD vAlign="top">
								<asp:Label id="Label75" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left"><STRONG>Salary:</STRONG></TD>
							<TD vAlign="top">
								<asp:Label id="Label76" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left"><STRONG>Mobile Entitlement:</STRONG></TD>
							<TD vAlign="top">
								<asp:Label id="Label77" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left"><STRONG>Fuel Entitlement:</STRONG></TD>
							<TD vAlign="top">
								<asp:Label id="Label78" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left"><STRONG>Car/Conveyance:</STRONG></TD>
							<TD vAlign="top">
								<asp:Label id="Label79" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left"><STRONG>Car Description:</STRONG></TD>
							<TD vAlign="top">
								<asp:Label id="Label80" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" vAlign="top" align="left"><STRONG>Reason for Leave:</STRONG></TD>
							<TD vAlign="top">
								<asp:Label id="Label81" runat="server"></asp:Label></TD>
						</TR>
						<TR>
							<TD style="WIDTH: 261px" align="left"><STRONG>Document:</STRONG></TD>
							<TD id="Td8" runat="server">
								<asp:Label id="Label56" runat="server" ForeColor="Blue" CssClass="hh">View</asp:Label></TD>
						</TR>
						<TR>
							<TD colSpan="2">
								<asp:Button id="Button12" runat="server" CssClass="button" Text="Accept"></asp:Button>
								<asp:Button id="Button11" runat="server" CssClass="button" Text="Reject"></asp:Button></TD>
						</TR>
						<TR>
							<TD id="Td9" colSpan="2" runat="server">
								<asp:Label id="Label55" runat="server"></asp:Label><BR>
								<asp:TextBox id="TextBox3" runat="server" Width="100%" CssClass="textbox" TextMode="MultiLine"
									Rows="4"></asp:TextBox><BR>
								<asp:Button id="Button10" runat="server" CssClass="button" Text="Submit" Enabled="False"></asp:Button>
								<asp:Button id="Button9" runat="server" CssClass="button" Text="Cancel"></asp:Button></TD>
						</TR>
					</TABLE>
				</asp:panel>
			</asp:panel><asp:panel id="pnlReOpen" runat="server" Visible="False">
				<TABLE id="Table8" cellSpacing="0" cellPadding="2" width="750" align="center" border="0">
					<TR>
						<TD class="OrangeFormTitle">Re-Open Grievance:</TD>
					</TR>
					<TR>
						<TD class="FormColor">
							<asp:datagrid id="dgReopen" runat="server" GridLines="Vertical" CellPadding="3" BackColor="White"
								BorderWidth="1px" BorderStyle="None" BorderColor="#999999" AutoGenerateColumns="False" Width="100%"
								Visible="False">
								<FooterStyle ForeColor="Black" BackColor="#CCCCCC"></FooterStyle>
								<SelectedItemStyle Font-Bold="True" ForeColor="White" BackColor="#008A8C"></SelectedItemStyle>
								<AlternatingItemStyle BackColor="Gainsboro"></AlternatingItemStyle>
								<ItemStyle ForeColor="Black" BackColor="#EEEEEE"></ItemStyle>
								<HeaderStyle Font-Bold="True" ForeColor="White" BackColor="#000084"></HeaderStyle>
								<Columns>
									<asp:TemplateColumn HeaderText="Re-Open">
										<ItemTemplate>
											<asp:LinkButton id="lblReOpen" runat="server" OnClick="OpenUp">Re-Open</asp:LinkButton>
										</ItemTemplate>
									</asp:TemplateColumn>
									<asp:BoundColumn DataField="rid" HeaderText="Request ID"></asp:BoundColumn>
									<asp:BoundColumn DataField="grievancetitle" HeaderText="Title"></asp:BoundColumn>
									<asp:BoundColumn Visible="False" DataField="grievancedetail" HeaderText="Detail"></asp:BoundColumn>
									<asp:BoundColumn DataField="requestby" HeaderText="Request by"></asp:BoundColumn>
									<asp:BoundColumn DataField="requestdate" HeaderText="Request on"></asp:BoundColumn>
									<asp:BoundColumn DataField="requesttimeline" HeaderText="Request Timeline"></asp:BoundColumn>
									<asp:BoundColumn Visible="False" DataField="sgid"></asp:BoundColumn>
									<asp:BoundColumn Visible="False" DataField="requisitecode"></asp:BoundColumn>
									<asp:TemplateColumn Visible="False" HeaderText="Timeline Status">
										<ItemTemplate>
											<asp:Label id="lblgStatus" runat="server"></asp:Label>
										</ItemTemplate>
									</asp:TemplateColumn>
									<asp:BoundColumn Visible="False" DataField="status"></asp:BoundColumn>
									<asp:BoundColumn Visible="False" DataField="sgrievance"></asp:BoundColumn>
									<asp:BoundColumn Visible="False" DataField="grievance"></asp:BoundColumn>
									<asp:BoundColumn DataField="requeststatus" HeaderText="Request Status"></asp:BoundColumn>
								</Columns>
								<PagerStyle HorizontalAlign="Center" ForeColor="Black" BackColor="#999999" Mode="NumericPages"></PagerStyle>
							</asp:datagrid></TD>
					</TR>
				</TABLE>
			</asp:panel>
			<asp:panel id="pnlccGrievance" runat="server" Visible="False">
      <TABLE id="Table9" cellSpacing="0" cellPadding="2" width="750" align="center" border="0">
					<TR>
						<TD class="OrangeFormTitle">CC&nbsp;Grievances:</TD>
					</TR>
					<TR>
						<TD class="FormColor">
							<asp:datagrid id="dgMyccGrievance" runat="server" GridLines="Vertical" CellPadding="3" BackColor="White"
								BorderWidth="1px" BorderStyle="None" BorderColor="#999999" AutoGenerateColumns="False" Width="100%"
								Visible="False">
								<FooterStyle ForeColor="Black" BackColor="#CCCCCC"></FooterStyle>
								<SelectedItemStyle Font-Bold="True" ForeColor="White" BackColor="#008A8C"></SelectedItemStyle>
								<AlternatingItemStyle BackColor="Gainsboro"></AlternatingItemStyle>
								<ItemStyle ForeColor="Black" BackColor="#EEEEEE"></ItemStyle>
								<HeaderStyle Font-Bold="True" ForeColor="White" BackColor="#000084"></HeaderStyle>
								<Columns>
									<asp:ButtonColumn Text="View" CommandName="Select"></asp:ButtonColumn>
									<asp:BoundColumn DataField="grievancetitle" HeaderText="Title"></asp:BoundColumn>
									<asp:BoundColumn Visible="False" DataField="grievancedetail" HeaderText="Detail"></asp:BoundColumn>
									<asp:BoundColumn DataField="requestby" HeaderText="Request by"></asp:BoundColumn>
									<asp:BoundColumn DataField="requestdate" HeaderText="Request on"></asp:BoundColumn>
									<asp:BoundColumn DataField="requesttimeline" HeaderText="Request Timeline"></asp:BoundColumn>
									<asp:BoundColumn Visible="False" DataField="rid"></asp:BoundColumn>
									<asp:BoundColumn Visible="False" DataField="sgid"></asp:BoundColumn>
									<asp:BoundColumn Visible="False" DataField="requisitecode"></asp:BoundColumn>
									<asp:TemplateColumn HeaderText="Timeline Status">
										<ItemTemplate>
											<asp:Label id="lblgStatus" runat="server"></asp:Label>
										</ItemTemplate>
									</asp:TemplateColumn>
									<asp:BoundColumn DataField="status"></asp:BoundColumn>
									<asp:BoundColumn Visible="False" DataField="sgrievance"></asp:BoundColumn>
									<asp:BoundColumn Visible="False" DataField="grievance"></asp:BoundColumn>
									<asp:BoundColumn Visible="False" DataField="requeststatus" HeaderText="Request Status"></asp:BoundColumn>
								</Columns>
								<PagerStyle HorizontalAlign="Center" ForeColor="Black" BackColor="#999999" Mode="NumericPages"></PagerStyle>
							</asp:datagrid>
							<asp:Panel id="pnlcView" runat="server">
								<BR>
								<TABLE id="Table10" cellSpacing="1" cellPadding="1" width="300" border="0">
									<TR>
										<TD><STRONG>Grievance Subject:&nbsp;</STRONG></TD>
										<TD>
											<asp:Label id="lblccTitle" runat="server"></asp:Label></TD>
									</TR>
									<TR>
										<TD><STRONG>Grievance Type:&nbsp;&nbsp;</STRONG></TD>
										<TD>
											<asp:Label id="lblccGrievanceType" runat="server"></asp:Label></TD>
									</TR>
									<TR>
										<TD><STRONG>Grievance Sub Type:</STRONG></TD>
										<TD>
											<asp:Label id="lblccGrievanceSubType" runat="server"></asp:Label></TD>
									</TR>
									<TR>
										<TD><STRONG>Grievance Detail:&nbsp;</STRONG></TD>
										<TD>
											<asp:Label id="txtccDetail" runat="server"></asp:Label></TD>
									</TR>
								</TABLE>
								<BR>
								<STRONG>Grievance Blog:<BR>
								</STRONG>
								<asp:DataGrid id="dgccBlog" runat="server" GridLines="Vertical" CellPadding="3" BackColor="White"
									BorderWidth="1px" BorderStyle="None" BorderColor="#999999" AutoGenerateColumns="False" Width="100%"
									Visible="False">
									<FooterStyle ForeColor="Black" BackColor="#CCCCCC"></FooterStyle>
									<SelectedItemStyle Font-Bold="True" ForeColor="White" BackColor="#008A8C"></SelectedItemStyle>
									<AlternatingItemStyle BackColor="Gainsboro"></AlternatingItemStyle>
									<ItemStyle ForeColor="Black" BackColor="#EEEEEE"></ItemStyle>
									<HeaderStyle Font-Bold="True" ForeColor="White" BackColor="#000084"></HeaderStyle>
									<Columns>
										<asp:BoundColumn DataField="comment" HeaderText="Comments"></asp:BoundColumn>
										<asp:BoundColumn DataField="sendon" HeaderText="Post on"></asp:BoundColumn>
										<asp:BoundColumn DataField="pcode" HeaderText="Pcode"></asp:BoundColumn>
										<asp:BoundColumn DataField="sendby" HeaderText="Posted by"></asp:BoundColumn>
										<asp:BoundColumn DataField="senderdesignation" HeaderText="Designation"></asp:BoundColumn>
										<asp:BoundColumn DataField="senderdepartment" HeaderText="Department"></asp:BoundColumn>
									</Columns>
									<PagerStyle HorizontalAlign="Center" ForeColor="Black" BackColor="#999999" Mode="NumericPages"></PagerStyle>
								</asp:DataGrid>
							</asp:Panel></TD>
					</TR>
				</TABLE>&nbsp;&nbsp; 
      </asp:panel></TD></TR>
			<tr>
				<td vAlign="middle" align="center" height="20">Copyright © 2005 Independent Media 
					Corporation <A href="http://www.geo.tv">www.geo.tv</A></td>
			</tr>
			</TBODY></TABLE></form>
	</body>
</HTML>
