using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;
namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for Policy.
	/// </summary>
	public class MyGrievance : System.Web.UI.Page
	{
		protected System.Web.UI.WebControls.ImageButton imgMyGrievance;
	    SqlConnection con;
		protected System.Web.UI.WebControls.Panel pnlMyGrievance;
		protected System.Web.UI.WebControls.DataGrid dgMyGrievance;
		protected System.Web.UI.WebControls.Button btnGrievanceSubmit;
		protected System.Web.UI.WebControls.TextBox txtComments;
		protected System.Web.UI.WebControls.Panel pnlgView;
		protected System.Web.UI.WebControls.DataGrid dgBlog;
		protected System.Web.UI.WebControls.Button btnCancel;
		protected System.Web.UI.WebControls.Label lblGrievanceSubType;
		protected System.Web.UI.WebControls.Label lblGrievanceType;
		protected System.Web.UI.WebControls.Label lblTitle;
		protected System.Web.UI.WebControls.Label txtpDetail;
		protected System.Web.UI.WebControls.Panel pnlWithdrawl;
		protected System.Web.UI.WebControls.DropDownList ddClosingCode;
		protected System.Web.UI.WebControls.Button btnSave;
		protected System.Web.UI.WebControls.Label lblClosingCode;
		protected System.Web.UI.WebControls.Panel Panel1;
		protected System.Web.UI.WebControls.ImageButton ImgMyAttendance;
		protected System.Web.UI.WebControls.ImageButton imgMyLeave;
		protected System.Web.UI.WebControls.ImageButton ibMySelf;
		protected System.Web.UI.WebControls.ImageButton ibSalary;
		protected System.Web.UI.WebControls.ImageButton imgPersonal;
		protected System.Web.UI.WebControls.ImageButton imgOrganogram;
		protected System.Web.UI.WebControls.Label lblEmpInfo;
		protected System.Web.UI.WebControls.Label lblPCode;
		protected System.Web.UI.WebControls.ImageButton imgTraining;
		protected System.Web.UI.WebControls.ImageButton imgEmp;
		protected System.Web.UI.WebControls.ImageButton imgFamily;
		protected System.Web.UI.WebControls.ImageButton imgEducation;
		protected System.Web.UI.WebControls.TextBox TextBox1;
		protected System.Web.UI.WebControls.TextBox txtX;
		protected System.Web.UI.WebControls.TextBox txtY;
		protected System.Web.UI.WebControls.Label lblmessage;
		protected System.Web.UI.WebControls.HyperLink hlMyExp;
		protected System.Web.UI.WebControls.HyperLink hlMyRequests;
		protected System.Web.UI.WebControls.Label lblRID;
		public static string userId="";
		/*private bool IsPageAccessAllowed()
		{
			
			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception )
			{
				Response.Redirect("Login.aspx");
			}

			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"View Policies")==true)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("Login.aspx");
				return false;
			}
		}*/



		private void Page_Load(object sender, System.EventArgs e)
		{   
			
			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			/*if(IsPageAccessAllowed())
			{
				
			  
			}
			else
			{
				Response.Redirect("ErrorPage.aspx");
			}*/
			if(!IsPostBack)
			{
			  //GetGrievance();
				this.pnlWithdrawl.Visible=false;
				this.pnlMyGrievance.Visible=true;
				GetMyGrievance();
				this.lblmessage.Visible=false;
				SetGrid(); 
				imgOrganogram.Attributes.Add("onclick","window.open('admin/organogram.htm','_blank','resizable=yes,menubar=no,scrollbars=yes,top=0,status=no');return false;");
				this.imgMyLeave.Attributes.Add("onclick","return OpenURL('MyLeaveBalance.aspx',650,300);");
				this.ImgMyAttendance.Attributes.Add("onclick","return OpenURL('MyAttendance.aspx',700,400);");
			    //GetEployeeCounter();
				//this.Image1.ImageUrl=@"employee\"+Session["user_id"].ToString()+".jpg";
				if(Request.QueryString.Count>0)
				{
				 this.lblPCode.Text=Request.QueryString[0].ToString();
				 this.lblEmpInfo.Text=Request.QueryString[1].ToString();
				 //this.lblDisName.Text=Request.QueryString[1].ToString();
				 }
			}
			SetGrid();
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.imgEmp.Click += new System.Web.UI.ImageClickEventHandler(this.imgEmp_Click);
			this.imgPersonal.Click += new System.Web.UI.ImageClickEventHandler(this.imgPersonal_Click);
			this.imgFamily.Click += new System.Web.UI.ImageClickEventHandler(this.imgFamily_Click);
			this.imgEducation.Click += new System.Web.UI.ImageClickEventHandler(this.imgEducation_Click);
			this.imgTraining.Click += new System.Web.UI.ImageClickEventHandler(this.imgTraining_Click);
			this.imgMyGrievance.Click += new System.Web.UI.ImageClickEventHandler(this.imgMyGrievance_Click);
			this.dgMyGrievance.SelectedIndexChanged += new System.EventHandler(this.dgMyGrievance_SelectedIndexChanged);
			this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
			this.btnGrievanceSubmit.Click += new System.EventHandler(this.btnGrievanceSubmit_Click);
			this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		
		public void GetMyGrievance()
		{
			try
			{
				con=new SqlConnection(Connection.ConnectionString);
				con.Open();
				SqlDataAdapter dr=new SqlDataAdapter("select e.grievancetitle,e.grievancedetail,e.requestdate,e.requesttimeline,e.rid,e.sgid,requeststatus=case e.requeststatus when 1 then 'Inprocess' when 2 then 'Cancelled' when 3 then 'Resolved' when 4 then 'Re-Open' when '5' then 'Interim Closed' end,Status=case e.newusermsg when 1 then 'You have an update' else ''end,gs.sgrievance,g.grievance from t_employeegrievancerequest e,t_grievancesubtype gs,t_grievance g where e.requisitecode='"+Session["user_id"].ToString()+"' and (e.requeststatus=1 or e.requeststatus=4) and e.sgid=gs.sid and gs.gid=g.gid order by e.requestdate",con);
				DataSet ds=new DataSet();
				dr.Fill(ds,"Record");
				ds.AcceptChanges();
				dr=new SqlDataAdapter("select e.grievancetitle,e.grievancedetail,e.requestdate,e.requesttimeline,e.rid,e.sgid,requeststatus=case e.requeststatus when 1 then 'Inprocess' when 2 then 'Cancelled' when 3 then 'Resolved' when 4 then 'Re-Open' when '5' then 'Interim Closed' end,Status=case e.newusermsg when 1 then 'You have an update' else ''end,gs.sgrievance,g.grievance from t_employeegrievancerequest e,t_grievancesubtype gs,t_grievance g where e.requisitecode='"+Session["user_id"].ToString()+"' and e.requeststatus=5 and e.sgid=gs.sid and gs.gid=g.gid order by e.requestdate",con);
				dr.Fill(ds,"Record");
				ds.AcceptChanges();
				dr=new SqlDataAdapter("select e.grievancetitle,e.grievancedetail,e.requestdate,e.requesttimeline,e.rid,e.sgid,requeststatus=case e.requeststatus when 1 then 'Inprocess' when 2 then 'Cancelled' when 3 then 'Resolved' when 4 then 'Re-Open' when '5' then 'Interim Closed' end,Status=case e.newusermsg when 1 then 'You have an update' else ''end,gs.sgrievance,g.grievance from t_employeegrievancerequest e,t_grievancesubtype gs,t_grievance g where e.requisitecode='"+Session["user_id"].ToString()+"' and e.requeststatus=3 and e.sgid=gs.sid and gs.gid=g.gid and datediff(d,e.requesthandledate,getdate()) <=7 order by e.requestdate",con);
				dr.Fill(ds,"Record");
				ds.AcceptChanges();
				this.dgMyGrievance.DataSource=ds;
				this.dgMyGrievance.DataBind();
				this.dgMyGrievance.Visible=true;
				con.Close();
				con.Dispose();
				SetGrid();
			}
			catch(Exception ex)
			{
			 Response.Write(ex.Message);
			}
		}

		private void dgMyGrievance_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			/*con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlTransaction trans=con.BeginTransaction();
			SqlCommand cmd=new SqlCommand("update t_employeegrievancerequest set requeststatus=2 where rid="+this.dgMyGrievance.Items[this.dgMyGrievance.SelectedIndex].Cells[7].Text+"",con);
			cmd.Transaction=trans;
			cmd.ExecuteNonQuery();
			trans.Commit();
			con.Close();
			con.Dispose();
            GetMyGrievance();
			this.lblmessage.Visible=true;
			this.lblmessage.Text="Grievance request has been cancelled";
			this.dgMyGrievance.SelectedIndex=-1;*/
			this.pnlWithdrawl.Visible=false;
			this.pnlgView.Visible=true;
			this.lblRID.Text=this.dgMyGrievance.Items[this.dgMyGrievance.SelectedIndex].Cells[7].Text;
			this.lblTitle.Text=this.dgMyGrievance.Items[this.dgMyGrievance.SelectedIndex].Cells[2].Text;
			this.txtpDetail.Text=this.dgMyGrievance.Items[this.dgMyGrievance.SelectedIndex].Cells[3].Text;
			GetComments(this.dgMyGrievance.Items[this.dgMyGrievance.SelectedIndex].Cells[7].Text);
			this.lblTitle.ToolTip=this.dgMyGrievance.Items[this.dgMyGrievance.SelectedIndex].Cells[7].Text;
			this.lblGrievanceType.Text=this.dgMyGrievance.Items[this.dgMyGrievance.SelectedIndex].Cells[11].Text;
			this.lblGrievanceSubType.Text=this.dgMyGrievance.Items[this.dgMyGrievance.SelectedIndex].Cells[10].Text;
			this.lblmessage.Visible=false;
			if(this.dgMyGrievance.Items[this.dgMyGrievance.SelectedIndex].Cells[6].Text =="Resolved" || this.dgMyGrievance.Items[this.dgMyGrievance.SelectedIndex].Cells[6].Text =="Interim Closed")
			{
				this.btnGrievanceSubmit.Visible=false;
			}
			else
			{
			  this.btnGrievanceSubmit.Visible=true;
			}
			string Val=this.dgMyGrievance.Items[this.dgMyGrievance.SelectedIndex].Cells[9].Text;
			if(Val !="&nbsp;")
			{
				con=new SqlConnection(Connection.ConnectionString);
				con.Open();
				SqlTransaction trans=con.BeginTransaction();
				SqlCommand cmd=new SqlCommand("update t_employeegrievancerequest set newusermsg=0 where rid="+this.lblTitle.ToolTip+"",con);
				cmd.Transaction=trans;
				cmd.ExecuteNonQuery();
				trans.Commit();
				con.Close();
				con.Dispose();
				GetMyGrievance();
			}
		}
		public void GetClosingCode()
		{
			this.ddClosingCode.Items.Clear();
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			ListItem itm0=new ListItem("Select--Closing Code","0");
			this.ddClosingCode.Items.Add(itm0);
			SqlCommand cmd=new SqlCommand("select * from t_grievanceclosingcode where isactive=1 and type=2",con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
				this.ddClosingCode.Items.Add(itm);
			}
			rd.Close();
			con.Close();
			con.Dispose();
			this.ddClosingCode.SelectedIndex=1;
		}
		public void GetComments(string ID)
		{
		 con=new SqlConnection(Connection.ConnectionString);
		 con.Open();
		 SqlDataAdapter dr=new SqlDataAdapter("select g.comment,e.pcode,e.name as sendby,g.senderdesignation,g.senderdepartment,g.sendon from t_grievanceblog g,t_employee e where e.pcode=g.sendby and rid="+ID+"order by g.sendon",con);
		 DataSet ds=new DataSet();
		 dr.Fill(ds,"Record");
		 dgBlog.DataSource=ds;
		 dgBlog.DataBind();
		 dgBlog.Visible=true;
		 con.Close();
		 con.Dispose();
		}
		private void dgMyGrievance_EditCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{
			
		}

		private void btnGrievanceSubmit_Click(object sender, System.EventArgs e)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			if(this.txtComments.Text.Length>0)
			{
				/*SqlCommand cmd=new SqlCommand("insert into t_grievanceblog(rid,comment,sendon,sendby) values(@rid,@comment,getdate(),@sendby)",con);
				SqlParameter _rid=new SqlParameter("@rid",SqlDbType.Int);
				SqlParameter _comment=new SqlParameter("@comment",SqlDbType.Text);
				SqlParameter _sendby=new SqlParameter("@sendby",SqlDbType.VarChar);
				_rid.Value=this.lblTitle.ToolTip;
				_comment.Value=this.txtComments.Text;
				_sendby.Value=Session["user_id"].ToString();
				cmd.Parameters.Add(_rid);
				cmd.Parameters.Add(_comment);
				cmd.Parameters.Add(_sendby);
				cmd.ExecuteNonQuery();*/
				SqlCommand cmd=new SqlCommand("select d.designation,dept.deptname,c.cityname from t_employee e,t_designation d,t_department dept,t_city c where e.desigid=d.desigid and dept.deptid=d.deptid and e.pcode='"+Session["user_id"].ToString()+"' and e.station=c.cityid",con);
				SqlDataReader rd=cmd.ExecuteReader();
				rd.Read();
				string designation=rd[0].ToString();
				string department=rd[1].ToString();
				string city=rd[2].ToString();
				rd.Close();
				cmd=new SqlCommand("insert into t_grievanceblog(rid,comment,sendon,sendby,senderdesignation,senderstation,senderdepartment) values(@rid,@comment,getdate(),@sendby,@senderdesignation,@senderstation,@senderdepartment)",con);
				SqlParameter _rid=new SqlParameter("@rid",SqlDbType.Int);
				SqlParameter _comment=new SqlParameter("@comment",SqlDbType.Text);
				SqlParameter _sendby=new SqlParameter("@sendby",SqlDbType.VarChar);
				SqlParameter _designation=new SqlParameter("@senderdesignation",SqlDbType.Text);
				SqlParameter _station=new SqlParameter("@senderstation",SqlDbType.Text);
				SqlParameter _department=new SqlParameter("@senderdepartment",SqlDbType.Text);
				_rid.Value=this.lblTitle.ToolTip;
				_comment.Value=this.txtComments.Text;
				_sendby.Value=Session["user_id"].ToString();
				_designation.Value=designation;
				_department.Value=department;
				_station.Value=city;
				cmd.Parameters.Add(_rid);
				cmd.Parameters.Add(_comment);
				cmd.Parameters.Add(_sendby);
				cmd.Parameters.Add(_designation);
				cmd.Parameters.Add(_station);
				cmd.Parameters.Add(_department);
				cmd.ExecuteNonQuery();
				cmd=new SqlCommand("update t_employeegrievancerequest set newspmsg=1 where rid="+this.lblTitle.ToolTip+"",con);
				cmd.ExecuteNonQuery();
			}
			con.Close();
			con.Dispose();
			GetComments(this.lblTitle.ToolTip);
			this.txtComments.Text="";
			this.lblmessage.Text="Your Message has been sent";
			this.lblmessage.Visible=true;
		}
		public void SetCancel(object sender,EventArgs e)
		{
		 this.pnlWithdrawl.Visible=true;
		 GetClosingCode();
		  LinkButton pLnk=(LinkButton)sender;
			for(int i=0;i<this.dgMyGrievance.Items.Count;i++)
			{
				LinkButton gLnk=(LinkButton)this.dgMyGrievance.Items[i].FindControl("lnkCancel");
				if(pLnk.Equals(gLnk))
				{
				 this.lblClosingCode.ToolTip=this.dgMyGrievance.Items[i].Cells[7].Text;
				}
			}
		}
		public void SetView(object sender,EventArgs e)
		{
		  LinkButton pLnk=(LinkButton)sender;
			for(int i=0;i<this.dgMyGrievance.Items.Count;i++)
			{
			 LinkButton gLnk=(LinkButton)this.dgMyGrievance.Items[i].FindControl("lnkView");
				if(pLnk.Equals(gLnk))
				{
					//Response.Write("Hello");
					con=new SqlConnection(Connection.ConnectionString);
					con.Open();
					SqlTransaction trans=con.BeginTransaction();
					SqlCommand cmd=new SqlCommand("select sgrievancetimeline,replyatrequest from t_grievancesubtype where sid="+this.dgMyGrievance.Items[i].Cells[8].Text+" and sgrievancetimeline is not null",con);
					cmd.Transaction=trans;
					SqlDataReader rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						int workingdays=Int32.Parse(rd[0].ToString());
						string timeline=DateTime.Now.Date.AddDays(workingdays).ToShortDateString();
					    rd.Close();
						cmd=new SqlCommand("update t_employeegrievancerequest set requeststatus=4,requestdate=getdate(),requesttimeline='"+timeline+"',closingcode=null where rid="+this.dgMyGrievance.Items[i].Cells[7].Text+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						//Bulletin.PostMessage("Grievance Request:"+this.dgMyGrievance.Items[i].Cells[2].Text,"Grievance Request:"+this.txtSubject.Text+" has been re-opened","Geo Raabta",7,2,"Geo Raabta",Session["user_id"].ToString(),con,trans);
						trans.Commit();
						con.Close();
						con.Dispose();
						GetMyGrievance();
						SetGrid();
						this.lblmessage.Visible=true;
						this.lblmessage.Text="Grievance request has been re-opened";
						this.dgMyGrievance.SelectedIndex=-1;
						this.pnlgView.Visible=false;
						return;
					}
					else
					{
						this.lblmessage.Visible=true;
						this.lblmessage.Text="Process flow not defined properly for your grievance. Request failed to send";
						trans.Rollback();
						return;
					}
				}
			}
		}
        
		public void SetGrid()
		{
			for(int i=0;i<this.dgMyGrievance.Items.Count;i++)
			{
				LinkButton gLnk=(LinkButton)this.dgMyGrievance.Items[i].FindControl("lnkView");
				LinkButton wLnk=(LinkButton)this.dgMyGrievance.Items[i].FindControl("lnkCancel");
				if(this.dgMyGrievance.Items[i].Cells[6].Text=="Resolved" || this.dgMyGrievance.Items[i].Cells[6].Text=="Interim Closed")
				{
					gLnk.Attributes.Add("onclick","return confirm('Are you sure to re-open your grievance request?');");
					gLnk.Text="Re-Open";
					wLnk.Visible=false;
				}
				else
				{
					if(this.dgMyGrievance.Items[i].Cells[6].Text=="Cancelled")
					{
					  wLnk.Visible=false;
					}
					else
					{
					 wLnk.Attributes.Add("onclick","return confirm('Are you sure to withdraw your grievance request?');");
					}
				 gLnk.Visible=false;
				 
				}
			}
		}

		private void btnCancel_Click(object sender, System.EventArgs e)
		{
			this.pnlgView.Visible=false;
			this.dgMyGrievance.SelectedIndex=-1;
			//SetGrid();
		}

		private void btnSave_Click(object sender, System.EventArgs e)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlTransaction trans=con.BeginTransaction();
			SqlCommand cmd=new SqlCommand("update t_employeegrievancerequest set requeststatus=2,closingcode="+this.ddClosingCode.SelectedItem.Value+" where rid="+this.lblClosingCode.ToolTip+"",con);
			cmd.Transaction=trans;
			cmd.ExecuteNonQuery();
			trans.Commit();
			con.Close();
			con.Dispose();
			GetMyGrievance();
			this.lblmessage.Visible=true;
			this.lblmessage.Text="Grievance request has been cancelled";
			this.dgMyGrievance.SelectedIndex=-1;
			con.Close();
			con.Dispose();
			this.pnlWithdrawl.Visible=false;
			SetGrid();
		}

		private void imgMyGrievance_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			this.pnlWithdrawl.Visible=false;
			this.pnlMyGrievance.Visible=true;
			GetMyGrievance();
			this.lblmessage.Visible=false;
			SetGrid(); 
		}

		private void imgEmployee_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("UserProfile.aspx?id=1");
		}

		private void imgPersonal_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("UserProfile.aspx?id=2");
		}
//		private void GetEployeeCounter()
//		{
//			con=new SqlConnection(Connection.ConnectionString);
//			con.Open();
//			SqlDataAdapter da = new SqlDataAdapter("select counter from t_systemuser where pcode='" + Session["user_id"].ToString() + "'",con);
//			DataSet ds = new DataSet();
//			da.Fill(ds,"Counting");
//			if (ds.Tables[0].Rows.Count>0)
//			{
//				lblCount.Text="<b>"+ds.Tables[0].Rows[0][0].ToString()+"</b>";
//			}
//			else
//			{
//				lblCount.Text=" zero ";
//			}
//			con.Close();
//			con.Dispose();
//		}

		private void ImageButton3_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("EmpFamily.aspx?pcode="+this.lblPCode.Text+"&name="+this.lblEmpInfo.Text);
		}

		private void ImageButton4_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("EmpEducation.aspx?pcode="+this.lblPCode.Text+"&name="+this.lblEmpInfo.Text);
		}

		private void imgTraining_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("EmpTraining.aspx?pcode="+this.lblPCode.Text+"&name="+this.lblEmpInfo.Text);
		}

		private void imgEmp_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("UserProfile.aspx?id=1");
		}

		private void imgFamily_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("empfamily.aspx");
		}

		private void imgEducation_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("empeducation.aspx");
		}
	}
}
