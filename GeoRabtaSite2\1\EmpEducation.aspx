<%@ Page language="c#" Codebehind="EmpEducation.aspx.cs" AutoEventWireup="false" Inherits="GeoRabtaSite.EmpEducation" %>
<%@ Register TagPrefix="ew" Namespace="eWorld.UI" Assembly="eWorld.UI" %>
<%@ Register TagPrefix="uc1" TagName="Organimeter" Src="Organimeter.ascx" %>
<%@ Register TagPrefix="uc1" TagName="MenuControl" Src="MenuControl.ascx" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Rabta :: User Profile</title>
		<meta content="text/html; charset=utf-8" http-equiv="Content-Type">
		<meta name="GENERATOR" content="Microsoft Visual Studio .NET 7.1">
		<meta name="vs_targetSchema" content="http://schemas.microsoft.com/intellisense/ie5">
		<LINK rel="stylesheet" type="text/css" href="StyleSheet1.css">
		<style type="text/css">.style2 { FONT-WEIGHT: bold; FONT-SIZE: 16pt; COLOR: #ffffff }
	A:link { COLOR: white }
		</style>
		<script src="jquery-1.2.6.js" type="text/javascript"></script>
		<script language="javascript" src="jquery.MetaData.js" type="text/javascript"></script>
		<script src="documentation.js" type="text/javascript"></script>
		<script language="javascript" src="jquery.MultiFile.js" type="text/javascript"></script>
		<script language="javascript" src="jquery.blockUI.js" type="text/javascript"></script>
		<script language="javascript">
String.prototype.trim = function() {
	return this.replace(/^\s+|\s+$/g,"");
}


function OpenURL(file,docw,doch)
{
var w=screen.width;
var h=screen.height;
var l=(w-docw)/2;
var t=(h-doch)/2;
var viewimageWin=window.open(file,"codewindow","toolbar=0,scrollbars=1,location=0,statusbar=0,menubar=0,resizable=0,width="+docw+",height="+doch+",left=" + l + ",top="+t);
return false;
}
function validateEdu()
{
	var txtDegree=document.getElementById("txtDegree");
	var txtOtherDegree=document.getElementById("txtOtherDegree");
	var txtInstitute=document.getElementById("txtInstitute");
	var txtOtherInstitute=document.getElementById("txtOtherInstitute");
	var txtResult=document.getElementById("txtResult");
	var txtOtherMajors=document.getElementById("txtOtherMajors");
	var DurationFrom=document.getElementById("DurationFrom");
	var DurationTo=document.getElementById("DurationTo");
	if(txtDegree.selectedIndex==0)
	{
		alert("Degree is required");
		txtDegree.focus();
		return false;
	}
	else if (txtDegree[txtDegree.selectedIndex].value=="-1")
	{
		if(txtOtherDegree.value.trim()=="")
		{
			alert("Degree is required");
			txtOtherDegree.focus();
			return false;
		}
	}

	if(txtInstitute.selectedIndex==0)
	{
		alert("Institute is required");
		txtInstitute.focus();
		return false;
	}
	else if (txtInstitute[txtInstitute.selectedIndex].value=="-1")
	{
		if(txtOtherInstitute.value=="")
		{
			alert("Institute is required");
			txtOtherInstitute.focus();
			return false;
		}
	}
	
	if(txtResult.selectedIndex==0)
	{
		alert("Result is required");
		txtResult.focus();
		return false;
	}
	
	if(txtOtherMajors.value=="")
	{
		alert("Please enter Education Major");
		txtOtherMajors.focus();
		return false;
	}
			
	if(DurationFrom.selectedIndex==0)
	{
		alert("'Duration From' is required");
		DurationFrom.focus();
		return false;
	}
			
	if(DurationTo.selectedIndex==0)
	{
		alert("'Duration To' is required");
		DurationTo.focus();
		return false;
	}
	var fileEdu=document.getElementById("fileEdu");
	if(fileEdu.value=="")
	{
		alert("Document is required, please select file for upload");
		return false;
	}
	return true;

}

	
function OpenURL(file,docw,doch)
{
	var w=screen.width;
	var h=screen.height;
	var l=(w-docw)/2;
	var t=(h-doch)/2;
	var viewimageWin=window.open(file,"codewindow","toolbar=0,scrollbars=1,location=0,statusbar=0,menubar=0,resizable=0,width="+docw+",height="+doch+",left=" + l + ",top="+t);
	return false;
}

function window_onload() 
{
//	d=document.getElementById('info');
//	d.style.visibility='hidden';
	document.body.scrollTop =document.getElementById("txtX").value;
	document.body.scrollLeft=document.getElementById("txtY").value;
	SetOther('txtDegree','-1','divOtherDegree');
	SetOther('txtInstitute','-1','divOtherInstitute');
}

function ShowOther(ddl,val,div,txtOther)
{
	var ddl=document.getElementById(ddl);
	showMe(div,ddl[ddl.selectedIndex].value==val)
	var txtO=document.getElementById(txtOther);
	if(ddl[ddl.selectedIndex].value==val)
		txtO.focus();

}
function SetOtherControl(ddl, val, oc)
{
	var ddl=document.getElementById(ddl);
	var oc=document.getElementById(oc);
	if(ddl && oc)
	{
	if(ddl[ddl.selectedIndex].value==val)
		{
		oc.disabled=true;
		oc.selectedIndex=0;
		}
	else
		oc.disabled=false;
	}
}

function SetOther(ddl,val,div)
{
	var ddl=document.getElementById(ddl);
	if(ddl)
		showMe(div,ddl[ddl.selectedIndex].value==val)

}

function showMe (it, status) {
//alert(it);
//alert(status);
var vis = (status) ? "block" : "none";
document.getElementById(it).style.display = vis;
}

function displayDiv()
{
	d=document.getElementById('info');
	d.style.visibility='visible';
	d.style.left=window.event.x+document.body.scrollLeft+10;
	d.style.top=window.event.y+document.body.scrollTop+15;
	var val=document.getElementById('TextBox1');
	d.innerHTML=val.value;
}


function window_onscroll() 
{
	document.getElementById("txtX").value=document.body.scrollTop;
	document.getElementById("txtY").value=document.body.scrollLeft;
}


		</script>
	</HEAD>
	<body onscroll="return window_onscroll()" oncontextmenu="return false" language="javascript"
		onselectstart="return false" ondrag="return false" onload="return window_onload()"
		bottomMargin="0" background="images\bg.jpg" leftMargin="0" rightMargin="0" bgProperties="fixed"
		topMargin="0">
		<script type="text/javascript" src="wz_tooltip.js"></script>
		<form id="myForm" runat="server">
			<table id="Table1" border="0" cellSpacing="0" cellPadding="0" width="1004" height="100%">
				<tr>
					<td style="HEIGHT: 200px">
						<OBJECT id="Shockwaveflash1" codeBase="http://fpdownload.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,0,0"
							height="200" width="1004" align="middle" classid="clsid:d27cdb6e-ae6d-11cf-96b8-444553540000"
							VIEWASTEXT>
							<PARAM NAME="_cx" VALUE="26564">
							<PARAM NAME="_cy" VALUE="5292">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="top.swf">
							<PARAM NAME="Src" VALUE="top.swf">
							<PARAM NAME="WMode" VALUE="Transparent">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="sameDomain">
							<PARAM NAME="Scale" VALUE="ExactFit">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="0066FF">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="top.swf" width="1004" height="200" align="middle" quality="high" wmode="transparent"
								bgcolor="#0066ff" scale="exactfit" allowscriptaccess="sameDomain" type="application/x-shockwave-flash"
								pluginspage="http://www.macromedia.com/go/getflashplayer" />
						</OBJECT>
					</td>
				</tr>
				<tr>
					<td vAlign="top" align="center">
						<table id="Table2" border="0" cellSpacing="0" cellPadding="0" width="100%">
							<tr>
								<td style="WIDTH: 175px" vAlign="top" align="center"><uc1:menucontrol id="MenuControl1" runat="server"></uc1:menucontrol></td>
								<td style="WIDTH: 650px; BACKGROUND-COLOR: #ffffff" vAlign="top" borderColor="white"
									align="left">
									<table style="BACKGROUND-COLOR: white" border="0" cellSpacing="0" cellPadding="2" width="100%"
										align="center">
										<tr>
											<td bgColor="#004477" height="65" background="images/PanelTop.jpg">
												<table border="0" cellSpacing="0" cellPadding="4" width="650">
													<tr>
														<td style="WIDTH: 40px"></td>
														<td><span class="style2">User Profile</span>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td>
												<asp:imagebutton id="ImageButton1" runat="server" ImageUrl="images\employee.gif" CausesValidation="False"></asp:imagebutton>
												<asp:imagebutton id="ImageButton2" runat="server" ImageUrl="images\personal.gif" CausesValidation="False"></asp:imagebutton>
												<asp:imagebutton id="ImageButton3" runat="server" ImageUrl="images\family.gif" CausesValidation="False"></asp:imagebutton>
												<asp:imagebutton id="ImageButton4" runat="server" ImageUrl="images\education.gif" CausesValidation="False"></asp:imagebutton>
												<asp:imagebutton id="imgTraining" runat="server" ImageUrl="images\training.gif"></asp:imagebutton>
												<asp:HyperLink id="hlMyExp" runat="server" ImageUrl="images/MyExperience.gif" NavigateUrl="EmpExperience.aspx"
													BorderStyle="None" BorderWidth="0px">HyperLink</asp:HyperLink>
												<asp:imagebutton id="ImageButton5" runat="server" ImageUrl="buttons/myorganogram.gif" CausesValidation="False"></asp:imagebutton>
												<asp:imagebutton id="ibSalary" runat="server" ImageUrl="images/payslip.gif" CausesValidation="False"></asp:imagebutton>
												<asp:imagebutton id="ibMySelf" runat="server" ImageUrl="Images\MeMyself.gif" CausesValidation="False"
													Visible="False"></asp:imagebutton>
												<asp:imagebutton id="imgMyLeave" runat="server" ImageUrl="images\MyLeaveBalance.gif" CausesValidation="False"></asp:imagebutton>
												<asp:imagebutton id="ImgMyAttendance" runat="server" ImageUrl="images\MyAttendance.gif" CausesValidation="False"></asp:imagebutton>
												<asp:imagebutton id="imgMyGrievance" runat="server" ImageUrl="images\mygrievance.gif"></asp:imagebutton>
												<asp:HyperLink id="hlMyRequests" runat="server" ImageUrl="images/myrequests.gif" NavigateUrl="MyRequest.aspx"
													BorderStyle="None" BorderWidth="0px">My Requests</asp:HyperLink>
												<asp:panel id="Panel1" runat="server" BackImageUrl="images\tabstrip.jpg" Height="10px" Width="100%"></asp:panel>
												<asp:Label id="lblPCode" runat="server" Visible="False"></asp:Label>
												<asp:Label id="lblEmpInfo" runat="server" Visible="False"></asp:Label><BR>
												<uc1:Organimeter id="Organimeter1" runat="server"></uc1:Organimeter><br>
												<asp:panel id="pnlEducation" runat="server">
													<TABLE class="FormColor1" id="Table17" cellSpacing="0" cellPadding="3" width="100%" border="0">
														<TR>
															<TD class="PanelTitle">Educational Information
															</TD>
														</TR>
														<TR>
															<TD class="FormColor1">
																<asp:DataGrid id="dgEducation" runat="server" BorderWidth="1px" Width="100%" AllowSorting="True"
																	AutoGenerateColumns="False" BorderColor="Tan" CellPadding="2" BackColor="LightGoldenrodYellow"
																	ForeColor="Black">
																	<FooterStyle BackColor="Tan"></FooterStyle>
																	<SelectedItemStyle ForeColor="GhostWhite" BackColor="DarkSlateBlue"></SelectedItemStyle>
																	<AlternatingItemStyle BackColor="PaleGoldenrod"></AlternatingItemStyle>
																	<HeaderStyle Font-Bold="True" BackColor="Tan"></HeaderStyle>
																	<Columns>
																		<asp:BoundColumn Visible="False" DataField="eduinfoid"></asp:BoundColumn>
																		<asp:BoundColumn DataField="degree" SortExpression="degree" HeaderText="Degree"></asp:BoundColumn>
																		<asp:BoundColumn DataField="institute" SortExpression="institute" HeaderText="Institute"></asp:BoundColumn>
																		<asp:BoundColumn Visible="False" DataField="durationfrom" SortExpression="durationfrom" HeaderText="Duration From"
																			DataFormatString="{0:d}"></asp:BoundColumn>
																		<asp:BoundColumn Visible="False" DataField="durationto" SortExpression="durationto" HeaderText="Duration To"
																			DataFormatString="{0:d}"></asp:BoundColumn>
																		<asp:BoundColumn DataField="result" SortExpression="result" HeaderText="Result"></asp:BoundColumn>
																		<asp:BoundColumn DataField="majors" SortExpression="majors" HeaderText="Majors"></asp:BoundColumn>
																		<asp:ButtonColumn Text="Select" CommandName="Select"></asp:ButtonColumn>
																		<asp:BoundColumn Visible="False" DataField="eduinfoid"></asp:BoundColumn>
																	</Columns>
																	<PagerStyle HorizontalAlign="Center" ForeColor="DarkSlateBlue" BackColor="PaleGoldenrod"></PagerStyle>
																</asp:DataGrid>
																<asp:Label id="lblMsg" runat="server" ForeColor="White" Font-Bold="True" Font-Size="Small">Your Request has been successfully posted...</asp:Label></TD>
														</TR>
														<TR>
															<TD class="FormColor1">
																<asp:LinkButton id="lbAddNewEdu" runat="server">Add New Education Info</asp:LinkButton></TD>
														</TR>
														<TR>
															<TD class="FormColor1">
																<asp:DataGrid id="dgEducationReq" runat="server" BorderWidth="1px" Visible="False" Width="100%"
																	AllowSorting="True" AutoGenerateColumns="False" BorderColor="Tan" CellPadding="2" BackColor="LightGoldenrodYellow"
																	ForeColor="Black">
																	<FooterStyle BackColor="Tan"></FooterStyle>
																	<SelectedItemStyle ForeColor="GhostWhite" BackColor="DarkSlateBlue"></SelectedItemStyle>
																	<AlternatingItemStyle BackColor="PaleGoldenrod"></AlternatingItemStyle>
																	<HeaderStyle Font-Bold="True" BackColor="Tan"></HeaderStyle>
																	<Columns>
																		<asp:BoundColumn Visible="False" DataField="reqid"></asp:BoundColumn>
																		<asp:BoundColumn Visible="False" DataField="eduinfoid"></asp:BoundColumn>
																		<asp:BoundColumn DataField="degree" SortExpression="degree" HeaderText="Degree"></asp:BoundColumn>
																		<asp:BoundColumn DataField="institute" SortExpression="institute" HeaderText="Institute"></asp:BoundColumn>
																		<asp:BoundColumn Visible="False" DataField="durationfrom" SortExpression="durationfrom" HeaderText="Duration From"
																			DataFormatString="{0:d}"></asp:BoundColumn>
																		<asp:BoundColumn Visible="False" DataField="durationto" SortExpression="durationto" HeaderText="Duration To"
																			DataFormatString="{0:d}"></asp:BoundColumn>
																		<asp:BoundColumn DataField="result" SortExpression="result" HeaderText="Result"></asp:BoundColumn>
																		<asp:BoundColumn DataField="majors" SortExpression="majors" HeaderText="Majors"></asp:BoundColumn>
																		<asp:BoundColumn Visible="False" DataField="eduinfoid"></asp:BoundColumn>
																		<asp:BoundColumn DataField="AddFlag" HeaderText="Request For"></asp:BoundColumn>
																		<asp:TemplateColumn>
																			<ItemTemplate>
																				<asp:LinkButton ID="lbCancelEdu" runat="server" ForeColor="Black" OnClick="CancelEduReq">Cancel Request</asp:LinkButton>
																			</ItemTemplate>
																		</asp:TemplateColumn>
																	</Columns>
																	<PagerStyle HorizontalAlign="Center" ForeColor="DarkSlateBlue" BackColor="PaleGoldenrod"></PagerStyle>
																</asp:DataGrid></TD>
														</TR>
													</TABLE>
													<TABLE id="Table18" cellSpacing="0" cellPadding="3" width="100%" border="0">
													</TABLE>
												</asp:panel><asp:panel id="Panel3" runat="server">
													<TABLE id="updateEducation" cellSpacing="0" cellPadding="0" width="100%" align="center"
														border="0" runat="server">
														<TR>
															<TD class="FormColor1" id="dropDown" vAlign="middle" align="left" colSpan="4" runat="server">
																<TABLE id="Table19" cellSpacing="0" cellPadding="3" width="100%" border="0">
																	<TR>
																		<TD class="PanelTitle">Update Educational Information:
																			<asp:Label id="lblEduToolTip" style="CURSOR: hand" runat="server" ForeColor="Yellow" Font-Bold="True"
																				Font-Size="Small" Font-Underline="True">?</asp:Label></TD>
																	</TR>
																</TABLE>
																<TABLE id="Table8" cellSpacing="0" cellPadding="2" width="100%" border="0">
																	<TR>
																		<TD class="FormColor1" style="WIDTH: 196px" vAlign="top" align="right"><STRONG>
																				<asp:Label id="lbleduinfoid" runat="server" Visible="False"></asp:Label>Degree: </STRONG>
																		</TD>
																		<TD class="FormColor1" vAlign="top" align="left">
																			<asp:DropDownList id="txtDegree" runat="server" Width="350px" CssClass="textbox">
																				<asp:ListItem Value="0">Select--Degree</asp:ListItem>
																			</asp:DropDownList><BR>
																			<DIV id="divOtherDegree" style="DISPLAY: none">Other Degree:<BR>
																				<asp:TextBox id="txtOtherDegree" runat="server" Width="350px" CssClass="textbox" MaxLength="100"></asp:TextBox></DIV>
																		</TD>
																	</TR>
																	<TR>
																		<TD class="FormColor2" style="WIDTH: 196px" vAlign="top" align="right"><STRONG>Institute:</STRONG>
																		</TD>
																		<TD class="FormColor2" vAlign="top" align="left">
																			<asp:DropDownList id="txtInstitute" runat="server" Width="350px" CssClass="textbox">
																				<asp:ListItem Value="0">Select--Institute</asp:ListItem>
																			</asp:DropDownList><BR>
																			<DIV id="divOtherInstitute" style="DISPLAY: none">Other Institute:<BR>
																				<asp:TextBox id="txtOtherInstitute" runat="server" Width="350px" CssClass="textbox" MaxLength="100"></asp:TextBox></DIV>
																		</TD>
																	</TR>
																	<TR>
																		<TD class="FormColor1" style="WIDTH: 196px" vAlign="top" align="right"><STRONG>Result:</STRONG>
																		</TD>
																		<TD class="FormColor1" vAlign="top" align="left">
																			<asp:DropDownList id="txtResult" runat="server" Width="350px" CssClass="textbox">
																				<asp:ListItem Value="0">Select--Results</asp:ListItem>
																				<asp:ListItem Value="A+ Grade">A+ Grade</asp:ListItem>
																				<asp:ListItem Value="A Grade">A Grade</asp:ListItem>
																				<asp:ListItem Value="B Grade">B Grade</asp:ListItem>
																				<asp:ListItem Value="C Grade">C Grade</asp:ListItem>
																				<asp:ListItem Value="D Grade">D Grade</asp:ListItem>
																				<asp:ListItem Value="F Grade">F Grade</asp:ListItem>
																				<asp:ListItem Value="2.1">2.1</asp:ListItem>
																				<asp:ListItem Value="2.2">2.2</asp:ListItem>
																				<asp:ListItem Value="2.3">2.3</asp:ListItem>
																				<asp:ListItem Value="2.4">2.4</asp:ListItem>
																				<asp:ListItem Value="2.5">2.5</asp:ListItem>
																				<asp:ListItem Value="2.6">2.6</asp:ListItem>
																				<asp:ListItem Value="2.7">2.7</asp:ListItem>
																				<asp:ListItem Value="2.8">2.8</asp:ListItem>
																				<asp:ListItem Value="2.9">2.9</asp:ListItem>
																				<asp:ListItem Value="3.0">3.0</asp:ListItem>
																				<asp:ListItem Value="3.1">3.1</asp:ListItem>
																				<asp:ListItem Value="3.2">3.2</asp:ListItem>
																				<asp:ListItem Value="3.3">3.3</asp:ListItem>
																				<asp:ListItem Value="3.4">3.4</asp:ListItem>
																				<asp:ListItem Value="3.5">3.5</asp:ListItem>
																				<asp:ListItem Value="3.6">3.6</asp:ListItem>
																				<asp:ListItem Value="3.7">3.7</asp:ListItem>
																				<asp:ListItem Value="3.8">3.8</asp:ListItem>
																				<asp:ListItem Value="3.9">3.9</asp:ListItem>
																				<asp:ListItem Value="4.0">4.0</asp:ListItem>
																				<asp:ListItem Value="Passed">Passed</asp:ListItem>
																				<asp:ListItem Value="Fail">Fail</asp:ListItem>
																				<asp:ListItem Value="Result Awaited">Result Awaited</asp:ListItem>
																			</asp:DropDownList></TD>
																	</TR>
																	<TR>
																		<TD class="FormColor2" style="WIDTH: 196px" vAlign="top" align="right"><STRONG>Majors:</STRONG>
																		</TD>
																		<TD class="FormColor2" vAlign="top" align="left">
																			<asp:TextBox id="txtOtherMajors" runat="server" Width="350px" CssClass="textbox" MaxLength="50"></asp:TextBox></TD>
																	</TR>
																	<TR>
																		<TD class="FormColor1" style="WIDTH: 196px" vAlign="top" align="right"><STRONG>Duration 
																				From:</STRONG>
																		</TD>
																		<TD class="FormColor1" vAlign="top" align="left">
																			<asp:DropDownList id="DurationFrom" runat="server" Width="350px" CssClass="textbox">
																				<asp:ListItem Value="0">Select--Start Date</asp:ListItem>
																			</asp:DropDownList></TD>
																	</TR>
																	<TR>
																		<TD class="FormColor2" style="WIDTH: 196px" vAlign="top" align="right"><STRONG>Duration 
																				To:</STRONG>
																		</TD>
																		<TD class="FormColor2" vAlign="top" align="left">
																			<asp:DropDownList id="DurationTo" runat="server" Width="350px" CssClass="textbox">
																				<asp:ListItem Value="0">Select--End Date</asp:ListItem>
																			</asp:DropDownList></TD>
																	</TR>
																	<TR>
																		<TD class="FormColor1" style="WIDTH: 196px" vAlign="top" align="right"><STRONG>Distinction:</STRONG>
																		</TD>
																		<TD class="FormColor1" vAlign="top" align="left">
																			<asp:TextBox id="txtAchievements" runat="server" Width="350px" CssClass="TextBox" MaxLength="200"
																				TextMode="MultiLine" Rows="3"></asp:TextBox></TD>
																	</TR>
																	<TR>
																		<TD class="FormColor1" style="WIDTH: 196px" vAlign="top" align="right"><STRONG>Attached 
																				Document:</STRONG>
																		</TD>
																		<TD class="FormColor1" vAlign="top" align="left"><INPUT class="TextBox" oncontextmenu="return false" onkeypress="return false" id="fileEdu"
																				style="WIDTH: 350px" onbeforeeditfocus="return false" type="file" name="FileToUpload" runat="server">
																		</TD>
																	</TR>
																	<TR>
																		<TD class="FormColor1" vAlign="top" align="left" colSpan="2">
																			<asp:Button id="btnEdNew" runat="server" Width="72px" Text="Save"></asp:Button>
																			<asp:Button id="btnEduDel" runat="server" Visible="False" Width="72px" Text="Delete"></asp:Button>
																			<asp:Button id="btnECancel" runat="server" CausesValidation="False" Width="72px" Text="Cancel"></asp:Button>
																			<asp:Label id="lblInvisiblity" runat="server" CssClass="Invisible"></asp:Label></TD>
																	</TR>
																</TABLE>
															</TD>
														</TR>
													</TABLE>
												</asp:panel><br>
												<asp:textbox style="VISIBILITY: hidden" id="TextBox1" runat="server"></asp:textbox><asp:textbox style="VISIBILITY: hidden" id="txtX" runat="server" Width="24px"></asp:textbox><asp:textbox style="VISIBILITY: hidden" id="txtY" runat="server" Width="24px"></asp:textbox></td>
										</tr>
									</table>
								</td>
								<td style="WIDTH: 180px" vAlign="top" align="center"></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td class="Fotter" height="20" align="center">Copyright © 2005 Independent Media 
						Corporation www.geo.tv<br>
					</td>
				</tr>
			</table>
		</form>
	</body>
</HTML>
