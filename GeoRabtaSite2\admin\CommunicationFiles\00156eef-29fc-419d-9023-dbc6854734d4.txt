<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Untitled Page</title>

    <script src="js/jquery.js" type="text/javascript"></script>

    <script src="js/jquery-ui.js" type="text/javascript"></script>

    <link href="js/jquery-ui.css" rel="stylesheet" type="text/css" />

    <script type="text/javascript">
        $(document).ready(function() {
            $("#accordion").accordion();
            $("#tabs").tabs();
            $("#Div1").tabs();
        });
    </script>

</head>
<body>
    <form id="form1" runat="server">
    <asp:Button ID="cndToggle" runat="server" Text="Toggle" 
        onclick="cndToggle_Click" />
    <!-- Accordion -->
    <h2 class="demoHeaders">
        Accordion</h2>
    <div id="accordion">
        <h3>
            <a href="#">Section 1</a></h3>
        <div>
            <h2 class="demoHeaders">
                Tabs</h2>
            <div id="tabs">
                <ul>
                    <li><a href="#tabs-1">First</a></li>
                    <li><a href="#tabs-2">Second</a></li>
                    <li><a href="#tabs-3">Third</a></li>
                </ul>
                <div id="tabs-1">
                    Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor
                    incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
                    exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</div>
                <div id="tabs-2">
                    Phasellus mattis tincidunt nibh. Cras orci urna, blandit id, pretium vel, aliquet
                    ornare, felis. Maecenas scelerisque sem non nisl. Fusce sed lorem in enim dictum
                    bibendum.</div>
                <div id="tabs-3">
                    Nam dui erat, auctor a, dignissim quis, sollicitudin eu, felis. Pellentesque nisi
                    urna, interdum eget, sagittis et, consequat vestibulum, lacus. Mauris porttitor
                    ullamcorper augue.</div>
            </div>
        </div>
        <h3 id="hObv" runat="server">
            <a href="#">Section 2</a></h3>
        <div id="dObv" runat="server">
            <div id="Div1">
                <ul>
                    <li><a href="#tabs-1">First</a></li>
                    <li><a href="#tabs-2">Second</a></li>
                    <li><a href="#tabs-3">Third</a></li>
                </ul>
                <div id="Div2">
                    Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor
                    incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud
                    exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</div>
                <div id="Div3">
                    Phasellus mattis tincidunt nibh. Cras orci urna, blandit id, pretium vel, aliquet
                    ornare, felis. Maecenas scelerisque sem non nisl. Fusce sed lorem in enim dictum
                    bibendum.</div>
                <div id="Div4">
                    Nam dui erat, auctor a, dignissim quis, sollicitudin eu, felis. Pellentesque nisi
                    urna, interdum eget, sagittis et, consequat vestibulum, lacus. Mauris porttitor
                    ullamcorper augue.</div>
            </div>
        </div>
        <h3>
            <a href="#">Section 3</a></h3>
        <div>
            <p>
                Nam enim risus, molestie et, porta ac, aliquam ac, risus. Quisque lobortis. Phasellus
                pellentesque purus in massa. Aenean in pede. Phasellus ac libero ac tellus pellentesque
                semper. Sed ac felis. Sed commodo, magna quis lacinia ornare, quam ante aliquam
                nisi, eu iaculis leo purus venenatis dui.
            </p>
            <ul>
                <li>List item one</li>
                <li>List item two</li>
                <li>List item three</li>
            </ul>
        </div>
    </div>
    </form>
</body>
</html>
