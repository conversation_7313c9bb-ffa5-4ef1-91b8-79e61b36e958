using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;


namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for EmpExperience.
	/// </summary>
	public class EmpExperience : System.Web.UI.Page
	{
		protected System.Web.UI.WebControls.TextBox txtX;
		protected System.Web.UI.WebControls.Panel pnlExp;
		protected System.Web.UI.WebControls.Panel pnlExpDetail;
		protected System.Web.UI.WebControls.LinkButton lbAddExp;
		protected System.Web.UI.HtmlControls.HtmlTable Table23;
		protected System.Web.UI.WebControls.DataGrid dgExperienceReq;
		protected System.Web.UI.WebControls.Button cmdCancelExp;
		protected System.Web.UI.WebControls.Button cmdSaveExp;
		protected System.Web.UI.WebControls.TextBox txtResonToLeave;
		protected System.Web.UI.WebControls.TextBox txtCarDescription;
		protected System.Web.UI.WebControls.DropDownList ddlCar;
		protected System.Web.UI.WebControls.TextBox txtPetrolEnt;
		protected System.Web.UI.WebControls.DropDownList ddlMobileCur;
		protected System.Web.UI.WebControls.TextBox txtMobileEnt;
		protected System.Web.UI.WebControls.DropDownList ddlSalarCur;
		protected System.Web.UI.WebControls.TextBox txtLastSalary;
		protected System.Web.UI.WebControls.TextBox txtAchivements;
		protected System.Web.UI.WebControls.TextBox txtMajorResponsibilities;
		protected System.Web.UI.WebControls.TextBox txtOrganizationPhone;
		protected System.Web.UI.WebControls.TextBox txtOrganizationAddress;
		protected System.Web.UI.WebControls.TextBox txtOrganizationEmail;
		protected System.Web.UI.WebControls.TextBox txtContactPerson;
		protected System.Web.UI.WebControls.DropDownList ddlSubordinates;
		protected System.Web.UI.WebControls.TextBox txtReportingTo;
		protected System.Web.UI.WebControls.DropDownList ddlServedTillYear;
		protected System.Web.UI.WebControls.DropDownList ddlServedTillMonth;
		protected System.Web.UI.WebControls.DropDownList ddlServedFromYear;
		protected System.Web.UI.WebControls.DropDownList ddlServedFromMonth;
		protected System.Web.UI.WebControls.Label lblExpID;
		protected System.Web.UI.WebControls.TextBox txtDepartment2;
		protected System.Web.UI.WebControls.TextBox txtPositionHeld;
		protected System.Web.UI.WebControls.TextBox txtCompanyName;
		protected System.Web.UI.WebControls.DropDownList ddlAreaOfExperience;
		protected System.Web.UI.WebControls.TextBox txtY;
		protected System.Web.UI.WebControls.DataGrid dgExperience;
		protected System.Web.UI.WebControls.TextBox txtOtherAreaOfExp;
		protected System.Web.UI.HtmlControls.HtmlInputFile fileExp;
		protected System.Web.UI.WebControls.Label lblPCode;
		protected System.Web.UI.WebControls.Label lblEmpInfo;
		protected System.Web.UI.WebControls.Label lblTrainingMsg;
		protected System.Web.UI.WebControls.Label lblEduToolTip;
		protected System.Web.UI.WebControls.Panel Panel1;
		protected System.Web.UI.WebControls.HyperLink hlMyRequests;
		protected System.Web.UI.WebControls.ImageButton imgMyGrievance;
		protected System.Web.UI.WebControls.ImageButton ImgMyAttendance;
		protected System.Web.UI.WebControls.ImageButton imgMyLeave;
		protected System.Web.UI.WebControls.ImageButton ibMySelf;
		protected System.Web.UI.WebControls.ImageButton ibSalary;
		protected System.Web.UI.WebControls.ImageButton ImageButton5;
		protected System.Web.UI.WebControls.HyperLink hlMyExp;
		protected System.Web.UI.WebControls.ImageButton imgTraining;
		protected System.Web.UI.WebControls.ImageButton ImageButton4;
		protected System.Web.UI.WebControls.ImageButton ImageButton3;
		protected System.Web.UI.WebControls.ImageButton ImageButton2;
		protected System.Web.UI.WebControls.ImageButton ImageButton1;
		private SqlConnection conn;
		string expCode="42";
	

		private string GetReplyMsg(string type, string wfid)
		{
			string sql="SELECT "+type+" FROM dbo.t_fieldsmgt WHERE (f_id = "+wfid+")";
			SqlConnection conn2=new SqlConnection(Connection.ConnectionString);
			conn2.Open();
			SqlDataAdapter da=new SqlDataAdapter(sql,conn2);
			DataSet ds=new DataSet();
			da.Fill(ds);
			conn2.Close();
			conn2.Dispose();
			return ds.Tables[0].Rows[0][type].ToString();
		}

		public string GetTooltip(int ID)
		{
			SqlConnection con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select tooltip from t_fieldsmgt where f_id="+ID+"",con);
			string message="";
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				message=rd[0].ToString();	
			}
			message+="<br>";
			message=HttpUtility.HtmlEncode(message);
			message=message.Replace("\r\n","<br/>");
			rd.Close();
			cmd=new SqlCommand("select d.documentname from t_documentsrequired d,t_fieldmgtdocEmp f where f.documentbyemployee=d.d_id and f.f_id="+ID+"",con);
			rd=cmd.ExecuteReader();
			message+="<ul>";
			while(rd.Read())
			{
				//message+="<STRONG>/STRONG> "+rd[0].ToString()+"<br>";
				message+="<li>"+HttpUtility.HtmlEncode(rd[0].ToString()).Replace("\r\n","<br/>")+"</li>";
			}
			message+="</ul>";
			rd.Close();
			con.Close();
			//Response.Write(message);
			return message;
		}


		private void Page_Load(object sender, System.EventArgs e)
		{
			lblTrainingMsg.Visible=false;

			conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			if(!IsPostBack)
			{

				ibMySelf.Attributes.Add("onclick","window.open('myinfo.aspx','MyProfile','toolbar=no,statusbar=no,addressbar=no,scrollbars=yes,resizable=no,width=700,height=650');return false;");
				lblEduToolTip.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Education</font></b><br/><br/>"+GetTooltip(38)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				lblEduToolTip.Attributes.Add("onmouseout","UnTip()");

				try
				{
					System.IO.Directory.CreateDirectory(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()));
				}
				catch
				{
				}
				ResetExpForm();
				UpdateExpGrid();
				FillYears();
				FillDDL(ddlAreaOfExperience,"cv_ExperinceFields","ExpField","ExpFieldID","1=1","Select Area of Experince",conn);
				ddlAreaOfExperience.Items.Add(new ListItem("Other","-1"));
				ddlAreaOfExperience.Attributes.Add("onchange","HH()");
				ddlServedTillMonth.Attributes.Add("onchange","SetOtherControl('ddlServedTillMonth','-1','ddlServedTillYear')");
				
				ImageButton5.Attributes.Add("onclick","window.open('admin/organogram.htm','_blank','resizable=yes,menubar=no,scrollbars=yes,top=0,status=no');return false;");
				this.imgMyLeave.Attributes.Add("onclick","return OpenURL('MyLeaveBalance.aspx',650,300);");
				this.ImgMyAttendance.Attributes.Add("onclick","return OpenURL('MyAttendance.aspx',700,400);");
				//this.Image1.ImageUrl=@"employee\"+Session["user_id"].ToString()+".jpg";
				//if(Request.QueryString.Count>0)
				//{
				try
				{
					this.lblPCode.Text=Request.QueryString[0].ToString();
					this.lblEmpInfo.Text=Request.QueryString[1].ToString();
				}
				catch(Exception)
				{

				}

			}
		}



		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.ImageButton1.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton1_Click);
			this.ImageButton2.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton2_Click);
			this.ImageButton3.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton3_Click);
			this.ImageButton4.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton4_Click);
			this.imgTraining.Click += new System.Web.UI.ImageClickEventHandler(this.imgTraining_Click);
			this.imgMyGrievance.Click += new System.Web.UI.ImageClickEventHandler(this.imgMyGrievance_Click);
			this.dgExperience.SelectedIndexChanged += new System.EventHandler(this.dgExperience_SelectedIndexChanged);
			this.lbAddExp.Click += new System.EventHandler(this.lbAddExp_Click);
			this.dgExperienceReq.SelectedIndexChanged += new System.EventHandler(this.dgExperienceReq_SelectedIndexChanged);
			this.cmdSaveExp.Click += new System.EventHandler(this.cmdSaveExp_Click);
			this.cmdCancelExp.Click += new System.EventHandler(this.cmdCancelExp_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		private void dgExperience_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			lblExpID.Text=dgExperience.SelectedItem.Cells[0].Text;
			string sql=" SELECT ExpID, PCode, AreaofExperience, AreaOfExperienceOther, CompanyName, Position, Department, ServedFromMonth, ServedFromYear, " +
				" ServedTillMonth, ServedTillYear, ReportingTo, Subordinate, OrganizationAddress, OrganizationPhone, ContactPerson, OrganizationEmail, " +
				" MajorResponsibilities, Achivements, LastSalary, MobileEntitlement, Petrol, CarConveyance, CarDescription, ResonToLeave, cdate, SalarySymb, " +
				" MobileSymb " +
				" FROM dbo.cv_Experince " +
				" WHERE (ExpID = "+lblExpID.Text+") ";
			
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			SetDDL(ddlAreaOfExperience,ds.Tables[0].Rows[0]["AreaofExperience"].ToString());
			SetText(ds,txtOtherAreaOfExp,"AreaOfExperienceOther");
			SetText(ds,txtCompanyName,"CompanyName");
			SetText(ds,txtPositionHeld,"Position");
			SetText(ds,txtDepartment2,"Department");
			SetDDL(ddlServedFromMonth,ds.Tables[0].Rows[0]["ServedFromMonth"].ToString());
			SetDDL(ddlServedFromYear,ds.Tables[0].Rows[0]["ServedFromYear"].ToString());
			SetDDL(ddlServedTillMonth,ds.Tables[0].Rows[0]["ServedTillMonth"].ToString());
			SetDDL(ddlServedTillYear,ds.Tables[0].Rows[0]["ServedTillYear"].ToString());
			SetText(ds,txtReportingTo,"ReportingTo");
			SetDDL(ddlSubordinates,ds.Tables[0].Rows[0]["Subordinate"].ToString());
			SetText(ds,txtContactPerson,"ContactPerson");
			SetText(ds,txtOrganizationEmail,"OrganizationEmail");
			SetText(ds,txtOrganizationAddress,"OrganizationAddress");
			SetText(ds,txtOrganizationPhone,"OrganizationPhone");
			SetText(ds,txtMajorResponsibilities,"MajorResponsibilities");
			SetText(ds,txtAchivements,"Achivements");
			SetText(ds,txtLastSalary,"LastSalary");
			SetDDL(ddlSalarCur,ds.Tables[0].Rows[0]["SalarySymb"].ToString());
			SetText(ds,txtMobileEnt,"MobileEntitlement");
			SetDDL(ddlMobileCur,ds.Tables[0].Rows[0]["MobileSymb"].ToString());
			SetText(ds,txtPetrolEnt,"Petrol");
			SetDDL(ddlCar,ds.Tables[0].Rows[0]["CarConveyance"].ToString());
			SetText(ds,txtCarDescription,"CarDescription");
			SetText(ds,txtResonToLeave,"ResonToLeave");
			pnlExp.Visible=false;
			pnlExpDetail.Visible=true;
			cmdSaveExp.Text="Update";
			cmdSaveExp.Attributes.Clear();
			if(IsExpReqExist(Session["user_id"].ToString(),lblExpID.Text))
			{
				cmdSaveExp.Attributes.Add("onclick","return confirm('A request for this record already exist, are you sure post it again?');");
			}

		}


		public void DeleteExp(object sender,EventArgs e)
		{
			string replyatrequest=GetReplyMsg("replyatrequest",expCode);
			SqlConnection con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlTransaction tran=con.BeginTransaction();

			LinkButton rLink=(LinkButton)sender;
			for(int i=0;i<dgExperience.Items.Count;i++)
			{
				LinkButton dLink=(LinkButton)dgExperience.Items[i].FindControl("lbDeleteExp");
				if(dLink.Equals(rLink))
				{
					string id=dgExperience.Items[i].Cells[0].Text;

					//lblExpID.Text=dgExperience.SelectedItem.Cells[0].Text;
					string sql=" SELECT ExpID, PCode, AreaofExperience, AreaOfExperienceOther, CompanyName, Position, Department, ServedFromMonth, ServedFromYear, " +
						" ServedTillMonth, ServedTillYear, ReportingTo, Subordinate, OrganizationAddress, OrganizationPhone, ContactPerson, OrganizationEmail, " +
						" MajorResponsibilities, Achivements, LastSalary, MobileEntitlement, Petrol, CarConveyance, CarDescription, ResonToLeave, cdate, SalarySymb, " +
						" MobileSymb " +
						" FROM dbo.cv_Experince " +
						" WHERE (ExpID = "+id+") ";
			
					SqlDataAdapter da=new SqlDataAdapter(sql,con);
					da.SelectCommand.Transaction=tran;
					da.SelectCommand.Transaction=tran;
					DataSet ds=new DataSet();
					da.Fill(ds);
					SetDDL(ddlAreaOfExperience,ds.Tables[0].Rows[0]["AreaofExperience"].ToString());
//					if(ddlAreaOfExperience.SelectedValue=="-1")
//					{
//						lblOtherAreaOfExp.Visible=true;
//						txtOtherAreaOfExp.Visible=true;
//					}
//					else
//					{
//						lblOtherAreaOfExp.Visible=false;
//						txtOtherAreaOfExp.Visible=false;
//					}
					SetText(ds,txtOtherAreaOfExp,"AreaOfExperienceOther");
					SetText(ds,txtCompanyName,"CompanyName");
					SetText(ds,txtPositionHeld,"Position");
					SetText(ds,txtDepartment2,"Department");
					SetDDL(ddlServedFromMonth,ds.Tables[0].Rows[0]["ServedFromMonth"].ToString());
					SetDDL(ddlServedFromYear,ds.Tables[0].Rows[0]["ServedFromYear"].ToString());
					SetDDL(ddlServedTillMonth,ds.Tables[0].Rows[0]["ServedTillMonth"].ToString());
					SetDDL(ddlServedTillYear,ds.Tables[0].Rows[0]["ServedTillYear"].ToString());
					SetText(ds,txtReportingTo,"ReportingTo");
					SetDDL(ddlSubordinates,ds.Tables[0].Rows[0]["Subordinate"].ToString());
					SetText(ds,txtContactPerson,"ContactPerson");
					SetText(ds,txtOrganizationEmail,"OrganizationEmail");
					SetText(ds,txtOrganizationAddress,"OrganizationAddress");
					SetText(ds,txtOrganizationPhone,"OrganizationPhone");
					SetText(ds,txtMajorResponsibilities,"MajorResponsibilities");
					SetText(ds,txtAchivements,"Achivements");
					SetText(ds,txtLastSalary,"LastSalary");
					SetDDL(ddlSalarCur,ds.Tables[0].Rows[0]["SalarySymb"].ToString());
					SetText(ds,txtMobileEnt,"MobileEntitlement");
					SetDDL(ddlMobileCur,ds.Tables[0].Rows[0]["MobileSymb"].ToString());
					SetText(ds,txtPetrolEnt,"Petrol");
					SetDDL(ddlCar,ds.Tables[0].Rows[0]["CarConveyance"].ToString());
					SetText(ds,txtCarDescription,"CarDescription");
					SetText(ds,txtResonToLeave,"ResonToLeave");
					//pnlExp.Visible=false;
					//pnlExpDetail.Visible=true;
					//cmdSaveExp.Text="Update";


					sql="insert into cv_ExperinceReq(ExpID,PCode,AreaofExperience,AreaOfExperienceOther,CompanyName,Position,Department,ServedFromMonth,ServedFromYear,ServedTillMonth,ServedTillYear,ReportingTo,Subordinate,OrganizationAddress,OrganizationPhone,ContactPerson,OrganizationEmail,MajorResponsibilities,Achivements,LastSalary,MobileEntitlement,Petrol,CarConveyance,CarDescription,ResonToLeave,cdate,SalarySymb,MobileSymb,c_at,Addflag,wfid,AppFlag)"+
						" values (@ExpID,@PCode,@AreaofExperience,@AreaOfExperienceOther,@CompanyName,@Position,@Department,@ServedFromMonth,@ServedFromYear,@ServedTillMonth,@ServedTillYear,@ReportingTo,@Subordinate,@OrganizationAddress,@OrganizationPhone,@ContactPerson,@OrganizationEmail,@MajorResponsibilities,@Achivements,@LastSalary,@MobileEntitlement,@Petrol,@CarConveyance,@CarDescription,@ResonToLeave,getdate(),@SalarySymb,@MobileSymb,getdate(),@Addflag,@wfid,@AppFlag)";

					SqlCommand cmd=new SqlCommand(sql,con);
					cmd.Transaction=tran;

					cmd.Parameters.Add(new SqlParameter("@PCode",Session["user_id"].ToString()));
					cmd.Parameters.Add(new SqlParameter("@AreaofExperience",ddlAreaOfExperience.SelectedValue));
					cmd.Parameters.Add(new SqlParameter("@AreaOfExperienceOther",txtOtherAreaOfExp.Text.Trim()));
					cmd.Parameters.Add(new SqlParameter("@CompanyName",txtCompanyName.Text.Trim()));
					cmd.Parameters.Add(new SqlParameter("@Position",txtPositionHeld.Text.Trim()));
					cmd.Parameters.Add(new SqlParameter("@Department",txtDepartment2.Text.Trim()));
					cmd.Parameters.Add(new SqlParameter("@ServedFromMonth",ddlServedFromMonth.SelectedValue));
					cmd.Parameters.Add(new SqlParameter("@ServedFromYear",ddlServedFromYear.SelectedValue));

					cmd.Parameters.Add(new SqlParameter("@ServedTillMonth",ddlServedTillYear.SelectedValue));
					if(ddlServedTillMonth.SelectedValue=="-1")
						cmd.Parameters.Add(new SqlParameter("@ServedTillYear",DBNull.Value));
					else
						cmd.Parameters.Add(new SqlParameter("@ServedTillYear",ddlServedTillYear.SelectedValue));
					
					cmd.Parameters.Add(new SqlParameter("@ReportingTo",txtReportingTo.Text.Trim()));
					cmd.Parameters.Add(new SqlParameter("@Subordinate",ddlSubordinates.SelectedValue));
					cmd.Parameters.Add(new SqlParameter("@OrganizationAddress",txtOrganizationAddress.Text.Trim()));
					cmd.Parameters.Add(new SqlParameter("@OrganizationPhone",txtOrganizationPhone.Text.Trim()));
					cmd.Parameters.Add(new SqlParameter("@ContactPerson",txtContactPerson.Text.Trim()));
					cmd.Parameters.Add(new SqlParameter("@OrganizationEmail",txtOrganizationEmail.Text.Trim()));
					cmd.Parameters.Add(new SqlParameter("@MajorResponsibilities",txtMajorResponsibilities.Text.Trim()));
					cmd.Parameters.Add(new SqlParameter("@Achivements",txtAchivements.Text.Trim()));
					cmd.Parameters.Add(new SqlParameter("@LastSalary",txtLastSalary.Text.Trim()));
					cmd.Parameters.Add(new SqlParameter("@MobileEntitlement",txtMobileEnt.Text.Trim()));
					cmd.Parameters.Add(new SqlParameter("@Petrol",txtPetrolEnt.Text.Trim()));
					cmd.Parameters.Add(new SqlParameter("@CarConveyance",ddlCar.SelectedValue));
					cmd.Parameters.Add(new SqlParameter("@CarDescription",txtCarDescription.Text.Trim()));
					cmd.Parameters.Add(new SqlParameter("@ResonToLeave",txtResonToLeave.Text.Trim()));
					cmd.Parameters.Add(new SqlParameter("@SalarySymb",ddlSalarCur.SelectedValue));
					cmd.Parameters.Add(new SqlParameter("@MobileSymb",ddlMobileCur.SelectedValue));
			
					cmd.Parameters.Add(new SqlParameter("@wfid",expCode));
					cmd.Parameters.Add(new SqlParameter("@AppFlag","2"));
					SqlParameter _expid=new SqlParameter("@ExpID",SqlDbType.Int);

					SqlCommand cmd2=new SqlCommand("update cv_ExperinceReq set appflag=4, AppRejDate=getdate(), AppRejBy='"+Session["user_id"].ToString()+"' where ExpID="+id+" and AppFlag=2 and pcode='"+Session["user_id"].ToString()+"'",con);
					cmd2.Transaction=tran;

					cmd2.ExecuteNonQuery();
					_expid.Value=id;
					cmd.Parameters.Add(_expid);
					cmd.Parameters.Add(new SqlParameter("@Addflag","3"));
					
					cmd.ExecuteNonQuery();
					Bulletin.PostReqMessage(con,tran,replyatrequest,"GEO Raabta",Session["user_id"].ToString(),1,"Experience","cv_ExperinceReq");
					tran.Commit();
					ResetExpForm();
					lbAddExp.Visible=true;
					pnlExp.Visible=false;
					UpdateExpGrid();
					ResetExpForm();

					UpdateExpGrid();
					lblTrainingMsg.Visible=true;
					break;
				}
			}
		}

		private void SetPos(DropDownList ddl, string val)
		{
			for(int j=0;j<ddl.Items.Count;j++)
			{
				string e=ddl.Items[j].Value;
				if(e==val)
				{
					ddl.SelectedIndex=j;
					break;
				}
			}
		}

		private void dgExperience_EditCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{

			lblExpID.Text=e.Item.Cells[0].Text;
			string sql=" SELECT ExpID, PCode, AreaofExperience, AreaOfExperienceOther, CompanyName, Position, Department, ServedFromMonth, ServedFromYear, "+
				" ServedTillMonth, ServedTillYear, ReportingTo, Subordinate, OrganizationAddress, OrganizationPhone, ContactPerson, OrganizationEmail, " +
				" MajorResponsibilities, Achivements, LastSalary, MobileEntitlement, Petrol, CarConveyance, CarDescription, ResonToLeave, SalarySymb, MobileSymb " +
				" FROM dbo.cv_Experince " +
				" WHERE (ExpID ="+lblExpID.Text+") ";

			lbAddExp.Visible=false;
			pnlExp.Visible=true;

			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			
			//ddlAreaOfExperience
			SetPos(ddlAreaOfExperience,ds.Tables[0].Rows[0]["AreaofExperience"].ToString());

			if(ddlAreaOfExperience.SelectedValue=="-1")
			{
				//lblOtherAreaOfExp.Visible=true;
				//txtOtherAreaOfExp.Visible=true;
				txtOtherAreaOfExp.Text=ds.Tables[0].Rows[0]["AreaOfExperienceOther"].ToString();
			}
			else
			{
				//lblOtherAreaOfExp.Visible=false;
				//txtOtherAreaOfExp.Visible=false;
				txtOtherAreaOfExp.Text="";
			}

			// txtCompanyName
			txtCompanyName.Text=ds.Tables[0].Rows[0]["CompanyName"].ToString();
			txtPositionHeld.Text=ds.Tables[0].Rows[0]["Position"].ToString();
			txtDepartment2.Text=ds.Tables[0].Rows[0]["Department"].ToString();
			ddlServedFromMonth.SelectedIndex=int.Parse(ds.Tables[0].Rows[0]["ServedFromMonth"].ToString());
			//ddlServedFromYear
			SetPos(ddlServedFromYear,ds.Tables[0].Rows[0]["ServedFromYear"].ToString());
			//ddlServedTillMonth
			SetPos(ddlServedTillMonth,ds.Tables[0].Rows[0]["ServedTillMonth"].ToString());
			if(ddlServedTillMonth.SelectedValue=="-1")
			{
				ddlServedTillYear.Enabled=false;
				ddlServedTillYear.SelectedIndex=0;
			}
			else
			{
				ddlServedTillYear.Enabled=true;
				SetPos(ddlServedTillYear,ds.Tables[0].Rows[0]["ServedTillYear"].ToString());
			}
			txtReportingTo.Text=ds.Tables[0].Rows[0]["ReportingTo"].ToString();
			SetPos(ddlSubordinates,ds.Tables[0].Rows[0]["Subordinate"].ToString());
			txtOrganizationAddress.Text=ds.Tables[0].Rows[0]["OrganizationAddress"].ToString();
			txtOrganizationPhone.Text=ds.Tables[0].Rows[0]["OrganizationPhone"].ToString();
			txtContactPerson.Text=ds.Tables[0].Rows[0]["ContactPerson"].ToString();
			txtOrganizationEmail.Text=ds.Tables[0].Rows[0]["OrganizationEmail"].ToString();
			txtMajorResponsibilities.Text=ds.Tables[0].Rows[0]["MajorResponsibilities"].ToString();
			txtAchivements.Text=ds.Tables[0].Rows[0]["Achivements"].ToString();
			txtLastSalary.Text=ds.Tables[0].Rows[0]["LastSalary"].ToString();
			txtMobileEnt.Text=ds.Tables[0].Rows[0]["MobileEntitlement"].ToString();
			txtPetrolEnt.Text=ds.Tables[0].Rows[0]["Petrol"].ToString();
			SetPos(ddlCar,ds.Tables[0].Rows[0]["CarConveyance"].ToString());
			txtCarDescription.Text=ds.Tables[0].Rows[0]["CarDescription"].ToString();
			txtResonToLeave.Text=ds.Tables[0].Rows[0]["ResonToLeave"].ToString();
			SetPos(ddlSalarCur,ds.Tables[0].Rows[0]["SalarySymb"].ToString());
			SetPos(ddlMobileCur,ds.Tables[0].Rows[0]["MobileSymb"].ToString());
			cmdSaveExp.Text="Update";


		}

		private bool ExpReqExit(string expid)
		{
			SqlDataAdapter da=new SqlDataAdapter("select reqid from cv_ExperinceReq where ExpID="+expid+" and AddFlag in (1,2,3) and AppFlag=2",conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			if(ds.Tables[0].Rows.Count>0)
				return true;
			else
				return false;
		}

		private bool IsExpReqExist(string pcode, string expid)
		{
			string sql=" SELECT reqid " +
				" FROM dbo.cv_ExperinceReq " +
				" WHERE (AppFlag = 2) AND (ExpID = "+expid+") AND (PCode = '"+pcode+"') ";
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			if(ds.Tables[0].Rows.Count>0)
				return true;
			else
				return false;

		}

		private void UpdateExpGrid()
		{

			string sql="SELECT TOP 100 PERCENT dbo.cv_Experince.ExpID, dbo.cv_Experince.PCode, dbo.cv_Experince.Position, dbo.cv_Experince.CompanyName, " +
				" dbo.Month_Name(dbo.cv_Experince.ServedFromMonth) + ' ' + CAST(dbo.cv_Experince.ServedFromYear AS varchar) AS ServedFrom, " +
				" CASE ServedTillMonth WHEN - 1 THEN 'Still Working' ELSE (dbo.Month_Name(dbo.cv_Experince.ServedTillMonth) " +
				" + ' ' + CAST(dbo.cv_Experince.ServedTillYear AS varchar)) END AS ServedTill, " +
				" CASE AreaofExperience WHEN - 1 THEN AreaOfExperienceOther ELSE " +
				" (SELECT ExpField " +
				" FROM dbo.cv_ExperinceFields " +
				" WHERE (ExpFieldID = AreaofExperience)) END AS AreaofExp " +
				" FROM dbo.cv_Experince INNER JOIN " +
				" dbo.cv_Months ON dbo.cv_Experince.ServedFromMonth = dbo.cv_Months.MonthID " +
				" WHERE dbo.cv_Experince.PCode = '"+Session["user_id"].ToString()+"' and isactive=1";

			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			dgExperience.DataSource=ds.Tables[0].DefaultView;
			dgExperience.DataBind();
			for(int j=0;j<dgExperience.Items.Count;j++)
			{
				if(ExpReqExit(dgExperience.Items[j].Cells[0].Text))
				{
					LinkButton lb=(LinkButton)dgExperience.Items[j].FindControl("lbDeleteExp");
					lb.Attributes.Add("onclick","return confirm('Are you sure to delete this record?');");
				}
				else
				{
					LinkButton lb=(LinkButton)dgExperience.Items[j].FindControl("lbDeleteExp");
					lb.Attributes.Add("onclick","return confirm('A request already exist for this record, Are you sure post this request for delete?');");
				}
			}
			//AssignConfim(dgExperience,"lbDeleteExp","Are you sure to delete this record?");
			for(int j=0;j<dgExperience.Items.Count;j++)
			{
				string pcode=Session["user_id"].ToString();
				string expid=dgExperience.Items[j].Cells[0].Text;
				LinkButton lb=(LinkButton)dgExperience.Items[j].FindControl("lbDeleteExp");

				if(IsExpReqExist(pcode,expid))
				{
					lb.Attributes.Add("onclick","return confirm('A previous request exist for this record, are you sure resend it for removal?');");
				}
				else
				{
					lb.Attributes.Add("onclick","return confirm('Are you sure to remvoe this record?');");
				}
			}

			sql=" SELECT TOP (100) PERCENT req.c_at as reqdate, req.reqid, req.ExpID, req.PCode, req.Position, req.CompanyName, dbo.Month_Name(req.ServedFromMonth) " +
				" + ' ' + CAST(req.ServedFromYear AS varchar) AS ServedFrom, " +
				" CASE ServedTillMonth WHEN - 1 THEN 'Still Working' ELSE (dbo.Month_Name(req.ServedTillMonth) + ' ' + CAST(req.ServedTillYear AS varchar)) " +
				" END AS ServedTill, CASE AreaofExperience WHEN - 1 THEN AreaOfExperienceOther ELSE " +
				" (SELECT ExpField " +
				" FROM dbo.cv_ExperinceFields " +
				" WHERE (ExpFieldID = AreaofExperience)) END AS AreaofExp, " +
				" CASE req.addflag WHEN 1 THEN 'New' WHEN 2 THEN 'Change' WHEN 3 THEN 'Remove' END AS AddFlag " +
				" FROM dbo.cv_ExperinceReq AS req INNER JOIN " +
				" dbo.cv_Months AS mo ON req.ServedFromMonth = mo.MonthID " +
				" WHERE (req.PCode = '"+Session["user_id"].ToString()+"') AND (req.AppFlag = 2) " ;
			
			da=new SqlDataAdapter(sql,conn);
			da.Fill(ds,"req");
			dgExperienceReq.DataSource=ds.Tables[1].DefaultView;
			dgExperienceReq.DataBind();
			AssignConfim(dgExperienceReq,"Linkbutton1","Are you sure to delete this record?");

		}

		private void AssignConfim(DataGrid dg, string controlName, string msg)
		{
			for(int i=0;i<dg.Items.Count;i++)
			{
				LinkButton lnk=(LinkButton)dg.Items[i].FindControl(controlName);
				lnk.Attributes.Clear();
				lnk.Attributes.Add("onclick","return confirm('"+msg+"');"); 
			}
		}

		private void FillDDL(DropDownList ddl, string table, string val, string key, string condition, string title, SqlConnection con)
		{
			if (condition!="")
				condition="where "+condition;
			string sql="select "+key+","+val+" from "+table+" "+condition+" order by "+val;
			SqlDataAdapter da=new SqlDataAdapter(sql,con);
			DataSet ds=new DataSet();
			da.Fill(ds);
			ddl.Items.Clear();
			ddl.Items.Add("Select - "+title);
			for(int j=0;j<ds.Tables[0].Rows.Count;j++)
			{
				ddl.Items.Add(new ListItem(ds.Tables[0].Rows[j][val].ToString(),
					ds.Tables[0].Rows[j][key].ToString()));
			}
			
		}

		private string GetTimeline(string wfid)
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			string sql=" SELECT timeline " +
				" FROM dbo.t_fieldsmgt " +
				" WHERE (f_id = "+wfid+") ";
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			return ds.Tables[0].Rows[0][0].ToString();
		}

		private void SaveExperienceRequest()
		{
			string replyatrequest=GetReplyMsg("replyatrequest",expCode);
			SqlConnection con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlTransaction tran=con.BeginTransaction();

			string fn="";
			
			if(fileExp.PostedFile.FileName!="")
			{
				fn=Guid.NewGuid().ToString()+".jpg";
			}
			else
			{
				fn="";
			}
			
			string sql="insert into cv_ExperinceReq(ExpID,PCode,AreaofExperience,AreaOfExperienceOther,CompanyName,Position,Department,ServedFromMonth,ServedFromYear,ServedTillMonth,ServedTillYear,ReportingTo,Subordinate,OrganizationAddress,OrganizationPhone,ContactPerson,OrganizationEmail,MajorResponsibilities,Achivements,LastSalary,MobileEntitlement,Petrol,CarConveyance,CarDescription,ResonToLeave,cdate,SalarySymb,MobileSymb,c_at,Addflag,wfid,AppFlag,filename,requesttimeline)"+
				" values (@ExpID,@PCode,@AreaofExperience,@AreaOfExperienceOther,@CompanyName,@Position,@Department,@ServedFromMonth,@ServedFromYear,@ServedTillMonth,@ServedTillYear,@ReportingTo,@Subordinate,@OrganizationAddress,@OrganizationPhone,@ContactPerson,@OrganizationEmail,@MajorResponsibilities,@Achivements,@LastSalary,@MobileEntitlement,@Petrol,@CarConveyance,@CarDescription,@ResonToLeave,getdate(),@SalarySymb,@MobileSymb,getdate(),@Addflag,@wfid,@AppFlag,@filename,@requesttimeline)";

			SqlCommand cmd=new SqlCommand(sql,con);
			cmd.Transaction=tran;

			cmd.Parameters.Add(new SqlParameter("@PCode",Session["user_id"].ToString()));
			cmd.Parameters.Add(new SqlParameter("@AreaofExperience",ddlAreaOfExperience.SelectedValue));
			cmd.Parameters.Add(new SqlParameter("@AreaOfExperienceOther",txtOtherAreaOfExp.Text.Trim()));
			cmd.Parameters.Add(new SqlParameter("@CompanyName",txtCompanyName.Text.Trim()));
			cmd.Parameters.Add(new SqlParameter("@Position",txtPositionHeld.Text.Trim()));
			cmd.Parameters.Add(new SqlParameter("@Department",txtDepartment2.Text.Trim()));
			cmd.Parameters.Add(new SqlParameter("@ServedFromMonth",ddlServedFromMonth.SelectedValue));
			cmd.Parameters.Add(new SqlParameter("@ServedFromYear",ddlServedFromYear.SelectedValue));
			cmd.Parameters.Add(new SqlParameter("@ServedTillMonth",ddlServedTillMonth.SelectedValue));
			if(ddlServedTillMonth.SelectedValue=="-1")
				cmd.Parameters.Add(new SqlParameter("@ServedTillYear",DBNull.Value));
			else
				cmd.Parameters.Add(new SqlParameter("@ServedTillYear",ddlServedTillYear.SelectedValue));
			cmd.Parameters.Add(new SqlParameter("@ReportingTo",txtReportingTo.Text.Trim()));
			cmd.Parameters.Add(new SqlParameter("@Subordinate",ddlSubordinates.SelectedValue));
			cmd.Parameters.Add(new SqlParameter("@OrganizationAddress",txtOrganizationAddress.Text.Trim()));
			cmd.Parameters.Add(new SqlParameter("@OrganizationPhone",txtOrganizationPhone.Text.Trim()));
			cmd.Parameters.Add(new SqlParameter("@ContactPerson",txtContactPerson.Text.Trim()));
			cmd.Parameters.Add(new SqlParameter("@OrganizationEmail",txtOrganizationEmail.Text.Trim()));
			cmd.Parameters.Add(new SqlParameter("@MajorResponsibilities",txtMajorResponsibilities.Text.Trim()));
			cmd.Parameters.Add(new SqlParameter("@Achivements",txtAchivements.Text.Trim()));
			cmd.Parameters.Add(new SqlParameter("@LastSalary",txtLastSalary.Text.Trim()));
			cmd.Parameters.Add(new SqlParameter("@MobileEntitlement",txtMobileEnt.Text.Trim()));
			cmd.Parameters.Add(new SqlParameter("@Petrol",txtPetrolEnt.Text.Trim()));
			cmd.Parameters.Add(new SqlParameter("@CarConveyance",ddlCar.SelectedValue));
			cmd.Parameters.Add(new SqlParameter("@CarDescription",txtCarDescription.Text.Trim()));
			cmd.Parameters.Add(new SqlParameter("@ResonToLeave",txtResonToLeave.Text.Trim()));
			cmd.Parameters.Add(new SqlParameter("@SalarySymb",ddlSalarCur.SelectedValue));
			cmd.Parameters.Add(new SqlParameter("@MobileSymb",ddlMobileCur.SelectedValue));
			cmd.Parameters.Add(new SqlParameter("@FileName",fn));
			SqlParameter _requesttimeline=new SqlParameter("@requesttimeline",SqlDbType.Int);
			_requesttimeline.Value=GetTimeline(expCode);

			
			cmd.Parameters.Add(new SqlParameter("@wfid",expCode));
			cmd.Parameters.Add(new SqlParameter("@AppFlag","2"));
			cmd.Parameters.Add(_requesttimeline);

			SqlParameter _expid=new SqlParameter("@ExpID",SqlDbType.Int);

			if(cmdSaveExp.Text=="Add" || cmdSaveExp.Text=="Save")
			{
				_expid.Value=DBNull.Value;
				cmd.Parameters.Add(_expid);
				
				cmd.Parameters.Add(new SqlParameter("@Addflag","1"));
			}
			else if (cmdSaveExp.Text=="Update")
			{
				SqlCommand cmd2=new SqlCommand("update cv_ExperinceReq set appflag=4, AppRejDate=getdate(), AppRejBy='"+Session["user_id"].ToString()+"' where ExpID="+lblExpID.Text+" and AppFlag=2 and pcode='"+Session["user_id"].ToString()+"'",con);
				cmd2.Transaction=tran;
				cmd2.ExecuteNonQuery();
				Bulletin.PostReqMessage(con,tran,"Request Canceled By User","GEO Raabta",Session["user_id"].ToString(),1,"Experience","cv_ExperinceReq");
				_expid.Value=lblExpID.Text;
				cmd.Parameters.Add(_expid);
				cmd.Parameters.Add(new SqlParameter("@Addflag","2"));
			}
			bool err=false;
			try
			{
				cmd.ExecuteNonQuery();
				Bulletin.PostReqMessage(con,tran,replyatrequest,"GEO Raabta",Session["user_id"].ToString(),1,"Experience","cv_ExperinceReq");
				if(fileExp.PostedFile.FileName!="")
					fileExp.PostedFile.SaveAs(Server.MapPath(@"tempEmployee\"+fn));
				tran.Commit();
				err=false;
			}
			catch(Exception ex)
			{
				Response.Write(ex);
				tran.Rollback();
				err=true;
			}
			tran.Dispose();
			if(!err)
			{
				ResetExpForm();
				lbAddExp.Visible=true;
				pnlExp.Visible=false;
				UpdateExpGrid();
				ResetExpForm();
				lblTrainingMsg.Visible=true;
				lblTrainingMsg.Text=GetReplyMsg("replyatrequest",expCode);
			}
			con.Close();
		}


		public void DeleteExpReq(object sender, System.EventArgs e)
		{
			LinkButton rLink=(LinkButton)sender;
			for(int i=0;i<dgExperienceReq.Items.Count;i++)
			{
				LinkButton dLink=(LinkButton)dgExperienceReq.Items[i].FindControl("Linkbutton1");
				if(dLink.Equals(rLink))
				{
					string id=dgExperienceReq.Items[i].Cells[0].Text;
					UpdateRequest("cv_ExperinceReq",id,"4");
				}
			}
			//UpdateExpGrid();
			ResetExpForm();
		}

		private void UpdateRequest(string tablename,string reqid, string status)
		{
			//SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			//conn.Open();
			SqlCommand cmd=new SqlCommand("update "+tablename+" set AppFlag="+status+",AppRejDate=Getdate(),AppRejBy='"+Session["user_id"].ToString()+"' where reqid="+reqid,conn);
			cmd.ExecuteNonQuery();
			//conn.Close();
		}
		private void ResetExpForm()
		{
			ddlAreaOfExperience.SelectedIndex=0;
			txtOtherAreaOfExp.Text="";
			txtCompanyName.Text="";
			txtPositionHeld.Text="";
			txtDepartment2.Text="";
			ddlServedFromMonth.SelectedIndex=0;
			ddlServedFromYear.SelectedIndex=0;
			ddlServedTillMonth.SelectedIndex=0;
			ddlServedTillYear.SelectedIndex=0;
			txtReportingTo.Text="";
			ddlSubordinates.SelectedIndex=0;
			txtOrganizationAddress.Text="";
			txtOrganizationPhone.Text="";
			txtContactPerson.Text="";
			txtOrganizationEmail.Text="";
			txtMajorResponsibilities.Text="";
			txtAchivements.Text="";
			txtLastSalary.Text="";
			txtMobileEnt.Text="";
			txtPetrolEnt.Text="";
			ddlCar.SelectedIndex=0;
			txtCarDescription.Text="";
			txtResonToLeave.Text="";
			ddlSalarCur.SelectedIndex=0;
			ddlMobileCur.SelectedIndex=0;
			cmdSaveExp.Text="Add";
			FillExpFieldDDL();
			pnlExp.Visible=true;
			pnlExpDetail.Visible=false;
			cmdSaveExp.Text="Save";
			UpdateExpGrid();
			dgExperience.SelectedIndex=-1;
			dgExperienceReq.SelectedIndex=-1;
		}

		private void FillExpFieldDDL()
		{
			string sql="SELECT ExpFieldID, ExpField FROM dbo.cv_ExperinceFields ORDER BY ExpField";
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			ddlAreaOfExperience.Items.Clear();
			ddlAreaOfExperience.Items.Add("Select -- Area of Experience");
			for(int j=0;j<ds.Tables[0].Rows.Count;j++)
			{
				ddlAreaOfExperience.Items.Add(new ListItem(ds.Tables[0].Rows[j]["ExpField"].ToString(),
					ds.Tables[0].Rows[j]["ExpFieldID"].ToString()));
			}
			ddlAreaOfExperience.Items.Add(new ListItem("Other","-1"));

		}
		private void cmdSaveExp_Click(object sender, System.EventArgs e)
		{
			SaveExperienceRequest();
			
			//pnlExpDetail.Visible=true;
			//ResetExpForm();
		}

		private void lbAddExp_Click(object sender, System.EventArgs e)
		{
			ResetExpForm();
			lbAddExp.Visible=false;
			pnlExp.Visible=false;
			pnlExpDetail.Visible=true;
		}

		private void cmdCancelExp_Click(object sender, System.EventArgs e)
		{
			lbAddExp.Visible=true;
			pnlExp.Visible=true;
			pnlExpDetail.Visible=false;
			ResetExpForm();
		}

		private void SetDDL(DropDownList ddl, string val)
		{
			ddl.SelectedIndex=0;
			for(int j=0;j<ddl.Items.Count;j++)
			{
				if(ddl.Items[j].Value==val)
				{
					ddl.SelectedIndex=j;
					break;
				}
			}
		}

		private void SetText(DataSet ds,TextBox tb,string field)
		{
			tb.Text=ds.Tables[0].Rows[0][field].ToString();
		}

		public void FillYears()
		{
			int startYear=1900;
			int endYear=DateTime.Today.Year;
			for(int i=endYear;i>=startYear;i--)
			{
				ListItem item=new ListItem(i.ToString(),i.ToString());
			
			
				//ddlTrainingYear.Items.Add(item);
				ddlServedFromYear.Items.Add(item);
				ddlServedTillYear.Items.Add(item);
			}
			//ddlTrainingYear.Items.Insert(0,new ListItem("","-1"));
			ddlServedFromYear.Items.Insert(0,new ListItem("",""));
			ddlServedTillYear.Items.Insert(0,new ListItem("",""));

			ListItem itm2=new ListItem("In Progress","-1");
			
		}

		private void imgEmp_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
		
		}

		private void imgFamily_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("EmpFamily.aspx?pcode="+this.lblPCode.Text+"&name="+this.lblEmpInfo.Text);
		}

		private void imgEducation_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("EmpEducation.aspx?pcode="+this.lblPCode.Text+"&name="+this.lblEmpInfo.Text);
		}

		private void imgTraining_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("EmpTraining.aspx?pcode="+this.lblPCode.Text+"&name="+this.lblEmpInfo.Text);
		}

		private void imgMyGrievance_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("MyGrievance.aspx?pcode="+lblPCode.Text+"&name="+lblEmpInfo.Text);
		}

		private void dgExperienceReq_SelectedIndexChanged(object sender, System.EventArgs e)
		{
		
		}

		private void ImageButton1_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("UserProfile.aspx?id=1");
		}

		private void ImageButton2_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("UserProfile.aspx?id=2");
		}
		private void ImageButton5_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			//pnlPaySlip.Visible=false;
			//Response.Write("<script language = Javascript >var win=window.showModalDialog('admin/textonimage1.aspx','','dialogHeight:900px;dialogWidth:800px;center:yes')</script"); 
			Response.Write("<script language = Javascript >win=window.open('admin/orgamogram.htm','_blank','resizable=yes,menubar=no,scrollbars=yes,top=0,status=0',true).focus();</script>");

		}

		private void ImageButton3_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("empfamily.aspx");
		}

		private void ImageButton4_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("empeducation.aspx");
		}



	}
}
