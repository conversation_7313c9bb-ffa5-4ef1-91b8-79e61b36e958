<%@ Register TagPrefix="ew" Namespace="eWorld.UI" Assembly="eWorld.UI" %>
<%@ Page CodeBehind="Categorization.aspx.cs" Language="c#" AutoEventWireup="false" Inherits="GeoRabtaSite.GeoCategory" smartNavigation="True"%>
<%@ Register TagPrefix="cc1" Namespace="ZmodemControls" Assembly="ZmodemControls" %>
<%@ Register TagPrefix="uc1" TagName="myMenus" Src="myMenus.ascx" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Rabta Admin :: Category</title>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="RaabtaAdmin.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body dir="ltr" bottomMargin="0" bgProperties="fixed" leftMargin="0" topMargin="0" rightMargin="0">
		<form id="myForm" name="MyForm" runat="server">
			<table height="100%" cellSpacing="0" cellPadding="0" width="780" align="center" bgColor="#ffffff"
				border="0">
				<tr>
					<td vAlign="middle" align="left" height="69">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="69" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="1826">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top1.swf">
							<PARAM NAME="Src" VALUE="flash/Top1.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<embed src="flash/Top1.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="69"> </embed>
						</OBJECT>
					</td>
				</tr>
				<tr>
					<td height="10">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="10" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="265">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top2.swf">
							<PARAM NAME="Src" VALUE="flash/Top2.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<embed src="flash/Top2.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="10"> </embed>
						</OBJECT>
					</td>
				</tr>
				<TR>
					<TD height="20" class="PageTitle">Geo Rabta Admin :: Category</TD>
				</TR>
				<tr>
					<td height="29" background="../images/menu-off-bg.gif"><uc1:mymenus id="MyMenus1" runat="server"></uc1:mymenus></td>
				</tr>
				<TR>
					<TD class="mainbg"><BR>
						<TABLE class="MainFormColor" id="pnlCategoryForm" cellSpacing="0" cellPadding="3" width="750"
							align="center" border="0" runat="server">
							<TR>
								<TD class="OrangeFormTitle" colSpan="3">Category Information:</TD>
							</TR>
							<TR>
								<TD class="menubar" vAlign="bottom" width="320">
									<asp:label id="Label2" runat="server" Font-Bold="True">Category Name :</asp:label>
									<asp:RequiredFieldValidator id="RequiredFieldValidator1" runat="server" ControlToValidate="txtCatName" Display="Dynamic"
										ErrorMessage="* Category Name is required" CssClass="ErrorLabel" ForeColor=" "></asp:RequiredFieldValidator></TD>
								<TD class="menubar" vAlign="bottom" width="95"></TD>
								<TD class="menubar" vAlign="bottom" width="320">
									<asp:label id="Label4" runat="server" Font-Bold="True">Category Criteria :</asp:label></TD>
							</TR>
							<TR>
								<TD vAlign="top" width="320">
									<asp:textbox id="txtCatName" runat="server" CssClass="textbox" Width="100%"></asp:textbox></TD>
								<TD vAlign="top" width="95"></TD>
								<TD vAlign="top" width="320">
									<asp:textbox id="txtCatCriteria" runat="server" CssClass="textbox" Width="100%" Rows="3" TextMode="MultiLine"></asp:textbox></TD>
							</TR>
							<TR>
								<TD class="menubar" vAlign="top" width="320">
									<asp:label id="Label3" runat="server" Font-Bold="True">Level Description :</asp:label></TD>
								<TD class="menubar" vAlign="top" width="95"></TD>
								<TD class="menubar" vAlign="top" width="320">
									<asp:label id="Label5" runat="server" Font-Bold="True">Category Color :</asp:label></TD>
							</TR>
							<TR>
								<TD vAlign="top" width="320">
									<asp:textbox id="txtCatLevel" runat="server" CssClass="textbox" Width="100%"></asp:textbox></TD>
								<TD vAlign="top" width="95"></TD>
								<TD vAlign="top" width="320">
									<asp:textbox id="txtCatColor" runat="server" CssClass="textbox" Width="100%"></asp:textbox></TD>
							</TR>
							<TR>
								<TD class="menubar" vAlign="top" width="320">
									<asp:label id="Label8" runat="server" Font-Bold="True">Parent Category :</asp:label></TD>
								<TD class="menubar" vAlign="top" width="95"></TD>
								<TD class="menubar" vAlign="top" width="320">
									<asp:label id="Label6" runat="server" Font-Bold="True">Positive :</asp:label></TD>
							</TR>
							<TR>
								<TD vAlign="top" width="320">
									<asp:dropdownlist id="ddParentCat" runat="server" CssClass="textbox" Width="100%">
										<asp:ListItem Value="0">Select--Parent Category</asp:ListItem>
									</asp:dropdownlist></TD>
								<TD vAlign="top" width="95"></TD>
								<TD vAlign="top" width="320">
									<asp:textbox id="txtPositive" runat="server" CssClass="textbox" Width="100%" Rows="3" TextMode="MultiLine"></asp:textbox></TD>
							</TR>
							<TR>
								<TD class="menubar" vAlign="top" width="320">
									<asp:label id="Label9" runat="server" Font-Bold="True">Supervisory Role : </asp:label>
									<asp:Label id="Label7" runat="server" CssClass="ErrorLabel">* Supervisory role is required</asp:Label></TD>
								<TD class="menubar" vAlign="top" width="95"></TD>
								<TD class="menubar" vAlign="top" width="320">
									<asp:label id="Label11" runat="server" Font-Bold="True">Level : </asp:label>
									<asp:label id="Label10" runat="server" Visible="False" Font-Bold="True" ForeColor="Red">This Level already in use</asp:label></TD>
							</TR>
							<TR>
								<TD vAlign="top" width="320">
									<asp:dropdownlist id="ddSRole" runat="server" CssClass="textbox" Width="100%">
										<asp:ListItem Value="0">Select--Role</asp:ListItem>
										<asp:ListItem Value="1">Yes</asp:ListItem>
										<asp:ListItem Value="2">No</asp:ListItem>
									</asp:dropdownlist></TD>
								<TD vAlign="top" width="95"></TD>
								<TD vAlign="top" width="320">
									<ew:numericbox id="txtLevel" runat="server" CssClass="TextBox" Width="100%" RealNumber="False"
										PositiveNumber="True" MaxLength="2"></ew:numericbox></TD>
							</TR>
							<TR>
								<TD vAlign="top" width="320">
									<asp:RadioButtonList id="rblCategory" runat="server" Width="100%" AutoPostBack="True" RepeatDirection="Horizontal"
										RepeatLayout="Flow">
										<asp:ListItem Value="1" Selected="True">Child Category</asp:ListItem>
										<asp:ListItem Value="2">Super Category</asp:ListItem>
									</asp:RadioButtonList></TD>
								<TD vAlign="top" width="95">
									<asp:Panel id="Panel1" runat="server" Visible="False">
										<asp:textbox id="txtCatId" runat="server" CssClass="textbox" Visible="False" ReadOnly="True"></asp:textbox>
										<asp:label id="Label1" runat="server" Font-Bold="True" Visible="False">Category Id</asp:label>
									</asp:Panel></TD>
								<TD vAlign="top" width="320">
									<ew:CollapsablePanel id="CollapsablePanel1" runat="server" TitleText="<b><font color=black>Color</font></b>"
										AllowTitleExpandCollapse="True" AllowTitleRowExpandCollapse="True" AllowSliding="True" CollapseText=" "
										ExpandText=" ">
										<cc1:colourpicker id="ColourPicker1" runat="server" CellWidth="1" TableHeight="150" TableWidth="150"
											ColourIncrement="10"></cc1:colourpicker>
										<BR>
									</ew:CollapsablePanel><BR>
									<asp:textbox id="txtColor" runat="server" Visible="False"></asp:textbox><BR>
								</TD>
							</TR>
							<TR>
								<TD vAlign="top" colSpan="3">
									<asp:button id="btnQueryButton" runat="server" Width="120px" Text="New Category"></asp:button>
									<asp:button id="cmdDelete" runat="server" Width="120px" Text="Delete" CausesValidation="False"></asp:button>
									<asp:button id="btnCancel" runat="server" Width="120px" Text="Cancel" CausesValidation="False"></asp:button></TD>
							</TR>
							<TR>
								<TD vAlign="top" colSpan="3">
									<asp:Label id="Label12" runat="server" Font-Bold="True" ForeColor="Red">You can not delete this category, Please first delete related designation or sub category</asp:Label></TD>
							</TR>
						</TABLE>
						<TABLE id="Table1" cellSpacing="0" cellPadding="3" width="750" align="center" border="0">
							<TR>
								<TD>
									<asp:LinkButton id="Button1" runat="server" CausesValidation="False">Add New Category</asp:LinkButton></TD>
							</TR>
							<TR>
								<TD>
									<asp:DataGrid id="DataGrid2" runat="server" Width="100%" BorderStyle="Solid" BorderWidth="1px"
										BorderColor="Navy" AutoGenerateColumns="False" CellPadding="3" GridLines="Horizontal">
										<AlternatingItemStyle CssClass="alternetitem"></AlternatingItemStyle>
										<ItemStyle CssClass="item"></ItemStyle>
										<HeaderStyle HorizontalAlign="Center" ForeColor="Black" CssClass="header" VerticalAlign="Bottom"></HeaderStyle>
										<Columns>
											<asp:BoundColumn Visible="False" DataField="id" HeaderText="id"></asp:BoundColumn>
											<asp:EditCommandColumn ButtonType="LinkButton" UpdateText="Update" CancelText="Cancel" EditText="Select"></asp:EditCommandColumn>
											<asp:BoundColumn DataField="clevel2" SortExpression="clevel" HeaderText="Category Lavel"></asp:BoundColumn>
											<asp:BoundColumn DataField="Name" SortExpression="Name" HeaderText="Name"></asp:BoundColumn>
											<asp:BoundColumn DataField="Level" SortExpression="Level" HeaderText="Level"></asp:BoundColumn>
											<asp:BoundColumn DataField="SupervisoryRole" SortExpression="SupervisoryRole" HeaderText="Supervisory Role"></asp:BoundColumn>
										</Columns>
									</asp:DataGrid></TD>
							</TR>
							<TR>
								<TD></TD>
							</TR>
						</TABLE>
						<BR>
					</TD>
				</TR>
				<TR>
					<TD align="center" height="15">Copyright © 2005 Independent Media Corporation <A href="http://www.geo.tv">
							www.geo.tv</A></TD>
				</TR>
			</table>
			</TD></TR><TR>
				<TD class="Fotter" height="20" align="center">
			</TR>
			</TABLE></form>
	</body>
</HTML>
