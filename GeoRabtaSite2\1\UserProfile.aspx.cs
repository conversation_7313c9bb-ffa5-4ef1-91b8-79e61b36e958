using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;
using System.IO;
namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for EmployeInfo.
	/// </summary>
	public class EmployeInfo : System.Web.UI.Page
	{
		private bool _refreshState;
		private bool _isRefresh;

		public bool IsRefresh
		{
			get
			{
				return _isRefresh;
			}
		}
		protected System.Web.UI.WebControls.Label lblExtension;
		protected System.Web.UI.WebControls.Label lblDepartment;
		protected System.Web.UI.WebControls.Label lblDesignation;
		protected System.Web.UI.WebControls.Label lblDob;
		protected System.Web.UI.WebControls.Label lblDoJ;
		protected System.Web.UI.WebControls.Label lblCity;
		protected System.Web.UI.WebControls.Label lblEmailPersonal;
		protected System.Web.UI.WebControls.TextBox txtExtension;
		protected System.Web.UI.WebControls.TextBox txtDepartment;
		protected System.Web.UI.WebControls.TextBox txtDesignation;
		protected System.Web.UI.WebControls.TextBox txtDOB;
		protected System.Web.UI.WebControls.TextBox txtDOJ;
		protected System.Web.UI.WebControls.TextBox txtEmailComp;
		protected System.Web.UI.WebControls.TextBox txtCity;
		protected System.Web.UI.WebControls.TextBox txtEmailPersonal;
		protected System.Web.UI.WebControls.TextBox TextBox25;
		protected System.Web.UI.WebControls.TextBox TextBox26;
		protected System.Web.UI.WebControls.TextBox TextBox27;
		protected System.Web.UI.WebControls.TextBox TextBox28;
		protected System.Web.UI.WebControls.TextBox TextBox29;
		protected System.Web.UI.WebControls.TextBox TextBox30;
		SqlConnection con;
		public static int FamilyId=0;
		public static int EducationId=0;
		public static string sort;
		public static string sort2;
		public static string sort3;
		public static string sort4;
		static int WebFormID=30;
		static int ProjectID=2;
		static string userId="";
		//		static bool isEmp;
		//        static bool isPer; 
		protected System.Web.UI.WebControls.Button btnOtherMajor;
		
		//public static int FamStat;
		//public static int EduinStat;
		
		//public static string FunctionalId;
		
		public DataSet dFamily;
		public DataSet dEducation;
		protected System.Web.UI.WebControls.DropDownList rlocalhostGender;
		public static DataTable tFamily;
		protected System.Web.UI.HtmlControls.HtmlGenericControl info;
		protected System.Web.UI.HtmlControls.HtmlInputFile FileToUploads;
		protected System.Web.UI.WebControls.LinkButton LinkButton1;
		protected System.Web.UI.WebControls.ImageButton ImageButton1;
		protected System.Web.UI.WebControls.ImageButton ImageButton2;
		protected System.Web.UI.WebControls.ImageButton ImageButton3;
		protected System.Web.UI.WebControls.ImageButton ImageButton4;
		protected System.Web.UI.WebControls.ImageButton ImageButton5;
		protected System.Web.UI.WebControls.ImageButton ibSalary;
		protected System.Web.UI.WebControls.ImageButton ibMySelf;
		protected System.Web.UI.WebControls.ImageButton imgMyLeave;
		protected System.Web.UI.WebControls.ImageButton ImgMyAttendance;
		protected System.Web.UI.WebControls.Panel Panel1;
		protected System.Web.UI.WebControls.Label lblDisName;
		protected System.Web.UI.WebControls.Label lblCount;
		protected System.Web.UI.WebControls.Label Label2;
		protected System.Web.UI.WebControls.Label lblPCode;
		protected System.Web.UI.WebControls.Label lblEmpInfo;
		protected System.Web.UI.WebControls.Label Label1;
		protected System.Web.UI.WebControls.Label lblName;
		protected System.Web.UI.WebControls.Label lblFName;
		protected System.Web.UI.WebControls.Label lblGender;
		protected System.Web.UI.WebControls.Label lblBloodGrp;
		protected System.Web.UI.WebControls.Label lblReligion;
		protected System.Web.UI.WebControls.Label lblPassport;
		protected System.Web.UI.WebControls.Label lblAddress;
		protected System.Web.UI.WebControls.Label lblMaritialStat;
		protected System.Web.UI.WebControls.Label lblEmail;
		protected System.Web.UI.WebControls.Label lblNTN;
		protected System.Web.UI.WebControls.Label lblcontactNo;
		protected System.Web.UI.WebControls.Label lblMobileNo;
		protected System.Web.UI.WebControls.Label lblNICNew;
		protected System.Web.UI.WebControls.Label lblNICOld;
		protected System.Web.UI.WebControls.Label lblKin;
		protected System.Web.UI.WebControls.Label lblBankAcctNo;
		protected System.Web.UI.WebControls.Label lblAccountDetails;
		protected System.Web.UI.WebControls.Label lblNick;
		protected System.Web.UI.WebControls.Label lblNationality;
		protected System.Web.UI.WebControls.Label lblNationality2;
		protected System.Web.UI.WebControls.Panel pnlPersonalUPdate;
		protected System.Web.UI.WebControls.Panel pnlPersonal;
		protected System.Web.UI.WebControls.Label lblEmpInfoName;
		protected System.Web.UI.WebControls.Label lnlEmpInfoDept;
		protected System.Web.UI.WebControls.Label lblEmpInfoDesignation;
		protected System.Web.UI.WebControls.Label lblFunctionalDesignation;
		protected System.Web.UI.WebControls.Label lblEmpInfoCategory;
		protected System.Web.UI.WebControls.Label lblEmpInfoDOJ;
		protected System.Web.UI.WebControls.Label lblEmpInfoEmail;
		protected System.Web.UI.WebControls.Label lblEmpInfoExtension;
		protected System.Web.UI.WebControls.Label lblEmpInfoSessi;
		protected System.Web.UI.WebControls.Label lblEmpInfoEObi;
		protected System.Web.UI.WebControls.Label lblEmpInfoCity;
		protected System.Web.UI.WebControls.Label lblEmpInfoDOC;
		protected System.Web.UI.WebControls.Label lblEmpInfoTOE;
		protected System.Web.UI.WebControls.Label lblEmpInfoComp;
		protected System.Web.UI.WebControls.Label lblEmpInfoOthers;
		protected System.Web.UI.WebControls.Label lblEmpLocation;
		protected System.Web.UI.WebControls.TextBox txtEmpInfoName;
		protected System.Web.UI.WebControls.Label lbltName;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator1;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator3;
		protected System.Web.UI.WebControls.LinkButton lnName;
		protected System.Web.UI.WebControls.Label lblfpName;
		protected System.Web.UI.WebControls.DropDownList ddDepartment;
		protected System.Web.UI.WebControls.Label lbltDepartment;
		protected System.Web.UI.WebControls.Panel pnlUpdateEmp;
		protected System.Web.UI.WebControls.Panel pnlEmployeeInfo;
		protected System.Web.UI.WebControls.Label lblPCodeP;
		protected System.Web.UI.WebControls.Label lblNameP;
		protected System.Web.UI.WebControls.Label lblDesgP;
		protected System.Web.UI.WebControls.Label lblDeptP;
		protected System.Web.UI.WebControls.DropDownList ddlYear;
		protected System.Web.UI.WebControls.DropDownList ddlMonths;
		protected System.Web.UI.WebControls.Label lblSalaryDateP;
		protected System.Web.UI.WebControls.Label lblBankP;
		protected System.Web.UI.WebControls.Label lblBranchP;
		protected System.Web.UI.WebControls.Label lblGrossSalaryP;
		protected System.Web.UI.WebControls.Label lblIncomTaxP;
		protected System.Web.UI.WebControls.Label lblArearsP;
		protected System.Web.UI.WebControls.Label lblNetPayRollP;
		protected System.Web.UI.WebControls.Label lblTotalWorkingDaysP;
		protected System.Web.UI.WebControls.Button btnPrint;
		protected System.Web.UI.WebControls.Label lblMessage;
		protected System.Web.UI.WebControls.Panel pnlPaySlip;
		protected System.Web.UI.WebControls.Button btnUpdate;
		protected System.Web.UI.WebControls.Label lblCombine;
		protected System.Web.UI.WebControls.Button btnIgnore;
		protected System.Web.UI.WebControls.Button btnResend;
		protected System.Web.UI.WebControls.Label lblCompare;
		protected System.Web.UI.WebControls.ValidationSummary ValidationSummary1;
		protected System.Web.UI.WebControls.TextBox TextBox1;
		protected System.Web.UI.WebControls.TextBox txtX;
		protected System.Web.UI.WebControls.TextBox txtY;
		protected System.Web.UI.HtmlControls.HtmlImage imgInfo;
		protected System.Web.UI.HtmlControls.HtmlInputFile fpName;
		protected System.Web.UI.HtmlControls.HtmlTable updateEmp;
		protected System.Web.UI.HtmlControls.HtmlTable tabPaySlip;
		protected System.Web.UI.WebControls.DropDownList ddDesignation;
		protected System.Web.UI.WebControls.DropDownList ddFunctionalDesignation;
		protected System.Web.UI.WebControls.DropDownList ddStation;
		protected System.Web.UI.WebControls.CustomValidator CustomValidator2;
		protected System.Web.UI.WebControls.CustomValidator CustomValidator3;
		protected System.Web.UI.WebControls.Label lbltDesignation;
		protected System.Web.UI.WebControls.CustomValidator CustomValidator4;
		protected System.Web.UI.WebControls.Label lbltStation;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator2;
		protected eWorld.UI.CalendarPopup CalenderDOJ;
		protected System.Web.UI.WebControls.Label lbltExtension;
		protected System.Web.UI.WebControls.TextBox txtEmpInfoExtension;
		protected System.Web.UI.WebControls.TextBox txtEmpInfoEmail;
		protected System.Web.UI.WebControls.TextBox txtEmpInfoSESSI;
		protected System.Web.UI.WebControls.TextBox txtEmpInfoEOBI;
		protected System.Web.UI.WebControls.TextBox txtEmpLocation;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator3;
		protected eWorld.UI.CalendarPopup calendarDOB;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator4;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator6;
		protected System.Web.UI.WebControls.TextBox txtFName;
		protected System.Web.UI.WebControls.RadioButtonList RadioButtonList1;
		protected System.Web.UI.WebControls.DropDownList ddBloodGrp;
		protected System.Web.UI.WebControls.DropDownList ddReligion;
		protected System.Web.UI.WebControls.TextBox txtNick;
		protected System.Web.UI.WebControls.TextBox txtAddress;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator5;
		protected System.Web.UI.WebControls.DropDownList ddStatus;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator9;
		protected System.Web.UI.WebControls.TextBox txtEmail;
		protected System.Web.UI.WebControls.TextBox txtNTN;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator7;
		protected System.Web.UI.WebControls.TextBox txtContactNo;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator8;
		protected System.Web.UI.WebControls.TextBox txtMobile;
		protected System.Web.UI.WebControls.CustomValidator CustomValidator1;
		protected System.Web.UI.WebControls.DropDownList ddNationality;
		protected System.Web.UI.WebControls.DropDownList ddNationality2;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator7;
		protected System.Web.UI.WebControls.TextBox txtPassport;
		protected System.Web.UI.WebControls.TextBox txtKin;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator6;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator1;
		protected System.Web.UI.WebControls.TextBox txtNewNic;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator2;
		protected System.Web.UI.WebControls.TextBox txtOldNic;
		protected System.Web.UI.WebControls.TextBox txtBankAcctNo;
		protected System.Web.UI.WebControls.TextBox txtAccountDetails;
		protected System.Web.UI.WebControls.Label lblEmp;
		protected System.Web.UI.HtmlControls.HtmlInputFile fpEOBI;
		protected System.Web.UI.WebControls.LinkButton lnEOBI;
		protected System.Web.UI.WebControls.Label lblfpEOBI;
		protected System.Web.UI.WebControls.Label lbltEOBI;
		protected System.Web.UI.HtmlControls.HtmlInputFile fpDateofBirth;
		protected System.Web.UI.HtmlControls.HtmlInputFile fpFatherName;
		protected System.Web.UI.HtmlControls.HtmlInputFile fpMartialStatus;
		protected System.Web.UI.HtmlControls.HtmlInputFile fpPassport;
		protected System.Web.UI.WebControls.LinkButton lnDateofBirth;
		protected System.Web.UI.WebControls.Label lblfpDateofBirth;
		protected System.Web.UI.WebControls.LinkButton lnFatherName;
		protected System.Web.UI.WebControls.Label lblfpFatherName;
		protected System.Web.UI.WebControls.LinkButton lnMartialStatus;
		protected System.Web.UI.WebControls.Label lblfpMaritalStatus;
		protected System.Web.UI.WebControls.LinkButton lnPassport;
		protected System.Web.UI.WebControls.Label lblfpPassprot;
		protected System.Web.UI.WebControls.Label lbltDateofBirth;
		protected System.Web.UI.WebControls.Label lbltFatherName;
		protected System.Web.UI.WebControls.Label lbltTelephoneNo;
		protected System.Web.UI.WebControls.Label lbltMobileNo;
		protected System.Web.UI.WebControls.Label lbltNationality;
		protected System.Web.UI.WebControls.LinkButton lnNationality;
		protected System.Web.UI.WebControls.Label lblfpNationality;
		protected System.Web.UI.HtmlControls.HtmlInputFile fpNationality;
		protected System.Web.UI.WebControls.Label lblfpNationality2;
		protected System.Web.UI.WebControls.LinkButton lnNationality2;
		protected System.Web.UI.WebControls.Label lbltNationality2;
		protected System.Web.UI.HtmlControls.HtmlInputFile fpNextofKin;
		protected System.Web.UI.WebControls.Label lbltPassport;
		protected System.Web.UI.WebControls.Label lbltNextofKin;
		protected System.Web.UI.HtmlControls.HtmlInputFile fpNICNew;
		protected System.Web.UI.HtmlControls.HtmlInputFile fpNICOld;
		protected System.Web.UI.HtmlControls.HtmlInputFile fpBankNo;
		protected System.Web.UI.WebControls.LinkButton lnNICNew;
		protected System.Web.UI.WebControls.Label lblfpNICNew;
		protected System.Web.UI.WebControls.LinkButton lnNICOld;
		protected System.Web.UI.WebControls.Label lblfpNICOld;
		protected System.Web.UI.WebControls.LinkButton lnBankNo;
		protected System.Web.UI.WebControls.Label lblfpBankNo;
		protected System.Web.UI.WebControls.LinkButton lnNextofKin;
		protected System.Web.UI.WebControls.Label lblfpNextofKin;
		protected System.Web.UI.WebControls.Label lbltNICNew;
		protected System.Web.UI.WebControls.Label lbltNICOld;
		protected System.Web.UI.WebControls.Label lbltBankNo;
		protected System.Web.UI.HtmlControls.HtmlInputFile fpBankDetail;
		protected System.Web.UI.WebControls.Label lbltBankDetail;
		protected System.Web.UI.WebControls.Label lblfpBankDetail;
		protected System.Web.UI.WebControls.LinkButton lnBankDetail;
		protected System.Web.UI.WebControls.Label lbltMaritalStatus;
		protected System.Web.UI.HtmlControls.HtmlInputFile fpNationality2;
		protected System.Web.UI.WebControls.ImageButton imgMyGrievance;
		protected System.Web.UI.HtmlControls.HtmlInputFile FileToUpload;
		protected System.Web.UI.HtmlControls.HtmlInputFile fpPicture;
		protected System.Web.UI.WebControls.LinkButton lnPicture;
		protected System.Web.UI.WebControls.Label lblfpPicture;
		protected System.Web.UI.WebControls.ImageButton imgTraining;
		protected System.Web.UI.WebControls.HyperLink hlMyExp;
		protected System.Web.UI.WebControls.HyperLink hlMyRequests;
		protected System.Web.UI.WebControls.Label lbltDateofJoin;
		protected System.Web.UI.WebControls.Label lbltEmailofficial;
		protected System.Web.UI.WebControls.Label lbltSessi;
		protected System.Web.UI.WebControls.Label lbltWorkstation;
		protected System.Web.UI.WebControls.Label lbltGender;
		protected System.Web.UI.WebControls.Label lbltBloodGroup;
		protected System.Web.UI.WebControls.Label lbltReligion;
		protected System.Web.UI.WebControls.Label lbltNick;
		protected System.Web.UI.WebControls.Label lbltAddress;
		protected System.Web.UI.WebControls.Label lbltEmailPersonal;
		protected System.Web.UI.WebControls.Label lbltNTN;
		protected System.Web.UI.WebControls.Label lbltPicture;
		
		string [] MonthName={"January","February","March","April","May","June","July","August","September","October","November","December"};

		private bool IsPageAccessAllowed()
		{
			
			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception )
			{
				Response.Redirect("Login.aspx");
			}

			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"View")==true)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("Login.aspx");
				return false;
			}
		}

		private void GetEployeeCounter()
		{
			SqlDataAdapter da = new SqlDataAdapter("select counter from t_systemuser where pcode='" + Session["user_id"].ToString() + "'",con);
			DataSet ds = new DataSet();
			da.Fill(ds,"Counting");
			if (ds.Tables[0].Rows.Count>0)
			{
				lblCount.Text="<b>"+ds.Tables[0].Rows[0][0].ToString()+"</b>";
			}
			else
			{
				lblCount.Text=" zero ";
			}
		}

		private void Page_Load(object sender, System.EventArgs e)
		{
			if(Request.QueryString.Count>0)
			{
				string Val=Request.QueryString["id"].ToString();
				if(Val=="1")
				{
					lblCombine.Visible=false;
					lblCompare.Visible=false;
					lblEmp.Visible=false;
					//this.lblEducationMsg.Visible=false;
					//this.lblFamilyMsg.Visible=false;
					this.Label1.Visible=false;
					this.Label2.Visible=false;
					this.pnlPersonal.Visible=false;
					this.pnlEmployeeInfo.Visible=true;
					//this.pnlFamilyInfo.Visible=false;
					//this.pnlEducation.Visible=false;
					if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"EmpInfo"))
						this.btnUpdate.Visible=true;
					pnlPaySlip.Visible=false;
					this.btnUpdate.Text="Submit Employement Request";
				}
				if(Val=="2")
				{
					lblCombine.Visible=false;
					lblCompare.Visible=false;
					lblEmp.Visible=false;
					//this.lblEducationMsg.Visible=false;
					//this.lblFamilyMsg.Visible=false;
					this.Label1.Visible=false;
					this.Label2.Visible=false;
					this.pnlPersonal.Visible=true;
					this.pnlEmployeeInfo.Visible=false;
					//this.pnlFamilyInfo.Visible=false;
					//this.pnlEducation.Visible=false;
					if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"PerInfo"))
						this.btnUpdate.Visible=true;
					pnlPaySlip.Visible=false;
					this.btnUpdate.Text="Submit Personal Request";
				}
			}
			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			FileToUpload.Visible=false;
			if(IsPageAccessAllowed())
			{
				if(!GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"EmpInfo"))
				{
					
					this.pnlUpdateEmp.Visible=false;
					this.btnUpdate.Visible=false;
					//empHeading.Visible=false;
				}
				if(!GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"PerInfo"))
				{
					this.pnlPersonalUPdate.Visible=false;
					this.btnUpdate.Visible=false; 
				}
				if(!GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"FamilyInfo"))
				{
					//this.Panel2.Visible=false;
					this.Label2.Visible=false;
					//this.DataGrid1.Columns[5].Visible=false;
					
				}
				if(!GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"EducationalInfo"))
				{
					//this.updateEducation.Visible=false;
					//this.btnEdNew.Visible=false;
					this.Label1.Visible=false;
					//this.DataGrid1.Columns[7].Visible=false;
					//this.dropDown.Visible=false;
				}
			}
			else
			{
				Response.Redirect("ErrorPage.aspx");
			}
			ArrayList tempList=new ArrayList();
			//			for(int z=0;z<this.DataGrid3.Items.Count;z++)
			//			{
			//				if(this.DataGrid3.Items[z].BackColor==Color.Red)
			//				{
			//					tempList.Add(z);
			//				}
			//			}
			//			for(int b=0;b<tempList.Count;b++)
			//			{
			//				int indx=(int)tempList[b];
			//				this.DataGrid3.Items[indx].BackColor=Color.Red;
			//				LinkButton eLnk=(LinkButton)this.DataGrid3.Items[indx].Cells[9].FindControl("lnkeDelete");
			//				eLnk.Visible=false;
			//				this.DataGrid3.Items[indx].Cells[7].Text="";
			//			}
			tempList=new ArrayList();
			//			for(int a=0;a<this.DataGrid1.Items.Count;a++)
			//			{
			//				if(this.DataGrid1.Items[a].BackColor==Color.Red)
			//				{
			//					tempList.Add(a);
			//				}
			//			}
			//			for(int c=0;c<tempList.Count;c++)
			//			{
			//				int idx=(int)tempList[c];
			//				this.DataGrid1.Items[idx].BackColor=Color.Red;
			//				LinkButton lnk=(LinkButton)this.DataGrid1.Items[idx].Cells[10].FindControl("lnkDelete");
			//				lnk.Visible=false;
			//				this.DataGrid1.Items[idx].Cells[7].Text="";
			//			}
			con=new SqlConnection(Connection.ConnectionString);
			//Page.SmartNavigation=true;
			//===============================Add Scan Copies=============================//
			if(IsPostBack && pnlPersonalUPdate.Visible)
			{
				if(!fpDateofBirth.Disabled)
				{
					if(this.fpDateofBirth.PostedFile.ContentLength>0)
					{
						fpDateofBirth.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\16"+Path.GetExtension(fpDateofBirth.PostedFile.FileName)));
						lnDateofBirth.Visible=true;
						lblfpDateofBirth.Visible=true;
						lblfpDateofBirth.Text=Path.GetFileName(fpDateofBirth.PostedFile.FileName);
						fpDateofBirth.Disabled=true;
					}
				}
				if(!fpFatherName.Disabled)
				{
					if(this.fpFatherName.PostedFile.ContentLength>0)
					{
						fpFatherName.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\24"+Path.GetExtension(fpFatherName.PostedFile.FileName)));
						lnFatherName.Visible=true;
						lblfpFatherName.Visible=true;
						lblfpFatherName.Text=Path.GetFileName(fpFatherName.PostedFile.FileName);
						fpFatherName.Disabled=true;
					}
				}
				if(!fpMartialStatus.Disabled)
				{
					if(this.fpMartialStatus.PostedFile.ContentLength>0)
					{
						fpMartialStatus.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\25"+Path.GetExtension(fpMartialStatus.PostedFile.FileName)));
						lnMartialStatus.Visible=true;
						lblfpMaritalStatus.Visible=true;
						lblfpMaritalStatus.Text=Path.GetFileName(fpMartialStatus.PostedFile.FileName);
						fpMartialStatus.Disabled=true;
					}
				}
				if(!fpNationality.Disabled)
				{
					if(this.fpNationality.PostedFile.ContentLength>0)
					{
						fpNationality.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\35"+Path.GetExtension(fpNationality.PostedFile.FileName)));
						lnNationality.Visible=true;
						lblfpNationality.Visible=true;
						lblfpNationality.Text=Path.GetFileName(fpNationality.PostedFile.FileName);
						fpNationality.Disabled=true;
					}
				}
				if(!fpNationality2.Disabled)
				{
					if(this.fpNationality2.PostedFile.ContentLength>0)
					{
						fpNationality2.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\36"+Path.GetExtension(fpNationality2.PostedFile.FileName)));
						lnNationality2.Visible=true;
						lblfpNationality2.Visible=true;
						lblfpNationality2.Text=Path.GetFileName(fpNationality2.PostedFile.FileName);
						fpNationality2.Disabled=true;
					}
				}
				if(!fpPassport.Disabled)
				{
					if(this.fpPassport.PostedFile.ContentLength>0)
					{
						fpPassport.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\19"+Path.GetExtension(fpPassport.PostedFile.FileName)));
						lnPassport.Visible=true;
						lblfpPassprot.Visible=true;
						lblfpPassprot.Text=Path.GetFileName(fpPassport.PostedFile.FileName);
						fpPassport.Disabled=true;
					}
				}
				if(!fpNextofKin.Disabled)
				{
					if(this.fpNextofKin.PostedFile.ContentLength>0)
					{
						fpNextofKin.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\30"+Path.GetExtension(fpNextofKin.PostedFile.FileName)));
						lnNextofKin.Visible=true;
						lblfpNextofKin.Visible=true;
						lblfpNextofKin.Text=Path.GetFileName(fpNextofKin.PostedFile.FileName);
						fpNextofKin.Disabled=true;
					}
				}
				if(!fpNICNew.Disabled)
				{
					if(this.fpNICNew.PostedFile.ContentLength>0)
					{
						fpNICNew.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\21"+Path.GetExtension(fpNICNew.PostedFile.FileName)));
						lnNICNew.Visible=true;
						lblfpNICNew.Visible=true;
						lblfpNICNew.Text=Path.GetFileName(fpNICNew.PostedFile.FileName);
						fpNICNew.Disabled=true;
					}
				}
				if(!fpNICOld.Disabled)
				{
					if(this.fpNICOld.PostedFile.ContentLength>0)
					{
						fpNICOld.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\20"+Path.GetExtension(fpNICOld.PostedFile.FileName)));
						lnNICOld.Visible=true;
						lblfpNICOld.Visible=true;
						lblfpNICOld.Text=Path.GetFileName(fpNICOld.PostedFile.FileName);
						fpNICOld.Disabled=true;
					}
				}
				if(!fpBankNo.Disabled)
				{
					if(this.fpBankNo.PostedFile.ContentLength>0)
					{
						fpBankNo.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\32"+Path.GetExtension(fpBankNo.PostedFile.FileName)));
						lnBankNo.Visible=true;
						lblfpBankNo.Visible=true;
						lblfpBankNo.Text=Path.GetFileName(fpBankNo.PostedFile.FileName);
						fpBankNo.Disabled=true;
					}
				}
				if(!fpBankDetail.Disabled)
				{
					if(this.fpBankDetail.PostedFile.ContentLength>0)
					{
						fpBankDetail.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\33"+Path.GetExtension(fpBankDetail.PostedFile.FileName)));
						lnBankDetail.Visible=true;
						lblfpBankDetail.Visible=true;
						lblfpBankDetail.Text=Path.GetFileName(fpBankDetail.PostedFile.FileName);
						fpBankDetail.Disabled=true;
					}
				}
				if(!fpPicture.Disabled)
				{
					if(this.fpPicture.PostedFile.ContentLength>0)
					{
						fpPicture.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\43"+Path.GetExtension(fpPicture.PostedFile.FileName)));
						lnPicture.Visible=true;
						lblfpPicture.Visible=true;
						lblfpPicture.Text=Path.GetFileName(fpPicture.PostedFile.FileName);
						fpPicture.Disabled=true;
					}
				}

			}
			if(IsPostBack && pnlEmployeeInfo.Visible)
			{
				
				if(!fpName.Disabled)
				{
					if(this.fpName.PostedFile.ContentLength>0)
					{
						fpName.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\7"+Path.GetExtension(fpName.PostedFile.FileName)));
						lnName.Visible=true;
						lblfpName.Visible=true;
						lblfpName.Text=Path.GetFileName(fpName.PostedFile.FileName);
						fpName.Disabled=true;
					}
				}
				/*if(!fpDesignation.Disabled)
				{
					if(this.fpDesignation.PostedFile.ContentLength>0)
					{
						fpDesignation.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\6"+Path.GetExtension(fpDesignation.PostedFile.FileName)));
						lnDesignation.Visible=true;
						lblfpDesignation.Visible=true;
						lblfpDesignation.Text=Path.GetFileName(fpDesignation.PostedFile.FileName);
						fpDesignation.Disabled=true;
					}
				}*/
				/*if(!fpDepartment.Disabled)
				{
					if(this.fpDepartment.PostedFile.ContentLength>0)
					{
						fpDepartment.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\10"+Path.GetExtension(fpDepartment.PostedFile.FileName)));
						lnDepartment.Visible=true;
						lblfpDepartment.Visible=true;
						lblfpDepartment.Text=Path.GetFileName(fpDepartment.PostedFile.FileName);
						fpDepartment.Disabled=true;
					}
				}*/
				/*if(!fpStation.Disabled)
				{
					if(this.fpStation.PostedFile.ContentLength>0)
					{
						fpStation.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\8"+Path.GetExtension(fpStation.PostedFile.FileName)));
						lnStation.Visible=true;
						lblfpStation.Visible=true;
						lblfpStation.Text=Path.GetFileName(fpStation.PostedFile.FileName);
						fpStation.Disabled=true;
					}
				}*/
				//				if(!fpDateofJoin.Disabled)
				//				{
				//					if(this.fpDateofJoin.PostedFile.ContentLength>0)
				//					{
				//						fpDateofJoin.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\9"+Path.GetExtension(fpDateofJoin.PostedFile.FileName)));
				//						lnDateOfJoin.Visible=true;
				//						lblfpDateofJoin.Visible=true;
				//						lblfpDateofJoin.Text=Path.GetFileName(fpDateofJoin.PostedFile.FileName);
				//						fpDateofJoin.Disabled=true;
				//					}
				//				}
				if(!fpEOBI.Disabled)
				{
					if(this.fpEOBI.PostedFile.ContentLength>0)
					{
						fpEOBI.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\13"+Path.GetExtension(fpEOBI.PostedFile.FileName)));
						lnEOBI.Visible=true;
						lblfpEOBI.Visible=true;
						lblfpEOBI.Text=Path.GetFileName(fpEOBI.PostedFile.FileName);
						fpEOBI.Disabled=true;
					}
				}
			}
			if(IsPostBack && pnlPersonalUPdate.Visible)
			{
				if(!fpDateofBirth.Disabled)
				{
					if(this.fpDateofBirth.PostedFile.ContentLength>0)
					{
						fpDateofBirth.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\16"+Path.GetExtension(fpDateofBirth.PostedFile.FileName)));
						lnDateofBirth.Visible=true;
						lblfpDateofBirth.Visible=true;
						lblfpDateofBirth.Text=Path.GetFileName(fpDateofBirth.PostedFile.FileName);
						fpDateofBirth.Disabled=true;
					}
				}
				if(!fpFatherName.Disabled)
				{
					if(this.fpFatherName.PostedFile.ContentLength>0)
					{
						fpFatherName.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\24"+Path.GetExtension(fpFatherName.PostedFile.FileName)));
						lnFatherName.Visible=true;
						lblfpFatherName.Visible=true;
						lblfpFatherName.Text=Path.GetFileName(fpFatherName.PostedFile.FileName);
						fpFatherName.Disabled=true;
					}
				}
				if(!fpMartialStatus.Disabled)
				{
					if(this.fpMartialStatus.PostedFile.ContentLength>0)
					{
						fpMartialStatus.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\25"+Path.GetExtension(fpMartialStatus.PostedFile.FileName)));
						lnMartialStatus.Visible=true;
						lblfpMaritalStatus.Visible=true;
						lblfpMaritalStatus.Text=Path.GetFileName(fpMartialStatus.PostedFile.FileName);
						fpMartialStatus.Disabled=true;
					}
				}
				if(!fpNationality.Disabled)
				{
					if(this.fpNationality.PostedFile.ContentLength>0)
					{
						fpNationality.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\35"+Path.GetExtension(fpNationality.PostedFile.FileName)));
						lnNationality.Visible=true;
						lblfpNationality.Visible=true;
						lblfpNationality.Text=Path.GetFileName(fpNationality.PostedFile.FileName);
						fpNationality.Disabled=true;
					}
				}
				if(!fpNationality2.Disabled)
				{
					if(this.fpNationality2.PostedFile.ContentLength>0)
					{
						fpNationality2.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\36"+Path.GetExtension(fpNationality2.PostedFile.FileName)));
						lnNationality2.Visible=true;
						lblfpNationality2.Visible=true;
						lblfpNationality2.Text=Path.GetFileName(fpNationality2.PostedFile.FileName);
						fpNationality2.Disabled=true;
					}
				}
				if(!fpPassport.Disabled)
				{
					if(this.fpPassport.PostedFile.ContentLength>0)
					{
						fpPassport.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\19"+Path.GetExtension(fpPassport.PostedFile.FileName)));
						lnPassport.Visible=true;
						lblfpPassprot.Visible=true;
						lblfpPassprot.Text=Path.GetFileName(fpPassport.PostedFile.FileName);
						fpPassport.Disabled=true;
					}
				}
				if(!fpNextofKin.Disabled)
				{
					if(this.fpNextofKin.PostedFile.ContentLength>0)
					{
						fpNextofKin.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\30"+Path.GetExtension(fpNextofKin.PostedFile.FileName)));
						lnNextofKin.Visible=true;
						lblfpNextofKin.Visible=true;
						lblfpNextofKin.Text=Path.GetFileName(fpNextofKin.PostedFile.FileName);
						fpNextofKin.Disabled=true;
					}
				}
				if(!fpNICNew.Disabled)
				{
					if(this.fpNICNew.PostedFile.ContentLength>0)
					{
						fpNICNew.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\21"+Path.GetExtension(fpNICNew.PostedFile.FileName)));
						lnNICNew.Visible=true;
						lblfpNICNew.Visible=true;
						lblfpNICNew.Text=Path.GetFileName(fpNICNew.PostedFile.FileName);
						fpNICNew.Disabled=true;
					}
				}
				if(!fpNICOld.Disabled)
				{
					if(this.fpNICOld.PostedFile.ContentLength>0)
					{
						fpNICOld.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\20"+Path.GetExtension(fpNICOld.PostedFile.FileName)));
						lnNICOld.Visible=true;
						lblNICOld.Visible=true;
						lblNICOld.Text=Path.GetFileName(fpNICOld.PostedFile.FileName);
						fpNICOld.Disabled=true;
					}
				}
				if(!fpBankNo.Disabled)
				{
					if(this.fpBankNo.PostedFile.ContentLength>0)
					{
						fpBankNo.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\32"+Path.GetExtension(fpBankNo.PostedFile.FileName)));
						lnBankNo.Visible=true;
						lblfpBankNo.Visible=true;
						lblfpBankNo.Text=Path.GetFileName(fpBankNo.PostedFile.FileName);
						fpBankNo.Disabled=true;
					}
				}
				if(!fpBankDetail.Disabled)
				{
					if(this.fpBankDetail.PostedFile.ContentLength>0)
					{
						fpBankDetail.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\33"+Path.GetExtension(fpBankDetail.PostedFile.FileName)));
						lnBankDetail.Visible=true;
						lblfpBankDetail.Visible=true;
						lblfpBankDetail.Text=Path.GetFileName(fpBankDetail.PostedFile.FileName);
						fpBankDetail.Disabled=true;
					}
				}
				if(!fpPicture.Disabled)
				{
					if(this.fpPicture.PostedFile.ContentLength>0)
					{
						fpPicture.PostedFile.SaveAs(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\43"+Path.GetExtension(fpPicture.PostedFile.FileName)));
						lnPicture.Visible=true;
						lblfpPicture.Visible=true;
						lblfpPicture.Text=Path.GetFileName(fpPicture.PostedFile.FileName);
						fpPicture.Disabled=true;
					}
				}
			 
			}
			//=========================================================================//
			if(!IsPostBack)
			{
				//this.info.Visible=false;
				if(!System.IO.Directory.Exists(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString())))
				{
					Directory.CreateDirectory(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()));
				}
				Session["pendingRequest"]="";
				Session["cRequest"]="";
				Session["rRequest"]="";
				Session["rpRequest"]="";
				if(Request.QueryString.Count<=0)
				{
					this.btnUpdate.Text="Submit Employement Request";
				}
				ibMySelf.Attributes.Add("onclick","window.open('myinfo.aspx','MyProfile','toolbar=no,statusbar=no,addressbar=no,scrollbars=yes,resizable=no,width=700,height=650');return false;");
				btnPrint.Attributes.Add("onclick","return PopUpSlip();");
				this.imgInfo.Attributes.Add("onmousemove","displayDiv();");
				this.imgInfo.Attributes.Add("onmouseout","disableDiv();");

				
				//===============================Employee Info ToolTips============================//
				this.lbltName.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Name</font></b><br/><br/>"+GetTooltip(7)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltName.Attributes.Add("onmouseout","UnTip()");
				this.lbltDesignation.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Designation</font></b><br/><br/>"+GetTooltip(6)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltDesignation.Attributes.Add("onmouseout","UnTip()");
				this.lbltDepartment.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Department</font></b><br/><br/>"+GetTooltip(10)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltDepartment.Attributes.Add("onmouseout","UnTip()");
				this.lbltStation.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Station</font></b><br/><br/>"+GetTooltip(8)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltStation.Attributes.Add("onmouseout","UnTip()");
				
				this.lbltDateofJoin.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Date of Join</font></b><br/><br/>"+GetTooltip(9)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltDateofJoin.Attributes.Add("onmouseout","UnTip()");
				
				this.lbltExtension.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Extension</font></b><br/><br/>"+GetTooltip(11)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltExtension.Attributes.Add("onmouseout","UnTip()");
				this.lbltEOBI.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>EOBI #</font></b><br/><br/>"+GetTooltip(13)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltEOBI.Attributes.Add("onmouseout","UnTip()");
				
				this.lbltEmailofficial.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Email (Official)</font></b><br/><br/>"+GetTooltip(31)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltEmailofficial.Attributes.Add("onmouseout","UnTip()");
				this.lbltSessi.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>SESSI #</font></b><br/><br/>"+GetTooltip(12)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltSessi.Attributes.Add("onmouseout","UnTip()");
				this.lbltWorkstation.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Workstation Address</font></b><br/><br/>"+GetTooltip(14)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltWorkstation.Attributes.Add("onmouseout","UnTip()");
				//===============================================================================//

				//=============================Personal Info ToolTips============================//
				this.lbltDateofBirth.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Date of Birth</font></b><br/><br/>"+GetTooltip(16)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltDateofBirth.Attributes.Add("onmouseout","UnTip()");
				this.lbltFatherName.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Father Name</font></b><br/><br/>"+GetTooltip(24)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltFatherName.Attributes.Add("onmouseout","UnTip()");
				this.lbltMaritalStatus.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Marital Status</font></b><br/><br/>"+GetTooltip(25)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltMaritalStatus.Attributes.Add("onmouseout","UnTip()");
				this.lbltTelephoneNo.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Residential Number</font></b><br/><br/>"+GetTooltip(27)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltTelephoneNo.Attributes.Add("onmouseout","UnTip()");
				this.lbltMobileNo.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Mobile Number</font></b><br/><br/>"+GetTooltip(28)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltMobileNo.Attributes.Add("onmouseout","UnTip()");
				this.lbltNationality.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Nationality</font></b><br/><br/>"+GetTooltip(35)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltNationality.Attributes.Add("onmouseout","UnTip()");
				this.lbltNationality2.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Nationality (Seconday)</font></b><br/><br/>"+GetTooltip(36)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltNationality2.Attributes.Add("onmouseout","UnTip()");
				this.lbltPassport.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Passport Number</font></b><br/><br/>"+GetTooltip(19)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltPassport.Attributes.Add("onmouseout","UnTip()");
				this.lbltNextofKin.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Next of Kin</font></b><br/><br/>"+GetTooltip(30)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltNextofKin.Attributes.Add("onmouseout","UnTip()");
				this.lbltNICNew.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>NIC (New)</font></b><br/><br/>"+GetTooltip(21)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltNICNew.Attributes.Add("onmouseout","UnTip()");
				this.lbltNICOld.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>NIC (Old)</font></b><br/><br/>"+GetTooltip(20)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltNICOld.Attributes.Add("onmouseout","UnTip()");
				this.lbltBankNo.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Bank Account Number</font></b><br/><br/>"+GetTooltip(32)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltBankNo.Attributes.Add("onmouseout","UnTip()");
				this.lbltBankDetail.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Bank Account Detail</font></b><br/><br/>"+GetTooltip(33)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltBankDetail.Attributes.Add("onmouseout","UnTip()");
				
				this.lbltGender.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Gender</font></b><br/><br/>"+GetTooltip(17)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltGender.Attributes.Add("onmouseout","UnTip()");
				this.lbltBloodGroup.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Blood Group</font></b><br/><br/>"+GetTooltip(18)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltBloodGroup.Attributes.Add("onmouseout","UnTip()");
				this.lbltReligion.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Religion</font></b><br/><br/>"+GetTooltip(26)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltReligion.Attributes.Add("onmouseout","UnTip()");
				this.lbltNick.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Nick</font></b><br/><br/>"+GetTooltip(37)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltNick.Attributes.Add("onmouseout","UnTip()");
				this.lbltAddress.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Address</font></b><br/><br/>"+GetTooltip(22)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltAddress.Attributes.Add("onmouseout","UnTip()");
				this.lbltEmailPersonal.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Email (Personal)</font></b><br/><br/>"+GetTooltip(29)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltEmailPersonal.Attributes.Add("onmouseout","UnTip()");
				this.lbltNTN.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>NTN</font></b><br/><br/>"+GetTooltip(34)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltNTN.Attributes.Add("onmouseout","UnTip()");
                this.lbltPicture.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Picture</font></b><br/><br/>"+GetTooltip(43)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				this.lbltPicture.Attributes.Add("onmouseout","UnTip()");


				//===============================================================================//

				pnlPaySlip.Visible=false;
				string var="";
				string getFamily=
					"select name,relationship = case relationship when '2' then 'Husband' " + 
					" when '3' then 'Wife' " + 
					" when '4' then 'Son' " + 
					" when '5' then 'Daughter' " + 
					" END, " + 
					" maritialstatus=case maritialstatus when '1' then 'Single' " + 
					" when '2' then 'Married' " + 
					" when '3' then 'Divorced' " + 
					" when '4' then 'Widow' " +
					" when '5' then 'Separated' " + 
					" END, " + 
					" gender=case gender WHEN '1' THEN 'Male' " + 
					" when '2' then 'Female' " + 
					" END ,occupation,DOB,dependent=case dependent when '1' then 'Yes' when '2' then 'No' end,pcode,relationship as relationshipid,maritialstatus as statusid,dependent as dependentid,fdid as fdid " + 
					" from t_familydetails where isactive='1' and pcode='" + var + "' And relationship in(2,3,4,5)";
				SqlDataAdapter rd=new SqlDataAdapter(getFamily,con);
				if(Session["dFamily"].ToString()=="")
				{
					dFamily=new DataSet();
					Session["dFamily"]=dFamily;
				}
				else
				{
					dFamily=(DataSet)Session["dFamily"];
					dFamily.Tables[0].Clear();
				}
				rd.Fill(dFamily,"FamilyInfo");					
				//this.btnSubmitFamily.Visible=false;
				//this.btnSubmitEducation.Visible=false;
				this.Label2.Visible=false;
				this.Label1.Visible=false;
				
				/*con.Open();
				string getDegree="select id,degree from t_degree where isactive='1' order by degree";
				SqlCommand cmDegree=new SqlCommand(getDegree,con);
				SqlDataReader rdDegree=cmDegree.ExecuteReader();
		
				ListItem it=new ListItem();
				int i=1;
				while(rdDegree.Read())
				{
					ListItem itm=new ListItem();
					itm.Value=rdDegree[0].ToString();
					itm.Text=rdDegree[1].ToString();
					this.txtDegree.Items.Insert(i,itm);
					i++;
				}
				rdDegree.Close();
				it=new ListItem("Other","-1");
				this.txtDegree.Items.Add(it);
 
				string getInstitute="select id,institute_name from t_institute where isactive='1' order by institute_name";
				SqlCommand cmInstitute=new SqlCommand(getInstitute,con);
				SqlDataReader rdInstitute=cmInstitute.ExecuteReader();
			
				ListItem item2=new ListItem();
				//				item2.Value="0";
				//				item2.Text="Select--Institute";
				//				this.txtInstitute.Items.Insert(0,item2);
				int j=1;
				while(rdInstitute.Read())
				{
					ListItem itm=new ListItem();
					itm.Value=rdInstitute[0].ToString();
					itm.Text=rdInstitute[1].ToString();
					this.txtInstitute.Items.Insert(j,itm);
					j++;
				}
				rdInstitute.Close();
				item2=new ListItem("Other","-1");
				this.txtInstitute.Items.Add(item2);
				con.Close();  
				EducationRequest();*/
				EmployeeInfo();
				GetEployeeCounter();		//  Check visiting counter
				PersonalInfo();
				//FamilyInfo();
				//EducationalInfo();
				//getYears();
				//				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"FamilyInfo"))
				//				{
				//					this.Label2.Visible=true;
				//				}
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"EmpInfo") && this.pnlEmployeeInfo.Visible)
				{
					this.btnUpdate.Visible=true;
				}
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"PerInfo") && this.pnlPersonal.Visible)
				{
					this.btnUpdate.Visible=true;
				}
				//this.txtServerDate.Text=Count();
				this.lblDisName.Text=this.txtEmpInfoName.Text;
				this.ImageButton5.Attributes.Add("onclick","window.open('admin/organogram.htm','_blank','resizable=yes,menubar=no,scrollbars=yes,top=0,status=no');return false;");
				//this.imgMyLeave.Attributes.Add("onclick","window.open('MyLeaveBalance.aspx','_blank','resizable=no,menubar=no,scrollbars=no,top=0,status=no');return false;");
				this.imgMyLeave.Attributes.Add("onclick","return OpenURL('MyLeaveBalance.aspx',650,300);");
				this.ImgMyAttendance.Attributes.Add("onclick","return OpenURL('MyAttendance.aspx',700,400);");
				this.btnUpdate.Attributes.Add("onclick","return checkValue();");
			}
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.ImageButton1.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton1_Click);
			this.ImageButton2.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton2_Click);
			this.ImageButton3.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton3_Click);
			this.ImageButton4.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton4_Click);
			this.imgTraining.Click += new System.Web.UI.ImageClickEventHandler(this.imgTraining_Click);
			this.ImageButton5.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton5_Click);
			this.ibSalary.Click += new System.Web.UI.ImageClickEventHandler(this.ibSalary_Click);
			this.imgMyGrievance.Click += new System.Web.UI.ImageClickEventHandler(this.imgMyGrievance_Click);
			this.lnDateofBirth.Click += new System.EventHandler(this.lnDateofBirth_Click);
			this.lnFatherName.Click += new System.EventHandler(this.lnFatherName_Click);
			this.lnMartialStatus.Click += new System.EventHandler(this.lnMartialStatus_Click);
			this.ddNationality.SelectedIndexChanged += new System.EventHandler(this.ddNationality_SelectedIndexChanged);
			this.CustomValidator1.ServerValidate += new System.Web.UI.WebControls.ServerValidateEventHandler(this.CustomValidator9_ServerValidate);
			this.lnNationality.Click += new System.EventHandler(this.lnNationality_Click);
			this.lnNationality2.Click += new System.EventHandler(this.lnNationality2_Click);
			this.lnPassport.Click += new System.EventHandler(this.lnPassport_Click);
			this.lnNextofKin.Click += new System.EventHandler(this.lnNextofKin_Click);
			this.lnNICNew.Click += new System.EventHandler(this.lnNICNew_Click);
			this.lnNICOld.Click += new System.EventHandler(this.lnNICOld_Click);
			this.lnBankNo.Click += new System.EventHandler(this.lnBankNo_Click);
			this.lnBankDetail.Click += new System.EventHandler(this.lnBankDetail_Click);
			this.lnName.Click += new System.EventHandler(this.lnName_Click);
			this.ddDepartment.SelectedIndexChanged += new System.EventHandler(this.ddDepartment_SelectedIndexChanged);
			this.CustomValidator2.ServerValidate += new System.Web.UI.WebControls.ServerValidateEventHandler(this.CustomValidator9_ServerValidate);
			this.ddDesignation.SelectedIndexChanged += new System.EventHandler(this.ddDesignation_SelectedIndexChanged);
			this.CustomValidator3.ServerValidate += new System.Web.UI.WebControls.ServerValidateEventHandler(this.CustomValidator9_ServerValidate);
			this.lnEOBI.Click += new System.EventHandler(this.lnEOBI_Click);
			this.btnUpdate.Click += new System.EventHandler(this.btnUpdate_Click);
			this.btnIgnore.Click += new System.EventHandler(this.btnIgnore_Click);
			this.btnResend.Click += new System.EventHandler(this.btnResend_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		public string GetTooltip(int ID)
		{
			con.Open();
			SqlCommand cmd=new SqlCommand("select tooltip from t_fieldsmgt where f_id="+ID+"",con);
			string message="";
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				message=rd[0].ToString();	
			}
			message=HttpUtility.HtmlEncode(message);
			message=message.Replace("\r\n","<br/>");
			message+="<br>";
			rd.Close();
			cmd=new SqlCommand("select d.documentname from t_documentsrequired d,t_fieldmgtdocEmp f where f.documentbyemployee=d.d_id and f.f_id="+ID+"",con);
			rd=cmd.ExecuteReader();
			message+="<ul>";
			while(rd.Read())
			{
				//message+="<STRONG>/STRONG> "+rd[0].ToString()+"<br>";
				message+="<li> "+ HttpUtility.HtmlEncode(rd[0].ToString()).Replace("\r\n","<br/>")+"</li>";
			}
			message+="</ul>";
			rd.Close();
			con.Close();
			//Response.Write(message);
			return message;
		}
		private void ImageButton2_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("UserProfile.aspx?id=2");
			lblCombine.Visible=false;
			lblCompare.Visible=false;
			lblEmp.Visible=false;
			//this.lblEducationMsg.Visible=false;
			//this.lblFamilyMsg.Visible=false;
			this.Label1.Visible=false;
			this.Label2.Visible=false;
			this.pnlPersonal.Visible=true;
			this.pnlEmployeeInfo.Visible=false;
			//this.pnlFamilyInfo.Visible=false;
			//this.pnlEducation.Visible=false;
			if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"PerInfo"))
				this.btnUpdate.Visible=true;
			pnlPaySlip.Visible=false;
			this.btnUpdate.Text="Submit Personal Request";
			//PersonalInfo();
		}

		private void ImageButton1_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("UserProfile.aspx?id=1");
			lblCombine.Visible=false;
			lblCompare.Visible=false;
			lblEmp.Visible=false;
			//this.lblEducationMsg.Visible=false;
			//this.lblFamilyMsg.Visible=false;
			this.Label1.Visible=false;
			this.Label2.Visible=false;
			this.pnlPersonal.Visible=false;
			this.pnlEmployeeInfo.Visible=true;
			//this.pnlFamilyInfo.Visible=false;
			//this.pnlEducation.Visible=false;
			if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"EmpInfo"))
				this.btnUpdate.Visible=true;
			pnlPaySlip.Visible=false;
			this.btnUpdate.Text="Submit Employement Request";
			
			//EmployeeInfo();
		}

		private void ImageButton3_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("EmpFamily.aspx?pcode="+this.lblPCode.Text+"&name="+this.lblEmpInfo.Text);
			pnlPaySlip.Visible=false;
			lblCombine.Visible=false;
			lblCompare.Visible=false;
			lblEmp.Visible=false;
			//this.lblEducationMsg.Visible=false;
			//this.lblFamilyMsg.Text="There are no current requestes"; 
			//this.lblFamilyMsg.Visible=true;
			this.ValidationSummary1.Visible=false;
			this.Label1.Visible=false;
			//			if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"FamilyInfo"))
			//			{
			//				this.Label2.Visible=true;
			//			}
			// this.Image1.Visible=false;
			//this.pnlFamilyInfo.Visible=true;
			//this.DataGrid1.Visible=true;
			this.pnlPersonal.Visible=false;
			this.pnlEmployeeInfo.Visible=false;
			//this.pnlEducation.Visible=false;
			this.btnUpdate.Visible=false;
			//FamilyInfo();
			
		}

		public void PersonalInfo()
		{
			con.Open();
			this.ddBloodGrp.Visible=true;
			string Id=Session["user_id"].ToString();
			
			string str="select * from view_Nationality";
			SqlCommand cmd=new SqlCommand(str,con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem(rd[0].ToString(),rd[1].ToString());
				this.ddNationality.Items.Add(itm);
			}
			rd.Close();

			//string getData="select e.pcode,e.dateofbirth,e.gender,e.bloodgroup,e.fathername,e.passportno,e.address,e.maritialstatus,e.email_personal,e.religion,e.telephone,e.mobile,e.nic_new,e.nic_old,e.kin,e.bankaccountno,e.bankaccountdetails,e.ntnno,e.nick,nationality=case e.pcode when e.pcode then(select c.nationality from t_country c where c.countryid=e.nationality)end,nationalityid=case e.pcode when e.pcode then(select c.countryid from t_country c where c.countryid=e.nationality)end from t_employee e where e.pcode='"+Id+"'";
			string getData="select e.pcode,e.dateofbirth,e.gender,e.bloodgroup,e.fathername,e.passportno,e.address,e.maritialstatus,e.email_personal,e.religion,e.telephone,e.mobile,e.nic_new,e.nic_old,e.kin,e.bankaccountno,e.bankaccountdetails,e.ntnno,e.nick,nationality=case e.pcode when e.pcode then(select c.nationality from t_country c where c.countryid=e.nationality)end,nationalityid=case e.pcode when e.pcode then(select c.countryid from t_country c where c.countryid=e.nationality)end,nationality2=case e.pcode when e.pcode then(select c.nationality from t_country c where c.countryid=e.nationality2)end,nationalityid2=case e.pcode when e.pcode then(select c.countryid from t_country c where c.countryid=e.nationality2)end,e.operatingcity from t_employee e where e.pcode='"+Id+"'";
			
			cmd=new SqlCommand(getData,con);
			rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				//this.lblEmpCode.Text=rd[0].ToString();
				//this.txtEmpCode.Text=rd[0].ToString();
 
				
				if(rd[1].ToString().Length>0)
				{
					//string []tempArr=rd[1].ToString().Split(' ');
					this.lblName.Text=DateTime.Parse(rd[1].ToString()).ToString("d MMM,yyyy");
					this.calendarDOB.SelectedDate=Convert.ToDateTime(rd[1].ToString());
					this.calendarDOB.DataBind();
				}
				
				if(rd[2].ToString()!="")
				{
					this.RadioButtonList1.SelectedValue=rd[2].ToString();
					if(rd[2].ToString()=="1")
					{
						//this.RadioButtonList1.Items[0].Selected=true;
						this.lblGender.Text="Male";
					}
					if(rd[2].ToString()=="2")
					{
						this.lblGender.Text="Female";
						//this.RadioButtonList1.Items[1].Selected=true;
					}
					
				}
				
				
				if(rd[3].ToString()!="")
				{
					if(rd[3].ToString()=="0")
					{
						this.ddBloodGrp.SelectedValue="0";
					}
					if(rd[3].ToString()=="1")
					{
						this.ddBloodGrp.SelectedValue=rd[3].ToString();
						this.lblBloodGrp.Text="A+";
					}
					if(rd[3].ToString()=="2")
					{
						this.ddBloodGrp.SelectedValue=rd[3].ToString();
						this.lblBloodGrp.Text="A-";
					}
					if(rd[3].ToString()=="3")
					{
						this.ddBloodGrp.SelectedValue=rd[3].ToString();
						this.lblBloodGrp.Text="B+";
					}
					if(rd[3].ToString()=="4")
					{
						this.ddBloodGrp.SelectedValue=rd[3].ToString();
						this.lblBloodGrp.Text="B-";
					}
					if(rd[3].ToString()=="5")
					{
						this.ddBloodGrp.SelectedValue=rd[3].ToString();
						this.lblBloodGrp.Text="AB+";
					}
					if(rd[3].ToString()=="6")
					{
						this.ddBloodGrp.SelectedValue=rd[3].ToString();
						this.lblBloodGrp.Text="AB-";
					}
					if(rd[3].ToString()=="7")
					{
						this.ddBloodGrp.SelectedValue=rd[3].ToString();
						this.lblBloodGrp.Text="O+";
					}
					if(rd[3].ToString()=="8")
					{
						this.ddBloodGrp.SelectedValue=rd[3].ToString();
						this.lblBloodGrp.Text="O-";
					}
				}
				

				this.lblFName.Text=rd[4].ToString();
				this.txtFName.Text=rd[4].ToString();

				this.lblEmail.Text=rd[8].ToString();
				this.txtEmail.Text=rd[8].ToString();

				this.lblPassport.Text=rd[5].ToString();
				this.txtPassport.Text=rd[5].ToString();              
 
				this.lblcontactNo.Text=rd[10].ToString();
				this.txtContactNo.Text=rd[10].ToString();
 
				this.lblMobileNo.Text=rd[11].ToString();
				this.txtMobile.Text=rd[11].ToString();
			 
				this.lblNICNew.Text=rd[12].ToString();
				this.txtNewNic.Text=rd[12].ToString();

				this.lblNICOld.Text=rd[13].ToString();
				this.txtOldNic.Text=rd[13].ToString();

				this.lblAddress.Text=rd[6].ToString();
				this.txtAddress.Text=rd[6].ToString();

				
				
				if(rd[7].ToString()!="")
				{
					if(rd[7].ToString()=="0")
					{
						this.ddStatus.SelectedValue="0";
					}
					if(rd[7].ToString()=="1")
					{
						this.ddStatus.SelectedValue=rd[7].ToString();
						this.lblMaritialStat.Text="Single";
					}
					if(rd[7].ToString()=="2")
					{
						this.ddStatus.SelectedValue=rd[7].ToString();
						this.lblMaritialStat.Text="Married";
					}
					if(rd[7].ToString()=="3")
					{
						this.ddStatus.SelectedValue=rd[7].ToString();
						this.lblMaritialStat.Text="Divorced";
					}
				}

				
				if(rd[9].ToString()!="")
				{
					if(rd[9].ToString()=="0")
					{
						this.ddReligion.SelectedValue="0";
					}
					if(rd[9].ToString()=="1")
					{
						this.ddReligion.SelectedValue=rd[9].ToString();
						this.lblReligion.Text="Islam";
					}
					if(rd[9].ToString()=="2")
					{
						this.ddReligion.SelectedValue=rd[9].ToString();
						this.lblReligion.Text="Christianity";
					}
					if(rd[9].ToString()=="3")
					{
						this.ddReligion.SelectedValue=rd[9].ToString();
						this.lblReligion.Text="Buddhism";
					}
					if(rd[9].ToString()=="4")
					{
						this.ddReligion.SelectedValue=rd[9].ToString();
						this.lblReligion.Text="Zoroastrian";
					}
					if(rd[9].ToString()=="5")
					{
						this.ddReligion.SelectedValue=rd[9].ToString();
						this.lblReligion.Text="Jewish";
					}
					if(rd[9].ToString()=="6")
					{
						this.ddReligion.SelectedValue=rd[9].ToString();
						this.lblReligion.Text="Hinduism";
					}
					if(rd[9].ToString()=="7")
					{
						this.ddReligion.SelectedValue=rd[9].ToString();
						this.lblReligion.Text="Others";
					}
				}
				

				this.lblKin.Text=rd[14].ToString();
				this.txtKin.Text=rd[14].ToString();

				this.lblBankAcctNo.Text=rd[15].ToString();
				this.txtBankAcctNo.Text=rd[15].ToString();

				this.lblAccountDetails.Text=rd[16].ToString();
				this.txtAccountDetails.Text=rd[16].ToString();

				this.lblNTN.Text=rd[17].ToString();
				this.txtNTN.Text=rd[17].ToString();

				this.lblNick.Text=rd[18].ToString();
				this.txtNick.Text=rd[18].ToString();
				this.lblNationality.Text=rd[19].ToString();
				this.lblNationality2.Text=rd[21].ToString();
				this.lblEmpLocation.Text=rd[23].ToString();
				this.txtEmpLocation.Text=rd[23].ToString();
				try
				{
					this.ddNationality.SelectedValue=rd[20].ToString();
				}
				catch(Exception ){}
				finally
				{
					setNationality();
					try
					{
						this.ddNationality2.SelectedValue=rd[22].ToString();
					}
					catch(Exception ){}
				}
			}
			rd.Close();
			con.Close();
			
		}

		public void EmployeeInfo()
		{
			// fill active departments
			con.Open();
			string getDept="select deptid,deptname from t_department where status='"+1+"'";
			SqlCommand cm=new SqlCommand(getDept,con);
			SqlDataReader r=cm.ExecuteReader();
			int x=1;	
			while(r.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=r[0].ToString();
				itm.Text=r[1].ToString();
				this.ddDepartment.Items.Insert(x,itm);
				x++;
			}
			r.Close();
			con.Close();

			// fill cities
			con.Open();
			string getCity="select cityid,cityname from t_city where status='1'";
			SqlCommand cCity=new SqlCommand(getCity,con);
			SqlDataReader rCity=cCity.ExecuteReader();
			int b=1;
			while(rCity.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rCity[0].ToString();
				itm.Text=rCity[1].ToString();
				this.ddStation.Items.Insert(b,itm);
				b++; 
			}
			rCity.Close();
			con.Close();
           
			con.Open();
			string getEmpInfo="";
			string filename="";
			string empId=Session["user_id"].ToString();
			//===========================================================//
       
			getEmpInfo="SELECT pcode, name, extension, dateofjoin, insurancecode, dateofconfirmation, typeofemployment, bondpaper, email_official, eobino, sessino, others,pic,desigid,compansatoryoff "+
				"FROM dbo.t_Employee WHERE     (pcode = '"+empId+"')";
			SqlCommand cmd=new SqlCommand(getEmpInfo,con);
			SqlDataReader rd=cmd.ExecuteReader();
			string desigId="";
			while(rd.Read())
			{
				
				//this.txtEmpInfoCode.Text=rd[0].ToString();
				lblPCode.Text=rd[0].ToString();

				this.txtEmpInfoName.Text=rd[1].ToString();
				this.lblEmpInfoName.Text=rd[1].ToString();  
				this.lblEmpInfo.Text=rd[1].ToString(); 
                  
				this.txtEmpInfoExtension.Text=rd[2].ToString();
				this.lblEmpInfoExtension.Text=rd[2].ToString();

				if(rd[3].ToString().Length>0)
				{
					this.CalenderDOJ.SelectedDate=Convert.ToDateTime(rd[3].ToString());
					this.CalenderDOJ.DataBind();
					//string []tempArr =rd[3].ToString().Split(' ');
					this.lblEmpInfoDOJ.Text=DateTime.Parse(rd[3].ToString()).ToString("d MMM,yyyy");
				}
				//this.txtEmpInfoInsurance.Text=rd[4].ToString();
				//this.lblEmpInfoInsurance.Text=rd[4].ToString();

				if(rd["dateofconfirmation"].ToString().Length>0)
				{
					this.lblEmpInfoDOC.Text=DateTime.Parse(rd[5].ToString()).ToString("d MMM,yyyy");
				}

				if(rd[6].ToString()!="")
				{
					
					if(rd[6].ToString()=="1")
					{
						this.lblEmpInfoTOE.Text="Permanent";
					}
					if(rd[6].ToString()=="2")
					{
						this.lblEmpInfoTOE.Text="Contractual";
					}
					if(rd[6].ToString()=="3")
					{
						this.lblEmpInfoTOE.Text="Retainer";
					}
					ListItem itm=new ListItem();
					itm.Value=rd[6].ToString();
					itm.Text=this.lblEmpInfoTOE.Text;
					
				}
				
				this.txtEmpInfoEmail.Text=rd[8].ToString();
				this.lblEmpInfoEmail.Text=rd[8].ToString();
      
				this.txtEmpInfoEOBI.Text=rd[9].ToString();
				this.lblEmpInfoEObi.Text=rd[9].ToString();

				this.txtEmpInfoSESSI.Text=rd[10].ToString();
				this.lblEmpInfoSessi.Text=rd[10].ToString();
 							
				//				if(rd[14].ToString()!="")
				//				{
				//					if(rd[14].ToString()=="1")
				//					{
				//						this.lblEmpInfoComp.Text="Yes";
				//					}
				//					if(rd[14].ToString()=="2")
				//					{
				//						this.lblEmpInfoComp.Text="No";
				//					}
				//				}
				
				//this.Image1.ImageUrl=@"employee\"+rd[12].ToString();
				filename=rd[12].ToString();
				desigId=rd[13].ToString();
				
			}
			rd.Close();
			
			
			string getStation2="select c.cityid,c.cityname from t_employee e,t_city c where e.station=c.cityid And e.pcode='"+empId+"' And e.del='"+1+"'";
			SqlCommand ccCity2=new SqlCommand(getStation2,con);
			SqlDataReader rrCity2=ccCity2.ExecuteReader();
			rrCity2.Read();
			if(rrCity2.HasRows)
			{
				this.ddStation.SelectedValue=rrCity2[0].ToString(); 
				this.lblEmpInfoCity.Text=rrCity2[1].ToString();
			}
			rrCity2.Close();

							
			string getDeptt="select dept.deptid,dept.deptname from t_employee e ,t_designation d,t_department dept  where e.desigid=d.desigid And e.pcode='"+empId+"' And dept.deptid=d.deptid";
			SqlCommand cDept=new SqlCommand(getDeptt,con);
			SqlDataReader dDept=cDept.ExecuteReader();
			dDept.Read();
			if(dDept.HasRows)
			{
				this.ddDepartment.SelectedValue=dDept[0].ToString();
				lnlEmpInfoDept.Text=this.ddDepartment.SelectedItem.Text;
				
			}
			dDept.Close();
			con.Close();
			getDes();
			try
			{
				this.ddDesignation.SelectedValue=desigId;
				//string []tempArr=this.ddDesignation.SelectedItem.Text.Split(':');
				lblEmpInfoDesignation.Text=this.ddDesignation.SelectedItem.Text;
				//lblEmpInfoDesignation.Text=tempArr[0];
				//tempArr=tempArr[1].Split(' ');
				con.Open();
				cmd=new SqlCommand("Select c.cat_name from t_categorization c,t_designation d where d.category=c.cat_id and d.desigid="+this.ddDesignation.SelectedValue.ToString()+"",con);
				rd=cmd.ExecuteReader();
				rd.Read();
				if(rd.HasRows)
				{
					lblEmpInfoCategory.Text=rd[0].ToString();
				}
				rd.Close();
				con.Close();
				
			}
			catch(Exception ex ){Response.Write(ex.Message);}
			getFunctional();
			CompensatoryStatus();
			/*con.Open();
			string getEmpDes="select dept.deptname,d.designation,f.functionaltitle from t_department dept,t_designation d,t_functionaldesignation f where dept.status=1 and d.status=1 And f.isactive=1  And d.deptid=dept.deptid And f.functionaldesignation=d.desigid And f.fdesigid=(select fdesigid from t_designationhistory where pcode='"+ empId +"' And isactive=1 )";
			cmd=new SqlCommand(getEmpDes,con);
			rd=cmd.ExecuteReader();
			while(rd.Read())
			{
			 //lnlEmpInfoDept.Text=rd[0].ToString();
			 //lblEmpInfoDesignation.Text=rd[1].ToString();
			 lblFunctionalDesignation.Text=rd[2].ToString();
			}
			con.Close();*/
		}

		public void CompensatoryStatus()
		{
			if(con.State==ConnectionState.Open)
			{
				con.Close();
			}
			con.Open();
			SqlCommand cmd=new SqlCommand("select d.desigid,d.designation,c.cat_name,compensatoryoff=case c.compensatoryoff when '1' then 'Yes' when '0' then 'No' else 'Not Defined'end from t_categorization c,t_designation d where d.category=c.cat_id and d.status=1 and d.desigid="+this.ddDesignation.SelectedValue.ToString()+"",con);
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			this.lblEmpInfoComp.Text=rd[3].ToString();
			rd.Close();
			con.Close();
		}
		public void getFunctional()
		{
			con.Open();
			this.ddFunctionalDesignation.Items.Clear();
			ListItem item=new ListItem();
			item.Value="0";
			item.Text="Select--Functional Designation";
			this.ddFunctionalDesignation.Items.Add(item);
			string getFunDes="select f.fdesigid,f.functionaltitle from t_functionaldesignation f,t_designationhistory d where f.functionaldesignation='"+ this.ddDesignation.SelectedValue.ToString() +"' And f.fdesigid=d.fdesigid And d.pcode='"+Session["user_id"].ToString()+"' And d.isactive=1 ";
			SqlCommand cmd=new SqlCommand(getFunDes,con);
			SqlDataReader rd=cmd.ExecuteReader();
			
			while(rd.Read())
			{
				ListItem itm=new ListItem();
				Session["FunctionalId"]=rd[0].ToString();
				itm.Value=rd[0].ToString();
				itm.Text=rd[1].ToString();
				itm.Selected=true;
				this.ddFunctionalDesignation.Items.Add(itm);
				lblFunctionalDesignation.Text=itm.Text;
			}
			rd.Close();
			con.Close();
			if(lblFunctionalDesignation.Text.Length>0)
			{
				this.imgInfo.Visible=false;
			}
			else
			{
				this.imgInfo.Visible=true;
				con.Open();
				string bName="";
				string bExtension="";
				string bEmail="";
				string hName="";
				string hExtension="";
				string hEmail="";
				string getBUManager="select e.name,e.extension,e.email_official from t_bumanagers b,t_employee e where b.isactive =1 And e.del=1 And e.pcode=b.pcode And b.deptid=Any(select dept.deptid from t_department dept,t_designation d where d.status=1 And dept.status=1 And dept.deptid=d.deptid And d.desigid=(select e.desigid from t_employee e where e.pcode='"+ Session["user_id"].ToString() +"'))";
				string getHeadOfDept="select e.name,e.extension,e.email_official from t_headofdept h,t_employee e where e.del=1 And e.pcode=h.pcode And h.isactive=1 And h.deptid=Any(select dept.deptid from t_department dept,t_designation d where d.status=1 And dept.status=1 And dept.deptid=d.deptid And d.desigid=(select e.desigid from t_employee e where e.pcode='"+ Session["user_id"].ToString() +"'))";
				cmd=new SqlCommand(getBUManager,con);
				rd=cmd.ExecuteReader();
				while(rd.Read())
				{
					bName=rd[0].ToString();
					bExtension=rd[1].ToString();
					bEmail=rd[2].ToString();
				}
				rd.Close();
				cmd=new SqlCommand(getHeadOfDept,con);
				rd=cmd.ExecuteReader();
				while(rd.Read())
				{
					hName=rd[0].ToString();
					hExtension=rd[1].ToString();
					hEmail=rd[2].ToString();
				}
				rd.Close();
				con.Close();
				string message="You do not have functional designation.Please contact your BU Manager OR Departmental Head. ";
				if(bName.Length>0)
				{
					message+="\n <b>BU-Manager:</b> Name "+bName;
				}
				if(bExtension.Length>0)
				{
					message+=",Extension "+bExtension;
				}
				if(bEmail.Length>0)
				{
					message+=",Email "+bEmail;
				}
				if(hName.Length>0)
				{
					message+="\n <b> Head Of Dept:</b> Name "+hName;
				}
				if(hExtension.Length>0)
				{
					message+=",Extension "+hExtension;
				}
				if(hEmail.Length>0)
				{
					message+=",Email "+hEmail;
				}
				this.TextBox1.Text=message;
			}
		}
		/*public void FamilyInfo()
		{
			con.Open();
			//string getFamily="select name,relationship,maritialstatus,gender,occupation,pcode,fdid from t_familydetails where isactive='1'";
			string empId=Session["user_id"].ToString();
			//Request.QueryString["id"].ToString();
			string getFamily=
				"select name,relationship = case relationship when '2' then 'Husband' " + 
				" when '3' then 'Wife' " + 
				" when '4' then 'Son' " + 
				" when '5' then 'Daughter' " + 
				" END, " + 
				" maritialstatus=case maritialstatus when '1' then 'Single' " + 
				" when '2' then 'Married' " + 
				" when '3' then 'Divorced' " + 
				" when '4' then 'Widow' " +
				" when '5' then 'Separated' " + 
				" END, " + 
				" gender=case gender WHEN '1' THEN 'Male' " + 
				" when '2' then 'Female' " + 
				" END ,occupation,DOB,dependent=case dependent when '1' then 'Yes' when '2' then 'No' end,pcode,relationship as relationshipid,maritialstatus as statusid,dependent as dependentid,fdid as fdid " + 
				" from t_familydetails where isactive='1' and pcode='" + Session["user_id"].ToString() + "' And relationship in(2,3,4,5)";
			SqlDataAdapter rd=new SqlDataAdapter(getFamily,con);
			if(Session["dFamily"].ToString()!="")
			{
				dFamily=(DataSet)Session["dFamily"];
			}
			if(dFamily==null)
			{
				dFamily=new DataSet();
				//Cache.Insert("CachedFamily",dFamily);
				Session["dFamily"]=dFamily;
			}
			rd.Fill(dFamily,"FamilyInfo");
			DataView dv=new DataView(dFamily.Tables["FamilyInfo"]);
			dv.Sort=sort;
			this.DataGrid1.DataSource=dv;
			this.DataGrid1.DataBind();
			int getItems=this.DataGrid1.Items.Count;
			if(getItems<=0)
			{
				this.lblRecord.Visible=true;
			}
			else
			{
				this.lblRecord.Visible=false;
			}
			for(int i=0;i<this.DataGrid1.Items.Count;i++)
			{
				LinkButton lnk=(LinkButton)this.DataGrid1.Items[i].FindControl("lnkDelete");
				lnk.Attributes.Add("onclick","return window.confirm('Are You Sure To Delete')");
			}
			con.Close();
			this.btnSubmitFamily.Visible=true;
		}*/

		/*public void EducationalInfo()
		{
			this.txtDegree.Items.Clear();
			this.txtInstitute.Items.Clear();
			con.Open();
			string empId=Session["user_id"].ToString();
			//Request.QueryString["id"].ToString();
			this.DataGrid3.Visible=true; 
			//string getInfo="select e.eduinfoid,degree=case e.degree when '-1' then (select temp.otherdegree from t_educationinfo temp where temp.eduinfoid=e.eduinfoid ) else (select d.degree from t_degree d where d.id=e.degree) end , institute=case e.institute when '-1' then (select temp.otherinstitute from t_educationinfo temp where temp.eduinfoid=e.eduinfoid) else (select temp.institute_name from t_institute temp where temp.id=e.institute)end ,e.durationto,e.result,e.durationfrom,e.majors,e.achievements,type from t_educationinfo e where e.isactive='1' And e.pcode='"+empId+"'";
			string getInfo="select degree=case e.degree when '-1' then (select temp.otherdegree from t_educationinfo temp where temp.eduinfoid=e.eduinfoid ) else (select d.degree from t_degree d where d.id=e.degree) end , institute=case e.institute when '-1' then (select temp.otherinstitute from t_educationinfo temp where temp.eduinfoid=e.eduinfoid) else (select temp.institute_name from t_institute temp where temp.id=e.institute)end ,e.durationfrom,durationto=case e.durationto when '-1' then 'In Progress' else (cast (e.durationto as varchar))end ,e.result,e.majors,e.achievements,e.eduinfoid,e.degree as degreeid,e.institute as instituteid,e.durationto as durationid from t_educationinfo e where e.isactive='1' And e.pcode='"+ Session["user_id"].ToString() +"'";
			SqlDataAdapter rd=new SqlDataAdapter(getInfo,con);
			DataSet rSet=new DataSet();
			rd.Fill(rSet,"EdutionalInfo");
			DataView dv=new DataView(rSet.Tables["EdutionalInfo"]);
			dv.Sort=sort3;
			this.DataGrid3.DataSource=dv;
			this.DataGrid3.DataBind();
			int getItems=this.DataGrid3.Items.Count;
			if(getItems<=0)
			{
				this.lblEdBoRecord.Visible=true;
			}
			else
			{
				this.lblEdBoRecord.Visible=false;
			}
			this.DataGrid3.Columns[8].Visible=false;
			this.DataGrid3.Columns[9].Visible=false; 
		
			string getDegree="select id,degree from t_degree where isactive='1' order by degree";
			SqlCommand cmDegree=new SqlCommand(getDegree,con);
			SqlDataReader rdDegree=cmDegree.ExecuteReader();
		
			/*ListItem it=new ListItem();
			it.Value="0";
			it.Text="Select--Degree";
			this.txtDegree.Items.Insert(0,it);
			int i=1;
			while(rdDegree.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rdDegree[0].ToString();
				itm.Text=rdDegree[1].ToString();
				this.txtDegree.Items.Insert(i,itm);
				i++;
			}
			rdDegree.Close();
			it=new ListItem("Other","-1");
			this.txtDegree.Items.Add(it);
 
			string getInstitute="select id,institute_name from t_institute where isactive='1' order by institute_name";
			SqlCommand cmInstitute=new SqlCommand(getInstitute,con);
			SqlDataReader rdInstitute=cmInstitute.ExecuteReader();
			
			ListItem item2=new ListItem();
			item2.Value="0";
			item2.Text="Select--Institute";
			this.txtInstitute.Items.Insert(0,item2);
			int j=1;
			while(rdInstitute.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rdInstitute[0].ToString();
				itm.Text=rdInstitute[1].ToString();
				this.txtInstitute.Items.Insert(j,itm);
				j++;
			}
			rdInstitute.Close();
			item2=new ListItem("Other","-1");
			this.txtInstitute.Items.Add(item2);
			con.Close();
        
		}*/

		private void ddDepartment_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			getDes();
		}

		public void getDes()
		{
			this.ddDesignation.Items.Clear();
			ListItem item=new ListItem();
			item.Value="0";
			item.Text="Select--Designation";
			this.ddDesignation.Items.Add(item);
			con.Open();
			string getDesignation="select d.desigid,d.designation,c.cat_name from t_designation d,t_categorization c where d.status=1 And c.isactive=1 And d.category=c.cat_id And d.deptid='"+this.ddDepartment.SelectedValue.ToString()+"' order by d.designation";
			SqlCommand cmd=new SqlCommand(getDesignation,con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rd[0].ToString();
				//itm.Text=rd[1].ToString()+":"+"Category "+rd[2].ToString();
				itm.Text=rd[1].ToString();
				this.ddDesignation.Items.Add(itm);
				
			}
			rd.Close();  
			con.Close();		
		}

		private void ImageButton4_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("EmpEducation.aspx?pcode="+this.lblPCode.Text+"&name="+this.lblEmpInfo.Text);
			/*pnlPaySlip.Visible=false;
			lblCombine.Visible=false;
			lblCompare.Visible=false;
			lblEmp.Visible=false;
			//this.lblEducationMsg.Visible=true;
			//lblEducationMsg.Text="There are no current requests";
			//this.lblFamilyMsg.Visible=false;
			this.ValidationSummary1.Visible=false;
			//this.Label1.Visible=true;
			this.Label2.Visible=false;
			//this.Image1.Visible=false;
			//this.pnlEducation.Visible=true;
			this.pnlEmployeeInfo.Visible=false;
			//this.pnlFamilyInfo.Visible=false;
			this.pnlPersonal.Visible=false;
			this.btnUpdate.Visible=false;*/
		}

		private void btnUpdate_Click(object sender, System.EventArgs e)
		{
			if(IsRefresh)
				return;
			bool Valid=false;
			if(IsValid)
			{		
				lblCompare.Visible=false;
				if(this.ddNationality.SelectedValue=="163")
				{
					Valid=verifyNIC();
					this.lblEmp.Text="The Given NIC # Already Assigned To Employee.Please Contact Site Administrator ";
				}
				else
				{
					if(this.ddNationality.SelectedValue !="0")
					{
						Valid=verifyNIC(true);
						this.lblEmp.Text="The Given Passport # Already Assigned To Employee.Please Contact Site Administrator ";
					}
				}
				if(Valid)
				{
					this.lblEmp.Visible=true;
				}
				else
				{
					this.lblEmp.Visible=false;
					if(this.pnlEmployeeInfo.Visible)
					{
						DateTime dt=DateTime.Now;
						bool flag=false;
						con.Open();
						string getDate="select dateofconfirmation from t_employee where pcode='"+ this.lblPCode.Text +"' And dateofconfirmation is not null";
						SqlCommand cmd=new SqlCommand(getDate,con);
						SqlDataReader rd=cmd.ExecuteReader();
						while(rd.Read())
						{
							if(rd[0].ToString().Length>0)
							{
								flag=true; 
								dt=DateTime.Parse(rd[0].ToString());
							}
						}
						rd.Close();
						con.Close();
						if(flag)
						{
							DateTime join=DateTime.Parse(this.CalenderDOJ.SelectedDate.ToString());
							TimeSpan s=dt-join;
							double comp=s.TotalDays;
							int comp2=dt.CompareTo(join);
							/*int month=0;
							int year=0;
							int day=0;
							month=dt.Month; 
							year=dt.Year;
							day=dt.Day;
							DateTime join=DateTime.Parse(this.CalenderDOJ.SelectedDate.ToString());
							//int comp=join.CompareTo(dt);							
							int comp=dt.CompareTo(join);
							bool condition1=false;
							bool condition2=false;
							bool condition3=false;
							if(join.Year <=year)
							{
								condition1=true;
								if(join.Month >=month)
								{
									condition2=true;
									if(join.Day < day)
									{
										condition3=true;
									}
									else
									{
										condition1=false;
										condition2=false;
									}
								}
								else
								{
									condition1=false;
								}
							}*/
							
							//if(!condition1 || !condition2 || !condition3)
							if(comp >0)
							{
								lblCompare.Visible=false;
								UpdateEmpInfo();
							}
							else
							{
								lblCompare.Visible=true;
								lblCombine.Visible=false;
							}
						}
						else
						{
							UpdateEmpInfo();
						}
					}
					if(this.pnlPersonal.Visible)
					{
						UpdatePersonalInfo();					
					}
				}
			}
		}

		public bool verifyNIC()
		{
			if(this.txtNewNic.Text!="")
			{
				con.Open();
				string verify="select pcode from t_employee where nic_new='"+ this.txtNewNic.Text +"' And pcode not in('"+ this.lblPCode.Text +"')";
				SqlCommand cmd=new SqlCommand(verify,con);
				SqlDataReader rd=cmd.ExecuteReader();
				rd.Read();
				if(rd.HasRows)
				{
					rd.Close();
					con.Close();
					return true; 
				}
				else
				{
					rd.Close();
					con.Close();
					return false; 
				}
			}
			else
			{
				return true;
			}
		
		}
		public bool verifyNIC(bool flag)
		{
			if(this.txtPassport.Text !="")
			{
				con.Open();
				string verify="select pcode from t_employee where passportno='"+ this.txtPassport.Text +"' And pcode not in('"+ this.lblPCode.Text +"')";
				SqlCommand cmd=new SqlCommand(verify,con);
				SqlDataReader rd=cmd.ExecuteReader();
				rd.Read();
				if(rd.HasRows)
				{
					rd.Close();
					con.Close();
					return true; 
				}
				else
				{
					rd.Close();
					con.Close();
					return false; 
				}
			}
			else
			{
				return true;
			}
		}
         
		public void docSetting()
		{
			//this.fpDesignation.Disabled=false;
			this.fpName.Disabled=false;
			this.lnName.Visible=false;
			this.lblfpName.Visible=false;
			//this.lblfpDesignation.Visible=false;
			//this.lnDesignation.Visible=false;
			//this.lblfpDesignation.Visible=false;
			//this.fpDepartment.Disabled=false;
			//this.fpDepartment.Disabled=false;
			//this.lnDepartment.Visible=false;
			//this.lblfpDepartment.Visible=false;
			//this.fpStation.Disabled=false;
			//this.fpStation.Disabled=false;
			//this.lnStation.Visible=false;
			//this.lblfpStation.Visible=false;
			this.fpEOBI.Disabled=false;
			this.fpEOBI.Disabled=false;
			this.lnEOBI.Visible=false;
			this.lblfpEOBI.Visible=false;
		}
		public void docSetting(bool i)
		{
			this.fpDateofBirth.Disabled=false;
			this.fpDateofBirth.Disabled=false;
			this.lnDateofBirth.Visible=false;
			this.lblfpDateofBirth.Visible=false;
			this.fpFatherName.Disabled=false;
			this.fpFatherName.Disabled=false;
			this.lnFatherName.Visible=false;
			this.lblfpFatherName.Visible=false;
			this.fpMartialStatus.Disabled=false;
			this.fpMartialStatus.Disabled=false;
			this.lnMartialStatus.Visible=false;
			this.lblfpMaritalStatus.Visible=false;
			this.fpNationality.Disabled=false;
			this.fpNationality.Disabled=false;
			this.lnNationality.Visible=false;
			this.lblfpNationality.Visible=false;
			this.fpNationality2.Disabled=false;
			this.fpNationality2.Disabled=false;
			this.lnNationality2.Visible=false;
			this.lblfpNationality2.Visible=false;
			this.fpPassport.Disabled=false;
			this.fpPassport.Disabled=false;
			this.lnPassport.Visible=false;
			this.lblfpPassprot.Visible=false;
			this.fpNextofKin.Disabled=false;
			this.fpNextofKin.Disabled=false;
			this.lnNextofKin.Visible=false;
			this.lblfpNextofKin.Visible=false;
			this.fpNICNew.Disabled=false;
			this.fpNICNew.Disabled=false;
			this.lnNICNew.Visible=false;
			this.lblfpNICNew.Visible=false;
			this.fpNICOld.Disabled=false;
			this.fpNICOld.Disabled=false;
			this.lnNICOld.Visible=false;
			this.lblfpNICOld.Visible=false;
			this.fpBankNo.Disabled=false;
			this.fpBankNo.Disabled=false;
			this.lnBankNo.Visible=false;
			this.lblfpBankNo.Visible=false;
			this.fpBankDetail.Disabled=false;
			this.fpBankDetail.Disabled=false;
			this.lnBankDetail.Visible=false;
			this.lblfpBankDetail.Visible=false;

			this.fpPicture.Disabled=false;
			this.fpPicture.Disabled=false;
			this.lnPicture.Visible=false;
			this.lblfpPicture.Visible=false;
		}
		public void UpdateEmpInfo()
		{
			
			ArrayList list=new ArrayList();
			if(CheckRequest(this.lblEmpInfoName.Text,this.txtEmpInfoName.Text))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=7;
				h.fieldname="Name";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=this.txtEmpInfoName.Text;
				h.requestforchangeID.Value=DBNull.Value;
				if(lnName.Visible)
				{
					h.docGUID=lblfpName.Text;
					h.isPosted=true;
				}
				else
				{
					h.docGUID=this.fpName.PostedFile.FileName;
					h.isPosted=false;
				}
				h.isDocRequired=true;
				list.Add(h);
			}
			if(CheckRequest(this.lblEmpInfoDesignation.Text,this.ddDesignation.SelectedItem.Text))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=6;
				h.fieldname="Designation";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=this.ddDesignation.SelectedItem.Text;
				h.requestforchangeID.Value=this.ddDesignation.SelectedItem.Value;
				/*if(lnDesignation.Visible)
				{
					h.docGUID=lblfpDesignation.Text;
					h.isPosted=true;
				}
				else
				{
					h.docGUID=this.fpDesignation.PostedFile.FileName;
					h.isPosted=false;
				}
				h.isDocRequired=true;
				list.Add(h);*/
				h.isPosted=false;
				h.isDocRequired=false;
				list.Add(h);
			}
			if(CheckRequest(this.lnlEmpInfoDept.Text,this.ddDepartment.SelectedItem.Text))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=10;
				h.fieldname="Department";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=this.ddDepartment.SelectedItem.Text;
				h.requestforchangeID.Value=this.ddDesignation.SelectedItem.Value;
				h.isPosted=false;
				h.isDocRequired=false;
				list.Add(h);
			}
			if(CheckRequest(this.lblEmpInfoCity.Text,this.ddStation.SelectedItem.Text))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=8;
				h.fieldname="Station";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=this.ddStation.SelectedItem.Text;
				h.requestforchangeID.Value=this.ddStation.SelectedItem.Value;
				/*if(lnStation.Visible)
				{
					h.docGUID=lblfpStation.Text;
					h.isPosted=true;
				}
				else
				{
					h.docGUID=this.fpStation.PostedFile.FileName;
					h.isPosted=false;
				}
				h.isDocRequired=true;
				list.Add(h);*/
				h.isPosted=false;
				h.isDocRequired=false;
				list.Add(h);
			}
			if(CheckRequest(this.lblEmpInfoDOJ.Text,this.CalenderDOJ.SelectedDate.ToString("dd MMM,yyyy")))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=9;
				h.fieldname="Date of Joining";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=this.CalenderDOJ.SelectedDate.ToString("dd MMM,yyyy");
				h.requestforchangeID.Value=this.CalenderDOJ.SelectedDate.ToString("d MMM,yyyy");
				h.isPosted=false;
				h.isDocRequired=false;
				list.Add(h);
			}
			if(CheckRequest(this.lblEmpInfoExtension.Text,this.txtEmpInfoExtension.Text))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=11;
				h.fieldname="Extension";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=this.txtEmpInfoExtension.Text;
				h.requestforchangeID.Value=DBNull.Value;
				h.isPosted=false;
				h.isDocRequired=false;
				list.Add(h);
			}
			if(CheckRequest(this.lblEmpInfoEmail.Text,this.txtEmpInfoEmail.Text))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=31;
				h.fieldname="Email (Official)";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=this.txtEmpInfoEmail.Text;
				h.requestforchangeID.Value=DBNull.Value;
				h.isPosted=false;
				h.isDocRequired=false;
				list.Add(h);
			}
			if(CheckRequest(this.lblEmpInfoSessi.Text,this.txtEmpInfoSESSI.Text))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=12;
				h.fieldname="SESSI #";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=this.txtEmpInfoSESSI.Text;
				h.requestforchangeID.Value=DBNull.Value;
				h.isPosted=false;
				h.isDocRequired=false;
				list.Add(h);
			}
			if(CheckRequest(this.lblEmpInfoEObi.Text,this.txtEmpInfoEOBI.Text))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=13;
				h.fieldname="EOBI #";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=this.txtEmpInfoEOBI.Text;
				h.requestforchangeID.Value=DBNull.Value;
				if(lnEOBI.Visible)
				{
					h.docGUID=lblfpEOBI.Text;
					h.isPosted=true;
				}
				else
				{
					h.docGUID=this.fpEOBI.PostedFile.FileName;
					h.isPosted=false;
				}
				h.isDocRequired=true;
				list.Add(h);
			}
			if(CheckRequest(this.lblEmpLocation.Text,this.txtEmpLocation.Text))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=14;
				h.fieldname="Workstation Address";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=this.txtEmpLocation.Text;
				h.requestforchangeID.Value=DBNull.Value;
				h.isPosted=false;
				h.isDocRequired=false;
				list.Add(h);
			}
			//==================================Check Change in Request====================//
			if(list.Count<=0)
			{
				//Response.Write("<script>window.alert('There is no change in request(s). Thank You')</script>");
				this.lblCombine.Visible=true;
				this.lblCombine.Text="There is no change in request(s). Thank You";
				return;
			}
			else
			{
				string message="'<ul>Please attach/upload scan copy(ies) of following fields before sending your request";
				string messagefordis="Please attach/upload scan copy(ies) of following fields before sending your request \\n";
				string field="";
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler h=(GeoRabtaSite.RequestHandler)list[i];
					if(h.isDocRequired && h.docGUID=="")
					{
						field+="<li>"+h.fieldname+"</li>";
						messagefordis+=h.fieldname+"\\n";
					}
				}
				if(field.Length>0)
				{
					message+=field;
					message+="</ul>'";
					//Response.Write("<script>window.alert('"+messagefordis+"')</script>");
					this.lblCombine.Visible=true;
					this.lblCombine.Text=message;
					return;
				}
			}
			//=============================================================================//
			//===============================Check Previous Pending Request==================//
			con.Open();
			SqlTransaction trans=con.BeginTransaction();
			try
			{
				SqlCommand cmd=null;
				SqlDataReader rd=null;
				ArrayList removeList=new ArrayList();
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler h=(GeoRabtaSite.RequestHandler)list[i];
					cmd=new SqlCommand("select timeline,vrequired,replyonaccept from t_fieldsmgt where f_id="+h.fieldno+"",con);
					cmd.Transaction=trans;
					rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						int vRequired=Int32.Parse(rd[1].ToString());
						string replyonaccept=rd[2].ToString();
						rd.Close();
						if(vRequired==2)
						{
							if(h.fieldno==7)
							{
								//Name//
								cmd=new SqlCommand("update t_employee set name=@name,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _name=new SqlParameter("@name",SqlDbType.VarChar);
								_name.Value=h.requestforchange;
								cmd.Parameters.Add(_name);
								cmd.Transaction=trans;
								//trans.Commit();
								cmd.ExecuteNonQuery();
								removeList.Add(h);
								list.Remove(h);
								i--;
							}
							if(h.fieldno==10)
							{
								//Department//
							}
							if(h.fieldno==6)
							{
								//Designation
							}
							if(h.fieldno==8)
							{
								//Station
								cmd=new SqlCommand("update t_employee set station=@station,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _station=new SqlParameter("@station",SqlDbType.Int);
								_station.Value=h.requestforchange;
								cmd.Parameters.Add(_station);
								cmd.Transaction=trans;
								//trans.Commit();
								cmd.ExecuteNonQuery();
								removeList.Add(h);
								list.Remove(h);
								i--;
							}
							if(h.fieldno==9)
							{
								//Date of Join
								cmd=new SqlCommand("update t_employee set dateofjoin=@dateofjoin,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _dateofjoin=new SqlParameter("@dateofjoin",SqlDbType.DateTime);
								_dateofjoin.Value=h.requestforchange;
								cmd.Parameters.Add(_dateofjoin);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								//trans.Commit();
								removeList.Add(h);
								list.Remove(h); 
								i--;
							}
							if(h.fieldno==11)
							{
								//Extension
								cmd=new SqlCommand("update t_employee set extension=@extension,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _extension=new SqlParameter("@extension",SqlDbType.VarChar);
								_extension.Value=h.requestforchange;
								cmd.Parameters.Add(_extension);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								//trans.Commit();
								removeList.Add(h);
								list.Remove(h);
								i--;
							}
							if(h.fieldno==31)
							{
								//Email (Official)
								cmd=new SqlCommand("update t_employee set email_official=@email_official,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _email=new SqlParameter("@email_official",SqlDbType.VarChar);
								_email.Value=h.requestforchange;
								cmd.Parameters.Add(_email);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								//trans.Commit();
								removeList.Add(h);
								list.Remove(h);
								i--;
							}
							if(h.fieldno==12)
							{
								//SESSI
								cmd=new SqlCommand("update t_employee set sessino=@sessino,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _sessi=new SqlParameter("@sessino",SqlDbType.VarChar);
								_sessi.Value=h.requestforchange;
								cmd.Parameters.Add(_sessi);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								//trans.Commit();
								removeList.Add(h);
								list.Remove(h);
								i--;
							}
							if(h.fieldno==13)
							{
								//EOBI
								cmd=new SqlCommand("update t_employee set eobino=@eobino,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _eobi=new SqlParameter("@eobino",SqlDbType.VarChar);
								_eobi.Value=h.requestforchange;
								cmd.Parameters.Add(_eobi);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								//trans.Commit();
								removeList.Add(h);
								list.Remove(h);
								i--;
							}
							if(h.fieldno==14)
							{
								//Workstation Address
								cmd=new SqlCommand("update t_employee set operatingcity=@operatingcity,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _workstation=new SqlParameter("@operatingcity",SqlDbType.VarChar);
								_workstation.Value=h.requestforchange;
								cmd.Parameters.Add(_workstation);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								//trans.Commit();
								removeList.Add(h);
								list.Remove(h);
								//Bulletin.PostMessage("Change of Workstation Request",replyonaccept,"Geo Raabta",7,2,"Geo Raabta",Session["user_id"].ToString());
							    i--;
							}
						}
					}
				}
				Session["rRequest"]=removeList;
				cmd=new SqlCommand("select * from t_employeerequests where requisitecode='"+Session["user_id"].ToString()+"' and requeststatus=1",con);
				cmd.Transaction=trans;
				rd=cmd.ExecuteReader();
				rd.Read();
				if(!rd.HasRows)
				{
					rd.Close();
					//===============================Enter New Request in DB=========================//
					for(int i=0;i<list.Count;i++)
					{
						GeoRabtaSite.RequestHandler h=(GeoRabtaSite.RequestHandler)list[i];
						cmd=new SqlCommand("select timeline,vrequired,replyatrequest from t_fieldsmgt where f_id="+h.fieldno+"",con);
						cmd.Transaction=trans;
						rd=cmd.ExecuteReader();
						rd.Read();
						if(rd.HasRows)
						{
							int workingdays=Int32.Parse(rd[0].ToString());
							int vRequired=Int32.Parse(rd[1].ToString());
							string replyatrequest=rd[2].ToString();
							h.requesttimeline=DateTime.Now.Date.AddDays(workingdays).ToShortDateString();
							rd.Close();
							if(vRequired==1)
							{
								cmd=new SqlCommand("insert into t_employeerequests(fieldno,fieldname,requestforchange,requestforchangeID,requisitecode,requestdate,requesttime,requesttimeline,requeststatus,documentID) values(@fieldno,@fieldname,@requestforchange,@requestforchangeID,@requisitecode,getdate(),@requesttime,@requesttimeline,@requeststatus,@documentID)",con);
								cmd.Transaction=trans;
								SqlParameter _fieldno=new SqlParameter("@fieldno",SqlDbType.Int);
								SqlParameter _fieldname=new SqlParameter("@fieldname",SqlDbType.VarChar);
								SqlParameter _requestforchange=new SqlParameter("@requestforchange",SqlDbType.VarChar);
								SqlParameter _requisitecode=new SqlParameter("@requisitecode",SqlDbType.VarChar);
								//SqlParameter _requestdate=new SqlParameter("@requestdate",SqlDbType.DateTime);
								SqlParameter _requesttime=new SqlParameter("@requesttime",SqlDbType.VarChar);
								SqlParameter _requesttimeline=new SqlParameter("@requesttimeline",SqlDbType.DateTime);
								SqlParameter _requeststatus=new SqlParameter("@requeststatus",SqlDbType.Int);
								SqlParameter _documentID=new SqlParameter("@documentID",SqlDbType.Text);
								_fieldno.Value=h.fieldno;
								_fieldname.Value=h.fieldname;
								_requestforchange.Value=h.requestforchange;
								_requisitecode.Value=h.requisitecode;
								//_requestdate.Value=h.requestdate;
								_requesttime.Value=h.requesttime;
								_requesttimeline.Value=h.requesttimeline;
								_requeststatus.Value=h.requeststatus;
								if(h.isDocRequired)
								{
									Guid g=Guid.NewGuid();
									string filepath=Server.MapPath(@"tempEmployee\");
									string filename=g.ToString();
									if(h.fieldno==7)
									{
										if(h.isPosted)
										{
											string dPath=filepath+filename+Path.GetExtension(lblfpName.Text);
											File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\7"+Path.GetExtension(lblfpName.Text)),dPath);
										}
										else
										{
											this.fpName.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpName.PostedFile.FileName));
										}
									}
									/*if(h.fieldno==6)
									{
										if(h.isPosted)
										{
											string dPath=filepath+filename+Path.GetExtension(lblfpDesignation.Text);
											File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\6"+Path.GetExtension(lblfpDesignation.Text)),dPath);
										}
										else
										{
											this.fpDesignation.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpDesignation.PostedFile.FileName));
										}
									}*/
									/*if(h.fieldno==10)
									{
										if(h.isPosted)
										{
											string dPath=filepath+filename+Path.GetExtension(lblfpDepartment.Text);
											File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\10"+Path.GetExtension(lblfpDepartment.Text)),dPath);
										}
										else
										{
											this.fpDepartment.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpDepartment.PostedFile.FileName));
										}
									}*/
									/*if(h.fieldno==8)
									{
										if(h.isPosted)
										{
											string dPath=filepath+filename+Path.GetExtension(lblfpStation.Text);
											File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\8"+Path.GetExtension(lblfpStation.Text)),dPath);
										}
										else
										{
											this.fpStation.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpStation.PostedFile.FileName));
										}
									}*/
									if(h.fieldno==13)
									{
										if(h.isPosted)
										{
											string dPath=filepath+filename+Path.GetExtension(lblfpEOBI.Text);
											File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\13"+Path.GetExtension(lblfpEOBI.Text)),dPath);
										}
										else
										{
											this.fpEOBI.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpEOBI.PostedFile.FileName));
										}
									}
									_documentID.Value=g.ToString();
								}
								else
								{
									_documentID.Value=DBNull.Value;
								}
								cmd.Parameters.Add(_fieldno);
								cmd.Parameters.Add(_fieldname);
								cmd.Parameters.Add(_requestforchange);
								cmd.Parameters.Add(h.requestforchangeID);
								cmd.Parameters.Add(_requisitecode);
								cmd.Parameters.Add(_requesttime);
								cmd.Parameters.Add(_requesttimeline);
								cmd.Parameters.Add(_requeststatus);
								cmd.Parameters.Add(_documentID);
								cmd.ExecuteNonQuery();
								//Bulletin.PostMessage("Change of "+h.fieldname,replyatrequest,"Geo Raabta",7,2,"Geo Raabta",Session["user_id"].ToString(),con,trans);
							    Bulletin.PostMessage(con,trans,replyatrequest,"Geo Raabta",Session["user_id"].ToString(),1);
							}
						}
						else
						{
							//Response.Write("Process flow not defined properly for request field"+h.fieldname+". Request failed to send");
							lblCombine.Visible=true;
							lblCombine.Text="Process flow not defined properly for request field"+h.fieldname+". Request failed to send";
							trans.Rollback();
							return;
						}
					}
					trans.Commit();
					con.Close();
					lblCombine.Visible=true;
					lblCombine.Text="Your request has been sent successfully. Please check your MY REQUEST tab for Requsition IDs";
					docSetting();
					//====================================================================================//
				}
				else
				{
					ArrayList requestList=new ArrayList();
					ArrayList pendingList=new ArrayList();
					rd.Close();
					cmd=new SqlCommand("select * from t_employeerequests where requisitecode='"+Session["user_id"].ToString()+"' and requeststatus=1",con);
					cmd.Transaction=trans;
					rd=cmd.ExecuteReader();
					while(rd.Read())
					{
						requestList.Add(rd[2].ToString());
					}
					rd.Close();
					for(int i=0;i<list.Count;i++)
					{
						GeoRabtaSite.RequestHandler h=(GeoRabtaSite.RequestHandler)list[i];
						string Val=""+h.fieldno+"";	
						if(requestList.Contains(Val))
						{
							pendingList.Add(h);
						}
					}
					if(pendingList.Count>0)
					{
						string Message="Following requisite field(s) already pending. Do you want to replace below field(s) with your previous requisition? <br>";
						for(int j=0;j<pendingList.Count;j++)
						{
							GeoRabtaSite.RequestHandler rh=(GeoRabtaSite.RequestHandler)pendingList[j];
							int Val=j;
							Val+=1;
							Message+=Val+". "+rh.fieldname+",<br>";
						}
						Message=Message.Remove(Message.Length-5,5);
						//Response.Write("<script>window.alert('Request already pending on the requisite field. Do you want to Re-send it?')</script>");
						//Response.Write("<script>window.alert('"+Message+"')</script>");
						this.lblCombine.Visible=true;
						this.lblCombine.Text=Message;
						this.btnUpdate.Enabled=false;
						this.btnIgnore.Visible=true;
						this.btnResend.Visible=true;
						Session["pendingRequest"]=pendingList;
						Session["cRequest"]=list;
						trans.Commit();
						return;
					}
					else
					{
						//==============New Request After Check Pending Request on User===========//
						for(int i=0;i<list.Count;i++)
						{
							GeoRabtaSite.RequestHandler h=(GeoRabtaSite.RequestHandler)list[i];
							cmd=new SqlCommand("select timeline,replyatrequest from t_fieldsmgt where f_id="+h.fieldno+"",con);
							cmd.Transaction=trans;
							rd=cmd.ExecuteReader();
							rd.Read();
							if(rd.HasRows)
							{
								int workingdays=Int32.Parse(rd[0].ToString());
								h.requesttimeline=DateTime.Now.Date.AddDays(workingdays).ToShortDateString();
								string replyatrequest=rd[1].ToString();
								rd.Close();
								cmd=new SqlCommand("insert into t_employeerequests(fieldno,fieldname,requestforchange,requestforchangeID,requisitecode,requestdate,requesttime,requesttimeline,requeststatus,documentID) values(@fieldno,@fieldname,@requestforchange,@requestforchangeID,@requisitecode,getdate(),@requesttime,@requesttimeline,@requeststatus,@documentID)",con);
								cmd.Transaction=trans;
								SqlParameter _fieldno=new SqlParameter("@fieldno",SqlDbType.Int);
								SqlParameter _fieldname=new SqlParameter("@fieldname",SqlDbType.VarChar);
								SqlParameter _requestforchange=new SqlParameter("@requestforchange",SqlDbType.VarChar);
								SqlParameter _requisitecode=new SqlParameter("@requisitecode",SqlDbType.VarChar);
								//SqlParameter _requestdate=new SqlParameter("@requestdate",SqlDbType.DateTime);
								SqlParameter _requesttime=new SqlParameter("@requesttime",SqlDbType.VarChar);
								SqlParameter _requesttimeline=new SqlParameter("@requesttimeline",SqlDbType.DateTime);
								SqlParameter _requeststatus=new SqlParameter("@requeststatus",SqlDbType.Int);
								SqlParameter _documentID=new SqlParameter("@documentID",SqlDbType.Text);
								_fieldno.Value=h.fieldno;
								_fieldname.Value=h.fieldname;
								_requestforchange.Value=h.requestforchange;
								_requisitecode.Value=h.requisitecode;
								//_requestdate.Value=h.requestdate;
								_requesttime.Value=h.requesttime;
								_requesttimeline.Value=h.requesttimeline;
								_requeststatus.Value=h.requeststatus;
								if(h.isDocRequired)
								{
									Guid g=Guid.NewGuid();
									string filepath=Server.MapPath(@"tempEmployee\");
									string filename=g.ToString();
									if(h.fieldno==7)
									{
										if(h.isPosted)
										{
											string dPath=filepath+filename+Path.GetExtension(lblfpName.Text);
											File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\7"+Path.GetExtension(lblfpName.Text)),dPath);
										}
										else
										{
											this.fpName.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpName.PostedFile.FileName));
										}
									}
									/*if(h.fieldno==6)
									{
										if(h.isPosted)
										{
											string dPath=filepath+filename+Path.GetExtension(lblfpDesignation.Text);
											File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\6"+Path.GetExtension(lblfpDesignation.Text)),dPath);
										}
										else
										{
											this.fpDesignation.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpDesignation.PostedFile.FileName));
										}
									}*/
									/*if(h.fieldno==10)
									{
										if(h.isPosted)
										{
											string dPath=filepath+filename+Path.GetExtension(lblfpDepartment.Text);
											File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\10"+Path.GetExtension(lblfpDepartment.Text)),dPath);
										}
										else
										{
											this.fpDepartment.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpDepartment.PostedFile.FileName));
										}
									}*/
									/*if(h.fieldno==8)
									{
										if(h.isPosted)
										{
											string dPath=filepath+filename+Path.GetExtension(lblfpStation.Text);
											File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\8"+Path.GetExtension(lblfpStation.Text)),dPath);
										}
										else
										{
											this.fpStation.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpStation.PostedFile.FileName));
										}
									}*/
									if(h.fieldno==13)
									{
										if(h.isPosted)
										{
											string dPath=filepath+filename+Path.GetExtension(lblfpEOBI.Text);
											File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\13"+Path.GetExtension(lblfpEOBI.Text)),dPath);
										}
										else
										{
											this.fpEOBI.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpEOBI.PostedFile.FileName));
										}
									}
									_documentID.Value=g.ToString();
								}
								else
								{
									_documentID.Value=DBNull.Value;
								}
								cmd.Parameters.Add(_fieldno);
								cmd.Parameters.Add(_fieldname);
								cmd.Parameters.Add(_requestforchange);
								cmd.Parameters.Add(h.requestforchangeID);
								cmd.Parameters.Add(_requisitecode);
								//cmd.Parameters.Add(_requestdate);
								cmd.Parameters.Add(_requesttime);
								cmd.Parameters.Add(_requesttimeline);
								cmd.Parameters.Add(_requeststatus);
								cmd.Parameters.Add(_documentID);
								cmd.ExecuteNonQuery();
                                //Bulletin.PostMessage("Change of "+h.fieldname,replyatrequest,"Geo Raabta",7,2,"Geo Raabta",Session["user_id"].ToString(),con,trans);
								Bulletin.PostMessage(con,trans,replyatrequest,"Geo Raabta",Session["user_id"].ToString(),1);
							}
							else
							{
								//Response.Write("Process flow not defined properly for request field"+h.fieldname+". Request failed to send");
								lblCombine.Visible=true;
								lblCombine.Text="Process flow not defined properly for request field"+h.fieldname+". Request failed to send";
								trans.Rollback();
								return;
							}
						}
						trans.Commit();
						con.Close();
						lblCombine.Visible=true;
						lblCombine.Text="Your request has been sent successfully. Please check your MY REQUEST tab for Requsition IDs";
						docSetting();
						//=======================================================================//
					}
				}
				Session["pendingRequest"]="";
				Session["cRequest"]="";
				con.Close();
			}
			catch(Exception ex)
			{
				Response.Write(ex.Message);
				trans.Rollback();
				con.Close();
			}
			//==============================================================================//
			
		}

		public bool CheckRequest(string original,string requested)
		{
			if(!requested.Trim().Equals(original.Trim()))
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		public void UpdatePersonalInfo()
		{
			ArrayList list=new ArrayList();
			if(CheckRequest(this.lblName.Text,this.calendarDOB.SelectedDate.ToString("dd MMM,yyyy")))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=16;
				h.fieldname="Date of Birth";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=this.calendarDOB.SelectedDate.ToString("dd MMM,yyyy");
				h.requestforchangeID.Value=this.calendarDOB.SelectedDate.ToString("d MMM,yyyy");
				if(lnDateofBirth.Visible)
				{
					h.docGUID=lblfpDateofBirth.Text;
					h.isPosted=true;
				}
				else
				{
					h.docGUID=this.fpDateofBirth.PostedFile.FileName;
					h.isPosted=false;
				}
				h.isDocRequired=true;
				list.Add(h);
			}
			if(CheckRequest(this.lblFName.Text,this.txtFName.Text))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=24;
				h.fieldname="Father Name";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=this.txtFName.Text;
				h.requestforchangeID.Value=DBNull.Value;
				if(lnFatherName.Visible)
				{
					h.docGUID=lblfpFatherName.Text;
					h.isPosted=true;
				}
				else
				{
					h.docGUID=this.fpFatherName.PostedFile.FileName;
					h.isPosted=false;
				}
				h.isDocRequired=true;
				list.Add(h);
			}
			if(CheckRequest(this.lblGender.Text,this.RadioButtonList1.SelectedItem.Text))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=17;
				h.fieldname="Gender";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=this.RadioButtonList1.SelectedItem.Text;
				h.requestforchangeID.Value=this.RadioButtonList1.SelectedItem.Value;
				h.isPosted=false;
                h.isDocRequired=false;
				list.Add(h);
			}
			if(this.ddBloodGrp.SelectedValue=="0")
			{
				string bloodGrp="";
				if(CheckRequest(this.lblBloodGrp.Text,bloodGrp))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=18;
					h.fieldname="Blood Group";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=ddBloodGrp.SelectedItem.Text;
					h.requestforchangeID.Value=this.ddBloodGrp.SelectedItem.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
			}
			else
			{
				string bloodGrp=this.ddBloodGrp.SelectedItem.Text;
				if(CheckRequest(this.lblBloodGrp.Text,bloodGrp))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=18;
					h.fieldname="Blood Group";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=ddBloodGrp.SelectedItem.Text;
					h.requestforchangeID.Value=this.ddBloodGrp.SelectedItem.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
			}
			if(this.ddReligion.SelectedValue=="0")
			{
				string Religion="";
				if(CheckRequest(this.lblReligion.Text,Religion))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=26;
					h.fieldname="Religion";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=ddReligion.SelectedItem.Text;
					h.requestforchangeID.Value=ddReligion.SelectedItem.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
			}
			else
			{
				string Religion=this.ddReligion.SelectedItem.Text;
				if(CheckRequest(this.lblReligion.Text,Religion))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=26;
					h.fieldname="Religion";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=ddReligion.SelectedItem.Text;
					h.requestforchangeID.Value=ddReligion.SelectedItem.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
			}
			if(CheckRequest(this.lblNick.Text,this.txtNick.Text))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=37;
				h.fieldname="Nick";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=txtNick.Text;
				h.requestforchangeID.Value=DBNull.Value;
				h.isPosted=false;
				h.isDocRequired=false;
				list.Add(h);
			}
			if(CheckRequest(this.lblAddress.Text,this.txtAddress.Text))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=22;
				h.fieldname="Address";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=txtAddress.Text;
				h.requestforchangeID.Value=DBNull.Value;
				h.isPosted=false;
				h.isDocRequired=false;
				list.Add(h);
			}
			if(this.ddStatus.SelectedValue=="0")
			{
				string status="";
				if(CheckRequest(this.lblMaritialStat.Text,status))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=25;
					h.fieldname="Marital Status";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.ddStatus.SelectedItem.Text;
					h.requestforchangeID.Value=this.ddStatus.SelectedItem.Value;
					if(lnMartialStatus.Visible)
					{
						h.docGUID=lblfpMaritalStatus.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpMartialStatus.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
			}
			else
			{
				string status=this.ddStatus.SelectedItem.Text;
				if(CheckRequest(this.lblMaritialStat.Text,status))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=25;
					h.fieldname="Marital Status";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.ddStatus.SelectedItem.Text;
					h.requestforchangeID.Value=this.ddStatus.SelectedItem.Value;
					if(lnMartialStatus.Visible)
					{
						h.docGUID=lblfpMaritalStatus.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpMartialStatus.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
			}
			if(CheckRequest(this.lblEmail.Text,this.txtEmail.Text))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=29;
				h.fieldname="Email (Personal)";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=txtEmail.Text;
				h.requestforchangeID.Value=DBNull.Value;
				h.isPosted=false;
				h.isDocRequired=false;
				list.Add(h);
			}
			if(CheckRequest(this.lblNTN.Text,this.txtNTN.Text))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=34;
				h.fieldname="NTN #";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=txtNTN.Text;
				h.requestforchangeID.Value=DBNull.Value;
				h.isPosted=false;
				h.isDocRequired=false;
				list.Add(h);
			}
			if(CheckRequest(this.lblcontactNo.Text,this.txtContactNo.Text))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=27;
				h.fieldname="Residential #";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=txtContactNo.Text;
				h.requestforchangeID.Value=DBNull.Value;
				h.isPosted=false;
				h.isDocRequired=false;
				list.Add(h);
			}
			if(CheckRequest(this.lblMobileNo.Text,this.txtMobile.Text))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=28;
				h.fieldname="Mobile #";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=txtMobile.Text;
				h.requestforchangeID.Value=DBNull.Value;
				h.isPosted=false;
				h.isDocRequired=false;
				list.Add(h);
			}
			if(this.ddNationality.SelectedValue=="0")
			{
				string Nationality="";
				if(CheckRequest(this.lblNationality.Text,Nationality))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=35;
					h.fieldname="Nationality";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.ddNationality.SelectedItem.Text;
					h.requestforchangeID.Value=this.ddNationality.SelectedItem.Value;
					if(lnNationality.Visible)
					{
						h.docGUID=lblfpNationality.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpNationality.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
			}
			else
			{
				string Nationality=this.ddNationality.SelectedItem.Text;
				if(CheckRequest(this.lblNationality.Text,Nationality))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=35;
					h.fieldname="Nationality";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.ddNationality.SelectedItem.Text;
					h.requestforchangeID.Value=this.ddNationality.SelectedItem.Value;
					if(lnNationality.Visible)
					{
						h.docGUID=lblfpNationality.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpNationality.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
			}
			if(this.ddNationality2.SelectedValue=="0")
			{
				string Nationality="";
				if(CheckRequest(this.lblNationality2.Text,Nationality))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=36;
					h.fieldname="Nationality (Secondary)";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.ddNationality2.SelectedItem.Text;
					h.requestforchangeID.Value=this.ddNationality2.SelectedItem.Value;
					if(lnNationality2.Visible)
					{
						h.docGUID=lblfpNationality2.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpNationality2.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
			}
			else
			{
				string Nationality=this.ddNationality2.SelectedItem.Text;
				if(CheckRequest(this.lblNationality2.Text,Nationality))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=36;
					h.fieldname="Nationality (Secondary)";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.ddNationality2.SelectedItem.Text;
					h.requestforchangeID.Value=this.ddNationality2.SelectedItem.Value;
					if(lnNationality2.Visible)
					{
						h.docGUID=lblfpNationality2.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpNationality2.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
			}
			if(CheckRequest(this.lblPassport.Text,this.txtPassport.Text))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=19;
				h.fieldname="Passport #";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=this.txtPassport.Text;
				h.requestforchangeID.Value=DBNull.Value;
				if(lnPassport.Visible)
				{
					h.docGUID=lblfpPassprot.Text;
					h.isPosted=true;
				}
				else
				{
					h.docGUID=this.fpPassport.PostedFile.FileName;
					h.isPosted=false;
				}
				h.isDocRequired=true;
				list.Add(h);
			}
			if(CheckRequest(this.lblKin.Text,this.txtKin.Text))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=30;
				h.fieldname="Next of Kin";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=this.txtKin.Text;
				h.requestforchangeID.Value=DBNull.Value;
				if(lnNextofKin.Visible)
				{
					h.docGUID=lblfpNextofKin.Text;
					h.isPosted=true;
				}
				else
				{
					h.docGUID=this.fpNextofKin.PostedFile.FileName;
					h.isPosted=false;
				}
				h.isDocRequired=true;
				list.Add(h);
			}
			if(CheckRequest(this.lblNICNew.Text,this.txtNewNic.Text))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=21;
				h.fieldname="NIC (New)";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=this.txtNewNic.Text;
				h.requestforchangeID.Value=DBNull.Value;
				if(lnNICNew.Visible)
				{
					h.docGUID=lblfpNICNew.Text;
					h.isPosted=true;
				}
				else
				{
					h.docGUID=this.fpNICNew.PostedFile.FileName;
					h.isPosted=false;
				}
				h.isDocRequired=true;
				list.Add(h);
			}
			if(CheckRequest(this.lblNICOld.Text,this.txtOldNic.Text))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=20;
				h.fieldname="NIC (Old)";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=this.txtOldNic.Text;
				h.requestforchangeID.Value=DBNull.Value;
				if(lnNICOld.Visible)
				{
					h.docGUID=lblfpNICOld.Text;
					h.isPosted=true;
				}
				else
				{
					h.docGUID=this.fpNICOld.PostedFile.FileName;
					h.isPosted=false;
				}
				h.isDocRequired=true;
				list.Add(h);
			}
			if(CheckRequest(this.lblBankAcctNo.Text,this.txtBankAcctNo.Text))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=32;
				h.fieldname="Bank Account #";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=this.txtBankAcctNo.Text;
				h.requestforchangeID.Value=DBNull.Value;
				if(lnBankNo.Visible)
				{
					h.docGUID=lblfpBankNo.Text;
					h.isPosted=true;
				}
				else
				{
					h.docGUID=this.fpBankNo.PostedFile.FileName;
					h.isPosted=false;
				}
				h.isDocRequired=true;
				list.Add(h);
			}
			if(CheckRequest(this.lblAccountDetails.Text,this.txtAccountDetails.Text))
			{
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=33;
				h.fieldname="Bank Account Details";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange=this.txtAccountDetails.Text;
				h.requestforchangeID.Value=DBNull.Value;
				if(lnBankDetail.Visible)
				{
					h.docGUID=lblfpBankDetail.Text;
					h.isPosted=true;
				}
				else
				{
					h.docGUID=this.fpBankDetail.PostedFile.FileName;
					h.isPosted=false;
				}
				h.isDocRequired=true;
				list.Add(h);
			}
			//if(this.fpPicture.PostedFile.ContentLength>0)
			//if(!fpPicture.Disabled)
			//{
			    if(fpPicture.MaxLength>1 || lnPicture.Visible)
		        {
				GeoRabtaSite.RequestHandler h=new RequestHandler();
				h.fieldno=43;
				h.fieldname="Employee Picture";
				h.requestdate=DateTime.Now.Date.ToShortDateString();
				h.requeststatus=1;
				h.requesttime=DateTime.Now.TimeOfDay.ToString();
				h.requisitecode=Session["user_id"].ToString();
				h.requestforchange="Picture Changed";
				h.requestforchangeID.Value=DBNull.Value;
				if(lnPicture.Visible)
				{
					h.docGUID=lblfpPicture.Text;
					h.isPosted=true;
				}
				else
				{
					h.docGUID=this.fpPicture.PostedFile.FileName;
					h.isPosted=false;
				}
				h.isDocRequired=true;
				list.Add(h);
				}
			
			
			if(list.Count<=0)
			{
				//Response.Write("<script>window.alert('There is no change in request(s). Thank You')</script>");
				this.lblCombine.Visible=true;
				this.lblCombine.Text="There is no change in request(s). Thank You";
				return;
			}
			else
			{
				string message="'<ul>Please attach/upload scan copy(ies) of following fields before sending your request";
				string messagefordis="Please attach/upload scan copy(ies) of following fields before sending your request \\n";
				string field="";
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler h=(GeoRabtaSite.RequestHandler)list[i];
					if(h.isDocRequired && h.docGUID=="")
					{
						field+="<li>"+h.fieldname+"</li>";
						messagefordis+=h.fieldname+"\\n";
					}
				}
				if(field.Length>0)
				{
					message+=field;
					message+="</ul>'";
					//Response.Write("<script>window.alert('"+messagefordis+"')</script>");
					this.lblCombine.Visible=true;
					this.lblCombine.Text=message;
					return;
				}
			}
			//===============================Check Previous Pending Request==================//
			con.Open();
			SqlTransaction trans=con.BeginTransaction();
			try
			{
				SqlCommand cmd=null;
				SqlDataReader rd=null;
				ArrayList removeList=new ArrayList();
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler h=(GeoRabtaSite.RequestHandler)list[i];
					cmd=new SqlCommand("select timeline,vrequired,replyonaccept from t_fieldsmgt where f_id="+h.fieldno+"",con);
					cmd.Transaction=trans;
					rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						int vRequired=Int32.Parse(rd[1].ToString());
						string replyonaccept=rd[2].ToString();
						rd.Close();
						if(vRequired==2)
						{
							if(h.fieldno==16)
							{
								//Date of Birth
								cmd=new SqlCommand("update t_employee set dateofbirth=@dateofbirth,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _dateofbirth=new SqlParameter("@dateofbirth",SqlDbType.DateTime);
								_dateofbirth.Value=h.requestforchange;
								cmd.Parameters.Add(_dateofbirth);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								removeList.Add(h);
								list.Remove(h);
								i--;
							}
							if(h.fieldno==24)
							{
								//Father Name
								cmd=new SqlCommand("update t_employee set fathername=@fathername,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _fathername=new SqlParameter("@fathername",SqlDbType.VarChar);
								_fathername.Value=h.requestforchange;
								cmd.Parameters.Add(_fathername);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								removeList.Add(h);
								list.Remove(h);
								i--;
							}
							if(h.fieldno==17)
							{
								//Gender
								cmd=new SqlCommand("update t_employee set gender=@gender,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _gender=new SqlParameter("@gender",SqlDbType.TinyInt);
								_gender.Value=h.requestforchangeID;
								cmd.Parameters.Add(_gender);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								removeList.Add(h);
								list.Remove(h);
								//Bulletin.PostMessage("Change of Gender Request",replyonaccept,"Geo Raabta",7,2,"Geo Raabta",Session["user_id"].ToString());
							    i--;
							}
							if(h.fieldno==18)
							{
								//BloodGroup
								cmd=new SqlCommand("update t_employee set bloodgroup=@bloodgroup,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _bloodgroup=new SqlParameter("@bloodgroup",SqlDbType.TinyInt);
								_bloodgroup.Value=h.requestforchangeID;
								cmd.Parameters.Add(_bloodgroup);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								removeList.Add(h);
								list.Remove(h);
								//Bulletin.PostMessage("Change of Bloodgroup Request",replyonaccept,"Geo Raabta",7,2,"Geo Raabta",Session["user_id"].ToString());
							    i--;
							}
							if(h.fieldno==26)
							{
								//Religion
								cmd=new SqlCommand("update t_employee set religion=@religion,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _religion=new SqlParameter("@religion",SqlDbType.Int);
								_religion.Value=h.requestforchangeID;
								cmd.Parameters.Add(_religion);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								removeList.Add(h);
								list.Remove(h);
								//Bulletin.PostMessage("Change of Religion Request",replyonaccept,"Geo Raabta",7,2,"Geo Raabta",Session["user_id"].ToString());
							    i--;
							}
							if(h.fieldno==37)
							{
								//Nick
								cmd=new SqlCommand("update t_employee set nick=@nick,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _nick=new SqlParameter("@nick",SqlDbType.VarChar);
								_nick.Value=h.requestforchange;
								cmd.Parameters.Add(_nick);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								removeList.Add(h);
								list.Remove(h);
								//Bulletin.PostMessage("Change of Nick Request",replyonaccept,"Geo Raabta",7,2,"Geo Raabta",Session["user_id"].ToString());
							    i--;
							}
							if(h.fieldno==22)
							{
								//Address
								cmd=new SqlCommand("update t_employee set address=@address,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _address=new SqlParameter("@address",SqlDbType.VarChar);
								_address.Value=h.requestforchange;
								cmd.Parameters.Add(_address);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								removeList.Add(h);
								list.Remove(h);
								//Bulletin.PostMessage("Change of Residential Address Request",replyonaccept,"Geo Raabta",7,2,"Geo Raabta",Session["user_id"].ToString());
							    i--;
							}
							if(h.fieldno==25)
							{
								//Marital Status
								cmd=new SqlCommand("update t_employee set maritialstatus=@maritialstatus,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _maritialstatus=new SqlParameter("@maritialstatus",SqlDbType.TinyInt);
								_maritialstatus.Value=h.requestforchangeID;
								cmd.Parameters.Add(_maritialstatus);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								removeList.Add(h);
								list.Remove(h);
							    i--;
							}
							if(h.fieldno==29)
							{
								//Email (Personal)
								cmd=new SqlCommand("update t_employee set email_personal=@email_personal,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _emailpersonal=new SqlParameter("@email_personal",SqlDbType.VarChar);
								_emailpersonal.Value=h.requestforchange;
								cmd.Parameters.Add(_emailpersonal);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								removeList.Add(h);
								list.Remove(h);
                                //Bulletin.PostMessage("Change of Email Address Request",replyonaccept,"Geo Raabta",7,2,"Geo Raabta",Session["user_id"].ToString()); 
							    i--;
							}
							if(h.fieldno==34)
							{
								//NTN#
								cmd=new SqlCommand("update t_employee set ntnno=@ntnno,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _ntnno=new SqlParameter("@ntnno",SqlDbType.VarChar);
								_ntnno.Value=h.requestforchange;
								cmd.Parameters.Add(_ntnno);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								removeList.Add(h);
								list.Remove(h);
							    i--;
							}
							if(h.fieldno==27)
							{
								//Telephone #(Home)
								cmd=new SqlCommand("update t_employee set telephone=@telephone,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _telephone=new SqlParameter("@telephone",SqlDbType.VarChar);
								_telephone.Value=h.requestforchange;
								cmd.Parameters.Add(_telephone);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								removeList.Add(h);
								list.Remove(h);
							    i--;
							}
							if(h.fieldno==28)
							{
								//Mobile #
								cmd=new SqlCommand("update t_employee set mobile=@mobile,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _mobile=new SqlParameter("@mobile",SqlDbType.VarChar);
								_mobile.Value=h.requestforchange;
								cmd.Parameters.Add(_mobile);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								removeList.Add(h);
								list.Remove(h);
							    i--;
							}
							if(h.fieldno==35)
							{
								//Nationality
								cmd=new SqlCommand("update t_employee set nationality=@nationality,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _nationality=new SqlParameter("@nationality",SqlDbType.Int);
								_nationality.Value=h.requestforchangeID;
								cmd.Parameters.Add(_nationality);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								removeList.Add(h);
							 	list.Remove(h);
							    i--;
							}
							if(h.fieldno==36)
							{
								//Nationality (Secondary)
								cmd=new SqlCommand("update t_employee set nationality2=@nationality2,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _nationality2=new SqlParameter("@nationality",SqlDbType.Int);
								_nationality2.Value=h.requestforchangeID;
								cmd.Parameters.Add(_nationality2);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								removeList.Add(h);
								list.Remove(h);
							    i--;
							}
							if(h.fieldno==19)
							{
								//Passport
								cmd=new SqlCommand("update t_employee set passportno=@passportno,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _passportno=new SqlParameter("@passportno",SqlDbType.VarChar);
								_passportno.Value=h.requestforchange;
								cmd.Parameters.Add(_passportno);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								removeList.Add(h);
								list.Remove(h);
							    i--;
							}
							if(h.fieldno==30)
							{
								//Next of Kin
								cmd=new SqlCommand("update t_employee set kin=@kin,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _kin=new SqlParameter("@kin",SqlDbType.VarChar);
								_kin.Value=h.requestforchange;
								cmd.Parameters.Add(_kin);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								removeList.Add(h);
								list.Remove(h);
							    i--;
							}
							if(h.fieldno==21)
							{
								//NIC (New)
								cmd=new SqlCommand("update t_employee set nic_new=@nic_new,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _nicnew=new SqlParameter("@nic_new",SqlDbType.VarChar);
								_nicnew.Value=h.requestforchange;
								cmd.Parameters.Add(_nicnew);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								removeList.Add(h);
								list.Remove(h);
							    i--;
							}
							if(h.fieldno==20)
							{
								//NIC (Old)
								cmd=new SqlCommand("update t_employee set nic_old=@nic_old,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _nicold=new SqlParameter("@nic_old",SqlDbType.VarChar);
								_nicold.Value=h.requestforchange;
								cmd.Parameters.Add(_nicold);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								removeList.Add(h);
								list.Remove(h);
								i--;
							}
							if(h.fieldno==32)
							{
								//Bank Account #
								cmd=new SqlCommand("update t_employee set bankaccountno=@bankaccountno,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _bankaccountno=new SqlParameter("@bankaccountno",SqlDbType.VarChar);
								_bankaccountno.Value=h.requestforchange;
								cmd.Parameters.Add(_bankaccountno);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								removeList.Add(h);
								list.Remove(h);
								i--;
							}
							if(h.fieldno==33)
							{
								//Bank Account Detail
								cmd=new SqlCommand("update t_employee set bankaccountdetails=@bankaccountdetails,profilemodifyby='"+h.requisitecode+"' where pcode='"+h.requisitecode+"'",con);
								SqlParameter _bankaccountdetails=new SqlParameter("@bankaccountdetails",SqlDbType.VarChar);
								_bankaccountdetails.Value=h.requestforchange;
								cmd.Parameters.Add(_bankaccountdetails);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
								removeList.Add(h);
								list.Remove(h);
								i--;
							}
						}
					}
				}
				Session["rpRequest"]=removeList;
				cmd=new SqlCommand("select * from t_employeerequests where requisitecode='"+Session["user_id"].ToString()+"' and requeststatus=1",con);
				cmd.Transaction=trans;
				rd=cmd.ExecuteReader();
				rd.Read();
				if(!rd.HasRows)
				{
					rd.Close();
					//===============================Enter New Request in DB=========================//
					for(int i=0;i<list.Count;i++)
					{
						GeoRabtaSite.RequestHandler h=(GeoRabtaSite.RequestHandler)list[i];
						cmd=new SqlCommand("select timeline,replyatrequest from t_fieldsmgt where f_id="+h.fieldno+"",con);
						cmd.Transaction=trans;
						rd=cmd.ExecuteReader();
						rd.Read();
						if(rd.HasRows)
						{
							int workingdays=Int32.Parse(rd[0].ToString());
							h.requesttimeline=DateTime.Now.Date.AddDays(workingdays).ToShortDateString();
							string replyatrequest=rd[1].ToString();
							rd.Close();
							cmd=new SqlCommand("insert into t_employeerequests(fieldno,fieldname,requestforchange,requestforchangeID,requisitecode,requestdate,requesttime,requesttimeline,requeststatus,documentID) values(@fieldno,@fieldname,@requestforchange,@requestforchangeID,@requisitecode,getdate(),@requesttime,@requesttimeline,@requeststatus,@documentID)",con);
							cmd.Transaction=trans;
							SqlParameter _fieldno=new SqlParameter("@fieldno",SqlDbType.Int);
							SqlParameter _fieldname=new SqlParameter("@fieldname",SqlDbType.VarChar);
							SqlParameter _requestforchange=new SqlParameter("@requestforchange",SqlDbType.VarChar);
							SqlParameter _requisitecode=new SqlParameter("@requisitecode",SqlDbType.VarChar);
							//SqlParameter _requestdate=new SqlParameter("@requestdate",SqlDbType.DateTime);
							SqlParameter _requesttime=new SqlParameter("@requesttime",SqlDbType.VarChar);
							SqlParameter _requesttimeline=new SqlParameter("@requesttimeline",SqlDbType.DateTime);
							SqlParameter _requeststatus=new SqlParameter("@requeststatus",SqlDbType.Int);
							SqlParameter _documentID=new SqlParameter("@documentID",SqlDbType.Text);
							_fieldno.Value=h.fieldno;
							_fieldname.Value=h.fieldname;
							_requestforchange.Value=h.requestforchange;
							_requisitecode.Value=h.requisitecode;
							//_requestdate.Value=h.requestdate;
							_requesttime.Value=h.requesttime;
							_requesttimeline.Value=h.requesttimeline;
							_requeststatus.Value=h.requeststatus;
							if(h.isDocRequired)
							{
								Guid g=Guid.NewGuid();
								string filepath=Server.MapPath(@"tempEmployee\");
								string filename=g.ToString();
								if(h.fieldno==16)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpDateofBirth.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\16"+Path.GetExtension(lblfpDateofBirth.Text)),dPath);
									}
									else
									{
										this.fpDateofBirth.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpDateofBirth.PostedFile.FileName));
									}
								}
								if(h.fieldno==24)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpFatherName.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\24"+Path.GetExtension(lblfpFatherName.Text)),dPath);
									}
									else
									{
										this.fpFatherName.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpFatherName.PostedFile.FileName));
									}
								}
								if(h.fieldno==25)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpMaritalStatus.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\25"+Path.GetExtension(lblfpMaritalStatus.Text)),dPath);
									}
									else
									{
										this.fpMartialStatus.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpMartialStatus.PostedFile.FileName));
									}
								}
								if(h.fieldno==35)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpNationality.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\35"+Path.GetExtension(lblfpNationality.Text)),dPath);
									}
									else
									{
										this.fpNationality.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpNationality.PostedFile.FileName));
									}
								}
								if(h.fieldno==36)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpNationality2.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\36"+Path.GetExtension(lblfpNationality2.Text)),dPath);
									}
									else
									{
										this.fpNationality2.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpNationality2.PostedFile.FileName));
									}
								}
								if(h.fieldno==19)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpPassprot.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\19"+Path.GetExtension(lblfpPassprot.Text)),dPath);
									}
									else
									{
										this.fpPassport.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpPassport.PostedFile.FileName));
									}
								}
								if(h.fieldno==30)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpNextofKin.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\30"+Path.GetExtension(lblfpNextofKin.Text)),dPath);
									}
									else
									{
										this.fpNextofKin.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpNextofKin.PostedFile.FileName));
									}
								}
								if(h.fieldno==21)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpNICNew.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\21"+Path.GetExtension(lblfpNICNew.Text)),dPath);
									}
									else
									{
										this.fpNICNew.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpNICNew.PostedFile.FileName));
									}
								}
								if(h.fieldno==20)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpNICOld.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\20"+Path.GetExtension(lblfpNICOld.Text)),dPath);
									}
									else
									{
										this.fpNICOld.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpNICOld.PostedFile.FileName));
									}
								}
								if(h.fieldno==32)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpBankNo.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\32"+Path.GetExtension(lblfpBankNo.Text)),dPath);
									}
									else
									{
										this.fpBankNo.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpBankNo.PostedFile.FileName));
									}
								}
								if(h.fieldno==33)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpBankDetail.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\33"+Path.GetExtension(lblfpBankDetail.Text)),dPath);
									}
									else
									{
										this.fpBankDetail.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpBankDetail.PostedFile.FileName));
									}
								}
								//=====================Picture=====================//
								if(h.fieldno==43)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpPicture.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\43"+Path.GetExtension(lblfpPicture.Text)),dPath);
									}
									else
									{
										this.fpPicture.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpPicture.PostedFile.FileName));
									}
								}
								//=================================================//
								_documentID.Value=g.ToString();
							}
							else
							{
								_documentID.Value=DBNull.Value;
							}
							cmd.Parameters.Add(_fieldno);
							cmd.Parameters.Add(_fieldname);
							cmd.Parameters.Add(_requestforchange);
							cmd.Parameters.Add(h.requestforchangeID);
							cmd.Parameters.Add(_requisitecode);
							cmd.Parameters.Add(_requesttime);
							cmd.Parameters.Add(_requesttimeline);
							cmd.Parameters.Add(_requeststatus);
							cmd.Parameters.Add(_documentID);
							cmd.ExecuteNonQuery();
							//Bulletin.PostMessage("Change of "+h.fieldname,replyatrequest,"Geo Raabta",7,2,"Geo Raabta",Session["user_id"].ToString(),con,trans);
						    Bulletin.PostMessage(con,trans,replyatrequest,"Geo Raabta",Session["user_id"].ToString(),1);
						}
						else
						{
							//Response.Write("Process flow not defined properly for request field"+h.fieldname+". Request failed to send");
							lblCombine.Visible=true;
							lblCombine.Text="Process flow not defined properly for request field"+h.fieldname+". Request failed to send";
							trans.Rollback();
							return;
						}
					}
					trans.Commit();
					con.Close();
					lblCombine.Visible=true;
					lblCombine.Text="Your request has been sent successfully. Please check your MY REQUEST tab for Requsition IDs";
					docSetting(true);
					//====================================================================================//
				}
				else
				{
					ArrayList requestList=new ArrayList();
					ArrayList pendingList=new ArrayList();
					rd.Close();
					cmd=new SqlCommand("select * from t_employeerequests where requisitecode='"+Session["user_id"].ToString()+"' and requeststatus=1",con);
					cmd.Transaction=trans;
					rd=cmd.ExecuteReader();
					while(rd.Read())
					{
						requestList.Add(rd[2].ToString());
					}
					rd.Close();
					for(int i=0;i<list.Count;i++)
					{
						GeoRabtaSite.RequestHandler h=(GeoRabtaSite.RequestHandler)list[i];
						string Val=""+h.fieldno+"";	
						if(requestList.Contains(Val))
						{
							pendingList.Add(h);
						}
					}
					if(pendingList.Count>0)
					{
						string Message="Following requisite field(s) already pending. Do you want to replace below field(s) with your previous requisition? <br>";
						for(int j=0;j<pendingList.Count;j++)
						{
							GeoRabtaSite.RequestHandler rh=(GeoRabtaSite.RequestHandler)pendingList[j];
							int Val=j;
							Val+=1;
							Message+=Val+". "+rh.fieldname+",<br>";
						}
						Message=Message.Remove(Message.Length-5,5);
						//Response.Write("<script>window.alert('Request already pending on the requisite field. Do you want to Re-send it?')</script>");
						//Response.Write("<script>window.alert('"+Message+"')</script>");
						this.lblCombine.Visible=true;
						this.lblCombine.Text=Message;
						this.btnUpdate.Enabled=false;
						this.btnIgnore.Visible=true;
						this.btnResend.Visible=true;
						Session["pendingRequest"]=pendingList;
						Session["cRequest"]=list;
						//if(trans.Connection==ConnectionState.Open)
						trans.Commit();
						return;
					}
					else
					{
						//==============New Request After Check Pending Request on User===========//
						for(int i=0;i<list.Count;i++)
						{
							GeoRabtaSite.RequestHandler h=(GeoRabtaSite.RequestHandler)list[i];
							cmd=new SqlCommand("select timeline,replyatrequest from t_fieldsmgt where f_id="+h.fieldno+"",con);
							cmd.Transaction=trans;
							rd=cmd.ExecuteReader();
							rd.Read();
							if(rd.HasRows)
							{
								int workingdays=Int32.Parse(rd[0].ToString());
								h.requesttimeline=DateTime.Now.Date.AddDays(workingdays).ToShortDateString();
								string replyatrequest=rd[1].ToString();
								rd.Close();
								cmd=new SqlCommand("insert into t_employeerequests(fieldno,fieldname,requestforchange,requestforchangeID,requisitecode,requestdate,requesttime,requesttimeline,requeststatus,documentID) values(@fieldno,@fieldname,@requestforchange,@requestforchangeID,@requisitecode,getdate(),@requesttime,@requesttimeline,@requeststatus,@documentID)",con);
								cmd.Transaction=trans;
								SqlParameter _fieldno=new SqlParameter("@fieldno",SqlDbType.Int);
								SqlParameter _fieldname=new SqlParameter("@fieldname",SqlDbType.VarChar);
								SqlParameter _requestforchange=new SqlParameter("@requestforchange",SqlDbType.VarChar);
								SqlParameter _requisitecode=new SqlParameter("@requisitecode",SqlDbType.VarChar);
								//SqlParameter _requestdate=new SqlParameter("@requestdate",SqlDbType.DateTime);
								SqlParameter _requesttime=new SqlParameter("@requesttime",SqlDbType.VarChar);
								SqlParameter _requesttimeline=new SqlParameter("@requesttimeline",SqlDbType.DateTime);
								SqlParameter _requeststatus=new SqlParameter("@requeststatus",SqlDbType.Int);
								SqlParameter _documentID=new SqlParameter("@documentID",SqlDbType.Text);
								_fieldno.Value=h.fieldno;
								_fieldname.Value=h.fieldname;
								_requestforchange.Value=h.requestforchange;
								_requisitecode.Value=h.requisitecode;
								//_requestdate.Value=h.requestdate;
								_requesttime.Value=h.requesttime;
								_requesttimeline.Value=h.requesttimeline;
								_requeststatus.Value=h.requeststatus;
								if(h.isDocRequired)
								{
									Guid g=Guid.NewGuid();
									string filepath=Server.MapPath(@"tempEmployee\");
									string filename=g.ToString();
									if(h.fieldno==16)
									{
										if(h.isPosted)
										{
											string dPath=filepath+filename+Path.GetExtension(lblfpDateofBirth.Text);
											File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\16"+Path.GetExtension(lblfpDateofBirth.Text)),dPath);
										}
										else
										{
											this.fpDateofBirth.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpDateofBirth.PostedFile.FileName));
										}
									}
									if(h.fieldno==24)
									{
										if(h.isPosted)
										{
											string dPath=filepath+filename+Path.GetExtension(lblfpFatherName.Text);
											File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\24"+Path.GetExtension(lblfpFatherName.Text)),dPath);
										}
										else
										{
											this.fpFatherName.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpFatherName.PostedFile.FileName));
										}
									}
									if(h.fieldno==25)
									{
										if(h.isPosted)
										{
											string dPath=filepath+filename+Path.GetExtension(lblfpMaritalStatus.Text);
											File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\25"+Path.GetExtension(lblfpMaritalStatus.Text)),dPath);
										}
										else
										{
											this.fpMartialStatus.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpMartialStatus.PostedFile.FileName));
										}
									}
									if(h.fieldno==35)
									{
										if(h.isPosted)
										{
											string dPath=filepath+filename+Path.GetExtension(lblfpNationality.Text);
											File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\35"+Path.GetExtension(lblfpNationality.Text)),dPath);
										}
										else
										{
											this.fpNationality.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpNationality.PostedFile.FileName));
										}
									}
									if(h.fieldno==36)
									{
										if(h.isPosted)
										{
											string dPath=filepath+filename+Path.GetExtension(lblfpNationality2.Text);
											File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\36"+Path.GetExtension(lblfpNationality2.Text)),dPath);
										}
										else
										{
											this.fpNationality2.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpNationality2.PostedFile.FileName));
										}
									}
									if(h.fieldno==19)
									{
										if(h.isPosted)
										{
											string dPath=filepath+filename+Path.GetExtension(lblfpPassprot.Text);
											File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\19"+Path.GetExtension(lblfpPassprot.Text)),dPath);
										}
										else
										{
											this.fpPassport.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpPassport.PostedFile.FileName));
										}
									}
									if(h.fieldno==30)
									{
										if(h.isPosted)
										{
											string dPath=filepath+filename+Path.GetExtension(lblfpNextofKin.Text);
											File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\30"+Path.GetExtension(lblfpNextofKin.Text)),dPath);
										}
										else
										{
											this.fpNextofKin.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpNextofKin.PostedFile.FileName));
										}
									}
									if(h.fieldno==21)
									{
										if(h.isPosted)
										{
											string dPath=filepath+filename+Path.GetExtension(lblfpNICNew.Text);
											File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\21"+Path.GetExtension(lblfpNICNew.Text)),dPath);
										}
										else
										{
											this.fpNICNew.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpNICNew.PostedFile.FileName));
										}
									}
									if(h.fieldno==20)
									{
										if(h.isPosted)
										{
											string dPath=filepath+filename+Path.GetExtension(lblfpNICOld.Text);
											File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\20"+Path.GetExtension(lblfpNICOld.Text)),dPath);
										}
										else
										{
											this.fpNICOld.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpNICOld.PostedFile.FileName));
										}
									}
									if(h.fieldno==32)
									{
										if(h.isPosted)
										{
											string dPath=filepath+filename+Path.GetExtension(lblfpBankNo.Text);
											File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\32"+Path.GetExtension(lblfpBankNo.Text)),dPath);
										}
										else
										{
											this.fpBankNo.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpBankNo.PostedFile.FileName));
										}
									}
									if(h.fieldno==33)
									{
										if(h.isPosted)
										{
											string dPath=filepath+filename+Path.GetExtension(lblfpBankDetail.Text);
											File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\33"+Path.GetExtension(lblfpBankDetail.Text)),dPath);
										}
										else
										{
											this.fpBankDetail.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpBankDetail.PostedFile.FileName));
										}
									}
									if(h.fieldno==43)
									{
										if(h.isPosted)
										{
											string dPath=filepath+filename+Path.GetExtension(lblfpPicture.Text);
											File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\43"+Path.GetExtension(lblfpPicture.Text)),dPath);
										}
										else
										{
											this.fpPicture.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpPicture.PostedFile.FileName));
										}
									}
									_documentID.Value=g.ToString();
								}
								else
								{
									_documentID.Value=DBNull.Value;
								}
								cmd.Parameters.Add(_fieldno);
								cmd.Parameters.Add(_fieldname);
								cmd.Parameters.Add(_requestforchange);
								cmd.Parameters.Add(h.requestforchangeID);
								cmd.Parameters.Add(_requisitecode);
								cmd.Parameters.Add(_requesttime);
								cmd.Parameters.Add(_requesttimeline);
								cmd.Parameters.Add(_requeststatus);
								cmd.Parameters.Add(_documentID);
								cmd.ExecuteNonQuery();
                                Bulletin.PostMessage(con,trans,replyatrequest,"Geo Raabta",Session["user_id"].ToString(),1);
							}
							else
							{
								//Response.Write("Process flow not defined properly for request field"+h.fieldname+". Request failed to send");
								lblCombine.Visible=true;
								lblCombine.Text="Process flow not defined properly for request field"+h.fieldname+". Request failed to send";
								trans.Rollback();
								return;
							}
						}
						trans.Commit();
						con.Close();
						lblCombine.Visible=true;
						lblCombine.Text="Your request has been sent successfully. Please check your MY REQUEST tab for Requsition IDs";
						docSetting(true);
						//=======================================================================//
					}
				}
				Session["pendingRequest"]="";
				Session["cRequest"]="";
				con.Close();
			}
			catch(Exception ex)
			{
				Response.Write(ex.Message);
				trans.Rollback();
				con.Close();
			}
			/*string filepath=Server.MapPath(@"tempEmployee\");
			string filename=Path.GetFileName(this.FileToUpload.PostedFile.FileName);
			string path="";
			string fileExtension=Path.GetExtension(FileToUpload.PostedFile.FileName);
			if((FileToUpload.PostedFile != null) && (FileToUpload.PostedFile.ContentLength > 0))
			{
				if((fileExtension.ToLower()==".jpg") || fileExtension.ToLower()==".bmp" || fileExtension.ToLower()==".png" || fileExtension.ToLower()==".gif")
				{																																			
					if(FileToUpload.PostedFile.ContentLength <=25600)
					{
						this.FileToUpload.PostedFile.SaveAs(filepath+filename);
						path=filepath+filename;
					}
					else
					{
						Response.Write("<script>window.alert('Picture File size is too large. File size should be 25kb or less')</script>");
						return;
					}
				}
				else
				{
					Response.Write("<script>window.alert('Picture file is incorrect format. JPG,BMP,PNG and GIF formatted file allow to upload')</script>");
					return;
				}
			}
			else
			{
				filename="NULL";          			
			}
			string var3="";
			if(this.calendarDOB.SelectedDate.ToString()!="1/1/0001 12:00:00 AM")
			{
				var3="'"+DateTime.Parse(this.calendarDOB.SelectedDate.ToString())+"'";
			}
			else
			{
				var3="NULL";
			}
			con.Open();
			string getRec="select pcode from t_personalinfo where pcode='"+this.lblPCode.Text.Trim()+"' And isrequested='"+1+"'";
			SqlCommand cm=new SqlCommand(getRec,con);
			SqlDataReader r=cm.ExecuteReader();
			r.Read();
			if(r.HasRows)
			{
				r.Close();
				//string updateInfo="update t_personalinfo set dateofbirth="+ var3+",gender='"+Convert.ToInt32(this.RadioButtonList1.SelectedValue.ToString())+"',bloodgroup='"+Convert.ToInt32(this.ddBloodGrp.SelectedValue.ToString())+"',fathername='"+this.txtFName.Text+"',passportno='"+this.txtPassport.Text+"',address='"+this.txtAddress.Text+"',maritialstatus='"+Convert.ToInt32(this.ddStatus.SelectedValue.ToString())+"',email_personal='"+this.txtEmail.Text+"',religion='"+Convert.ToInt32(this.ddReligion.SelectedValue.ToString())+"',telephone='"+this.txtContactNo.Text+"',mobile='"+this.txtMobile.Text+"',nic_new='"+this.txtNewNic.Text+"',nic_old='"+this.txtOldNic.Text+"',kin='"+this.txtKin.Text+"',bankaccountno='"+this.txtBankAcctNo.Text+"',ntnno='"+this.txtNTN.Text+"',bankaccountdetails='"+this.txtAccountDetails.Text+"',isactive='"+1+"',requestdate='"+DateTime.Now+"',isrequested='"+1+"',requestphoto= '"+filename+ "',nationality='"+ this.ddNationality.SelectedValue.ToString() +"',nationality2='"+ this.ddNationality2.SelectedValue.ToString() +"',nick='"+ this.txtNick.Text +"' where pcode='"+this.lblPCode.Text+"' And isrequested='"+1+"'";
				string updateInfo="UPDATE  dbo.t_personalinfo "+
					"SET dateofbirth = @dateofbirth, gender = @gender, bloodgroup = @bloodgroup, fathername = @fathername, passportno = @passportno, "+
					"address = @address, maritialstatus = @maritialstatus, email_personal = @email_personal, religion = @religion, telephone = @telephone, "+ 
					"mobile = @mobile, nic_new = @nic_new, nic_old = @nic_old, kin = @kin, bankaccountno = @bankaccountno, ntnno = @ntnno, "+
					"bankaccountdetails = @bankaccountdetails, isactive = 1, requestdate = @requestdate, isrequested = 1, requestphoto = @requestphoto ,nick=@nick,nationality=@nationality,nationality2=@nationality2 "+
					"WHERE (pcode = @pcode and isrequested=1)";
				SqlCommand cmd=new SqlCommand(updateInfo,con);
				SqlParameter _pcode=new SqlParameter("@pcode",SqlDbType.Char);
				SqlParameter _dateofbirth=new SqlParameter("@dateofbirth",SqlDbType.SmallDateTime);
				SqlParameter _gender=new SqlParameter("@gender",SqlDbType.TinyInt);
				SqlParameter _bloodgroup=new SqlParameter("@bloodgroup",SqlDbType.TinyInt);
				SqlParameter _fathername=new SqlParameter("@fathername",SqlDbType.VarChar);
				SqlParameter _passportno=new SqlParameter("@passportno",SqlDbType.VarChar);
				SqlParameter _address=new SqlParameter("@address",SqlDbType.Char);
				SqlParameter _maritialstatus=new SqlParameter("@maritialstatus",SqlDbType.TinyInt);
				SqlParameter _email_personal=new SqlParameter("@email_personal",SqlDbType.VarChar);
				SqlParameter _religion=new SqlParameter("@religion",SqlDbType.Int);
				SqlParameter _telephone=new SqlParameter("@telephone",SqlDbType.VarChar);
				SqlParameter _mobile=new SqlParameter("@mobile",SqlDbType.VarChar);
				SqlParameter _nic_new=new SqlParameter("@nic_new",SqlDbType.VarChar);
				SqlParameter _nic_old=new SqlParameter("@nic_old",SqlDbType.VarChar);
				SqlParameter _kin=new SqlParameter("@kin",SqlDbType.VarChar);
				SqlParameter _bankaccountno=new SqlParameter("@bankaccountno",SqlDbType.VarChar);
				SqlParameter _ntnno=new SqlParameter("@ntnno",SqlDbType.VarChar);
				SqlParameter _bankaccountdetails=new SqlParameter("@bankaccountdetails",SqlDbType.VarChar);
				SqlParameter _requestdate=new SqlParameter("@requestdate",SqlDbType.SmallDateTime);
				SqlParameter _requestedphoto=new SqlParameter("@requestphoto",SqlDbType.Text);
				SqlParameter _nick=new SqlParameter("@nick",SqlDbType.VarChar);
				SqlParameter _nationality=new SqlParameter("@nationality",SqlDbType.Int);
				SqlParameter _nationality2=new SqlParameter("@nationality2",SqlDbType.Int);
                
				_pcode.Value=this.lblPCode.Text;
				_dateofbirth.Value=this.calendarDOB.SelectedDate.ToString();
				_gender.Value=this.RadioButtonList1.SelectedValue.ToString();
				_bloodgroup.Value=this.ddBloodGrp.SelectedValue.ToString();
				_fathername.Value=this.txtFName.Text;
				_passportno.Value=this.txtPassport.Text;
				_address.Value=this.txtAddress.Text; 
				_maritialstatus.Value=this.ddStatus.SelectedValue.ToString();
				_email_personal.Value=this.txtEmail.Text;
				_religion.Value=this.ddReligion.SelectedValue.ToString();
				_telephone.Value=this.txtContactNo.Text;
				_mobile.Value=this.txtMobile.Text;
				_nic_new.Value=this.txtNewNic.Text;
				_nic_old.Value=this.txtOldNic.Text;
				_kin.Value=this.txtKin.Text;
				_bankaccountno.Value=this.txtBankAcctNo.Text;
				_ntnno.Value=this.txtNTN.Text;
				_bankaccountdetails.Value=this.txtAccountDetails.Text;
				_requestdate.Value=DateTime.Now.ToString();
				_requestedphoto.Value=filename;
				_nick.Value=this.txtNick.Text;
				_nationality.Value=this.ddNationality.SelectedValue.ToString();
				_nationality2.Value=this.ddNationality2.SelectedValue.ToString();
				
				cmd.Parameters.Add(_pcode);
				cmd.Parameters.Add(_dateofbirth);
				cmd.Parameters.Add(_gender);
				cmd.Parameters.Add(_bloodgroup);
				cmd.Parameters.Add(_fathername);
				cmd.Parameters.Add(_passportno);
				cmd.Parameters.Add(_address);
				cmd.Parameters.Add(_maritialstatus);
				cmd.Parameters.Add(_email_personal);
				cmd.Parameters.Add(_religion);
				cmd.Parameters.Add(_telephone);
				cmd.Parameters.Add(_mobile);
				cmd.Parameters.Add(_nic_new);
				cmd.Parameters.Add(_nic_old);
				cmd.Parameters.Add(_kin);
				cmd.Parameters.Add(_bankaccountno);
				cmd.Parameters.Add(_ntnno);
				cmd.Parameters.Add(_bankaccountdetails);
				cmd.Parameters.Add(_requestdate);
				cmd.Parameters.Add(_requestedphoto);
				cmd.Parameters.Add(_nick);
				cmd.Parameters.Add(_nationality);
				cmd.Parameters.Add(_nationality2);
				int i=cmd.ExecuteNonQuery();
				lblCombine.Visible=true;
				lblCombine.Text="Your request has been sent successfully"; 
			}
			else
			{
				r.Close();
				//string updateInfo="insert into t_personalinfo values('"+this.lblPCode.Text+"',"+ var3 +",'"+Convert.ToInt32(this.RadioButtonList1.SelectedValue.ToString())+"','"+Convert.ToInt32(this.ddBloodGrp.SelectedValue.ToString())+"','"+this.txtFName.Text+"','"+this.txtPassport.Text+"','"+this.txtAddress.Text+"','"+Convert.ToInt32(this.ddStatus.SelectedValue.ToString())+"','"+this.txtEmail.Text+"','"+Convert.ToInt32(this.ddReligion.SelectedValue.ToString())+"','"+this.txtContactNo.Text+"','"+this.txtMobile.Text+"','"+this.txtNewNic.Text+"','"+this.txtOldNic.Text+"','"+this.txtKin.Text+"','"+this.txtBankAcctNo.Text+"','"+this.txtNTN.Text+"','"+this.txtAccountDetails.Text+"','"+1+"','"+DateTime.Now+"','"+1+"',"+filename+",'"+ this.txtNick.Text +"','"+ this.ddNationality.SelectedValue.ToString() +"','"+ this.ddNationality2.SelectedValue.ToString() +"' )";
				string updateInfo="INSERT INTO dbo.t_personalinfo "+
					"(pcode, dateofbirth, gender, bloodgroup, fathername, passportno, address, maritialstatus, email_personal, religion, telephone, mobile, nic_new, "+
					"nic_old, kin, bankaccountno, ntnno, bankaccountdetails, isactive, requestdate, isrequested, requestphoto,nick,nationality,nationality2) "+
					"VALUES (@pcode, @dateofbirth, @gender, @bloodgroup, @fathername, @passportno, @address, @maritialstatus, @email_personal, @religion, @telephone, @mobile, @nic_new, "+ 
					"@nic_old, @kin, @bankaccountno, @ntnno, @bankaccountdetails, 1, @requestdate,1, @requestphoto,@nick,@nationality,@nationality2) ";
				SqlCommand cmd=new SqlCommand(updateInfo,con);
				SqlParameter _pcode=new SqlParameter("@pcode",SqlDbType.Char);
				SqlParameter _dateofbirth=new SqlParameter("@dateofbirth",SqlDbType.SmallDateTime);
				SqlParameter _gender=new SqlParameter("@gender",SqlDbType.TinyInt);
				SqlParameter _bloodgroup=new SqlParameter("@bloodgroup",SqlDbType.TinyInt);
				SqlParameter _fathername=new SqlParameter("@fathername",SqlDbType.VarChar);
				SqlParameter _passportno=new SqlParameter("@passportno",SqlDbType.VarChar);
				SqlParameter _address=new SqlParameter("@address",SqlDbType.Char);
				SqlParameter _maritialstatus=new SqlParameter("@maritialstatus",SqlDbType.TinyInt);
				SqlParameter _email_personal=new SqlParameter("@email_personal",SqlDbType.VarChar);
				SqlParameter _religion=new SqlParameter("@religion",SqlDbType.Int);
				SqlParameter _telephone=new SqlParameter("@telephone",SqlDbType.VarChar);
				SqlParameter _mobile=new SqlParameter("@mobile",SqlDbType.VarChar);
				SqlParameter _nic_new=new SqlParameter("@nic_new",SqlDbType.VarChar);
				SqlParameter _nic_old=new SqlParameter("@nic_old",SqlDbType.VarChar);
				SqlParameter _kin=new SqlParameter("@kin",SqlDbType.VarChar);
				SqlParameter _bankaccountno=new SqlParameter("@bankaccountno",SqlDbType.VarChar);
				SqlParameter _ntnno=new SqlParameter("@ntnno",SqlDbType.VarChar);
				SqlParameter _bankaccountdetails=new SqlParameter("@bankaccountdetails",SqlDbType.VarChar);
				SqlParameter _requestdate=new SqlParameter("@requestdate",SqlDbType.SmallDateTime);
				SqlParameter _requestedphoto=new SqlParameter("@requestphoto",SqlDbType.Text);
				SqlParameter _nick=new SqlParameter("@nick",SqlDbType.VarChar);
				SqlParameter _nationality=new SqlParameter("@nationality",SqlDbType.Int);
				SqlParameter _nationality2=new SqlParameter("@nationality2",SqlDbType.Int);
                
				_pcode.Value=this.lblPCode.Text;
				_dateofbirth.Value=this.calendarDOB.SelectedDate.ToString();
				_gender.Value=this.RadioButtonList1.SelectedValue.ToString();
				_bloodgroup.Value=this.ddBloodGrp.SelectedValue.ToString();
				_fathername.Value=this.txtFName.Text;
				_passportno.Value=this.txtPassport.Text;
				_address.Value=this.txtAddress.Text; 
				_maritialstatus.Value=this.ddStatus.SelectedValue.ToString();
				_email_personal.Value=this.txtEmail.Text;
				_religion.Value=this.ddReligion.SelectedValue.ToString();
				_telephone.Value=this.txtContactNo.Text;
				_mobile.Value=this.txtMobile.Text;
				_nic_new.Value=this.txtNewNic.Text;
				_nic_old.Value=this.txtOldNic.Text;
				_kin.Value=this.txtKin.Text;
				_bankaccountno.Value=this.txtBankAcctNo.Text;
				_ntnno.Value=this.txtNTN.Text;
				_bankaccountdetails.Value=this.txtAccountDetails.Text;
				_requestdate.Value=DateTime.Now.ToString();
				_requestedphoto.Value=filename;
				_nick.Value=this.txtNick.Text;
				_nationality.Value=this.ddNationality.SelectedValue.ToString();
				_nationality2.Value=this.ddNationality2.SelectedValue.ToString();
				cmd.Parameters.Add(_pcode);
				cmd.Parameters.Add(_dateofbirth);
				cmd.Parameters.Add(_gender);
				cmd.Parameters.Add(_bloodgroup);
				cmd.Parameters.Add(_fathername);
				cmd.Parameters.Add(_passportno);
				cmd.Parameters.Add(_address);
				cmd.Parameters.Add(_maritialstatus);
				cmd.Parameters.Add(_email_personal);
				cmd.Parameters.Add(_religion);
				cmd.Parameters.Add(_telephone);
				cmd.Parameters.Add(_mobile);
				cmd.Parameters.Add(_nic_new);
				cmd.Parameters.Add(_nic_old);
				cmd.Parameters.Add(_kin);
				cmd.Parameters.Add(_bankaccountno);
				cmd.Parameters.Add(_ntnno);
				cmd.Parameters.Add(_bankaccountdetails);
				cmd.Parameters.Add(_requestdate);
				cmd.Parameters.Add(_requestedphoto);
				cmd.Parameters.Add(_nick);
				cmd.Parameters.Add(_nationality);
				cmd.Parameters.Add(_nationality2);
				int i=cmd.ExecuteNonQuery();
				lblCombine.Visible=true;
				lblCombine.Text="Your request has been sent successfully"; 
			}
			con.Close();*/
		}

		
		/*private void btnNew_Click(object sender, System.EventArgs e)
		{
			if(!IsRefresh)
			{
				this.ValidationSummary1.Visible=true;
				if(IsValid)
				{
					this.btnNew.Text="Add New";
					this.ValidationSummary1.Visible=false;
					this.lblFamilyMsg.Visible=false;			
					this.Label2.Visible=true;
					this.btnSubmitFamily.Visible=true;
					FamilyId=0;
					//string var="NULL";
					this.Label2.Visible=true;
					if(this.lblInvisible.Text =="&nbsp;")
					{
						dFamily=(DataSet)Session["dFamily"];
						this.DataGrid1.Items[Int32.Parse(this.txtFamilyName.ToolTip)].Cells[0].Text=this.txtFamilyName.Text;
						dFamily.Tables["FamilyInfo"].Rows[Int32.Parse(this.txtFamilyName.ToolTip)][0]=this.txtFamilyName.Text;
						if(this.rdRelation.SelectedValue !="0")
						{
							_enumRelation IsRelation=(_enumRelation)Int32.Parse(this.rdRelation.SelectedValue.ToString());
							this.DataGrid1.Items[Int32.Parse(this.txtFamilyName.ToolTip)].Cells[1].Text=IsRelation.ToString();
							dFamily.Tables["FamilyInfo"].Rows[Int32.Parse(this.txtFamilyName.ToolTip)][1]=IsRelation.ToString();
						}
						if(this.rdStatus.SelectedValue !="0")
						{
							_enumStatus IsStatus=(_enumStatus)Int32.Parse(this.rdStatus.SelectedValue.ToString());
							this.DataGrid1.Items[Int32.Parse(this.txtFamilyName.ToolTip)].Cells[2].Text=IsStatus.ToString();
							dFamily.Tables["FamilyInfo"].Rows[Int32.Parse(this.txtFamilyName.ToolTip)][2]=IsStatus.ToString();
						}		
					
						this.DataGrid1.Items[Int32.Parse(this.txtFamilyName.ToolTip)].Cells[3].Text=this.rdFamilyGender.SelectedItem.Text;
						dFamily.Tables["FamilyInfo"].Rows[Int32.Parse(this.txtFamilyName.ToolTip)][3]=this.rdFamilyGender.SelectedItem.Text;

						this.DataGrid1.Items[Int32.Parse(this.txtFamilyName.ToolTip)].Cells[4].Text=this.txtOccupation.Text;
						dFamily.Tables["FamilyInfo"].Rows[Int32.Parse(this.txtFamilyName.ToolTip)][4]=this.txtOccupation.Text;
						this.DataGrid1.Items[Int32.Parse(this.txtFamilyName.ToolTip)].Cells[5].Text=this.CalendarPopup1.SelectedDate.ToString("d MMM,yyyy");
						dFamily.Tables["FamilyInfo"].Rows[Int32.Parse(this.txtFamilyName.ToolTip)][5]=this.CalendarPopup1.SelectedDate.ToString("d MMM,yyyy");
						
						if(this.ddDependent.SelectedValue !="0")
						{
							_enumGender IsDependent=(_enumGender)Int32.Parse(this.ddDependent.SelectedValue.ToString());
							this.DataGrid1.Items[Int32.Parse(this.txtFamilyName.ToolTip)].Cells[6].Text=IsDependent.ToString();
							dFamily.Tables["FamilyInfo"].Rows[Int32.Parse(this.txtFamilyName.ToolTip)][6]=IsDependent.ToString();
						}
						this.DataGrid1.Items[Int32.Parse(this.txtFamilyName.ToolTip)].Cells[12].Text=this.rdRelation.SelectedValue.ToString();
						dFamily.Tables["FamilyInfo"].Rows[Int32.Parse(this.txtFamilyName.ToolTip)][8]=this.rdRelation.SelectedValue.ToString();
 
						this.DataGrid1.Items[Int32.Parse(this.txtFamilyName.ToolTip)].Cells[13].Text=this.rdStatus.SelectedValue.ToString();
						dFamily.Tables["FamilyInfo"].Rows[Int32.Parse(this.txtFamilyName.ToolTip)][9]=this.rdStatus.SelectedValue.ToString();

						this.DataGrid1.Items[Int32.Parse(this.txtFamilyName.ToolTip)].Cells[14].Text=this.ddDependent.SelectedValue.ToString();
						dFamily.Tables["FamilyInfo"].Rows[Int32.Parse(this.txtFamilyName.ToolTip)][10]=this.ddDependent.SelectedValue.ToString();

						this.lblInvisible.Text="";
						this.txtFamilyName.ToolTip="";
						//lbAddNewFamily.Visible=true;
					}
					else
					{
						int count=0;
						for(int i=0;i<this.DataGrid1.Items.Count;i++)
						{
							if(this.DataGrid1.Items[i].Cells[9].Text.Equals(this.lblInvisible.Text))
							{
								count++;
								dFamily=(DataSet)Session["dFamily"];
								this.DataGrid1.Items[i].Cells[0].Text=this.txtFamilyName.Text;
								dFamily.Tables["FamilyInfo"].Rows[i][0]=this.txtFamilyName.Text;

								if(this.rdRelation.SelectedValue !="0")
								{
									_enumRelation IsRelation=(_enumRelation)Int32.Parse(this.rdRelation.SelectedValue.ToString());
									this.DataGrid1.Items[i].Cells[1].Text=IsRelation.ToString();
									dFamily.Tables["FamilyInfo"].Rows[i][1]=IsRelation.ToString();
								}
								if(this.rdStatus.SelectedValue !="0")
								{
									_enumStatus IsStatus=(_enumStatus)Int32.Parse(this.rdStatus.SelectedValue.ToString());
									this.DataGrid1.Items[i].Cells[2].Text=IsStatus.ToString();
									dFamily.Tables["FamilyInfo"].Rows[i][2]=IsStatus.ToString();
								}		
					
								this.DataGrid1.Items[i].Cells[3].Text=this.rdFamilyGender.SelectedItem.Text;
								dFamily.Tables["FamilyInfo"].Rows[i][3]=this.rdFamilyGender.SelectedItem.Text;

								this.DataGrid1.Items[i].Cells[4].Text=this.txtOccupation.Text;
								dFamily.Tables["FamilyInfo"].Rows[i][4]=this.txtOccupation.Text;

								this.DataGrid1.Items[i].Cells[5].Text=this.CalendarPopup1.SelectedDate.ToString("d MMM,yyyy");
								dFamily.Tables["FamilyInfo"].Rows[i][5]=this.CalendarPopup1.SelectedDate.ToString("d MMM,yyyy");

								if(this.ddDependent.SelectedValue !="0")
								{
									_enumGender IsDependent=(_enumGender)Int32.Parse(this.ddDependent.SelectedValue.ToString());
									this.DataGrid1.Items[i].Cells[6].Text=IsDependent.ToString();
									dFamily.Tables["FamilyInfo"].Rows[i][6]=IsDependent.ToString();
								}
								this.DataGrid1.Items[i].Cells[12].Text=this.rdRelation.SelectedValue.ToString();
								dFamily.Tables["FamilyInfo"].Rows[i][8]=this.rdRelation.SelectedValue.ToString();

								this.DataGrid1.Items[i].Cells[13].Text=this.rdStatus.SelectedValue.ToString();
								dFamily.Tables["FamilyInfo"].Rows[i][9]=this.rdStatus.SelectedValue.ToString();

								this.DataGrid1.Items[i].Cells[14].Text=this.ddDependent.SelectedValue.ToString();
								dFamily.Tables["FamilyInfo"].Rows[i][10]=this.ddDependent.SelectedValue.ToString();

								//lbAddNewFamily.Visible=true;
								break;
							}
						}
			
						if(count==0)
						{
							dFamily=(DataSet)Session["dFamily"];
							//dFamily=(DataSet)Cache.Get("CachedFamily");
							DataRow blankRow ;
							blankRow = dFamily.Tables["FamilyInfo"].NewRow();
							blankRow[0]=this.txtFamilyName.Text;
							int rowCount=dFamily.Tables["FamilyInfo"].Columns.Count;
							if(this.rdRelation.SelectedValue !="0")
							{
								_enumRelation IsRelation=(_enumRelation)Int32.Parse(this.rdRelation.SelectedValue.ToString());
								blankRow[1]=IsRelation.ToString();
							}
							if(this.rdStatus.SelectedValue !="0")
							{
								_enumStatus IsStatus=(_enumStatus)Int32.Parse(this.rdStatus.SelectedValue.ToString());
								blankRow[2]=IsStatus.ToString();
							}
							if(this.ddDependent.SelectedValue !="0")
							{
								_enumGender IsDependent=(_enumGender)Int32.Parse(this.ddDependent.SelectedValue.ToString());
								blankRow[6]=IsDependent.ToString();
							}
							blankRow[3]=this.rdFamilyGender.SelectedItem.Text;
							blankRow[4]=this.txtOccupation.Text;
							blankRow[5]=this.CalendarPopup1.SelectedDate.ToString("d MMM,yyyy");
			
							blankRow[8]=this.rdRelation.SelectedValue.ToString();
							blankRow[9]=this.rdStatus.SelectedValue.ToString();
							blankRow[10]=this.ddDependent.SelectedValue.ToString();
							//blankRow[11]=this.lblInvisible.Text;
				
							dFamily.Tables["FamilyInfo"].Rows.Add(blankRow);
							//DataView dv=new DataView(dFamily.Tables["FamilyInfo"]); 
							ArrayList tempList=new ArrayList();
							for(int z=0;z<this.DataGrid1.Items.Count;z++)
							{
								if(this.DataGrid1.Items[z].BackColor==Color.Red)
								{
									tempList.Add(z);
								}
							}
							DataGrid1.DataSource=dFamily;
							DataGrid1.DataBind();
							DataGrid1.Visible=true;
							Session["dFamily"]=dFamily;
							generateId();
							for(int b=0;b<tempList.Count;b++)
							{
								int indx=(int)tempList[b];
								this.DataGrid1.Items[indx].BackColor=Color.Red;
								LinkButton eLnk=(LinkButton)this.DataGrid1.Items[indx].Cells[9].FindControl("lnkDelete");
								eLnk.Visible=false;
								this.DataGrid1.Items[indx].Cells[7].Text="";
							}
							for(int i=0;i<this.DataGrid1.Items.Count;i++)
							{
								LinkButton lnk=(LinkButton)this.DataGrid1.Items[i].FindControl("lnkDelete");
								if(lnk.Visible)
									lnk.Attributes.Add("onclick","return window.confirm('Are You Sure To Delete')");
							}
							//lbAddNewFamily.Visible=true;
						}
					}
					txtFamilyName.Text="";
					this.txtOccupation.Text="";
					this.lblInvisible.Text="";
					for(int i=0;i<this.rdFamilyGender.Items.Count;i++)
					{
						this.rdFamilyGender.Items[i].Selected=false;
					}
					for(int i=0;i<this.rdRelation.Items.Count;i++)
					{
						this.rdRelation.Items[i].Selected=false;
					}
					for(int i=0;i<this.rdStatus.Items.Count;i++)
					{
						this.rdStatus.Items[i].Selected=false;
					}
					this.ddDependent.SelectedIndex=0;
					this.CalendarPopup1.Reset();
					//pnlFamilyForm.Visible=false;
					//this.lbAddNewFamily.Visible=true;
				}
			}
			else
			{
				ArrayList tempList=new ArrayList();
				for(int z=0;z<this.DataGrid1.Items.Count;z++)
				{
					if(this.DataGrid1.Items[z].BackColor==Color.Red)
					{
						tempList.Add(z);
					}
				}
				dFamily=(DataSet)Session["dFamily"];
				this.DataGrid1.DataSource=dFamily;
				this.DataGrid1.DataBind();
				generateId();
				for(int b=0;b<tempList.Count;b++)
				{
					int indx=(int)tempList[b];
					this.DataGrid1.Items[indx].BackColor=Color.Red;
					LinkButton eLnk=(LinkButton)this.DataGrid1.Items[indx].Cells[10].FindControl("lnkDelete");
					eLnk.Visible=false;
					this.DataGrid3.Items[indx].Cells[7].Text="";
				}
				for(int i=0;i<this.DataGrid1.Items.Count;i++)
				{
					LinkButton lnk=(LinkButton)this.DataGrid1.Items[i].FindControl("lnkDelete");
					lnk.Attributes.Add("onclick","return window.confirm('Are You Sure To Delete')");
				}
				this.txtOccupation.Text="";
				this.lblInvisible.Text="";
				for(int i=0;i<this.rdFamilyGender.Items.Count;i++)
				{
					this.rdFamilyGender.Items[i].Selected=false;
				}
				for(int i=0;i<this.rdRelation.Items.Count;i++)
				{
					this.rdRelation.Items[i].Selected=false;
				}
				for(int i=0;i<this.rdStatus.Items.Count;i++)
				{
					this.rdStatus.Items[i].Selected=false;
				}
				this.ddDependent.SelectedIndex=0;
				this.CalendarPopup1.Reset();
			}
		}*/
		private void btnEdUpdate_Click(object sender, System.EventArgs e)
		{
			/*//CustomValidator9.Enabled=true;
			//CustomValidator10.Enabled=true;
			//CustomValidator11.Enabled=true;
			if(!IsRefresh)
			{
			this.ValidationSummary1.Visible=true;
				if(IsValid)
				{
					lblEducationMsg.Visible=false;
					this.ValidationSummary1.Visible=false;	 
					this.btnSubmitEducation.Visible=true;
					this.Label1.Visible=true;
					this.Label1.Visible=true;
					con.Open();
					string empId=Session["user_id"].ToString();
					dEducation=(DataSet)Session["dEducation"];
					if(this.lblInvisiblity.Text=="&nbsp;")
					{
						if(this.txtDegree.SelectedValue.ToString() !="-1")
						{
							this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[0].Text=this.txtDegree.SelectedItem.Text;           			 
							dEducation.Tables[0].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][0]=this.txtDegree.SelectedItem.Text;
							this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[9].Text=this.txtDegree.SelectedValue.ToString();
							dEducation.Tables[0].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][8]=this.txtDegree.SelectedValue.ToString();
						}
						else
						{
							string st=this.txtOtherDegree.ToolTip;
							this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[0].Text=this.txtOtherDegree.Text;
							dEducation.Tables[0].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][0]=this.txtDegree.SelectedItem.Text;
							this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[9].Text="-1";
							dEducation.Tables[0].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][8]="-1";
						}
						if(this.txtInstitute.SelectedValue !="-1")
						{
							this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[1].Text=this.txtInstitute.SelectedItem.Text; 
							dEducation.Tables[0].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][1]=this.txtInstitute.SelectedItem.Text;
							this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[10].Text=this.txtInstitute.SelectedValue.ToString();
							dEducation.Tables[0].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][9]=this.txtInstitute.SelectedItem.Text;
						}
						else
						{
							this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[1].Text=this.txtOtherInstitute.Text;
							dEducation.Tables[0].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][1]=this.txtInstitute.SelectedItem.Text;
							this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[10].Text="-1";
							dEducation.Tables[0].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][9]="-1";
						}
						this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[2].Text=this.DurationFrom.SelectedValue.ToString();
						dEducation.Tables[0].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][2]=this.DurationFrom.SelectedValue.ToString();

						this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[3].Text=this.DurationTo.SelectedValue.ToString();
						dEducation.Tables[0].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][3]=this.DurationTo.SelectedValue.ToString();

						this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[4].Text=this.txtResult.SelectedValue.ToString();
						dEducation.Tables[0].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][4]=this.txtResult.SelectedValue.ToString();
						
						this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[5].Text=this.txtOtherMajors.Text;
						dEducation.Tables[0].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][5]=this.txtOtherMajors.Text;
						
						this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[6].Text=this.txtAchievements.Text;
						dEducation.Tables[0].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][6]=this.txtAchievements.Text;

						this.txtOtherDegree.ToolTip="";
					}
					else
					{
						int count=0;
						for(int i=0;i<this.DataGrid3.Items.Count;i++)
						{
							if(this.lblInvisiblity.Text.Equals(this.DataGrid3.Items[i].Cells[8].Text))
							{
								count++;
								if(this.txtDegree.SelectedValue.ToString() !="-1")
								{
									this.DataGrid3.Items[i].Cells[0].Text=this.txtDegree.SelectedItem.Text;           			 
									dEducation.Tables[0].Rows[i][0]=this.txtDegree.SelectedItem.Text;
									this.DataGrid3.Items[i].Cells[9].Text=this.txtDegree.SelectedValue.ToString();
									dEducation.Tables[0].Rows[i][8]=this.txtDegree.SelectedValue.ToString();
								}
								else
								{
									this.DataGrid3.Items[i].Cells[0].Text=this.txtOtherDegree.Text;
									dEducation.Tables[0].Rows[i][0]=this.txtDegree.SelectedItem.Text;
									this.DataGrid3.Items[i].Cells[9].Text="-1";
									dEducation.Tables[0].Rows[i][8]="-1";
								}
								if(this.txtInstitute.SelectedValue !="-1")
								{
									this.DataGrid3.Items[i].Cells[1].Text=this.txtInstitute.SelectedItem.Text; 
									dEducation.Tables[0].Rows[i][1]=this.txtInstitute.SelectedItem.Text;
									this.DataGrid3.Items[i].Cells[10].Text=this.txtInstitute.SelectedValue.ToString();
									dEducation.Tables[0].Rows[i][9]=this.txtInstitute.SelectedItem.Text;
								}
								else
								{
									this.DataGrid3.Items[i].Cells[1].Text=this.txtOtherInstitute.Text;
									dEducation.Tables[0].Rows[i][1]=this.txtInstitute.SelectedItem.Text;
									this.DataGrid3.Items[i].Cells[10].Text="-1";
									 dEducation.Tables[0].Rows[i][9]="-1";
								}
								this.DataGrid3.Items[i].Cells[2].Text=this.DurationFrom.SelectedValue.ToString();
								dEducation.Tables[0].Rows[i][2]=this.DurationFrom.SelectedValue.ToString();

								this.DataGrid3.Items[i].Cells[3].Text=this.DurationTo.SelectedValue.ToString();
								dEducation.Tables[0].Rows[i][3]=this.DurationTo.SelectedValue.ToString();
								
								this.DataGrid3.Items[i].Cells[4].Text=this.txtResult.SelectedValue.ToString();
								dEducation.Tables[0].Rows[i][4]=this.txtResult.SelectedValue.ToString();
								
								this.DataGrid3.Items[i].Cells[5].Text=this.txtOtherMajors.Text;
								dEducation.Tables[0].Rows[i][5]=this.txtOtherMajors.Text;
								
								this.DataGrid3.Items[i].Cells[6].Text=this.txtAchievements.Text;
								dEducation.Tables[0].Rows[i][6]=this.txtAchievements.Text;
								break;
							}
						}
						if(count==0)
						{
							dEducation=(DataSet)Session["dEducation"];
							DataRow blankRow;
							blankRow=dEducation.Tables["EdutionalInfo"].NewRow();
            
							if(this.txtDegree.SelectedValue !="-1")
							{
								blankRow[0]=this.txtDegree.SelectedItem.Text;
								blankRow[8]=this.txtDegree.SelectedValue.ToString();
							}
							else
							{
								blankRow[0]=this.txtOtherDegree.Text;
								blankRow[8]="-1";
							}
							if(this.txtInstitute.SelectedValue !="-1")
							{
								blankRow[1]=this.txtInstitute.SelectedItem.Text; 
								blankRow[9]=this.txtInstitute.SelectedValue.ToString();
							}
							else
							{
								blankRow[1]=this.txtOtherInstitute.Text;
								blankRow[9]="-1"; 
							}
							blankRow[2]=this.DurationFrom.SelectedValue.ToString();
							blankRow[3]=this.DurationTo.SelectedValue.ToString();
							blankRow[4]=this.txtResult.SelectedValue.ToString();
							blankRow[5]=this.txtOtherMajors.Text;
							blankRow[6]=this.txtAchievements.Text;
							blankRow[7]=this.lblInvisiblity.Text;
							dEducation.Tables["EdutionalInfo"].Rows.Add(blankRow);
							DataView dv=new DataView(dEducation.Tables["EdutionalInfo"]);
							this.DataGrid3.DataSource=dv;
							this.DataGrid3.DataBind();
							this.DataGrid3.Visible=true;
							eCount();
							
						}
					}
					con.Close();          
					this.btnEdUpdate.Enabled=false;
					this.btnEdNew.Enabled=true;
					this.txtDegree.SelectedValue="0";
					this.txtInstitute.SelectedValue="0";
					this.txtResult.SelectedIndex=0;
					this.txtAchievements.Text="";
					this.DurationTo.SelectedIndex=0;
					this.DurationFrom.SelectedIndex=0;
					this.txtOtherDegree.Text="";
					this.txtOtherInstitute.Text="";
					this.txtOtherMajors.Text="";
					this.txtOtherDegree.Visible=false;
					this.txtOtherInstitute.Visible=false;
					this.txtDegree.Visible=true;
					this.txtInstitute.Visible=true;
					this.txtOtherDegree.ToolTip="";
					this.lblInvisiblity.Text="";
				}
				else
				{
					dEducation=(DataSet)Session["dEducation"];
					DataView dv=new DataView(dEducation.Tables["EdutionalInfo"]);
					this.DataGrid3.DataSource=dv;
					this.DataGrid3.DataBind();
					this.DataGrid3.Visible=true;
					if(this.DataGrid3.Items.Count>0)
					{
						lblEducationMsg.Visible=false;
					}
					else
					{
						lblEducationMsg.Visible=true;
					}
					eCount();
					for(int i=0;i<this.DataGrid3.Items.Count;i++)
					{
						LinkButton lnk=(LinkButton)this.DataGrid3.Items[i].FindControl("lnkeDelete");
						lnk.Attributes.Add("onclick","return confirm('Are You Sure To Delete')");
					}
					this.btnEdUpdate.Enabled=false;
					this.btnEdNew.Enabled=true;
					this.txtDegree.SelectedValue="0";
					this.txtInstitute.SelectedValue="0";
					this.txtResult.SelectedIndex=0;
					this.txtAchievements.Text="";
					this.DurationTo.SelectedIndex=0;
					this.DurationFrom.SelectedIndex=0;
					this.txtOtherDegree.Text="";
					this.txtOtherInstitute.Text="";
					this.txtOtherMajors.Text="";
					this.txtOtherDegree.Visible=false;
					this.txtOtherInstitute.Visible=false;
					this.txtDegree.Visible=true;
					this.txtInstitute.Visible=true;
					this.txtOtherDegree.ToolTip="";
					this.lblInvisiblity.Text="";
				}
				//CustomValidator9.Enabled=false;
				//CustomValidator10.Enabled=false;
				//CustomValidator11.Enabled=false;
				
			}*/
		}

		/*private void btnEdNew_Click(object sender, System.EventArgs e)
		{
			if(!IsRefresh)
			{
				this.ValidationSummary1.Visible=true;
				if(IsValid)
				{
					this.btnEdNew.Text="Add New";
					this.ValidationSummary1.Visible=false; 
					this.btnSubmitEducation.Visible=true;
					this.Label1.Visible=true;
					lblEducationMsg.Visible=false;
					EducationId=0;
					this.Label1.Visible=true;
					
					if(Page.IsValid)
					{
						if(this.lblInvisiblity.Text=="&nbsp;")
						{
							dEducation=(DataSet)Session["dEducation"];
							if(this.txtDegree.SelectedValue.ToString() !="-1")
							{
								this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[0].Text=this.txtDegree.SelectedItem.Text;           			 
								dEducation.Tables["EdutionalInfo"].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][0]=this.txtDegree.SelectedItem.Text;
								this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[11].Text=this.txtDegree.SelectedValue.ToString();
								dEducation.Tables["EdutionalInfo"].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][8]=this.txtDegree.SelectedValue.ToString();
							}
							else
							{
								string st=this.txtOtherDegree.ToolTip;
								this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[0].Text=this.txtOtherDegree.Text;
								dEducation.Tables["EdutionalInfo"].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][0]=this.txtOtherDegree.Text;
								this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[11].Text="-1";
								dEducation.Tables["EdutionalInfo"].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][8]="-1";
							}
							if(this.txtInstitute.SelectedValue !="-1")
							{
								this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[1].Text=this.txtInstitute.SelectedItem.Text; 
								dEducation.Tables["EdutionalInfo"].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][1]=this.txtInstitute.SelectedItem.Text;
								this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[12].Text=this.txtInstitute.SelectedValue.ToString();
								dEducation.Tables["EdutionalInfo"].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][9]=this.txtInstitute.SelectedValue.ToString();
							}
							else
							{
								this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[1].Text=this.txtOtherInstitute.Text;
								dEducation.Tables["EdutionalInfo"].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][1]=this.txtOtherInstitute.Text;
								this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[12].Text="-1"; 
								dEducation.Tables["EdutionalInfo"].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][9]="-1";
							}
							this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[2].Text=this.DurationFrom.SelectedValue.ToString();
							dEducation.Tables["EdutionalInfo"].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][2]=this.DurationFrom.SelectedValue.ToString();

							if(this.DurationTo.SelectedValue.ToString()=="-1")
							{
								this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[3].Text=this.DurationTo.SelectedItem.Text;
								dEducation.Tables["EdutionalInfo"].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][3]=this.DurationTo.SelectedItem.Text;
								this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[13].Text=this.DurationTo.SelectedValue.ToString(); 
								dEducation.Tables["EdutionalInfo"].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][10]=this.DurationTo.SelectedValue.ToString();
							}
							else
							{
								this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[3].Text=this.DurationTo.SelectedValue.ToString();
								dEducation.Tables["EdutionalInfo"].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][3]=this.DurationTo.SelectedValue.ToString();
								this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[13].Text=this.DurationTo.SelectedValue.ToString();
								dEducation.Tables["EdutionalInfo"].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][10]=this.DurationTo.SelectedValue.ToString();
							}
				
							this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[4].Text=this.txtResult.SelectedValue.ToString();
							dEducation.Tables["EdutionalInfo"].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][4]=this.txtResult.SelectedValue.ToString();
						
							this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[5].Text=this.txtOtherMajors.Text;
							dEducation.Tables["EdutionalInfo"].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][5]=this.txtOtherMajors.Text; 
						
							this.DataGrid3.Items[Int32.Parse(this.txtOtherDegree.ToolTip)].Cells[6].Text=this.txtAchievements.Text;
							dEducation.Tables["EdutionalInfo"].Rows[Int32.Parse(this.txtOtherDegree.ToolTip)][6]=this.txtAchievements.Text;
							this.txtOtherDegree.ToolTip="";
						}
						else
						{
							int count=0;
							for(int i=0;i<this.DataGrid3.Items.Count;i++)
							{
								if(this.lblInvisiblity.Text.Equals(this.DataGrid3.Items[i].Cells[8].Text))
								{
									dEducation=(DataSet)Session["dEducation"];
									count++;
									if(this.txtDegree.SelectedValue.ToString() !="-1")
									{
										this.DataGrid3.Items[i].Cells[0].Text=this.txtDegree.SelectedItem.Text;           			 
										dEducation.Tables["EdutionalInfo"].Rows[i][0]=this.txtDegree.SelectedItem.Text;
										this.DataGrid3.Items[i].Cells[11].Text=this.txtDegree.SelectedValue.ToString();
										dEducation.Tables["EdutionalInfo"].Rows[i][8]=this.txtDegree.SelectedValue.ToString();
									}
									else
									{
										this.DataGrid3.Items[i].Cells[0].Text=this.txtOtherDegree.Text;
										dEducation.Tables["EdutionalInfo"].Rows[i][0]=this.txtOtherDegree.Text;
										this.DataGrid3.Items[i].Cells[11].Text="-1";
										dEducation.Tables["EdutionalInfo"].Rows[i][8]="-1";
									}
									if(this.txtInstitute.SelectedValue !="-1")
									{
										this.DataGrid3.Items[i].Cells[1].Text=this.txtInstitute.SelectedItem.Text; 
										dEducation.Tables["EdutionalInfo"].Rows[i][1]=this.txtInstitute.SelectedItem.Text;
										this.DataGrid3.Items[i].Cells[12].Text=this.txtInstitute.SelectedValue.ToString();
										dEducation.Tables["EdutionalInfo"].Rows[i][9]=this.txtInstitute.SelectedValue.ToString();
									}
									else
									{
										this.DataGrid3.Items[i].Cells[1].Text=this.txtOtherInstitute.Text;
										dEducation.Tables["EdutionalInfo"].Rows[i][1]=this.txtOtherInstitute.Text;
										this.DataGrid3.Items[i].Cells[12].Text="-1"; 
										dEducation.Tables["EdutionalInfo"].Rows[i][9]="-1";
									}
									this.DataGrid3.Items[i].Cells[2].Text=this.DurationFrom.SelectedValue.ToString();
									dEducation.Tables["EdutionalInfo"].Rows[i][2]=this.DurationFrom.SelectedValue.ToString();

									if(this.DurationTo.SelectedValue.ToString()=="-1")
									{
										this.DataGrid3.Items[i].Cells[3].Text=this.DurationTo.SelectedItem.Text;
										dEducation.Tables["EdutionalInfo"].Rows[i][3]=this.DurationTo.SelectedItem.Text;
										this.DataGrid3.Items[i].Cells[13].Text=this.DurationTo.SelectedValue.ToString();
										dEducation.Tables["EdutionalInfo"].Rows[i][10]=this.DurationTo.SelectedValue.ToString();
									}
									else
									{
										this.DataGrid3.Items[i].Cells[3].Text=this.DurationTo.SelectedValue.ToString();
										dEducation.Tables["EdutionalInfo"].Rows[i][3]=this.DurationTo.SelectedValue.ToString();
										this.DataGrid3.Items[i].Cells[13].Text=this.DurationTo.SelectedValue.ToString();
										dEducation.Tables["EdutionalInfo"].Rows[i][10]=this.DurationTo.SelectedValue.ToString();
									}
									this.DataGrid3.Items[i].Cells[4].Text=this.txtResult.SelectedValue.ToString();
									dEducation.Tables["EdutionalInfo"].Rows[i][4]=this.txtResult.SelectedValue.ToString();

									this.DataGrid3.Items[i].Cells[5].Text=this.txtOtherMajors.Text;
									dEducation.Tables["EdutionalInfo"].Rows[i][5]=this.txtOtherMajors.Text;

									this.DataGrid3.Items[i].Cells[6].Text=this.txtAchievements.Text;
									dEducation.Tables["EdutionalInfo"].Rows[i][6]=this.txtAchievements.Text; 
									break;
								}
							}
							if(count==0)
							{
								dEducation=(DataSet)Session["dEducation"];
								//dEducation=(DataSet)Cache.Get("CachedEducation");
								DataRow blankRow;
								blankRow=dEducation.Tables["EdutionalInfo"].NewRow();
            
								if(this.txtDegree.SelectedValue !="-1")
								{
									blankRow[0]=this.txtDegree.SelectedItem.Text;
									blankRow[8]=this.txtDegree.SelectedValue.ToString();
								}
								else
								{
									blankRow[0]=this.txtOtherDegree.Text;
									blankRow[8]="-1";
								}
								if(this.txtInstitute.SelectedValue !="-1")
								{
									blankRow[1]=this.txtInstitute.SelectedItem.Text; 
									blankRow[9]=this.txtInstitute.SelectedValue.ToString();
								}
								else
								{
									blankRow[1]=this.txtOtherInstitute.Text;
									blankRow[9]="-1"; 
								}
								blankRow[2]=this.DurationFrom.SelectedValue.ToString();
								if(this.DurationTo.SelectedValue.ToString()=="-1")
								{
							
									blankRow[3]=this.DurationTo.SelectedValue.ToString();
									blankRow[10]=this.DurationTo.SelectedValue.ToString();
								}
								else
								{
									blankRow[3]=this.DurationTo.SelectedValue.ToString();
									blankRow[10]=this.DurationTo.SelectedValue.ToString();
								}
								blankRow[4]=this.txtResult.SelectedValue.ToString();
								blankRow[5]=this.txtOtherMajors.Text;
								blankRow[6]=this.txtAchievements.Text;
								//blankRow[7]=this.lblInvisiblity.Text;
								dEducation.Tables["EdutionalInfo"].Rows.Add(blankRow);
								//DataView dv=new DataView(dEducation.Tables["EdutionalInfo"]);
								
								ArrayList tempList=new ArrayList();
								for(int z=0;z<this.DataGrid3.Items.Count;z++)
								{
									if(this.DataGrid3.Items[z].BackColor==Color.Red)
									{
										tempList.Add(z);
									}
								}
								this.DataGrid3.DataSource=dEducation;
								this.DataGrid3.DataBind();
								this.DataGrid3.Visible=true;
								Session["dEducation"]=dEducation;
								eCount();

								for(int b=0;b<tempList.Count;b++)
								{
									int indx=(int)tempList[b];
									this.DataGrid3.Items[indx].BackColor=Color.Red;
									LinkButton eLnk=(LinkButton)this.DataGrid3.Items[indx].Cells[9].FindControl("lnkeDelete");
									eLnk.Visible=false;
									this.DataGrid3.Items[indx].Cells[7].Text="";
								}
								for(int i=0;i<this.DataGrid3.Items.Count;i++)
								{
									if(this.DataGrid3.Items[i].BackColor!=Color.Red)
									{
										LinkButton lnk=(LinkButton)this.DataGrid3.Items[i].FindControl("lnkeDelete");
										lnk.Attributes.Add("onclick","return confirm('Are You Sure To Delete')");
									}
								}
							}
						}
			      
						this.txtDegree.SelectedValue="0";
						this.txtInstitute.SelectedValue="0";
						this.txtResult.SelectedIndex=0;
						this.txtAchievements.Text="";
						this.DurationTo.SelectedIndex=0;
						this.DurationFrom.SelectedIndex=0;
						this.txtOtherDegree.Text="";
						this.txtOtherInstitute.Text="";
						this.txtOtherMajors.Text="";
						this.txtOtherDegree.Visible=false;
						this.txtOtherInstitute.Visible=false;
						this.txtDegree.Visible=true;
						this.txtInstitute.Visible=true;
						this.txtOtherDegree.ToolTip="";
						this.lblInvisiblity.Text="";
						//tabEducation.Visible=false;
					}
				}
				else
				{
					dEducation=(DataSet)Session["dEducation"];
					this.DataGrid3.DataSource=dEducation;
					this.DataGrid3.DataBind();
					this.txtDegree.SelectedValue="0";
					this.txtInstitute.SelectedValue="0";
					this.txtResult.SelectedIndex=0;
					this.txtAchievements.Text="";
					this.DurationTo.SelectedIndex=0;
					this.DurationFrom.SelectedIndex=0;
					this.txtOtherDegree.Text="";
					this.txtOtherInstitute.Text="";
					this.txtOtherMajors.Text="";
					this.txtOtherDegree.Visible=false;
					this.txtOtherInstitute.Visible=false;
					this.txtDegree.Visible=true;
					this.txtInstitute.Visible=true;
					this.txtOtherDegree.ToolTip="";
					this.lblInvisiblity.Text="";
					//tabEducation.Visible=false;
					eCount();
					for(int i=0;i<this.DataGrid3.Items.Count;i++)
					{
						LinkButton lnk=(LinkButton)this.DataGrid3.Items[i].FindControl("lnkeDelete");
						lnk.Attributes.Add("onclick","return confirm('Are You Sure To Delete')");
					}
				}
			}
			else
			{
				ArrayList tempList=new ArrayList();
				for(int z=0;z<this.DataGrid3.Items.Count;z++)
				{
					if(this.DataGrid3.Items[z].BackColor==Color.Red)
					{
						tempList.Add(z);
					}
				}
				dEducation=(DataSet)Session["dEducation"];
				this.DataGrid3.DataSource=dEducation;
				this.DataGrid3.DataBind();
				this.txtDegree.SelectedValue="0";
				this.txtInstitute.SelectedValue="0";
				this.txtResult.SelectedIndex=0;
				this.txtAchievements.Text="";
				this.DurationTo.SelectedIndex=0;
				this.DurationFrom.SelectedIndex=0;
				this.txtOtherDegree.Text="";
				this.txtOtherInstitute.Text="";
				this.txtOtherMajors.Text="";
				this.txtOtherDegree.Visible=false;
				this.txtOtherInstitute.Visible=false;
				this.txtDegree.Visible=true;
				this.txtInstitute.Visible=true;
				this.txtOtherDegree.ToolTip="";
				this.lblInvisiblity.Text="";
				//tabEducation.Visible=false;
				eCount();
				for(int b=0;b<tempList.Count;b++)
				{
					int indx=(int)tempList[b];
					this.DataGrid3.Items[indx].BackColor=Color.Red;
					LinkButton eLnk=(LinkButton)this.DataGrid3.Items[indx].Cells[9].FindControl("lnkeDelete");
					eLnk.Visible=false;
					this.DataGrid3.Items[indx].Cells[7].Text="";
				}
				for(int i=0;i<this.DataGrid3.Items.Count;i++)
				{
					if(this.DataGrid3.Items[i].BackColor!=Color.Red)
					{
						LinkButton lnk=(LinkButton)this.DataGrid3.Items[i].FindControl("lnkeDelete");
						lnk.Attributes.Add("onclick","return confirm('Are You Sure To Delete')");
					}
				}

			}
		}*/

		/*public void EducationRequest()
		{
			con.Open();
			//string empId="";
			//string getInfo="select degree=case e.degree when '-1' then (select temp.otherdegree from t_educationinfo_temp temp where temp.eduinfoid=e.eduinfoid ) else (select d.degree from t_degree d where d.id=e.degree) end , institute=case e.institute when '-1' then (select temp.otherinstitute from t_educationinfo_temp temp where temp.eduinfoid=e.eduinfoid) else (select temp.institute_name from t_institute temp where temp.id=e.institute)end ,e.durationfrom,e.durationto,e.result,e.majors,e.achievements,e.p_id,e.degree as degreeid,e.institute as instituteid from t_educationinfo_temp e where e.isactive='1' And e.pcode='"+empId+"' And isrequest=1";
			string getInfo="select degree=case e.degree when '-1' then (select temp.otherdegree from t_educationinfo temp where temp.eduinfoid=e.eduinfoid ) else (select d.degree from t_degree d where d.id=e.degree) end , institute=case e.institute when '-1' then (select temp.otherinstitute from t_educationinfo temp where temp.eduinfoid=e.eduinfoid) else (select temp.institute_name from t_institute temp where temp.id=e.institute)end ,e.durationfrom,durationto=case e.durationto when '-1' then 'In Progress' else (cast (e.durationto as varchar))end ,e.result,e.majors,e.achievements,e.eduinfoid,e.degree as degreeid,e.institute as instituteid,e.durationto as durationid from t_educationinfo e where e.isactive='1' And e.pcode='"+ Session["user_id"].ToString() +"'";
			SqlDataAdapter rd=new SqlDataAdapter(getInfo,con);
			if(Session["dEducation"].ToString()=="")
			{
				dEducation=new DataSet();
				Session["dEducation"]=dEducation;
			}
			else
			{
				dEducation=(DataSet)Session["dEducation"];
				dEducation.Tables[0].Clear();
			}
			rd.Fill(dEducation,"EdutionalInfo");
			DataView dv=new DataView(dEducation.Tables["EdutionalInfo"]);
			dv.Sort=sort4;
			this.DataGrid3.DataSource=dv;
			this.DataGrid3.DataBind();
			int getItems=this.DataGrid3.Items.Count;
			if(getItems<=0)
			{
				this.lblEdBoRecord.Visible=true;
			}
			else
			{
				this.lblEdBoRecord.Visible=false;
			}
			this.DataGrid3.Visible=true;
			this.btnSubmitEducation.Visible=true; 
			for(int jk=0;jk<this.DataGrid3.Items.Count;jk++)
			{
				LinkButton lnk=(LinkButton)this.DataGrid3.Items[jk].FindControl("lnkeDelete");
				lnk.Attributes.Add("onclick","return confirm('Are You Sure To Delete')");
			}
			con.Close(); 
		}*/

		/*private void DataGrid1_SelectedIndexChanged_1(object sender, System.EventArgs e)
		{
		   
			if(!_isRefresh)
			{
				//pnlFamilyForm.Visible=true;
				this.btnNew.Text="Update";
				this.txtFamilyName.Text=this.DataGrid1.Items[this.DataGrid1.SelectedIndex].Cells[0].Text;
				Label lbl=(Label)this.DataGrid1.Items[this.DataGrid1.SelectedIndex].FindControl("lblId");
				this.txtFamilyName.ToolTip=lbl.Text;
			
				if(this.DataGrid1.Items[this.DataGrid1.SelectedIndex].Cells[12].Text !="&nbsp;")
				{
					this.rdRelation.SelectedValue=this.DataGrid1.Items[this.DataGrid1.SelectedIndex].Cells[12].Text;
				}
				else
				{
					this.rdRelation.SelectedValue="0";
				}
				if(this.DataGrid1.Items[this.DataGrid1.SelectedIndex].Cells[13].Text !="&nbsp;")
				{
					this.rdStatus.SelectedValue=this.DataGrid1.Items[this.DataGrid1.SelectedIndex].Cells[13].Text;
				}
				else
				{
					this.rdStatus.SelectedValue="0";
				}
				if(this.DataGrid1.Items[this.DataGrid1.SelectedIndex].Cells[14].Text !="&nbsp;")
				{
					this.ddDependent.SelectedValue=this.DataGrid1.Items[this.DataGrid1.SelectedIndex].Cells[14].Text;
				}
				else
				{
					this.ddDependent.SelectedValue="0";
				}
				string Val=this.DataGrid1.Items[this.DataGrid1.SelectedIndex].Cells[3].Text;
				if(Val=="Male")
				{
					this.rdFamilyGender.SelectedValue="1";
				}
				else if(Val=="Female")
				{
					this.rdFamilyGender.SelectedValue="2";
				}
				else
				{
					this.rdFamilyGender.SelectedValue="0";
				}
				if(this.DataGrid1.Items[this.DataGrid1.SelectedIndex].Cells[4].Text !="&nbsp;")
				{
					this.txtOccupation.Text=this.DataGrid1.Items[this.DataGrid1.SelectedIndex].Cells[4].Text;
				}
				else
				{
					this.txtOccupation.Text="";
				}
				if(this.DataGrid1.Items[this.DataGrid1.SelectedIndex].Cells[5].Text !="&nbsp;")
				{
					this.CalendarPopup1.SelectedDate=DateTime.Parse(this.DataGrid1.Items[this.DataGrid1.SelectedIndex].Cells[5].Text);
				}
				//			this.btnNew.Enabled=false;
				//			this.btnFupdate.Enabled=true;
				this.lblInvisible.Text=this.DataGrid1.Items[this.DataGrid1.SelectedIndex].Cells[9].Text;
				btnFupdate.Enabled=true;
			}
			else
			{
				//this.txtFamilyName.Text="";
				this.btnNew.Text="Add New";
				this.txtOccupation.Text="";
				this.lblInvisible.Text="";
				for(int i=0;i<this.rdFamilyGender.Items.Count;i++)
				{
					this.rdFamilyGender.Items[i].Selected=false;
				}
				for(int i=0;i<this.rdRelation.Items.Count;i++)
				{
					this.rdRelation.Items[i].Selected=false;
				}
				for(int i=0;i<this.rdStatus.Items.Count;i++)
				{
					this.rdStatus.Items[i].Selected=false;
				}
				this.ddDependent.SelectedIndex=0;
				this.CalendarPopup1.Reset();
				//pnlFamilyForm.Visible=false;
				//lbAddNewFamily.Visible=true;
				this.lblInvisible.Text="";
				//this.txtFamilyName.ToolTip="";
				generateId();
			}
		}*/

		/*private void DataGrid3_SelectedIndexChanged_1(object sender, System.EventArgs e)
		{
			if(!_isRefresh)
			{
				//tabEducation.Visible=true;
				this.btnEdNew.Text="Update";
				if(this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[11].Text !="-1")
				{
					this.txtOtherDegree.Text="";
					this.txtOtherDegree.Visible=false;
					this.txtDegree.SelectedValue=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[11].Text; 
					Label lbl=(Label)this.DataGrid3.Items[this.DataGrid3.SelectedIndex].FindControl("lbleCount");
					this.txtOtherDegree.ToolTip=lbl.Text;
				}
				else
				{
					this.txtDegree.SelectedValue="-1";
					this.txtOtherDegree.Visible=true;
					this.txtOtherDegree.Text=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[0].Text;
					Label lbl=(Label)this.DataGrid3.Items[this.DataGrid3.SelectedIndex].FindControl("lbleCount");
					this.txtOtherDegree.ToolTip=lbl.Text;
				}
				if(this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[12].Text !="-1")
				{
					this.txtOtherInstitute.Text="";
					this.txtOtherInstitute.Visible=false;
					this.txtInstitute.SelectedValue=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[12].Text; 
				}
				else
				{
					this.txtInstitute.SelectedValue="-1";
					this.txtOtherInstitute.Visible=true;
					this.txtOtherInstitute.Text=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[1].Text; 
				}
				if(this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[2].Text !="&nbsp;")
				{
					this.DurationFrom.SelectedValue=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[2].Text;
				}
				else
				{
					this.DurationFrom.SelectedValue="0";
				}
				if(this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[13].Text !="&nbsp;")
				{
					this.DurationTo.SelectedValue=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[13].Text;
				}
				else
				{
					this.DurationTo.SelectedValue="0";
				}
				if(this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[4].Text!="&nbsp;")
				{
					try
					{
						this.txtResult.SelectedValue=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[4].Text;
					}
					catch(Exception eXM){}
				}
				else
				{
					this.txtResult.SelectedValue="0";
				}
				if(this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[5].Text !="&nbsp;")
				{
					this.txtOtherMajors.Text=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[5].Text;
				}
				else
				{
					this.txtOtherMajors.Text="";
				}
			
				if(this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[6].Text !="&nbsp;")
				{
					this.txtAchievements.Text=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[6].Text;
				}
				else
				{
					this.txtAchievements.Text="";
				}
				this.lblInvisiblity.Text=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[8].Text;
			}
			else
			{
				this.btnEdNew.Text="Add New";
				this.txtDegree.SelectedValue="0";
				this.txtInstitute.SelectedValue="0";
				this.txtResult.SelectedIndex=0;
				this.txtAchievements.Text="";
				this.DurationTo.SelectedIndex=0;
				this.DurationFrom.SelectedIndex=0;
				this.txtOtherDegree.Text="";
				this.txtOtherInstitute.Text="";
				this.txtOtherMajors.Text="";
				this.txtOtherDegree.Visible=false;
				this.txtOtherInstitute.Visible=false;
				this.txtDegree.Visible=true;
				this.txtInstitute.Visible=true;
				//this.txtOtherDegree.ToolTip="";
				this.lblInvisiblity.Text="";
				//tabEducation.Visible=false;
				eCount();
			}
		}*/
		private void DataGrid1_SortCommand(object source, System.Web.UI.WebControls.DataGridSortCommandEventArgs e)
		{
			sort=e.SortExpression.ToString();
			//FamilyInfo();
		}

		private void DataGrid3_SortCommand(object source, System.Web.UI.WebControls.DataGridSortCommandEventArgs e)
		{
			sort3=e.SortExpression.ToString();
			//EducationalInfo();
			//--->EducationRequest();
		}

		
		protected void TextValidate(object source, ServerValidateEventArgs args)
		{
			args.IsValid = (args.Value !="0");
		}

		
		/*private void txtInstitute_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			if(this.txtInstitute.SelectedValue=="-1")
			{
				this.txtOtherInstitute.Visible=true;
				RequiredFieldValidator15.Enabled=true;
			}
			else
			{
				RequiredFieldValidator15.Enabled=false;
				this.txtOtherInstitute.Visible=false;
				this.txtOtherInstitute.Text=""; 
			}
		}*/
		

		private void ddDesignation_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			
			this.ddFunctionalDesignation.Items.Clear();
			ListItem item=new ListItem();
			item.Value="0";
			item.Text="Select--Functional Designation";
			this.ddFunctionalDesignation.Items.Add(item);
			con.Open();
			string getFunDes="select fdesigid,functionaltitle from t_functionaldesignation where functionaldesignation='"+ this.ddDesignation.SelectedValue.ToString() +"'";
			SqlCommand cmd=new SqlCommand(getFunDes,con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rd[0].ToString();
				itm.Text=rd[1].ToString();
				this.ddFunctionalDesignation.Items.Add(itm);
			}
			rd.Close();
			con.Close();
		}

		private void ImageButton5_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			pnlPaySlip.Visible=false;
			//Response.Write("<script language = Javascript >var win=window.showModalDialog('admin/textonimage1.aspx','','dialogHeight:900px;dialogWidth:800px;center:yes')</script"); 
			Response.Write("<script language = Javascript >win=window.open('admin/orgamogram.htm','_blank','resizable=yes,menubar=no,scrollbars=yes,top=0,status=0',true).focus();</script>");
		}

		/*private void txtDegree_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			if(this.txtDegree.SelectedValue=="-1")
			{
				this.txtOtherDegree.Visible=true;
				RequiredFieldValidator14.Enabled=true;
			}
			else
			{
				this.txtOtherDegree.Visible=false;
				this.txtOtherDegree.Text="";
				RequiredFieldValidator14.Enabled=false;
			}
		}*/

		/*private void btnECancel_Click(object sender, System.EventArgs e)
		{
			this.ValidationSummary1.Visible=false;
			this.lblEducationMsg.Text="";
			this.lblEducationMsg.Visible=false;
			//this.btnEdUpdate.Enabled=false;
			this.btnEdNew.Enabled=true;
			this.txtDegree.SelectedValue="0";
			this.txtInstitute.SelectedValue="0";
			this.txtResult.SelectedIndex=0;
			this.txtAchievements.Text="";
			this.DurationTo.SelectedIndex=0;
			this.DurationFrom.SelectedIndex=0;
			this.txtOtherDegree.Text="";
			this.txtOtherInstitute.Text="";
			this.txtOtherMajors.Text="";
			this.txtOtherDegree.Visible=false;
			this.txtOtherInstitute.Visible=false;
			this.txtDegree.Visible=true;
			this.txtInstitute.Visible=true;
			this.txtOtherDegree.ToolTip="";
			this.lblInvisiblity.Text="";
			this.btnEdNew.Text="Add New"; 
			
		}*/

		private void ddNationality_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			setNationality();	
		}
		public void setNationality()
		{
			if(this.ddNationality.SelectedValue !="0")
			{
				this.ddNationality2.Items.Clear();
				ListItem item=new ListItem("Select--Nationality","0");
				this.ddNationality2.Items.Add(item);
				for(int i=0;i<this.ddNationality.Items.Count;i++)
				{
					ListItem itm=new ListItem();
					itm.Text=this.ddNationality.Items[i].Text;
					itm.Value=this.ddNationality.Items[i].Value;
					if(itm.Value !=this.ddNationality.SelectedValue && this.ddNationality.Items[i].Value !="0")
					{
						this.ddNationality2.Items.Add(itm);
					}
				}
				this.ddNationality2.Enabled=true;
			}
			else
			{
				this.ddNationality2.Items.Clear();
				ListItem item=new ListItem("Select--Nationality","0");
				this.ddNationality2.Items.Add(item);
				this.ddNationality2.SelectedIndex=0;
				this.ddNationality2.Enabled=false;
			}
			settings();
		}

		public void settings()
		{
			if(this.ddNationality.SelectedValue !="0" && this.ddNationality.SelectedValue=="163")
			{
				RequiredFieldValidator6.Enabled=true;
			}
			if(this.ddNationality.SelectedValue !="0" && this.ddNationality.SelectedValue !="163")
			{
				RequiredFieldValidator7.Enabled=true;
			}
			if(this.ddNationality.SelectedValue !="0" && this.ddNationality.SelectedValue=="163" && this.ddNationality2.SelectedValue=="0")
			{
				RequiredFieldValidator7.Enabled=false;
			}
			if(this.ddNationality.SelectedValue !="0" && this.ddNationality.SelectedValue !="163" && this.ddNationality2.SelectedValue=="0")
			{
				RequiredFieldValidator6.Enabled=false;
			} 
			if(this.ddNationality2.SelectedValue !="0" && this.ddNationality2.SelectedValue=="163")
			{
				RequiredFieldValidator6.Enabled=true;
			}
			if(this.ddNationality2.SelectedValue !="0" && this.ddNationality2.SelectedValue !="163")
			{
				RequiredFieldValidator7.Enabled=true;
			}
			if(this.ddNationality2.SelectedValue !="0" && this.ddNationality2.SelectedValue=="163" && this.ddNationality.SelectedValue=="0")
			{
				RequiredFieldValidator7.Enabled=false;
			}
			if(this.ddNationality2.SelectedValue !="0" && this.ddNationality2.SelectedValue !="163" && this.ddNationality.SelectedValue=="0")
			{
				RequiredFieldValidator6.Enabled=false;
			}
			if(this.ddNationality.SelectedValue=="0" && this.ddNationality2.SelectedValue=="0")
			{
				RequiredFieldValidator6.Enabled=false;
				RequiredFieldValidator7.Enabled=false;
			}
		}

		/*public void getYears()
		{
			int startYear=1900;
			int endYear=DateTime.Today.Year;
			for(int i=endYear;i>=startYear;i--)
			{
				ListItem itm=new ListItem(i.ToString(),i.ToString());
				ListItem item=new ListItem(i.ToString(),i.ToString());
				this.DurationFrom.Items.Add(itm);
				this.DurationTo.Items.Add(item);  
			}
			ListItem itm2=new ListItem("In Progress","-1");
			this.DurationTo.Items.Insert(1,itm2);
		}

		public enum _enumGender
		{
			Yes=1,
			No=2
		}; 
		public enum _enumStatus
		{
			
			Single=1,
			Married=2,
			Divorced=3,
			Widow=4,
			Separated=5
		};

		public enum _enumRelation
		{
			Husband=2,
			Wife=3,
			Son=4,
			Daughter=5 		
		};*/

		/*public void generateId()
		{
			for(int i=0;i<this.DataGrid1.Items.Count;i++)
			{
				Label lbl=(Label)this.DataGrid1.Items[i].FindControl("lblId");
				int tVar=i;
				lbl.Text=tVar.ToString();  
			}
		}*/

		/*public void eCount()
		{
			for(int i=0;i<this.DataGrid3.Items.Count;i++)
			{
				Label lbl=(Label)this.DataGrid3.Items[i].FindControl("lbleCount");
				int tVar=i;
				lbl.Text=tVar.ToString();  
			}
		}*/

		/*private void btnSubmitFamily_Click(object sender, System.EventArgs e)
		{
			if(IsRefresh)
				return;
			ArrayList list=new ArrayList();
			for(int i=0;i<this.DataGrid1.Items.Count;i++)
			{
				FamilyDetails f=new FamilyDetails();
				f.Name=this.DataGrid1.Items[i].Cells[0].Text;
				f.RelationShip=Int32.Parse(this.DataGrid1.Items[i].Cells[12].Text);
				if(this.DataGrid1.Items[i].Cells[3].Text=="Male")
				{
					f.Gender=1;
				}
				else if(this.DataGrid1.Items[i].Cells[3].Text=="Female")
				{
					f.Gender=2;
				}
				else
				{
					f.Gender=0;
				}
				f.MaritialStatus=Int32.Parse(this.DataGrid1.Items[i].Cells[13].Text);
				if(this.DataGrid1.Items[i].Cells[4].Text !="&nbsp;")
				{
					f.Occupation=this.DataGrid1.Items[i].Cells[4].Text;
				}
				f.DOB=DateTime.Parse(this.DataGrid1.Items[i].Cells[5].Text);
				f.Dependent=Int32.Parse(this.DataGrid1.Items[i].Cells[14].Text); 
				if(this.DataGrid1.Items[i].Cells[9].Text !="&nbsp;")
				{
					if(this.DataGrid1.Items[i].BackColor==Color.Red)
					{
						f.IsNew=-1;
						f.fdId=this.DataGrid1.Items[i].Cells[9].Text;
					}
					else
					{
						f.fdId=this.DataGrid1.Items[i].Cells[9].Text;
						f.IsNew=0;
					}
				}
				else
				{
					
					f.IsNew=1;
				}
				list.Add(f);  
			}
			con.Open();
			int k=0;
			for(int j=0;j<list.Count;j++)
			{
				FamilyDetails _fam=(FamilyDetails)list[j];
				//string setRecords="insert into t_familydetail_temp values('"+Session["user_id"].ToString()+"','"+_fam.Name+"',"+ _fam.RelationShip +","+ _fam.MaritialStatus +","+ _fam.Gender +",'"+ _fam.Occupation +"','"+ 1 +"','"+ _fam.requestDate +"','"+ 1 +"',"+ _fam.IsNew +","+ _fam.fdId +",'"+ _fam.DOB +"',"+ _fam.Dependent +")";
				try
				{
					string setRecords="insert into t_familydetail_temp(pcode,name,relationship,maritialstatus,gender,occupation,isactive,requestdate,isrequest,isnew,dob,dependent,fdid) values(@pcode,@name,@relationship,@maritialstatus,@gender,@occupation,1,@requestdate,1,@isnew,@dob,@dependent,@fdid)";
					SqlCommand cmd=new SqlCommand(setRecords,con);
					SqlParameter _pcode=new SqlParameter("@pcode",SqlDbType.Char);
					SqlParameter _name=new SqlParameter("@name",SqlDbType.VarChar);
					SqlParameter _relationship=new SqlParameter("@relationship",SqlDbType.Int);
					SqlParameter _maritialstatus=new SqlParameter("@maritialstatus",SqlDbType.TinyInt);
					SqlParameter _gender=new SqlParameter("@gender",SqlDbType.TinyInt);
					SqlParameter _occupation=new SqlParameter("@occupation",SqlDbType.VarChar);
					SqlParameter _requestdate=new SqlParameter("@requestdate",SqlDbType.SmallDateTime);
					SqlParameter _isnew=new SqlParameter("@isnew",SqlDbType.Int);
					SqlParameter _fdid=new SqlParameter("@fdid",SqlDbType.Int);
					SqlParameter _dob=new SqlParameter("@dob",SqlDbType.SmallDateTime);
					SqlParameter _dependent=new SqlParameter("@dependent",SqlDbType.TinyInt);
					_pcode.Value=Session["user_id"].ToString();
					_name.Value=_fam.Name;
					_relationship.Value=_fam.RelationShip;
					_maritialstatus.Value=_fam.MaritialStatus;
					_gender.Value=_fam.Gender;
					_occupation.Value=_fam.Occupation;
					_requestdate.Value=_fam.requestDate;
					_isnew.Value=_fam.IsNew;
					if(_fam.fdId=="NULL")
					{
						_fdid.Value=DBNull.Value;
					}
					else
					{
						_fdid.Value=Int32.Parse(_fam.fdId);
					}
					//_fdid.Value=_fam.fdId;
					_dob.Value=_fam.DOB;
					_dependent.Value=_fam.Dependent;
					cmd.Parameters.Add(_pcode);
					cmd.Parameters.Add(_name);
					cmd.Parameters.Add(_relationship);
					cmd.Parameters.Add(_maritialstatus);
					cmd.Parameters.Add(_gender);
					cmd.Parameters.Add(_occupation);
					cmd.Parameters.Add(_dob);
					cmd.Parameters.Add(_dependent);
					cmd.Parameters.Add(_requestdate);
					cmd.Parameters.Add(_isnew);
					cmd.Parameters.Add(_fdid);
					k=cmd.ExecuteNonQuery();
				}
				catch(Exception ex)
				{
					Response.Write(ex.Message+ex.StackTrace);
				}
			}
			con.Close();
			if(k>0)
			{
				this.lblFamilyMsg.Text="Your Family Information has been successfully submitted";
				this.lblFamilyMsg.Visible=true;
				dFamily=(DataSet)Session["dFamily"];
				dFamily.Clear();
				this.DataGrid1.DataSource=dFamily;
				this.DataGrid1.DataBind();
				this.Label2.Visible=false;
				this.btnSubmitFamily.Visible=false;
				this.DataGrid1.Visible=false;
				
			}
			else
			{
				this.lblFamilyMsg.Text="";
				this.lblFamilyMsg.Visible=false;
			}
		}*/

		/*private void DataGrid3_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			btnEdNew.Enabled=false;
			//btnEdUpdate.Enabled=true;
			if(this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[9].Text !="-1")
			{
				this.txtOtherDegree.Text="";
				this.txtOtherDegree.Visible=false;
				this.txtDegree.SelectedValue=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[9].Text; 
				Label lbl=(Label)this.DataGrid3.Items[this.DataGrid3.SelectedIndex].FindControl("lbleCount");
				this.txtOtherDegree.ToolTip=lbl.Text;
			}
			else
			{
				this.txtDegree.SelectedValue="-1";
				this.txtOtherDegree.Visible=true;
				this.txtOtherDegree.Text=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[0].Text;
				Label lbl=(Label)this.DataGrid3.Items[this.DataGrid3.SelectedIndex].FindControl("lbleCount");
				this.txtOtherDegree.ToolTip=lbl.Text;
			}
			if(this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[10].Text !="-1")
			{
				this.txtOtherInstitute.Text="";
				this.txtOtherInstitute.Visible=false;
				this.txtInstitute.SelectedValue=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[10].Text; 
			}
			else
			{
				this.txtInstitute.SelectedValue="-1";
				this.txtOtherInstitute.Visible=true;
				this.txtOtherInstitute.Text=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[1].Text; 
			}
			this.DurationFrom.SelectedValue=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[2].Text;
			this.DurationTo.SelectedValue=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[3].Text;
			this.txtResult.SelectedValue=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[4].Text;
			
			if(this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[5].Text !="&nbsp;")
			{
				this.txtOtherMajors.Text=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[5].Text;
			}
			else
			{
				this.txtOtherMajors.Text="";
			}
			
			if(this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[6].Text !="&nbsp;")
			{
				this.txtAchievements.Text=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[6].Text;
			}
			else
			{
				this.txtAchievements.Text="";
			}
			this.lblInvisiblity.Text=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[8].Text;
		}*/

		/*private void btnSubmitEducation_Click(object sender, System.EventArgs e)
		{
			if(IsRefresh)
				return;
			ArrayList _list=new ArrayList();
			for(int i=0;i<this.DataGrid3.Items.Count;i++)
			{
				EducationalDetails ed=new EducationalDetails();
				if(this.DataGrid3.Items[i].Cells[11].Text!="-1")
				{
					ed.degree=Int32.Parse(this.DataGrid3.Items[i].Cells[11].Text);
				}
				else
				{
					ed.degree=-1;
					ed.otherDegree=this.DataGrid3.Items[i].Cells[0].Text; 
				}
				if(this.DataGrid3.Items[i].Cells[12].Text !="-1")
				{
					ed.institute=Int32.Parse(this.DataGrid3.Items[i].Cells[12].Text);
				}
				else
				{
					ed.institute=-1;
					ed.otherInstitute=this.DataGrid3.Items[i].Cells[1].Text;
				}
				ed.durationfrom=Int32.Parse(this.DataGrid3.Items[i].Cells[2].Text);
				ed.durationto=Int32.Parse(this.DataGrid3.Items[i].Cells[13].Text);
				ed.result=this.DataGrid3.Items[i].Cells[4].Text;

				if(this.DataGrid3.Items[i].Cells[5].Text !="&nbsp;")
				{
					ed.majors=this.DataGrid3.Items[i].Cells[5].Text;
				}
				if(this.DataGrid3.Items[i].Cells[6].Text !="&nbsp;")
				{
					ed.achievements=this.DataGrid3.Items[i].Cells[6].Text;
				}
				if(this.DataGrid3.Items[i].Cells[8].Text !="&nbsp;")
				{
					if(this.DataGrid3.Items[i].BackColor==Color.Red)
					{
						ed.parentId="-1";
						ed.type=this.DataGrid3.Items[i].Cells[8].Text;
					}
					else
					{
						ed.parentId=this.DataGrid3.Items[i].Cells[8].Text;
					}
				}
				_list.Add(ed);
			}
			con.Open();
			int k=0;
			for(int j=0;j<_list.Count;j++)
			{
				EducationalDetails _edu=(EducationalDetails)_list[j];
				//string var="NULL";
				//string setRecords="insert into t_educationinfo_temp values('"+ Session["user_id"].ToString() +"',"+ _edu.degree +","+ _edu.institute +","+ _edu.durationfrom +","+ _edu.durationto +",'"+ _edu.result +"','"+ _edu.majors +"','"+ _edu.achievements +"','"+1+"','"+ _edu.resquest +"','"+1+"',"+ _edu.parentId +",'"+ _edu.otherDegree +"','"+ _edu.otherInstitute +"','"+ _edu.majors +"',"+ _edu.type +")";
				string setRecords="insert into t_educationinfo_temp(pcode,degree,institute,durationfrom,durationto,result,majors,achievements,isactive,requestdate,isrequest,p_id,otherdegree,otherinstitute,othermajors,type) values(@pcode,@degree,@institute,@durationfrom,@durationto,@result,@majors,@achievement,1,@requestdate,1,@p_id,@otherdegree,@otherinstitute,@othermajors,@type)";
				SqlCommand cmd=new SqlCommand(setRecords,con);
				SqlParameter _pcode=new SqlParameter("@pcode",SqlDbType.Char);
				SqlParameter _degree=new SqlParameter("@degree",SqlDbType.Int);
				SqlParameter _institute=new SqlParameter("@institute",SqlDbType.Int);
				SqlParameter _durationfrom=new SqlParameter("@durationfrom",SqlDbType.Int);
				SqlParameter _durationto=new SqlParameter("@durationto",SqlDbType.Int);
				SqlParameter _result=new SqlParameter("@result",SqlDbType.VarChar);
				SqlParameter _majors=new SqlParameter("@majors",SqlDbType.VarChar);
				SqlParameter _achievement=new SqlParameter("@achievement",SqlDbType.Text);
				SqlParameter _requestdate=new SqlParameter("@requestdate",SqlDbType.SmallDateTime);
				SqlParameter _p_id=new SqlParameter("@p_id",SqlDbType.Int);
				SqlParameter _otherdegree=new SqlParameter("@otherdegree",SqlDbType.VarChar);
				SqlParameter _otherinstitute=new SqlParameter("@otherinstitute",SqlDbType.VarChar);
				SqlParameter _othermajors=new SqlParameter("@othermajors",SqlDbType.VarChar);	
				SqlParameter _type=new SqlParameter("@type",SqlDbType.Int);
				_pcode.Value=Session["user_id"].ToString();
				_degree.Value=_edu.degree;
				_institute.Value=_edu.institute;
				_durationfrom.Value=_edu.durationfrom;
				_durationto.Value=_edu.durationto;
				_result.Value=_edu.result;
				_majors.Value=_edu.majors;
				_achievement.Value=_edu.achievements;
				_requestdate.Value=_edu.resquest;
				if(_edu.parentId=="NULL")
				{
					_p_id.Value=DBNull.Value;
				}
				else
				{
					_p_id.Value=Int32.Parse(_edu.parentId);
				}
				_otherdegree.Value=_edu.otherDegree;
				_otherinstitute.Value=_edu.otherInstitute;
				_othermajors.Value=_edu.majors;
				if(_edu.type=="NULL")
				{
					_type.Value=DBNull.Value;
				}
				else
				{
					_type.Value=Int32.Parse(_edu.type);
				}
				cmd.Parameters.Add(_pcode);
				cmd.Parameters.Add(_degree);
				cmd.Parameters.Add(_institute);
				cmd.Parameters.Add(_durationfrom);
				cmd.Parameters.Add(_durationto);
				cmd.Parameters.Add(_result);
				cmd.Parameters.Add(_majors);
				cmd.Parameters.Add(_achievement);
				cmd.Parameters.Add(_requestdate);
				cmd.Parameters.Add(_p_id);
				cmd.Parameters.Add(_otherdegree);
				cmd.Parameters.Add(_otherinstitute);
				cmd.Parameters.Add(_othermajors);
				cmd.Parameters.Add(_type);
				k=cmd.ExecuteNonQuery();
			}
			con.Close();
			if(k>0)
			{
				this.lblEducationMsg.Text="Your Education Information has been successfully submitted";
				this.lblEducationMsg.Visible=true;
				this.btnSubmitEducation.Visible=false;
				this.Label1.Visible=false;
				dEducation=(DataSet)Session["dEducation"];
				dEducation.Clear();
				this.DataGrid3.DataSource=dEducation;
				this.DataGrid3.DataBind();
				this.DataGrid3.Visible=false;
			}
			else
			{
				this.lblEducationMsg.Text="";
				this.lblEducationMsg.Visible=false; 
			}
		}*/

		private void ddNationality2_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			settings();
		}
		public string Count()
		{
			return DateTime.Today.Date.ToString();
		}

		private void CustomValidator9_ServerValidate(object source, System.Web.UI.WebControls.ServerValidateEventArgs args)
		{
			args.IsValid = (args.Value !="0");
		}

		private void CustomValidator10_ServerValidate(object source, System.Web.UI.WebControls.ServerValidateEventArgs args)
		{
			args.IsValid = (args.Value !="0");
		}

		private void FillMonthsDDL()
		{

			SqlConnection conn2 = new SqlConnection(Connection.ConnectionStringPaySlip);
			conn2.Open();

			SqlDataAdapter da = new SqlDataAdapter("SELECT DISTINCT MONTH(SalaryDate) AS Months " +
				" FROM dbo.Vw_PaySlip " +
				" WHERE (REPLACE(Staff_Code, '-', '') = '" + Session["user_id"].ToString() + "') AND (YEAR(SalaryDate) = " + ddlYear.SelectedValue + ") " +
				" ORDER BY MONTH(SalaryDate)",conn2);

			DataSet ds = new DataSet();

			da.Fill(ds,"Months");

			ddlMonths.Items.Clear();
			ddlMonths.Items.Add("Select -- Month");

			for(int c=0;c<ds.Tables[0].Rows.Count;c++)
			{
				int k = int.Parse(ds.Tables[0].Rows[c]["Months"].ToString());
				ddlMonths.Items.Add(new ListItem(MonthName[k-1],k.ToString()));
			}

		}
		private void DropDownList1_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			if (ddlYear.SelectedIndex>0)
			{
				FillMonthsDDL();
				ddlMonths.Enabled=true;
			}
			else
			{
				ddlMonths.Enabled=false;
			}
			GetSalary();
			tabPaySlip.Visible=false;
		}

		private void GetSalary()
		{

			if (ddlMonths.SelectedIndex>0 && ddlYear.SelectedIndex>0)
			{
				SqlConnection conn2 = new SqlConnection(Connection.ConnectionStringPaySlip);
				conn2.Open();

				SqlDataAdapter da = new SqlDataAdapter("select gross,income_tax,Arrears,SalaryDate,Net_Payroll,attendance,bank_name,branch_name from Vw_payslip where year(salarydate)="  + ddlYear.SelectedValue +  " and month(salarydate)=" + ddlMonths.SelectedValue + " and replace(staff_code,'-','')='" + Session["user_id"].ToString() + "'",conn2);
				DataSet ds = new DataSet();
				da.Fill(ds,"PaySlip");
				if(ds.Tables["PaySlip"].Rows.Count>0)
				{
					lblGrossSalaryP.Text=ds.Tables["PaySlip"].Rows[0]["Gross"].ToString();
					int gs = int.Parse(lblGrossSalaryP.Text);
					lblGrossSalaryP.Text+="<br>"+Number.NumberToWord(gs)+" Rupees";
					lblGrossSalaryP.Text="Rs. "+lblGrossSalaryP.Text;
					lblBankP.Text=ds.Tables["PaySlip"].Rows[0]["Bank_Name"].ToString();
					lblBranchP.Text=ds.Tables["PaySlip"].Rows[0]["Branch_Name"].ToString();
					lblIncomTaxP.Text=ds.Tables["PaySlip"].Rows[0]["Income_Tax"].ToString();
					gs=int.Parse(lblIncomTaxP.Text);
					lblIncomTaxP.Text="Rs. " + lblIncomTaxP.Text+"<br>"+Number.NumberToWord(gs)+" Rupees";
					lblNetPayRollP.Text=ds.Tables["PaySlip"].Rows[0]["Net_Payroll"].ToString();
					gs=int.Parse(lblNetPayRollP.Text);
					lblNetPayRollP.Text="Rs. " + lblNetPayRollP.Text+"<br>"+Number.NumberToWord(gs)+" Rupees";
					lblArearsP.Text=ds.Tables["PaySlip"].Rows[0]["Arrears"].ToString();
					gs=int.Parse(lblArearsP.Text);
					lblArearsP.Text="Rs. " + lblArearsP.Text+"<br>"+Number.NumberToWord(gs)+" Rupees";
					lblTotalWorkingDaysP.Text=ds.Tables["PaySlip"].Rows[0]["attendance"].ToString()+" Days";
					lblSalaryDateP.Text=DateTime.Parse(ds.Tables["PaySlip"].Rows[0]["salarydate"].ToString()).ToString("d MMM, yyyy");
					tabPaySlip.Visible=true;
					lblMessage.Visible=false;
				}
				else
				{
					tabPaySlip.Visible=false;
					lblGrossSalaryP.Text="";
					lblBankP.Text="";
					lblBranchP.Text="";
					lblIncomTaxP.Text="";
					lblNetPayRollP.Text="";
					lblArearsP.Text="";
					lblTotalWorkingDaysP.Text="";
					lblSalaryDateP.Text="";
					lblMessage.Text="Salary Record Not Found...";
					lblMessage.Visible=true;
				}
				
			}


		}

		private void FillYearDDL()
		{
			SqlConnection conn2 = new SqlConnection(Connection.ConnectionStringPaySlip);
			conn2.Open();

			SqlDataAdapter da = new SqlDataAdapter("SELECT DISTINCT YEAR(SalaryDate) AS Years " +
				" FROM dbo.Vw_PaySlip " +
				" WHERE (REPLACE(Staff_Code, '-', '') = '" + Session["user_id"].ToString() + "') " +
				" ORDER BY YEAR(SalaryDate) DESC",conn2); 

			DataSet ds = new DataSet();

			da.Fill(ds,"Years");

			if (ds.Tables[0].Rows.Count>0)
			{
				ddlYear.Items.Clear();
				ddlYear.Items.Add("Select -- Year");
				for(int j=0;j<ds.Tables[0].Rows.Count;j++)
				{
					ddlYear.Items.Add(new ListItem(ds.Tables[0].Rows[j][0].ToString(),ds.Tables[0].Rows[j][0].ToString()));
				}

			}
			else
			{
				ddlYear.Items.Clear();
				ddlYear.Items.Add("Select -- Year");
			}
		}
		private void ibSalary_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			pnlPersonal.Visible=false;
			pnlEmployeeInfo.Visible=false;
			//pnlFamilyInfo.Visible=false;
			//pnlEducation.Visible=false;
			pnlPaySlip.Visible=true;
			lblPCodeP.Text=lblPCode.Text;
			lblNameP.Text=lblEmpInfo.Text;
			lblDeptP.Text=lnlEmpInfoDept.Text;
			lblDesgP.Text=lblEmpInfoDesignation.Text;
			FillYearDDL();
			GetSalary();
			btnUpdate.Visible=false;
			tabPaySlip.Visible=false;
		}

		private void ddlMonths_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			GetSalary();
			if(this.ddlMonths.SelectedIndex>0)
			{
				Session["sal_month"]=this.ddlMonths.SelectedValue.ToString();
				Session["sal_year"]=this.ddlYear.SelectedValue.ToString();
				this.btnPrint.Enabled=true;
			}
			else
			{
				Session["sal_month"]="";
				Session["sal_year"]="";
				this.btnPrint.Enabled=false;
			}
		}

		public class FamilyDetails
		{
			public string Name="";
			public int RelationShip=0;
			public int MaritialStatus=0;
			public int Gender=0;
			public string Occupation="";
			public DateTime DOB;
			public int Dependent=0;
			public int IsNew=-2;
			public DateTime requestDate=DateTime.Now;
			public string fdId="NULL";
		}

		public class EducationalDetails
		{
			public int degree=0;
			public int institute=0;
			public int durationfrom=0;
			public int durationto=0;
			public string result="";
			public string majors="";
			public string achievements="";
			public DateTime resquest=DateTime.Now;
			public string parentId="NULL";
			public string otherDegree="";
			public string otherInstitute="";
			public string type="NULL";
		
		}
		protected override void LoadViewState(object savedState)
		{
			object[] allStates = (object[]) savedState;
			base.LoadViewState(allStates[0]);
			_refreshState = (bool) allStates[1];
			try
			{
				_isRefresh = _refreshState == (bool) Session["__ISREFRESH"];
			}
			catch(Exception)
			{
				Response.Redirect("Login.aspx");
			}

		}

		protected override object SaveViewState()
		{
			Session["__ISREFRESH"] = _refreshState;
			object[] allStates = new object[2];
			allStates[0] = base.SaveViewState();
			allStates[1] = !_refreshState;
			return allStates;
		}
		/*public void delFamily(object sender,EventArgs e)
		{
			if(!_isRefresh)
			{
				LinkButton rLink=(LinkButton)sender;
				for(int i=0;i<this.DataGrid1.Items.Count;i++)
				{
					LinkButton lnk=(LinkButton)this.DataGrid1.Items[i].FindControl("lnkDelete");
					if(this.DataGrid1.Items[i].BackColor==Color.Red)
					{
						this.DataGrid1.Items[i].Cells[7].Text=""; 
					}
					if(lnk.Equals(rLink))
					{
						if(this.DataGrid1.Items[i].Cells[9].Text=="&nbsp;")
						{
							ArrayList tempList=new ArrayList();
							for(int z=0;z<this.DataGrid1.Items.Count;z++)
							{
								if(this.DataGrid1.Items[z].BackColor==Color.Red)
								{
									tempList.Add(z);
								}
							}
							dFamily=(DataSet)Session["dFamily"];
							dFamily.Tables[0].Rows[i].Delete();
							this.DataGrid1.DataSource=dFamily;
							this.DataGrid1.DataBind();
							generateId();
							dFamily.AcceptChanges();
							for(int b=0;b<tempList.Count;b++)
							{
								int indx=(int)tempList[b];
								this.DataGrid1.Items[indx].BackColor=Color.Red;
								LinkButton eLnk=(LinkButton)this.DataGrid1.Items[indx].Cells[10].FindControl("lnkDelete");
								eLnk.Visible=false;
								this.DataGrid1.Items[indx].Cells[7].Text="";
							}
						}
						else
						{
							this.DataGrid1.Items[i].BackColor=Color.Red;
							this.DataGrid1.Items[i].Cells[7].Text="";
							LinkButton sLnk=(LinkButton)this.DataGrid1.Items[i].FindControl("lnkDelete");
							sLnk.Visible=false;
						}
						for(int ik=0;ik<this.DataGrid1.Items.Count;ik++)
						{
							LinkButton link=(LinkButton)this.DataGrid1.Items[ik].FindControl("lnkDelete");
							if(link.Visible)
								link.Attributes.Add("onclick","return window.confirm('Are You Sure To Delete')");
						}
						break;
					}
				}
			}
			else
			{
				dFamily=(DataSet)Session["dFamily"];
				this.DataGrid1.DataSource=dFamily;
				this.DataGrid1.DataBind();
				generateId();
				for(int i=0;i<this.DataGrid1.Items.Count;i++)
				{
					LinkButton lnk=(LinkButton)this.DataGrid1.Items[i].FindControl("lnkDelete");
					lnk.Attributes.Add("onclick","return window.confirm('Are You Sure To Delete')");
				}
			}
		}*/

		/*public void delEducation(object sender,EventArgs e)
		{
			if(!_isRefresh)
			{
				LinkButton rLink=(LinkButton)sender;
				for(int i=0;i<this.DataGrid3.Items.Count;i++)
				{
					if(this.DataGrid3.Items[i].BackColor==Color.Red)
					{
						this.DataGrid3.Items[i].Cells[7].Text=""; 
					}
					LinkButton lnk=(LinkButton)this.DataGrid3.Items[i].FindControl("lnkeDelete");
					if(lnk.Equals(rLink))
					{
						if(this.DataGrid3.Items[i].Cells[8].Text=="&nbsp;")
						{
							ArrayList tempList=new ArrayList();
							for(int z=0;z<this.DataGrid3.Items.Count;z++)
							{
								if(this.DataGrid3.Items[z].BackColor==Color.Red)
								{
									tempList.Add(z);
								}
							}
							dEducation=(DataSet)Session["dEducation"];
							dEducation.Tables[0].Rows[i].Delete();
							this.DataGrid3.DataSource=dEducation;
							this.DataGrid3.DataBind();
							eCount();
							dEducation.AcceptChanges();
							for(int b=0;b<tempList.Count;b++)
							{
								int indx=(int)tempList[b];
								this.DataGrid3.Items[indx].BackColor=Color.Red;
								LinkButton eLnk=(LinkButton)this.DataGrid3.Items[indx].Cells[9].FindControl("lnkeDelete");
								eLnk.Visible=false;
								this.DataGrid3.Items[indx].Cells[7].Text="";
							}
						}
						else
						{
							this.DataGrid3.Items[i].BackColor=Color.Red;
							LinkButton eLnk=(LinkButton)this.DataGrid3.Items[i].Cells[9].FindControl("lnkeDelete");
							eLnk.Visible=false;
							this.DataGrid3.Items[i].Cells[7].Text="";
						}
						for(int jk=0;jk<this.DataGrid3.Items.Count;jk++)
						{
							LinkButton link=(LinkButton)this.DataGrid3.Items[jk].FindControl("lnkeDelete");
							if(link.Visible)
							{
								link.Attributes.Add("onclick","return confirm('Are You Sure To Delete')");
							}
						}
						break;
					}
				}
			}
			else
			{
				dEducation=(DataSet)Session["dEducation"];
				this.DataGrid3.DataSource=dEducation;
				this.DataGrid3.DataBind();
				eCount();
				for(int jk=0;jk<this.DataGrid3.Items.Count;jk++)
				{
					LinkButton lnk=(LinkButton)this.DataGrid3.Items[jk].FindControl("lnkeDelete");
					lnk.Attributes.Add("onclick","return confirm('Are You Sure To Delete')");
				}
			}
		}*/

		private void btnIgnore_Click(object sender, System.EventArgs e)
		{
			if(pnlEmployeeInfo.Visible)
			{
				//ArrayList list=(ArrayList)Session["cRequest"];
				//ArrayList pendingList=(ArrayList)Session["pendingRequest"];
				ArrayList list=new ArrayList();
				if(CheckRequest(this.lblEmpInfoName.Text,this.txtEmpInfoName.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=7;
					h.fieldname="Name";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtEmpInfoName.Text;
					h.requestforchangeID.Value=DBNull.Value;
					if(lnName.Visible)
					{
						h.docGUID=lblfpName.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpName.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
				if(CheckRequest(this.lblEmpInfoDesignation.Text,this.ddDesignation.SelectedItem.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=6;
					h.fieldname="Designation";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.ddDesignation.SelectedItem.Text;
					h.requestforchangeID.Value=this.ddDesignation.SelectedItem.Value;
					/*if(lnDesignation.Visible)
					{
						h.docGUID=lblfpDesignation.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpDesignation.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);*/

					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(CheckRequest(this.lnlEmpInfoDept.Text,this.ddDepartment.SelectedItem.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=10;
					h.fieldname="Department";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.ddDepartment.SelectedItem.Text;
					h.requestforchangeID.Value=this.ddDesignation.SelectedItem.Value;
					/*if(lnDepartment.Visible)
					{
						h.docGUID=lblfpDepartment.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpDepartment.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);*/

					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(CheckRequest(this.lblEmpInfoCity.Text,this.ddStation.SelectedItem.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=8;
					h.fieldname="Station";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.ddStation.SelectedItem.Text;
					h.requestforchangeID.Value=this.ddStation.SelectedItem.Value;
					/*if(lnStation.Visible)
					{
						h.docGUID=lblfpStation.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpStation.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);*/
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(CheckRequest(this.lblEmpInfoDOJ.Text,this.CalenderDOJ.SelectedDate.ToString("dd MMM,yyyy")))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=9;
					h.fieldname="Date of Joining";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.CalenderDOJ.SelectedDate.ToString("dd MMM,yyyy");
					h.requestforchangeID.Value=this.CalenderDOJ.SelectedDate.ToString("d MMM,yyyy");
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(CheckRequest(this.lblEmpInfoExtension.Text,this.txtEmpInfoExtension.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=11;
					h.fieldname="Extension";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtEmpInfoExtension.Text;
					h.requestforchangeID.Value=DBNull.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(CheckRequest(this.lblEmpInfoEmail.Text,this.txtEmpInfoEmail.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=31;
					h.fieldname="Email (Official)";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtEmpInfoEmail.Text;
					h.requestforchangeID.Value=DBNull.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(CheckRequest(this.lblEmpInfoSessi.Text,this.txtEmpInfoSESSI.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=12;
					h.fieldname="SESSI #";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtEmpInfoSESSI.Text;
					h.requestforchangeID.Value=DBNull.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(CheckRequest(this.lblEmpInfoEObi.Text,this.txtEmpInfoEOBI.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=13;
					h.fieldname="EOBI #";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtEmpInfoEOBI.Text;
					h.requestforchangeID.Value=DBNull.Value;
					if(lnEOBI.Visible)
					{
						h.docGUID=lblfpEOBI.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpEOBI.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
				if(CheckRequest(this.lblEmpLocation.Text,this.txtEmpLocation.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=14;
					h.fieldname="Workstation Address";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtEmpLocation.Text;
					h.requestforchangeID.Value=DBNull.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				Session["cRequest"]=list;
				ArrayList requestList=new ArrayList();
				ArrayList pendingList=new ArrayList();
				ArrayList removeList=(ArrayList)Session["rRequest"];
				for(int i=0;i<removeList.Count;i++)
				{
					RequestHandler r=(GeoRabtaSite.RequestHandler)removeList[i];
					for(int z=0;z<list.Count;z++)
					{
						RequestHandler Or=(GeoRabtaSite.RequestHandler)list[z];
						if(r.fieldno.Equals(Or.fieldno))
						{
							list.RemoveAt(z);
						}
					}
				}
				con.Open();
				SqlTransaction tran=con.BeginTransaction();
				SqlCommand conQuery=new SqlCommand("select * from t_employeerequests where requisitecode='"+Session["user_id"].ToString()+"' and requeststatus=1",con);
				conQuery.Transaction=tran;
				SqlDataReader conrd=conQuery.ExecuteReader();
				while(conrd.Read())
				{
					requestList.Add(conrd[2].ToString());
				}
				conrd.Close();
				tran.Commit();
				con.Close();
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler h=(GeoRabtaSite.RequestHandler)list[i];
					string Val=""+h.fieldno+"";	
					if(requestList.Contains(Val))
					{
						pendingList.Add(h);
					}
				}
				Session["pendingRequest"]=pendingList;
				for(int i=0;i<pendingList.Count;i++)
				{
					GeoRabtaSite.RequestHandler cR=(GeoRabtaSite.RequestHandler)pendingList[i];
					string fieldname=cR.fieldname;
					if(list.Contains(cR))
						list.Remove(cR);
				
				}
				//==================================Check Change in Request====================//
				if(list.Count<=0)
				{
					//Response.Write("<script>window.alert('There is no change in request(s). Thank You')</script>");
					this.lblCombine.Visible=true;
					this.lblCombine.Text="There is no change in request(s). Thank You";
					return;
				}

				string message="'<ul>Please attach/upload scan copy(ies) of following fields before sending your request";
				string messagefordis="Please attach/upload scan copy(ies) of following fields before sending your request \\n";
				string field="";
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler h=(GeoRabtaSite.RequestHandler)list[i];
					if(h.isDocRequired && h.docGUID=="")
					{
						field+="<li>"+h.fieldname+"</li>";
						messagefordis+=h.fieldname+"\\n";
					}
				}
				if(field.Length>0)
				{
					message+=field;
					message+="</ul>'";
					//Response.Write("<script>window.alert('"+messagefordis+"')</script>");
					this.lblCombine.Visible=true;
					this.lblCombine.Text=message;
					return;
				}
			    
				con.Open();
				SqlTransaction trans=con.BeginTransaction();
				try
				{
					for(int i=0;i<list.Count;i++)
					{
						GeoRabtaSite.RequestHandler h=(GeoRabtaSite.RequestHandler)list[i];
						SqlCommand cmd=new SqlCommand("select timeline,replyatrequest from t_fieldsmgt where f_id="+h.fieldno+"",con);
						cmd.Transaction=trans;
						SqlDataReader rd=cmd.ExecuteReader();
						rd.Read();
						if(rd.HasRows)
						{
							int workingdays=Int32.Parse(rd[0].ToString());
							h.requesttimeline=DateTime.Now.Date.AddDays(workingdays).ToShortDateString();
							string replyatrequest=rd[1].ToString();
							rd.Close();
							cmd=new SqlCommand("insert into t_employeerequests(fieldno,fieldname,requestforchange,requisitecode,requestdate,requesttime,requesttimeline,requeststatus,documentID) values(@fieldno,@fieldname,@requestforchange,@requisitecode,getdate(),@requesttime,@requesttimeline,@requeststatus,@documentID)",con);
							cmd.Transaction=trans;
							SqlParameter _fieldno=new SqlParameter("@fieldno",SqlDbType.Int);
							SqlParameter _fieldname=new SqlParameter("@fieldname",SqlDbType.VarChar);
							SqlParameter _requestforchange=new SqlParameter("@requestforchange",SqlDbType.VarChar);
							SqlParameter _requisitecode=new SqlParameter("@requisitecode",SqlDbType.VarChar);
							//SqlParameter _requestdate=new SqlParameter("@requestdate",SqlDbType.DateTime);
							SqlParameter _requesttime=new SqlParameter("@requesttime",SqlDbType.VarChar);
							SqlParameter _requesttimeline=new SqlParameter("@requesttimeline",SqlDbType.DateTime);
							SqlParameter _requeststatus=new SqlParameter("@requeststatus",SqlDbType.Int);
							SqlParameter _documentID=new SqlParameter("@documentID",SqlDbType.Text);
							_fieldno.Value=h.fieldno;
							_fieldname.Value=h.fieldname;
							_requestforchange.Value=h.requestforchange;
							_requisitecode.Value=h.requisitecode;
							//_requestdate.Value=h.requestdate;
							_requesttime.Value=h.requesttime;
							_requesttimeline.Value=h.requesttimeline;
							_requeststatus.Value=h.requeststatus;
							if(h.isDocRequired)
							{
								Guid g=Guid.NewGuid();
								string filepath=Server.MapPath(@"tempEmployee\");
								string filename=g.ToString();
								if(h.fieldno==7)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpName.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\7"+Path.GetExtension(lblfpName.Text)),dPath);
									}
									else
									{
										this.fpName.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpName.PostedFile.FileName));
									}
								}
								/*if(h.fieldno==6)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpDesignation.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\6"+Path.GetExtension(lblfpDesignation.Text)),dPath);
									}
									else
									{
										this.fpDesignation.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpDesignation.PostedFile.FileName));
									}
								}*/
								/*if(h.fieldno==10)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpDepartment.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\10"+Path.GetExtension(lblfpDepartment.Text)),dPath);
									}
									else
									{
										this.fpDepartment.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpDepartment.PostedFile.FileName));
									}
								}*/
								/*if(h.fieldno==8)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpStation.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\8"+Path.GetExtension(lblfpStation.Text)),dPath);
									}
									else
									{
										this.fpStation.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpStation.PostedFile.FileName));
									}
								}*/
								if(h.fieldno==13)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpEOBI.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\13"+Path.GetExtension(lblfpEOBI.Text)),dPath);
									}
									else
									{
										this.fpEOBI.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpEOBI.PostedFile.FileName));
									}
								}
								_documentID.Value=g.ToString();
							}
							else
							{
								_documentID.Value=DBNull.Value;
							}
							cmd.Parameters.Add(_fieldno);
							cmd.Parameters.Add(_fieldname);
							cmd.Parameters.Add(_requestforchange);
							cmd.Parameters.Add(_requisitecode);
							//cmd.Parameters.Add(_requestdate);
							cmd.Parameters.Add(_requesttime);
							cmd.Parameters.Add(_requesttimeline);
							cmd.Parameters.Add(_requeststatus);
							cmd.Parameters.Add(_documentID);
							cmd.ExecuteNonQuery();
                            Bulletin.PostMessage(con,trans,replyatrequest,"Geo Raabta",Session["user_id"].ToString(),1);  
						}
						else
						{
							//Response.Write("Process flow not defined properly for request field"+h.fieldname+". Request failed to send");
							lblCombine.Visible=true;
							lblCombine.Text="Process flow not defined properly for request field"+h.fieldname+". Request failed to send";
							trans.Rollback();
							return;
						}
					}
					trans.Commit();
					con.Close();
					lblCombine.Visible=true;
					lblCombine.Text="Your request has been sent successfully. Please check your MY REQUEST tab for Requsition IDs";
					this.btnIgnore.Visible=false;
					this.btnResend.Visible=false;
					this.btnUpdate.Enabled=true;
				}
				catch(Exception ex)
				{
					Response.Write(ex.Message);
					trans.Rollback();
					con.Close();
				}
			}
			if(pnlPersonalUPdate.Visible)
			{
				ArrayList list=new ArrayList();
				if(CheckRequest(this.lblName.Text,this.calendarDOB.SelectedDate.ToString("dd MMM,yyyy")))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=16;
					h.fieldname="Date of Birth";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.calendarDOB.SelectedDate.ToString("dd MMM,yyyy");
					h.requestforchangeID.Value=this.calendarDOB.SelectedDate.ToString("d MMM,yyyy");
					if(lnDateofBirth.Visible)
					{
						h.docGUID=lblfpDateofBirth.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpDateofBirth.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
				if(CheckRequest(this.lblFName.Text,this.txtFName.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=24;
					h.fieldname="Father Name";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtFName.Text;
					h.requestforchangeID.Value=DBNull.Value;
					if(lnFatherName.Visible)
					{
						h.docGUID=lblfpFatherName.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpFatherName.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
				if(CheckRequest(this.lblGender.Text,this.RadioButtonList1.SelectedItem.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=17;
					h.fieldname="Gender";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.RadioButtonList1.SelectedItem.Text;
					h.requestforchangeID.Value=this.RadioButtonList1.SelectedItem.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(this.ddBloodGrp.SelectedValue=="0")
				{
					string bloodGrp="";
					if(CheckRequest(this.lblBloodGrp.Text,bloodGrp))
					{
						GeoRabtaSite.RequestHandler h=new RequestHandler();
						h.fieldno=18;
						h.fieldname="Blood Group";
						h.requestdate=DateTime.Now.Date.ToShortDateString();
						h.requeststatus=1;
						h.requesttime=DateTime.Now.TimeOfDay.ToString();
						h.requisitecode=Session["user_id"].ToString();
						h.requestforchange=ddBloodGrp.SelectedItem.Text;
						h.requestforchangeID.Value=this.ddBloodGrp.SelectedItem.Value;
						h.isPosted=false;
						h.isDocRequired=false;
						list.Add(h);
					}
				}
				else
				{
					string bloodGrp=this.ddBloodGrp.SelectedItem.Text;
					if(CheckRequest(this.lblBloodGrp.Text,bloodGrp))
					{
						GeoRabtaSite.RequestHandler h=new RequestHandler();
						h.fieldno=18;
						h.fieldname="Blood Group";
						h.requestdate=DateTime.Now.Date.ToShortDateString();
						h.requeststatus=1;
						h.requesttime=DateTime.Now.TimeOfDay.ToString();
						h.requisitecode=Session["user_id"].ToString();
						h.requestforchange=ddBloodGrp.SelectedItem.Text;
						h.requestforchangeID.Value=this.ddBloodGrp.SelectedItem.Value;
						h.isPosted=false;
						h.isDocRequired=false;
						list.Add(h);
					}
				}
				if(this.ddReligion.SelectedValue=="0")
				{
					string Religion="";
					if(CheckRequest(this.lblReligion.Text,Religion))
					{
						GeoRabtaSite.RequestHandler h=new RequestHandler();
						h.fieldno=26;
						h.fieldname="Religion";
						h.requestdate=DateTime.Now.Date.ToShortDateString();
						h.requeststatus=1;
						h.requesttime=DateTime.Now.TimeOfDay.ToString();
						h.requisitecode=Session["user_id"].ToString();
						h.requestforchange=ddReligion.SelectedItem.Text;
						h.requestforchangeID.Value=ddReligion.SelectedItem.Value;
						h.isPosted=false;
						h.isDocRequired=false;
						list.Add(h);
					}
				}
				else
				{
					string Religion=this.ddReligion.SelectedItem.Text;
					if(CheckRequest(this.lblReligion.Text,Religion))
					{
						GeoRabtaSite.RequestHandler h=new RequestHandler();
						h.fieldno=26;
						h.fieldname="Religion";
						h.requestdate=DateTime.Now.Date.ToShortDateString();
						h.requeststatus=1;
						h.requesttime=DateTime.Now.TimeOfDay.ToString();
						h.requisitecode=Session["user_id"].ToString();
						h.requestforchange=ddReligion.SelectedItem.Text;
						h.requestforchangeID.Value=ddReligion.SelectedItem.Value;
						h.isPosted=false;
						h.isDocRequired=false;
						list.Add(h);
					}
				}
				if(CheckRequest(this.lblNick.Text,this.txtNick.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=37;
					h.fieldname="Nick";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=txtNick.Text;
					h.requestforchangeID.Value=DBNull.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(CheckRequest(this.lblAddress.Text,this.txtAddress.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=22;
					h.fieldname="Address";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=txtAddress.Text;
					h.requestforchangeID.Value=DBNull.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(this.ddStatus.SelectedValue=="0")
				{
					string Status="";
					if(CheckRequest(this.lblMaritialStat.Text,Status))
					{
						GeoRabtaSite.RequestHandler h=new RequestHandler();
						h.fieldno=25;
						h.fieldname="Marital Status";
						h.requestdate=DateTime.Now.Date.ToShortDateString();
						h.requeststatus=1;
						h.requesttime=DateTime.Now.TimeOfDay.ToString();
						h.requisitecode=Session["user_id"].ToString();
						h.requestforchange=this.ddStatus.SelectedItem.Text;
						h.requestforchangeID.Value=this.ddStatus.SelectedItem.Value;
						if(lnMartialStatus.Visible)
						{
							h.docGUID=lblfpMaritalStatus.Text;
							h.isPosted=true;
						}
						else
						{
							h.docGUID=this.fpMartialStatus.PostedFile.FileName;
							h.isPosted=false;
						}
						h.isDocRequired=true;
						list.Add(h);
					}
				}
				else
				{
					string Status=this.ddStatus.SelectedItem.Text;
					if(CheckRequest(this.lblMaritialStat.Text,Status))
					{
						GeoRabtaSite.RequestHandler h=new RequestHandler();
						h.fieldno=25;
						h.fieldname="Marital Status";
						h.requestdate=DateTime.Now.Date.ToShortDateString();
						h.requeststatus=1;
						h.requesttime=DateTime.Now.TimeOfDay.ToString();
						h.requisitecode=Session["user_id"].ToString();
						h.requestforchange=this.ddStatus.SelectedItem.Text;
						h.requestforchangeID.Value=this.ddStatus.SelectedItem.Value;
						if(lnMartialStatus.Visible)
						{
							h.docGUID=lblfpMaritalStatus.Text;
							h.isPosted=true;
						}
						else
						{
							h.docGUID=this.fpMartialStatus.PostedFile.FileName;
							h.isPosted=false;
						}
						h.isDocRequired=true;
						list.Add(h);
					}
				}
				if(CheckRequest(this.lblEmail.Text,this.txtEmail.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=29;
					h.fieldname="Email (Personal)";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=txtEmail.Text;
					h.requestforchangeID.Value=DBNull.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(CheckRequest(this.lblNTN.Text,this.txtNTN.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=34;
					h.fieldname="NTN #";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=txtNTN.Text;
					h.requestforchangeID.Value=DBNull.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(CheckRequest(this.lblcontactNo.Text,this.txtContactNo.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=27;
					h.fieldname="Residential #";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=txtContactNo.Text;
					h.requestforchangeID.Value=DBNull.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(CheckRequest(this.lblMobileNo.Text,this.txtMobile.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=28;
					h.fieldname="Mobile #";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=txtMobile.Text;
					h.requestforchangeID.Value=DBNull.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(this.ddNationality.SelectedValue=="0")
				{
					string Nationality="";
					if(CheckRequest(this.lblNationality.Text,Nationality))
					{
						GeoRabtaSite.RequestHandler h=new RequestHandler();
						h.fieldno=35;
						h.fieldname="Nationality";
						h.requestdate=DateTime.Now.Date.ToShortDateString();
						h.requeststatus=1;
						h.requesttime=DateTime.Now.TimeOfDay.ToString();
						h.requisitecode=Session["user_id"].ToString();
						h.requestforchange=this.ddNationality.SelectedItem.Text;
						h.requestforchangeID.Value=this.ddNationality.SelectedItem.Value;
						if(lnNationality.Visible)
						{
							h.docGUID=lblfpNationality.Text;
							h.isPosted=true;
						}
						else
						{
							h.docGUID=this.fpNationality.PostedFile.FileName;
							h.isPosted=false;
						}
						h.isDocRequired=true;
						list.Add(h);
					}
				}
				else
				{
					string Nationality=this.ddNationality.SelectedItem.Text;
					if(CheckRequest(this.lblNationality.Text,Nationality))
					{
						GeoRabtaSite.RequestHandler h=new RequestHandler();
						h.fieldno=35;
						h.fieldname="Nationality";
						h.requestdate=DateTime.Now.Date.ToShortDateString();
						h.requeststatus=1;
						h.requesttime=DateTime.Now.TimeOfDay.ToString();
						h.requisitecode=Session["user_id"].ToString();
						h.requestforchange=this.ddNationality.SelectedItem.Text;
						h.requestforchangeID.Value=this.ddNationality.SelectedItem.Value;
						if(lnNationality.Visible)
						{
							h.docGUID=lblfpNationality.Text;
							h.isPosted=true;
						}
						else
						{
							h.docGUID=this.fpNationality.PostedFile.FileName;
							h.isPosted=false;
						}
						h.isDocRequired=true;
						list.Add(h);
					}
				}
				if(this.ddNationality2.SelectedValue=="0")
				{
					string Nationality2="";
					if(CheckRequest(this.lblNationality2.Text,Nationality2))
					{
						GeoRabtaSite.RequestHandler h=new RequestHandler();
						h.fieldno=36;
						h.fieldname="Nationality (Secondary)";
						h.requestdate=DateTime.Now.Date.ToShortDateString();
						h.requeststatus=1;
						h.requesttime=DateTime.Now.TimeOfDay.ToString();
						h.requisitecode=Session["user_id"].ToString();
						h.requestforchange=this.ddNationality2.SelectedItem.Text;
						h.requestforchangeID.Value=this.ddNationality2.SelectedItem.Value;
						if(lnNationality2.Visible)
						{
							h.docGUID=lblfpNationality2.Text;
							h.isPosted=true;
						}
						else
						{
							h.docGUID=this.fpNationality2.PostedFile.FileName;
							h.isPosted=false;
						}
						h.isDocRequired=true;
						list.Add(h);
					}
				}
				else
				{
					string Nationality2=this.ddNationality2.SelectedItem.Text;
					if(CheckRequest(this.lblNationality2.Text,Nationality2))
					{
						GeoRabtaSite.RequestHandler h=new RequestHandler();
						h.fieldno=36;
						h.fieldname="Nationality (Secondary)";
						h.requestdate=DateTime.Now.Date.ToShortDateString();
						h.requeststatus=1;
						h.requesttime=DateTime.Now.TimeOfDay.ToString();
						h.requisitecode=Session["user_id"].ToString();
						h.requestforchange=this.ddNationality2.SelectedItem.Text;
						h.requestforchangeID.Value=this.ddNationality2.SelectedItem.Value;
						if(lnNationality2.Visible)
						{
							h.docGUID=lblfpNationality2.Text;
							h.isPosted=true;
						}
						else
						{
							h.docGUID=this.fpNationality2.PostedFile.FileName;
							h.isPosted=false;
						}
						h.isDocRequired=true;
						list.Add(h);
					}
				}
				if(CheckRequest(this.lblPassport.Text,this.txtPassport.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=19;
					h.fieldname="Passport #";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtPassport.Text;
					h.requestforchangeID.Value=DBNull.Value;
					if(lnPassport.Visible)
					{
						h.docGUID=lblfpPassprot.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpPassport.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
				if(CheckRequest(this.lblKin.Text,this.txtKin.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=30;
					h.fieldname="Next of Kin";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtKin.Text;
					h.requestforchangeID.Value=DBNull.Value;
					if(lnNextofKin.Visible)
					{
						h.docGUID=lblfpNextofKin.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpNextofKin.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
				if(CheckRequest(this.lblNICNew.Text,this.txtNewNic.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=21;
					h.fieldname="NIC (New)";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtNewNic.Text;
					h.requestforchangeID.Value=DBNull.Value;
					if(lnNICNew.Visible)
					{
						h.docGUID=lblfpNICNew.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpNICNew.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
				if(CheckRequest(this.lblNICOld.Text,this.txtOldNic.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=20;
					h.fieldname="NIC (Old)";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtOldNic.Text;
					h.requestforchangeID.Value=DBNull.Value;
					if(lnNICOld.Visible)
					{
						h.docGUID=lblfpNICOld.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpNICOld.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
				if(CheckRequest(this.lblBankAcctNo.Text,this.txtBankAcctNo.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=32;
					h.fieldname="Bank Account #";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtBankAcctNo.Text;
					h.requestforchangeID.Value=DBNull.Value;
					if(lnBankNo.Visible)
					{
						h.docGUID=lblfpBankNo.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpBankNo.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
				if(CheckRequest(this.lblAccountDetails.Text,this.txtAccountDetails.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=33;
					h.fieldname="Bank Account Details";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtAccountDetails.Text;
					h.requestforchangeID.Value=DBNull.Value;
					if(lnBankDetail.Visible)
					{
						h.docGUID=lblfpBankDetail.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpBankDetail.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
				Session["cRequest"]=list;
				ArrayList requestList=new ArrayList();
				ArrayList pendingList=new ArrayList();
				ArrayList removeList=(ArrayList)Session["rpRequest"];
				for(int i=0;i<removeList.Count;i++)
				{
					RequestHandler r=(GeoRabtaSite.RequestHandler)removeList[i];
					for(int z=0;z<list.Count;z++)
					{
						RequestHandler Or=(GeoRabtaSite.RequestHandler)list[z];
						if(r.fieldno.Equals(Or.fieldno))
						{
							list.RemoveAt(z);
						}
					}
				}
				con.Open();
				SqlTransaction tran=con.BeginTransaction();
				SqlCommand conQuery=new SqlCommand("select * from t_employeerequests where requisitecode='"+Session["user_id"].ToString()+"' and requeststatus=1",con);
				conQuery.Transaction=tran;
				SqlDataReader conrd=conQuery.ExecuteReader();
				while(conrd.Read())
				{
					requestList.Add(conrd[2].ToString());
				}
				conrd.Close();
				tran.Commit();
				con.Close();
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler h=(GeoRabtaSite.RequestHandler)list[i];
					string Val=""+h.fieldno+"";	
					if(requestList.Contains(Val))
					{
						pendingList.Add(h);
					}
				}
				Session["pendingRequest"]=pendingList;
				for(int i=0;i<pendingList.Count;i++)
				{
					GeoRabtaSite.RequestHandler cR=(GeoRabtaSite.RequestHandler)pendingList[i];
					string fieldname=cR.fieldname;
					if(list.Contains(cR))
						list.Remove(cR);
				
				}
				//==================================Check Change in Request====================//
				if(list.Count<=0)
				{
					//Response.Write("<script>window.alert('There is no change in request(s). Thank You')</script>");
					this.lblCombine.Visible=true;
					this.lblCombine.Text="There is no change in request(s). Thank You";
					return;
				}

				string message="'<ul>Please attach/upload scan copy(ies) of following fields before sending your request";
				string messagefordis="Please attach/upload scan copy(ies) of following fields before sending your request \\n";
				string field="";
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler h=(GeoRabtaSite.RequestHandler)list[i];
					if(h.isDocRequired && h.docGUID=="")
					{
						field+="<li>"+h.fieldname+"</li>";
						messagefordis+=h.fieldname+"\\n";
					}
				}
				if(field.Length>0)
				{
					message+=field;
					message+="</ul>'";
					//Response.Write("<script>window.alert('"+messagefordis+"')</script>");
					this.lblCombine.Visible=true;
					this.lblCombine.Text=message;
					return;
				}
			
				con.Open();
				SqlTransaction trans=con.BeginTransaction();
				try
				{
					for(int i=0;i<list.Count;i++)
					{
						GeoRabtaSite.RequestHandler h=(GeoRabtaSite.RequestHandler)list[i];
						SqlCommand cmd=new SqlCommand("select timeline,replyatrequest from t_fieldsmgt where f_id="+h.fieldno+"",con);
						cmd.Transaction=trans;
						SqlDataReader rd=cmd.ExecuteReader();
						rd.Read();
						if(rd.HasRows)
						{
							int workingdays=Int32.Parse(rd[0].ToString());
							h.requesttimeline=DateTime.Now.Date.AddDays(workingdays).ToShortDateString();
							string replyatrequest=rd[1].ToString();
							rd.Close();
							cmd=new SqlCommand("insert into t_employeerequests(fieldno,fieldname,requestforchange,requisitecode,requestdate,requesttime,requesttimeline,requeststatus,documentID) values(@fieldno,@fieldname,@requestforchange,@requisitecode,getdate(),@requesttime,@requesttimeline,@requeststatus,@documentID)",con);
							cmd.Transaction=trans;
							SqlParameter _fieldno=new SqlParameter("@fieldno",SqlDbType.Int);
							SqlParameter _fieldname=new SqlParameter("@fieldname",SqlDbType.VarChar);
							SqlParameter _requestforchange=new SqlParameter("@requestforchange",SqlDbType.VarChar);
							SqlParameter _requisitecode=new SqlParameter("@requisitecode",SqlDbType.VarChar);
							//SqlParameter _requestdate=new SqlParameter("@requestdate",SqlDbType.DateTime);
							SqlParameter _requesttime=new SqlParameter("@requesttime",SqlDbType.VarChar);
							SqlParameter _requesttimeline=new SqlParameter("@requesttimeline",SqlDbType.DateTime);
							SqlParameter _requeststatus=new SqlParameter("@requeststatus",SqlDbType.Int);
							SqlParameter _documentID=new SqlParameter("@documentID",SqlDbType.Text);
							_fieldno.Value=h.fieldno;
							_fieldname.Value=h.fieldname;
							_requestforchange.Value=h.requestforchange;
							_requisitecode.Value=h.requisitecode;
							//_requestdate.Value=h.requestdate;
							_requesttime.Value=h.requesttime;
							_requesttimeline.Value=h.requesttimeline;
							_requeststatus.Value=h.requeststatus;
							if(h.isDocRequired)
							{
								Guid g=Guid.NewGuid();
								string filepath=Server.MapPath(@"tempEmployee\");
								string filename=g.ToString();
								if(h.fieldno==16)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpDateofBirth.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\16"+Path.GetExtension(lblfpDateofBirth.Text)),dPath);
									}
									else
									{
										this.fpDateofBirth.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpDateofBirth.PostedFile.FileName));
									}
								}
								if(h.fieldno==24)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpFatherName.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\24"+Path.GetExtension(lblfpFatherName.Text)),dPath);
									}
									else
									{
										this.fpFatherName.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpFatherName.PostedFile.FileName));
									}
								}
								if(h.fieldno==25)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpMaritalStatus.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\25"+Path.GetExtension(lblfpMaritalStatus.Text)),dPath);
									}
									else
									{
										this.fpMartialStatus.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpMartialStatus.PostedFile.FileName));
									}
								}
								if(h.fieldno==35)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpNationality.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\35"+Path.GetExtension(lblfpNationality.Text)),dPath);
									}
									else
									{
										this.fpNationality.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpNationality.PostedFile.FileName));
									}
								}
								if(h.fieldno==36)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpNationality2.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\36"+Path.GetExtension(lblfpNationality2.Text)),dPath);
									}
									else
									{
										this.fpNationality2.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpNationality2.PostedFile.FileName));
									}
								}
								if(h.fieldno==19)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpPassprot.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\19"+Path.GetExtension(lblfpPassprot.Text)),dPath);
									}
									else
									{
										this.fpPassport.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpPassport.PostedFile.FileName));
									}
								}
								if(h.fieldno==30)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpNextofKin.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\30"+Path.GetExtension(lblfpNextofKin.Text)),dPath);
									}
									else
									{
										this.fpNextofKin.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpNextofKin.PostedFile.FileName));
									}
								}
								if(h.fieldno==21)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpNICNew.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\21"+Path.GetExtension(lblfpNICNew.Text)),dPath);
									}
									else
									{
										this.fpNICNew.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpNICNew.PostedFile.FileName));
									}
								}
								if(h.fieldno==20)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpNICOld.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\20"+Path.GetExtension(lblfpNICOld.Text)),dPath);
									}
									else
									{
										this.fpNICOld.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpNICOld.PostedFile.FileName));
									}
								}
								if(h.fieldno==32)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpBankNo.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\32"+Path.GetExtension(lblfpBankNo.Text)),dPath);
									}
									else
									{
										this.fpBankNo.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpBankNo.PostedFile.FileName));
									}
								}
								if(h.fieldno==33)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpBankDetail.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\33"+Path.GetExtension(lblfpBankDetail.Text)),dPath);
									}
									else
									{
										this.fpBankDetail.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpBankDetail.PostedFile.FileName));
									}
								}
								_documentID.Value=g.ToString();
							}
							else
							{
								_documentID.Value=DBNull.Value;
							}
							cmd.Parameters.Add(_fieldno);
							cmd.Parameters.Add(_fieldname);
							cmd.Parameters.Add(_requestforchange);
							cmd.Parameters.Add(_requisitecode);
							//cmd.Parameters.Add(_requestdate);
							cmd.Parameters.Add(_requesttime);
							cmd.Parameters.Add(_requesttimeline);
							cmd.Parameters.Add(_requeststatus);
							cmd.Parameters.Add(_documentID);
							cmd.ExecuteNonQuery();
							Bulletin.PostMessage(con,trans,replyatrequest,"Geo Raabta",Session["user_id"].ToString(),1);
						}
						else
						{
							//Response.Write("Process flow not defined properly for request field"+h.fieldname+". Request failed to send");
							lblCombine.Visible=true;
							lblCombine.Text="Process flow not defined properly for request field"+h.fieldname+". Request failed to send";
							trans.Rollback();
							return;
						}
					}
					trans.Commit();
					con.Close();
					lblCombine.Visible=true;
					lblCombine.Text="Your request has been sent successfully. Please check your MY REQUEST tab for Requsition IDs";
					this.btnIgnore.Visible=false;
					this.btnResend.Visible=false;
					this.btnUpdate.Enabled=true;
				}
				catch(Exception ex)
				{
					Response.Write(ex.Message);
					trans.Rollback();
					con.Close();
				}
			}
		}

		private void lnName_Click(object sender, System.EventArgs e)
		{
			this.lnName.Visible=false;
			this.fpName.Disabled=false;
			this.lblfpName.Visible=false;
			File.Delete(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\7.jpg"));
			if(Session["cRequest"]=="")
			{
			 return;
			}
			else
			{
				ArrayList list =(ArrayList)Session["cRequest"];
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler rf=(GeoRabtaSite.RequestHandler)list[i];
					if(rf.fieldno==7)
					{
						rf.docGUID="";
						return;
					}
				}
			}
		}

		private void lnDesignation_Click(object sender, System.EventArgs e)
		{
			//this.lnDesignation.Visible=false;
			//this.fpDesignation.Disabled=false;
			//this.lblfpDesignation.Visible=false;
			File.Delete(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\6.jpg"));
			if(Session["cRequest"]=="")
			{
				return;
			}
			else
			{
				ArrayList list =(ArrayList)Session["cRequest"];
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler rf=(GeoRabtaSite.RequestHandler)list[i];
					if(rf.fieldno==6)
					{
						rf.docGUID="";
						return;
					}
				}
			}
		}

		private void lnDepartment_Click(object sender, System.EventArgs e)
		{
			//this.lnDepartment.Visible=false;
			//this.fpDepartment.Disabled=false;
			//this.lblfpDepartment.Visible=false;
			File.Delete(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\10.jpg"));
			if(Session["cRequest"]=="")
			{
				return;
			}
			else
			{
				ArrayList list =(ArrayList)Session["cRequest"];
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler rf=(GeoRabtaSite.RequestHandler)list[i];
					if(rf.fieldno==10)
					{
						rf.docGUID="";
						return;
					}
				}
			}
		}

		private void lnStation_Click(object sender, System.EventArgs e)
		{
			//this.lnStation.Visible=false;
			//this.fpStation.Disabled=false;
			//this.lblfpStation.Visible=false;
			File.Delete(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\8.jpg"));
			if(Session["cRequest"]=="")
			{
				return;
			}
			else
			{
				ArrayList list =(ArrayList)Session["cRequest"];
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler rf=(GeoRabtaSite.RequestHandler)list[i];
					if(rf.fieldno==8)
					{
						rf.docGUID="";
						return;
					}
				}
			}
		}

		private void lnEOBI_Click(object sender, System.EventArgs e)
		{
			this.lnEOBI.Visible=false;
			this.fpEOBI.Disabled=false;
			this.lblfpEOBI.Visible=false;
			File.Delete(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\13.jpg"));
			if(Session["cRequest"]=="")
			{
				return;
			}
			else
			{
				ArrayList list =(ArrayList)Session["cRequest"];
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler rf=(GeoRabtaSite.RequestHandler)list[i];
					if(rf.fieldno==13)
					{
						rf.docGUID="";
						return;
					}
				}
			}
		}

		private void lnDateofBirth_Click(object sender, System.EventArgs e)
		{
			this.lnDateofBirth.Visible=false;
			this.fpDateofBirth.Disabled=false;
			this.lblfpDateofBirth.Visible=false;
			File.Delete(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\16.jpg"));
			if(Session["cRequest"]=="")
			{
				return;
			}
			else
			{
				ArrayList list =(ArrayList)Session["cRequest"];
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler rf=(GeoRabtaSite.RequestHandler)list[i];
					if(rf.fieldno==16)
					{
						rf.docGUID="";
						return;
					}
				}
			}
		}

		private void lnFatherName_Click(object sender, System.EventArgs e)
		{
			this.lnFatherName.Visible=false;
			this.fpFatherName.Disabled=false;
			this.lblfpFatherName.Visible=false;
			File.Delete(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\24.jpg"));
			if(Session["cRequest"]=="")
			{
				return;
			}
			else
			{
				ArrayList list =(ArrayList)Session["cRequest"];
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler rf=(GeoRabtaSite.RequestHandler)list[i];
					if(rf.fieldno==24)
					{
						rf.docGUID="";
						return;
					}
				}
			}
		}

		private void lnMartialStatus_Click(object sender, System.EventArgs e)
		{
			this.lnMartialStatus.Visible=false;
			this.fpMartialStatus.Disabled=false;
			this.lblfpMaritalStatus.Visible=false;
			File.Delete(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\25.jpg"));
			if(Session["cRequest"]=="")
			{
				return;
			}
			else
			{
				ArrayList list =(ArrayList)Session["cRequest"];
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler rf=(GeoRabtaSite.RequestHandler)list[i];
					if(rf.fieldno==25)
					{
						rf.docGUID="";
						return;
					}
				}
			}
		}

		private void lnNationality_Click(object sender, System.EventArgs e)
		{
			this.lnNationality.Visible=false;
			this.fpNationality.Disabled=false;
			this.lblfpNationality.Visible=false;
			File.Delete(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\35.jpg"));
			if(Session["cRequest"]=="")
			{
				return;
			}
			else
			{
				ArrayList list =(ArrayList)Session["cRequest"];
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler rf=(GeoRabtaSite.RequestHandler)list[i];
					if(rf.fieldno==35)
					{
						rf.docGUID="";
						return;
					}
				}
			}
		}

		private void lnNationality2_Click(object sender, System.EventArgs e)
		{
			this.lnNationality2.Visible=false;
			this.fpNationality2.Disabled=false;
			this.lblfpNationality2.Visible=false;
			File.Delete(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\36.jpg"));
			if(Session["cRequest"]=="")
			{
				return;
			}
			else
			{
				ArrayList list =(ArrayList)Session["cRequest"];
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler rf=(GeoRabtaSite.RequestHandler)list[i];
					if(rf.fieldno==36)
					{
						rf.docGUID="";
						return;
					}
				}
			}
		}

		private void lnPassport_Click(object sender, System.EventArgs e)
		{
			this.lnPassport.Visible=false;
			this.fpPassport.Disabled=false;
			this.lblfpPassprot.Visible=false;
			File.Delete(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\19.jpg"));
			if(Session["cRequest"].ToString()=="")
			{
				return;
			}
			else
			{
				ArrayList list =(ArrayList)Session["cRequest"];
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler rf=(GeoRabtaSite.RequestHandler)list[i];
					if(rf.fieldno==19)
					{
						rf.docGUID="";
						return;
					}
				}
			}
		}

		private void lnNextofKin_Click(object sender, System.EventArgs e)
		{
			this.lnNextofKin.Visible=false;
			this.fpNextofKin.Disabled=false;
			this.lblfpNextofKin.Visible=false;
			File.Delete(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\30.jpg"));
			if(Session["cRequest"]=="")
			{
				return;
			}
			else
			{
				ArrayList list =(ArrayList)Session["cRequest"];
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler rf=(GeoRabtaSite.RequestHandler)list[i];
					if(rf.fieldno==30)
					{
						rf.docGUID="";
						return;
					}
				}
			}
		}

		private void lnNICNew_Click(object sender, System.EventArgs e)
		{
			this.lnNICNew.Visible=false;
			this.fpNICNew.Disabled=false;
			this.lblfpNICNew.Visible=false;
			File.Delete(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\21.jpg"));
			if(Session["cRequest"]=="")
			{
				return;
			}
			else
			{
				ArrayList list =(ArrayList)Session["cRequest"];
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler rf=(GeoRabtaSite.RequestHandler)list[i];
					if(rf.fieldno==21)
					{
						rf.docGUID="";
						return;
					}
				}
			}
		}

		private void lnNICOld_Click(object sender, System.EventArgs e)
		{
			this.lnNICOld.Visible=false;
			this.fpNICOld.Disabled=false;
			this.lblfpNICOld.Visible=false;
			File.Delete(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\20.jpg"));
			if(Session["cRequest"]=="")
			{
				return;
			}
			else
			{
				ArrayList list =(ArrayList)Session["cRequest"];
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler rf=(GeoRabtaSite.RequestHandler)list[i];
					if(rf.fieldno==20)
					{
						rf.docGUID="";
						return;
					}
				}
			}
		}

		private void lnBankNo_Click(object sender, System.EventArgs e)
		{
			this.lnBankNo.Visible=false;
			this.fpBankNo.Disabled=false;
			this.lblfpBankNo.Visible=false;
			File.Delete(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\32.jpg"));
			if(Session["cRequest"]=="")
			{
				return;
			}
			else
			{
				ArrayList list =(ArrayList)Session["cRequest"];
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler rf=(GeoRabtaSite.RequestHandler)list[i];
					if(rf.fieldno==32)
					{
						rf.docGUID="";
						return;
					}
				}
			}
		}

		private void lnBankDetail_Click(object sender, System.EventArgs e)
		{
			this.lnBankDetail.Visible=false;
			this.fpBankDetail.Disabled=false;
			this.lblfpBankDetail.Visible=false;
			File.Delete(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\33.jpg"));
			if(Session["cRequest"]=="")
			{
				return;
			}
			else
			{
				ArrayList list =(ArrayList)Session["cRequest"];
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler rf=(GeoRabtaSite.RequestHandler)list[i];
					if(rf.fieldno==33)
					{
						rf.docGUID="";
						return;
					}
				}
			}
		}

		private void btnResend_Click(object sender, System.EventArgs e)
		{
			//=======================================================//
			if(pnlEmployeeInfo.Visible)
			{
				//ArrayList list=(ArrayList)Session["cRequest"];
				//ArrayList pendingList=(ArrayList)Session["pendingRequest"];
				ArrayList list=new ArrayList();
				if(CheckRequest(this.lblEmpInfoName.Text,this.txtEmpInfoName.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=7;
					h.fieldname="Name";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtEmpInfoName.Text;
					h.requestforchangeID.Value=DBNull.Value;
					if(lnName.Visible)
					{
						h.docGUID=lblfpName.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpName.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
				if(CheckRequest(this.lblEmpInfoDesignation.Text,this.ddDesignation.SelectedItem.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=6;
					h.fieldname="Designation";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.ddDesignation.SelectedItem.Text;
					h.requestforchangeID.Value=this.ddDesignation.SelectedItem.Value;
					/*if(lnDesignation.Visible)
					{
						h.docGUID=lblfpDesignation.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpDesignation.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);*/

					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(CheckRequest(this.lnlEmpInfoDept.Text,this.ddDepartment.SelectedItem.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=10;
					h.fieldname="Department";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.ddDepartment.SelectedItem.Text;
					h.requestforchangeID.Value=this.ddDesignation.SelectedItem.Value;
					/*if(lnDepartment.Visible)
					{
						h.docGUID=lblfpDepartment.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpDepartment.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);*/

					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(CheckRequest(this.lblEmpInfoCity.Text,this.ddStation.SelectedItem.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=8;
					h.fieldname="Station";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.ddStation.SelectedItem.Text;
					h.requestforchangeID.Value=this.ddStation.SelectedItem.Value;
					/*if(lnStation.Visible)
					{
						h.docGUID=lblfpStation.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpStation.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);*/
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(CheckRequest(this.lblEmpInfoDOJ.Text,this.CalenderDOJ.SelectedDate.ToString("dd MMM,yyyy")))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=9;
					h.fieldname="Date of Joining";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.CalenderDOJ.SelectedDate.ToString("dd MMM,yyyy");
					h.requestforchangeID.Value=this.CalenderDOJ.SelectedDate.ToString("d MMM,yyyy");
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(CheckRequest(this.lblEmpInfoExtension.Text,this.txtEmpInfoExtension.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=11;
					h.fieldname="Extension";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtEmpInfoExtension.Text;
					h.requestforchangeID.Value=DBNull.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(CheckRequest(this.lblEmpInfoEmail.Text,this.txtEmpInfoEmail.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=31;
					h.fieldname="Email (Official)";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtEmpInfoEmail.Text;
					h.requestforchangeID.Value=DBNull.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(CheckRequest(this.lblEmpInfoSessi.Text,this.txtEmpInfoSESSI.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=12;
					h.fieldname="SESSI #";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtEmpInfoSESSI.Text;
					h.requestforchangeID.Value=DBNull.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(CheckRequest(this.lblEmpInfoEObi.Text,this.txtEmpInfoEOBI.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=13;
					h.fieldname="EOBI #";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtEmpInfoEOBI.Text;
					h.requestforchangeID.Value=DBNull.Value;
					if(lnEOBI.Visible)
					{
						h.docGUID=lblfpEOBI.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpEOBI.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
				if(CheckRequest(this.lblEmpLocation.Text,this.txtEmpLocation.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=14;
					h.fieldname="Workstation Address";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtEmpLocation.Text;
					h.requestforchangeID.Value=DBNull.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				Session["cRequest"]=list;
				ArrayList requestList=new ArrayList();
				ArrayList pendingList=new ArrayList();
				ArrayList removeList=(ArrayList)Session["rRequest"];
				for(int i=0;i<removeList.Count;i++)
				{
					RequestHandler r=(GeoRabtaSite.RequestHandler)removeList[i];
					for(int z=0;z<list.Count;z++)
					{
						RequestHandler Or=(GeoRabtaSite.RequestHandler)list[z];
						if(r.fieldno.Equals(Or.fieldno))
						{
							list.RemoveAt(z);
						}
					}
				}
				con.Open();
				SqlTransaction tran=con.BeginTransaction();
				SqlCommand conQuery=new SqlCommand("select * from t_employeerequests where requisitecode='"+Session["user_id"].ToString()+"' and requeststatus=1",con);
				conQuery.Transaction=tran;
				SqlDataReader conrd=conQuery.ExecuteReader();
				while(conrd.Read())
				{
					requestList.Add(conrd[2].ToString());
				}
				conrd.Close();
				tran.Commit();
				con.Close();
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler h=(GeoRabtaSite.RequestHandler)list[i];
					string Val=""+h.fieldno+"";	
					if(requestList.Contains(Val))
					{
						pendingList.Add(h);
					}
				}
				Session["pendingRequest"]=pendingList;
				/*for(int i=0;i<pendingList.Count;i++)
				{
					GeoRabtaSite.RequestHandler cR=(GeoRabtaSite.RequestHandler)pendingList[i];
					string fieldname=cR.fieldname;
					if(list.Contains(cR))
						list.Remove(cR);
				
				}*/
				//==================================Check Change in Request====================//
				if(list.Count<=0)
				{
					//Response.Write("<script>window.alert('There is no change in request(s). Thank You')</script>");
					this.lblCombine.Visible=true;
					this.lblCombine.Text="There is no change in request(s). Thank You";
					return;
				}

				string message="'<ul>Please attach/upload scan copy(ies) of following fields before sending your request";
				string messagefordis="Please attach/upload scan copy(ies) of following fields before sending your request \\n";
				string field="";
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler h=(GeoRabtaSite.RequestHandler)list[i];
					if(h.isDocRequired && h.docGUID=="")
					{
						field+="<li>"+h.fieldname+"</li>";
						messagefordis+=h.fieldname+"\\n";
					}
				}
				if(field.Length>0)
				{
					message+=field;
					message+="</ul>'";
					//Response.Write("<script>window.alert('"+messagefordis+"')</script>");
					this.lblCombine.Visible=true;
					this.lblCombine.Text=message;
					return;
				}
			    
				con.Open();
				SqlTransaction trans=con.BeginTransaction();
				try
				{
					for(int j=0;j<pendingList.Count;j++)
					{
					 GeoRabtaSite.RequestHandler h=(GeoRabtaSite.RequestHandler)pendingList[j];
					 SqlCommand cmd=new SqlCommand("update t_employeerequests set requeststatus=2 where requisitecode='"+Session["user_id"].ToString()+"' and requeststatus=1 and fieldno="+h.fieldno+"",con);
					 cmd.Transaction=trans;
					 cmd.ExecuteNonQuery();
					}
					for(int i=0;i<list.Count;i++)
					{
						GeoRabtaSite.RequestHandler h=(GeoRabtaSite.RequestHandler)list[i];
						SqlCommand cmd=new SqlCommand("select timeline,replyatrequest from t_fieldsmgt where f_id="+h.fieldno+"",con);
						cmd.Transaction=trans;
						SqlDataReader rd=cmd.ExecuteReader();
						rd.Read();
						if(rd.HasRows)
						{
							int workingdays=Int32.Parse(rd[0].ToString());
							h.requesttimeline=DateTime.Now.Date.AddDays(workingdays).ToShortDateString();
							string replyatrequest=rd[1].ToString();
							rd.Close();
							cmd=new SqlCommand("insert into t_employeerequests(fieldno,fieldname,requestforchange,requisitecode,requestdate,requesttime,requesttimeline,requeststatus,documentID) values(@fieldno,@fieldname,@requestforchange,@requisitecode,getdate(),@requesttime,@requesttimeline,@requeststatus,@documentID)",con);
							cmd.Transaction=trans;
							SqlParameter _fieldno=new SqlParameter("@fieldno",SqlDbType.Int);
							SqlParameter _fieldname=new SqlParameter("@fieldname",SqlDbType.VarChar);
							SqlParameter _requestforchange=new SqlParameter("@requestforchange",SqlDbType.VarChar);
							SqlParameter _requisitecode=new SqlParameter("@requisitecode",SqlDbType.VarChar);
							//SqlParameter _requestdate=new SqlParameter("@requestdate",SqlDbType.DateTime);
							SqlParameter _requesttime=new SqlParameter("@requesttime",SqlDbType.VarChar);
							SqlParameter _requesttimeline=new SqlParameter("@requesttimeline",SqlDbType.DateTime);
							SqlParameter _requeststatus=new SqlParameter("@requeststatus",SqlDbType.Int);
							SqlParameter _documentID=new SqlParameter("@documentID",SqlDbType.Text);
							_fieldno.Value=h.fieldno;
							_fieldname.Value=h.fieldname;
							_requestforchange.Value=h.requestforchange;
							_requisitecode.Value=h.requisitecode;
							//_requestdate.Value=h.requestdate;
							_requesttime.Value=h.requesttime;
							_requesttimeline.Value=h.requesttimeline;
							_requeststatus.Value=h.requeststatus;
							if(h.isDocRequired)
							{
								Guid g=Guid.NewGuid();
								string filepath=Server.MapPath(@"tempEmployee\");
								string filename=g.ToString();
								if(h.fieldno==7)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpName.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\7"+Path.GetExtension(lblfpName.Text)),dPath);
									}
									else
									{
										this.fpName.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpName.PostedFile.FileName));
									}
								}
								/*if(h.fieldno==6)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpDesignation.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\6"+Path.GetExtension(lblfpDesignation.Text)),dPath);
									}
									else
									{
										this.fpDesignation.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpDesignation.PostedFile.FileName));
									}
								}*/
								/*if(h.fieldno==10)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpDepartment.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\10"+Path.GetExtension(lblfpDepartment.Text)),dPath);
									}
									else
									{
										this.fpDepartment.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpDepartment.PostedFile.FileName));
									}
								}*/
								/*if(h.fieldno==8)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpStation.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\8"+Path.GetExtension(lblfpStation.Text)),dPath);
									}
									else
									{
										this.fpStation.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpStation.PostedFile.FileName));
									}
								}*/
								if(h.fieldno==13)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpEOBI.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\13"+Path.GetExtension(lblfpEOBI.Text)),dPath);
									}
									else
									{
										this.fpEOBI.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpEOBI.PostedFile.FileName));
									}
								}
								_documentID.Value=g.ToString();
							}
							else
							{
								_documentID.Value=DBNull.Value;
							}
							cmd.Parameters.Add(_fieldno);
							cmd.Parameters.Add(_fieldname);
							cmd.Parameters.Add(_requestforchange);
							cmd.Parameters.Add(_requisitecode);
							//cmd.Parameters.Add(_requestdate);
							cmd.Parameters.Add(_requesttime);
							cmd.Parameters.Add(_requesttimeline);
							cmd.Parameters.Add(_requeststatus);
							cmd.Parameters.Add(_documentID);
							cmd.ExecuteNonQuery();
							Bulletin.PostMessage(con,trans,replyatrequest,"Geo Raabta",Session["user_id"].ToString(),1);
						}
						else
						{
							//Response.Write("Process flow not defined properly for request field"+h.fieldname+". Request failed to send");
							lblCombine.Visible=true;
							lblCombine.Text="Process flow not defined properly for request field"+h.fieldname+". Request failed to send";
							trans.Rollback();
							return;
						}
					}
					trans.Commit();
					con.Close();
					lblCombine.Visible=true;
					lblCombine.Text="Your request has been sent successfully. Please check your MY REQUEST tab for Requsition IDs";
					this.btnIgnore.Visible=false;
					this.btnResend.Visible=false;
					this.btnUpdate.Enabled=true;
				}
				catch(Exception ex)
				{
					Response.Write(ex.Message);
					trans.Rollback();
					con.Close();
				}
			}
			if(pnlPersonalUPdate.Visible)
			{
				ArrayList list=new ArrayList();
				if(CheckRequest(this.lblName.Text,this.calendarDOB.SelectedDate.ToString("dd MMM,yyyy")))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=16;
					h.fieldname="Date of Birth";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.calendarDOB.SelectedDate.ToString("dd MMM,yyyy");
					h.requestforchangeID.Value=this.calendarDOB.SelectedDate.ToString("d MMM,yyyy");
					if(lnDateofBirth.Visible)
					{
						h.docGUID=lblfpDateofBirth.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpDateofBirth.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
				if(CheckRequest(this.lblFName.Text,this.txtFName.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=24;
					h.fieldname="Father Name";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtFName.Text;
					h.requestforchangeID.Value=DBNull.Value;
					if(lnFatherName.Visible)
					{
						h.docGUID=lblfpFatherName.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpFatherName.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
				if(CheckRequest(this.lblGender.Text,this.RadioButtonList1.SelectedItem.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=17;
					h.fieldname="Gender";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.RadioButtonList1.SelectedItem.Text;
					h.requestforchangeID.Value=this.RadioButtonList1.SelectedItem.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(this.ddBloodGrp.SelectedValue=="0")
				{
					string bloodGrp="";
					if(CheckRequest(this.lblBloodGrp.Text,bloodGrp))
					{
						GeoRabtaSite.RequestHandler h=new RequestHandler();
						h.fieldno=18;
						h.fieldname="Blood Group";
						h.requestdate=DateTime.Now.Date.ToShortDateString();
						h.requeststatus=1;
						h.requesttime=DateTime.Now.TimeOfDay.ToString();
						h.requisitecode=Session["user_id"].ToString();
						h.requestforchange=ddBloodGrp.SelectedItem.Text;
						h.requestforchangeID.Value=this.ddBloodGrp.SelectedItem.Value;
						h.isPosted=false;
						h.isDocRequired=false;
						list.Add(h);
					}
				}
				else
				{
					string bloodGrp=this.ddBloodGrp.SelectedItem.Text;
					if(CheckRequest(this.lblBloodGrp.Text,bloodGrp))
					{
						GeoRabtaSite.RequestHandler h=new RequestHandler();
						h.fieldno=18;
						h.fieldname="Blood Group";
						h.requestdate=DateTime.Now.Date.ToShortDateString();
						h.requeststatus=1;
						h.requesttime=DateTime.Now.TimeOfDay.ToString();
						h.requisitecode=Session["user_id"].ToString();
						h.requestforchange=ddBloodGrp.SelectedItem.Text;
						h.requestforchangeID.Value=this.ddBloodGrp.SelectedItem.Value;
						h.isPosted=false;
						h.isDocRequired=false;
						list.Add(h);
					}
				}
				if(this.ddReligion.SelectedValue=="0")
				{
					string Religion="";
					if(CheckRequest(this.lblReligion.Text,Religion))
					{
						GeoRabtaSite.RequestHandler h=new RequestHandler();
						h.fieldno=26;
						h.fieldname="Religion";
						h.requestdate=DateTime.Now.Date.ToShortDateString();
						h.requeststatus=1;
						h.requesttime=DateTime.Now.TimeOfDay.ToString();
						h.requisitecode=Session["user_id"].ToString();
						h.requestforchange=ddReligion.SelectedItem.Text;
						h.requestforchangeID.Value=ddReligion.SelectedItem.Value;
						h.isPosted=false;
						h.isDocRequired=false;
						list.Add(h);
					}
				}
				else
				{
					string Religion=this.ddReligion.SelectedItem.Text;
					if(CheckRequest(this.lblReligion.Text,Religion))
					{
						GeoRabtaSite.RequestHandler h=new RequestHandler();
						h.fieldno=26;
						h.fieldname="Religion";
						h.requestdate=DateTime.Now.Date.ToShortDateString();
						h.requeststatus=1;
						h.requesttime=DateTime.Now.TimeOfDay.ToString();
						h.requisitecode=Session["user_id"].ToString();
						h.requestforchange=ddReligion.SelectedItem.Text;
						h.requestforchangeID.Value=ddReligion.SelectedItem.Value;
						h.isPosted=false;
						h.isDocRequired=false;
						list.Add(h);
					}
				}
				if(CheckRequest(this.lblNick.Text,this.txtNick.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=37;
					h.fieldname="Nick";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=txtNick.Text;
					h.requestforchangeID.Value=DBNull.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(CheckRequest(this.lblAddress.Text,this.txtAddress.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=22;
					h.fieldname="Address";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=txtAddress.Text;
					h.requestforchangeID.Value=DBNull.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(this.ddStatus.SelectedValue=="0")
				{
					string Status="";
					if(CheckRequest(this.lblMaritialStat.Text,Status))
					{
						GeoRabtaSite.RequestHandler h=new RequestHandler();
						h.fieldno=25;
						h.fieldname="Marital Status";
						h.requestdate=DateTime.Now.Date.ToShortDateString();
						h.requeststatus=1;
						h.requesttime=DateTime.Now.TimeOfDay.ToString();
						h.requisitecode=Session["user_id"].ToString();
						h.requestforchange=this.ddStatus.SelectedItem.Text;
						h.requestforchangeID.Value=this.ddStatus.SelectedItem.Value;
						if(lnMartialStatus.Visible)
						{
							h.docGUID=lblfpMaritalStatus.Text;
							h.isPosted=true;
						}
						else
						{
							h.docGUID=this.fpMartialStatus.PostedFile.FileName;
							h.isPosted=false;
						}
						h.isDocRequired=true;
						list.Add(h);
					}
				}
				else
				{
					string Status=this.ddStatus.SelectedItem.Text;
					if(CheckRequest(this.lblMaritialStat.Text,Status))
					{
						GeoRabtaSite.RequestHandler h=new RequestHandler();
						h.fieldno=25;
						h.fieldname="Marital Status";
						h.requestdate=DateTime.Now.Date.ToShortDateString();
						h.requeststatus=1;
						h.requesttime=DateTime.Now.TimeOfDay.ToString();
						h.requisitecode=Session["user_id"].ToString();
						h.requestforchange=this.ddStatus.SelectedItem.Text;
						h.requestforchangeID.Value=this.ddStatus.SelectedItem.Value;
						if(lnMartialStatus.Visible)
						{
							h.docGUID=lblfpMaritalStatus.Text;
							h.isPosted=true;
						}
						else
						{
							h.docGUID=this.fpMartialStatus.PostedFile.FileName;
							h.isPosted=false;
						}
						h.isDocRequired=true;
						list.Add(h);
					}
				}
				if(CheckRequest(this.lblEmail.Text,this.txtEmail.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=29;
					h.fieldname="Email (Personal)";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=txtEmail.Text;
					h.requestforchangeID.Value=DBNull.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(CheckRequest(this.lblNTN.Text,this.txtNTN.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=34;
					h.fieldname="NTN #";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=txtNTN.Text;
					h.requestforchangeID.Value=DBNull.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(CheckRequest(this.lblcontactNo.Text,this.txtContactNo.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=27;
					h.fieldname="Residential #";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=txtContactNo.Text;
					h.requestforchangeID.Value=DBNull.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(CheckRequest(this.lblMobileNo.Text,this.txtMobile.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=28;
					h.fieldname="Mobile #";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=txtMobile.Text;
					h.requestforchangeID.Value=DBNull.Value;
					h.isPosted=false;
					h.isDocRequired=false;
					list.Add(h);
				}
				if(this.ddNationality.SelectedValue=="0")
				{
					string Nationality="";
					if(CheckRequest(this.lblNationality.Text,Nationality))
					{
						GeoRabtaSite.RequestHandler h=new RequestHandler();
						h.fieldno=35;
						h.fieldname="Nationality";
						h.requestdate=DateTime.Now.Date.ToShortDateString();
						h.requeststatus=1;
						h.requesttime=DateTime.Now.TimeOfDay.ToString();
						h.requisitecode=Session["user_id"].ToString();
						h.requestforchange=this.ddNationality.SelectedItem.Text;
						h.requestforchangeID.Value=this.ddNationality.SelectedItem.Value;
						if(lnNationality.Visible)
						{
							h.docGUID=lblfpNationality.Text;
							h.isPosted=true;
						}
						else
						{
							h.docGUID=this.fpNationality.PostedFile.FileName;
							h.isPosted=false;
						}
						h.isDocRequired=true;
						list.Add(h);
					}
				}
				else
				{
					string Nationality=this.ddNationality.SelectedItem.Text;
					if(CheckRequest(this.lblNationality.Text,Nationality))
					{
						GeoRabtaSite.RequestHandler h=new RequestHandler();
						h.fieldno=35;
						h.fieldname="Nationality";
						h.requestdate=DateTime.Now.Date.ToShortDateString();
						h.requeststatus=1;
						h.requesttime=DateTime.Now.TimeOfDay.ToString();
						h.requisitecode=Session["user_id"].ToString();
						h.requestforchange=this.ddNationality.SelectedItem.Text;
						h.requestforchangeID.Value=this.ddNationality.SelectedItem.Value;
						if(lnNationality.Visible)
						{
							h.docGUID=lblfpNationality.Text;
							h.isPosted=true;
						}
						else
						{
							h.docGUID=this.fpNationality.PostedFile.FileName;
							h.isPosted=false;
						}
						h.isDocRequired=true;
						list.Add(h);
					}
				}
				if(this.ddNationality2.SelectedValue=="0")
				{
					string Nationality2="";
					if(CheckRequest(this.lblNationality2.Text,Nationality2))
					{
						GeoRabtaSite.RequestHandler h=new RequestHandler();
						h.fieldno=36;
						h.fieldname="Nationality (Secondary)";
						h.requestdate=DateTime.Now.Date.ToShortDateString();
						h.requeststatus=1;
						h.requesttime=DateTime.Now.TimeOfDay.ToString();
						h.requisitecode=Session["user_id"].ToString();
						h.requestforchange=this.ddNationality2.SelectedItem.Text;
						h.requestforchangeID.Value=this.ddNationality2.SelectedItem.Value;
						if(lnNationality2.Visible)
						{
							h.docGUID=lblfpNationality2.Text;
							h.isPosted=true;
						}
						else
						{
							h.docGUID=this.fpNationality2.PostedFile.FileName;
							h.isPosted=false;
						}
						h.isDocRequired=true;
						list.Add(h);
					}
				}
				else
				{
					string Nationality2=this.ddNationality2.SelectedItem.Text;
					if(CheckRequest(this.lblNationality2.Text,Nationality2))
					{
						GeoRabtaSite.RequestHandler h=new RequestHandler();
						h.fieldno=36;
						h.fieldname="Nationality (Secondary)";
						h.requestdate=DateTime.Now.Date.ToShortDateString();
						h.requeststatus=1;
						h.requesttime=DateTime.Now.TimeOfDay.ToString();
						h.requisitecode=Session["user_id"].ToString();
						h.requestforchange=this.ddNationality2.SelectedItem.Text;
						h.requestforchangeID.Value=this.ddNationality2.SelectedItem.Value;
						if(lnNationality2.Visible)
						{
							h.docGUID=lblfpNationality2.Text;
							h.isPosted=true;
						}
						else
						{
							h.docGUID=this.fpNationality2.PostedFile.FileName;
							h.isPosted=false;
						}
						h.isDocRequired=true;
						list.Add(h);
					}
				}
				if(CheckRequest(this.lblPassport.Text,this.txtPassport.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=19;
					h.fieldname="Passport #";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtPassport.Text;
					h.requestforchangeID.Value=DBNull.Value;
					if(lnPassport.Visible)
					{
						h.docGUID=lblfpPassprot.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpPassport.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
				if(CheckRequest(this.lblKin.Text,this.txtKin.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=30;
					h.fieldname="Next of Kin";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtKin.Text;
					h.requestforchangeID.Value=DBNull.Value;
					if(lnNextofKin.Visible)
					{
						h.docGUID=lblfpNextofKin.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpNextofKin.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
				if(CheckRequest(this.lblNICNew.Text,this.txtNewNic.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=21;
					h.fieldname="NIC (New)";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtNewNic.Text;
					h.requestforchangeID.Value=DBNull.Value;
					if(lnNICNew.Visible)
					{
						h.docGUID=lblfpNICNew.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpNICNew.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
				if(CheckRequest(this.lblNICOld.Text,this.txtOldNic.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=20;
					h.fieldname="NIC (Old)";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtOldNic.Text;
					h.requestforchangeID.Value=DBNull.Value;
					if(lnNICOld.Visible)
					{
						h.docGUID=lblfpNICOld.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpNICOld.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
				if(CheckRequest(this.lblBankAcctNo.Text,this.txtBankAcctNo.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=32;
					h.fieldname="Bank Account #";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtBankAcctNo.Text;
					h.requestforchangeID.Value=DBNull.Value;
					if(lnBankNo.Visible)
					{
						h.docGUID=lblfpBankNo.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpBankNo.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
				if(CheckRequest(this.lblAccountDetails.Text,this.txtAccountDetails.Text))
				{
					GeoRabtaSite.RequestHandler h=new RequestHandler();
					h.fieldno=33;
					h.fieldname="Bank Account Details";
					h.requestdate=DateTime.Now.Date.ToShortDateString();
					h.requeststatus=1;
					h.requesttime=DateTime.Now.TimeOfDay.ToString();
					h.requisitecode=Session["user_id"].ToString();
					h.requestforchange=this.txtAccountDetails.Text;
					h.requestforchangeID.Value=DBNull.Value;
					if(lnBankDetail.Visible)
					{
						h.docGUID=lblfpBankDetail.Text;
						h.isPosted=true;
					}
					else
					{
						h.docGUID=this.fpBankDetail.PostedFile.FileName;
						h.isPosted=false;
					}
					h.isDocRequired=true;
					list.Add(h);
				}
				Session["cRequest"]=list;
				ArrayList requestList=new ArrayList();
				ArrayList pendingList=new ArrayList();
				ArrayList removeList=(ArrayList)Session["rpRequest"];
				for(int i=0;i<removeList.Count;i++)
				{
					RequestHandler r=(GeoRabtaSite.RequestHandler)removeList[i];
					for(int z=0;z<list.Count;z++)
					{
						RequestHandler Or=(GeoRabtaSite.RequestHandler)list[z];
						if(r.fieldno.Equals(Or.fieldno))
						{
							list.RemoveAt(z);
						}
					}
				}
				con.Open();
				SqlTransaction tran=con.BeginTransaction();
				SqlCommand conQuery=new SqlCommand("select * from t_employeerequests where requisitecode='"+Session["user_id"].ToString()+"' and requeststatus=1",con);
				conQuery.Transaction=tran;
				SqlDataReader conrd=conQuery.ExecuteReader();
				while(conrd.Read())
				{
					requestList.Add(conrd[2].ToString());
				}
				conrd.Close();
				tran.Commit();
				con.Close();
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler h=(GeoRabtaSite.RequestHandler)list[i];
					string Val=""+h.fieldno+"";	
					if(requestList.Contains(Val))
					{
						pendingList.Add(h);
					}
				}
				Session["pendingRequest"]=pendingList;
				/*for(int i=0;i<pendingList.Count;i++)
				{
					GeoRabtaSite.RequestHandler cR=(GeoRabtaSite.RequestHandler)pendingList[i];
					string fieldname=cR.fieldname;
					if(list.Contains(cR))
						list.Remove(cR);
				
				}*/
				//==================================Check Change in Request====================//
				if(list.Count<=0)
				{
					//Response.Write("<script>window.alert('There is no change in request(s). Thank You')</script>");
					this.lblCombine.Visible=true;
					this.lblCombine.Text="There is no change in request(s). Thank You";
					return;
				}

				string message="'<ul>Please attach/upload scan copy(ies) of following fields before sending your request";
				string messagefordis="Please attach/upload scan copy(ies) of following fields before sending your request \\n";
				string field="";
				for(int i=0;i<list.Count;i++)
				{
					GeoRabtaSite.RequestHandler h=(GeoRabtaSite.RequestHandler)list[i];
					if(h.isDocRequired && h.docGUID=="")
					{
						field+="<li>"+h.fieldname+"</li>";
						messagefordis+=h.fieldname+"\\n";
					}
				}
				if(field.Length>0)
				{
					message+=field;
					message+="</ul>'";
					//Response.Write("<script>window.alert('"+messagefordis+"')</script>");
					this.lblCombine.Visible=true;
					this.lblCombine.Text=message;
					return;
				}
			
				con.Open();
				SqlTransaction trans=con.BeginTransaction();
				try
				{
					for(int j=0;j<pendingList.Count;j++)
					{
						GeoRabtaSite.RequestHandler h=(GeoRabtaSite.RequestHandler)pendingList[j];
						SqlCommand cmd=new SqlCommand("update t_employeerequests set requeststatus=2 where requisitecode='"+Session["user_id"].ToString()+"' and requeststatus=1 and fieldno="+h.fieldno+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
					}
					for(int i=0;i<list.Count;i++)
					{
						GeoRabtaSite.RequestHandler h=(GeoRabtaSite.RequestHandler)list[i];
						SqlCommand cmd=new SqlCommand("select timeline,replyatrequest from t_fieldsmgt where f_id="+h.fieldno+"",con);
						cmd.Transaction=trans;
						SqlDataReader rd=cmd.ExecuteReader();
						rd.Read();
						if(rd.HasRows)
						{
							int workingdays=Int32.Parse(rd[0].ToString());
							h.requesttimeline=DateTime.Now.Date.AddDays(workingdays).ToShortDateString();
							string replyatrequest=rd[1].ToString();
							rd.Close();
							cmd=new SqlCommand("insert into t_employeerequests(fieldno,fieldname,requestforchange,requisitecode,requestdate,requesttime,requesttimeline,requeststatus,documentID) values(@fieldno,@fieldname,@requestforchange,@requisitecode,getdate(),@requesttime,@requesttimeline,@requeststatus,@documentID)",con);
							cmd.Transaction=trans;
							SqlParameter _fieldno=new SqlParameter("@fieldno",SqlDbType.Int);
							SqlParameter _fieldname=new SqlParameter("@fieldname",SqlDbType.VarChar);
							SqlParameter _requestforchange=new SqlParameter("@requestforchange",SqlDbType.VarChar);
							SqlParameter _requisitecode=new SqlParameter("@requisitecode",SqlDbType.VarChar);
							//SqlParameter _requestdate=new SqlParameter("@requestdate",SqlDbType.DateTime);
							SqlParameter _requesttime=new SqlParameter("@requesttime",SqlDbType.VarChar);
							SqlParameter _requesttimeline=new SqlParameter("@requesttimeline",SqlDbType.DateTime);
							SqlParameter _requeststatus=new SqlParameter("@requeststatus",SqlDbType.Int);
							SqlParameter _documentID=new SqlParameter("@documentID",SqlDbType.Text);
							_fieldno.Value=h.fieldno;
							_fieldname.Value=h.fieldname;
							_requestforchange.Value=h.requestforchange;
							_requisitecode.Value=h.requisitecode;
							//_requestdate.Value=h.requestdate;
							_requesttime.Value=h.requesttime;
							_requesttimeline.Value=h.requesttimeline;
							_requeststatus.Value=h.requeststatus;
							if(h.isDocRequired)
							{
								Guid g=Guid.NewGuid();
								string filepath=Server.MapPath(@"tempEmployee\");
								string filename=g.ToString();
								if(h.fieldno==16)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpDateofBirth.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\16"+Path.GetExtension(lblfpDateofBirth.Text)),dPath);
									}
									else
									{
										this.fpDateofBirth.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpDateofBirth.PostedFile.FileName));
									}
								}
								if(h.fieldno==24)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpFatherName.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\24"+Path.GetExtension(lblfpFatherName.Text)),dPath);
									}
									else
									{
										this.fpFatherName.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpFatherName.PostedFile.FileName));
									}
								}
								if(h.fieldno==25)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpMaritalStatus.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\25"+Path.GetExtension(lblfpMaritalStatus.Text)),dPath);
									}
									else
									{
										this.fpMartialStatus.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpMartialStatus.PostedFile.FileName));
									}
								}
								if(h.fieldno==35)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpNationality.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\35"+Path.GetExtension(lblfpNationality.Text)),dPath);
									}
									else
									{
										this.fpNationality.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpNationality.PostedFile.FileName));
									}
								}
								if(h.fieldno==36)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpNationality2.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\36"+Path.GetExtension(lblfpNationality2.Text)),dPath);
									}
									else
									{
										this.fpNationality2.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpNationality2.PostedFile.FileName));
									}
								}
								if(h.fieldno==19)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpPassprot.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\19"+Path.GetExtension(lblfpPassprot.Text)),dPath);
									}
									else
									{
										this.fpPassport.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpPassport.PostedFile.FileName));
									}
								}
								if(h.fieldno==30)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpNextofKin.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\30"+Path.GetExtension(lblfpNextofKin.Text)),dPath);
									}
									else
									{
										this.fpNextofKin.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpNextofKin.PostedFile.FileName));
									}
								}
								if(h.fieldno==21)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpNICNew.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\21"+Path.GetExtension(lblfpNICNew.Text)),dPath);
									}
									else
									{
										this.fpNICNew.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpNICNew.PostedFile.FileName));
									}
								}
								if(h.fieldno==20)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpNICOld.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\20"+Path.GetExtension(lblfpNICOld.Text)),dPath);
									}
									else
									{
										this.fpNICOld.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpNICOld.PostedFile.FileName));
									}
								}
								if(h.fieldno==32)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpBankNo.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\32"+Path.GetExtension(lblfpBankNo.Text)),dPath);
									}
									else
									{
										this.fpBankNo.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpBankNo.PostedFile.FileName));
									}
								}
								if(h.fieldno==33)
								{
									if(h.isPosted)
									{
										string dPath=filepath+filename+Path.GetExtension(lblfpBankDetail.Text);
										File.Move(Server.MapPath(@"tempDoc\"+Session["user_id"].ToString()+"\\33"+Path.GetExtension(lblfpBankDetail.Text)),dPath);
									}
									else
									{
										this.fpBankDetail.PostedFile.SaveAs(filepath+filename+Path.GetExtension(this.fpBankDetail.PostedFile.FileName));
									}
								}
								_documentID.Value=g.ToString();
							}
							else
							{
								_documentID.Value=DBNull.Value;
							}
							cmd.Parameters.Add(_fieldno);
							cmd.Parameters.Add(_fieldname);
							cmd.Parameters.Add(_requestforchange);
							cmd.Parameters.Add(_requisitecode);
							//cmd.Parameters.Add(_requestdate);
							cmd.Parameters.Add(_requesttime);
							cmd.Parameters.Add(_requesttimeline);
							cmd.Parameters.Add(_requeststatus);
							cmd.Parameters.Add(_documentID);
							cmd.ExecuteNonQuery();
							Bulletin.PostMessage(con,trans,replyatrequest,"Geo Raabta",Session["user_id"].ToString(),1);
						}
						else
						{
							//Response.Write("Process flow not defined properly for request field"+h.fieldname+". Request failed to send");
							lblCombine.Visible=true;
							lblCombine.Text="Process flow not defined properly for request field"+h.fieldname+". Request failed to send";
							trans.Rollback();
							return;
						}
					}
					trans.Commit();
					con.Close();
					lblCombine.Visible=true;
					lblCombine.Text="Your request has been sent successfully. Please check your MY REQUEST tab for Requsition IDs";
					this.btnIgnore.Visible=false;
					this.btnResend.Visible=false;
					this.btnUpdate.Enabled=true;
				}
				catch(Exception ex)
				{
					Response.Write(ex.Message);
					trans.Rollback();
					con.Close();
				}
			}
			//=======================================================//
		}

		private void imgMyGrievance_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("MyGrievance.aspx?pcode="+lblPCode.Text+"&name="+lblEmpInfo.Text);
		}

		private void imgTraining_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("EmpTraining.aspx?pcode="+this.lblPCode.Text+"&name="+this.lblEmpInfo.Text);
		}	
	

	}
}
