<%@ Register TagPrefix="ew" Namespace="eWorld.UI" Assembly="eWorld.UI" %>
<%@ Page CodeBehind="SystemBusinessUnit.aspx.cs" Language="c#" AutoEventWireup="false" Inherits="GeoRabtaSite.GeoSBU" %>
<%@ Register TagPrefix="uc1" TagName="myMenus" Src="myMenus.ascx" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Rabta ::</title>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="../StyleSheet1.css" type="text/css" rel="stylesheet">
		<LINK href="../Styles4.css" type="text/css" rel="stylesheet">
		<style type="text/css">.style1 { FONT-WEIGHT: bold; FONT-SIZE: 8px }
	.style2 { FONT-WEIGHT: bold; FONT-SIZE: 9pt }
		</style>
	</HEAD>
	<body dir="ltr" bottomMargin="0" bgProperties="fixed" leftMargin="0" background="../images/bg.jpg"
		topMargin="0" rightMargin="0">
		<form id="myForm" name="MyForm" runat="server">
			<table height="100%" cellSpacing="0" cellPadding="2" width="817" align="center" bgColor="#ffffff"
				border="0">
				<tr>
					<td vAlign="middle" align="left" background="../images/orangestrip2.jpg" height="70"><FONT size="6"><STRONG>Geo&nbsp;HR-IS</STRONG></FONT></td>
				</tr>
				<tr>
					<td background="../images/bluestrip2.jpg" height="12"></td>
				</tr>
				<tr>
					<td height="20"><uc1:mymenus id="MyMenus1" runat="server"></uc1:mymenus></td>
				</tr>
				<tr>
					<td background="../images/bluestrip2.jpg" height="20"><STRONG><FONT size="4">Geo Raabta 
								Admin :: Strategic Business Unit</FONT></STRONG></td>
				</tr>
				<tr>
					<td vAlign="top" align="left">
						<TABLE id="Table4" cellSpacing="0" cellPadding="3" width="100%" border="0">
							<TR>
								<TD colSpan="3"><STRONG>System Business Unit Name:
										<asp:requiredfieldvalidator id="RequiredFieldValidator1" runat="server" ControlToValidate="txtName" Display="Dynamic"
											ErrorMessage="* Strategic business unit name is required"></asp:requiredfieldvalidator></STRONG><BR>
									<asp:textbox id="txtName" runat="server" Width="250px" CssClass="TextBox"></asp:textbox><asp:label id="lblSBUID" runat="server" Visible="False"></asp:label></TD>
							</TR>
							<TR>
								<TD colSpan="3"><asp:panel id="Panel12" runat="server" BorderColor="Silver" BorderWidth="1px" BorderStyle="Solid"
										BackColor="GhostWhite">
										<TABLE id="Table2" cellSpacing="0" cellPadding="3" width="100%" border="0">
											<TR>
												<TD colSpan="3">
													<asp:DataGrid id="DataGrid2" runat="server" Width="100%" BackColor="White" BorderStyle="None"
														BorderWidth="1px" BorderColor="#E7E7FF" DataKeyField="sbuheadid" CellPadding="3" GridLines="Horizontal"
														AllowSorting="True" AutoGenerateColumns="False">
														<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
														<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
														<AlternatingItemStyle BackColor="#F7F7F7"></AlternatingItemStyle>
														<ItemStyle ForeColor="#4A3C8C" BackColor="#E7E7FF"></ItemStyle>
														<HeaderStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#4A3C8C"></HeaderStyle>
														<Columns>
															<asp:BoundColumn DataField="Name" SortExpression="Name" HeaderText="Name"></asp:BoundColumn>
															<asp:BoundColumn DataField="DateFrom" SortExpression="datefrom" HeaderText="From" DataFormatString="{0:d}"></asp:BoundColumn>
															<asp:BoundColumn DataField="dateto" SortExpression="dateto" HeaderText="To" DataFormatString="{0:d}"></asp:BoundColumn>
															<asp:BoundColumn DataField="IsActive" SortExpression="IsActive" HeaderText="IsActive"></asp:BoundColumn>
															<asp:EditCommandColumn ButtonType="LinkButton" UpdateText="Update" CancelText="Cancel" EditText="Edit"></asp:EditCommandColumn>
															<asp:BoundColumn Visible="False" DataField="sbuheadid" HeaderText="sbuheadid"></asp:BoundColumn>
														</Columns>
														<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
													</asp:DataGrid>
													<asp:Label id="Label1" runat="server" Visible="False">Record Not Found</asp:Label></TD>
											</TR>
											<TR>
												<TD width="350" colSpan="3">
													<asp:Button id="btnChange" runat="server" Width="184px" Text="Change SBU Head"></asp:Button></TD>
											</TR>
											<TR>
												<TD colSpan="3">
													<asp:Panel id="Panel1" runat="server" BorderStyle="Solid" BorderWidth="1px" BorderColor="Gray">
														<P>
															<TABLE id="Table1" cellSpacing="0" cellPadding="3" width="100%" border="0">
																<TR>
																	<TD width="350"><STRONG>SBU Head:</STRONG><BR>
																		<asp:DropDownList id="ddlHOD" runat="server" Width="100%"></asp:DropDownList></TD>
																	<TD></TD>
																	<TD width="350">
																		<asp:Label id="lblSbuHeadID" runat="server" Visible="False"></asp:Label></TD>
																</TR>
																<TR>
																	<TD width="350"><STRONG>From:</STRONG><BR>
																		<ew:CalendarPopup id="dpFrom" runat="server">
																			<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></WeekdayStyle>
																			<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Yellow"></MonthHeaderStyle>
																			<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																				BackColor="AntiqueWhite"></OffMonthStyle>
																			<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></GoToTodayStyle>
																			<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="LightGoldenrodYellow"></TodayDayStyle>
																			<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Orange"></DayHeaderStyle>
																			<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="LightGray"></WeekendStyle>
																			<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Yellow"></SelectedDateStyle>
																			<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></ClearDateStyle>
																			<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></HolidayStyle>
																		</ew:CalendarPopup></TD>
																	<TD></TD>
																	<TD width="350"><STRONG>
																			<asp:CheckBox id="CheckBox1" runat="server" Text="To" AutoPostBack="True"></asp:CheckBox></STRONG><BR>
																		<ew:CalendarPopup id="dpTo" runat="server" Visible="False">
																			<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></WeekdayStyle>
																			<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Yellow"></MonthHeaderStyle>
																			<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																				BackColor="AntiqueWhite"></OffMonthStyle>
																			<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></GoToTodayStyle>
																			<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="LightGoldenrodYellow"></TodayDayStyle>
																			<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Orange"></DayHeaderStyle>
																			<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="LightGray"></WeekendStyle>
																			<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Yellow"></SelectedDateStyle>
																			<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></ClearDateStyle>
																			<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></HolidayStyle>
																		</ew:CalendarPopup></TD>
																</TR>
																<TR>
																	<TD colSpan="3">
																		<asp:Button id="cmdSave" runat="server" Width="90px" Text="Save"></asp:Button>&nbsp;
																		<asp:Button id="Button1" runat="server" Width="90px" Text="Cancel"></asp:Button></TD>
																</TR>
															</TABLE>
														</P>
													</asp:Panel></TD>
											</TR>
											<TR>
												<TD colSpan="3"></TD>
											</TR>
										</TABLE>
									</asp:panel></TD>
							</TR>
							<TR>
								<TD colSpan="3"><asp:button id="btnAdd" runat="server" Width="90px" Text="Add"></asp:button>&nbsp;
									<asp:button id="cmdDelete" runat="server" Width="90px" Text="Delete" Enabled="False"></asp:button>&nbsp;
									<asp:button id="cmdCancel" runat="server" Width="90px" Text="Cancel" Enabled="False"></asp:button>&nbsp;
									<asp:button id="cmdSBUHead" runat="server" Width="90px" Visible="False" Text="SBU Head" Enabled="False"></asp:button></TD>
							</TR>
							<TR>
								<TD colSpan="3"><asp:datagrid id="DataGrid1" runat="server" Width="100%" BorderColor="Tan" BorderWidth="1px" BackColor="LightGoldenrodYellow"
										AutoGenerateColumns="False" AllowSorting="True" GridLines="None" CellPadding="2" DataKeyField="sbuid"
										ForeColor="Black">
										<FooterStyle BackColor="Tan"></FooterStyle>
										<SelectedItemStyle ForeColor="GhostWhite" BackColor="DarkSlateBlue"></SelectedItemStyle>
										<AlternatingItemStyle BackColor="PaleGoldenrod"></AlternatingItemStyle>
										<HeaderStyle Font-Bold="True" BackColor="Tan"></HeaderStyle>
										<Columns>
											<asp:EditCommandColumn ButtonType="LinkButton" UpdateText="Update" CancelText="Cancel" EditText="Edit"></asp:EditCommandColumn>
											<asp:BoundColumn Visible="False" DataField="sbuid"></asp:BoundColumn>
											<asp:BoundColumn DataField="sbuname" HeaderText="SBU Name"></asp:BoundColumn>
										</Columns>
										<PagerStyle HorizontalAlign="Center" ForeColor="DarkSlateBlue" BackColor="PaleGoldenrod"></PagerStyle>
									</asp:datagrid></TD>
							</TR>
						</TABLE>
					</td>
				</tr>
				<tr>
					<td vAlign="middle" align="center" height="20">Copyright © 2005 Independent Media 
						Corporation <A href="http://www.geo.tv">www.geo.tv</A></td>
				</tr>
			</table>
		</form>
	</body>
</HTML>
