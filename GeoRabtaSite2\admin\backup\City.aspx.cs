using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for GeoIdeas.
	/// </summary>
	public class GeoCity : System.Web.UI.Page
	{
		SqlConnection con;
		protected System.Web.UI.WebControls.DataGrid DataGrid1;
		protected System.Web.UI.WebControls.Panel Panel1;
		protected System.Web.UI.WebControls.TextBox txtDesId;
		protected System.Web.UI.WebControls.Label Label1;
		protected System.Web.UI.WebControls.Button btnCancel;
		protected System.Web.UI.WebControls.Button btnQueryButton;
		protected System.Web.UI.WebControls.LinkButton lnkAddNewStation;
		protected System.Web.UI.WebControls.TextBox txtName;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator1;
		protected System.Web.UI.WebControls.Label Label2;
		protected System.Web.UI.WebControls.DropDownList ddCountry;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator2;
		protected System.Web.UI.WebControls.Label Label3;
		private static DataSet ds;
		int WebFormID=4;	// City.aspx
		int ProjectID=2;
		protected System.Web.UI.WebControls.Label lblMsg;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator1;
		protected System.Web.UI.HtmlControls.HtmlTable Table2;
		protected System.Web.UI.HtmlControls.HtmlTable tdFrom2;	// 
		string userId="";
		private bool IsPageAccessAllowed()
		{


			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception ex)
			{
				Response.Redirect("../Login.aspx");
			}
		
			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"View")==true)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("../Login.aspx");
				return false;
			}
		}
		
		private void Page_Load(object sender, System.EventArgs e)
		{

			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			if(IsPageAccessAllowed())
			{
				con=new SqlConnection(Connection.ConnectionString);
				con.Open();
				if(!IsPostBack)
				{
					getCountry();
					BindGrid();
					tdFrom2.Visible=false;
					
				} 
				con.Close();
				lnkAddNewStation.Visible=GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Add");
				DataGrid1.Columns[0].Visible=GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Edit");
				DataGrid1.Columns[1].Visible=GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Delete");
			}
			else
			{
				Response.Redirect("ErrorPage.aspx");
			}
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.btnQueryButton.Click += new System.EventHandler(this.btnQueryButton_Click);
			this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
			this.lnkAddNewStation.Click += new System.EventHandler(this.lnkAddNewStation_Click);
			this.DataGrid1.EditCommand += new System.Web.UI.WebControls.DataGridCommandEventHandler(this.DataGrid1_EditCommand);
			this.DataGrid1.DeleteCommand += new System.Web.UI.WebControls.DataGridCommandEventHandler(this.DataGrid1_DeleteCommand);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		public void getCountry()
		{
			string country="select countryid,cname from t_country";
			SqlCommand cmd=new SqlCommand(country,con);
			SqlDataReader rd=cmd.ExecuteReader();
			ddCountry.Items.Clear();
			ddCountry.Items.Add("");
			while(rd.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rd[0].ToString();
				itm.Text=rd[1].ToString();
				this.ddCountry.Items.Add(itm);
			}
			rd.Close();
		}

		public void BindGrid()
		{
			string str="select c.cityid as Id,c.cityname as CityName ,cc.cname Country from t_city c,t_country cc where c.countryid=cc.countryid and c.status='"+1+"'";
			SqlDataAdapter ord=new SqlDataAdapter(str,con);
			ds=new DataSet();
			ord.Fill(ds,"City");
			this.DataGrid1.DataSource=ds.Tables["City"].DefaultView;
			this.DataGrid1.DataBind();
		}

		private void btnQueryButton_Click(object sender, System.EventArgs e)
		{
			con.Open();
			if(this.btnQueryButton.Text=="New City")
			{
				this.btnQueryButton.Text="Save";
				this.btnCancel.Enabled=true;
				this.txtName.Text="";
				this.ddCountry.SelectedIndex=0;
				this.txtDesId.Text="";
				//btnQueryButton.CausesValidation=true;
			}
			else if (btnQueryButton.Text=="Save")
			{
				if(this.ddCountry.SelectedValue !="")
				{
					string verify="select cityname from t_city where cityname='"+ this.txtName.Text +"' And status=1 And countryid='"+ this.ddCountry.SelectedValue.ToString() +"' ";
					SqlCommand cCity=new SqlCommand(verify,con);
					SqlDataReader rCity=cCity.ExecuteReader();
					rCity.Read();
					if(rCity.HasRows)
					{
						this.lblMsg.Visible=true;
						this.lblMsg.Text="The name of inserting city already entered,please choose another name"; 
					}
					else
					{
						rCity.Close();
						string str="insert into t_city values('"+this.txtName.Text+"','"+Convert.ToInt32(this.ddCountry.SelectedValue.ToString())+"','"+1+"')"; 
						SqlCommand c=new SqlCommand(str,con);
						int i=c.ExecuteNonQuery();
						BindGrid();
						//btnQueryButton.CausesValidation=false;
						tdFrom2.Visible=false;
						lnkAddNewStation.Visible=true;
						btnQueryButton.Text="Add";
						//btnQueryButton.CausesValidation=true;
						this.lblMsg.Visible=false;
					}	
				}
				else
				{
					this.lblMsg.Visible=true;
					this.lblMsg.Text="Please Select Country Before Insertion";
					this.lblMsg.ForeColor=Color.Red;
				}
				
			}
			else if (btnQueryButton.Text=="Update")
			{
					
				if(this.ddCountry.SelectedValue !="")
				{
					string verify="select cityname from t_city where cityname='"+ this.txtName.Text +"' And status=1 And countryid='"+ this.ddCountry.SelectedValue.ToString() +"' And cityid not in("+this.txtDesId.Text+")";
					SqlCommand cCity=new SqlCommand(verify,con);
					SqlDataReader rCity=cCity.ExecuteReader();
					rCity.Read();
					if(rCity.HasRows)
					{
						this.lblMsg.Visible=true;
						this.lblMsg.Text="The name of city already entered,please choose another name"; 
					}
					else
					{
						rCity.Close();
						string str="update t_city set cityname='"+this.txtName.Text+"',countryid='"+Convert.ToInt32(this.ddCountry.SelectedValue.ToString())+"',status='"+1+"' where cityid='"+Int32.Parse(this.txtDesId.Text)+"'"; 
						SqlCommand c=new SqlCommand(str,con);
						int i=c.ExecuteNonQuery();
						BindGrid();
						this.btnQueryButton.Text="Save";
						this.btnCancel.Enabled=false;
						this.txtDesId.Enabled=true;
						this.Label1.Visible=false;
						this.txtDesId.Visible=false;
						this.txtDesId.Text="";
						this.ddCountry.SelectedIndex=0;
						this.txtName.Text="";
						//btnQueryButton.CausesValidation=false;
						tdFrom2.Visible=false;
						lnkAddNewStation.Visible=true;
						btnQueryButton.Text="Add";
						//btnQueryButton.CausesValidation=true;
						this.lblMsg.Visible=false;
					}
				}
				else
				{
					this.lblMsg.Visible=true;
					this.lblMsg.Text="Please Select Country Before Insertion";
					this.lblMsg.ForeColor=Color.Red;				
				}
				
			}
			con.Close();
			
		}

		private void btnCancel_Click(object sender, System.EventArgs e)
		{
			this.btnQueryButton.Text="Save";
			this.btnCancel.Enabled=false;
			this.txtDesId.Text="";
			this.ddCountry.SelectedIndex=0;
			this.txtName.Text="";
			this.Label1.Visible=false;
			this.txtDesId.Visible=false;
			//btnQueryButton.CausesValidation=false;
			tdFrom2.Visible=false;
			lnkAddNewStation.Visible=true;
			this.lblMsg.Visible=false;
		}

		private void DataGrid1_DeleteCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{
			bool functional=false;
			bool employee=false;
			con.Open();
			
			string verifyDes="select fdesigid from t_functionaldesignation where isactive=1 And station='"+ this.DataGrid1.DataKeys[e.Item.ItemIndex].ToString() +"'";
			SqlCommand cDes=new SqlCommand(verifyDes,con);
			SqlDataReader rDes=cDes.ExecuteReader();
			rDes.Read();
			if(rDes.HasRows)
			{
				functional=true;
				rDes.Close();
			}
			else
			{
			 rDes.Close();
			}
            string verifyEmp="select pcode from t_employee where station='"+ this.DataGrid1.DataKeys[e.Item.ItemIndex].ToString() +"' And del=1";
			SqlCommand cEmp=new SqlCommand(verifyEmp,con);
			SqlDataReader rEmp=cEmp.ExecuteReader();
			rEmp.Read();
			if(rEmp.HasRows)
			{
				rEmp.Close();
				employee=true;
			}
			else
			{
			 rEmp.Close();
			}
			
			if(functional || employee)
			{
			 this.lblMsg.Visible=true;
             this.lblMsg.Text=" Functional Designation OR Employee exist on selected City.Please first delete them";
			}
			else
			{
				string str="update t_city set status='"+0+"' where cityid=@Id";
				SqlCommand cm=new SqlCommand(str,con);
				cm.Parameters.Add(new SqlParameter("@Id",SqlDbType.VarChar,20));
				cm.Parameters["@Id"].Value=this.DataGrid1.DataKeys[e.Item.ItemIndex]; 
				cm.ExecuteNonQuery();
				this.DataGrid1.EditItemIndex=-1;
				BindGrid();
				con.Close();
				this.lblMsg.Visible=false;
			}
		}

		private void DataGrid1_EditCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{
			con.Open();
			this.btnQueryButton.Text="Update";
			this.btnCancel.Enabled=true;
			this.txtDesId.Enabled=false;
			this.Label1.Visible=true;
			this.txtDesId.Visible=true;
			this.lblMsg.Visible=false;

			string str="select c.cityid,c.cityname,cc.countryid from t_city c,t_country cc where c.countryid=cc.countryid and c.status='"+1+"' And c.cityid=@Id";
			SqlCommand cmd=new SqlCommand(str,con);
			cmd.Parameters.Add(new SqlParameter("@Id",SqlDbType.VarChar,20));
			cmd.Parameters["@Id"].Value=this.DataGrid1.DataKeys[e.Item.ItemIndex];
			SqlDataReader rd=cmd.ExecuteReader();
			string desId="";
			string name="";
			string des="";
			//string dept="";
			while(rd.Read())
			{
				desId=rd[0].ToString(); 
				name=rd[1].ToString();
				des=rd[2].ToString();
				
			}
			this.txtDesId.Text=desId;
			this.txtName.Text=name;
			this.ddCountry.SelectedValue=des;
			con.Close();
			tdFrom2.Visible=true;
			lnkAddNewStation.Visible=false;

		}

		private void lnkAddNewStation_Click(object sender, System.EventArgs e)
		{
			tdFrom2.Visible=true;
			lnkAddNewStation.Visible=false;
			btnCancel.Enabled=true;
			this.lblMsg.Visible=false;
		}


	}
}
