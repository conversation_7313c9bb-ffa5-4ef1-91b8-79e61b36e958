using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

namespace GeoRabtaSite.admin
{
	/// <summary>
	/// Summary description for ControlSetup.
	/// </summary>
	public class SelectUser : System.Web.UI.Page
	{
		protected System.Web.UI.WebControls.DataGrid dgEmployee;
		protected System.Web.UI.WebControls.DropDownList ddlDepartment;
		protected System.Web.UI.WebControls.DropDownList ddlSBU;
		public static string sort;
		public static int WebFormID=37;
		public static int ProjectID=2;
		public static string userId="";
		
		private bool IsPageAccessAllowed()
		{
			
			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception ex)
			{
				Response.Redirect("Login.aspx");
			}

			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"View")==true)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("Login.aspx");
				return false;
			}
		}
		private void FillSBUDDL()
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			SqlCommand cmd=new SqlCommand("select * from t_sbu where isActive=1 and Del=1 order by sbuname",conn);
			SqlDataReader dr=cmd.ExecuteReader();
			ddlSBU.Items.Clear();
			ddlSBU.Items.Add("Please select SBU");
			while (dr.Read())
			{
				ddlSBU.Items.Add(new ListItem(dr["sbuname"].ToString(), dr["sbuid"].ToString()));
			}
			dr.Close();
			conn.Close();
		}

		private void FillDepartmentDDL()
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			SqlCommand cmd=new SqlCommand("select * from t_department where status=1 and sbu=" + ddlSBU.SelectedItem.Value.ToString() + " order by deptname",conn);
			SqlDataReader dr=cmd.ExecuteReader();
			ddlDepartment.Items.Clear();
			ddlDepartment.Items.Add("Please select department");
			while (dr.Read())
			{
				ddlDepartment.Items.Add(new ListItem(dr["deptname"].ToString(), dr["deptid"].ToString()));
			}
			dr.Close();
			conn.Close();
		}
		private void Page_Load(object sender, System.EventArgs e)
		{
			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			if(IsPageAccessAllowed())
			{
              
			}
			else
			{
				Response.Redirect("ErrorPage.aspx");
			}
			if (!IsPostBack)
			{
				FillSBUDDL();
			}
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.ddlSBU.SelectedIndexChanged += new System.EventHandler(this.ddlSBU_SelectedIndexChanged);
			this.ddlDepartment.SelectedIndexChanged += new System.EventHandler(this.ddlDepartment_SelectedIndexChanged);
			this.dgEmployee.SortCommand += new System.Web.UI.WebControls.DataGridSortCommandEventHandler(this.dgEmployee_SortCommand);
			this.dgEmployee.SelectedIndexChanged += new System.EventHandler(this.dgEmployee_SelectedIndexChanged);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		private void ddlSBU_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			
			if(ddlSBU.SelectedIndex>0)
			{
				FillDepartmentDDL();
				dgEmployee.Visible=false;
			}
			else
			{
				ddlDepartment.Items.Clear();
			}
		}

		private void FillEmployees()
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			string str="SELECT dbo.t_Employee.pcode, dbo.t_Employee.name, dbo.t_Designation.designation " +
				" FROM dbo.t_Department INNER JOIN " +
				" dbo.t_Designation ON dbo.t_Department.deptid = dbo.t_Designation.deptid INNER JOIN " +
				" dbo.t_Employee ON dbo.t_Designation.desigid = dbo.t_Employee.desigid INNER JOIN " +
				" dbo.t_Sbu ON dbo.t_Department.sbu = dbo.t_Sbu.sbuid " +
				" WHERE (dbo.t_Employee.del = 1) AND (dbo.t_Department.deptid = " + ddlDepartment.SelectedValue.ToString() + ") AND (dbo.t_Sbu.sbuid = " + ddlSBU.SelectedValue.ToString() + ") order by t_Designation.designation,t_employee.name"; 
			SqlDataAdapter dr=new SqlDataAdapter(str,conn);
			DataSet ds=new DataSet();
			dr.Fill(ds,"Record");
			DataView dv=new DataView(ds.Tables["Record"]);
			dv.Sort=sort;
			dgEmployee.DataSource=dv;
			dgEmployee.DataBind();
			conn.Close();

		}
		private void ddlDepartment_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			if(ddlDepartment.SelectedIndex>0)
			{
				dgEmployee.Visible=true;
				FillEmployees();
			}
			else
			{
				dgEmployee.Visible=false;
			}
		}

		private void dgEmployee_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			string employeeCode=dgEmployee.Items[dgEmployee.SelectedIndex].Cells[0].Text;
			string employeeName=dgEmployee.Items[dgEmployee.SelectedIndex].Cells[1].Text;
			Session.Add("_PCode",employeeCode);
			Session.Add("_EmployeeName",employeeName);
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			SqlCommand cmd=new SqlCommand("SELECT user_id FROM dbo.t_systemuser WHERE (pcode = '" + employeeCode + "')",conn);
			SqlDataReader dr=cmd.ExecuteReader();
			dr.Read();
			if (dr.HasRows)
			{
				Session.Add("_user_id",dr["user_id"].ToString());
			}
			else
			{
				Session.Add("_user_id","");
			}
			dr.Close();
			conn.Close();
			Response.Redirect("UserManagement.aspx");
		}

		private void dgEmployee_SortCommand(object source, System.Web.UI.WebControls.DataGridSortCommandEventArgs e)
		{
			sort=e.SortExpression.ToString();
			this.FillEmployees();
		}
	}
}
