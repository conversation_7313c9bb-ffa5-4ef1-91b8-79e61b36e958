<%@ Register TagPrefix="uc1" TagName="LoginUser" Src="../LoginUser.ascx" %>
<%@ Page language="c#" Codebehind="Dictionary.aspx.cs" AutoEventWireup="false" Inherits="GeoRabtaSite.admin.Dictionary" %>
<%@ Register TagPrefix="uc1" TagName="AdminUserControl" Src="../AdminUserControl.ascx" %>
<%@ Register TagPrefix="uc1" TagName="myMenus" Src="myMenus.ascx" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
  <HEAD>
		<title>Geo Rabta ::</title>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="RaabtaAdmin.css" type="text/css" rel="stylesheet">
		<LINK href="../Styles4.css" type="text/css" rel="stylesheet">
		<script src="../client.js"></script>
		<script language="javascript">
		//if (parent.location.href!="<%=RaabtaURL()%>admin/default.aspx")
			//parent.location.href="<%=RaabtaURL()%>admin/default.aspx";
			
			var highlightColor='blue';
			var highlighText='white';

			var normalColor='white';
			var normalText='black';
			
			var idx=-1;
			var totalItems;
			
			function GetDesignations(e)
			{
				var divMain=document.getElementById("divMain");
				var ddMdept=document.getElementById("ddMdept");

				var desg=document.getElementById("txtName").value;
				var kc=e.keyCode;
				
				if(kc==27 || kc==9)
				{
					divMain.style.visibility="hidden";
				}
				if ((kc>=48 && kc<=57) || (kc>=65 && kc<=90) || kc==32 || kc==8 || kc==46 )
				{
					var desg=document.getElementById("txtName");
					if (desg.value!="")
					{
						var desgParameter="id="+desg.value+"&sbu=-1";
						SearchDic('../searchdesignation.aspx',desgParameter);
					    TD1.style.visibility="hidden";	
					}
					else
					{
						divMain.style.visibility="hidden";
					    TD1.style.visibility="visible";
					}
				}
				if(desg.value=="")
				{
					divMain.style.visibility="hidden";
					var ddMdept=document.getElementById("ddMdept");
				}
			}
			
			
			function Highlight(i)
			{
			}
			
			
			function disableEnterKey(e)
			{
			    var key;
			    if(window.event)
					key = window.event.keyCode;     //IE
				else
					key = e.which;     //firefox
				if(key == 13)
					return false;
				else
					return true;
			}
			function CloseIt()
			{
				var txtName=document.getElementById("txtName");
				if(txtName!=null)
					txtName.focus();
				var divMain = document.getElementById("divMain");
				divMain.style.visibility="hidden";
				TD1.style.visibility="visible";
			}
			
			function SelectIt(i)
			{
			}
			
			function findPosX(obj)
{
	var curleft = 0;
	if(obj.offsetParent)
		while(1) 
		{
			curleft += obj.offsetLeft;
			if(!obj.offsetParent)
				break;
			obj = obj.offsetParent;
        }
    else if(obj.x)
        curleft += obj.x;
    return curleft;
}

function findPosY(obj)
{
	var curtop = 0;
	if(obj.offsetParent)
		while(1)
		{
			curtop += obj.offsetTop;
			if(!obj.offsetParent)
				break;
			obj = obj.offsetParent;
		}
	else if(obj.y)
	curtop += obj.y;
	return curtop;
}			

function Verify(theForm)
 {
        if(document.getElementById('txtName').value=='')
         {
          alert("Please enter designation title");
          theForm.txtName.focus();
          return false;
         } 
         if (theForm.ddnCat.selectedIndex == 0)
            {
            alert("Please select category");
            theForm.ddnCat.focus();
            return false;
            }
        var Check=document.getElementById('chkVerify')
        if(Check.checked==true)
         {
          if (theForm.ddcCat.selectedIndex == 0)
            {
            alert("Please select category after departmental head criteria");
            theForm.ddcCat.focus();
            return false;
            }
         }     
 }
			
		</script>
</HEAD>
	<body dir="ltr" bottomMargin="0" leftMargin="0" topMargin="0" rightMargin="0">
		<form id="myForm" name="MyForm" runat="server">
			<DIV id="divMain" style="POSITION: absolute; BORDER-BOTTOM-COLOR: gray; BORDER-RIGHT-WIDTH: 1px; BORDER-TOP-COLOR: gray; WIDTH: 312px; FONT-FAMILY: Verdana; BORDER-TOP-WIDTH: 1px; BORDER-BOTTOM-WIDTH: 1px; HEIGHT: 284px; VISIBILITY: hidden; COLOR: #4d4d4d; BORDER-RIGHT-COLOR: gray; FONT-SIZE: 12px; OVERFLOW: auto; BORDER-LEFT-COLOR: gray; BORDER-LEFT-WIDTH: 1px; TOP: 304px; FONT-WEIGHT: normal; LEFT: 224px"><SPAN id="txtHint"></SPAN></DIV>
			<table height="100%" cellSpacing="0" cellPadding="0" width="750" align="center" bgColor="#ffffff"
				border="0">
				<tr>
					<td vAlign="middle" align="left" height="69">
      <OBJECT 
      codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0" 
      classid=clsid:D27CDB6E-AE6D-11cf-96B8-444553540000 
      data="data:application/x-oleobject;base64,btt80m2uzxGWuERFU1QAAGdVZlUACQAAnVAAACEHAAAIAAIAAAAAAAgAAAAAAAgAAAAAAAgADgAAAFcAaQBuAGQAbwB3AAAACAAGAAAALQAxAAAACAAGAAAALQAxAAAACAAKAAAASABpAGcAaAAAAAgAAgAAAAAACAAGAAAALQAxAAAACAAAAAAACAACAAAAAAAIABAAAABTAGgAbwB3AEEAbABsAAAACAAEAAAAMAAAAAgABAAAADAAAAAIAAIAAAAAAAgAAAAAAAgAAgAAAAAADQAAAAAAAAAAAAAAAAAAAAAACAAEAAAAMQAAAAgABAAAADAAAAAIAAAAAAAIAAQAAAAwAAAACAAIAAAAYQBsAGwAAAAIAAwAAABmAGEAbABzAGUAAAA=" 
      width=780 height=69>
	<embed src="flash/Top1.swf" quality="high" 
      pluginspage="http://www.macromedia.com/go/getflashplayer"
      								type="application/x-shockwave-flash" width="780" height="69">
	</embed>
	</OBJECT>
					</td>
				</tr>
				<tr>
					<td height="10">
      <OBJECT 
      codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0" 
      classid=clsid:D27CDB6E-AE6D-11cf-96B8-444553540000 
      data="data:application/x-oleobject;base64,btt80m2uzxGWuERFU1QAAGdVZlUACQAAnVAAAAgBAAAIAAIAAAAAAAgAAAAAAAgAAAAAAAgADgAAAFcAaQBuAGQAbwB3AAAACAAGAAAALQAxAAAACAAGAAAALQAxAAAACAAKAAAASABpAGcAaAAAAAgAAgAAAAAACAAGAAAALQAxAAAACAAAAAAACAACAAAAAAAIABAAAABTAGgAbwB3AEEAbABsAAAACAAEAAAAMAAAAAgABAAAADAAAAAIAAIAAAAAAAgAAAAAAAgAAgAAAAAADQAAAAAAAAAAAAAAAAAAAAAACAAEAAAAMQAAAAgABAAAADAAAAAIAAAAAAAIAAQAAAAwAAAACAAIAAAAYQBsAGwAAAAIAAwAAABmAGEAbABzAGUAAAA=" 
      width=780 height=10>
	<embed src="flash/Top2.swf" quality="high" 
      pluginspage="http://www.macromedia.com/go/getflashplayer"
      								type="application/x-shockwave-flash" width="780" height="10">
	</embed>
	</OBJECT>
					</td>
				</tr>
				<TR>
					<TD class="PageTitle" height="20">Welcome To HR - IS Admin Site</TD>
				</TR>
				<tr>
					<td background="../images/menu-off-bg.gif" height="20"><uc1:mymenus id="MyMenus1" runat="server"></uc1:mymenus></td>
				</tr>
				<tr>
					<td class="MainBG" vAlign="top" align="left">
						<P>
							<TABLE id="Table1" cellSpacing="1" cellPadding="1" width="100%" border="1">
								<TR>
									<TD style="HEIGHT: 26px">
										<P><STRONG>Important Guidline for Designation Creation in Dictionary:</STRONG><SPAN style="FONT-FAMILY: 'Times New Roman'; FONT-SIZE: 12pt; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA"><FONT face="Verdana" size="1"></P>
										<OL style="MARGIN-TOP: 0in" type="1">
											<LI class="MsoNormal" style="mso-list: l0 level1 lfo1; tab-stops: list .5in">
												All alpha-numeric characters and symbols including "space, comma, slash, dash, 
												brackets and like" are considered separate characters.<SPAN style="mso-spacerun: yes">&nbsp;&nbsp;
												</SPAN>
											<LI class="MsoNormal" style="mso-list: l0 level1 lfo1; tab-stops: list .5in">
												Short forms and abbreviations of words like "Sr." ,"Jr.", "Asst." and like are 
												prohibited, and shall be considered separate designations.<SPAN style="mso-spacerun: yes">&nbsp;
												</SPAN>
											<LI class="MsoNormal" style="mso-list: l0 level1 lfo1; tab-stops: list .5in">
												To the extend possible, try to avoid using symbols mentioned earlier.<SPAN style="mso-spacerun: yes">&nbsp;&nbsp;
												</SPAN>
											<LI class="MsoNormal" style="mso-list: l0 level1 lfo1; tab-stops: list .5in">
												If a designation like Manager, Senior Manager and General Manager or equivalent 
												like Producer, Senior Producer and Executive Producer holds the position of 
												HOD, please re-assign category as per requirement, after the general category 
												is allocated.<SPAN style="mso-spacerun: yes">&nbsp; </SPAN>
											<LI class="MsoNormal" style="mso-list: l0 level1 lfo1; tab-stops: list .5in">
											Rights of designation creation are included in the roll of "BU" i.e. 
											designation can be created by any personnel who possess rights of "BU" for any 
											set of departments.
											<LI class="MsoNormal" style="mso-list: l0 level1 lfo1; tab-stops: list .5in">
												<SPAN style="FONT-FAMILY: 'Times New Roman'; FONT-SIZE: 12pt; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA">
													<FONT face="Verdana" size="1">Rights of designation editing and deletion are 
														available to Senior Manager Human Resources&nbsp;- Business Unit only</FONT>.</SPAN>
											<LI class="MsoNormal" style="mso-list: l0 level1 lfo1; tab-stops: list .5in">
												<SPAN style="FONT-FAMILY: 'Times New Roman'; FONT-SIZE: 12pt; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA">
													<SPAN style="FONT-FAMILY: 'Times New Roman'; FONT-SIZE: 12pt; mso-fareast-font-family: 'Times New Roman'; mso-ansi-language: EN-US; mso-fareast-language: EN-US; mso-bidi-language: AR-SA">
														<FONT face="Verdana" size="1">Please note that if any given designation is amended/ 
															deleted, the impact will be organisation wide, and constant to all the 
															positions available against that particular designation</FONT></SPAN><BR>
												</SPAN></FONT></SPAN></LI></OL>
									</TD>
								</TR>
								<TR>
									<TD style="HEIGHT: 26px">Designation Title
										<asp:label id="lnkduplicate" runat="server" Visible="False" ForeColor="Red"></asp:label><BR>
										<asp:textbox id="txtName" runat="server" autocomplete="off" Width="336px" CssClass="textbox"
											MaxLength="200"></asp:textbox></TD>
								</TR>
								<TR>
									<TD id="TD1" style="HEIGHT: 26px" runat="server">General Category:<BR>
										<asp:dropdownlist id="ddnCat" runat="server" Width="336px" CssClass="textbox" AutoPostBack="True">
											<asp:ListItem Value="0">Select--Category</asp:ListItem>
										</asp:dropdownlist></TD>
								</TR>
								<TR>
									<TD style="HEIGHT: 26px">Is Departmental Head:<BR>
										<asp:checkbox id="chkVerify" runat="server" AutoPostBack="True" Enabled="False" Text="Yes / No (IF Yes please check)"></asp:checkbox></TD>
								</TR>
								<TR>
									<TD id="Criteria" style="HEIGHT: 26px" runat="server">
										Re-assign Category&nbsp;as Per Policy:<BR>
										<asp:dropdownlist id="ddcCat" runat="server" Width="336px" CssClass="textbox">
											<asp:ListItem Value="0">Select--Category</asp:ListItem>
										</asp:dropdownlist></TD>
								</TR>
								<TR>
									<TD><asp:button id="btnAdd" runat="server" CssClass="BlueButton" Text="Add To Dictionary"></asp:button>&nbsp;<asp:button id="btnDelete" runat="server" CssClass="BlueButton" Text="Delete From Dictionary"></asp:button>&nbsp;<asp:button id="btnCancel" runat="server" CssClass="BlueButton" Text="Cancel" CausesValidation="False"></asp:button><BR>
										<asp:label id="lblmsg" runat="server" Visible="False"></asp:label><BR>
										<asp:linkbutton id="hNew" runat="server" Visible="False">Add New</asp:linkbutton></TD>
								</TR>
								<TR>
									<TD class="OrangeFormTitle" colSpan="3"><asp:label id="Label1" runat="server">Dictionary Items</asp:label></TD>
								</TR>
								<TR>
									<TD><asp:datagrid id="dgItems" runat="server" Width="100%" DataKeyField="ID" AllowSorting="True" AutoGenerateColumns="False">
											<AlternatingItemStyle CssClass="AlternetItem"></AlternatingItemStyle>
											<ItemStyle CssClass="Item"></ItemStyle>
											<HeaderStyle CssClass="header"></HeaderStyle>
											<Columns>
												<asp:ButtonColumn Text="View" HeaderText="View To Modify" CommandName="Select">
													<HeaderStyle HorizontalAlign="Left" Width="13%"></HeaderStyle>
												</asp:ButtonColumn>
												<asp:BoundColumn Visible="False" DataField="ID" SortExpression="ID"></asp:BoundColumn>
												<asp:BoundColumn DataField="Designation" SortExpression="Designation" HeaderText="Designation Title"></asp:BoundColumn>
												<asp:BoundColumn DataField="category" SortExpression="category" HeaderText="Category"></asp:BoundColumn>
												<asp:BoundColumn DataField="isdepartmentalhead" SortExpression="isdepartmentalhead" HeaderText="Is Departmental Head"></asp:BoundColumn>
												<asp:BoundColumn DataField="CategoryAfterDHCriteria" SortExpression="CategoryAfterDHCriteria" HeaderText="Category After DH Criteria"></asp:BoundColumn>
												<asp:BoundColumn Visible="False" DataField="cat_id"></asp:BoundColumn>
												<asp:BoundColumn Visible="False" DataField="catafterdh"></asp:BoundColumn>
											</Columns>
											<PagerStyle Mode="NumericPages"></PagerStyle>
										</asp:datagrid><asp:label id="lnkdgmsg" runat="server" Visible="False"></asp:label></TD>
								</TR>
							</TABLE>
						</P>
					</td>
				</tr>
				<tr>
					<td vAlign="middle" align="center" height="20">Copyright © 2005 Independent Media 
						Corporation <A href="http://www.geo.tv">www.geo.tv</A></td>
				</tr>
			</table>
		</form>
	</body>
</HTML>
