(function(){tinymce.create("tinymce.plugins.FullPagePlugin",{init:function(a,b){var c=this;c.editor=a;a.addCommand("mceFullPageProperties",function(){a.windowManager.open({file:b+"/fullpage.htm",width:430+parseInt(a.getLang("fullpage.delta_width",0)),height:495+parseInt(a.getLang("fullpage.delta_height",0)),inline:1},{plugin_url:b,head_html:c.head})});a.addButton("fullpage",{title:"fullpage.desc",cmd:"mceFullPageProperties"});a.onBeforeSetContent.add(c._setContent,c);a.onSetContent.add(c._setBodyAttribs,c);a.onGetContent.add(c._getContent,c)},getInfo:function(){return{longname:"Fullpage",author:"Moxiecode Systems AB",authorurl:"http://tinymce.moxiecode.com",infourl:"http://wiki.moxiecode.com/index.php/TinyMCE:Plugins/fullpage",version:tinymce.majorVersion+"."+tinymce.minorVersion}},_setBodyAttribs:function(e,a){var p,d,g,l,b,m,n,h=this.head.match(/body(.*?)>/i),j="",c,f=this.head.match(/<html([^>]*?)>/i);if(h&&h[1]){p=h[1].match(/\s*(\w+\s*=\s*".*?"|\w+\s*=\s*'.*?'|\w+\s*=\s*\w+|\w+)\s*/g);if(p){for(d=0,g=p.length;d<g;d++){l=p[d].split("=");b=l[0].replace(/\s/,"");m=l[1];if(m){m=m.replace(/^\s+/,"").replace(/\s+$/,"");n=m.match(/^["'](.*)["']$/);if(n){m=n[1]}if(b=="dir"){j=m}}else{m=b}e.dom.setAttrib(e.getBody(),"style",m)}}}if(j==""&&f&&f[1]){c=f[1].match(/dir\s*=\s*["']([^"']*)["']/i);if(c&&c[1]){j=c[1]}}bd=e.getBody();bd.setAttribute("dir",j)},_createSerializer:function(){return new tinymce.dom.Serializer({dom:this.editor.dom,indent:true,apply_source_formatting:true,indent_before:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,ul,li,area,title,meta,head",indent_after:"p,h1,h2,h3,h4,h5,h6,blockquote,div,title,style,pre,script,td,ul,li,area,title,meta,head"})},_setContent:function(d,b){var i=this,a,l,f=b.content,h,j="";if(b.format=="raw"&&i.head){return}if(b.source_view&&d.getParam("fullpage_hide_in_source_view")){return}f=f.replace(/<(\/?)BODY/gi,"<$1body");a=f.indexOf("<body");if(a!=-1){a=f.indexOf(">",a);i.head=f.substring(0,a+1);var k=0,g;i.css="";while((k=i.head.indexOf("<style",k))!=-1){k=f.indexOf(">",k)+1;if((g=i.head.indexOf("</style",k))==-1){break}i.css+=i.head.substring(k,g);k=g}l=f.indexOf("</body",a);if(l==-1){l=f.length}b.content=f.substring(a+1,l);i.foot=f.substring(l);function e(c){return c.replace(/<\/?[A-Z]+/g,function(m){return m.toLowerCase()})}i.head=e(i.head);i.foot=e(i.foot)}else{i.head="";if(d.getParam("fullpage_default_xml_pi")){i.head+='<?xml version="1.0" encoding="'+d.getParam("fullpage_default_encoding","ISO-8859-1")+'" ?>\n'}i.head+=d.getParam("fullpage_default_doctype",'<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">');i.head+="\n<html>\n<head>\n";if(h=d.getParam("fullpage_default_title")){i.head+="<title>"+h+"</title>\n"}if(h=d.getParam("fullpage_default_encoding")){i.head+='<meta http-equiv="Content-Type" content="text/html; charset='+h+'" />\n'}if(h=d.getParam("fullpage_default_font_family")){j+="font-family: "+h+";"}if(h=d.getParam("fullpage_default_font_size")){j+="font-size: "+h+";"}if(h=d.getParam("fullpage_default_text_color")){j+="color: "+h+";"}i.head+="</head>\n<body"+(j?' style="'+j+'"':"")+">\n";i.foot="\n</body>\n</html>"}},_getContent:function(a,c){var b=this;if(!c.source_view||!a.getParam("fullpage_hide_in_source_view")){c.content=tinymce.trim(b.head)+"\n"+tinymce.trim(c.content)+"\n"+tinymce.trim(b.foot);if(b.css){b._setStyle(a,b.css)}}},_setStyle:function(a,b){a.dom.remove("injectedCSS");var d=a.dom.doc,c=d.createElement("style");c.type="text/css";c.id="injectedCSS";if(c.styleSheet){c.styleSheet.cssText=b}else{c.appendChild(d.createTextNode(b))}d.getElementsByTagName("head")[0].appendChild(c)}});tinymce.PluginManager.add("fullpage",tinymce.plugins.FullPagePlugin)})();