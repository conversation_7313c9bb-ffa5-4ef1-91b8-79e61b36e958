<%@ Register TagPrefix="uc1" TagName="LoginUser" Src="../LoginUser.ascx" %>
<%@ Register TagPrefix="uc1" TagName="AdminUserControl" Src="../AdminUserControl.ascx" %>
<%@ Register TagPrefix="uc1" TagName="myMenus" Src="myMenus.ascx" %>
<%@ Page language="c#" Codebehind="ProjectManage.aspx.cs" AutoEventWireup="false" Inherits="GeoRabtaSite.admin.ProjectManage" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Rabta ::</title>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="RaabtaAdmin.css" type="text/css" rel="stylesheet">
		<LINK href="../Styles4.css" type="text/css" rel="stylesheet">
		<script language="javascript">
		function CallWindow(param)
		{
		 var Val=param;
		 window.showModalDialog("projectedit.aspx?id="+Val,param, "dialogWidth:1000px; dialogHeight:900px; center:yes; status:no");
		 return false;
		}
		function CallDraftWindow(param)
		{
		 var Val=param;
		 window.showModalDialog("projectdraftedit.aspx?id="+Val,param, "dialogWidth:1000px; dialogHeight:900px; center:yes; status:no");
		 return false;
		}
		</script>
		<script language="javascript" id="clientEventHandlersJS">
<!--

function window_onload() {
document.body.scrollTop=document.getElementById("txtX").value;
document.body.scrollLeft=document.getElementById("txtY").value;
}

function window_onscroll() {
document.getElementById("txtX").value=document.body.scrollTop;
document.getElementById("txtY").value=document.body.scrollLeft;
}

//-->
		</script>
	</HEAD>
	<body language="javascript" dir="ltr" onscroll="return window_onscroll()" bottomMargin="0"
		leftMargin="0" topMargin="0" onload="return window_onload()" rightMargin="0">
		<form id="myForm" name="MyForm" runat="server">
			<table height="100%" cellSpacing="0" cellPadding="0" width="750" align="center" bgColor="#ffffff"
				border="0">
				<tr>
					<td vAlign="middle" align="left" height="69">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="69" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="1826">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top1.swf">
							<PARAM NAME="Src" VALUE="flash/Top1.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top1.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="69"> </embed>
						</OBJECT>
					</td>
				</tr>
				<tr>
					<td height="10">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="10" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" VIEWASTEXT>
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="265">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top2.swf">
							<PARAM NAME="Src" VALUE="flash/Top2.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top2.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="10"> </embed>
						</OBJECT>
					</td>
				</tr>
				<TR>
					<TD class="PageTitle" height="20">Geo Raabta :: Project Management</TD>
				</TR>
				<tr>
					<td background="../images/menu-off-bg.gif" height="20"><uc1:mymenus id="MyMenus1" runat="server"></uc1:mymenus></td>
				</tr>
				<tr>
					<td class="MainBG" vAlign="top" align="left">
						<P>
							<TABLE id="Table1" cellSpacing="1" cellPadding="1" width="100%" border="0">
								<TR>
									<TD><asp:imagebutton id="imgMyTaskSheet" runat="server" ImageUrl="..\images\MyIssue&amp;Tasksheets.jpg"></asp:imagebutton><asp:imagebutton id="imgNewProject" runat="server" ImageUrl="../images/CreateNewProject.jpg"></asp:imagebutton><asp:imagebutton id="imgcCGrievance" runat="server" ImageUrl="../images/MyTeamProjects.jpg"></asp:imagebutton><asp:imagebutton id="imgDraft" runat="server" ImageUrl="..\images\draftprojects.gif"></asp:imagebutton><asp:imagebutton id="imgBack" runat="server" ImageUrl="..\images\back.gif"></asp:imagebutton></TD>
								</TR>
								<TR>
									<TD class="Menuar" id="rDraft" style="HEIGHT: 20px" runat="server">
										<TABLE id="Table5" cellSpacing="1" cellPadding="1" width="100%" border="1">
											<TR>
												<TD class="MenuBar">
													<P align="left"><FONT size="3"><STRONG>Draft 
																Projects&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
																&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
																&nbsp;<asp:linkbutton id="lnkCloseDraft" runat="server">Close</asp:linkbutton></STRONG></FONT></P>
												</TD>
											</TR>
											<TR>
												<TD width="100%"><STRONG><asp:datagrid id="dgProjectDraft" runat="server" BorderColor="#FFC0C0" Width="100%" AutoGenerateColumns="False">
															<Columns>
																<asp:BoundColumn Visible="False" DataField="pid" HeaderText="Project ID"></asp:BoundColumn>
																<asp:BoundColumn DataField="title" HeaderText="Title"></asp:BoundColumn>
																<asp:BoundColumn DataField="description" HeaderText="Description"></asp:BoundColumn>
																<asp:BoundColumn DataField="scope" HeaderText="Scope"></asp:BoundColumn>
																<asp:BoundColumn DataField="deliverables" HeaderText="Deliverables"></asp:BoundColumn>
																<asp:BoundColumn DataField="durationfrom" HeaderText="Duration (from)" DataFormatString="{0:d MMM,yyyy}"></asp:BoundColumn>
																<asp:BoundColumn DataField="durationto" HeaderText="Duration (to)" DataFormatString="{0:d MMM,yyyy}"></asp:BoundColumn>
																<asp:BoundColumn DataField="totalworkingdays" HeaderText="Total Working Day(s)"></asp:BoundColumn>
																<asp:TemplateColumn HeaderText="Edit Project">
																	<ItemTemplate>
																		<asp:LinkButton id="lnkEditDraft" runat="server">Edit Project</asp:LinkButton>
																	</ItemTemplate>
																</asp:TemplateColumn>
															</Columns>
														</asp:datagrid></STRONG></TD>
											</TR>
										</TABLE>
									</TD>
								</TR>
								<TR>
									<TD class="OrangeFormTitle">My Projects</TD>
								</TR>
								<TR>
									<TD><asp:datagrid id="dgProjects" runat="server" BorderColor="#FFC0C0" Width="100%" AutoGenerateColumns="False">
											<Columns>
												<asp:BoundColumn DataField="pid" HeaderText="Project ID"></asp:BoundColumn>
												<asp:BoundColumn DataField="title" HeaderText="Title"></asp:BoundColumn>
												<asp:BoundColumn DataField="description" HeaderText="Description"></asp:BoundColumn>
												<asp:BoundColumn DataField="scope" HeaderText="Scope"></asp:BoundColumn>
												<asp:BoundColumn DataField="deliverables" HeaderText="Deliverables"></asp:BoundColumn>
												<asp:BoundColumn DataField="durationfrom" HeaderText="Duration (from)" DataFormatString="{0:d MMM,yyyy}"></asp:BoundColumn>
												<asp:BoundColumn DataField="durationto" HeaderText="Duration (to)" DataFormatString="{0:d MMM,yyyy}"></asp:BoundColumn>
												<asp:BoundColumn DataField="totalworkingdays" HeaderText="Total Working Day(s)"></asp:BoundColumn>
												<asp:BoundColumn HeaderText="Status"></asp:BoundColumn>
												<asp:TemplateColumn HeaderText="Edit Project">
													<ItemTemplate>
														<asp:LinkButton id="lnkEdit" runat="server">Edit Project</asp:LinkButton>
													</ItemTemplate>
												</asp:TemplateColumn>
												<asp:ButtonColumn Text="Team Progress" CommandName="Select"></asp:ButtonColumn>
												<asp:TemplateColumn>
													<ItemTemplate>
														<asp:LinkButton id="lnkStatus" onclick="Updates" runat="server">Update Status</asp:LinkButton>
													</ItemTemplate>
												</asp:TemplateColumn>
												<asp:EditCommandColumn Visible="False" ButtonType="LinkButton" UpdateText="Update" CancelText="Cancel"
													EditText="Edit Project"></asp:EditCommandColumn>
												<asp:BoundColumn Visible="False" DataField="frequency"></asp:BoundColumn>
												<asp:BoundColumn Visible="False" DataField="teamleader" HeaderText="teamlead"></asp:BoundColumn>
												<asp:TemplateColumn>
													<ItemTemplate>
														<asp:LinkButton id="lnkRemove" runat="server" OnClick="Remove">Remove Team Project</asp:LinkButton>
													</ItemTemplate>
												</asp:TemplateColumn>
											</Columns>
										</asp:datagrid></TD>
								</TR>
								<TR>
									<TD id="r1" runat="server"><FONT size="1">
											<TABLE id="Table2" cellSpacing="1" cellPadding="1" width="100%" border="1">
												<TR>
													<TD class="MenuBar"><FONT size="3"><STRONG>Team Progress:: Project ID:
																<asp:label id="Label1" runat="server"></asp:label>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
																<asp:linkbutton id="lnkCloseTeamProgress" runat="server">Close</asp:linkbutton></STRONG></FONT></TD>
												</TR>
												<TR>
													<TD><asp:datagrid id="dgTask" runat="server" BorderColor="#FFC0C0" Width="100%" AutoGenerateColumns="False"
															Height="88px">
															<Columns>
																<asp:BoundColumn DataField="title" HeaderText="Task Title"></asp:BoundColumn>
																<asp:BoundColumn DataField="tdes" HeaderText="Description"></asp:BoundColumn>
																<asp:BoundColumn Visible="False" DataField="tid"></asp:BoundColumn>
																<asp:BoundColumn DataField="durationfrom" HeaderText="Duration (from)" DataFormatString="{0:d MMM,yyyy}"></asp:BoundColumn>
																<asp:BoundColumn DataField="durationto" HeaderText="Duration (to)" DataFormatString="{0:d MMM,yyyy}"></asp:BoundColumn>
																<asp:TemplateColumn HeaderText="Responsible"></asp:TemplateColumn>
																<asp:BoundColumn HeaderText="Status"></asp:BoundColumn>
															</Columns>
														</asp:datagrid></TD>
												</TR>
												<TR>
													<TD id="EditProject" runat="server"><IFRAME id="frmDetail" name="frmDetail" frameBorder="0" width="100%" scrolling="auto" height="200"
															runat="server"></IFRAME>
													</TD>
												</TR>
											</TABLE>
										</FONT>
									</TD>
								</TR>
								<TR>
									<TD class="MenuBar" id="r2" runat="server"><STRONG><FONT size="3">Edit 
												Project&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
												<asp:linkbutton id="lnkClose" runat="server">Close</asp:linkbutton></FONT></STRONG></TD>
								</TR>
								<TR>
									<TD id="r4" runat="server"><asp:label id="Label2" runat="server"></asp:label></TD>
								</TR>
								<TR>
									<TD></TD>
								</TR>
								<TR>
									<TD id="r3" runat="server"><STRONG><FONT size="3">
												<TABLE id="Table3" cellSpacing="1" cellPadding="1" width="70%" border="1">
													<TR>
														<TD class="MenuBar" colSpan="2"><STRONG><FONT size="2"><STRONG><FONT size="3">Update Project Status</FONT></STRONG></FONT></STRONG></TD>
													</TR>
													<TR>
														<TD><STRONG>Current Status:</STRONG></TD>
														<TD><asp:textbox id="txtcStatus" runat="server" Width="359px" Height="70px" TextMode="MultiLine"
																CssClass="textbox"></asp:textbox></TD>
													</TR>
													<TR>
														<TD style="HEIGHT: 21px"><STRONG>Next Step:</STRONG></TD>
														<TD style="HEIGHT: 21px"><asp:textbox id="txtnStep" runat="server" Width="359px" Height="72px" TextMode="MultiLine" CssClass="textbox"></asp:textbox></TD>
													</TR>
													<TR>
														<TD style="HEIGHT: 43px"><STRONG>Upload File:</STRONG></TD>
														<TD style="HEIGHT: 43px"><INPUT id="FileToUpload" style="WIDTH: 360px; HEIGHT: 22px" type="file" size="40" name="File1"
																runat="server"></TD>
													</TR>
													<TR>
														<TD colSpan="2"><asp:button id="btnSubmit" runat="server" CssClass="button" Text="Submit"></asp:button><asp:button id="btnCancel" runat="server" CssClass="button" Text="Cancel"></asp:button></TD>
													</TR>
												</TABLE>
											</FONT></STRONG>
									</TD>
								</TR>
								<TR>
									<TD class="MenuBar" id="r5" runat="server"><STRONG><FONT id="r5" size="2">Project Progress</FONT></STRONG></TD>
								</TR>
								<TR>
									<TD id="r6" runat="server"><asp:datagrid id="dgProjectStatus" runat="server" BorderColor="#FFC0C0" Width="100%" AutoGenerateColumns="False">
											<Columns>
												<asp:ButtonColumn Visible="False" Text="Delete" CommandName="Delete"></asp:ButtonColumn>
												<asp:BoundColumn DataField="cstatus" HeaderText="Current Status"></asp:BoundColumn>
												<asp:BoundColumn DataField="nstep" HeaderText="Next Step"></asp:BoundColumn>
												<asp:BoundColumn DataField="update" HeaderText="Update On " DataFormatString="{0:d MMM,yyyy h:mm:ss tt}"></asp:BoundColumn>
												<asp:BoundColumn DataField="file" HeaderText="Attachment"></asp:BoundColumn>
												<asp:BoundColumn Visible="False" DataField="uid"></asp:BoundColumn>
												<asp:BoundColumn Visible="False" DataField="filename"></asp:BoundColumn>
												<asp:BoundColumn DataField="remarks" HeaderText="Remarks"></asp:BoundColumn>
												<asp:BoundColumn DataField="rupdateon" HeaderText="Remarks Date"></asp:BoundColumn>
												<asp:BoundColumn DataField="remarks by" HeaderText="Remarks By"></asp:BoundColumn>
											</Columns>
										</asp:datagrid></TD>
								</TR>
								<TR>
									<TD id="MyTask" runat="server">
										<TABLE id="Table4" cellSpacing="1" cellPadding="1" width="100%" border="1">
											<TR>
												<TD class="OrangeFormTitle">My Tasks</TD>
											</TR>
											<TR>
												<TD><IFRAME name="frmTask" src="ProjectTask.aspx" frameBorder="0" width="100%" height="300"></IFRAME>
												</TD>
											</TR>
										</TABLE>
										<asp:textbox id="txtX" style="VISIBILITY: hidden" runat="server" Width="24px" CssClass="textbox"></asp:textbox><asp:textbox id="txtY" style="VISIBILITY: hidden" runat="server" Width="21px" CssClass="textbox"></asp:textbox></TD>
								</TR>
							</TABLE>
						</P>
					</td>
				</tr>
				<tr>
					<td vAlign="middle" align="center" height="20">Copyright © 2009 Independent Media 
						Corporation <A href="http://www.geo.tv">www.geo.tv</A></td>
				</tr>
			</table>
		</form>
	</body>
</HTML>
