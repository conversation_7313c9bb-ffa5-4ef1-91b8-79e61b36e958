<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<script language="javascript" src="../../tiny_mce_popup.js"></script>
<script type="text/javascript" src="jscripts/embed.js"></script>
<script type="text/javascript">
tinyMCEPopup.onInit.add(function(ed) {
	var dom = tinyMCEPopup.dom;

	// Load editor content_css
	tinymce.each(ed.settings.content_css.split(','), function(u) {
		dom.loadCSS(ed.documentBaseURI.toAbsolute(u));
	});

	// Place contents inside div container
	dom.setHTML('content', ed.getContent());
});
</script>
<title>Example of a custom preview page</title>
</head>
<body>

Editor contents: <br />
<div id="content">
<!-- Gets filled with editor contents -->
</div>

</body>
</html>
