<%@ Register TagPrefix="uc1" TagName="myMenus" Src="myMenus.ascx" %>
<%@ Page CodeBehind="City.aspx.cs" Language="c#" AutoEventWireup="false" Inherits="GeoRabtaSite.GeoCity" smartNavigation="True"%>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Rabta :: Station Setup</title>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="RaabtaAdmin.css" type="text/css" rel="stylesheet">
		<script language="javascript" id="clientEventHandlersJS">
<!--

function window_onload() {
	t=window.document.getElementById('txtName');
	if(t)
		t.focus();
}

//-->
		</script>
	</HEAD>
	<body language="javascript" dir="ltr" bottomMargin="0" bgProperties="fixed" leftMargin="0"
		topMargin="0" onLoad="return window_onload()" rightMargin="0">
		<form id="myForm" name="MyForm" runat="server">
			<table height="100%" cellSpacing="0" cellPadding="0" width="750" align="center" bgColor="#ffffff"
				border="0">
				<TR>
					<TD vAlign="top" align="left" height="69">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="69" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="1826">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top1.swf">
							<PARAM NAME="Src" VALUE="flash/Top1.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<embed src="flash/Top1.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="69"> </embed>
						</OBJECT>
					</TD>
				</TR>
				<tr>
					<td height="10">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="10" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="265">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top2.swf">
							<PARAM NAME="Src" VALUE="flash/Top2.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<embed src="flash/Top2.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="10"> </embed>
						</OBJECT>
					</td>
				</tr>
				<TR>
					<TD height="20" class="PageTitle">Geo Raabta Admin :: Station Setup</TD>
				</TR>
				<tr>
					<td height="20" background="../images/menu-off-bg.gif"><uc1:mymenus id="MyMenus1" runat="server"></uc1:mymenus></td>
				</tr>
				<tr>
					<td vAlign="top" align="left" class="MainBG"><STRONG><FONT size="4"><BR>
								<TABLE class="MainFormColor" id="tdFrom2" cellSpacing="0" cellPadding="3" width="90%" align="center"
									border="0" runat="server">
									<TR>
										<TD class="OrangeFormTitle" colSpan="3">Station</TD>
									</TR>
									<TR>
										<TD width="320"><asp:label id="Label2" runat="server" Font-Bold="True">Name</asp:label><asp:requiredfieldvalidator id="RequiredFieldValidator1" runat="server" ControlToValidate="txtName" Display="Dynamic"
												ErrorMessage="* Station name is required"></asp:requiredfieldvalidator>
											<asp:RegularExpressionValidator id="RegularExpressionValidator1" runat="server" ErrorMessage="Special Character Not Allowed"
												ControlToValidate="txtName" ValidationExpression="^[ a-z A-Z 0-9 ( ),.-]*$">*</asp:RegularExpressionValidator><BR>
											<asp:textbox id="txtName" runat="server" CssClass="textbox" Width="100%"></asp:textbox></TD>
										<TD></TD>
										<TD width="320"><asp:label id="Label3" runat="server" Font-Bold="True">Country</asp:label><asp:requiredfieldvalidator id="RequiredFieldValidator2" runat="server" ControlToValidate="ddCountry" Display="Dynamic"
												ErrorMessage="* Country is required"></asp:requiredfieldvalidator><BR>
											<asp:dropdownlist id="ddCountry" runat="server" CssClass="TextBox" Width="100%"></asp:dropdownlist></TD>
									</TR>
									<TR>
										<TD colSpan="3">
											<asp:button id="btnQueryButton" tabIndex="4" runat="server" Width="80px" Text="Save"></asp:button>
											<asp:button id="btnCancel" tabIndex="5" runat="server" Width="79px" Text="Cancel" CausesValidation="False"></asp:button></TD>
									</TR>
								</TABLE>
							</FONT></STRONG>
						<BR>
						<TABLE id="Table2" cellSpacing="0" cellPadding="3" width="90%" align="center" border="0"
							runat="server">
							<TR>
								<TD width="320">
									<asp:LinkButton id="lnkAddNewStation" runat="server" CausesValidation="False">Add New Station</asp:LinkButton></TD>
								<TD width="120"></TD>
								<TD width="320"></TD>
							</TR>
							<TR>
								<TD colSpan="3">
									<asp:datagrid id="DataGrid1" runat="server" Width="100%" BorderStyle="Solid" GridLines="Horizontal"
										CellPadding="3" BackColor="White" BorderWidth="1px" BorderColor="Navy" DataKeyField="Id" PageSize="2"
										AutoGenerateColumns="False">
										<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
										<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
										<AlternatingItemStyle CssClass="AlternetItem"></AlternatingItemStyle>
										<ItemStyle ForeColor="#4A3C8C" CssClass="item"></ItemStyle>
										<HeaderStyle Font-Bold="True" ForeColor="#F7F7F7" CssClass="header"></HeaderStyle>
										<Columns>
											<asp:BoundColumn Visible="False" DataField="id" HeaderText="ID"></asp:BoundColumn>
											<asp:BoundColumn DataField="CityName" SortExpression="CityName" HeaderText="City Name"></asp:BoundColumn>
											<asp:BoundColumn DataField="Country" SortExpression="Country" HeaderText="Country"></asp:BoundColumn>
											<asp:EditCommandColumn ButtonType="LinkButton" UpdateText="Update" CancelText="Cancel" EditText="Edit">
												<ItemStyle HorizontalAlign="Center" Width="40px" VerticalAlign="Middle"></ItemStyle>
											</asp:EditCommandColumn>
											<asp:TemplateColumn>
												<ItemStyle HorizontalAlign="Center" Width="40px" VerticalAlign="Middle"></ItemStyle>
												<ItemTemplate>
													<asp:LinkButton id="lbDelete" runat="server" CausesValidation="False" OnClick="deleteCity">Delete</asp:LinkButton>
												</ItemTemplate>
											</asp:TemplateColumn>
										</Columns>
										<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
									</asp:datagrid></TD>
							</TR>
						</TABLE>
						<asp:Label id="lblMsg" runat="server" Font-Bold="True" Visible="False" ForeColor="Black"></asp:Label>
						<asp:panel id="Panel1" runat="server" Visible="False">
							<asp:label id="Label1" runat="server" Font-Bold="True" Visible="False">Id</asp:label>
							<asp:TextBox id="txtDesId" runat="server" Width="300px" CssClass="textbox" Visible="False" ReadOnly="True"></asp:TextBox>
						</asp:panel></td>
				</tr>
				<tr>
					<td vAlign="middle" align="center" height="15">Copyright © 2005 Independent Media 
						Corporation <A href="http://www.geo.tv">www.geo.tv</A></td>
				</tr>
			</table>
		</form>
	</body>
</HTML>
