using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Configuration;
using System.Data.SqlClient;
using System.IO;
namespace GeoRabtaSite.admin
{
	/// <summary>
	/// Summary description for _default.
	/// </summary>
	public class ProjectManage : System.Web.UI.Page
	{
		protected System.Web.UI.WebControls.DataGrid dgProjects;
		protected System.Web.UI.HtmlControls.HtmlTableCell r2;
		protected System.Web.UI.HtmlControls.HtmlTableCell r3;
		protected System.Web.UI.WebControls.Button btnCancel;
		protected System.Web.UI.WebControls.Button btnSubmit;
		protected System.Web.UI.WebControls.TextBox txtnStep;
		protected System.Web.UI.WebControls.TextBox txtcStatus;
		protected System.Web.UI.HtmlControls.HtmlInputFile FileToUpload;
		protected System.Web.UI.WebControls.Label Label2;
		protected System.Web.UI.HtmlControls.HtmlTableCell r4;
		protected System.Web.UI.WebControls.DataGrid dgProjectStatus;
		protected System.Web.UI.HtmlControls.HtmlTableCell r5;
		protected System.Web.UI.HtmlControls.HtmlTableCell r6;
		protected System.Web.UI.HtmlControls.HtmlTableCell MyTask;
		protected System.Web.UI.WebControls.LinkButton lnkClose;
		protected System.Web.UI.WebControls.Label Label1;
		protected System.Web.UI.WebControls.DataGrid dgTask;
		protected System.Web.UI.HtmlControls.HtmlTableCell r1;
		protected System.Web.UI.HtmlControls.HtmlTableCell EditProject;
		protected System.Web.UI.HtmlControls.HtmlGenericControl frmDetail;
		protected System.Web.UI.WebControls.LinkButton lnkCloseTeamProgress;
		protected System.Web.UI.WebControls.ImageButton imgcCGrievance;
		protected System.Web.UI.WebControls.ImageButton imgMyTaskSheet;
		protected System.Web.UI.WebControls.ImageButton imgNewProject;
		protected System.Web.UI.WebControls.ImageButton imgBack;
		protected System.Web.UI.WebControls.ImageButton imgDraft;
		protected System.Web.UI.HtmlControls.HtmlTableCell rDraft;
		protected System.Web.UI.WebControls.LinkButton lnkCloseDraft;
		protected System.Web.UI.WebControls.DataGrid dgProjectDraft;
        SqlConnection con=new SqlConnection(Connection.ConnectionString);
		protected System.Web.UI.WebControls.TextBox txtY;
		protected System.Web.UI.WebControls.TextBox txtX;
		string userId="";
		private bool IsPageAccessAllowed()
		{
					
			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception) 
			{
				Response.Redirect("../notavailable.htm");
			}
			if(userId!="")
			{
			 return true;
			}
			else
			{
			 return false;
			}

//			if(userId!="")
//			{
//				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"View")==true)
//				{
//					return true;
//				}
//				else
//				{
//					return false;
//				}
//			}
//			else
//			{
//				Response.Redirect("../notavailable.htm");
//				return false;
//			}
		}
		private void Page_Load(object sender, System.EventArgs e)
		{
			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			// Put user code to initialize the page here
			if(IsPageAccessAllowed())
			{
			 
			}
			else
			{
				Response.Redirect("../notavailable.htm");
			}
			if(!IsPostBack)
			{
				r1.Visible=false;
				r2.Visible=false;
				r3.Visible=false;
				r5.Visible=false;
				r6.Visible=false;
				r4.Visible=false;
				rDraft.Visible=false;
				GetMyProjects();
			}
		}
		public void GetMyProjects()
		{
		 /*string Query="SELECT pid, title, description, scope, deliverables, durationfrom,durationto,case frequency when 1 then cast(totalworkingdays as varchar) else 'Continuous' end as totalworkingdays "+
         "FROM  dbo.t_GeoProjects "+
         "WHERE (teamleader = '"+Session["user_id"].ToString()+"') OR "+
         "(pid IN (SELECT pid FROM dbo.t_GeoProjectsOwnerList WHERE (powner = '"+Session["user_id"].ToString()+"'))) ";*/
		 string Query="SELECT pid, title, description, scope, deliverables, durationfrom, durationto, "+
         "CASE frequency WHEN 1 THEN CAST(totalworkingdays AS varchar) ELSE 'Continuous' END AS totalworkingdays,frequency,teamleader "+
         "FROM dbo.t_GeoProjects "+
         "WHERE (teamleader = '"+Session["user_id"].ToString()+"') AND (frequency = 2) AND(dbo.t_GeoProjects.status=1) OR "+
         "(pid IN (SELECT  pid FROM  dbo.t_GeoProjectsOwnerList WHERE (powner = '"+Session["user_id"].ToString()+"') AND isactive=1 AND ishown=1 AND (pid = dbo.t_GeoProjects.pid) AND (dbo.t_GeoProjects.frequency = 2)) and dbo.t_GeoProjects.status=1) ORDER BY title";
		 SqlDataAdapter dr=new SqlDataAdapter(Query,con);
		 DataSet ds=new DataSet();
		 dr.Fill(ds,"Records");

		 Query="SELECT pid, title, description, scope, deliverables, durationfrom, durationto, "+ 
         "CASE frequency WHEN 1 THEN CAST(totalworkingdays AS varchar) ELSE 'Continuous' END AS totalworkingdays,frequency,teamleader "+
         "FROM dbo.t_GeoProjects "+
         "WHERE (teamleader = '"+Session["user_id"].ToString()+"') AND (frequency = 1) AND(dbo.t_GeoProjects.status=1) OR "+
         "(pid IN (SELECT pid FROM dbo.t_GeoProjectsOwnerList WHERE (powner = '"+Session["user_id"].ToString()+"') AND isactive=1 AND ishown=1 AND (pid = dbo.t_GeoProjects.pid) AND (dbo.t_GeoProjects.frequency = 1))and dbo.t_GeoProjects.status=1) ORDER BY durationto"; 
		 dr=new SqlDataAdapter(Query,con);
		 dr.Fill(ds,"Records");
		 this.dgProjects.DataSource=ds;
		 this.dgProjects.DataBind();
			for(int i=0;i<this.dgProjects.Items.Count;i++)
			{
				LinkButton btn=(LinkButton)dgProjects.Items[i].FindControl("lnkEdit");
				btn.Attributes.Add("onclick","return CallWindow('"+dgProjects.Items[i].Cells[0].Text+"');"); 	
			}
			for(int i=0;i<this.dgProjects.Items.Count;i++)
			{
				CalculateStatus(this.dgProjects.Items[i].Cells[0].Text,i,this.dgProjects.Items[i].Cells[13].Text);
			}
			RemoveProject();
		}
		public void RemoveProject()
		{
			for(int i=0;i<dgProjects.Items.Count;i++)
			{
				if(this.dgProjects.Items[i].Cells[14].Text.Trim().Equals(Session["user_id"].ToString()))
				{
					LinkButton lnk=(LinkButton)this.dgProjects.Items[i].FindControl("lnkRemove");
					lnk.Visible=false;
				}
				else
				{
				 LinkButton lnk=(LinkButton)this.dgProjects.Items[i].FindControl("lnkRemove");
				 lnk.Attributes.Add("onclick","return confirm('Are you sure to remove your team project from your Issue & Task Sheet?')");
				}
			}
		}
		public void Remove(object sender,EventArgs e)
		{
		 LinkButton btn=(LinkButton)sender;
			for(int i=0;i<this.dgProjects.Items.Count;i++)
			{
			 LinkButton stn=(LinkButton)this.dgProjects.Items[i].FindControl("lnkRemove");
				if(stn.Equals(btn))
				{
					con.Open();
					SqlCommand cmd=new SqlCommand("update t_geoprojectsownerlist set isactive=0,ishown=0 where pid='"+this.dgProjects.Items[i].Cells[0].Text.Trim()+"' and powner='"+Session["user_id"].ToString()+"'",con);
					cmd.ExecuteNonQuery();
					con.Close();
					GetMyProjects();
					return;
				}
			}
		}
		public void CalculateStatus(string pid,int indx,string frequency)
		{
			if(frequency=="1")
			{
				con.Open();
				SqlCommand cmd=new SqlCommand("select tid,weightage from t_geoprojectstask where pid='"+pid+"' and status=1",con);
				ArrayList list=new ArrayList();
				SqlDataReader rd=cmd.ExecuteReader();
				while(rd.Read())
				{
					list.Add(rd[0].ToString()+"^"+rd[1].ToString());
				}
				rd.Close();
				if(list.Count<=0)
				{
					this.dgProjects.Items[indx].Cells[8].Text="Task Not Created";
				}
				else
				{
					int percent=0;
					for(int i=0;i<list.Count;i++)
					{
						string []taskid=list[i].ToString().Split('^');
						string Query="select count(tid) from t_geoprojectsteamtasks where tid="+taskid[0]+" and isactive=1";
						cmd=new SqlCommand(Query,con);
						rd=cmd.ExecuteReader();
						rd.Read();
						if(rd.HasRows)
						{
							int count=Int32.Parse(rd[0].ToString());
							rd.Close();
							cmd=new SqlCommand("select count(tid) from t_geoprojectsteamtasks where iscomplete=1 and isactive=1 and tid="+taskid[0]+"",con);
							rd=cmd.ExecuteReader();
							rd.Read();
							if(rd.HasRows)
							{
								int count2=Int32.Parse(rd[0].ToString());
								rd.Close();
								if(count==count2)
								{
									percent+=Int32.Parse(taskid[1]);
								}
							}
							this.dgProjects.Items[indx].Cells[8].Text=percent.ToString()+"%";
						}
						else
						{
							this.dgProjects.Items[indx].Cells[8].Text="Task Not Assigned to Anyone";
						}
					}
				}
				con.Close();
			}
			else
			{
				//this.dgProjects.Items[indx].Cells[7].Text="Continuous";
				con.Open();
				SqlCommand cmd=new SqlCommand("select tid from t_geoprojectstask where pid='"+pid+"' and status=1",con);
				ArrayList list=new ArrayList();
				SqlDataReader rd=cmd.ExecuteReader();
				while(rd.Read())
				{
					list.Add(rd[0].ToString());
				}
				rd.Close();
				if(list.Count<=0)
				{
					this.dgProjects.Items[indx].Cells[8].Text="Task Not Created";
				}
				else
				{
					double totalTask=0;
					double percent=0;
					cmd=new SqlCommand("select count(tid) from t_geoprojectstask where pid='"+pid+"' and status=1",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					totalTask=Int32.Parse(rd[0].ToString());
					rd.Close();
					for(int i=0;i<list.Count;i++)
					{
						string taskid=list[i].ToString();
						string Query="select count(tid) from t_geoprojectsteamtasks where tid="+taskid+" and isactive=1";
						cmd=new SqlCommand(Query,con);
						rd=cmd.ExecuteReader();
						rd.Read();
						if(rd.HasRows)
						{
							int count=Int32.Parse(rd[0].ToString());
							rd.Close();
							cmd=new SqlCommand("select count(tid) from t_geoprojectsteamtasks where iscomplete=1 and isactive=1 and tid="+taskid+"",con);
							rd=cmd.ExecuteReader();
							rd.Read();
							if(rd.HasRows)
							{
								int count2=Int32.Parse(rd[0].ToString());
								rd.Close();
								if(count==count2)
								{
									percent++;
								}
							}
							double status=(percent/totalTask)*100;
							status=Math.Round(status,2);
							this.dgProjects.Items[indx].Cells[8].Text=status.ToString()+"%";
						}
						else
						{
							this.dgProjects.Items[indx].Cells[8].Text="Task Not Assigned to Anyone";
						}
					}
				}
				con.Close();
			}
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.imgMyTaskSheet.Click += new System.Web.UI.ImageClickEventHandler(this.imgMyTaskSheet_Click);
			this.imgNewProject.Click += new System.Web.UI.ImageClickEventHandler(this.imgNewProject_Click);
			this.imgcCGrievance.Click += new System.Web.UI.ImageClickEventHandler(this.imgcCGrievance_Click);
			this.imgDraft.Click += new System.Web.UI.ImageClickEventHandler(this.imgDraft_Click);
			this.imgBack.Click += new System.Web.UI.ImageClickEventHandler(this.imgBack_Click);
			this.lnkCloseDraft.Click += new System.EventHandler(this.lnkCloseDraft_Click);
			this.dgProjects.EditCommand += new System.Web.UI.WebControls.DataGridCommandEventHandler(this.dgProjects_EditCommand);
			this.dgProjects.SelectedIndexChanged += new System.EventHandler(this.dgProjects_SelectedIndexChanged);
			this.lnkCloseTeamProgress.Click += new System.EventHandler(this.lnkCloseTeamProgress_Click);
			this.lnkClose.Click += new System.EventHandler(this.lnkClose_Click);
			this.btnSubmit.Click += new System.EventHandler(this.btnSubmit_Click);
			this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		private void dgProjects_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			r1.Visible=true;
			r2.Visible=false;
			r4.Visible=false;
			r3.Visible=false;
			r5.Visible=false;
			r6.Visible=false;
			Label1.Text=this.dgProjects.Items[this.dgProjects.SelectedIndex].Cells[0].Text;
			FillProjectasks(Label1.Text);
			Label2.Visible=false;
			frmDetail.Visible=false;
		}

		public void FillProjectasks(string pid)
		{
			con.Open();
			string Query="SELECT dbo.t_GeoProjectsTask.tid, dbo.t_GeoProjectsTask.title, dbo.t_GeoProjectsTask.tdes, dbo.t_GeoProjectsTask.durationfrom, "+ 
				"dbo.t_GeoProjectsTask.durationto "+
				"FROM dbo.t_GeoProjects INNER JOIN "+
				"dbo.t_GeoProjectsTask ON dbo.t_GeoProjects.pid = dbo.t_GeoProjectsTask.pid "+
				"WHERE (dbo.t_GeoProjectsTask.pid = '"+pid+"') and dbo.t_GeoProjectsTask.status=1";
			SqlDataAdapter dr=new SqlDataAdapter(Query,con);
			DataSet ds=new DataSet();
			dr.Fill(ds,"Records");
			this.dgTask.DataSource=ds;
			this.dgTask.DataBind();
			
			for(int i=0;i<this.dgTask.Items.Count;i++)
			{
				string tid=this.dgTask.Items[i].Cells[2].Text;
				Query="SELECT dbo.t_GeoProjectsTask.tid, dbo.t_GeoProjectsTask.pid, dbo.t_GeoProjectsTeamTasks.team, "+ 
					"CASE iscomplete WHEN 0 THEN 'Pending' WHEN 1 THEN 'Completed' ELSE ' ' END AS Status, dbo.t_GeoProjectsTeamTasks.cdate, "+
					"dbo.t_Employee.name "+
					"FROM dbo.t_GeoProjectsTask INNER JOIN "+
					"dbo.t_GeoProjectsTeamTasks ON dbo.t_GeoProjectsTask.tid = dbo.t_GeoProjectsTeamTasks.tid INNER JOIN "+
					"dbo.t_Employee ON dbo.t_GeoProjectsTeamTasks.team = dbo.t_Employee.pcode "+
					"WHERE (dbo.t_GeoProjectsTask.tid = "+tid+") and dbo.t_GeoProjectsTeamTasks.isactive=1";
				SqlCommand cmd=new SqlCommand(Query,con);
				SqlDataReader rd=cmd.ExecuteReader();
				while(rd.Read())
				{
					string pcode=rd["team"].ToString();
					Convertor c=new Convertor();
					string team=c.Pconvertor(rd["team"].ToString().Trim(),true);
					string id=c.Pconvertor(tid.ToString().Trim(),true);
					this.dgTask.Items[i].Cells[5].Text+="<a href='ProjectTaskDetail.aspx?id="+c.Pconvertor(rd["team"].ToString().Trim(),true)+"&task="+c.Pconvertor(tid.ToString().Trim(),true)+"' target=frmDetail><img border=0 src=../empthumbnail/"+rd["team"].ToString().Trim()+".jpg width=50>"+rd["name"].ToString().Trim()+"</a>("+rd["status"].ToString()+")<br/>";
				}
				rd.Close();
			}
			for(int i=0;i<this.dgTask.Items.Count;i++)
			{
				string taskid=this.dgTask.Items[i].Cells[2].Text;
				Query="select count(tid) from t_geoprojectsteamtasks where tid="+taskid+" and isactive=1";
				SqlCommand cmd=new SqlCommand(Query,con);
				SqlDataReader rd=cmd.ExecuteReader();
				rd.Read();
				if(rd.HasRows)
				{
					int count=Int32.Parse(rd[0].ToString());
					rd.Close();
					cmd=new SqlCommand("select count(tid) from t_geoprojectsteamtasks where iscomplete=1 and isactive=1 and tid="+taskid+"",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						int count2=Int32.Parse(rd[0].ToString());
						rd.Close();
						if(count==count2)
						{
							this.dgTask.Items[i].Cells[6].Text="Complete";
						}
						else
						{
						 this.dgTask.Items[i].Cells[6].Text="Pending";
						}
					}
					else
					{
					this.dgTask.Items[i].Cells[6].Text="Pending";
					}
				}
			}
			con.Close(); 
		}
		public void FillProgress(string pid)
		{
			//string Query="SELECT taskid, cstatus, nstep, updateon as[update],'<A href=ProjectFiles/' +filename+ '><IMG src=../Images/'+fileicon+' border=0 />' + actualname + '</A>' AS [file],uid,filename,remarks,rupdateon FROM dbo.t_GeoProjectsTaskUpdates WHERE (updateby = '"+Session["user_id"].ToString()+"') AND (taskid = "+taskid+") order by updateon desc";
			string Query="SELECT pid, cstatus, nstep, updateon as[update],'<A onclick='+CHAR(34)+'alert('+CHAR(39)+' To view document right click on file and select option Save Target As to download this file '+CHAR(39)+'); return false;'+CHAR(34)+' href=ProjectFiles/' +filename+ '><IMG src=../Images/'+fileicon+' border=0 />' + actualname + '</A>' AS [file],uid,filename,remarks,rupdateon,case rupdateby when rupdateby then(select e.name from t_employee e where e.pcode=dbo.t_GeoProjectUpdates.rupdateby)end as [Remarks By] FROM dbo.t_GeoProjectUpdates WHERE (updateby = '"+Session["user_id"].ToString()+"') AND (pid = '"+pid+"') order by updateon,rupdateon desc";
			SqlDataAdapter dr=new SqlDataAdapter(Query,con);
			DataSet ds=new DataSet();
			dr.Fill(ds,"Records");
			this.dgProjectStatus.DataSource=ds;
			this.dgProjectStatus.DataBind();
		} 
		public void Updates(object sender , EventArgs e)
		{
		 r3.Visible=true;
		 r5.Visible=true;
		 r6.Visible=true;
		 r1.Visible=false;
		 r2.Visible=false;
		 r4.Visible=false;
		 LinkButton rbtn=(LinkButton)sender;
			for(int i=0;i<this.dgProjects.Items.Count;i++)
			{
				LinkButton lnk=(LinkButton)this.dgProjects.Items[i].FindControl("lnkStatus");
				if(lnk.Equals(rbtn))
				{
					Label1.Text=this.dgProjects.Items[i].Cells[0].Text;
					FillProgress(this.dgProjects.Items[i].Cells[0].Text);
					return;
				}
			}
		}

		private void dgProjects_EditCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{
			r2.Visible=true;
			r4.Visible=true;
			r1.Visible=false;
			r3.Visible=false;
			r5.Visible=false;
			r6.Visible=false;
			Label2.Text="<iframe src=projectedit.aspx?id="+e.Item.Cells[0].Text+" height=800 width=100% scrolling=auto frameborder=0></iframe>";
		    Label2.Visible=true;
		}

		private void btnSubmit_Click(object sender, System.EventArgs e)
		{
			con.Open();
			SqlCommand cmd=new SqlCommand("insert into t_geoprojectupdates (pid,cstatus,nstep,updateon,updateby,filename,actualname,fileicon)values (@taskid,@cstatus,@nstep,getdate(),@updateby,@filename,@actualname,@fileicon)",con);
			SqlParameter _tid=new SqlParameter("@taskid",SqlDbType.VarChar);
			SqlParameter _cstatus=new SqlParameter("@cstatus",SqlDbType.Text);
			SqlParameter _nstep=new SqlParameter("@nstep",SqlDbType.Text);
			SqlParameter _updateby=new SqlParameter("@updateby",SqlDbType.Char);
			SqlParameter _filename=new SqlParameter("@filename",SqlDbType.VarChar);
			SqlParameter _actualname=new SqlParameter("@actualname",SqlDbType.VarChar);
			SqlParameter _fileicon=new SqlParameter("@fileicon",SqlDbType.VarChar);
			_tid.Value=this.Label1.Text;
			_cstatus.Value=this.txtcStatus.Text;
			_nstep.Value=this.txtnStep.Text;
			_updateby.Value=Session["user_id"].ToString();
			string filepath=Server.MapPath(@"ProjectFiles\");
			string GUID="";
			GUID=Guid.NewGuid().ToString();
			if((FileToUpload.PostedFile != null) && (FileToUpload.PostedFile.ContentLength > 0))
			{
				FileInfo f=new FileInfo(FileToUpload.PostedFile.FileName);
				string filename= f.Name;
				_filename.Value=GUID+f.Extension;
				_actualname.Value=filename;
				string ext=f.Extension;
				if(ext.Trim().ToLower()==".doc" || (ext.Trim().ToLower()==".docx"))
				{
					_fileicon.Value="word.jpg";
				}
				else if(ext.Trim().ToLower()==".xls" || (ext.Trim().ToLower()==".xlsx"))
				{
					_fileicon.Value="excel.jpg";
				}
				else if(ext.Trim().ToLower()==".ppt" || (ext.Trim().ToLower()==".pptx"))
				{
					_fileicon.Value="powerpoint.jpg";
				}
				else if(ext.Trim().ToLower()==".pdf")
				{
					_fileicon.Value="pdf.jpg";
				}
				else if(ext.Trim().ToLower()==".txt")
				{
					_fileicon.Value="notepad.jpg";
				}
				else if (ext.Trim().ToLower()==".jpg")
				{
					_fileicon.Value="jpg.jpg";
				}
				else if (ext.Trim().ToLower()==".gif")
				{
					_fileicon.Value="gif.jpg";
				}
			}
			else
			{
				_filename.Value=DBNull.Value;
				_actualname.Value=DBNull.Value;
				_fileicon.Value=DBNull.Value;
			}
			cmd.Parameters.Add(_tid);
			cmd.Parameters.Add(_cstatus);
			cmd.Parameters.Add(_nstep);
			cmd.Parameters.Add(_updateby);
			cmd.Parameters.Add(_filename);
			cmd.Parameters.Add(_actualname);
			cmd.Parameters.Add(_fileicon);
			int i=cmd.ExecuteNonQuery();
			if(i>0)
			{
				if((FileToUpload.PostedFile != null) && (FileToUpload.PostedFile.ContentLength > 0))
				{
					FileInfo f=new FileInfo(FileToUpload.PostedFile.FileName);
					string filename= GUID+f.Extension;
					FileToUpload.PostedFile.SaveAs(filepath + filename);
				}
			}
			FillProgress(Label1.Text);
			con.Close();
			this.txtcStatus.Text="";
			this.txtnStep.Text="";
		}

		private void btnCancel_Click(object sender, System.EventArgs e)
		{
			r3.Visible=false;
			r5.Visible=false;
			r6.Visible=false;
		}

		private void lnkClose_Click(object sender, System.EventArgs e)
		{
			r2.Visible=false;
			r4.Visible=false;
			r1.Visible=false;
			r3.Visible=false;
			r5.Visible=false;
			r6.Visible=false;
			Label2.Visible=false;
		}

		private void lnkCloseTeamProgress_Click(object sender, System.EventArgs e)
		{
			r1.Visible=false;
			r2.Visible=false;
			r4.Visible=false;
			r3.Visible=false;
			r5.Visible=false;
			r6.Visible=false;
		}

		private void imgMyTaskSheet_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("ProjectManage.aspx");
		}

		private void imgNewProject_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
		 Response.Redirect("Project.aspx");
		}

		private void imgcCGrievance_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
		 Response.Redirect("ProjectLineView.aspx");
		}

		private void imgBack_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("ManageRequest.aspx");
		}

		private void imgDraft_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			rDraft.Visible=true;
			GetDrafProjects();
		}

		private void lnkCloseDraft_Click(object sender, System.EventArgs e)
		{
			rDraft.Visible=false;
		}
         
		public void GetDrafProjects()
		{
		 con.Open();
		 string Query="SELECT pid, title, description, scope, deliverables, durationfrom, durationto, CASE frequency WHEN 1 THEN CAST(totalworkingdays AS varchar) "+ 
         "ELSE 'Continuous' END AS totalworkingdays "+
         "FROM dbo.t_GeoProjectsTemp "+
         "WHERE (teamleader = '"+Session["user_id"].ToString()+"')";
		 SqlDataAdapter dr=new SqlDataAdapter(Query,con);
		 DataSet ds=new DataSet();
		 dr.Fill(ds,"draft");
		 dgProjectDraft.DataSource=ds;
		 dgProjectDraft.DataBind();
		 con.Close();
		 con.Dispose();
			for(int i=0;i<this.dgProjectDraft.Items.Count;i++)
			{
				LinkButton btn=(LinkButton)dgProjectDraft.Items[i].FindControl("lnkEditDraft");
				btn.Attributes.Add("onclick","return CallDraftWindow('"+dgProjectDraft.Items[i].Cells[0].Text+"');"); 	
			}
		}
	}
}
