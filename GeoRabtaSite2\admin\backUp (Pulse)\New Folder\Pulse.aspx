<%@ Page CodeBehind="Pulse.aspx.cs" Language="c#" AutoEventWireup="false" Inherits="GeoRabtaSite.Pulse" validateRequest="false"%>
<%@ Register TagPrefix="uc1" TagName="myMenus" Src="myMenus.ascx" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Raabta Admin :: Pulse</title>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="RaabtaAdmin.css" type="text/css" rel="stylesheet">
		<LINK href="../Styles4.css" type="text/css" rel="stylesheet">
		<style type="text/css">.style1 { FONT-SIZE: 8px; FONT-WEIGHT: bold }
	.style2 { FONT-SIZE: 9pt; FONT-WEIGHT: bold }
	.hid { DISPLAY: none }
		</style>
		<script language="javascript">
		function ValidatePage()
		{
		 //alert(document.getElementById('r1'));		
		 if(document.getElementById('txtTitle').value=='')
		 {
		  alert('Please Enter Pulse Title');
		  document.getElementById('txtTitle').focus();
		  return false;
		 }
		 else if(document.getElementById('ddType').selectedIndex==0)
		 {
		  alert('Please Select Pulse Type');
		  document.getElementById('ddType').focus();
		  return false;
		 }
		 else if(document.getElementById('txtSadaText').value=='')
		 {
		  alert('Please Enter Description');
		  document.getElementById('divEdit').focus();
		  return false;
		 }
		 else if(document.getElementById('ddDept').selectedIndex==0)
		 {
		  alert('Please Select Department');
		  document.getElementById('ddDept').focus();
		  return false;
		 }
		 else if(document.getElementById('ddStation').selectedIndex==0)
		 {
		  alert('Please Select Station');
		  document.getElementById('ddStation').focus();
		  return false;
		 }
		 else if(document.getElementById('ddSendTo').selectedIndex==0)
		 {
		  alert('Please Select Send To Option');
		  document.getElementById('ddSendTo').focus();
		  return false;
		 }
		 else if(document.getElementById('r1')!=null)
		 {
		 var aControl=document.getElementById('chkReportingList');
		 var arrayOfCheckBoxes= aControl.getElementsByTagName("input");
		 var count=0;
		 for(var a=0;a<arrayOfCheckBoxes.length;a++)
		  {
		    if(arrayOfCheckBoxes[a].checked)
		    count++;
		  }
		   if(count==0)
		   {
		   window.alert('Please Select Team/Line To Send Pulse'); 
		   return false;
		   }
		 }
		 else
		 {
		  return true;
		 }
	 }
		</script>
		<script id="clientEventHandlersJS" language="javascript">
<!--

function divEdit_onpaste() {
	UpdateText('divEdit','txtDescription');
}

function divEdit_onkeyup() {
	UpdateText('divEdit','txtDescription');
}

function divEdit_onkeypress() {
	UpdateText('divEdit','txtDescription');
}

function window_onload() {
	UpdateHTML('txtDescription','divEdit');
}

function UpdateText(html, txt)
{
	var t=document.getElementById(txt);
	var h=document.getElementById(html);
	var s=document.getElementById("txtSadaText");
	if(t!=null && h!=null)
	{
		t.value=h.innerHTML;
		s.value=h.innerText;
	}
}
function UpdateHTML(txt, html)
{
	var t=document.getElementById(txt);
	var h=document.getElementById(html)
	if(t!=null && h!=null)
	{
		h.innerHTML=t.value
	}
}

function EditCommand(html, txt, cmd)
{
	var t=document.getElementById(txt);
	var h=document.getElementById(html)
	var s=document.getElementById("txtSadaText");
	//alert (html+" " +txt+" " + cmd);
	//alert(h);
	if(t!=null && h!=null)
	{
		//alert('ok');
		h.document.execCommand(cmd,false,null);
		t.value=h.innerHTML;
		s.value=h.innerText;
		h.focus()
	}
}

function ChangeFontSize(html, txt, size)
{
	var t=document.getElementById(txt);
	var h=document.getElementById(html)
	var s=document.getElementById("txtSadaText");
	
	if(t!=null && h!=null)
	{
		h.document.execCommand('FontSize', false, size);
		t.value=h.innerHTML;
		s.value=h.innerText;
		html.focus();
	}
}
function ddlFontSize_onchange() {
	var ddl =document.getElementById('ddlFontSize');
	var fs=ddl[ddl.selectedIndex].value;
	//alert(fs);
	ChangeFontSize('divEdit','txtDescription',fs);
}

//-->
		</script>
	</HEAD>
	<body dir="ltr" bottomMargin="0" bgProperties="fixed" leftMargin="0" topMargin="0" rightMargin="0"
		language="javascript" onload="return window_onload()">
		<form id="ideas" runat="server">
			<table height="100%" cellSpacing="0" cellPadding="0" width="780" align="center" bgColor="#ffffff"
				border="0">
				<tr>
					<td vAlign="middle" align="left" height="69">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" width="780" height="69">
							<PARAM NAME="_cx" VALUE="20637">
							<PARAM NAME="_cy" VALUE="1825">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top1.swf">
							<PARAM NAME="Src" VALUE="flash/Top1.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top1.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="69"> </embed>
						</OBJECT>
					</td>
				</tr>
				<tr>
					<td height="10">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" width="780" height="10">
							<PARAM NAME="_cx" VALUE="20637">
							<PARAM NAME="_cy" VALUE="264">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top2.swf">
							<PARAM NAME="Src" VALUE="flash/Top2.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top2.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="10"> </embed>
						</OBJECT>
					</td>
				</tr>
				<tr>
					<td class="PageTitle" height="20">Geo Raabta Admin :: Pulse</td>
				</tr>
				<TR>
					<TD class="MenuBar" height="20"><uc1:mymenus id="MyMenus1" runat="server"></uc1:mymenus></TD>
				</TR>
				<tr>
					<td class="MainBG" vAlign="top" align="left"><BR>
						<table class="MainFormColor" id="tabForm" cellSpacing="0" cellPadding="3" width="750" align="center"
							border="0" runat="server">
							<tr>
								<td class="OrangeFormTitle" width="320" colSpan="3">New Pulse</td>
							</tr>
							<TR>
								<TD width="320" colSpan="3">
									<asp:ImageButton id="imgBack" runat="server" ImageUrl="..\images\back.gif"></asp:ImageButton></TD>
							</TR>
							<TR>
								<TD class="MenuBar" width="320" colSpan="3"><STRONG>Title:</STRONG></TD>
							</TR>
							<TR>
								<TD width="320" colSpan="3"><asp:textbox id="txtTitle" runat="server" ToolTip="Pulse Title" MaxLength="50" Width="712px"
										CssClass="textbox"></asp:textbox></TD>
							</TR>
							<TR>
								<TD class="menuBar" colSpan="3"><STRONG>Pulse Type:</STRONG></TD>
							</TR>
							<TR>
								<TD colSpan="3"><asp:dropdownlist id="ddType" runat="server" Width="480px" CssClass="textbox">
										<asp:ListItem Value="0">Select--Pulse Type</asp:ListItem>
									</asp:dropdownlist></TD>
							</TR>
							<TR>
								<TD class="menuBar" colSpan="3"><STRONG>Description:</STRONG></TD>
							</TR>
							<TR>
								<TD valign="top" colSpan="3"><IMG language="javascript" class="Hand" id="btnBold" onclick="EditCommand('divEdit', 'txtDescription','Bold');"
										height="24" alt="Bold Selected Text" src="../scripts/bold.gif" width="25"><IMG class="Hand" id="btnItalic" onclick="EditCommand('divEdit', 'txtDescription','Italic');"
										height="24" alt="Italic Selected Text" src="../scripts/italic.gif" width="25"><IMG class="Hand" id="btnUnderline" onclick="EditCommand('divEdit', 'txtDescription','Underline');"
										height="24" alt="Underline Selected Text" src="../scripts/underline.gif" width="25"><IMG language="javascript" class="Hand" id="btnLeft" onclick="EditCommand('divEdit', 'txtDescription','justifyleft');"
										height="24" alt="Justify Left Selected Text" src="../scripts/left_just.gif" width="25"><IMG class="Hand" id="btnCenter" onclick="EditCommand('divEdit', 'txtDescription','justifycenter');"
										height="24" alt="Center Justify Selected Text" src="../scripts/centre.gif" width="25"><IMG language="javascript" class="Hand" id="btnRight" onclick="EditCommand('divEdit', 'txtDescription','justifyright');"
										height="24" alt="Justify Right Selected Text" src="../scripts/right_just.gif" width="25"><IMG language="javascript" class="Hand" id="btnUnorderdList" onclick="EditCommand('divEdit', 'txtDescription','insertunorderedlist');"
										height="24" alt="Bulites Selected Text" src="../scripts/unoderlist.gif" width="25"><IMG language="javascript" class="Hand" id="btnOrderdList" onclick="EditCommand('divEdit', 'txtDescription','insertorderedlist');"
										height="24" alt="Orderd List " src="../scripts/orderdlist.gif" width="25"><FONT size="2"><STRONG>Font 
											Size:</STRONG></FONT>
									<SELECT id="ddlFontSize" name="ddlFontSize" language="javascript" onchange="return ddlFontSize_onchange()"
										style="HEIGHT: 24px">
										<OPTION value="1" selected>1</OPTION>
										<OPTION value="2">2</OPTION>
										<OPTION value="3">3</OPTION>
										<OPTION value="4">4</OPTION>
										<OPTION value="5">5</OPTION>
										<OPTION value="6">6</OPTION>
										<OPTION value="7">7</OPTION>
									</SELECT>
								</TD>
							</TR>
							<TR>
								<TD colSpan="3">
									<DIV language="javascript" id="divEdit" oncontextmenu="return false" dir="ltr" contentEditable="true"
										style="BORDER-BOTTOM: black 1px solid; TEXT-ALIGN: left; BORDER-LEFT: black 1px solid; BACKGROUND-COLOR: white; WIDTH: 100%; FONT-FAMILY: Arial; DIRECTION: ltr; HEIGHT: 338px; FONT-SIZE: 12px; VERTICAL-ALIGN: baseline; OVERFLOW: auto; BORDER-TOP: black 1px solid; BORDER-RIGHT: black 1px solid"
										onpaste="return divEdit_onpaste()" onkeyup="return divEdit_onkeyup()" onkeypress="return divEdit_onkeypress()"></DIV>
									<asp:textbox id="txtDescription" runat="server" ToolTip="Pulse Description" Width="531px" TextMode="MultiLine"
										Rows="5" CssClass="hid"></asp:textbox>
									<asp:TextBox id="txtSadaText" runat="server" CssClass="hid"></asp:TextBox></TD>
							</TR>
							<TR>
								<TD class="menuBar" colSpan="3"><STRONG>Department:</STRONG> (For which Pulse is 
									generating)</TD>
							</TR>
							<TR>
								<TD colSpan="3"><asp:dropdownlist id="ddDept" runat="server" Width="480px" CssClass="textbox">
										<asp:ListItem Value="0">Select--Department</asp:ListItem>
									</asp:dropdownlist></TD>
							</TR>
							<TR>
								<TD class="menuBar" style="HEIGHT: 18px" colSpan="3"><STRONG>Station:</STRONG> (For 
									which Pulse is generating)</TD>
							</TR>
							<TR>
								<TD colSpan="3"><asp:dropdownlist id="ddStation" runat="server" Width="480px" CssClass="textbox">
										<asp:ListItem Value="0">Select--Station</asp:ListItem>
									</asp:dropdownlist></TD>
							</TR>
							<TR>
								<TD class="menuBar" colSpan="3"><STRONG>Pulse Send To:</STRONG></TD>
							</TR>
							<TR>
								<TD colSpan="3"><asp:dropdownlist id="ddSendTo" runat="server" Width="480px" CssClass="textbox" AutoPostBack="True">
										<asp:ListItem Value="0">Select--Send To</asp:ListItem>
										<asp:ListItem Value="1">Send to MyTeam</asp:ListItem>
										<asp:ListItem Value="2">Send to MyLine Manager</asp:ListItem>
										<asp:ListItem Value="3">Send to Both</asp:ListItem>
									</asp:dropdownlist></TD>
							</TR>
							<TR>
								<TD id="r1" colSpan="3" runat="server">
									<DIV style="WIDTH: 712px; HEIGHT: 126px; OVERFLOW: auto" ms_positioning="FlowLayout"><asp:label id="Label1" runat="server">Label</asp:label><STRONG>(Based 
											on Organogram)</STRONG>
										<asp:checkboxlist id="chkReportingList" runat="server"></asp:checkboxlist></DIV>
								</TD>
							</TR>
							<TR>
								<TD class="menubar" id="r2" colSpan="3" runat="server"><STRONG id="r2">Is Pulse Add To 
										Issue &amp; Task Sheet of&nbsp;above&nbsp;selected&nbsp;Team?:</STRONG></TD>
							</TR>
							<TR>
								<TD id="r3" colSpan="3" runat="server"><asp:dropdownlist id="ddAddToTS" runat="server" Width="480px" CssClass="textbox">
										<asp:ListItem Value="0">No</asp:ListItem>
										<asp:ListItem Value="1">Yes</asp:ListItem>
									</asp:dropdownlist></TD>
							</TR>
							<TR>
								<TD colSpan="3"><asp:button id="btnSend" runat="server" CssClass="button" Text="Send Pulse"></asp:button><asp:button id="btnNew" runat="server" CssClass="button" Text="New Pulse"></asp:button><BR>
									<asp:Label id="Label2" runat="server" Visible="False"></asp:Label></TD>
							</TR>
						</table>
					</td>
				</tr>
				<TR>
					<TD vAlign="middle" align="center" height="20">Copyright © 2005 Independent Media 
						Corporation <A href="http://www.geo.tv">www.geo.tv</A></TD>
				</TR>
			</table>
		</form>
	</body>
</HTML>
