<%@ Page language="c#" Codebehind="EmployeeReport.aspx.cs" AutoEventWireup="false" Inherits="GeoRabtaSite.EmployeeReport" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<HTML>
	<HEAD>
		<title>Employee Info :: </title>
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="C#" name="CODE_LANGUAGE">
		<meta content="JavaScript" name="vs_defaultClientScript">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="StyleSheet1.css" type="text/css" rel="stylesheet">
		<script language="javascript" id="clientEventHandlersJS">
<!--

function imgPrint_onclick() {
	window.print();
		
}

//-->
		</script>
	</HEAD>
	<body bottomMargin="0" leftMargin="0" topMargin="0" rightMargin="0">
		<form id="Form1" method="post" runat="server">
			<TABLE id="Table1" cellSpacing="0" cellPadding="3" width="650" align="left" border="0">
				<TR>
					<TD bgColor="lightgrey" align="center"><FONT size="5"><STRONG>Employee Profile</STRONG></FONT></TD>
					</TD></TR>
				<TR>
					<TD bgColor="#ffffff"><asp:image id="empPhoto" runat="server" Height="120px"></asp:image>&nbsp;
						<asp:label id="lblEmpInfoName2" runat="server" Font-Size="20pt" Font-Bold="True"></asp:label></TD>
				</TR>
				<TR>
					<TD bgColor="#a9a9a9" align="center"><STRONG><FONT size="4">Employee Information:</FONT></STRONG></TD>
				</TR>
				<TR>
					<TD>
						<asp:Panel id="Panel1" runat="server">
							<TABLE id="tabPersonal" cellSpacing="0" cellPadding="3" align="center" border="1">
								<TBODY>
									<TR vAlign="middle" align="left">
										<TD vAlign="top" align="left" width="127"><STRONG>Employee Code:</STRONG><SPAN style="mso-spacerun: yes">&nbsp;</SPAN></TD>
					</TD>
					</TD>
					<TD vAlign="top" width="179">
						<asp:label id="lblEmpInfoCode" runat="server" Width="100%"></asp:label></TD>
					<TD vAlign="top" align="left" width="142"><STRONG>Name:</STRONG><SPAN style="mso-spacerun: yes"><BR>
						</SPAN><FONT class="font5" size="1">(As on NIC)</FONT></TD>
					</TD>
					<TD vAlign="top" colSpan="1">
						<asp:label id="lblEmpInfoName" runat="server" Width="100%"></asp:label></TD>
				</TR>
				<TR vAlign="middle" align="left">
					<TD vAlign="top" align="left" width="127"><STRONG>Department:</STRONG></TD>
					</TD></TD>
					<TD vAlign="top" width="179">
						<asp:label id="lnlEmpInfoDept" runat="server" Width="100%"></asp:label></TD>
					<TD vAlign="top" align="left" width="142"><STRONG>Designation:</STRONG></TD>
					</TD>
					<TD vAlign="top" width="177">
						<asp:label id="lblEmpInfoDesignation" runat="server" Width="100%"></asp:label></TD>
				</TR>
				<TR>
					<TD vAlign="top" align="left" width="127"><STRONG>Functional Desination:</STRONG></TD>
					<TD vAlign="top" width="179">
						<asp:label id="lblFuncDes" runat="server" Width="100%"></asp:label></TD>
					<TD vAlign="top" align="left" width="142"><STRONG>Date Of Joining:<BR>
						</STRONG><FONT class="font5" size="1">(DD-MM-YYYY)</FONT></TD>
					<TD vAlign="top" width="177">
						<asp:label id="lblEmpInfoDOJ" runat="server"></asp:label></TD>
				</TR>
				<TR>
					<TD vAlign="top" align="left" width="127"><STRONG>Confirmation Due Date:</STRONG></TD>
					<TD vAlign="top" width="179">
						<asp:label id="lblConfirmationDueDate" runat="server" Width="100%"></asp:label></TD>
					<TD vAlign="top" align="left" width="142"><STRONG>Confirmation Date:</STRONG></TD>
					<TD vAlign="top" width="177">
						<asp:label id="lblConfirmationDate" runat="server" Width="100%"></asp:label></TD>
				</TR>
				<TR vAlign="middle" align="left">
					<TD vAlign="top" align="left" width="127"><STRONG>Station:</STRONG></TD>
					</TD>
					<TD vAlign="top" width="179">
						<asp:label id="lblEmpInfoCity" runat="server" Width="100%"></asp:label></TD>
					<TD vAlign="top" align="left" width="142">
						<FONT class="font5" size="1"><STRONG>Probation Period:<BR>
							</STRONG>(in Months)</FONT></TD>
					</TD>
					<TD vAlign="top" width="177">
						<asp:label id="lblProbationPriod" runat="server" Width="100%"></asp:label></TD>
				</TR>
				<TR vAlign="middle" align="left">
					<TD vAlign="top" align="left" width="127"><STRONG>Email Official:</STRONG></TD>
					</TD></TD>
					<TD vAlign="top" width="179">
						<asp:label id="lblEmpInfoEmail" runat="server" Width="100%"></asp:label></TD>
					<TD vAlign="top" align="left" width="142"><STRONG>Ext #:</STRONG>
					</TD>
					</TD>
					<TD vAlign="top" width="177">
						<asp:label id="lblEmpInfoExtension" runat="server" Width="100%"></asp:label></TD>
				</TR>
				<TR vAlign="middle" align="left">
					<TD vAlign="top" align="left" width="127"><STRONG>SESSI #:</STRONG><BR>
						<FONT class="font5" size="1">(If Available)</FONT></TD>
					</TD></TD>
					<TD vAlign="top" width="179">
						<asp:label id="lblEmpInfoSessi" runat="server" Width="100%" DESIGNTIMEDRAGDROP="915"></asp:label></TD>
					<TD vAlign="top" align="left" width="142"><STRONG>EOBI #:</STRONG><SPAN style="mso-spacerun: yes"><BR>
						</SPAN><FONT class="font5" size="1">(If Available)</FONT></TD>
					</TD>
					<TD vAlign="top" width="177">
						<asp:label id="lblEmpInfoEObi" runat="server" Width="100%"></asp:label></TD>
				</TR>
				<TR>
					<TD vAlign="top" align="left" width="127"><STRONG>Category&nbsp;#:</STRONG></TD>
					<TD vAlign="top" width="179">
						<asp:label id="lblCategory" runat="server" Width="100%" DESIGNTIMEDRAGDROP="915"></asp:label></TD>
					<TD vAlign="top" align="left" width="142">&nbsp;</TD>
					<TD vAlign="top" width="177">&nbsp;</TD>
				</TR>
				<TR>
					<TD vAlign="top" align="left" width="127"><STRONG>Location</STRONG></TD>
					<TD vAlign="top" colSpan="3">
						<asp:label id="lblLocation" runat="server" Width="100%" DESIGNTIMEDRAGDROP="915"></asp:label></TD>
				</TR>
			</TABLE>
			</asp:Panel></SPAN></TD></TR>
			<TR>
				<TD bgColor="#a9a9a9" align="center"><FONT size="4"><STRONG>Personal Information:</STRONG></FONT></TD>
				</FONT></TD></TR>
			<TR>
				<TD>
					<asp:Panel id="Panel2" runat="server">
						<TABLE id="Table5" cellSpacing="0" cellPadding="3" align="center" border="1">
							<TBODY>
								<TR vAlign="middle" align="left">
									<TD vAlign="top" width="127"><STRONG>Date Of Birth:<BR>
										</STRONG><FONT class="font5" size="1">(DD-MM-YYYY)</FONT>
									</TD>
				</TD>
				<TD vAlign="top" width="179">
					<asp:label id="lblDOB" runat="server" Width="100%"></asp:label></TD>
				<TD vAlign="top" align="left" width="142"><STRONG>Father Name:</STRONG></TD>
				</TD>
				<TD vAlign="top" width="176">
					<asp:label id="lblFName" runat="server" Width="100%"></asp:label></TD>
			</TR>
			<TR vAlign="middle" align="left">
				<TD vAlign="top" align="left" width="127"><STRONG>Gender:</STRONG></TD>
				</STRONG></TD>
				<TD vAlign="top" width="179">
					<asp:label id="lblGender" runat="server" Width="100%"></asp:label></TD>
				<TD vAlign="top" align="left" width="142"><STRONG>Blood Group:</STRONG></TD>
				</STRONG></TD>
				<TD vAlign="top" width="176">
					<asp:label id="lblBloodGrp" runat="server" Width="100%"></asp:label></TD>
			</TR>
			<TR vAlign="middle" align="left">
				<TD vAlign="top" width="127"><STRONG>Religion:</STRONG></TD>
				</TD>
				<TD vAlign="top" width="179">
					<asp:label id="lblReligion" runat="server" Width="100%"></asp:label></TD>
				<TD vAlign="top" width="142"><STRONG>Passport #:</STRONG><SPAN style="mso-spacerun: yes"><BR>
					</SPAN><FONT class="font5" size="1">(If Available)</FONT></TD>
				</TD>
				<TD vAlign="top" width="176">
					<asp:label id="lblPassport" runat="server" Width="100%"></asp:label></TD>
			</TR>
			<TR vAlign="middle" align="left">
				<TD style="HEIGHT: 2px" vAlign="top" width="127"><STRONG>Address:</STRONG></TD>
				</TD>
				<TD style="HEIGHT: 2px" vAlign="top" width="179">
					<asp:label id="lblAddress" runat="server" Width="100%"></asp:label></TD>
				<TD style="HEIGHT: 2px" vAlign="top" width="142"><STRONG>Marital Status:<BR>
					</STRONG><SPAN style="mso-spacerun: yes"></SPAN>
				</TD>
				</TD>
				<TD style="HEIGHT: 2px" vAlign="top" width="176">
					<asp:RadioButtonList id="rblMaritialStat" runat="server" Width="100%" RepeatDirection="Horizontal" RepeatLayout="Flow">
						<asp:ListItem Value="1">Single</asp:ListItem>
						<asp:ListItem Value="2">Married</asp:ListItem>
						<asp:ListItem Value="3">Divorced</asp:ListItem>
						<asp:ListItem Value="4">Widow</asp:ListItem>
						<asp:ListItem Value="5">Separated</asp:ListItem>
					</asp:RadioButtonList></TD>
			</TR>
			<TR vAlign="middle" align="left">
				<TD vAlign="top" width="127"><STRONG>Email<BR>
					</STRONG><FONT size="1">(Personal):</FONT></TD>
				</TD>
				<TD vAlign="top" width="179">
					<asp:label id="lblEmail" runat="server" Width="100%"></asp:label></TD>
				<TD vAlign="top" width="142"><STRONG>NTN #:</STRONG><SPAN style="mso-spacerun: yes"><BR>
					</SPAN><FONT class="font5" size="1">(If Available)</FONT></TD>
				</TD>
				<TD vAlign="top" width="176">
					<asp:label id="lblNTN" runat="server" Width="100%"></asp:label></TD>
			</TR>
			<TR vAlign="middle" align="left">
				<TD vAlign="top" width="127"><STRONG>Residence Tel#:<BR>
					</STRONG><FONT class="font5" size="1">(Country code - city code - Tel#)</FONT></TD>
				</TD>
				<TD vAlign="top" width="179">
					<asp:label id="lblcontactNo" runat="server"></asp:label></TD>
				<TD vAlign="top" width="142"><STRONG>Mobile #:</STRONG>
					<BR>
					<FONT class="font5" size="1">(Country code - city code - Mob#)</FONT></TD>
				</TD>
				<TD vAlign="top" width="176">
					<asp:label id="lblMobileNo" runat="server" Width="100%"></asp:label></TD>
			</TR>
			<TR vAlign="middle" align="left">
				<TD vAlign="top" width="127"><STRONG>NIC (New):</STRONG></TD>
				</STRONG></TD>
				<TD vAlign="top" width="179">
					<asp:label id="lblNICNew" runat="server" Width="100%"></asp:label></TD>
				<TD vAlign="top" width="142"><STRONG>NIC (Old):<SPAN style="mso-spacerun: yes">&nbsp;</SPAN></STRONG></TD>
				</STRONG></TD>
				<TD vAlign="top" width="176">
					<asp:label id="lblNICOld" runat="server" Width="100%"></asp:label></TD>
			</TR>
			<TR>
				<TD vAlign="top" width="127"><STRONG>Next of Kin:</STRONG></TD>
				</TD>
				<TD vAlign="top" width="179">
					<asp:label id="lblKin" runat="server" Width="100%"></asp:label></TD>
				<TD vAlign="top" width="142"><STRONG>Nick Name:</STRONG><FONT class="font5"><SPAN style="mso-spacerun: yes"><BR>
						</SPAN><FONT size="1">(If any)</FONT></FONT></TD>
				</TD>
				<TD vAlign="top" width="176">
					<asp:label id="lblNick" runat="server" Width="100%"></asp:label></TD>
			</TR>
			<TR>
				<TD vAlign="top" width="127"><STRONG>Nationality:</STRONG></TD>
				</STRONG></TD>
				<TD vAlign="top" width="179">
					<asp:label id="lblNat" runat="server" Width="100%"></asp:label></TD>
				<TD vAlign="top" width="142"><STRONG>Bank Account #:</STRONG></TD>
				</TD>
				<TD vAlign="top" width="176">
					<asp:label id="lblBankAcctNo" runat="server" Width="100%"></asp:label></TD>
			</TR>
			<TR>
				<TD vAlign="top" width="127"><STRONG>Bank Name, Branch, Address</STRONG></TD>
				</TD>
				<TD vAlign="top" colSpan="3">
					<asp:label id="lblAccountDetails" runat="server" Width="100%"></asp:label></TD>
			</TR>
			</TBODY></TABLE></asp:Panel>
			<asp:label id="lblTpcode" runat="server" Font-Bold="True" Font-Size="Medium" Visible="False"></asp:label>&nbsp;
			<asp:label id="lblTname" runat="server" Font-Bold="True" Font-Size="Medium" Visible="False"></asp:label></TD></TR>
			<TR>
				<TD bgColor="#a9a9a9" align="center"><FONT size="4"><STRONG>Family Information:</STRONG></FONT></TD>
			</TR>
			<TR>
				<TD><asp:datagrid id="DataGrid1" runat="server" Width="100%" BorderStyle="Solid" Visible="False" AutoGenerateColumns="False"
						BorderWidth="1px" BorderColor="Gray" Height="216px">
						<HeaderStyle HorizontalAlign="Center" VerticalAlign="Middle"></HeaderStyle>
						<Columns>
							<asp:BoundColumn DataField="name" SortExpression="name" HeaderText="&lt;b&gt;Name of Relative&lt;/b&gt;&lt;br&gt;&lt;font size=1&gt;(Spouse &amp; Children only)&lt;/font&gt;">
								<ItemStyle HorizontalAlign="Center"></ItemStyle>
							</asp:BoundColumn>
							<asp:BoundColumn DataField="dob" HeaderText="&lt;b&gt;Date of Birth&lt;/b&gt;&lt;br&gt;&lt;font size=1&gt;(dd-mm-yyyy)&lt;/font&gt;">
								<ItemStyle HorizontalAlign="Center"></ItemStyle>
							</asp:BoundColumn>
							<asp:BoundColumn DataField="relationship" HeaderText="&lt;b&gt;Relationship&lt;/b&gt;">
								<ItemStyle HorizontalAlign="Center"></ItemStyle>
							</asp:BoundColumn>
							<asp:BoundColumn DataField="maritialstatus" HeaderText="&lt;b&gt;Marital Status&lt;/b&gt;">
								<ItemStyle HorizontalAlign="Center"></ItemStyle>
							</asp:BoundColumn>
							<asp:BoundColumn DataField="Dependent" HeaderText="&lt;b&gt;Dependent&lt;/b&gt;&lt;br&gt;&lt;font size=1&gt;(Yes / No)&lt;/font&gt;">
								<ItemStyle HorizontalAlign="Center"></ItemStyle>
							</asp:BoundColumn>
							<asp:BoundColumn DataField="gender" HeaderText="&lt;b&gt;Gender&lt;/b&gt;">
								<ItemStyle HorizontalAlign="Center"></ItemStyle>
							</asp:BoundColumn>
							<asp:BoundColumn DataField="occupation" HeaderText="&lt;b&gt;Occupation&lt;/b&gt;">
								<ItemStyle HorizontalAlign="Center"></ItemStyle>
							</asp:BoundColumn>
						</Columns>
					</asp:datagrid></TD>
			</TR>
			<TR>
				<TD bgColor="#a9a9a9" align="center"><STRONG><FONT size="4">Academic Information:</FONT></STRONG></TD>
				</TD>
			</TR>
			<TR>
				<TD><asp:datagrid id="DataGrid3" runat="server" BorderColor="Gray" BorderWidth="1px" AutoGenerateColumns="False"
						Visible="False" BorderStyle="Solid" Width="100%">
						<HeaderStyle HorizontalAlign="Center" VerticalAlign="Middle"></HeaderStyle>
						<Columns>
							<asp:BoundColumn DataField="degree" SortExpression="degree" HeaderText="&lt;b&gt;Degree/Certificate/ Diploma&lt;/b&gt;">
								<HeaderStyle Width="80px"></HeaderStyle>
								<ItemStyle HorizontalAlign="Center"></ItemStyle>
							</asp:BoundColumn>
							<asp:BoundColumn DataField="institute_name" SortExpression="institute" HeaderText="&lt;b&gt;Institute&lt;/b&gt;">
								<HeaderStyle Width="120px"></HeaderStyle>
								<ItemStyle HorizontalAlign="Center"></ItemStyle>
							</asp:BoundColumn>
							<asp:BoundColumn DataField="result" SortExpression="result" HeaderText="&lt;b&gt;Result&lt;/b&gt;&lt;br&gt;&lt;font size=1&gt;(Div, Grade, GPA,%)&lt;/font&gt;">
								<HeaderStyle Width="60px"></HeaderStyle>
								<ItemStyle HorizontalAlign="Center"></ItemStyle>
							</asp:BoundColumn>
							<asp:BoundColumn DataField="major" SortExpression="majors" HeaderText="&lt;b&gt;Majors / Subjects&lt;/b&gt;">
								<HeaderStyle Width="70px"></HeaderStyle>
								<ItemStyle HorizontalAlign="Center"></ItemStyle>
							</asp:BoundColumn>
							<asp:BoundColumn DataField="DurationFrom" HeaderText="&lt;b&gt;Duration&lt;br&gt;From&lt;/b&gt;&lt;br&gt;&lt;font size=1&gt;(dd-mm-yyyy)&lt;/font&gt;">
								<HeaderStyle Wrap="False"></HeaderStyle>
								<ItemStyle HorizontalAlign="Center"></ItemStyle>
							</asp:BoundColumn>
							<asp:BoundColumn DataField="DurationTo" HeaderText="&amp;nbsp;&lt;br&gt;&lt;b&gt;To&lt;/b&gt;&lt;br&gt;&lt;font size=1&gt;(dd-mm-yyyy)&lt;/font&gt;">
								<HeaderStyle Wrap="False"></HeaderStyle>
								<ItemStyle HorizontalAlign="Center"></ItemStyle>
							</asp:BoundColumn>
							<asp:BoundColumn DataField="achievements" HeaderText="&lt;b&gt;Distinction&lt;/b&gt;">
								<ItemStyle HorizontalAlign="Center"></ItemStyle>
							</asp:BoundColumn>
						</Columns>
					</asp:datagrid></TD>
			</TR>
			<TR>
				<TD align="center" bgColor="#a9a9a9">
					<P class="MsoNormal"><FONT size="4"><STRONG>Compensation and Benefits Information: </STRONG>
						</FONT>
					</P>
				</TD>
			</TR>
			<TR>
				<TD>
					<asp:Panel id="Panel3" runat="server">
						<TABLE id="Table2" cellSpacing="0" cellPadding="2" width="100%" border="1">
							<TR>
								<TD width="127"><STRONG>Basic Salary</STRONG></TD>
								<TD width="179">
									<asp:label id="lblBasicSal" runat="server" Width="100%"></asp:label></TD>
								<TD width="127"><STRONG>Fuel Entitlement</STRONG></TD>
								<TD width="179">
									<asp:label id="lblPertrol" runat="server" Width="100%"></asp:label></TD>
							</TR>
							<TR>
								<TD><STRONG>House Rent</STRONG></TD>
								<TD>
									<asp:label id="lblHouseRent" runat="server" Width="100%"></asp:label></TD>
								<TD><STRONG>Mobile Entitlement</STRONG></TD>
								<TD>
									<asp:label id="lblMobile" runat="server" Width="100%"></asp:label></TD>
							</TR>
							<TR>
								<TD><STRONG>Utilities</STRONG></TD>
								<TD>
									<asp:label id="lblUtility" runat="server" Width="100%"></asp:label></TD>
								<TD><STRONG>Car Entitlement</STRONG></TD>
								<TD>
									<asp:label id="lblCar" runat="server" Width="100%"></asp:label></TD>
							</TR>
							<TR>
								<TD><STRONG>Gross Salary</STRONG></TD>
								<TD>
									<asp:label id="lblGrossSal" runat="server" Width="100%"></asp:label></TD>
								<TD><STRONG>Car Description</STRONG></TD>
								<TD>
									<asp:label id="lblCarDes" runat="server" Width="100%"></asp:label></TD>
							</TR>
							<TR>
								<TD><STRONG></STRONG></TD>
								<TD></TD>
								<TD><STRONG>Other</STRONG></TD>
								<TD colSpan="3">
									<asp:label id="lblOther" runat="server" Width="100%"></asp:label></TD>
							</TR>
						</TABLE>
					</asp:Panel>
					<BR>
					<BR>
					__________________________________<BR>
					<STRONG>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Signature of 
						Employee</STRONG></TD>
				</TD></TR>
			<TR>
				<TD><FONT size="1">Please confirm the above mentioned data and fill out columns with 
						gray (if applicable).</FONT></TD>
				</FONT></TD></TR>
			</TBODY></TABLE></form>
	</body>
</HTML>
