using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Configuration;
using System.Data.SqlClient;
namespace GeoRabtaSite.admin
{
	/// <summary>
	/// Summary description for _default.
	/// </summary>
	public class IDCardsRequestsAdmin : System.Web.UI.Page
	{
		protected System.Web.UI.WebControls.ImageButton imgBack;
		protected System.Web.UI.WebControls.Label lblHTMLClosed;
		protected System.Web.UI.WebControls.Label lblHTML;
		protected System.Web.UI.WebControls.LinkButton lnkRefresh;
		SqlConnection con;
		static string userId="";
		private void Page_Load(object sender, System.EventArgs e)
		{
			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			try
			{
				string userId=Session["user_id"].ToString();
				if(userId=="")
				{
					Response.Redirect("../../login.aspx");
				}
			}
			catch
			{
				Response.Redirect("../../login.aspx");
			}
			if(!IsPostBack)
			{
			 GetSpecialistRequest(GetLoginSpNo());
			}
			if(IsPageAccessAllowed())
			{
				if(!GeoSecurity.isControlVisible(2,68,userId,"Process Request"))
				{
					Response.Redirect("../../NotAuthorized.aspx");
				}
			}
			else
			{
			 Response.Redirect("../../NotAuthorized.aspx");
			}
			// Put user code to initialize the page here
		}
		private bool IsPageAccessAllowed()
		{
			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception ex)
			{
				string s=ex.Message;
				Response.Redirect("../../Login.aspx");
			}
		
			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(2,68,userId,"View")==true)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("../../Login.aspx");
				return false;
			}
		}
		public string GetLoginSpNo()
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			string Query="SELECT dbo.t_Specialist.SpecialistNo, dbo.t_Employee.pcode, dbo.t_Employee.name "+
				"FROM dbo.t_SpecialistModule INNER JOIN "+
				"dbo.t_SpecialistOwner ON dbo.t_SpecialistModule.spno = dbo.t_SpecialistOwner.SpecialistNo INNER JOIN "+
				"dbo.t_Employee INNER JOIN "+
				"dbo.t_Designation ON dbo.t_Employee.desigid = dbo.t_Designation.desigid INNER JOIN "+
				"dbo.t_DesignationHistory ON dbo.t_Employee.pcode = dbo.t_DesignationHistory.pcode ON "+
				"dbo.t_SpecialistOwner.s_fdesigid = dbo.t_DesignationHistory.fdesigid INNER JOIN "+
				"dbo.t_Specialist ON dbo.t_SpecialistOwner.SpecialistNo = dbo.t_Specialist.SpecialistNo "+
				"WHERE (dbo.t_Designation.status = 1) AND (dbo.t_DesignationHistory.isactive = 1) AND (dbo.t_Employee.del = 1) AND (dbo.t_Employee.pcode = '"+Session["user_id"].ToString()+"') AND "+ 
				"(dbo.t_SpecialistModule.SpModuleId = 4) AND (dbo.t_Specialist.isactive = 1) AND ((dbo.t_Specialist.SpType = 1) OR (dbo.t_Specialist.SpType = 4))";
			SqlCommand cmd=new SqlCommand(Query,con);
			SqlDataReader rd=cmd.ExecuteReader();
			string SpNo="";	
			while(rd.Read())
			{
				SpNo+=rd["SpecialistNo"].ToString().Trim()+",";
			}
			rd.Close();
			con.Close();
			con.Dispose();
			if(SpNo.Length>0)
			{
				SpNo=SpNo.Remove(SpNo.Length-1,1);
			}
			else
			{
				SpNo="''";
			}
			con.Close();
			return SpNo;
		}
		public void GetSpecialistRequest(string SpNo)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			string GetDepts="SELECT s_deptid, SpecialistNo, SpModuleId FROM  t_specialistdept WHERE (SpecialistNo IN ("+SpNo+")) AND (SpModuleId = 4) ORDER BY s_deptid";
			SqlCommand cmd=new SqlCommand(GetDepts,con);
			SqlDataReader rd=cmd.ExecuteReader();
			string dept="";
			while(rd.Read())
			{
				dept+=rd[0].ToString()+",";
			}
			rd.Close();
			if(dept.Length>0)
			{
				dept=dept.Remove(dept.Length-1,1);
			}
			string GetCats="SELECT s_catid, SpecialistNo, SpModuleId FROM  t_specialistcat WHERE (SpecialistNo IN ("+SpNo+")) AND (SpModuleId = 4) ORDER BY s_catid";
			cmd=new SqlCommand(GetCats,con);
			rd=cmd.ExecuteReader();
			string cat="";
			while(rd.Read())
			{
				cat+=rd[0].ToString()+",";
			}
			rd.Close();
			if(cat.Length>0)
			{
				cat=cat.Remove(cat.Length-1,1);
			}
			string GetStat="SELECT s_stationid, SpecialistNo, SpModuleId FROM  t_specialiststation WHERE (SpecialistNo IN ("+SpNo+")) AND (SpModuleId = 4) ORDER BY s_stationid";
			cmd=new SqlCommand(GetStat,con);
			rd=cmd.ExecuteReader();
			string stat="";
			while(rd.Read())
			{
				stat+=rd[0].ToString()+",";
			}
			rd.Close();
			if(stat.Length>0)
			{
				stat=stat.Remove(stat.Length-1,1);
			}
			con.Close();
			string cQuery="SELECT idreq.RequestID, emp.pcode, emp.name, dpt.deptname AS Department, desg.designation, sta.cityname AS Station, emp.nic_new AS CNIC, "+
            "CASE emp.del WHEN 1 THEN 'Active' WHEN 2 THEN 'Hold' WHEN 0 THEN 'Exited' ELSE '' END AS EmployeeStatus, "+
            "CASE WHEN idreq.ActionCodeID IS NULL THEN 'Pending' ELSE "+
            "(SELECT     ac.actioncode "+
            "FROM          t_idcardactioncode AS ac "+
            "WHERE      ac.id = idreq.actioncodeid) END AS ActionCode, idreq.ActionDate "+
            "FROM dbo.t_IDCardRequest AS idreq INNER JOIN "+
            "dbo.t_Employee AS emp ON idreq.PCode = emp.pcode INNER JOIN "+
            "dbo.t_Designation AS desg ON emp.desigid = desg.desigid INNER JOIN "+
            "dbo.t_Department AS dpt ON desg.deptid = dpt.deptid INNER JOIN "+
            "dbo.t_City AS sta ON emp.station = sta.cityid "+
            "WHERE (desg.deptid IN ("+dept+")) AND (emp.station IN ("+stat+")) AND (desg.category IN ("+cat+")) AND (idreq.isActive = 1) AND "+
            "(idreq.Status = 0)";
			SqlDataAdapter dr=new SqlDataAdapter(cQuery,con);
			DataSet ds=new DataSet();
			dr.Fill(ds,"Records");
			ds.AcceptChanges();
			string html="<table cellpadding=1 cellspacing=0 border=1 class='display' id='example'> "+
				"<thead> "+
				"<tr> "+
				"<th>View Request</th> "+
				//"<th>Request #</th> "+
				"<th>PCode</th> "+
				"<th>Name</th> "+
				"<th>Department</th> "+
				"<th>Designation</th> "+
				//"<th>Station</th> "+
				//"<th>CNIC #</th> "+
				"<th>Employee Current Status</th> "+
				//"<th>Last Action Code</th> "+
				//"<th>Last Action Date</th> "+
				"</tr> "+
				"</thead> "+
				"<tbody>";
			for(int i=0;i<ds.Tables[0].Rows.Count;i++)
			{
				string reqNo="";
				string code="";
				string name="";
				string depart="";
				string desig="";
				string station="";
				string cnic="";
				string empStatus="";
				string acode="";
				string date="";
				reqNo=ds.Tables[0].Rows[i]["RequestID"].ToString();
				code=ds.Tables[0].Rows[i]["pcode"].ToString(); 
				name=ds.Tables[0].Rows[i]["name"].ToString(); ;
				depart=ds.Tables[0].Rows[i]["department"].ToString(); ;
				desig=ds.Tables[0].Rows[i]["designation"].ToString(); ;
				station=ds.Tables[0].Rows[i]["station"].ToString(); ;
				cnic=ds.Tables[0].Rows[i]["cnic"].ToString(); ;
				empStatus=ds.Tables[0].Rows[i]["EmployeeStatus"].ToString();
				acode=ds.Tables[0].Rows[i]["ActionCode"].ToString();
				date=ds.Tables[0].Rows[i]["ActionDate"].ToString();
				html+="<tr> "+
				"<td><a href=# onClick=window.open('IDCardRequestDetail.aspx?id="+reqNo+"&code="+code.Trim()+"&name="+Server.UrlEncode(name)+"&dept="+Server.UrlEncode(depart)+"&desg="+Server.UrlEncode(desig)+"&stat="+Server.UrlEncode(station)+"&nic="+Server.UrlEncode(cnic)+"&acode="+Server.UrlEncode(acode)+"&adate="+Server.UrlEncode(date)+"','MyRequest','height=900,width=950,scrollbars=1,toolbar=0,location=0,status=1,resizable=1,menubar=0').focus()>View Request</a></td>"+
				//"<td><a href=# onClick=window.open('IDCardRequestDetail.aspx?id="+reqNo+"&code="+code.Trim()+"&name="+Server.UrlEncode(name)+"','MyRequest','height=900,width=950,scrollbars=1,toolbar=0,location=0,status=1,resizable=1,menubar=0').focus()>View Request</a></td>"+
				//"<td>"+reqNo+"</td> "+
				"<td>"+code+"</td> "+
				"<td>"+name+"</td> "+
				"<td>"+depart+"</td> "+
				"<td>"+desig+"</td> "+
				//"<td>"+station+"</td> "+
				//"<td>"+cnic+"</td> "+
				"<td>"+empStatus+"</td> "+
				//"<td>"+acode+"</td> "+
				//"<td>"+date+"</td> "+
				"</tr> ";
			}
			html+="</tbody>";
			html+="</table>";
			this.lblHTML.Text=html;
			GetDeliveredReq(dept,cat,stat,SpNo);
		}
		public void GetDeliveredReq(string dept,string cat,string stat,string SpNo)
		{
			string cQuery="SELECT idreq.RequestID, emp.pcode, emp.name, dpt.deptname AS Department, desg.designation, sta.cityname AS Station, emp.nic_new AS CNIC, "+
				"CASE emp.del WHEN 1 THEN 'Active' WHEN 2 THEN 'Hold' WHEN 0 THEN 'Exited' ELSE '' END AS EmployeeStatus, "+
				"CASE WHEN idreq.ActionCodeID IS NULL THEN 'Pending' ELSE "+
				"(SELECT     ac.actioncode "+
				"FROM          t_idcardactioncode AS ac "+
				"WHERE      ac.id = idreq.actioncodeid) END AS ActionCode, idreq.ActionDate "+
				"FROM dbo.t_IDCardRequest AS idreq INNER JOIN "+
				"dbo.t_Employee AS emp ON idreq.PCode = emp.pcode INNER JOIN "+
				"dbo.t_Designation AS desg ON emp.desigid = desg.desigid INNER JOIN "+
				"dbo.t_Department AS dpt ON desg.deptid = dpt.deptid INNER JOIN "+
				"dbo.t_City AS sta ON emp.station = sta.cityid "+
				"WHERE (desg.deptid IN ("+dept+")) AND (emp.station IN ("+stat+")) AND (desg.category IN ("+cat+")) AND (idreq.isActive = 1) AND "+
				"(idreq.Status = 1)";
			SqlDataAdapter dr=new SqlDataAdapter(cQuery,con);
			DataSet ds=new DataSet();
			dr.Fill(ds,"Records");
			ds.AcceptChanges();
			string html="<table cellpadding=1 cellspacing=0 border=1 class='display' id='example2'> "+
				"<thead> "+
				"<tr> "+
				"<th>View Request</th> "+
				//"<th>Request #</th> "+
				"<th>PCode</th> "+
				"<th>Name</th> "+
				"<th>Department</th> "+
				"<th>Designation</th> "+
				//"<th>Station</th> "+
				//"<th>CNIC #</th> "+
				//"<th>Employee Current Status</th> "+
				//"<th>Last Action Code</th> "+
				//"<th>Last Action Date</th> "+
				"</tr> "+
				"</thead> "+
				"<tbody>";
			for(int i=0;i<ds.Tables[0].Rows.Count;i++)
			{
				string reqNo="";
				string code="";
				string name="";
				string depart="";
				string desig="";
				string station="";
				string cnic="";
				string empStatus="";
				string acode="";
				string date="";
				reqNo=ds.Tables[0].Rows[i]["RequestID"].ToString();
				code=ds.Tables[0].Rows[i]["pcode"].ToString(); 
				name=ds.Tables[0].Rows[i]["name"].ToString(); ;
				depart=ds.Tables[0].Rows[i]["department"].ToString(); ;
				desig=ds.Tables[0].Rows[i]["designation"].ToString(); ;
				station=ds.Tables[0].Rows[i]["station"].ToString(); ;
				cnic=ds.Tables[0].Rows[i]["cnic"].ToString(); ;
				empStatus=ds.Tables[0].Rows[i]["EmployeeStatus"].ToString();
				acode=ds.Tables[0].Rows[i]["ActionCode"].ToString();
				date=ds.Tables[0].Rows[i]["ActionDate"].ToString();
				html+="<tr> "+
					"<td><a href=# onClick=window.open('IDCardRequestDetail.aspx?id="+reqNo+"&code="+code.Trim()+"&name="+Server.UrlEncode(name)+"&dept="+Server.UrlEncode(depart)+"&desg="+Server.UrlEncode(desig)+"&stat="+Server.UrlEncode(station)+"&nic="+Server.UrlEncode(cnic)+"&acode="+Server.UrlEncode(acode)+"&adate="+Server.UrlEncode(date)+"','MyRequest','height=900,width=950,scrollbars=1,toolbar=0,location=0,status=1,resizable=1,menubar=0').focus()>View Request</a></td>"+
					//"<td>"+reqNo+"</td> "+
					"<td>"+code+"</td> "+
					"<td>"+name+"</td> "+
					"<td>"+depart+"</td> "+
					"<td>"+desig+"</td> "+
					//"<td>"+station+"</td> "+
					//"<td>"+cnic+"</td> "+
					//"<td>"+empStatus+"</td> "+
					//"<td>"+acode+"</td> "+
					//"<td>"+date+"</td> "+
					"</tr> ";
			}
			html+="</tbody>";
			html+="</table>";
			this.lblHTMLClosed.Text=html;
		}
		#region Web Form Designer generated code
					override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.imgBack.Click += new System.Web.UI.ImageClickEventHandler(this.imgBack_Click);
			this.lnkRefresh.Click += new System.EventHandler(this.lnkRefresh_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		private void lnkRefresh_Click(object sender, System.EventArgs e)
		{
		 GetSpecialistRequest(GetLoginSpNo());
		}

		private void imgBack_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("IDCardRequestPost.aspx");
		}
	}
}
