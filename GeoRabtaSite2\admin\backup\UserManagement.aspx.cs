using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

namespace GeoRabtaSite.admin
{
	/// <summary>
	/// Summary description for ControlSetup.
	/// </summary>
	public class UserManagement : System.Web.UI.Page
	{
		protected System.Web.UI.WebControls.Button cmdCreate;
		protected System.Web.UI.WebControls.CheckBox chkActiveAccount;
		protected System.Web.UI.WebControls.TextBox txtPassword1;
		protected System.Web.UI.WebControls.TextBox txtUserID;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator1;
		protected System.Web.UI.WebControls.Label lblDepartment;
		protected System.Web.UI.WebControls.Label lblDesignation;
		protected System.Web.UI.WebControls.Label lblName;
		protected System.Web.UI.WebControls.HyperLink HyperLink1;
		protected System.Web.UI.WebControls.Label lblEmployeeCode;
		public static int WebFormID=37;
		public static int ProjectID=2;
		protected System.Web.UI.WebControls.Label lblEmailOfficial;
		protected System.Web.UI.WebControls.Label lblUserID;
		protected System.Web.UI.WebControls.Label Label1;
		protected System.Web.UI.WebControls.LinkButton lnkNewPassword;
		public static string userId="";
		protected System.Web.UI.WebControls.TextBox txtEmpInfoEmail2;
		protected System.Web.UI.WebControls.Label lblAvailable;
		protected System.Web.UI.WebControls.LinkButton LinkButton1;
		private Random RandomNumber = new Random();
		protected System.Web.UI.WebControls.Label Label2;
		protected System.Web.UI.WebControls.DataGrid DataGrid1;
		protected System.Web.UI.WebControls.Button Button1;
		private SqlConnection conn;
		
		private bool IsPageAccessAllowed()
		{
			
			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception ex)
			{
				Response.Redirect("Login.aspx");
			}

			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"View")==true)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("Login.aspx");
				return false;
			}
		}

		private string NewPassword ()
		{
			char [] digits = {'0','1','2','3','4','5','6','7','8','9','a','b','c','d','e','f','g','h','i','j','k','l','m','n','o','p','q','r','s','t','u','v','w','x','y','z','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z'};
			int digitsLength = digits.Length;
			
			string temp="";
			for(int k=0; k < 8; k++)
			{
				int j = RandomNumber.Next(digitsLength);
				temp+=digits[j].ToString();
			}
			//txtPassword.Text=temp;
			return temp;
		}

		private void FillRoles()
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			string str="select * from t_roles ";
			SqlDataAdapter dr=new SqlDataAdapter(str,conn);
			DataSet ds=new DataSet();
			dr.Fill(ds,"Record");
			DataView dv=new DataView(ds.Tables["Record"]);
			
			DataGrid1.DataSource=dv;
			DataGrid1.DataBind();
			
			foreach( DataGridItem di in DataGrid1.Items )
			{
				CheckBox cb = (CheckBox)di.FindControl("chkSelectRole") ;
				String RoleID = di.Cells[0].Text;
				SqlCommand cmd2=new SqlCommand("select isActive from t_usersroles where roleid=" + RoleID + " and LoginID='" + txtUserID.Text + "'",conn);
				SqlDataReader dd=cmd2.ExecuteReader();
				dd.Read();
				if (dd.HasRows)
				{
					if(dd["isActive"].ToString()=="1")
					{
						cb.Checked=true;
					}
					else
					{
						cb.Checked=false;
					}
				}
				else
				{
					cb.Checked=false;
				}
				dd.Close();
			}
		}


		private void Page_Load(object sender, System.EventArgs e)
		{
			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			conn = new SqlConnection(Connection.ConnectionString);
			conn.Open();

			if(IsPageAccessAllowed())
			{
				
				if (!IsPostBack)
				{
					cmdCreate.Attributes.Add("onclick","return CheckOfficalEmail();");
					GetEmployeeInfo();
					FillRoles();
				}
			}
			else
			{
				Response.Redirect("ErrorPage.aspx");
			}

			if(!IsPostBack)
			{

			}
		}

		private void GetEmployeeInfo()
		{
			
			

			string pcode="";
			try
			{
				pcode=Session["_PCode"].ToString();
				try
				{
						
					SqlCommand cmd=new SqlCommand("SELECT dbo.t_Employee.name, dbo.t_Designation.designation, dbo.t_Department.deptname, dbo.t_Sbu.sbuname,dbo.t_employee.email_official " +
						" FROM dbo.t_Department INNER JOIN " +
						" dbo.t_Designation ON dbo.t_Department.deptid = dbo.t_Designation.deptid INNER JOIN " +
						" dbo.t_Employee ON dbo.t_Designation.desigid = dbo.t_Employee.desigid INNER JOIN " +
						" dbo.t_Sbu ON dbo.t_Department.sbu = dbo.t_Sbu.sbuid " +
						" WHERE (dbo.t_Employee.pcode = '" + pcode + "')",conn);

					SqlDataReader dr=cmd.ExecuteReader();
						
					dr.Read();
						
					lblDepartment.Text=dr["deptname"].ToString();
					lblDesignation.Text=dr["designation"].ToString();
					lblName.Text=dr["name"].ToString();
					lblEmployeeCode.Text=pcode;
						
					lblEmailOfficial.Text=dr["email_official"].ToString();
					txtEmpInfoEmail2.Text=dr["email_official"].ToString();

					dr.Close();
					cmd=new SqlCommand("SELECT user_id, isActive,password FROM dbo.t_systemuser WHERE (pcode = '" + pcode + "') ",conn);
					dr=cmd.ExecuteReader();
					dr.Read();
					if (dr.HasRows)
					{
						cmdCreate.Text="Update Account";
						txtUserID.ReadOnly=true;
						LinkButton1.Visible=false;
						lblAvailable.Visible=false;
						cmdCreate.Enabled=true;
						txtPassword1.Text=dr["password"].ToString();
						//							txtPassword2.Text=dr["password"].ToString();
						txtUserID.Text=dr["User_id"].ToString();
							
						if (dr["isActive"].ToString()=="1")
							chkActiveAccount.Checked=true;
						else
							chkActiveAccount.Checked=false;
						Button1.Enabled=true;
					}
					else
					{
						txtUserID.ReadOnly=false;
						LinkButton1.Visible=true;
						cmdCreate.Enabled=false;

						string [] s = lblEmailOfficial.Text.Split('@');

						txtUserID.Text=s[0];

						cmdCreate.Text="Create Account";
						cmdCreate.Enabled=false;
						Button1.Enabled=false;

					}
					dr.Close();
				}
				catch (Exception ex)
				{
					Response.Write(ex.Message);
				}
			}
			catch (Exception ex)
			{
				Response.Redirect("SelectUser.aspx");
				string s=ex.Message;
			}
		}
		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{ 
			this.LinkButton1.Click += new System.EventHandler(this.LinkButton1_Click);
			this.lnkNewPassword.Click += new System.EventHandler(this.lnkNewPassword_Click);
			this.cmdCreate.Click += new System.EventHandler(this.cmdCreate_Click);
			this.Button1.Click += new System.EventHandler(this.Button1_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		private void cmdCreate_Click(object sender, System.EventArgs e)
		{
			if (txtUserID.Text=="")
			{
				Label1.Text="User ID is required";
				return;
			}
			else if (txtPassword1.Text=="")
			{
				Label1.Text="Password is not assigned, Please click Generate New Password Link";
				return;
			}
			else if (!AccountAvailable())
			{
				Label1.Text="Please check account availability first";
				return;
			}

			if (cmdCreate.Text=="Create Account")
			{
				try
				{
					SqlConnection conn=new SqlConnection(Connection.ConnectionString);
					conn.Open();
					int isActive;
					if (chkActiveAccount.Checked==true)
						isActive=1;
					else
						isActive=0;
					SqlCommand cmd=new SqlCommand("Insert Into t_SystemUser(user_id,pcode,password,created_by,create_date, isActive,isExpire) " +
						"values ('" + txtUserID.Text.Trim() + "','" + lblEmployeeCode.Text + "','" + txtPassword1.Text + "','P991',getdate(), " + isActive + ",0)",conn);
					cmd.ExecuteNonQuery();
					conn.Close();
					//Response.Redirect("SelectUser.aspx");
					cmdCreate.Text="Update Account";
					txtUserID.ReadOnly=true;

					//lblPCode.Text=Session["_PCode"].ToString();
					Session["_user_id"]=txtUserID.Text;
					//lblUserID.Text=Session["_user_id"].ToString();
					//lblUsername.Text=Session["_EmployeeName"].ToString();
					Button1.Enabled=true;
				}
				catch (Exception ex)
				{
					Response.Write(ex.Message);
				}
			}
			else if (cmdCreate.Text=="Update Account")
			{
				try
				{
					SqlConnection conn=new SqlConnection(Connection.ConnectionString);
					conn.Open();
					int isActive;
					if (chkActiveAccount.Checked==true)
						isActive=1;
					else
						isActive=0;
					//Response.Write("udate t_systemuser set user_id='" + txtUserID.Text + "', password='" + txtPassword1.Text + "', created_by='P991', isActive=" + isActive + " isExpire=0 wher pcode='"+lblEmployeeCode.Text+"'");
					SqlCommand cmd=new SqlCommand("update t_systemuser set user_id='" + txtUserID.Text + "', password='" + txtPassword1.Text + "', created_by='P991', isActive=" + isActive + ", isExpire=0 where pcode='"+lblEmployeeCode.Text+"'",conn);
					cmd.ExecuteNonQuery();
					conn.Close();
					//Response.Redirect("SelectUser.aspx");
					Button1.Enabled=true;
				}
				catch (Exception ex)
				{
					Response.Write(ex.Message);
				}

			}
		}

		private void ibUserManagement_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("UserManagement.aspx");
		}

		private void ibRoles_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Session["_pcode"]=lblEmployeeCode.Text;
			Session["_name"]=lblName.Text;
			Session["_designation"]=lblDesignation.Text;
			Session["_department"]=lblDepartment.Text;
			Session["_EmailOfficial"]=lblEmailOfficial.Text;
			
			Response.Redirect("UserRoles.aspx");
		}

		private void lnkCheckID_Click(object sender, System.EventArgs e)
		{
//			SqlDataAdapter da = new SqlDataAdapter(sql,conn);
//
		}

		private void lnkNewPassword_Click(object sender, System.EventArgs e)
		{
			txtPassword1.Text=NewPassword();
			Label2.Text="New password has been generated";

		}


		private bool AccountAvailable()
		{
			if (cmdCreate.Text=="Create Account")
			{
				txtUserID.Text=txtUserID.Text.ToLower();
				SqlCommand cmd = new SqlCommand("SELECT user_id FROM dbo.t_systemuser WHERE (user_id = '" + txtUserID.Text + "')",conn);
				SqlDataReader dr=cmd.ExecuteReader();
				dr.Read();
				lblAvailable.Visible=true;
				if (dr.HasRows)
				{
					lblAvailable.Text="(Not Available)";
					cmdCreate.Enabled=false;
					return false;
				}
				else
				{
					lblAvailable.Text="(<b>Available</b>)";
					cmdCreate.Enabled=true;
					return true;
				}
				
			}
			else
			{
				return true;
			}

		}
		private void LinkButton1_Click(object sender, System.EventArgs e)
		{
			if (txtUserID.Text.Trim()=="")
			{
				lblAvailable.Text="Please enter User ID";
				cmdCreate.Enabled=false;
			}
			else
			{
				AccountAvailable();
			}
		}

		private void Button1_Click(object sender, System.EventArgs e)
		{
			//Label1.Text="";
			SqlCommand cmd = new SqlCommand("delete from t_usersroles where loginid='"+txtUserID.Text+"'",conn);
			cmd.ExecuteNonQuery();

			foreach( DataGridItem di in DataGrid1.Items )
			{
				CheckBox cb = (CheckBox)di.FindControl("chkSelectRole") ;
				if( cb.Checked==true )
				{
					//Label1.Text+="Checked...<bt>";
					//Label lb = (Label)di.FindControl("lblRoleID");
					//Response.Write( lb.Text +"<br>" );
					String RoleID = di.Cells[0].Text;
					cmd=new SqlCommand("Insert into t_usersroles (loginid,roleid,isActive) values('" + txtUserID.Text + "'," + RoleID + ",1)",conn);
					cmd.ExecuteNonQuery();
				}
				else
				{
					//Label1.Text+="Un Checked...<bt> "+cb.Checked.ToString();
				}
			}
		}



	}
}
