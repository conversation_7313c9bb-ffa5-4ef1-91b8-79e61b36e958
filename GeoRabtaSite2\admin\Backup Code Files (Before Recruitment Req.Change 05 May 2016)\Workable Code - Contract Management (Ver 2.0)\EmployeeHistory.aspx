<%@ Page language="c#" Codebehind="EmployeeHistory.aspx.cs" AutoEventWireup="false" Inherits="GeoRabtaSite.admin.EmployeeHistory" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Raabta Admin :: Employee History</title>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="RaabtaAdmin.css" type="text/css" rel="stylesheet">
		<LINK href="../Styles4.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body dir="ltr" bottomMargin="0" bgProperties="fixed" leftMargin="0" topMargin="0" rightMargin="0">
		<form id="ideas" runat="server">
			<table height="100%" cellSpacing="0" cellPadding="0" width="780" align="center" bgColor="#ffffff"
				border="0">
				<tr>
					<td vAlign="middle" align="left" height="69">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" data="data:application/x-oleobject;base64,btt80m2uzxGWuERFU1QAAGdVZlUACQAAnVAAACEHAAAIAAIAAAAAAAgAHgAAAGYAbABhAHMAaAAvAFQAbwBwADEALgBzAHcAZgAAAAgAHgAAAGYAbABhAHMAaAAvAFQAbwBwADEALgBzAHcAZgAAAAgADgAAAFcAaQBuAGQAbwB3AAAACAAGAAAALQAxAAAACAAGAAAALQAxAAAACAAKAAAASABpAGcAaAAAAAgAAgAAAAAACAAGAAAALQAxAAAACAACAAAAAAAIAAIAAAAAAAgAEAAAAFMAaABvAHcAQQBsAGwAAAAIAAQAAAAwAAAACAAEAAAAMAAAAAgAAgAAAAAACAACAAAAAAAIAAIAAAAAAA0AAAAAAAAAAAAAAAAAAAAAAAgABAAAADEAAAAIAAQAAAAwAAAACAAAAAAACAAEAAAAMAAAAAgACAAAAGEAbABsAAAACAAMAAAAZgBhAGwAcwBlAAAA"
							width="780" height="69">
							<embed src="flash/Top1.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="69"> </embed>
						</OBJECT>
					</td>
				</tr>
				<tr>
					<td height="10">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" data="data:application/x-oleobject;base64,btt80m2uzxGWuERFU1QAAGdVZlUACQAAnVAAAAgBAAAIAAIAAAAAAAgAHgAAAGYAbABhAHMAaAAvAFQAbwBwADIALgBzAHcAZgAAAAgAHgAAAGYAbABhAHMAaAAvAFQAbwBwADIALgBzAHcAZgAAAAgADgAAAFcAaQBuAGQAbwB3AAAACAAGAAAALQAxAAAACAAGAAAALQAxAAAACAAKAAAASABpAGcAaAAAAAgAAgAAAAAACAAGAAAALQAxAAAACAACAAAAAAAIAAIAAAAAAAgAEAAAAFMAaABvAHcAQQBsAGwAAAAIAAQAAAAwAAAACAAEAAAAMAAAAAgAAgAAAAAACAACAAAAAAAIAAIAAAAAAA0AAAAAAAAAAAAAAAAAAAAAAAgABAAAADEAAAAIAAQAAAAwAAAACAAAAAAACAAEAAAAMAAAAAgACAAAAGEAbABsAAAACAAMAAAAZgBhAGwAcwBlAAAA"
							width="780" height="10">
							<embed src="flash/Top2.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="10"> </embed>
						</OBJECT>
					</td>
				</tr>
				<TR>
					<TD class="PageTitle" height="20">Geo Raabta Admin :: Employee History</TD>
				</TR>
				<TR>
					<TD class="MainBG" vAlign="top" align="left"><BR>
						<TABLE class="MainFormColor" id="Table1" cellSpacing="0" cellPadding="3" width="750" align="center"
							border="0">
							<TR>
								<TD class="OrangeFormTitle">Personal Information</TD>
							</TR>
							<TR>
								<TD><asp:datagrid id="dgPersonal" runat="server" BorderStyle="Solid" BorderColor="Navy" BorderWidth="1px"
										BackColor="White" CellPadding="3" GridLines="Horizontal" AutoGenerateColumns="False" Width="100%">
										<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
										<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
										<AlternatingItemStyle CssClass="ALternetItem"></AlternatingItemStyle>
										<ItemStyle ForeColor="#4A3C8C" CssClass="Item"></ItemStyle>
										<HeaderStyle Font-Bold="True" ForeColor="#F7F7F7" CssClass="header"></HeaderStyle>
										<Columns>
											<asp:BoundColumn DataField="Description" HeaderText="Description"></asp:BoundColumn>
											<asp:BoundColumn DataField="OldValue" HeaderText="Old Value"></asp:BoundColumn>
											<asp:BoundColumn DataField="NewValue" HeaderText="New Value"></asp:BoundColumn>
											<asp:BoundColumn DataField="ModifiedBy" HeaderText="Modified By"></asp:BoundColumn>
											<asp:BoundColumn DataField="ModificationDate" HeaderText="Modification Date" DataFormatString="{0:d MMM, yyyy h:mm:ss}"></asp:BoundColumn>
										</Columns>
										<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
									</asp:datagrid></TD>
							</TR>
						</TABLE>
						<TABLE class="MainFormColor" id="Table2" cellSpacing="0" cellPadding="3" width="750" align="center"
							border="0">
							<TR>
								<TD class="OrangeFormTitle">Employment Information</TD>
							</TR>
							<TR>
								<TD><asp:datagrid id="dgEmployment" runat="server" BorderStyle="Solid" BorderColor="Navy" BorderWidth="1px"
										BackColor="White" CellPadding="3" GridLines="Horizontal" AutoGenerateColumns="False" Width="100%">
										<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
										<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
										<AlternatingItemStyle CssClass="ALternetItem"></AlternatingItemStyle>
										<ItemStyle ForeColor="#4A3C8C" CssClass="Item"></ItemStyle>
										<HeaderStyle Font-Bold="True" ForeColor="#F7F7F7" CssClass="header"></HeaderStyle>
										<Columns>
											<asp:BoundColumn DataField="Description" HeaderText="Description"></asp:BoundColumn>
											<asp:BoundColumn DataField="OldValue" HeaderText="Old Value"></asp:BoundColumn>
											<asp:BoundColumn DataField="NewValue" HeaderText="New Value"></asp:BoundColumn>
											<asp:BoundColumn DataField="ModifiedBy" HeaderText="Modified By"></asp:BoundColumn>
											<asp:BoundColumn DataField="ModificationDate" HeaderText="Modification Date" DataFormatString="{0:d MMM, yyyy h:mm:ss}"></asp:BoundColumn>
										</Columns>
										<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
									</asp:datagrid></TD>
							</TR>
						</TABLE>
						<TABLE class="MainFormColor" id="Table3" cellSpacing="0" cellPadding="3" width="750" align="center"
							border="0">
							<TR>
								<TD class="OrangeFormTitle">Educational Information</TD>
							</TR>
							<TR>
								<TD><asp:datagrid id="dgEducation" runat="server" BorderStyle="Solid" BorderColor="Navy" BorderWidth="1px"
										BackColor="White" CellPadding="3" GridLines="Horizontal" AutoGenerateColumns="False" Width="100%">
										<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
										<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
										<AlternatingItemStyle CssClass="ALternetItem"></AlternatingItemStyle>
										<ItemStyle ForeColor="#4A3C8C" CssClass="Item"></ItemStyle>
										<HeaderStyle Font-Bold="True" ForeColor="#F7F7F7" CssClass="header"></HeaderStyle>
										<Columns>
											<asp:BoundColumn DataField="Description" HeaderText="Description"></asp:BoundColumn>
											<asp:BoundColumn DataField="OldValue" HeaderText="Old Value"></asp:BoundColumn>
											<asp:BoundColumn DataField="NewValue" HeaderText="New Value"></asp:BoundColumn>
											<asp:BoundColumn DataField="ModifiedBy" HeaderText="Modified By"></asp:BoundColumn>
											<asp:BoundColumn DataField="ModificationDate" HeaderText="Modification Date" DataFormatString="{0:d MMM, yyyy h:mm:ss}"></asp:BoundColumn>
										</Columns>
										<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
									</asp:datagrid></TD>
							</TR>
						</TABLE>
						<TABLE class="MainFormColor" id="Table4" cellSpacing="0" cellPadding="3" width="750" align="center"
							border="0">
							<TR>
								<TD class="OrangeFormTitle">Family Information</TD>
							</TR>
							<TR>
								<TD><asp:datagrid id="dgFamily" runat="server" BorderStyle="Solid" BorderColor="Navy" BorderWidth="1px"
										BackColor="White" CellPadding="3" GridLines="Horizontal" AutoGenerateColumns="False" Width="100%">
										<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
										<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
										<AlternatingItemStyle CssClass="ALternetItem"></AlternatingItemStyle>
										<ItemStyle ForeColor="#4A3C8C" CssClass="Item"></ItemStyle>
										<HeaderStyle Font-Bold="True" ForeColor="#F7F7F7" CssClass="header"></HeaderStyle>
										<Columns>
											<asp:BoundColumn DataField="Description" HeaderText="Description"></asp:BoundColumn>
											<asp:BoundColumn DataField="OldValue" HeaderText="Old Value"></asp:BoundColumn>
											<asp:BoundColumn DataField="NewValue" HeaderText="New Value"></asp:BoundColumn>
											<asp:BoundColumn DataField="ModifiedBy" HeaderText="Modified By"></asp:BoundColumn>
											<asp:BoundColumn DataField="ModificationDate" HeaderText="Modification Date" DataFormatString="{0:d MMM, yyyy h:mm:ss}"></asp:BoundColumn>
										</Columns>
										<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
									</asp:datagrid></TD>
							</TR>
						</TABLE>
						<TABLE class="MainFormColor" id="Table7" cellSpacing="0" cellPadding="3" width="750" align="center"
							border="0">
							<TR>
								<TD class="OrangeFormTitle">
									<P class="MsoNormal">Compensation and Benefits
										<asp:LinkButton id="ViewComp" runat="server" Font-Names="Verdana" Font-Size="XX-Small">View Compensation  Info</asp:LinkButton></P>
								</TD>
							</TR>
							<TR>
								<TD><asp:datagrid id="dgCnB" runat="server" BorderStyle="Solid" BorderColor="Navy" BorderWidth="1px"
										BackColor="White" CellPadding="3" GridLines="Horizontal" AutoGenerateColumns="False" Width="100%"
										Visible="False">
										<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
										<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
										<AlternatingItemStyle CssClass="ALternetItem"></AlternatingItemStyle>
										<ItemStyle ForeColor="#4A3C8C" CssClass="Item"></ItemStyle>
										<HeaderStyle Font-Bold="True" ForeColor="#F7F7F7" CssClass="header"></HeaderStyle>
										<Columns>
											<asp:BoundColumn DataField="Description" HeaderText="Description"></asp:BoundColumn>
											<asp:BoundColumn DataField="OldValue" HeaderText="Old Value"></asp:BoundColumn>
											<asp:BoundColumn DataField="NewValue" HeaderText="New Value"></asp:BoundColumn>
											<asp:BoundColumn DataField="ModifiedBy" HeaderText="Modified By"></asp:BoundColumn>
											<asp:BoundColumn DataField="ModificationDate" HeaderText="Modification Date" DataFormatString="{0:d MMM, yyyy h:mm:ss}"></asp:BoundColumn>
										</Columns>
										<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
									</asp:datagrid></TD>
							</TR>
						</TABLE>
						<TABLE class="MainFormColor" id="Table5" cellSpacing="0" cellPadding="3" width="750" align="center"
							border="0">
							<TR>
								<TD class="OrangeFormTitle">Designation History</TD>
							</TR>
							<TR>
								<TD><asp:datagrid id="dgFunctionalDesignation" runat="server" BorderStyle="Solid" BorderColor="Navy"
										BorderWidth="1px" BackColor="White" CellPadding="3" GridLines="Horizontal" AutoGenerateColumns="False"
										Width="100%" Visible="False">
										<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
										<AlternatingItemStyle CssClass="AlternetItem"></AlternatingItemStyle>
										<ItemStyle ForeColor="#4A3C8C" CssClass="Item"></ItemStyle>
										<HeaderStyle Font-Bold="True" ForeColor="#F7F7F7" CssClass="header"></HeaderStyle>
										<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
										<Columns>
											<asp:BoundColumn Visible="False" DataField="fdesigid"></asp:BoundColumn>
											<asp:BoundColumn DataField="designation" HeaderText="Designation"></asp:BoundColumn>
											<asp:BoundColumn DataField="title" HeaderText="Functional Designation"></asp:BoundColumn>
											<asp:BoundColumn DataField="_from" HeaderText="From" DataFormatString="{0:d}"></asp:BoundColumn>
											<asp:BoundColumn DataField="_to" HeaderText="To" DataFormatString="{0:d}"></asp:BoundColumn>
											<asp:BoundColumn DataField="status" HeaderText="Status"></asp:BoundColumn>
										</Columns>
										<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
									</asp:datagrid></TD>
							</TR>
						</TABLE>
						<TABLE class="MainFormColor" id="Table6" cellSpacing="0" cellPadding="3" width="750" align="center"
							border="0">
							<TR>
								<TD class="OrangeFormTitle">Bond Paper Information</TD>
							</TR>
							<TR>
								<TD><asp:datagrid id="dgBondPaper" runat="server" BorderStyle="Solid" BorderColor="Navy" BorderWidth="1px"
										BackColor="White" CellPadding="3" GridLines="Horizontal" AutoGenerateColumns="False" Width="100%">
										<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
										<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
										<AlternatingItemStyle CssClass="ALternetItem"></AlternatingItemStyle>
										<ItemStyle ForeColor="#4A3C8C" CssClass="Item"></ItemStyle>
										<HeaderStyle Font-Bold="True" ForeColor="#F7F7F7" CssClass="header"></HeaderStyle>
										<Columns>
											<asp:BoundColumn DataField="Description" HeaderText="Description"></asp:BoundColumn>
											<asp:BoundColumn DataField="OldValue" HeaderText="Old Value"></asp:BoundColumn>
											<asp:BoundColumn DataField="NewValue" HeaderText="New Value"></asp:BoundColumn>
											<asp:BoundColumn DataField="ModifiedBy" HeaderText="Modified By"></asp:BoundColumn>
											<asp:BoundColumn DataField="ModificationDate" HeaderText="Modification Date" DataFormatString="{0:d MMM, yyyy h:mm:ss}"></asp:BoundColumn>
										</Columns>
										<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
									</asp:datagrid></TD>
							</TR>
						</TABLE>
					</TD>
				</TR>
				<tr>
					<td vAlign="middle" align="center" height="20">Copyright © 2005 Independent Media 
						Corporation <A href="http://www.geo.tv">www.geo.tv</A></td>
				</tr>
			</table>
		</form>
	</body>
</HTML>
