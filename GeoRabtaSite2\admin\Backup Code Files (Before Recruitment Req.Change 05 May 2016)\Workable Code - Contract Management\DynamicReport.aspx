<%@ Register TagPrefix="ew" Namespace="eWorld.UI" Assembly="eWorld.UI" %>
<%@ Page language="c#" Codebehind="DynamicReport.aspx.cs" AutoEventWireup="false" Inherits="GeoRabtaSite.DynamicReport" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<HTML>
	<HEAD>
		<title>Advance Report</title>
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="C#" name="CODE_LANGUAGE">
		<meta content="JavaScript" name="vs_defaultClientScript">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="main.css" type="text/css" rel="stylesheet">
		<script language="javascript">

        // sort function - ascending (case-insensitive)
        function sortFuncAsc(record1, record2) {
            var value1 = record1.optText.toLowerCase();
            var value2 = record2.optText.toLowerCase();
            if (value1 > value2) return(1);
            if (value1 < value2) return(-1);
            return(0);
        }

        // sort function - descending (case-insensitive)
        function sortFuncDesc(record1, record2) {
            var value1 = record1.optText.toLowerCase();
            var value2 = record2.optText.toLowerCase();
            if (value1 > value2) return(-1);
            if (value1 < value2) return(1);
            return(0);
        }

        function sortSelect(ddlName, ascendingOrder) {
			selectToSort=document.getElementById(ddlName);
            if (arguments.length == 1) ascendingOrder = true;    // default to ascending sort

            // copy options into an array
            var myOptions = [];
            for (var loop=0; loop<selectToSort.options.length; loop++) {
                myOptions[loop] = { optText:selectToSort.options[loop].text, optValue:selectToSort.options[loop].value };
            }

            // sort array
            if (ascendingOrder) {
                myOptions.sort(sortFuncAsc);
            } else {
                myOptions.sort(sortFuncDesc);
            }

            // copy sorted options from array back to select box
            selectToSort.options.length = 0;
            for (var loop=0; loop<myOptions.length; loop++) {
                var optObj = document.createElement('option');
                optObj.text = myOptions[loop].optText;
                optObj.value = myOptions[loop].optValue;
                selectToSort.options.add(optObj);
            }
        }


		</script>
	</HEAD>
	<body onload="sortSelect('ddSearchAdvance',true)">
		<form id="Form1" method="post" runat="server">
			<TABLE id="Table4" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Station</TD>
					<TD><asp:dropdownlist id="ddStation" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
						</asp:dropdownlist></TD>
					<td width="95"><asp:DropDownList id="ddStationOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<TD width="50"><asp:linkbutton id="Lnk4" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table2" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Designation</TD>
					<TD><asp:dropdownlist id="ddDesignation" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
						</asp:dropdownlist></TD>
					<td width="95"><asp:DropDownList id="ddDesignationOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<TD width="50"><asp:linkbutton id="Lnk2" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table1" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Employee Code</TD>
					<TD><asp:dropdownlist id="ddCode" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtCodeLike" runat="server" Width="120px"></asp:textbox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator1" runat="server" ErrorMessage="Entered Character Not Allowed"
							ControlToValidate="txtCodeLike" ValidationExpression="^[a-z A-Z 0-9]*$">*</asp:RegularExpressionValidator></TD>
					<td width="95"><asp:DropDownList id="ddCodeOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<TD width="50"><asp:linkbutton id="lnk1" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table3" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Name</TD>
					<TD><asp:dropdownlist id="ddName" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtNameLike" runat="server" Width="120px"></asp:textbox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator2" runat="server" ErrorMessage="Entered Character Not Allowed"
							ControlToValidate="txtNameLike" ValidationExpression="^[a-z A-Z 0-9()]*$">*</asp:RegularExpressionValidator></TD>
					<td width="95"><asp:DropDownList id="ddNameOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<TD width="50"><asp:linkbutton id="Lnk3" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table5" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Date of Join (mm/dd/yyyy)</TD>
					<TD><asp:dropdownlist id="ddDoj" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is less than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is greater than or equal to</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150">
						<ew:CalendarPopup id="txtDojLike" runat="server" EnableHideDropDown="True" Width="110px">
							<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></WeekdayStyle>
							<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Yellow"></MonthHeaderStyle>
							<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
								BackColor="AntiqueWhite"></OffMonthStyle>
							<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></GoToTodayStyle>
							<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="LightGoldenrodYellow"></TodayDayStyle>
							<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Orange"></DayHeaderStyle>
							<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="LightGray"></WeekendStyle>
							<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Yellow"></SelectedDateStyle>
							<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></ClearDateStyle>
							<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></HolidayStyle>
						</ew:CalendarPopup></TD>
					<td width="95"><asp:DropDownList id="ddDojOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<TD width="50"><asp:linkbutton id="Lnk5" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table6" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Confirmation Due Date</TD>
					<TD><asp:dropdownlist id="ddConfirmation" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is less than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is greater than or equal to</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150">
						<ew:CalendarPopup id="txtConfirmation" runat="server" EnableHideDropDown="True" Width="110px">
							<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></WeekdayStyle>
							<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Yellow"></MonthHeaderStyle>
							<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
								BackColor="AntiqueWhite"></OffMonthStyle>
							<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></GoToTodayStyle>
							<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="LightGoldenrodYellow"></TodayDayStyle>
							<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Orange"></DayHeaderStyle>
							<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="LightGray"></WeekendStyle>
							<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Yellow"></SelectedDateStyle>
							<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></ClearDateStyle>
							<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></HolidayStyle>
						</ew:CalendarPopup></TD>
					<td width="95"><asp:DropDownList id="ddConfOrderby" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<TD width="50">
						<asp:linkbutton id="Lnk6" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table7" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Date of Exit</TD>
					<TD>
						<asp:DropDownList id="ddDoE" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is less than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is greater than or equal to</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<ew:CalendarPopup id="txtDOE" runat="server" EnableHideDropDown="True" Width="110px">
							<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></WeekdayStyle>
							<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Yellow"></MonthHeaderStyle>
							<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
								BackColor="AntiqueWhite"></OffMonthStyle>
							<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></GoToTodayStyle>
							<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="LightGoldenrodYellow"></TodayDayStyle>
							<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Orange"></DayHeaderStyle>
							<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="LightGray"></WeekendStyle>
							<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Yellow"></SelectedDateStyle>
							<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></ClearDateStyle>
							<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></HolidayStyle>
						</ew:CalendarPopup></TD>
					<td width="95"><asp:DropDownList id="ddDoEOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<TD width="50"><asp:LinkButton id="Lnk7" runat="server">Remove</asp:LinkButton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table8" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Category</TD>
					<TD>
						<asp:DropDownList id="ddCategoryChk" runat="server" Width="328px">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is less than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is greater than or equal to</asp:ListItem>
						</asp:DropDownList>
						<asp:DropDownList id="ddCategory" runat="server" Width="152px">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
						</asp:DropDownList></TD>
					<td width="95"><asp:DropDownList id="ddCategoryOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<TD width="50"><asp:LinkButton id="Lnk8" runat="server">Remove</asp:LinkButton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table9" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Date of Birth</TD>
					<TD>
						<asp:DropDownList id="ddDoB" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is less than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is greater than or equal to</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<ew:CalendarPopup id="txtDoB" runat="server" EnableHideDropDown="True" Width="110px">
							<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></WeekdayStyle>
							<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Yellow"></MonthHeaderStyle>
							<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
								BackColor="AntiqueWhite"></OffMonthStyle>
							<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></GoToTodayStyle>
							<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="LightGoldenrodYellow"></TodayDayStyle>
							<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Orange"></DayHeaderStyle>
							<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="LightGray"></WeekendStyle>
							<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Yellow"></SelectedDateStyle>
							<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></ClearDateStyle>
							<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></HolidayStyle>
						</ew:CalendarPopup></TD>
					<td width="95"><asp:DropDownList id="ddDoBOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<TD width="50"><asp:LinkButton id="Lnk9" runat="server">Remove</asp:LinkButton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table10" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Gender</TD>
					<TD>
						<asp:DropDownList id="ddGender" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="1">Male</asp:ListItem>
							<asp:ListItem Value="2">Female</asp:ListItem>
						</asp:DropDownList></TD>
					<td width="95"><asp:DropDownList id="ddGenderOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk10" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table11" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Blood Group</TD>
					<TD>
						<asp:DropDownList id="ddBloodGrp" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="1">A+</asp:ListItem>
							<asp:ListItem Value="2">A-</asp:ListItem>
							<asp:ListItem Value="3">B+</asp:ListItem>
							<asp:ListItem Value="4">B-</asp:ListItem>
							<asp:ListItem Value="5">AB+</asp:ListItem>
							<asp:ListItem Value="6">AB-</asp:ListItem>
							<asp:ListItem Value="7">O+</asp:ListItem>
							<asp:ListItem Value="8">O-</asp:ListItem>
						</asp:DropDownList></TD>
					<td width="95"><asp:DropDownList id="ddBloodGrpOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk11" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table12" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Passport #</TD>
					<TD>
						<asp:DropDownList id="ddPassport" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<asp:TextBox id="txtPassport" runat="server" Width="120px"></asp:TextBox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator3" runat="server" ErrorMessage="Entered Character Not Allowed"
							ControlToValidate="txtPassport" ValidationExpression="^[a-z A-Z 0-9 -]*$">*</asp:RegularExpressionValidator></TD>
					<td width="95"><asp:DropDownList id="ddPassPortOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<TD width="50">
						<asp:LinkButton id="Lnk12" runat="server">Remove</asp:LinkButton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table13" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">NIC (New)</TD>
					<TD>
						<asp:DropDownList id="ddNICNew" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<asp:TextBox id="txtNICNew" runat="server" Width="120px"></asp:TextBox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator4" runat="server" ErrorMessage="Entered Character Not Allowed"
							ControlToValidate="txtNICNew" ValidationExpression="^[a-z A-Z 0-9 -]*$">*</asp:RegularExpressionValidator></TD>
					<td width="95"><asp:DropDownList id="ddNICNewOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk13" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table14" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">NIC (Old)</TD>
					<td><asp:DropDownList id="ddNICOld" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:DropDownList></td>
					<TD width="150">
						<asp:TextBox id="txtNICOld" runat="server" Width="120px"></asp:TextBox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator5" runat="server" ErrorMessage="Entered Character Not Allowed"
							ControlToValidate="txtNICOld" ValidationExpression="^[a-z A-Z 0-9 -]*$">*</asp:RegularExpressionValidator></TD>
					<td width="95"><asp:DropDownList id="ddNICOldOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk14" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table15" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Address</TD>
					<TD>
						<asp:DropDownList id="ddAddress" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<asp:TextBox id="txtAddress" runat="server" Width="120px"></asp:TextBox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator6" runat="server" ErrorMessage="Entered Character Not Allowed"
							ControlToValidate="txtAddress" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'>*</asp:RegularExpressionValidator></TD>
					<td width="95"><asp:DropDownList id="ddAddressOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk15" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table16" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Extension</TD>
					<TD>
						<asp:DropDownList id="ddExtension" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<asp:TextBox id="txtExtension" runat="server" Width="120px"></asp:TextBox></TD>
					<td width="95"><asp:DropDownList id="ddExtensionOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk16" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table17" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Employment Type</TD>
					<TD>
						<asp:DropDownList id="ddEmploymentType" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="1">Permanent</asp:ListItem>
							<asp:ListItem Value="2">Contractual</asp:ListItem>
							<asp:ListItem Value="3">Retainer</asp:ListItem>
							<asp:ListItem Value="4">Honorary</asp:ListItem>
							<asp:ListItem Value="5">Talent</asp:ListItem>
						</asp:DropDownList></TD>
					<td width="95"><asp:DropDownList id="ddEmploymentTypeOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk17" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table18" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Father's Name</TD>
					<TD>
						<asp:DropDownList id="ddFatherName" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<asp:TextBox id="txtFatherName" runat="server" Width="120px"></asp:TextBox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator7" runat="server" ErrorMessage="Entered Character Not Allowed"
							ControlToValidate="txtFatherName" ValidationExpression="^[a-z A-Z 0-9()]*$">*</asp:RegularExpressionValidator></TD>
					<td width="95"><asp:DropDownList id="ddFatherNameOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk18" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table19" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Marital Status</TD>
					<TD>
						<asp:DropDownList id="ddMaritalStatus" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="1">Single</asp:ListItem>
							<asp:ListItem Value="2">Married</asp:ListItem>
							<asp:ListItem Value="3">Divorced</asp:ListItem>
							<asp:ListItem Value="4">Widow</asp:ListItem>
							<asp:ListItem Value="5">Separated</asp:ListItem>
						</asp:DropDownList></TD>
					<td width="95"><asp:DropDownList id="ddMaritalStatusOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk19" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table20" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Religion</TD>
					<TD>
						<asp:DropDownList id="ddReligion" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="1">Islam</asp:ListItem>
							<asp:ListItem Value="2">Christianity</asp:ListItem>
							<asp:ListItem Value="3">Buddhism</asp:ListItem>
							<asp:ListItem Value="4">Zoroastrian</asp:ListItem>
							<asp:ListItem Value="5">Jewish</asp:ListItem>
							<asp:ListItem Value="6">Hinduism</asp:ListItem>
							<asp:ListItem Value="7">Others</asp:ListItem>
						</asp:DropDownList></TD>
					<td width="95"><asp:DropDownList id="ddReligionOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk20" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table21" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Telephone #</TD>
					<TD>
						<asp:DropDownList id="ddTelephone" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<asp:TextBox id="txtTelephone" runat="server" Width="120px"></asp:TextBox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator8" runat="server" ErrorMessage="Entered Character Not Allowed"
							ControlToValidate="txtTelephone" ValidationExpression="^[a-z A-Z 0-9 -]*$">*</asp:RegularExpressionValidator></TD>
					<td width="95"><asp:DropDownList id="ddTelephoneOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk21" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table22" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Mobile&nbsp;#</TD>
					<TD>
						<asp:DropDownList id="ddMobileNo" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<asp:TextBox id="txtMobileNo" runat="server" Width="120px"></asp:TextBox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator9" runat="server" ErrorMessage="Entered Character Not Allowed"
							ControlToValidate="txtMobileNo" ValidationExpression="^[a-z A-Z 0-9 -]*$">*</asp:RegularExpressionValidator></TD>
					<td width="95"><asp:DropDownList id="ddMobileNoOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk22" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table23" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">E-mail(Personal)</TD>
					<TD>
						<asp:DropDownList id="ddEmailPersonal" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<asp:TextBox id="txtEmailPersonal" runat="server" Width="120px"></asp:TextBox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator10" runat="server" ErrorMessage="Entered Character Not Allowed"
							ControlToValidate="txtEmailPersonal" ValidationExpression="\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*">*</asp:RegularExpressionValidator></TD>
					<td width="95"><asp:DropDownList id="ddEmailPersonalOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk23" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table24" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Next of Kin</TD>
					<TD>
						<asp:DropDownList id="ddNextofKin" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<asp:TextBox id="txtNextofKin" runat="server" Width="120px"></asp:TextBox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator11" runat="server" ErrorMessage="Entered Character Not Allowed"
							ControlToValidate="txtNextofKin" ValidationExpression="^[a-z A-Z 0-9()]*$">*</asp:RegularExpressionValidator></TD>
					<td width="95"><asp:DropDownList id="ddNextofKinOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk24" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table25" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Bond</TD>
					<TD>
						<asp:DropDownList id="ddBond" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="1">Yes</asp:ListItem>
							<asp:ListItem Value="2">No</asp:ListItem>
						</asp:DropDownList></TD>
					<td width="95"><asp:DropDownList id="ddBondOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk25" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table26" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">E-mail (Official)</TD>
					<TD>
						<asp:DropDownList id="ddEmailOfficial" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<asp:TextBox id="txtEmailOfficial" runat="server" Width="120px"></asp:TextBox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator12" runat="server" ErrorMessage="Entered Character Not Allowed"
							ControlToValidate="txtEmailOfficial" ValidationExpression="\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*">*</asp:RegularExpressionValidator></TD>
					<td width="95"><asp:DropDownList id="ddEmailOfficialOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk26" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table27" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Bank Account #</TD>
					<TD>
						<asp:DropDownList id="ddBankAccountNo" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<asp:TextBox id="txtBankAccountNo" runat="server" Width="120px"></asp:TextBox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator13" runat="server" ErrorMessage="Entered Character Not Allowed"
							ControlToValidate="txtBankAccountNo" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'>*</asp:RegularExpressionValidator></TD>
					<td width="95"><asp:DropDownList id="ddBankAccountNoOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk27" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table28" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Bank&nbsp;Detail</TD>
					<TD>
						<asp:DropDownList id="ddBankDetail" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<asp:TextBox id="txtBankDetail" runat="server" Width="120px"></asp:TextBox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator14" runat="server" ErrorMessage="Entered Character Not Allowed"
							ControlToValidate="txtBankDetail" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'>*</asp:RegularExpressionValidator></TD>
					<td width="95"><asp:DropDownList id="ddBankDetailOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk28" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table29" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">NTN #</TD>
					<TD>
						<asp:DropDownList id="ddNTN" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<asp:TextBox id="txtNTN" runat="server" Width="120px"></asp:TextBox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator15" runat="server" ErrorMessage="Entered Character Not Allowed"
							ControlToValidate="txtNTN" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'>*</asp:RegularExpressionValidator></TD>
					<td width="95"><asp:DropDownList id="ddNTNOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk29" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table30" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">
						EOBI&nbsp;#</TD>
					<TD>
						<asp:DropDownList id="ddEOBI" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<asp:TextBox id="txtEOBI" runat="server" Width="120px"></asp:TextBox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator16" runat="server" ErrorMessage="Entered Character Not Allowed"
							ControlToValidate="txtEOBI" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'>*</asp:RegularExpressionValidator></TD>
					<td width="95"><asp:DropDownList id="ddEOBIOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk30" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table31" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">SESSI&nbsp;#</TD>
					<TD>
						<asp:DropDownList id="ddSESSI" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<asp:TextBox id="txtSESSI" runat="server" Width="120px"></asp:TextBox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator17" runat="server" ErrorMessage="Entered Character Not Allowed"
							ControlToValidate="txtSESSI" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'>*</asp:RegularExpressionValidator></TD>
					<td width="95"><asp:DropDownList id="ddSESSIOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk31" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table32" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Insurance Code</TD>
					<TD>
						<asp:DropDownList id="ddInsuranceNo" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<asp:TextBox id="txtInsuranceNo" runat="server" Width="120px"></asp:TextBox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator18" runat="server" ErrorMessage="Entered Character Not Allowed"
							ControlToValidate="txtInsuranceNo" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'>*</asp:RegularExpressionValidator></TD>
					<td width="95"><asp:DropDownList id="ddInsuranceNoOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk32" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table33" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Compensatory off</TD>
					<TD>
						<asp:DropDownList id="ddCompensatory" runat="server" Width="100%">
							<asp:ListItem Value="-1" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="1">Yes</asp:ListItem>
							<asp:ListItem Value="0">No</asp:ListItem>
						</asp:DropDownList></TD>
					<td width="95"><asp:DropDownList id="ddCompensatoryOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk33" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table34" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Nationality</TD>
					<TD>
						<asp:DropDownList id="ddNationality" runat="server" Width="100%">
							<asp:ListItem Value="-1" Selected="True">-Select Where-</asp:ListItem>
						</asp:DropDownList></TD>
					<td width="95"><asp:DropDownList id="ddNationalityOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk34" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table35" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Nationality(Secondary)&nbsp;</TD>
					<TD>
						<asp:DropDownList id="ddNationality2" runat="server" Width="100%">
							<asp:ListItem Value="-1" Selected="True">-Select Where-</asp:ListItem>
						</asp:DropDownList></TD>
					<td width="95"><asp:DropDownList id="ddNationality2Order" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk35" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table36" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Mobile Entitlement</TD>
					<TD>
						<asp:DropDownList id="ddMobile" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is less than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is greater than or equal to</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<asp:TextBox id="txtMobile" runat="server" Width="120px"></asp:TextBox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator19" runat="server" ErrorMessage="Entered Character Not Allowed"
							ControlToValidate="txtMobile" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'>*</asp:RegularExpressionValidator></TD>
					<td width="95"><asp:DropDownList id="ddMobileOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk36" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table37" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Petrol Entitlement</TD>
					<TD>
						<asp:DropDownList id="ddPetrol" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is less than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is greater than or equal to</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<asp:TextBox id="txtPetrol" runat="server" Width="120px"></asp:TextBox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator20" runat="server" ErrorMessage="Entered Character Not Allowed"
							ControlToValidate="txtPetrol" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'>*</asp:RegularExpressionValidator></TD>
					<td width="95"><asp:DropDownList id="ddPetrolOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk37" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table38" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Car &amp; Conveyance</TD>
					<TD>
						<asp:DropDownList id="ddCar" runat="server" Width="100%">
							<asp:ListItem Value="-1" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="1">Company Maintained Car</asp:ListItem>
							<asp:ListItem Value="2">Conveyance Allowance</asp:ListItem>
							<asp:ListItem Value="3">Leased Car</asp:ListItem>
							<asp:ListItem Value="4">Pick &amp; Drop</asp:ListItem>
						</asp:DropDownList></TD>
					<td width="95"><asp:DropDownList id="ddCarOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk38" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table39" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Car &amp; Conveyance Description</TD>
					<TD>
						<asp:DropDownList id="ddCarDes" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<asp:TextBox id="txtCarDes" runat="server" Width="120px"></asp:TextBox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator21" runat="server" ErrorMessage="Entered Character Not Allowed"
							ControlToValidate="txtCarDes" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'>*</asp:RegularExpressionValidator></TD>
					<td width="95"><asp:DropDownList id="ddCarDesOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk39" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table40" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Probation Period</TD>
					<TD>
						<asp:DropDownList id="ddProbation" runat="server" Width="100%">
							<asp:ListItem Value="-1" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="0">0</asp:ListItem>
							<asp:ListItem Value="3">3</asp:ListItem>
							<asp:ListItem Value="6">6</asp:ListItem>
							<asp:ListItem Value="12">12</asp:ListItem>
							<asp:ListItem Value="-2">Other</asp:ListItem>
						</asp:DropDownList></TD>
					<td width="95"><asp:DropDownList id="ddProbationOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk40" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table41" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Confirmation Date</TD>
					<TD>
						<asp:DropDownList id="ddDoC" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is less than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is greater than or equal to</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<ew:CalendarPopup id="txtDoC" runat="server" EnableHideDropDown="True" Width="110px">
							<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></WeekdayStyle>
							<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Yellow"></MonthHeaderStyle>
							<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
								BackColor="AntiqueWhite"></OffMonthStyle>
							<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></GoToTodayStyle>
							<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="LightGoldenrodYellow"></TodayDayStyle>
							<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Orange"></DayHeaderStyle>
							<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="LightGray"></WeekendStyle>
							<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Yellow"></SelectedDateStyle>
							<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></ClearDateStyle>
							<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></HolidayStyle>
						</ew:CalendarPopup></TD>
					<td width="95"><asp:DropDownList id="ddDoCOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk41" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table42" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">New Joining&nbsp;</TD>
					<TD>
						<asp:DropDownList id="ddNew" runat="server" Width="100%">
							<asp:ListItem Value="-1" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="1">Yes</asp:ListItem>
							<asp:ListItem Value="0">No</asp:ListItem>
						</asp:DropDownList></TD>
					<td width="95"><asp:DropDownList id="ddNewOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk42" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table43" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Other</TD>
					<TD>
						<asp:DropDownList id="ddOther" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<asp:TextBox id="txtOther" runat="server" Width="120px"></asp:TextBox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator22" runat="server" ErrorMessage="Entered Character Not Allowed"
							ControlToValidate="txtOther" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'>*</asp:RegularExpressionValidator></TD>
					<td width="95"><asp:DropDownList id="ddOtherOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk43" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<TABLE id="Table44" cellSpacing="0" cellPadding="1" width="850" border="1" runat="server">
				<TR>
					<TD width="200">Department</TD>
					<TD>
						<asp:dropdownlist id="ddDept" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="95">
						<asp:DropDownList id="ddDeptOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="50">
						<asp:linkbutton id="Lnk44" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table45" cellSpacing="0" cellPadding="1" width="850" border="1" runat="server">
				<TR>
					<TD width="200">Employment Status</TD>
					<TD>
						<asp:dropdownlist id="ddEStatus" runat="server" Width="100%">
							<asp:ListItem Value="-1" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="1">Active</asp:ListItem>
							<asp:ListItem Value="2">Hold/InActive</asp:ListItem>
							<asp:ListItem Value="0">Exited</asp:ListItem>
							<asp:ListItem Value="4">Expected To Join</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="95">
						<asp:DropDownList id="ddEStatusOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="50">
						<asp:linkbutton id="Lnk45" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table46" cellSpacing="0" cellPadding="1" width="850" border="1" runat="server">
				<TR>
					<TD width="200">Basic Salary</TD>
					<TD>
						<asp:DropDownList id="ddBasicSal" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is less than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is greater than or equal to</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<asp:TextBox id="txtBasicSal" runat="server" Width="120px"></asp:TextBox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator23" runat="server" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'
							ControlToValidate="txtBasicSal" ErrorMessage="Entered Character Not Allowed">*</asp:RegularExpressionValidator></TD>
					<TD width="95">
						<asp:DropDownList id="ddBasicSalOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="50">
						<asp:LinkButton id="Lnk46" runat="server">Remove</asp:LinkButton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table47" cellSpacing="0" cellPadding="1" width="850" border="1" runat="server">
				<TR>
					<TD width="200">Gross&nbsp;Salary</TD>
					<TD>
						<asp:DropDownList id="ddGrossSal" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is less than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is greater than or equal to</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<asp:TextBox id="txtGrossSal" runat="server" Width="120px"></asp:TextBox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator24" runat="server" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'
							ControlToValidate="txtGrossSal" ErrorMessage="Entered Character Not Allowed">*</asp:RegularExpressionValidator></TD>
					<TD width="95">
						<asp:DropDownList id="ddGrossSalOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="50">
						<asp:LinkButton id="Lnk47" runat="server">Remove</asp:LinkButton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table48" cellSpacing="0" cellPadding="1" width="850" border="1" runat="server">
				<TR>
					<TD width="200">House Rent</TD>
					<TD>
						<asp:DropDownList id="ddRent" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is less than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is greater than or equal to</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<asp:TextBox id="txtRent" runat="server" Width="120px"></asp:TextBox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator25" runat="server" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'
							ControlToValidate="txtRent" ErrorMessage="Entered Character Not Allowed">*</asp:RegularExpressionValidator></TD>
					<TD width="95">
						<asp:DropDownList id="ddRentOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="50">
						<asp:LinkButton id="Lnk48" runat="server">Remove</asp:LinkButton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table49" cellSpacing="0" cellPadding="1" width="850" border="1" runat="server">
				<TR>
					<TD width="200">Utilities</TD>
					<TD>
						<asp:DropDownList id="ddUtilities" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is less than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is greater than or equal to</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<asp:TextBox id="txtUtilities" runat="server" Width="120px"></asp:TextBox>
						<asp:RegularExpressionValidator id="RegularExpressionValidator26" runat="server" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'
							ControlToValidate="txtUtilities" ErrorMessage="Entered Character Not Allowed">*</asp:RegularExpressionValidator></TD>
					<TD width="95">
						<asp:DropDownList id="ddUtilitiesOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="50">
						<asp:LinkButton id="Lnk49" runat="server">Remove</asp:LinkButton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table50" cellSpacing="0" cellPadding="1" width="850" border="1" runat="server">
				<TR>
					<TD width="200">Functional Designation</TD>
					<TD>
						<asp:dropdownlist id="ddFunc" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="95">
						<asp:DropDownList id="ddFuncOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="50">
						<asp:linkbutton id="Lnk50" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table51" cellSpacing="0" cellPadding="1" width="850" border="1" runat="server">
				<TR>
					<TD width="200">Business Unit</TD>
					<TD>
						<asp:dropdownlist id="ddBusinessUnit" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="95">
						<asp:DropDownList id="ddBusinessUnitOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="50">
						<asp:linkbutton id="lnk51" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table53" cellSpacing="0" cellPadding="1" border="1" runat="server" width="850">
				<TR>
					<TD width="200">Contract Expiry</TD>
					<TD>
						<asp:DropDownList id="ddCt" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is less than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is greater than or equal to</asp:ListItem>
						</asp:DropDownList></TD>
					<TD width="150">
						<ew:CalendarPopup id="txtCtDate" runat="server" EnableHideDropDown="True" Width="110px">
							<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></WeekdayStyle>
							<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Yellow"></MonthHeaderStyle>
							<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
								BackColor="AntiqueWhite"></OffMonthStyle>
							<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></GoToTodayStyle>
							<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="LightGoldenrodYellow"></TodayDayStyle>
							<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Orange"></DayHeaderStyle>
							<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="LightGray"></WeekendStyle>
							<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Yellow"></SelectedDateStyle>
							<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></ClearDateStyle>
							<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></HolidayStyle>
						</ew:CalendarPopup></TD>
					<td width="95"><asp:DropDownList id="ddCtOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:DropDownList></td>
					<td width="50"><asp:LinkButton id="Lnk52" runat="server">Remove</asp:LinkButton></td>
				</TR>
			</TABLE>
			<BR>
			<BR>
			<P align="center" style="FONT-WEIGHT: bold">BU HR Managers can only view 
				Compensation &amp; Benefits details of their own department(s)</P>
			<DIV>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<asp:dropdownlist id="ddSearchAdvance_" runat="server" Visible="False">
					<asp:ListItem Value="1">Employee Code</asp:ListItem>
					<asp:ListItem Value="2">Designation</asp:ListItem>
					<asp:ListItem Value="3">Name</asp:ListItem>
				</asp:dropdownlist></DIV>
			<DIV align="center">
				<TABLE id="Table52" style="WIDTH: 482px; HEIGHT: 88px" cellSpacing="1" cellPadding="1"
					width="482" align="center" border="0">
					<TR>
						<TD>
							<P align="center">
								<asp:dropdownlist id="ddNetwork" runat="server" Width="200px" AutoPostBack="True"></asp:dropdownlist></P>
						</TD>
					</TR>
					<TR>
						<TD>
							<P align="center">
								<asp:dropdownlist id="ddSearchAdvance" runat="server">
									<asp:ListItem Value="1">Employee Code</asp:ListItem>
									<asp:ListItem Value="2">Designation</asp:ListItem>
									<asp:ListItem Value="3">Name</asp:ListItem>
									<asp:ListItem Value="4">Station</asp:ListItem>
									<asp:ListItem Value="5">Date of Join</asp:ListItem>
									<asp:ListItem Value="6">Confirmation Due Date</asp:ListItem>
									<asp:ListItem Value="7">Date of Exit</asp:ListItem>
									<asp:ListItem Value="8">Category</asp:ListItem>
									<asp:ListItem Value="9">Date of Birth</asp:ListItem>
									<asp:ListItem Value="10">Gender</asp:ListItem>
									<asp:ListItem Value="11">Blood Group</asp:ListItem>
									<asp:ListItem Value="12">Passport No</asp:ListItem>
									<asp:ListItem Value="13">NIC (new)</asp:ListItem>
									<asp:ListItem Value="14">NIC (old)</asp:ListItem>
									<asp:ListItem Value="15">Address</asp:ListItem>
									<asp:ListItem Value="16">Extension</asp:ListItem>
									<asp:ListItem Value="17">Employment Type</asp:ListItem>
									<asp:ListItem Value="18">Father Name</asp:ListItem>
									<asp:ListItem Value="19">Marital Status</asp:ListItem>
									<asp:ListItem Value="20">Religion</asp:ListItem>
									<asp:ListItem Value="21">Telephone (Home)</asp:ListItem>
									<asp:ListItem Value="22">Mobile</asp:ListItem>
									<asp:ListItem Value="23">Email (Personal)</asp:ListItem>
									<asp:ListItem Value="24">Next of Kin</asp:ListItem>
									<asp:ListItem Value="25">Bond Paper</asp:ListItem>
									<asp:ListItem Value="26">Email (Official)</asp:ListItem>
									<asp:ListItem Value="27">Bank Account#</asp:ListItem>
									<asp:ListItem Value="28">Bank Account Detail</asp:ListItem>
									<asp:ListItem Value="29">NTN#</asp:ListItem>
									<asp:ListItem Value="30">EOBI#</asp:ListItem>
									<asp:ListItem Value="31">SESSI#</asp:ListItem>
									<asp:ListItem Value="32">Insurance Code</asp:ListItem>
									<asp:ListItem Value="33">Compensatory Off</asp:ListItem>
									<asp:ListItem Value="34">Nationality</asp:ListItem>
									<asp:ListItem Value="35">Nationality (Secondary)</asp:ListItem>
									<asp:ListItem Value="36">Mobile Entitlement</asp:ListItem>
									<asp:ListItem Value="37">Petrol Entitlement</asp:ListItem>
									<asp:ListItem Value="38">Car Conveyance</asp:ListItem>
									<asp:ListItem Value="39">Car Description</asp:ListItem>
									<asp:ListItem Value="40">Probabtion Period</asp:ListItem>
									<asp:ListItem Value="41">Confirmation Date</asp:ListItem>
									<asp:ListItem Value="42">New Joiner</asp:ListItem>
									<asp:ListItem Value="43">Other</asp:ListItem>
									<asp:ListItem Value="44">Department</asp:ListItem>
									<asp:ListItem Value="45">Employment Status</asp:ListItem>
									<asp:ListItem Value="46">Basic Salary</asp:ListItem>
									<asp:ListItem Value="47">Gross Salary</asp:ListItem>
									<asp:ListItem Value="48">House Rent</asp:ListItem>
									<asp:ListItem Value="49">Utilities</asp:ListItem>
									<asp:ListItem Value="50">Functional Designation</asp:ListItem>
									<asp:ListItem Value="51">Business Unit</asp:ListItem>
									<asp:ListItem Value="52">Contract Expiry</asp:ListItem>
								</asp:dropdownlist>
								<asp:button id="btnAdd" runat="server" Text="Add"></asp:button></P>
						</TD>
					</TR>
					<TR>
						<TD>
							<P align="center">
								<asp:LinkButton id="ViewComp" runat="server" Font-Size="XX-Small" Font-Names="Verdana">Add Compensation & Benefits Info</asp:LinkButton></P>
						</TD>
					</TR>
				</TABLE>
				&nbsp;
			</DIV>
			<P align="center">
				<asp:HyperLink id="HyperLink1" runat="server" NavigateUrl="default.aspx">Back</asp:HyperLink></P>
			<asp:panel id="Panel1" runat="server" Height="168px">
				<P align="center">
					<asp:PlaceHolder id="PlaceHolder1" runat="server"></asp:PlaceHolder><BR>
					<asp:CheckBox id="chkEmpPic" runat="server" Visible="False" Text="Employee Picture"></asp:CheckBox>
					<asp:CheckBox id="chkFamilyDetail" runat="server" Visible="False" Text="Family Detail"></asp:CheckBox>
					<asp:CheckBox id="chkRelative" runat="server" Visible="False" Text="Relative Detail"></asp:CheckBox>&nbsp;<BR>
					<asp:CheckBox id="chkEducation" runat="server" Visible="False" Text="Education Detail"></asp:CheckBox>&nbsp;
					<asp:CheckBox id="chkExperience" runat="server" Visible="False" Text="Experience Detail"></asp:CheckBox>&nbsp;
					<asp:CheckBox id="chkTraining" runat="server" Visible="False" Text="Training Detail"></asp:CheckBox><BR>
					<asp:CheckBox id="chkDirectReport" runat="server" Visible="False" Text="Direct  Reporting"></asp:CheckBox>&nbsp;
					<asp:CheckBox id="chkDottedReport" runat="server" Visible="False" Text="Dotted Reporting"></asp:CheckBox><BR>
					<asp:Button id="btnSubmit" runat="server" Visible="False" Text="Submit"></asp:Button><BR>
				</P>
			</asp:panel>
			<asp:DataGrid id="DataGrid1" runat="server" CellPadding="3" BackColor="White" BorderWidth="1px"
				BorderStyle="None" BorderColor="#000040">
				<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
				<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
				<AlternatingItemStyle BackColor="#F7F7F7"></AlternatingItemStyle>
				<ItemStyle ForeColor="#4A3C8C" BackColor="#E7E7FF"></ItemStyle>
				<HeaderStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#4A3C8C"></HeaderStyle>
				<Columns>
					<asp:HyperLinkColumn Text="Select" Target="_blank" DataNavigateUrlField="Employee Code" DataNavigateUrlFormatString="updateemployee.aspx?id={0}"
						DataTextField="Employee Code" DataTextFormatString="Select"></asp:HyperLinkColumn>
					<asp:HyperLinkColumn Text="View" Target="_blank" DataNavigateUrlField="Employee Code" DataNavigateUrlFormatString="employeereport.aspx?id={0}"
						DataTextField="Employee Code" DataTextFormatString="View"></asp:HyperLinkColumn>
				</Columns>
				<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
			</asp:DataGrid>
			<asp:ValidationSummary id="ValidationSummary1" runat="server" ShowMessageBox="True"></asp:ValidationSummary></form>
	</body>
</HTML>
