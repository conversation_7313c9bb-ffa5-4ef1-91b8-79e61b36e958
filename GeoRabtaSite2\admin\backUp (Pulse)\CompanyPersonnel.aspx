<%@ Register TagPrefix="uc1" TagName="myMenus" Src="myMenus.ascx" %>
<%@ Register TagPrefix="ajax" Namespace="MagicAjax.UI.Controls" Assembly="MagicAjax" %>
<%@ Page CodeBehind="CompanyPersonnel.aspx.cs" Language="c#" AutoEventWireup="false" Inherits="GeoRabtaSite.EmployeeTrackingSystems" smartNavigation="False" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Raabta Admin :: Find Employee</title>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="RaabtaAdmin.css" type="text/css" rel="stylesheet">
		<LINK href="../Styles4.css" type="text/css" rel="stylesheet">
		<!--<script src="../client.js"></script>-->
		<script language="javascript">
		
		
		var xmlHttp;

function createXMLHttpRequest(){
    //var xmlHttp = null;
    if(typeof XMLHttpRequest != "undefined"){
        xmlHttp = new XMLHttpRequest();
    }
    else if(typeof window.ActiveXObject != "undefined"){
        try
        {
            xmlHttp = new ActiveXObject("Msxml2.XMLHTTP.4.0");
        }
        catch(e)
        {
            try 
            {
                xmlHttp = new ActiveXObject("MSXML2.XMLHTTP");
            }
            catch(e)
            {
                try 
                {
                    xmlHttp = new ActiveXObject("Microsoft.XMLHTTP");
                }
                catch(e)
                {
                    xmlHttp = null;
                }
            }
        }
    }
    return xmlHttp;
}

function callServ(path, parameter)
{
	//var URL="http://georaabta/admin/GetRecords.aspx?sid="+Math.random()+ "&pcode=&location=1&name="+ parameter +"&dept=0&desg=0&fdesg=0";
	var URL=path+"?sid="+Math.random()+ "&"+parameter;
		 
    if(xmlHttp)
    {
        xmlHttp.abort();
    }
    // Create the object each time a call is about to be made
    if(createXMLHttpRequest())
	{
		xmlHttp.onreadystatechange = contentIsReady;
		xmlHttp.open("GET", URL, true);
		xmlHttp.send(null);
    }
}

function contentIsReady()
{
    if(xmlHttp && xmlHttp.readyState == 4)
    {
        //xmlHttp.responseText
        document.getElementById("txtHint").innerHTML=xmlHttp.responseText;
        xmlHttp = null;
    }
    else
    {
		txtHint.innerHTML="<img src='../images/searching.gif' border=0>&nbsp;Loading...";
    }
}

		
		
		function getValues()
		{
		 var param;
		 //var pcode=document.getElementById("ddEmployeeName");
		 var pcode=document.getElementById("txtPcode");
		 var locations=document.getElementById("ddLocation");
		 var name=document.getElementById("txtAdName");
		 var department=document.getElementById("ddDepartment");
		 var designation=document.getElementById("ddDesignation");
		 var fdesignation=document.getElementById("ddFunctionalDesignation");
		 param="pcode="+pcode.value+"&location="+locations[locations.selectedIndex].value+"&name="+name.value+"&dept="+department[department.selectedIndex].value+"&desg="+designation[designation.selectedIndex].value+"&fdesg="+fdesignation[fdesignation.selectedIndex].value;
		 var verify=document.getElementById("TextBox1");
		 if(verify.value==1)
		 {
		 var age=document.getElementById("ddAge");
		 var bloodgrp=document.getElementById("ddBloodGrp");
		 var doj=document.getElementById("ddDOJ");
		 var cat=document.getElementById("ddCategory");
		 var gender=document.getElementById("ddGender");
		 var rstation=document.getElementById("ddReportingStation");
		 param=param+"&age="+age[age.selectedIndex].value+"&bloodgrp="+bloodgrp[bloodgrp.selectedIndex].value+"&doj="+doj[doj.selectedIndex].value+"&cat="+cat[cat.selectedIndex].value+"&gender="+gender[gender.selectedIndex].value+"&rstation="+rstation[rstation.selectedIndex].value;
		 }
		 //prompt("",param);
		 txtHint.innerHTML="<img src='../images/searching.gif' border=0>&nbsp;Loading...";
		 
		 callServ('GetRecords.aspx',param);
		}
		</script>
		<script language="javascript" id="clientEventHandlersJS">
<!--

function window_onload() {
	
	di=document.getElementById('divPhoto');
	di.style.visibility='hidden';
	getValues();
}

function ShowPhoto(photo)
{
	di=document.getElementById('divPhoto');
	di.style.visibility='visible';
	di.style.left=window.event.x+document.body.scrollLeft;
	di.style.top=window.event.y+document.body.scrollTop;
	
  	img=document.getElementById('imgNewEmp');
	img.src='../employee/'+photo;

}
function HidePhoto()
{
	di=document.getElementById('divPhoto');
	di.style.visibility='hidden';
}
function ViewProfile(pcode)
{
 window.open('employeereport.aspx?id='+pcode,'MyProfile','toolbar=no,statusbar=no,addressbar=no,scrollbars=yes,resizable=yes,width=700,height=650');
}
function EditProfile(pcode)
{
 window.open('UpdateEmployee.aspx?id='+pcode,'MyProfile','toolbar=no,statusbar=no,addressbar=no,scrollbars=yes,resizable=yes,width=700,height=650');
}
function ViewHistory(pcode)
{
 window.open('EmployeeHistory.aspx?id='+pcode,'MyProfile','toolbar=no,statusbar=no,addressbar=no,scrollbars=yes,resizable=yes,width=700,height=650');
}
//-->
		</script>
	</HEAD>
	<body language="javascript" bottomMargin="0" bgProperties="fixed" leftMargin="0" topMargin="0"
		onload="return window_onload()" rightMargin="0">
		<div id="divPhoto" style="BORDER-RIGHT: #000000 1px solid; BORDER-TOP: #000000 1px solid; LEFT: 0px; BORDER-LEFT: #000000 1px solid; WIDTH: 103px; BORDER-BOTTOM: #000000 1px solid; POSITION: absolute; TOP: 0px; HEIGHT: 104px"><img id="imgNewEmp" style="WIDTH: 100px; HEIGHT: 112px" height="112" width="100" name="imgNewEmp"></div>
		<form id="myForm" name="MyForm" runat="server">
			<table height="100%" cellSpacing="0" cellPadding="0" width="780" align="center" bgColor="#ffffff"
				border="0">
				<tr>
					<td vAlign="middle" align="left" height="69">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="69" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="1826">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top1.swf">
							<PARAM NAME="Src" VALUE="flash/Top1.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top1.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="69"> </embed>
						</OBJECT>
					</td>
				</tr>
				<tr>
					<td height="10">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="10" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="265">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top2.swf">
							<PARAM NAME="Src" VALUE="flash/Top2.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top2.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="10"> </embed>
						</OBJECT>
					</td>
				</tr>
				<TR>
					<TD class="PageTitle" height="20">Geo Raabta Admin :: Find Employee</TD>
				</TR>
				<tr>
					<td class="MenuBar" height="4" style="HEIGHT: 4px"><uc1:mymenus id="MyMenus1" runat="server"></uc1:mymenus></td>
				</tr>
				<tr>
					<td class="MainBG" vAlign="top" align="left">
						<TABLE id="Table4" cellSpacing="0" cellPadding="3" width="100%" align="center" border="0">
							<TR>
								<TD vAlign="top" align="left">
									<TABLE class="MainFormColor" id="Table1" cellSpacing="0" cellPadding="3" width="750" align="center"
										border="0">
										<TR>
											<TD class="OrangeFormTitle" width="320" colSpan="3"><STRONG></STRONG><STRONG></STRONG><STRONG>Search 
													Employee</STRONG></TD>
										</TR>
										<TR>
											<TD class="MenuBar" width="320"><STRONG>Pcode:</STRONG></TD>
											<TD class="MenuBar" width="60"></TD>
											<TD class="MenuBar" width="320"><STRONG>Current Station:</STRONG></TD>
										</TR>
										<TR>
											<TD width="320">
												<asp:textbox id="txtPcode" runat="server" Width="100%" CssClass="textbox"></asp:textbox></TD>
											<TD width="60"></TD>
											<TD width="320"><asp:dropdownlist id="ddLocation" runat="server" CssClass="textbox" Width="100%">
													<asp:ListItem Value="0">Select Current Station</asp:ListItem>
												</asp:dropdownlist></TD>
										</TR>
										<TR>
											<TD class="MenuBar" width="320"><STRONG>Name: </STRONG><FONT size="1">(at least 3 
													characters)</FONT></TD>
											<TD class="MenuBar" width="60"></TD>
											<TD class="MenuBar" width="320"><STRONG>Department:</STRONG></TD>
										</TR>
										<TR>
											<TD style="HEIGHT: 14px" width="320"><asp:textbox id="txtAdName" runat="server" CssClass="textbox" Width="100%"></asp:textbox></TD>
											<TD style="HEIGHT: 14px" width="60"></TD>
											<TD style="HEIGHT: 14px" width="320"><asp:dropdownlist id="ddDepartment" runat="server" CssClass="textbox" Width="100%" AutoPostBack="True">
													<asp:ListItem Value="0">Select--Department</asp:ListItem>
												</asp:dropdownlist></TD>
										</TR>
										<TR>
											<TD class="MenuBar" width="320"><STRONG>Designation:</STRONG></TD>
											<TD class="MenuBar" width="60"></TD>
											<TD class="MenuBar" width="320"><STRONG>Functional Designation:</STRONG></TD>
										</TR>
										<TR>
											<TD width="320">
												<asp:dropdownlist id="ddDesignation" runat="server" Width="100%" CssClass="textbox" AutoPostBack="True">
													<asp:ListItem Value="0">Select Designation</asp:ListItem>
												</asp:dropdownlist></TD>
											<TD width="60"></TD>
											<TD width="320"><asp:dropdownlist id="ddFunctionalDesignation" runat="server" CssClass="textbox" Width="100%">
													<asp:ListItem Value="0">Select--Functional Designation</asp:ListItem>
												</asp:dropdownlist></TD>
										</TR>
									</TABLE>
									<BR>
									<TABLE id="Table3" cellSpacing="0" cellPadding="3" width="100%" border="0">
										<TR>
											<TD noWrap align="left" colSpan="5"><asp:panel id="Panel7" runat="server" Width="750px" Visible="False">
													<TABLE class="MainFormColor" id="Table2" cellSpacing="0" cellPadding="3" width="100%" border="0">
														<TR>
															<TD class="OrangeFormTitle" width="320" colSpan="3">Special Criteria:</TD>
														</TR>
														<TR>
															<TD class="MenuBar" width="320"><STRONG>Age:</STRONG></TD>
															<TD class="MenuBar" width="60"></TD>
															<TD class="MenuBar" width="320"><STRONG>Blood Group:</STRONG></TD>
														</TR>
														<TR>
															<TD width="320">
																<asp:DropDownList id="ddAge" runat="server" CssClass="TextBox" Width="100%">
																	<asp:ListItem Value="0">Select Age</asp:ListItem>
																	<asp:ListItem Value="20-25">20-25</asp:ListItem>
																	<asp:ListItem Value="25-30">25-30</asp:ListItem>
																	<asp:ListItem Value="30-35">30-35</asp:ListItem>
																	<asp:ListItem Value="35-40">35-40</asp:ListItem>
																	<asp:ListItem Value="40-45">40-45</asp:ListItem>
																	<asp:ListItem Value="45-50">45-50</asp:ListItem>
																</asp:DropDownList><BR>
																<asp:Label id="Label6" runat="server" CssClass="Label" Visible="False" ForeColor="Red">Please Select Option</asp:Label></TD>
															<TD width="60"></TD>
															<TD width="320">
																<asp:DropDownList id="ddBloodGrp" runat="server" CssClass="TextBox" Width="100%">
																	<asp:ListItem Value="0">Select Blood Group</asp:ListItem>
																	<asp:ListItem Value="1">A+</asp:ListItem>
																	<asp:ListItem Value="2">A-</asp:ListItem>
																	<asp:ListItem Value="3">B+</asp:ListItem>
																	<asp:ListItem Value="4">B-</asp:ListItem>
																	<asp:ListItem Value="5">AB+</asp:ListItem>
																	<asp:ListItem Value="6">AB-</asp:ListItem>
																	<asp:ListItem Value="7">O+</asp:ListItem>
																	<asp:ListItem Value="8">O-</asp:ListItem>
																</asp:DropDownList><BR>
																<asp:Label id="Label7" runat="server" CssClass="Label" Visible="False" ForeColor="Red">Please Select Option</asp:Label></TD>
														</TR>
														<TR>
															<TD class="MenuBar" width="320"><STRONG>Date of Join:</STRONG></TD>
															<TD class="MenuBar" width="60"></TD>
															<TD class="MenuBar" width="320"><STRONG>Category:</STRONG></TD>
														</TR>
														<TR>
															<TD width="320">
																<asp:DropDownList id="ddDOJ" runat="server" CssClass="TextBox" Width="100%">
																	<asp:ListItem Value="0">Select Date Of Join</asp:ListItem>
																	<asp:ListItem Value="1-2">1-2 Year</asp:ListItem>
																	<asp:ListItem Value="2-3">2-3 Year</asp:ListItem>
																	<asp:ListItem Value="3-4">3-4 Year</asp:ListItem>
																	<asp:ListItem Value="4-5">4-5 Year</asp:ListItem>
																	<asp:ListItem Value="5-20">above 5</asp:ListItem>
																</asp:DropDownList><BR>
																<asp:Label id="Label8" runat="server" CssClass="Label" Visible="False" ForeColor="Red">Please Select Option</asp:Label></TD>
															<TD width="60"></TD>
															<TD width="320">
																<asp:DropDownList id="ddCategory" runat="server" CssClass="TextBox" Width="100%">
																	<asp:ListItem Value="0">Select--Category</asp:ListItem>
																</asp:DropDownList><BR>
																<asp:Label id="Label9" runat="server" CssClass="Label" Visible="False" ForeColor="Red">Please Select Option</asp:Label></TD>
														</TR>
														<TR>
															<TD class="menubar" width="320"><STRONG>Gender:</STRONG></TD>
															<TD class="menubar" width="60"></TD>
															<TD class="menubar" width="320"><STRONG>Reporting Station:</STRONG></TD>
														</TR>
														<TR>
															<TD width="320"><STRONG>
																	<asp:DropDownList id="ddGender" runat="server" CssClass="textbox" Width="100%">
																		<asp:ListItem Value="0">Select--Gender</asp:ListItem>
																		<asp:ListItem Value="1">Male</asp:ListItem>
																		<asp:ListItem Value="2">Female</asp:ListItem>
																	</asp:DropDownList></STRONG></TD>
															<TD width="60"></TD>
															<TD width="320">
																<asp:DropDownList id="ddReportingStation" runat="server" CssClass="textbox" Width="318px" Visible="False">
																	<asp:ListItem Value="0">Select--Reporting Station</asp:ListItem>
																</asp:DropDownList></TD>
														</TR>
													</TABLE>
												</asp:panel><asp:button id="btnSpecial" runat="server" Text="Special Criteria" Font-Bold="True" Font-Names="Courier New"></asp:button></TD>
										</TR>
										<TR>
											<TD vAlign="middle" align="center" colSpan="5"><span id="txtHint"></span><BR>
											</TD>
										</TR>
									</TABLE>
								</TD>
							</TR>
						</TABLE>
					</td>
				</tr>
				<tr>
					<td vAlign="middle" align="center" height="20">Copyright © 2005 Independent Media 
						Corporation <A href="http://www.geo.tv">www.geo.tv</A></td>
				</tr>
			</table>
			<asp:TextBox id="TextBox1" runat="server" style="VISIBILITY:hidden"></asp:TextBox></form>
	</body>
</HTML>
