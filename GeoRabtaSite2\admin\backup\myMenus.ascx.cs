namespace GeoRabtaSite.admin
{
	using System;
	using System.Data;
	using System.Drawing;
	using System.Web;
	using System.Web.UI.WebControls;
	using System.Web.UI.HtmlControls;
	using System.Data.SqlClient;
	using System.Web.SessionState;

	
	/// <summary>
	///		Summary description for myMenus.
	/// </summary>
	public class myMenus : System.Web.UI.UserControl
	{
		protected skmMenu.Menu Menu2;
		//protected skmMenu.Menu Menu1;
		SqlConnection con;
		
		private void UpdateMenu()
		{
		
			string userId="";
			try
			{
				userId=Session["user_id"].ToString();  
			}
			catch (Exception ex)
			{
				string s=ex.Message;
				Response.Redirect("../login.aspx");
			}
						
			skmMenu.MenuItem mi=new skmMenu.MenuItem("<TABLE style='cursor:hand' height='29' cellSpacing='0' cellPadding='0' width='100%' border='0'>" +
				"<TR><TD width='7'><img src='../images/menu-on-bg-left.gif'></TD>" +
				"<TD background='../images/menu-on-bg.gif'><font color=white>User Management</font></TD>" +
				"<TD width='7'><img src='../images/menu-on-bg-right.gif'></TD></TR></TABLE>","","Managment of HR ERP");
		    
			if(GeoSecurity.isControlVisible(2,36,userId,"View"))
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Project Setup</font></TD>" +
					"</TR></TABLE>","ProjectFormsAndControls.aspx","Manage Diffrent Projects & their Controls"));
			
			if(GeoSecurity.isControlVisible(2,37,userId,"View")==true)
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Account Management</font></TD>" +
					"</TR></TABLE>","SelectUser.aspx","Managment of Users and assign Roles"));
			
			if(GeoSecurity.isControlVisible(2,38,userId,"Create"))
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Roles</font></TD>" +
					"</TR></TABLE>","roles.aspx","Roles Creations"));
			
			if(GeoSecurity.isControlVisible(2,38,userId,"Manage"))
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Role Management</font></TD>" +
					"</TR></TABLE>","newroles.aspx","Roles management"));  
			
			
//			if(GeoSecurity.isControlVisible(2,37,userId,"Change"))
//				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
//					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Change Password</font></TD>" +
//					"</TR></TABLE>","../changepassword.aspx","Change Password"));
			
			if(GeoSecurity.isControlVisible(2,51,userId,"View"))
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Account Creation For Compensation View</font></TD>" +
					"</TR></TABLE>","PasswordCreation.aspx","Account Creation For Compensation View"));
			
			if(GeoSecurity.isControlVisible(2,50,userId,"View"))
			mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
				"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Password Management</font></TD>" +
				"</TR></TABLE>","PasswordManagement.aspx","Password Management"));
			
			mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
				"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Back To Raabta</font></TD>" +
				"</TR></TABLE>","../main.htm","Geo Raabta"));
			
			mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
				"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Logout</font></TD>" +
				"</TR></TABLE>","logout.aspx","Logout"));
			Menu2.Items.Add(mi);
			mi.Dispose();   
			
			mi=new skmMenu.MenuItem("<TABLE style='cursor:hand' height='29' cellSpacing='0' cellPadding='0' width='100%' border='0'>" +
				"<TR><TD width='7'><img src='../images/menu-on-bg-left.gif'></TD>" +
				"<TD background='../images/menu-on-bg.gif'><font color=white>Employee Management</font></TD>" +
				"<TD width='7'><img src='../images/menu-on-bg-right.gif'></TD></TR></TABLE>","","Managment of Employees");
						
			if(GeoSecurity.isControlVisible(2,35,userId,"View"))
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Organogram</font></TD>" +
					"</TR></TABLE>","OrganizationChart.aspx",""));
            
			if(GeoSecurity.isControlVisible(2,35,userId,"Print"))
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Print Organogram</font></TD>" +
					"</TR></TABLE>","printorgano.aspx",""));
			
			if(GeoSecurity.isControlVisible(2,32,userId,"View"))
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Create New Employee Profile</font></TD>" +
					"</TR></TABLE>","NewEmployee.aspx",""));

			if(GeoSecurity.isControlVisible(2,32,userId,"Edit")==true || GeoSecurity.isControlVisible(2,32,userId,"Family")==true || GeoSecurity.isControlVisible(2,32,userId,"EducationalInfo")==true || GeoSecurity.isControlVisible(2,33,userId,"ViewAll")==true ) 
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Search and Edit Employee</font></TD>" +
					"</TR></TABLE>","CompanyPersonnel.aspx",""));  

			
			if(GeoSecurity.isControlVisible(2,31,userId,"View")==true)
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Update Profile Change Request</font></TD>" +
					"</TR></TABLE>","UpdateRequests.aspx",""));

			if(GeoSecurity.isControlVisible(2,44,userId,"View"))
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Institute Details</font></TD>" +
					"</TR></TABLE>","Institute.aspx",""));

			if(GeoSecurity.isControlVisible(2,22,userId,"View")==true)
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Educational Degrees</font></TD>" +
					"</TR></TABLE>","Degree.aspx","Add new Educational Degrees"));

            if(GeoSecurity.isControlVisible(2,43,userId,"Page View")==true)
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Employee Verification</font></TD>" +
					"</TR></TABLE>","SummaryReport.aspx","Employee Verification Summary Report"));
			
			if(GeoSecurity.isControlVisible(2,48,userId,"View")==true)
			mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
				"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Advance Report</font></TD>" +
				"</TR></TABLE>","DynamicReport.aspx",""));
				
			if (mi.SubItems.Count>0)
			   Menu2.Items.Add(mi);
			mi.Dispose();   

			mi=new skmMenu.MenuItem("<TABLE style='cursor:hand' height='29' cellSpacing='0' cellPadding='0' width='100%' border='0'>" +
				"<TR><TD width='7'><img src='../images/menu-on-bg-left.gif'></TD>" +
				"<TD background='../images/menu-on-bg.gif'><font color=white>Organization Setup</font></TD>" +
				"<TD width='7'><img src='../images/menu-on-bg-right.gif'></TD></TR></TABLE>","","");
			
			
			if(GeoSecurity.isControlVisible(2,1,userId,"View"))
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>SBU</font></TD>" +
					"</TR></TABLE>","SystemBusinessUnit.aspx",""));

			if(GeoSecurity.isControlVisible(2,2,userId,"View"))
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Department</font></TD>" +
					"</TR></TABLE>","Department.aspx",""));              
			
			if(GeoSecurity.isControlVisible(2,29,userId,"View"))
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Designation</font></TD>" +
					"</TR></TABLE>","Designation.aspx",""));
			
			if(GeoSecurity.isControlVisible(2,3,userId,"View"))
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Categorization</font></TD>" +
					"</TR></TABLE>","Categorization.aspx",""));
			
			if(GeoSecurity.isControlVisible(2,4,userId,"View"))
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Station</font></TD>" +
					"</TR></TABLE>","City.aspx",""));
			if(GeoSecurity.isControlVisible(2,47,userId,"View"))
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Dictionary</font></TD>" +
					"</TR></TABLE>","Dictionary.aspx",""));
			if (mi.SubItems.Count>0)
				Menu2.Items.Add(mi); 
			mi.Dispose();
   
			//=================================Workflow Management==========================//
			mi=new skmMenu.MenuItem("<TABLE style='cursor:hand' height='29' cellSpacing='0' cellPadding='0' width='100%' border='0'>" +
				"<TR><TD width='7'><img src='../images/menu-on-bg-left.gif'></TD>" +
				"<TD background='../images/menu-on-bg.gif'><font color=white>Workflow Management</font></TD>" +
				"<TD width='7'><img src='../images/menu-on-bg-right.gif'></TD></TR></TABLE>","","Workflow Managment of Raabta");
			
			if(GeoSecurity.isControlVisible(2,53,userId,"View")==true )
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Create Queuelist Specialist</font></TD>" +
					"</TR></TABLE>","ManageSpecialist.aspx",""));

			if(GeoSecurity.isControlVisible(2,57,userId,"View")==true )
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Edit Queuelist Specialist</font></TD>" +
					"</TR></TABLE>","EditSpecialist.aspx",""));

			if(GeoSecurity.isControlVisible(2,54,userId,"View")==true )
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Data Verification Workflow</font></TD>" +
					"</TR></TABLE>","ManageEmployeeRequest.aspx",""));

			if(GeoSecurity.isControlVisible(2,55,userId,"View")==true )
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Employee Grievance Workflow</font></TD>" +
					"</TR></TABLE>","EmployeeGrievance.aspx",""));

			if(GeoSecurity.isControlVisible(2,56,userId,"View")==true )
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>My Queuelist</font></TD>" +
					"</TR></TABLE>","ManageRequest.aspx",""));
			if(GeoSecurity.isControlVisible(2,56,userId,"View")==true )
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>MyTeam Queuelist</font></TD>" +
					"</TR></TABLE>","TeamQueuelist.aspx",""));
			if (mi.SubItems.Count>0)
				Menu2.Items.Add(mi);
			mi.Dispose();
			//==============================================================================//
             string showform=""; 
			mi=new skmMenu.MenuItem("<TABLE style='cursor:hand' height='29' cellSpacing='0' cellPadding='0' width='100%' border='0'>" +
				"<TR><TD width='7'><img src='../images/menu-on-bg-left.gif'></TD>" +
				"<TD background='../images/menu-on-bg.gif'><font color=white>Content Management</font></TD>" +
				"<TD width='7'><img src='../images/menu-on-bg-right.gif'></TD></TR></TABLE>","","Content Managment of Raabta");
			
			if(GeoSecurity.isControlVisible(2,9,userId,"Add Policies")==true || GeoSecurity.isControlVisible(2,9,userId,"Delete Policies")==true || GeoSecurity.isControlVisible(2,9,userId,"Edit Policies")==true)
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Policies</font></TD>" +
					"</TR></TABLE>","GeoPolicy.aspx",""));

			if(GeoSecurity.isControlVisible(2,10,userId,"Add Forms")==true || GeoSecurity.isControlVisible(2,10,userId,"Edit Forms")==true || GeoSecurity.isControlVisible(2,10,userId,"Delete Forms")==true || GeoSecurity.isControlVisible(2,10,userId,"Add/Edit")==true)
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>IMC Forms</font></TD>" +
					"</TR></TABLE>","GeoForms.aspx","")); 
             
			if(GeoSecurity.isControlVisible(2,41,userId,"create")==true || GeoSecurity.isControlVisible(2,41,userId,"assign")==true)
			{  if(GeoSecurity.isControlVisible(2,41,userId,"create")==true && GeoSecurity.isControlVisible(2,41,userId,"assign")==true)
				   showform="Evaluationforms.aspx";
			   if(GeoSecurity.isControlVisible(2,41,userId,"create")==true && GeoSecurity.isControlVisible(2,41,userId,"assign")==false)
					showform="Evaluationforms.aspx";
			   if(GeoSecurity.isControlVisible(2,41,userId,"create")==false && GeoSecurity.isControlVisible(2,41,userId,"assign")==true)
					showform="EmployeeTalaash.aspx";

				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Survey Forms</font></TD>" +
					"</TR></TABLE>",showform,"")); 
			}
			if(GeoSecurity.isControlVisible(2,11,userId,"Add")==true || GeoSecurity.isControlVisible(2,11,userId,"Edit")==true || GeoSecurity.isControlVisible(2,11,userId,"Delete")==true)
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Quick Links</font></TD>" +
					"</TR></TABLE>","Links.aspx",""));

			if(GeoSecurity.isControlVisible(2,12,userId,"Post/Approved")==true || GeoSecurity.isControlVisible(2,12,userId,"Delete")==true || GeoSecurity.isControlVisible(2,39,userId,"Threer")==true)
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Article Management</font></TD>" +
					"</TR></TABLE>","PostArticles.aspx",""));
            
			if(GeoSecurity.isControlVisible(2,17,userId,"RequestNews")==true )
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Approved Messages For Team Geo</font></TD>" +
					"</TR></TABLE>","Postnews.aspx",""));
             
			if(GeoSecurity.isControlVisible(2,13,userId,"Post/Approved Jokes")==true || GeoSecurity.isControlVisible(2,13,userId,"Delete Jokes")==true)
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Update Jokes</font></TD>" +
					"</TR></TABLE>","PostJokess.aspx",""));

			if(GeoSecurity.isControlVisible(2,15,userId,"Approve")==true)
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Tasaweer</font></TD>" +
					"</TR></TABLE>","GeoTasaweer.aspx",""));

			if(GeoSecurity.isControlVisible(2,34,userId,"View")==true)
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>Exception Report</font></TD>" +
					"</TR></TABLE>","ActivityReport.aspx",""));
			if (mi.SubItems.Count>0)
				Menu2.Items.Add(mi); 
			mi.Dispose();       
			
			mi=new skmMenu.MenuItem("<TABLE style='cursor:hand' height='29' cellSpacing='0' cellPadding='0' width='100%' border='0'>" +
				"<TR><TD width='7'><img src='../images/menu-on-bg-left.gif'></TD>" +
				"<TD background='../images/menu-on-bg.gif'><font color=white>Help</font></TD>" +
				"<TD width='7'><img src='../images/menu-on-bg-right.gif'></TD></TR></TABLE>","","How To Use");
		    
			if(GeoSecurity.isControlVisible(2,49,userId,"View"))
				mi.SubItems.Add(new skmMenu.MenuItem("<TABLE style='cursor:hand' height='22' cellSpacing='0' cellPadding='3' width='100%' border='0'>" +
					"<TR><td bgcolor=gray width=1><img width=1></td><TD background='../images/menu-off-bg.gif'><font color=black>How to Manage Organogram</font></TD>" +
					"</TR></TABLE>","training.aspx","How to Manage Organogram"));

			if (mi.SubItems.Count>0)
				Menu2.Items.Add(mi); 
			mi.Dispose();
			
			//Menu2.CssClass = "menustyle";
			//Menu2.DefaultCssClass = "menuitem";
			//Menu2.DefaultMouseOverCssClass = "mouseover";

			Menu2.HighlightTopMenu = true;
			Menu2.Opacity="100";
			Menu2.zIndex = 100;
			Menu2.ClickToOpen = true;
			Menu2.Dispose();
		}



		private void Page_Load(object sender, System.EventArgs e)
		{

			con=new SqlConnection(Connection.ConnectionString);
			con.Open();

			if (!IsPostBack)
			{
				UpdateMenu();
			}
			con.Close();
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		///		Required method for Designer support - do not modify
		///		the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			this.Menu2.MenuItemClick += new skmMenu.MenuItemClickedEventHandler(this.Menu2_MenuItemClick);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		private void Menu2_MenuItemClick(object sender, skmMenu.MenuItemClickEventArgs e)
		{
		
		}
	}
}
