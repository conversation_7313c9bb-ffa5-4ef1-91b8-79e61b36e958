<%@ Register TagPrefix="uc1" TagName="myMenus" Src="myMenus.ascx" %>
<%@ Page CodeBehind="Designation.aspx.cs" Language="c#" AutoEventWireup="false" Inherits="GeoRabtaSite.GeoDesignation" smartNavigation="False"%>
<%@ Register TagPrefix="ew" Namespace="eWorld.UI" Assembly="eWorld.UI" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Raabta Admin :: Designation</title>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="RaabtaAdmin.css" type="text/css" rel="stylesheet">
		<LINK href="../Styles4.css" type="text/css" rel="stylesheet">
		<style type="text/css">.style1 { FONT-WEIGHT: bold; FONT-SIZE: 8px }
	.style2 { FONT-WEIGHT: bold; FONT-SIZE: 9pt }
		</style>
		<script src="../client.js"></script>
		<script language="javascript">
			var highlightColor='blue';
			var highlighText='white';

			var normalColor='white';
			var normalText='black';
			
			var idx=-1;
			var totalItems=200;
			
			function CloseIt()
			{
				var txtDesg=document.getElementById("txtName");
				var txtTitle=document.getElementById("txtTitle");
				var divMain=document.getElementById("divMain");
				var td=document.getElementById("td"+idx);
				if (idx==-1)
				{
					if (txtDesg!=null)
					{
						txtDesg.value="";
						txtDesg.focus();
					}
					else if (txtTitle!=null)
					{
						//txtTitle.value="";
						txtTitle.focus();
					}
				}
				if (idx!=-1)
				{
					//txtDesg.value=td.innerText;				
					if (txtDesg!=null)
					{
						txtDesg.value=td.innerText;
						txtDesg.focus();
					}
					else if (txtTitle!=null)
					{
						//txtTitle.value=td.innerText;
						txtTitle.focus();
					}
				}
				
				divMain.style.visibility="hidden";
				var ddMdept=document.getElementById("ddMdept");
				if (ddMdept!=null)
					ddMdept.style.visibility="visible";
			}
			
			function CloseIt2()
			{
				/*var txtDesg=document.getElementById("txtName")
				var td=document.getElementById("td"+idx);
				if (idx==-1)
				{
					if (txtDesg!=null)
						txtDesg.value=""
				}
				if (idx!=-1)
				{
					txtDesg.value=td.innerText;				
				}
				divMain.style.visibility="hidden";
				*/
			}
			
			function disableEnterKey(e)
			{
			    var key;
			    if(window.event)
					key = window.event.keyCode;     //IE
				else
					key = e.which;     //firefox
				if(key == 13)
					return false;
				else
					return true;
			}
//-----------------------------------			
// Functional designation			
//-----------------------------------

			function GetFD(e)
			{
				var divMain=document.getElementById("divMain");
				var tabMain=document.getElementById("tabMain");
				//totalItems=tabMain.rows.length;
				//var ddMdept=document.getElementById("ddMdept");
				//divMain.style.visibility="hidden";
				//ddMdept.style.visibility="visible";
			
			
				// Deselect previous
				
				var tr=document.getElementById("tr"+idx);	
				if(tr != null)
				{
					tr.style.background='white';
					tr.style.color='blue';
				}
			
				//alert(totalItems);
				//var txtIDX=document.getElementById("txtIdx")
				//txtIDX.value=idx;
				//var txtItems=document.getElementById("txtItems");
				//txtItems.value=totalItems;
				
				var txtTitle=document.getElementById("txtTitle").value;
				
				if (txtTitle!=null)
				{
					if (txtTitle=="")
					{
						divMain.style.visibility="hidden";
						//var ddMdept=document.getElementById("ddMdept");
						//ddMdept.style.visibility="visible";
					}
				}
				//var ddMdept=document.getElementById("ddMdept");
				//var divMain=document.getElementById("divMain");
				//var txtName=document.getElementById("txtName");
				//ddMdept.style.visibility="hidden";
				//divMain.style.visibility="visible";
				//divMain.style.top= findPosY(txtName)+23;
				//divMain.style.left=findPosX(txtName);
				//divMain.style.width=txtName.clientWidth;
				//divMain.scrollTop=0;
				
				var eKey=false;
				kc=event.keyCode;
				
				if (kc==13)
				{
					if (idx>-1)
					{
						var td=document.getElementById('td'+idx);
						var txtTitle=document.getElementById("txtTitle");
						txtTitle.value=td.innerText;
						txtTitle.focus();
						idx=-1;
						divMain.style.visibility="hidden";
						//var ddMdept=document.getElementById("ddMdept");
						//ddMdept.style.visibility="visible";
					}
				}
				
				if(kc==38)
				{
					eKey=true;
					if(idx==-1)
						idx=totalItems-1
					else
						idx--;
				}
				if(kc==40)
				{
					eKey=true;
					// down;
					if(idx==totalItems)
						idx=-1;
					else
						idx++;
				}

				var tr=document.getElementById("tr"+idx);	
				if(tr != null)
				{
					//tr.style.color="white";
					//tr.bgColor="blue";
					tr.style.background='blue';
					tr.style.color='white';
				}

				if ((kc>=48 && kc<=57) || (kc>=65 && kc<=90) || kc==32 || kc==8 || kc==46 )
				{
					idx=-1;
					var txtTitle=document.getElementById("txtTitle");
					if (txtTitle.value!="")
					{
						var desgParameter="id="+txtTitle.value+"&sbu=-1";
						//alert(desgParameter);
						SearchFD('../searchfd.aspx',desgParameter);
					}
					else
					{
						divMain.style.visibility="hidden";
						//var ddMdept=document.getElementById("ddMdept");
						//ddMdept.style.visibility="visible";
					}
				}
				if(txtTitle.value=="")
				{
					divMain.style.visibility="hidden";
					//var ddMdept=document.getElementById("ddMdept");
					//ddMdept.style.visibility="visible";					
				}
			}


//-----------------------------------
// Functional designation /
//-----------------------------------			
			
			function GetDesignations(e)
			{
				var divMain=document.getElementById("divMain");
				var ddMdept=document.getElementById("ddMdept");
				//divMain.style.visibility="hidden";
				//ddMdept.style.visibility="visible";
				//var tabMain=document.getElementById("tabMain");
				//totalItems=tabMain.rows.length;
				
				// Deselect previous
				
				var tr=document.getElementById("tr"+idx);	
				if(tr != null)
				{
					//tr.style.color="black";
					//tr.bgColor="white";
					tr.style.background='white';
					tr.style.color='black';
				}
			
				//alert(totalItems);
				//var txtIDX=document.getElementById("txtIdx")
				//txtIDX.value=idx;
				//var txtItems=document.getElementById("txtItems");
				//txtItems.value=totalItems;
				
				var desg=document.getElementById("txtName").value;
				
				if (desg!=null)
				{
					if (desg=="")
					{
						divMain.style.visibility="hidden";
						var ddMdept=document.getElementById("ddMdept");
						ddMdept.style.visibility="visible";
					}
				}
				//var ddMdept=document.getElementById("ddMdept");
				//var divMain=document.getElementById("divMain");
				//var txtName=document.getElementById("txtName");
				//ddMdept.style.visibility="hidden";
				//divMain.style.visibility="visible";
				//divMain.style.top= findPosY(txtName)+23;
				//divMain.style.left=findPosX(txtName);
				//divMain.style.width=txtName.clientWidth;
				//divMain.scrollTop=0;
				
				var eKey=false;
				kc=e.keyCode;
				
				if (kc==13)
				{
					if (idx>-1)
					{
						var td=document.getElementById('td'+idx);
						var txtDesg=document.getElementById("txtName");
						txtDesg.value=td.innerText;
						txtDesg.focus();
						idx=-1;
						divMain.style.visibility="hidden";
						var ddMdept=document.getElementById("ddMdept");
						ddMdept.style.visibility="visible";

					}
				}
				
				if(kc==38)
				{
					eKey=true;
					if(idx==-1)
						idx=totalItems-1
					else
						idx--;
				}
				if(kc==40)
				{
					eKey=true;
					// down;
					if(idx==totalItems)
						idx=-1;
					else
						idx++;
				}

				var tr=document.getElementById("tr"+idx);	
				if(tr != null)
				{
					tr.style.background='blue';
					tr.style.color='white';
				}

				if ((kc>=48 && kc<=57) || (kc>=65 && kc<=90) || kc==32 || kc==8 || kc==46 )
				{
					idx=-1;
					var desg=document.getElementById("txtName");
					if (desg.value!="")
					{
						var desgParameter="id="+desg.value+"&sbu=-1";
						//alert(desgParameter);
						SearchDesignation('../searchdesignation.aspx',desgParameter);
					}
					else
					{
						divMain.style.visibility="hidden";
						var ddMdept=document.getElementById("ddMdept");
						ddMdept.style.visibility="visible";
					}
				}
				if(desg.value=="")
				{
					divMain.style.visibility="hidden";
					var ddMdept=document.getElementById("ddMdept");
					ddMdept.style.visibility="visible";					
				}
			}
			
			function Highlight(i)
			{
				var k=idx;
				var tr=document.getElementById("tr"+k);	
				if(tr != null)
				{
					//tr.style.color="black";
					//tr.bgColor="white";
					tr.style.background='white';
					tr.style.color='black';
					
				}
				var tr=document.getElementById("tr"+i);	
				if(tr != null)
				{
					tr.style.background='blue';
					tr.style.color='white';
					idx=i;
				}
			}
			
			function SelectIt(i)
			{
				var td=document.getElementById("td"+i);
				var txtDesg=document.getElementById("txtName");
				var ddMdept=document.getElementById("ddMdept");
				var txtTitle=document.getElementById("txtTitle");
				if(txtDesg!=null)
				{
					txtDesg.value=td.innerText;
					txtDesg.focus();
				}
				else if (txtTitle!=null)
				{
					txtTitle.value=td.innerText;
					txtTitle.focus();	
				}
				divMain.style.visibility="hidden";
				if(ddMdept!=null)
					ddMdept.style.visibility="visible";			
			}
			
			function findPosX(obj)
{
	var curleft = 0;
	if(obj.offsetParent)
		while(1) 
		{
			curleft += obj.offsetLeft;
			if(!obj.offsetParent)
				break;
			obj = obj.offsetParent;
        }
    else if(obj.x)
        curleft += obj.x;
    return curleft;
}

function findPosY(obj)
{
	var curtop = 0;
	if(obj.offsetParent)
		while(1)
		{
			curtop += obj.offsetTop;
			if(!obj.offsetParent)
				break;
			obj = obj.offsetParent;
		}
	else if(obj.y)
	curtop += obj.y;
	return curtop;
}

		</script>
		<script language="javascript">
		function checkVal()
		{
		 var IsConfirm=confirm('If you delete selected employee on this functional designation then the employee will have same current designation otherwise you have to assign new functional designation to that employee,Are You Sure To Delete');
		 if(IsConfirm)
		 {
		  var strReturn;
		  strReturn=window.showModalDialog('EndDate.aspx?id=1',null,'dialogWidth:400px;dialogHeight:280px');
		  if(strReturn != null)
		   { 
		  document.getElementById('txttest').value=strReturn;
		  event.returnValue=true;
		  return true;  
		   }
		   else
		   {
		    event.returnValue=false;
		    return false;
		   }
		 }
		  else
		  {
		   event.returnValue=false;
		   return false;
		  }
		}
		
		function getDate()
		{
		  var strReturn;
		  strReturn=window.showModalDialog('EndDate.aspx?id=2',null,'dialogWidth:400px;dialogHeight:280px');
		  if(strReturn != null)
		   { 
		  document.getElementById('txtDate').value=strReturn;
		  event.returnValue=true;
		  return true;  
		   }
		   else
		   {
		    event.returnValue=false;
		    return false;
		   }
		}
		function getPrevDate()
		{
		  var strReturn;
		  strReturn=window.showModalDialog('EndDate.aspx?id=3',null,'dialogWidth:400px;dialogHeight:280px');
		  if(strReturn != null)
		   { 
		  document.getElementById('txtPrev').value=strReturn;
		  event.returnValue=true;
		  return true;  
		   }
		   else
		   {
		    event.returnValue=false;
		    return false;
		   }
		}
		
		
		
		</script>
		<script language="javascript" id="clientEventHandlersJS">
<!--

function window_onscroll() {
document.getElementById("txtX").value=document.body.scrollTop;
document.getElementById("txtY").value=document.body.scrollLeft;
}

function window_onload() {
document.body.scrollTop=document.getElementById("txtX").value;
document.body.scrollLeft=document.getElementById("txtY").value;
}

//-->
		</script>
	</HEAD>
	<body language="javascript" dir="ltr" onscroll="return window_onscroll()" bottomMargin="0"
		bgProperties="fixed" leftMargin="0" topMargin="0" onload="return window_onload()"
		rightMargin="0">
		<form id="myForm" name="MyForm" runat="server">
			<DIV id="divMain" style="BORDER-TOP-WIDTH: 1px; FONT-WEIGHT: normal; BORDER-LEFT-WIDTH: 1px; FONT-SIZE: 12px; BORDER-LEFT-COLOR: gray; LEFT: 112px; VISIBILITY: hidden; BORDER-BOTTOM-WIDTH: 1px; BORDER-BOTTOM-COLOR: gray; OVERFLOW: auto; WIDTH: 312px; COLOR: #4d4d4d; BORDER-TOP-COLOR: gray; FONT-FAMILY: Verdana; POSITION: absolute; TOP: 256px; HEIGHT: 284px; BORDER-RIGHT-WIDTH: 1px; BORDER-RIGHT-COLOR: gray"><SPAN id="txtHint"></SPAN></DIV>
			<table height="100%" cellSpacing="0" cellPadding="0" width="780" align="center" bgColor="#ffffff"
				border="0">
				<tr>
					<td vAlign="middle" align="left" height="69">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="69" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="1826">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top1.swf">
							<PARAM NAME="Src" VALUE="flash/Top1.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top1.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="69"> </embed>
						</OBJECT>
					</td>
				</tr>
				<tr>
					<td height="10">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="10" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="265">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top2.swf">
							<PARAM NAME="Src" VALUE="flash/Top2.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top2.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="10"> </embed>
						</OBJECT>
					</td>
				</tr>
				<TR>
					<TD class="PageTitle" height="20">Geo Raabta Admin :: Designation</TD>
				</TR>
				<tr>
					<td class="MenuBar" height="20"><uc1:mymenus id="MyMenus1" runat="server"></uc1:mymenus></td>
				</tr>
				<tr>
					<td class="mainbg" vAlign="top" align="left"><BR>
						<TABLE class="MainFormColor" id="myRow" cellSpacing="0" cellPadding="3" width="750" align="center"
							border="0" runat="server">
							<TR>
								<TD class="OrangeFormTitle" colSpan="3">Designation Info</TD>
							</TR>
							<TR>
								<TD class="MenuBar" width="320"><asp:label id="Label3" runat="server" Font-Bold="True">Department :</asp:label></TD>
								<TD class="MenuBar" width="90"></TD>
								<TD class="MenuBar" width="320"></TD>
							</TR>
							<TR>
								<TD width="320"><asp:dropdownlist id="dropDownDept" runat="server" CssClass="textbox" Width="100%">
										<asp:ListItem Value="0">Select--Department</asp:ListItem>
									</asp:dropdownlist><BR>
									<asp:customvalidator id="CustomValidator2" runat="server" ControlToValidate="dropDownDept" ErrorMessage="Please select department"></asp:customvalidator></TD>
								<TD width="60"></TD>
								<TD width="320"></TD>
							</TR>
							<TR>
								<TD class="MenuBar" width="320"><asp:label id="Label2" runat="server" Font-Bold="True">Designation Name:</asp:label><asp:requiredfieldvalidator id="RequiredFieldValidator5" runat="server" ControlToValidate="txtName" ErrorMessage="Field cannot left empty"></asp:requiredfieldvalidator></TD>
								<TD class="MenuBar" width="60"></TD>
								<TD class="MenuBar" width="320"><asp:label id="Label5" runat="server" Font-Bold="True">Category:</asp:label></TD>
							</TR>
							<TR>
								<TD style="HEIGHT: 1px" vAlign="top" width="320"><asp:textbox id="txtName" tabIndex="1" runat="server" CssClass="textbox" Width="100%" autocomplete="off"
										MaxLength="500"></asp:textbox></TD>
								<TD style="HEIGHT: 1px" vAlign="top" width="60"></TD>
								<TD style="HEIGHT: 1px" vAlign="top" width="320"><asp:dropdownlist id="ddCategory" runat="server" CssClass="textbox" Width="100%">
										<asp:ListItem Value="0">Select--Category</asp:ListItem>
									</asp:dropdownlist></TD>
							</TR>
							<TR>
								<TD class="MenuBar" vAlign="top" width="320"><asp:label id="Label7" runat="server" Font-Bold="True">Total Vacancies:</asp:label><asp:requiredfieldvalidator id="RequiredFieldValidator6" runat="server" ControlToValidate="txtVacancies" ErrorMessage="Field cannot left empty"></asp:requiredfieldvalidator></TD>
								<TD class="MenuBar" vAlign="top" width="60"></TD>
								<TD class="MenuBar" vAlign="top" width="320"></TD>
							</TR>
							<TR>
								<TD vAlign="top" width="320"><asp:textbox id="txtVacancies" runat="server" CssClass="textbox" Width="100%" MaxLength="4"></asp:textbox><BR>
									<asp:label id="Label11" runat="server" Visible="False" ForeColor="Red">Vacancy must be greater than 0</asp:label><asp:regularexpressionvalidator id="RegularExpressionValidator3" runat="server" ControlToValidate="txtVacancies"
										ErrorMessage="Enter numeric values only" ValidationExpression="^[0-9]*$"></asp:regularexpressionvalidator></TD>
								<TD vAlign="top" width="60"></TD>
								<TD vAlign="top" width="320"><asp:panel id="Panel1" runat="server" Visible="False">
										<asp:label id="Label1" runat="server" Font-Bold="True" Width="104px" Visible="False">Designation Id</asp:label>
										<BR>
										<asp:TextBox id="txtDesId" runat="server" Width="100%"></asp:TextBox>
									</asp:panel></TD>
							</TR>
							<TR>
								<TD class="MenuBar" vAlign="top" colSpan="3"><asp:label id="lblMsg" runat="server" Font-Bold="True" Visible="False" ForeColor="Red"></asp:label><asp:label id="lblNewDes" runat="server" Font-Bold="True" Visible="False"></asp:label></TD>
							</TR>
							<TR>
								<TD vAlign="top" colSpan="3"><asp:button id="btnQueryButton" tabIndex="4" runat="server" Width="160px" Text="New Designation"></asp:button><asp:button id="btnFunctionalDesignation" tabIndex="4" runat="server" Width="160px" Text="Funcational  Designation"
										Enabled="False"></asp:button><asp:button id="btnCancel" tabIndex="5" runat="server" Width="160px" Text="Cancel" Enabled="False"
										CausesValidation="False"></asp:button></TD>
							</TR>
						</TABLE>
						<TABLE class="MainFormColor" id="CurrentFuncDesignation" cellSpacing="0" cellPadding="3"
							width="750" align="center" border="0" runat="server">
							<TR>
								<TD class="MenuBar"><asp:linkbutton id="lnkNewDesignation" runat="server" CausesValidation="False">Add New Designation</asp:linkbutton></TD>
							</TR>
							<TR>
								<TD class="OrangeFormTitle"><STRONG>Current Functional Designations:</STRONG></TD>
							</TR>
							<TR>
								<TD><asp:datagrid id="DataGrid2" runat="server" Width="100%" AutoGenerateColumns="False" CellPadding="3"
										BackColor="White" BorderWidth="1px" BorderColor="Navy" BorderStyle="Solid" GridLines="Horizontal"
										AllowSorting="True">
										<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
										<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
										<AlternatingItemStyle CssClass="AlternetItem"></AlternatingItemStyle>
										<ItemStyle CssClass="Item"></ItemStyle>
										<HeaderStyle Font-Bold="True" ForeColor="#F7F7F7" CssClass="header"></HeaderStyle>
										<Columns>
											<asp:TemplateColumn HeaderText="SNo.">
												<ItemTemplate>
													<asp:Label id="lblCount" runat="server">
														<%#Count()%>
													</asp:Label>
												</ItemTemplate>
											</asp:TemplateColumn>
											<asp:BoundColumn Visible="False" DataField="Id"></asp:BoundColumn>
											<asp:BoundColumn DataField="Title" SortExpression="Title" HeaderText="Function Title"></asp:BoundColumn>
											<asp:BoundColumn DataField="Designation" SortExpression="Designation" HeaderText="Functional Designation"></asp:BoundColumn>
											<asp:ButtonColumn Text="View" CommandName="Select"></asp:ButtonColumn>
											<asp:TemplateColumn SortExpression="Title">
												<ItemTemplate>
													<asp:LinkButton id="lnkDelete" onclick="DelFunctionalDesignation" runat="server" CausesValidation="False">Delete</asp:LinkButton>
												</ItemTemplate>
											</asp:TemplateColumn>
										</Columns>
										<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
									</asp:datagrid></TD>
							</TR>
							<TR>
								<TD><asp:label id="lblMesg" runat="server" Font-Bold="True" Visible="False" ForeColor="Red"></asp:label></TD>
							</TR>
							<TR>
								<TD><asp:label id="Label4" runat="server" Visible="False">No Records Found</asp:label></TD>
							</TR>
						</TABLE>
						<BR>
						<TABLE class="MainFormColor" id="myCurrentDes" cellSpacing="0" cellPadding="3" width="750"
							align="center" border="0" runat="server">
							<TR>
								<TD class="OrangeFormTitle" style="HEIGHT: 23px">Department wise Designations</TD>
							</TR>
							<TR>
								<TD class="MenuBar"><STRONG>Select Department&nbsp;</STRONG></TD>
							</TR>
							<TR>
								<TD style="HEIGHT: 13px"><asp:dropdownlist id="ddMdept" runat="server" CssClass="textbox" Width="350px" AutoPostBack="True">
										<asp:ListItem Value="0">Select--Department</asp:ListItem>
									</asp:dropdownlist></TD>
							</TR>
							<TR>
								<TD><asp:datagrid id="DataGrid1" runat="server" Width="100%" AutoGenerateColumns="False" CellPadding="3"
										BackColor="White" BorderWidth="1px" BorderColor="Navy" BorderStyle="Solid" GridLines="Horizontal"
										AllowSorting="True" PageSize="2" DataKeyField="Id">
										<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
										<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
										<AlternatingItemStyle CssClass="AlternetItem"></AlternatingItemStyle>
										<ItemStyle CssClass="Item"></ItemStyle>
										<HeaderStyle Font-Bold="True" ForeColor="#F7F7F7" CssClass="header"></HeaderStyle>
										<Columns>
											<asp:EditCommandColumn ButtonType="LinkButton" UpdateText="Update" CancelText="Cancel" EditText="View"></asp:EditCommandColumn>
											<asp:TemplateColumn>
												<ItemTemplate>
													<asp:LinkButton id="lnkDelRec" onclick="DelRec" runat="server">Delete</asp:LinkButton>
												</ItemTemplate>
											</asp:TemplateColumn>
											<asp:BoundColumn Visible="False" DataField="Id"></asp:BoundColumn>
											<asp:BoundColumn DataField="designation" SortExpression="designation" HeaderText="Designation"></asp:BoundColumn>
											<asp:BoundColumn DataField="Department" SortExpression="Department" HeaderText="Department"></asp:BoundColumn>
											<asp:BoundColumn DataField="Category" SortExpression="Category" HeaderText="Category"></asp:BoundColumn>
											<asp:BoundColumn Visible="False" DataField="deptid"></asp:BoundColumn>
										</Columns>
										<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
									</asp:datagrid>&nbsp;
									<asp:label id="lblError" runat="server" Visible="False" ForeColor="Red"></asp:label></TD>
							</TR>
							<TR>
								<TD><asp:label id="Label6" runat="server" Visible="False">No Records Found</asp:label></TD>
							</TR>
						</TABLE>
						<BR>
						<TABLE class="MainFormColor" id="FunctionalDesignation" cellSpacing="0" cellPadding="3"
							width="750" align="center" border="0" runat="server">
							<TR>
								<TD class="OrangeFormTitle" colSpan="3">Functional Designation:
									<asp:label id="lblTitle" runat="server" Font-Bold="True">Label</asp:label></TD>
							</TR>
							<TR>
								<TD class="MenuBar" width="320"><STRONG>Title</STRONG><asp:requiredfieldvalidator id="RequiredFieldValidator1" runat="server" ControlToValidate="txtTitle" ErrorMessage="Please enter Designation title">*</asp:requiredfieldvalidator></TD>
								<TD class="MenuBar" width="90"></TD>
								<TD class="MenuBar" width="320"></TD>
							</TR>
							<TR>
								<TD vAlign="top" align="left" width="320"><asp:textbox id="txtTitle" runat="server" CssClass="TextBox" Width="100%" autocomplete="off"
										MaxLength="50"></asp:textbox></TD>
								<TD vAlign="top" align="left" width="60"></TD>
								<TD vAlign="top" align="left" width="320"></TD>
							</TR>
							<TR>
								<TD class="MenuBar" vAlign="top" align="left" width="320" colSpan="3"><STRONG>Job 
										Description:</STRONG></TD>
							</TR>
							<TR>
								<TD vAlign="top" align="left" colSpan="3"><asp:textbox id="txtJobDescription" runat="server" CssClass="TextBox" Width="100%" MaxLength="50"
										Rows="3" TextMode="MultiLine"></asp:textbox></TD>
							</TR>
							<TR>
								<TD class="MenuBar" vAlign="top" align="left" width="320"><STRONG>Salary Min Range:</STRONG>
									<asp:requiredfieldvalidator id="RequiredFieldValidator2" runat="server" ControlToValidate="txtSalaryRangeMin"
										ErrorMessage="*">*</asp:requiredfieldvalidator><asp:regularexpressionvalidator id="RegularExpressionValidator1" runat="server" ControlToValidate="txtSalaryRangeMin"
										ErrorMessage="Only numeric values" ValidationExpression="^[0-9].*$"></asp:regularexpressionvalidator></TD>
								<TD class="MenuBar" vAlign="top" align="left" width="60"></TD>
								<TD class="MenuBar" vAlign="top" align="left" width="320"><STRONG>Salary Max Range:</STRONG>
									<asp:requiredfieldvalidator id="RequiredFieldValidator3" runat="server" ControlToValidate="txtSalaryRangeMax"
										ErrorMessage="*">*</asp:requiredfieldvalidator><asp:regularexpressionvalidator id="RegularExpressionValidator2" runat="server" ControlToValidate="txtSalaryRangeMax"
										ErrorMessage="Only numeric values" ValidationExpression="^[0-9].*$"></asp:regularexpressionvalidator></TD>
							</TR>
							<TR>
								<TD vAlign="top" align="left" width="320"><asp:textbox id="txtSalaryRangeMin" runat="server" CssClass="TextBox" Width="150px"></asp:textbox></TD>
								<TD vAlign="top" align="left" width="60"></TD>
								<TD vAlign="top" align="left" width="320"><asp:textbox id="txtSalaryRangeMax" runat="server" CssClass="TextBox" Width="150px"></asp:textbox></TD>
							</TR>
							<TR>
								<TD vAlign="top" align="left" width="320"><asp:rangevalidator id="RangeValidator1" runat="server" ControlToValidate="txtSalaryRangeMin" ErrorMessage="Min.Salary greater than 0"
										Type="Double" MinimumValue="1" MaximumValue="99999999"></asp:rangevalidator></TD>
								<TD vAlign="top" align="left" width="60"></TD>
								<TD vAlign="top" align="left" width="320"><asp:comparevalidator id="CompareValidator1" runat="server" ControlToValidate="txtSalaryRangeMax" ErrorMessage="Max.Salary must be greater than Min.Salary"
										Type="Double" Operator="GreaterThanEqual" ControlToCompare="txtSalaryRangeMin"></asp:comparevalidator><BR>
									<asp:rangevalidator id="RangeValidator2" runat="server" ControlToValidate="txtSalaryRangeMax" ErrorMessage="Max.Salary must be in number"
										MinimumValue="0" MaximumValue="99999"></asp:rangevalidator></TD>
							</TR>
							<TR>
								<TD class="MenuBar" vAlign="top" align="left" width="320"><STRONG>No. Of Vacancies:</STRONG>
									<asp:requiredfieldvalidator id="RequiredFieldValidator4" runat="server" ControlToValidate="txtNoOfVacancy" ErrorMessage="Please enter No. of Vacancies">*</asp:requiredfieldvalidator></TD>
								<TD class="MenuBar" vAlign="top" align="left" width="60"></TD>
								<TD class="MenuBar" vAlign="top" align="left" width="320"><STRONG>Station:</STRONG></TD>
							</TR>
							<TR>
								<TD vAlign="top" align="left" width="320"><asp:textbox id="txtNoOfVacancy" runat="server" CssClass="TextBox" Width="100%"></asp:textbox></TD>
								<TD vAlign="top" align="left" width="60"></TD>
								<TD vAlign="top" align="left" width="320"><asp:dropdownlist id="ddStation" runat="server" CssClass="TextBox" Width="100%">
										<asp:ListItem Value="0">Select--Station</asp:ListItem>
									</asp:dropdownlist></TD>
							</TR>
							<TR>
								<TD vAlign="top" align="left" width="320"><asp:label id="Label8" runat="server" Visible="False" ForeColor="Red">Please enter vacancy more than Zero</asp:label><asp:rangevalidator id="RangeValidator3" runat="server" ControlToValidate="txtNoOfVacancy" ErrorMessage="Vacancies must be grater than zero"
										Type="Integer" MinimumValue="1" MaximumValue="1000" Display="Dynamic"></asp:rangevalidator></TD>
								<TD vAlign="top" align="left" width="60"></TD>
								<TD vAlign="top" align="left" width="320"><asp:customvalidator id="CustomValidator1" runat="server" ControlToValidate="ddStation" ErrorMessage="Please Select Proper Station"></asp:customvalidator></TD>
							</TR>
							<TR>
								<TD vAlign="top" align="left" colSpan="3"><asp:button id="btnSave" runat="server" Width="120px" Text="Save"></asp:button><asp:button id="Button1" runat="server" Width="120px" Text="Cancel" CausesValidation="False"></asp:button><asp:button id="btnAssign" runat="server" Width="120px" Text="Assign Employees" Enabled="False"></asp:button><asp:textbox id="txttest" style="VISIBILITY: hidden" runat="server"></asp:textbox></TD>
							</TR>
							<TR>
								<TD vAlign="top" align="left" colSpan="3"><asp:label id="lblTitleMsg" runat="server" Visible="False" ForeColor="Red"></asp:label></TD>
							</TR>
							<TR>
								<TD vAlign="top" align="left" colSpan="3"><asp:label id="lblVacancy" runat="server" Visible="False" ForeColor="Red">Please Remove employee to the corresponding designation then change no.of vacancies Or The Given Vacancies Exceeds The Limit</asp:label></TD>
							</TR>
							<TR>
								<TD vAlign="top" align="left" colSpan="3"><asp:datagrid id="DataGrid3" runat="server" Width="100%" AutoGenerateColumns="False" CellPadding="3"
										BackColor="White" BorderWidth="1px" BorderColor="Navy" BorderStyle="Solid" GridLines="Horizontal">
										<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
										<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
										<AlternatingItemStyle CssClass="AlternetItem"></AlternatingItemStyle>
										<ItemStyle CssClass="Item"></ItemStyle>
										<HeaderStyle Font-Bold="True" ForeColor="#F7F7F7" CssClass="header"></HeaderStyle>
										<Columns>
											<asp:BoundColumn DataField="Photo"></asp:BoundColumn>
											<asp:BoundColumn DataField="PCode" HeaderText="Employee Code"></asp:BoundColumn>
											<asp:BoundColumn DataField="Name" HeaderText="Name"></asp:BoundColumn>
											<asp:TemplateColumn>
												<ItemTemplate>
													<asp:LinkButton id="lnkDel" onclick="Del" runat="server">Delete</asp:LinkButton>
												</ItemTemplate>
											</asp:TemplateColumn>
										</Columns>
										<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
									</asp:datagrid></TD>
							</TR>
							<TR>
								<TD vAlign="top" align="left" width="320" colSpan="3"><asp:label id="Label9" runat="server" Visible="False">No Records Found</asp:label></TD>
							</TR>
							<TR>
								<TD vAlign="top" align="left" colSpan="3"><asp:panel id="Panel2" runat="server" Width="100%" Height="116px">
										<TABLE class="MainFormColor" id="myRowAssign" cellSpacing="0" cellPadding="3" width="100%"
											border="0" runat="server">
											<TR>
												<TD class="OrangeFormTitle" vAlign="top" align="left" width="320" colSpan="3">Search 
													Employees</TD>
											</TR>
											<TR>
												<TD class="MenuBar" vAlign="top" align="left" width="320"><STRONG>User(Code):</STRONG></TD>
												<TD class="MenuBar" width="60"></TD>
												<TD class="MenuBar" vAlign="top" align="left" width="320"><STRONG>Location:</STRONG></TD>
											</TR>
											<TR>
												<TD style="HEIGHT: 16px" vAlign="top" align="left" width="320">
													<asp:DropDownList id="ddEmployeeName" runat="server" Width="100%" CssClass="textbox">
														<asp:ListItem Value="0">Select: Employee</asp:ListItem>
													</asp:DropDownList></TD>
												<TD style="HEIGHT: 16px" width="80"></TD>
												<TD style="HEIGHT: 16px" vAlign="top" align="left" width="320">
													<asp:DropDownList id="ddLocation" runat="server" Width="100%" CssClass="textbox">
														<asp:ListItem Value="0">Select Location</asp:ListItem>
													</asp:DropDownList></TD>
											</TR>
											<TR>
												<TD class="MenuBar" vAlign="top" align="left" width="320"><STRONG>Name:</STRONG></TD>
												<TD class="MenuBar" width="60"></TD>
												<TD class="MenuBar" vAlign="top" align="left" width="320"><STRONG>NIC:</STRONG></TD>
											</TR>
											<TR>
												<TD vAlign="top" align="left" width="320">
													<asp:TextBox id="txtAdName" runat="server" Width="100%" CssClass="textbox"></asp:TextBox></TD>
												<TD width="60"></TD>
												<TD vAlign="top" align="left" width="320">
													<asp:TextBox id="txtNIC" runat="server" Width="100%" CssClass="textbox"></asp:TextBox></TD>
											</TR>
											<TR>
												<TD vAlign="top" align="left" colSpan="3">
													<asp:Button id="btnsearchAdvance" runat="server" Width="120px" Text="Search"></asp:Button>
													<asp:Button id="btnSclose" runat="server" Width="120px" Text="Close"></asp:Button>
													<asp:TextBox id="txtDate" style="VISIBILITY: hidden" runat="server"></asp:TextBox></TD>
											</TR>
											<TR>
												<TD vAlign="top" align="left" colSpan="3">
													<asp:DataGrid id="DataGrid4" runat="server" Width="100%" Visible="False" AllowSorting="True" GridLines="Horizontal"
														BorderStyle="Solid" BorderColor="Navy" BorderWidth="1px" BackColor="White" CellPadding="3"
														AutoGenerateColumns="False">
														<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
														<AlternatingItemStyle CssClass="AlternetItem"></AlternatingItemStyle>
														<ItemStyle ForeColor="#4A3C8C" CssClass="Item"></ItemStyle>
														<HeaderStyle Font-Bold="True" ForeColor="#F7F7F7" CssClass="header"></HeaderStyle>
														<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
														<Columns>
															<asp:TemplateColumn>
																<ItemTemplate>
																	<asp:LinkButton id="btnSelect" onclick="Selected" runat="server" CausesValidation="False">Select</asp:LinkButton>
																</ItemTemplate>
															</asp:TemplateColumn>
															<asp:BoundColumn DataField="Photo">
																<ItemStyle Width="120px"></ItemStyle>
															</asp:BoundColumn>
															<asp:BoundColumn DataField="PCode" SortExpression="PCode" HeaderText="Employee Code">
																<ItemStyle Width="80px"></ItemStyle>
															</asp:BoundColumn>
															<asp:BoundColumn DataField="Name" SortExpression="Name" HeaderText="Name"></asp:BoundColumn>
															<asp:BoundColumn DataField="Email" SortExpression="Email" HeaderText="E-Mail "></asp:BoundColumn>
															<asp:BoundColumn DataField="Extension" SortExpression="Extension" HeaderText="Extension"></asp:BoundColumn>
														</Columns>
														<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
													</asp:DataGrid></TD>
											</TR>
											<TR>
												<TD vAlign="top" align="left" colSpan="3">
													<asp:Label id="Label10" runat="server" ForeColor="Red" Visible="False"></asp:Label></TD>
											</TR>
											<TR>
												<TD vAlign="top" align="left" width="320" colSpan="3">
													<asp:Button id="btnRemove" runat="server" Width="120px" Visible="False" Text="Replace"></asp:Button>
													<asp:TextBox id="txtPrev" style="VISIBILITY: hidden" runat="server"></asp:TextBox></TD>
											</TR>
										</TABLE>
									</asp:panel></TD>
							</TR>
						</TABLE>
						<BR>
						<TABLE id="Table4" cellSpacing="0" cellPadding="2" width="100%" align="center" bgColor="white"
							border="0">
							<TR>
								<TD id="currentDesignation" vAlign="top" runat="server"><BR>
								</TD>
							</TR>
						</TABLE>
					</td>
				</tr>
				<tr>
					<td vAlign="middle" align="center" height="20">Copyright © 2005 Independent Media 
						Corporation <A href="http://www.geo.tv">www.geo.tv</A></td>
				</tr>
			</table>
			<asp:textbox id="txtX" style="VISIBILITY: hidden" runat="server"></asp:textbox><asp:textbox id="txtY" style="VISIBILITY: hidden" runat="server"></asp:textbox></form>
	</body>
</HTML>
