<%@ Page language="c#" Codebehind="IDCardsRequests.aspx.cs" AutoEventWireup="false" Inherits="GeoRabtaSite.admin.IDCardsRequestsAdmin" %>
<%@ Register TagPrefix="uc1" TagName="myMenus" Src="../myMenus.ascx" %>
<%@ Register TagPrefix="uc1" TagName="AdminUserControl" Src="../../AdminUserControl.ascx" %>
<%@ Register TagPrefix="uc1" TagName="LoginUser" Src="../../LoginUser.ascx" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Rabta ::</title>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="../RaabtaAdmin.css" type="text/css" rel="stylesheet">
		<LINK href="../Styles4.css" type="text/css" rel="stylesheet">
		<LINK href="../css/start/jquery-ui-1.8.4.custom.css" type="text/css" rel="stylesheet">
		<script src="../js/jquery-1.4.2.min.js" type="text/javascript"></script>
		<script src="../js/jquery-ui-1.8.2.custom.min.js" type="text/javascript"></script>
		<style type="text/css">.style1 { FONT-SIZE: 8px; FONT-WEIGHT: bold }
	.hid { DISPLAY: none }
	TH { FONT-SIZE: 12px }
		</style>
		<style title="currentStyle" type="text/css">@import url( ../../media2/css/demos.css ); 
		</style>
		</STYLE> 
		<!--<script language="javascript" src="../media/js/jquery.js" type="text/javascript"></script>-->
		<script language="javascript" src="../../media2/js/jquery.dataTables.js" type="text/javascript"></script>
		<script type="text/javascript" charset="utf-8">
			$(document).ready(function() {
			   	//$('#example').dataTable();
				$('#example').dataTable({
                "bJQueryUI": true,
                "sPaginationType": "full_numbers"
                 });
                 $('#example2').dataTable({
                "bJQueryUI": true,
                "sPaginationType": "full_numbers"
                 });
                 $('#example3').dataTable({
                "bJQueryUI": true,
                "sPaginationType": "full_numbers"
                 });
                 $('#example4').dataTable({
                "bJQueryUI": true,
                "sPaginationType": "full_numbers"
                 });
				//$('#example2').dataTable();
				//$('#example3').dataTable();
				//$('#example4').dataTable();
				$('#tabs').tabs();
				$('#tabs2').tabs();
				$('#accordion').accordion();
			} );
		</script>
		<script language="javascript">
		function reFresh()
		{
		 __doPostBack('lnkRefresh','');
		} 
		</script>
	</HEAD>
	<body dir="ltr" bottomMargin="0" leftMargin="0" topMargin="0" rightMargin="0">
		<form id="myForm" name="MyForm" runat="server">
			<table height="100%" cellSpacing="0" cellPadding="0" width="750" align="center" bgColor="#ffffff"
				border="0">
				<tr>
					<td vAlign="middle" align="left"></td>
				</tr>
				<tr>
					<td height="10"></td>
				</tr>
				<TR>
					<TD class="PageTitle" height="20">Geo Raabta Admin :: I.D Cards Requests</TD>
				</TR>
				<tr>
					<td background="../images/menu-off-bg.gif" height="20"></td>
				</tr>
				<tr>
					<td class="MainBG" vAlign="top" align="left">
						<P><asp:imagebutton id="imgBack" runat="server" ImageUrl="..\..\images\back.gif"></asp:imagebutton><BR>
							<BR>
							<TABLE id="Table1" cellSpacing="1" cellPadding="1" width="100%" border="0">
								<TR>
									<TD class="OrangeFormTitle">I.D Cards&nbsp;Request List&nbsp;
										<asp:linkbutton id="lnkRefresh" runat="server">Refresh</asp:linkbutton></TD>
								</TR>
								<TR>
									<TD>
										<DIV id="accordion">
											<H3><A href="#">Request&nbsp;Q-list</A></H3>
											<DIV>
												<DIV id="tabs">
													<UL>
														<LI>
															<A href="#tab-1">Current Requests</A>
														<LI>
															<A href="#tab-2">Delivered</A>
														</LI>
													</UL>
													<DIV id="tab-1"><asp:label id="lblHTML" runat="server"></asp:label></DIV>
													<DIV id="tab-2"><asp:label id="lblHTMLClosed" runat="server"></asp:label><BR>
													</DIV>
												</DIV>
											</DIV>
										</DIV>
									</TD>
								</TR>
								<TR>
									<TD></TD>
								</TR>
							</TABLE>
						</P>
					</td>
				</tr>
				<tr>
					<td vAlign="middle" align="center" height="20">Copyright ©
						<% =DateTime.Now.Year%>
						Independent Media Corporation <A href="http://www.geo.tv">www.geo.tv</A></td>
				</tr>
			</table>
		</form>
	</body>
</HTML>
