using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;
namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for Policy.
	/// </summary>
	/// 
	enum JFlag
	{
		Inprocess=2,
		Canceled=4,
		Accepted=1,
		Rejected=0
	};
	enum KFlag
	{
		Inprocess=1,
		Canceled=2,
		Accepted=3,
		Rejected=4
	}
	public class MyRequest : System.Web.UI.Page
	{
	    SqlConnection con;
		protected System.Web.UI.WebControls.Panel pnlMyRequest;
		protected System.Web.UI.WebControls.DataGrid dgMyRequest;
		protected System.Web.UI.WebControls.Label lblMessage;
		protected System.Web.UI.WebControls.Label lblmsgTitle;
		protected System.Web.UI.WebControls.TextBox txtX;
		protected System.Web.UI.WebControls.TextBox txtY;
		protected System.Web.UI.WebControls.Label lblPCode;
		protected System.Web.UI.WebControls.Label lblEmpInfo;
		protected System.Web.UI.WebControls.Panel Panel1;
		protected System.Web.UI.WebControls.HyperLink hlMyRequests;
		protected System.Web.UI.WebControls.ImageButton imgMyGrievance;
		protected System.Web.UI.WebControls.ImageButton ImgMyAttendance;
		protected System.Web.UI.WebControls.ImageButton imgMyLeave;
		protected System.Web.UI.WebControls.ImageButton ibMySelf;
		protected System.Web.UI.WebControls.ImageButton ibSalary;
		protected System.Web.UI.WebControls.ImageButton ImageButton5;
		protected System.Web.UI.WebControls.HyperLink hlMyExp;
		protected System.Web.UI.WebControls.ImageButton imgTraining;
		protected System.Web.UI.WebControls.ImageButton ImageButton4;
		protected System.Web.UI.WebControls.ImageButton ImageButton3;
		protected System.Web.UI.WebControls.ImageButton ImageButton2;
		protected System.Web.UI.WebControls.ImageButton ImageButton1;
		public static string userId="";
		/*private bool IsPageAccessAllowed()
		{
			
			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception )
			{
				Response.Redirect("Login.aspx");
			}

			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"View Policies")==true)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("Login.aspx");
				return false;
			}
		}*/



		private void Page_Load(object sender, System.EventArgs e)
		{   
			
			
			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			/*if(IsPageAccessAllowed())
			{
				
			  
			}
			else
			{
				Response.Redirect("ErrorPage.aspx");
			}*/
			if(!IsPostBack)
			{
				GetMyRequest();
			}
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.dgMyRequest.SelectedIndexChanged += new System.EventHandler(this.dgMyRequest_SelectedIndexChanged);
			this.imgMyGrievance.Click += new System.Web.UI.ImageClickEventHandler(this.imgMyGrievance_Click);
			this.imgTraining.Click += new System.Web.UI.ImageClickEventHandler(this.imgTraining_Click);
			this.ImageButton4.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton4_Click);
			this.ImageButton3.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton3_Click);
			this.ImageButton2.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton2_Click);
			this.ImageButton1.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton1_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion


		private void FillInProcessRequest(SqlConnection conn, DataSet ds, string field, string table, string pcode,string NewMsg, string ChangeMsg, string RemoevMsg )
		{
			string sql=" SELECT TOP (100) PERCENT '"+table+"' AS tab, reqid AS rid, " +
				" CASE req.Addflag WHEN 1 THEN '"+NewMsg+"' WHEN 2 THEN '"+ChangeMsg+"' WHEN 3 THEN '"+RemoevMsg+"' END AS requestforchange, " +
				" '"+field+"' AS fieldname, c_at AS requestdate, DATEADD(d, requesttimeline, c_at) AS requesttimeline, " +
				" 'Inprocess'  AS requeststatus, '"+field+"' as section " +
				" FROM "+table+" AS req " +
				" WHERE (PCode = '"+pcode+"') AND (AppFlag = 2) " +
				" ORDER BY requestdate ";
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			da.Fill(ds,"Record");
			ds.AcceptChanges();
		}

		private void FillCanceledRequest(SqlConnection conn, DataSet ds, string field, string table, string pcode,string NewMsg, string ChangeMsg, string RemoevMsg )
		{
			string sql=" SELECT TOP (100) PERCENT '"+table+"' AS tab, reqid AS rid, " +
				" CASE req.Addflag WHEN 1 THEN '"+NewMsg+"' WHEN 2 THEN '"+ChangeMsg+"' WHEN 3 THEN '"+RemoevMsg+"' END AS requestforchange, " +
				" '"+field+"' AS fieldname, c_at AS requestdate, DATEADD(d, requesttimeline, c_at) AS requesttimeline, " +
				" 'Cancelled' AS requeststatus, '"+field+"' as section " +
				" FROM "+table+" AS req " +
				" WHERE (PCode = '"+pcode+"') AND (AppFlag = 4) AND (DATEDIFF(d, c_at, GETDATE()) <= 7) " +
				" ORDER BY requestdate ";
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			da.Fill(ds,"Record");
			ds.AcceptChanges();
		}

		private void FillAcceptedRequest(SqlConnection conn, DataSet ds, string field, string table, string pcode,string NewMsg, string ChangeMsg, string RemoevMsg )
		{
			string sql=" SELECT TOP (100) PERCENT '"+table+"' AS tab, reqid AS rid, " +
				" CASE req.Addflag WHEN 1 THEN '"+NewMsg+"' WHEN 2 THEN '"+ChangeMsg+"' WHEN 3 THEN '"+RemoevMsg+"' END AS requestforchange, " +
				" '"+field+"' AS fieldname, c_at AS requestdate, DATEADD(d, requesttimeline, c_at) AS requesttimeline, " +
				" 'Accepted' AS requeststatus, '"+field+"' as section " +
				" FROM dbo.cv_TrainingReq AS req " +
				" WHERE (PCode = '"+pcode+"') AND (AppFlag = 1) AND (DATEDIFF(d, c_at, GETDATE()) <= 7) " +
				" ORDER BY requestdate ";
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			da.Fill(ds,"Record");
			ds.AcceptChanges();
		}

		private void FillRejectedRequest(SqlConnection conn, DataSet ds, string field, string table, string pcode,string NewMsg, string ChangeMsg, string RemoevMsg )
		{
			string sql=" SELECT TOP (100) PERCENT '"+table+"' AS tab, reqid AS rid, " +
				" CASE req.Addflag WHEN 1 THEN '"+NewMsg+"' WHEN 2 THEN '"+ChangeMsg+"' WHEN 3 THEN '"+RemoevMsg+"' END AS requestforchange, " +
				" '"+field+"' AS fieldname, c_at AS requestdate, DATEADD(d, requesttimeline, c_at) AS requesttimeline, " +
				" 'Rejected' AS requeststatus, '"+field+"' as section " +
				" FROM dbo.cv_TrainingReq AS req " +
				" WHERE (PCode = '"+pcode+"') AND (AppFlag = 0) AND (DATEDIFF(d, c_at, GETDATE()) <= 7) " +
				" ORDER BY requestdate ";
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			da.Fill(ds,"Record");
			ds.AcceptChanges();
		}

//		private void FillMyRequests(SqlConnection conn, DataSet ds, string table, string pcode,string NewMsg, string ChangeMsg, string RemoevMsg )
//		{
//			string sql=" SELECT TOP (100) PERCENT '"+table+"' AS tab, reqid AS rid, " +
//				" CASE req.Addflag WHEN 1 THEN '"+NewMsg+"' WHEN 2 THEN '"+ChangeMsg+"' WHEN 3 THEN '"+RemoevMsg+"' END AS requestforchange, " +
//				" 'Training' AS fieldname, c_at AS requestdate, DATEADD(d, requesttimeline, c_at) AS requesttimeline, " +
//				" CASE AppFlag WHEN 0 THEN 'Rejected' WHEN 1 THEN 'Accepted' WHEN 2 THEN 'Inprocess' WHEN 4 THEN 'Cancelled' END AS requeststatus " +
//				" FROM "+table+" AS req " +
//				" WHERE (PCode = '"+pcode+"') AND (AppFlag = 2) " +
//				" ORDER BY requestdate ";
//			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
//			da.Fill(ds,"Record");
//			ds.AcceptChanges();
//
//			sql=" SELECT TOP (100) PERCENT '"+table+"' AS tab, reqid AS rid, " +
//				" CASE req.Addflag WHEN 1 THEN '"+NewMsg+"' WHEN 2 THEN '"+ChangeMsg+"' WHEN 3 THEN '"+RemoevMsg+"' END AS requestforchange, " +
//				" 'Training' AS fieldname, c_at AS requestdate, DATEADD(d, requesttimeline, c_at) AS requesttimeline, " +
//				" CASE AppFlag WHEN 0 THEN 'Rejected' WHEN 1 THEN 'Accepted' WHEN 2 THEN 'Inprocess' WHEN 4 THEN 'Cancelled' END AS requeststatus " +
//				" FROM "+table+" AS req " +
//				" WHERE (PCode = '"+pcode+"') AND (AppFlag = 4) AND (DATEDIFF(d, c_at, GETDATE()) <= 7) " +
//				" ORDER BY requestdate ";
//			da=new SqlDataAdapter(sql,conn);
//			da.Fill(ds,"Record");
//			ds.AcceptChanges();
//
//
//			sql=" SELECT TOP (100) PERCENT '"+table+"' AS tab, reqid AS rid, " +
//				" CASE req.Addflag WHEN 1 THEN '"+NewMsg+"' WHEN 2 THEN '"+ChangeMsg+"' WHEN 3 THEN '"+RemoevMsg+"' END AS requestforchange, " +
//				" 'Training' AS fieldname, c_at AS requestdate, DATEADD(d, requesttimeline, c_at) AS requesttimeline, " +
//				" CASE AppFlag WHEN 0 THEN 'Rejected' WHEN 1 THEN 'Accepted' WHEN 2 THEN 'Inprocess' WHEN 4 THEN 'Cancelled' END AS requeststatus " +
//				" FROM dbo.cv_TrainingReq AS req " +
//				" WHERE (PCode = '"+pcode+"') AND (AppFlag = 1) AND (DATEDIFF(d, c_at, GETDATE()) <= 7) " +
//				" ORDER BY requestdate ";
//			da=new SqlDataAdapter(sql,conn);
//			da.Fill(ds,"Record");
//			ds.AcceptChanges();
//
//			sql=" SELECT TOP (100) PERCENT '"+table+"' AS tab, reqid AS rid, " +
//				" CASE req.Addflag WHEN 1 THEN '"+NewMsg+"' WHEN 2 THEN '"+ChangeMsg+"' WHEN 3 THEN '"+RemoevMsg+"' END AS requestforchange, " +
//				" 'Training' AS fieldname, c_at AS requestdate, DATEADD(d, requesttimeline, c_at) AS requesttimeline, " +
//				" CASE AppFlag WHEN 0 THEN 'Rejected' WHEN 1 THEN 'Accepted' WHEN 2 THEN 'Inprocess' WHEN 4 THEN 'Cancelled' END AS requeststatus " +
//				" FROM "+table+" AS req " +
//				" WHERE (PCode = '"+pcode+"') AND (AppFlag = 0) AND (DATEDIFF(d, c_at, GETDATE()) <= 7) " +
//				" ORDER BY requestdate ";
//			da=new SqlDataAdapter(sql,conn);
//			da.Fill(ds,"Record");
//			ds.AcceptChanges();
//		}
		public void GetMyRequest()
		{
			try
			{
				con=new SqlConnection(Connection.ConnectionString);
				con.Open();
				//SqlDataAdapter dr=new SqlDataAdapter("select e.rid,e.fieldname,e.requestforchange,e.requestdate,e.requesttimeline,requeststatus=case e.requeststatus when 1 then 'Inprocess' when 2 then 'Cancelled' when 3 then 'Accepted' when 4 then 'Rejected' end, 'Employee' as section from t_employeerequests e where e.requisitecode='"+Session["user_id"].ToString()+"' and (e.requeststatus=1) order by e.requestdate asc",con);
				SqlDataAdapter dr=new SqlDataAdapter(" SELECT TOP (100) PERCENT rID, fieldname, requestforchange, requestdate, requesttimeline, " +
					" CASE requeststatus WHEN 1 THEN 'Inprocess' WHEN 2 THEN 'Cancelled' WHEN 3 THEN 'Accepted' WHEN 4 THEN 'Rejected' END AS requeststatus, " +
					" 'Employee' AS section " +
					" FROM dbo.t_employeerequests " +
					" WHERE (requisitecode = '"+Session["user_id"].ToString()+"') AND (requeststatus = 1) " +
					" ORDER BY requestdate ",con);

				DataSet ds=new DataSet();
				dr.Fill(ds,"Record");
				ds.AcceptChanges();
				FillInProcessRequest(con, ds,"Training", "cv_TrainingReq",Session["user_id"].ToString(),"New Request","Change Request", "Removal Request");
				FillInProcessRequest(con, ds,"Education", "t_educationInfoRequest",Session["user_id"].ToString(),"New Request","Change Request", "Removal Request");
				FillInProcessRequest(con, ds,"Experince", "cv_ExperinceReq",Session["user_id"].ToString(),"New Request","Change Request", "Removal Request");
				FillInProcessRequest(con, ds,"Family", "t_FamilyDetailsRequest",Session["user_id"].ToString(),"New Request","Change Request", "Removal Request");

				
				//dr=new SqlDataAdapter("select e.rid,e.fieldname,e.requestforchange,e.requestdate,e.requesttimeline,requeststatus=case e.requeststatus when 1 then 'Inprocess' when 2 then 'Cancelled' when 3 then 'Accepted' when 4 then 'Rejected' end  'Employee' as section from t_employeerequests e where e.requisitecode='"+Session["user_id"].ToString()+"' and (e.requeststatus=2) and datediff(d,e.rhandledate,getdate()) <=7 order by e.requestdate asc",con);
				dr=new SqlDataAdapter(" SELECT TOP (100) PERCENT rID, fieldname, requestforchange, requestdate, requesttimeline, " +
					" CASE requeststatus WHEN 1 THEN 'Inprocess' WHEN 2 THEN 'Cancelled' WHEN 3 THEN 'Accepted' WHEN 4 THEN 'Rejected' END AS requeststatus, " +
					" 'Employee' AS section " +
					" FROM dbo.t_employeerequests " +
					" WHERE (requisitecode = '"+Session["user_id"].ToString()+"') AND (requeststatus = 2) AND (DATEDIFF(d, rhandledate, GETDATE()) <= 7) " +
					" ORDER BY requestdate ",con);

				dr.Fill(ds,"Record");
				ds.AcceptChanges();
				FillCanceledRequest(con, ds,"Training", "cv_TrainingReq",Session["user_id"].ToString(),"New Request","Change Request", "Removal Request");
				FillCanceledRequest(con, ds,"Education", "t_educationInfoRequest",Session["user_id"].ToString(),"New Request","Change Request", "Removal Request");
				FillCanceledRequest(con, ds,"Experince", "cv_ExperinceReq",Session["user_id"].ToString(),"New Request","Change Request", "Removal Request");
				FillCanceledRequest(con, ds,"Family", "t_FamilyDetailsRequest",Session["user_id"].ToString(),"New Request","Change Request", "Removal Request");

				

				//dr=new SqlDataAdapter("select e.rid,e.fieldname,e.requestforchange,e.requestdate,e.requesttimeline,requeststatus=case e.requeststatus when 1 then 'Inprocess' when 2 then 'Cancelled' when 3 then 'Accepted' when 4 then 'Rejected' end  'Employee' as section from t_employeerequests e where e.requisitecode='"+Session["user_id"].ToString()+"' and (e.requeststatus=3) and datediff(d,e.rhandledate,getdate()) <=7 order by e.requestdate asc",con);
				dr=new SqlDataAdapter(" SELECT TOP (100) PERCENT rID, fieldname, requestforchange, requestdate, requesttimeline, " +
					" CASE requeststatus WHEN 1 THEN 'Inprocess' WHEN 2 THEN 'Cancelled' WHEN 3 THEN 'Accepted' WHEN 4 THEN 'Rejected' END AS requeststatus, " +
					" 'Employee' AS section " +
					" FROM dbo.t_employeerequests " +
					" WHERE (requisitecode = '"+Session["user_id"].ToString()+"') AND (requeststatus = 3) AND (DATEDIFF(d, rhandledate, GETDATE()) <= 7) " +
					" ORDER BY requestdate ",con);
				dr.Fill(ds,"Record");
				ds.AcceptChanges();
				FillAcceptedRequest(con, ds,"Training", "cv_TrainingReq",Session["user_id"].ToString(),"New Request","Change Request", "Removal Request");
				FillAcceptedRequest(con, ds,"Education", "t_educationInfoRequest",Session["user_id"].ToString(),"New Request","Change Request", "Removal Request");
				FillAcceptedRequest(con, ds,"Experince", "cv_ExperinceReq",Session["user_id"].ToString(),"New Request","Change Request", "Removal Request");
				FillAcceptedRequest(con, ds,"Family", "t_FamilyDetailsRequest",Session["user_id"].ToString(),"New Request","Change Request", "Removal Request");

				
				//dr=new SqlDataAdapter("select e.rid,e.fieldname,e.requestforchange,e.requestdate,e.requesttimeline,requeststatus=case e.requeststatus when 1 then 'Inprocess' when 2 then 'Cancelled' when 3 then 'Accepted' when 4 then 'Rejected' end  'Employee' as section from t_employeerequests e where e.requisitecode='"+Session["user_id"].ToString()+"' and (e.requeststatus=4) and datediff(d,e.rhandledate,getdate()) <=7 order by e.requestdate asc",con);
				dr=new SqlDataAdapter(" SELECT TOP (100) PERCENT rID, fieldname, requestforchange, requestdate, requesttimeline, " +
					" CASE e.requeststatus WHEN 1 THEN 'Inprocess' WHEN 2 THEN 'Cancelled' WHEN 3 THEN 'Accepted' WHEN 4 THEN 'Rejected' END AS requeststatus, " +
					" 'Employee' AS section " +
					" FROM dbo.t_employeerequests AS e " +
					" WHERE (requisitecode = '"+Session["user_id"].ToString()+"') AND (requeststatus = 4) AND (DATEDIFF(d, rhandledate, GETDATE()) <= 7) " +
					" ORDER BY requestdate ",con);

				dr.Fill(ds,"Record");
				ds.AcceptChanges();
				FillRejectedRequest(con, ds,"Training", "cv_TrainingReq",Session["user_id"].ToString(),"New Request","Change Request", "Removal Request");
				FillRejectedRequest(con, ds,"Education", "t_educationInfoRequest",Session["user_id"].ToString(),"New Request","Change Request", "Removal Request");
				FillRejectedRequest(con, ds,"Experince", "cv_ExperinceReq",Session["user_id"].ToString(),"New Request","Change Request", "Removal Request");
				FillRejectedRequest(con, ds,"Family", "t_FamilyDetailsRequest",Session["user_id"].ToString(),"New Request","Change Request", "Removal Request");

				
				//FillMyRequests(con, ds,"cv_TrainingReq",Session["user_id"].ToString(),"Request for New Training","Request for Change in Training", "Request for Remvoe Training");
				this.dgMyRequest.DataSource=ds;
				this.dgMyRequest.DataBind();
				this.dgMyRequest.Visible=true;
				ds.AcceptChanges();
				
				con.Close();
				con.Dispose();
				for(int i=0;i<this.dgMyRequest.Items.Count;i++)
				{
					LinkButton lnk=(LinkButton)this.dgMyRequest.Items[i].FindControl("lnkCancel");
					if(this.dgMyRequest.Items[i].Cells[7].Text=="Accepted" || this.dgMyRequest.Items[i].Cells[7].Text=="Rejected" || this.dgMyRequest.Items[i].Cells[7].Text=="Cancelled")
					{
						lnk.Visible=false;
					}
					else
					{
						lnk.Attributes.Add("onclick","return confirm('Are you sure to cancel this request?');");
					}
				}
			}
			catch(Exception ex)
			{
				Response.Write(ex.Message);
			}
		}  
		private void UpdateRequest(string section, string pcode, string reqid)
		{
			string sql="";
			string table="";
			if(section=="Training")
			{
				sql="update cv_TrainingReq set appflag=4,AppRejDate=getdate(), AppRejBy='"+Session["user_id"].ToString()+"' where reqid="+reqid+" and appflag=2 and pcode='"+Session["user_id"].ToString()+"'";
				table="cv_TrainingReq";
			}
			if(section=="Experince")
			{
				sql="update cv_ExperinceReq set appflag=4,AppRejDate=getdate(), AppRejBy='"+Session["user_id"].ToString()+"' where reqid="+reqid+" and appflag=2 and pcode='"+Session["user_id"].ToString()+"'";
				table="cv_ExperinceReq";
			}
			if(section=="Education")
			{
				sql="update t_educationInfoRequest set appflag=4,AppRejDate=getdate(), AppRejBy='"+Session["user_id"].ToString()+"' where reqid="+reqid+" and appflag=2 and pcode='"+Session["user_id"].ToString()+"'";
				table="t_educationInfoRequest";
			}
			if(section=="Family")
			{
				sql="update t_FamilyDetailsRequest set appflag=4,AppRejDate=getdate(), AppRejBy='"+Session["user_id"].ToString()+"' where reqid="+reqid+" and appflag=2 and pcode='"+Session["user_id"].ToString()+"'";
				table="t_FamilyDetailsRequest";
			}
			if(section=="Employee")
			{
				table="";
				sql="update t_employeerequests set requeststatus=2,rhandledate=getdate() where requeststatus=1 and requisitecode='"+Session["user_id"].ToString()+"' and rid="+reqid+"";
			}

			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			SqlTransaction tran=conn.BeginTransaction();

			SqlCommand cmd=new SqlCommand(sql,conn);
			cmd.Transaction=tran;

			cmd.ExecuteNonQuery();
			if(table!="")
			{
				//Bulletin.PostReqMessage(conn,tran,"Request Canceled By User","GEO Raabta",Session["user_id"].ToString(),2,section,table);
				PostReqMessage(conn,tran,reqid, "Request Canceled By User","GEO Raabta",Session["user_id"].ToString(),"2",section);
			}
			else
			{
				//Bulletin.PostMessage(conn,tran,"Request Canceled By User","GEO Raabta",Session["user_id"].ToString(),2);
				Bulletin.PostMessage(conn,tran,"Request Canceled by User","GEO Raabta",pcode,2,reqid);
			}

			tran.Commit();
			conn.Close();

		}
		public void PostReqMessage(SqlConnection conn, SqlTransaction tran, string rid, string message, string sendby, string sendto, string messagetype, string section)
		{
			
			string sql="INSERT INTO t_employeerequestmessages(rid, message, sendby, sendon, sendto, messagetype, section) " +
				" VALUES (@rid, @message, @sendby, getdate(), @sendto, @messagetype, @section) ";
			SqlCommand cmd=new SqlCommand(sql,conn);
			cmd.Transaction=tran;
			cmd.Parameters.Add(new SqlParameter("@rid",rid));
			cmd.Parameters.Add(new SqlParameter("@message",message));
			cmd.Parameters.Add(new SqlParameter("@sendby",sendby));
			cmd.Parameters.Add(new SqlParameter("@sendto",sendto));
			cmd.Parameters.Add(new SqlParameter("@messagetype",messagetype));
			cmd.Parameters.Add(new SqlParameter("@section",section));
			cmd.ExecuteNonQuery();

		}
		public void CancelReq(object sender, EventArgs e)
		{
			LinkButton rLink=(LinkButton)sender;
			for(int i=0;i<this.dgMyRequest.Items.Count;i++)
			{
				LinkButton lnk=(LinkButton)this.dgMyRequest.Items[i].FindControl("lnkCancel");
				if(lnk.Equals(rLink))
				{
					string reqid=dgMyRequest.Items[i].Cells[2].Text;
					string status=dgMyRequest.Items[i].Cells[7].Text;
					string section=dgMyRequest.Items[i].Cells[8].Text;
					if (status=="Inprocess")
					{
						UpdateRequest(section,Session["user_id"].ToString(),reqid);
						GetMyRequest();
					}
					break;

				}
			}
			
		}
		private void imgEmployee_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("UserProfile.aspx?id=1");
		}

		private void imgPersonal_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
		 Response.Redirect("UserProfile.aspx?id=2");
		}
		private void ImageButton3_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("EmpFamily.aspx?pcode="+this.lblPCode.Text+"&name="+this.lblEmpInfo.Text);
		}

		private void ImageButton4_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("EmpEducation.aspx?pcode="+this.lblPCode.Text+"&name="+this.lblEmpInfo.Text);
		}

		private void imgTraining_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("EmpTraining.aspx?pcode="+this.lblPCode.Text+"&name="+this.lblEmpInfo.Text);
		}

		private void ReadMessage(string section, string rid,string mssagetype)
		{
			string sql=" SELECT message, CASE m.sendby WHEN 'Geo Raabta' THEN 'Geo Raabta' ELSE " +
				" (SELECT e.name " +
				" FROM t_employee e " +
				" WHERE m.sendby = e.pcode) END AS sender, sendon, CASE m.sendto WHEN m.sendto THEN " +
				" (SELECT e.name " +
				" FROM t_employee e " +
				" WHERE e.pcode = m.sendto) END AS receiver " +
				" FROM dbo.t_employeerequestmessages AS m " +
				" WHERE (rid = "+rid+") AND (messagetype = "+mssagetype+") AND (section = '"+section+"') ";

			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			lblmsgTitle.Visible=true;
			lblMessage.Visible=true;

			if (ds.Tables[0].Rows.Count>0)
			{
				lblMessage.Text=ds.Tables[0].Rows[0]["message"].ToString();
			}
			else
			{
				lblMessage.Text="No Message Found";
			}

		}

		private void dgMyRequest_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			string rid=dgMyRequest.SelectedItem.Cells[2].Text;
			string typename=dgMyRequest.SelectedItem.Cells[7].Text;
			string type="";
			if(typename=="Inprocess")
				type="1";
			else if (typename=="Cancelled")
				type="2";
			else if (typename=="Accepted")
				type="3";
			else if(typename=="Rejected")
				type="4";
			string section=dgMyRequest.SelectedItem.Cells[8].Text;

			ReadMessage(section,rid,type);

		}

		private void ImageButton1_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("UserProfile.aspx?id=1");
		}

		private void ImageButton2_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("UserProfile.aspx?id=2");
		}

		private void imgMyGrievance_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("MyGrievance.aspx?pcode="+lblPCode.Text+"&name="+lblEmpInfo.Text);
		}
		/*private void dgMyRequest_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=null;
			SqlDataReader rd=null;
			this.lblMessage.Text="";
			this.lblmsgTitle.Visible=false;
			if(this.dgMyRequest.Items[this.dgMyRequest.SelectedIndex].Cells[7].Text=="Inprocess")
			{
			 cmd=new SqlCommand("select m.message,sender=case m.sendby when 'Geo Raabta' then 'Geo Raabta' else(select e.name from t_employee e where m.sendby=e.pcode)end,m.sendon,receiver=case m.sendto when m.sendto then(select e.name from t_employee e where e.pcode=m.sendto)end from t_employeerequestmessages m where m.rid="+this.dgMyRequest.Items[this.dgMyRequest.SelectedIndex].Cells[2].Text+" and m.messagetype=1",con);
			 rd=cmd.ExecuteReader();
			 rd.Read();
				if(rd.HasRows)
				{
					this.lblMessage.Text=rd[0].ToString();
					this.lblMessage.Visible=true;
					this.lblmsgTitle.Visible=true;
					rd.Close();
				}
				else
				{
					this.lblMessage.Text="No Message Found";
					this.lblMessage.Visible=true;
					this.lblmsgTitle.Visible=true;
				}
			}
			if(this.dgMyRequest.Items[this.dgMyRequest.SelectedIndex].Cells[7].Text=="Cancelled")
			{
				cmd=new SqlCommand("select m.message,sender=case m.sendby when 'Geo Raabta' then 'Geo Raabta' else(select e.name from t_employee e where m.sendby=e.pcode)end,m.sendon,receiver=case m.sendto when m.sendto then(select e.name from t_employee e where e.pcode=m.sendto)end from t_employeerequestmessages m where m.rid="+this.dgMyRequest.Items[this.dgMyRequest.SelectedIndex].Cells[2].Text+" and m.messagetype=2",con);
				rd=cmd.ExecuteReader();
				rd.Read();
				if(rd.HasRows)
				{
					this.lblMessage.Text=rd[0].ToString();
					this.lblMessage.Visible=true;
					this.lblmsgTitle.Visible=true;
					rd.Close();
				}
				else
				{
					this.lblMessage.Text="No Message Found";
					this.lblMessage.Visible=true;
					this.lblmsgTitle.Visible=true;
				}
			}
			if(this.dgMyRequest.Items[this.dgMyRequest.SelectedIndex].Cells[7].Text=="Accepted")
			{
				cmd=new SqlCommand("select m.message,sender=case m.sendby when 'Geo Raabta' then 'Geo Raabta' else(select e.name from t_employee e where m.sendby=e.pcode)end,m.sendon,receiver=case m.sendto when m.sendto then(select e.name from t_employee e where e.pcode=m.sendto)end from t_employeerequestmessages m where m.rid="+this.dgMyRequest.Items[this.dgMyRequest.SelectedIndex].Cells[2].Text+" and m.messagetype=3",con);
				rd=cmd.ExecuteReader();
				rd.Read();
				if(rd.HasRows)
				{
					this.lblMessage.Text=rd[0].ToString();
					this.lblMessage.Visible=true;
					this.lblmsgTitle.Visible=true;
					rd.Close();
				}
				else
				{
					this.lblMessage.Text="No Message Found";
					this.lblMessage.Visible=true;
					this.lblmsgTitle.Visible=true;
				}
			}
			if(this.dgMyRequest.Items[this.dgMyRequest.SelectedIndex].Cells[7].Text=="Rejected")
			{
				cmd=new SqlCommand("select m.message,sender=case m.sendby when 'Geo Raabta' then 'Geo Raabta' else(select e.name from t_employee e where m.sendby=e.pcode)end,m.sendon,receiver=case m.sendto when m.sendto then(select e.name from t_employee e where e.pcode=m.sendto)end from t_employeerequestmessages m where m.rid="+this.dgMyRequest.Items[this.dgMyRequest.SelectedIndex].Cells[2].Text+" and m.messagetype=4",con);
				rd=cmd.ExecuteReader();
				rd.Read();
				if(rd.HasRows)
				{
					this.lblMessage.Text=rd[0].ToString();
					this.lblMessage.Visible=true;
					this.lblmsgTitle.Visible=true;
					rd.Close();
				}
				else
				{
					this.lblMessage.Text="No Message Found";
					this.lblMessage.Visible=true;
					this.lblmsgTitle.Visible=true;
				}
			}
			con.Close();
		}*/
	}
}
