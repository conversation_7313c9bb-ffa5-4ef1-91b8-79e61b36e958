<%@ Register TagPrefix="uc1" TagName="myMenus" Src="myMenus.ascx" %>
<%@ Page language="c#" Codebehind="SelectUser.aspx.cs" AutoEventWireup="false" Inherits="GeoRabtaSite.admin.SelectUser" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Rabta ::</title>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="../StyleSheet1.css" type="text/css" rel="stylesheet">
		<link href="../Styles5.css" type="text/css" rel="stylesheet">
		<style type="text/css">.style1 { FONT-WEIGHT: bold; FONT-SIZE: 8px }
	.style2 { FONT-WEIGHT: bold; FONT-SIZE: 9pt }
		</style>
	</HEAD>
	<body bottomMargin="0" bgProperties="fixed" leftMargin="0" background="../images/bg.jpg"
		topMargin="0" rightMargin="0">
		<form id="myForm" name="MyForm" runat="server">
			<table width="817" height="100%" cellpadding="2" cellspacing="0" border="0" align="center"
				bgcolor="#ffffff">
				<tr>
					<td height="70" background="../images/orangestrip2.jpg" vAlign="middle" align="left"><FONT size="5"><STRONG>Geo 
								HR IS</STRONG></FONT></td>
				</tr>
				<tr>
					<td height="12" background="../images/bluestrip2.jpg"></td>
				</tr>
				<tr>
					<td height="20">
						<uc1:myMenus id="MyMenus1" runat="server"></uc1:myMenus></td>
				</tr>
				<tr>
					<td height="20" background="../images/bluestrip2.jpg"><STRONG><FONT size="4"><STRONG><FONT size="4">User 
										Management : Select User</FONT></STRONG></FONT></STRONG></td>
				</tr>
				<tr>
					<td vAlign="top" align="left">
						<TABLE id="Table3" cellSpacing="0" cellPadding="3" width="100%" border="0">
							<TR>
								<TD width="300">SBU:<BR>
									<asp:DropDownList id="ddlSBU" runat="server" AutoPostBack="True" Width="100%" CssClass="TextBox"></asp:DropDownList></TD>
								<TD></TD>
								<TD width="300">Department:<BR>
									<asp:DropDownList id="ddlDepartment" runat="server" AutoPostBack="True" Width="100%" CssClass="TextBox"></asp:DropDownList></TD>
							</TR>
							<TR>
								<TD colSpan="3">
									<asp:DataGrid id="dgEmployee" runat="server" Width="100%" BorderStyle="None" BorderWidth="1px"
										CellPadding="4" AutoGenerateColumns="False" BackColor="White" BorderColor="#3366CC" AllowSorting="True">
										<SelectedItemStyle Font-Bold="True" ForeColor="#CCFF99" BackColor="#009999"></SelectedItemStyle>
										<ItemStyle ForeColor="#003399" BackColor="White"></ItemStyle>
										<HeaderStyle Font-Bold="True" ForeColor="#CCCCFF" BackColor="#003399"></HeaderStyle>
										<FooterStyle ForeColor="#003399" BackColor="#99CCCC"></FooterStyle>
										<Columns>
											<asp:BoundColumn DataField="PCode" SortExpression="PCode" HeaderText="Employee Code"></asp:BoundColumn>
											<asp:BoundColumn DataField="name" SortExpression="name" HeaderText="Name"></asp:BoundColumn>
											<asp:BoundColumn DataField="designation" SortExpression="designation" HeaderText="Designation"></asp:BoundColumn>
											<asp:ButtonColumn Text="Select" CommandName="Select">
												<ItemStyle HorizontalAlign="Center" Width="60px" VerticalAlign="Bottom"></ItemStyle>
											</asp:ButtonColumn>
										</Columns>
										<PagerStyle HorizontalAlign="Left" ForeColor="#003399" BackColor="#99CCCC" Mode="NumericPages"></PagerStyle>
									</asp:DataGrid></TD>
							</TR>
						</TABLE>
					</td>
				</tr>
				<tr>
					<td height="20" vAlign="middle" align="center">Copyright © 2005 Independent Media 
						Corporation <A href="http://www.geo.tv">www.geo.tv</A></td>
				</tr>
			</table>
		</form>
	</body>
</HTML>
