<%@ Register TagPrefix="uc1" TagName="myMenus" Src="myMenus.ascx" %>
<%@ Page CodeBehind="Pulse.aspx.cs" Language="c#" AutoEventWireup="false" Inherits="GeoRabtaSite.Pulse" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Raabta Admin :: Pulse</title>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="RaabtaAdmin.css" type="text/css" rel="stylesheet">
		<LINK href="../Styles4.css" type="text/css" rel="stylesheet">
		<style type="text/css">.style1 { FONT-WEIGHT: bold; FONT-SIZE: 8px }
	.style2 { FONT-WEIGHT: bold; FONT-SIZE: 9pt }
		</style>
		<script language="javascript">
		function ValidatePage()
		{
		 //alert(document.getElementById('r1'));		
		 if(document.getElementById('txtTitle').value=='')
		 {
		  alert('Please Enter Pulse Title');
		  document.getElementById('txtTitle').focus();
		  return false;
		 }
		 else if(document.getElementById('ddType').selectedIndex==0)
		 {
		  alert('Please Select Pulse Type');
		  document.getElementById('ddType').focus();
		  return false;
		 }
		 else if(document.getElementById('txtDescription').value=='')
		 {
		  alert('Please Enter Description');
		  document.getElementById('txtDescription').focus();
		  return false;
		 }
		 else if(document.getElementById('ddDept').selectedIndex==0)
		 {
		  alert('Please Select Department');
		  document.getElementById('ddDept').focus();
		  return false;
		 }
		 else if(document.getElementById('ddStation').selectedIndex==0)
		 {
		  alert('Please Select Station');
		  document.getElementById('ddStation').focus();
		  return false;
		 }
		 else if(document.getElementById('ddSendTo').selectedIndex==0)
		 {
		  alert('Please Select Send To Option');
		  document.getElementById('ddSendTo').focus();
		  return false;
		 }
		 else if(document.getElementById('r1')!=null)
		 {
		 var aControl=document.getElementById('chkReportingList');
		 var arrayOfCheckBoxes= aControl.getElementsByTagName("input");
		 var count=0;
		 for(var a=0;a<arrayOfCheckBoxes.length;a++)
		  {
		    if(arrayOfCheckBoxes[a].checked)
		    count++;
		  }
		   if(count==0)
		   {
		   window.alert('Please Select Team/Line To Send Pulse'); 
		   return false;
		   }
		 }
		 else
		 {
		  return true;
		 }
	 }
		</script>
	</HEAD>
	<body dir="ltr" bottomMargin="0" bgProperties="fixed" leftMargin="0" topMargin="0" rightMargin="0">
		<form id="ideas" runat="server">
			<table height="100%" cellSpacing="0" cellPadding="0" width="780" align="center" bgColor="#ffffff"
				border="0">
				<tr>
					<td vAlign="middle" align="left" height="69">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="69" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="1826">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top1.swf">
							<PARAM NAME="Src" VALUE="flash/Top1.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top1.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="69"> </embed>
						</OBJECT>
					</td>
				</tr>
				<tr>
					<td height="10">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="10" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="265">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top2.swf">
							<PARAM NAME="Src" VALUE="flash/Top2.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top2.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="10"> </embed>
						</OBJECT>
					</td>
				</tr>
				<tr>
					<td class="PageTitle" height="20">Geo Raabta Admin :: Pulse</td>
				</tr>
				<TR>
					<TD class="MenuBar" height="20"><uc1:mymenus id="MyMenus1" runat="server"></uc1:mymenus></TD>
				</TR>
				<tr>
					<td class="MainBG" vAlign="top" align="left"><BR>
						<table class="MainFormColor" id="tabForm" cellSpacing="0" cellPadding="3" width="750" align="center"
							border="0" runat="server">
							<tr>
								<td class="OrangeFormTitle" width="320" colSpan="3">Pulse</td>
							</tr>
							<TR>
								<TD class="MenuBar" width="320" colSpan="3"><STRONG>Title:</STRONG></TD>
							</TR>
							<TR>
								<TD width="320" colSpan="3"><asp:textbox id="txtTitle" runat="server" ToolTip="Pulse Title" MaxLength="50" Width="712px"
										CssClass="textbox"></asp:textbox></TD>
							</TR>
							<TR>
								<TD class="menuBar" colSpan="3"><STRONG>Pulse Type:</STRONG></TD>
							</TR>
							<TR>
								<TD colSpan="3"><asp:dropdownlist id="ddType" runat="server" Width="480px" CssClass="textbox">
										<asp:ListItem Value="0">Select--Pulse Type</asp:ListItem>
									</asp:dropdownlist></TD>
							</TR>
							<TR>
								<TD class="menuBar" colSpan="3"><STRONG>Description:</STRONG></TD>
							</TR>
							<TR>
								<TD colSpan="3"><asp:textbox id="txtDescription" runat="server" ToolTip="Pulse Description" Width="712px" CssClass="textbox"
										Height="160px" TextMode="MultiLine"></asp:textbox></TD>
							</TR>
							<TR>
								<TD class="menuBar" colSpan="3"><STRONG>Department:</STRONG> (For which Pulse is 
									generating)</TD>
							</TR>
							<TR>
								<TD colSpan="3"><asp:dropdownlist id="ddDept" runat="server" Width="480px" CssClass="textbox">
										<asp:ListItem Value="0">Select--Department</asp:ListItem>
									</asp:dropdownlist></TD>
							</TR>
							<TR>
								<TD class="menuBar" style="HEIGHT: 18px" colSpan="3"><STRONG>Station:</STRONG> (For 
									which Pulse is generating)</TD>
							</TR>
							<TR>
								<TD colSpan="3"><asp:dropdownlist id="ddStation" runat="server" Width="480px" CssClass="textbox">
										<asp:ListItem Value="0">Select--Station</asp:ListItem>
									</asp:dropdownlist></TD>
							</TR>
							<TR>
								<TD class="menuBar" colSpan="3"><STRONG>Pulse Send To:</STRONG></TD>
							</TR>
							<TR>
								<TD colSpan="3"><asp:dropdownlist id="ddSendTo" runat="server" Width="480px" CssClass="textbox" AutoPostBack="True">
										<asp:ListItem Value="0">Select--Send To</asp:ListItem>
										<asp:ListItem Value="1">Send to MyTeam</asp:ListItem>
										<asp:ListItem Value="2">Send to MyLine Manager</asp:ListItem>
										<asp:ListItem Value="3">Send to Both</asp:ListItem>
									</asp:dropdownlist></TD>
							</TR>
							<TR>
								<TD id="r1" colSpan="3" runat="server">
									<DIV style="OVERFLOW: auto; WIDTH: 712px; HEIGHT: 126px" ms_positioning="FlowLayout"><asp:label id="Label1" runat="server">Label</asp:label><STRONG>(Based 
											on Organogram)</STRONG>
										<asp:checkboxlist id="chkReportingList" runat="server"></asp:checkboxlist></DIV>
								</TD>
							</TR>
							<TR>
								<TD class="menubar" id="r2" colSpan="3" runat="server"><STRONG id="r2">Is Pulse Add To 
										Issue &amp; Task Sheet of&nbsp;above&nbsp;selected&nbsp;Team?:</STRONG></TD>
							</TR>
							<TR>
								<TD id="r3" colSpan="3" runat="server"><asp:dropdownlist id="ddAddToTS" runat="server" Width="480px" CssClass="textbox">
										<asp:ListItem Value="0">No</asp:ListItem>
										<asp:ListItem Value="1">Yes</asp:ListItem>
									</asp:dropdownlist></TD>
							</TR>
							<TR>
								<TD colSpan="3"><asp:button id="btnSend" runat="server" CssClass="button" Text="Send Pulse"></asp:button><asp:button id="btnNew" runat="server" CssClass="button" Text="New Pulse"></asp:button><BR>
									<asp:Label id="Label2" runat="server" Visible="False"></asp:Label></TD>
							</TR>
						</table>
						<BR>
						<TABLE id="Table1" cellSpacing="0" cellPadding="3" width="750" align="center" border="0">
							<TR>
								<TD></TD>
							</TR>
							<TR>
								<TD></TD>
							</TR>
						</TABLE>
						<BR>
						<TABLE id="Table4" cellSpacing="0" cellPadding="3" width="100%" border="0">
							<TR>
								<TD colSpan="3"></TD>
							</TR>
						</TABLE>
					</td>
				</tr>
				<TR>
					<TD vAlign="middle" align="center" height="20">Copyright © 2005 Independent Media 
						Corporation <A href="http://www.geo.tv">www.geo.tv</A></TD>
				</TR>
			</table>
		</form>
	</body>
</HTML>
