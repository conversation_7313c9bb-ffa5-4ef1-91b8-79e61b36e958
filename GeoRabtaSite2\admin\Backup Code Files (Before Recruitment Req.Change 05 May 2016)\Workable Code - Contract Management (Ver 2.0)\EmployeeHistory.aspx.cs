using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

namespace GeoRabtaSite.admin
{
	/// <summary>
	/// Summary description for EmployeeHistory.
	/// </summary>
	public class EmployeeHistory : System.Web.UI.Page
	{
		protected System.Web.UI.WebControls.DataGrid dgPersonal;
		protected System.Web.UI.WebControls.DataGrid dgEmployment;
		protected System.Web.UI.WebControls.DataGrid dgEducation;
		protected System.Web.UI.WebControls.DataGrid dgFamily;
		protected System.Web.UI.WebControls.DataGrid dgBondPaper;
		protected SqlConnection conn;
		protected System.Web.UI.WebControls.DataGrid dgCnB;
		protected System.Web.UI.WebControls.DataGrid dgFunctionalDesignation;
		protected System.Web.UI.WebControls.LinkButton ViewComp;
		private string pcode;

		private void FillFamilyHistory()
		{
			string sql="SELECT TOP 100 PERCENT Description, OldValue, NewValue, ModificationDate, PCode, CASE ModifiedBy WHEN 'Admin' THEN 'HRIS Admin' ELSE " +
				" (SELECT name FROM t_employee " +
				" WHERE pcode = eh.modifiedby) END AS ModifiedBy " +
				" FROM dbo.h_employee AS eh " +
				" WHERE (eh.SectionType = 'Family') AND (eh.PCode = '" + pcode + "') " +
				" ORDER BY eh.Description, eh.ModificationDate DESC " ;
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds = new DataSet();
			da.Fill(ds);
			if (ds.Tables[0].Rows.Count>0)
			{
				dgFamily.DataSource=ds.Tables[0].DefaultView;
				dgFamily.DataBind();
			}
		}

		private void FillPersoanlHistory()
		{
			string sql="SELECT TOP 100 PERCENT Description, OldValue, NewValue, ModificationDate, PCode, CASE ModifiedBy WHEN 'Admin' THEN 'HRIS Admin' ELSE " +
				" (SELECT name FROM t_employee " +
				" WHERE pcode = eh.modifiedby) END AS ModifiedBy " +
				" FROM dbo.h_employee AS eh " +
				" WHERE (eh.SectionType = 'Personal') AND (eh.PCode = '" + pcode + "') " +
				" ORDER BY eh.Description, eh.ModificationDate DESC " ;
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds = new DataSet();
			da.Fill(ds);
			if (ds.Tables[0].Rows.Count>0)
			{
				dgPersonal.DataSource=ds.Tables[0].DefaultView;
				dgPersonal.DataBind();
			}
		}

		private void FillEmploymentHistory()
		{
			string sql="SELECT TOP 100 PERCENT Description, OldValue, NewValue, ModificationDate, PCode, CASE ModifiedBy WHEN 'Admin' THEN 'HRIS Admin' ELSE " +
				" (SELECT name FROM t_employee " +
				" WHERE pcode = eh.modifiedby) END AS ModifiedBy " +
				" FROM dbo.h_employee AS eh " +
				" WHERE (eh.SectionType = 'Employment') AND (eh.PCode = '" + pcode + "') " +
				" ORDER BY eh.Description, eh.ModificationDate DESC " ;
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds = new DataSet();
			da.Fill(ds);
			if (ds.Tables[0].Rows.Count>0)
			{
				dgEmployment.DataSource=ds.Tables[0].DefaultView;
				dgEmployment.DataBind();
			}

		}
		private void FillEducationalHistory()
		{
			string sql="SELECT TOP 100 PERCENT Description, OldValue, NewValue, ModificationDate, PCode, CASE ModifiedBy WHEN 'Admin' THEN 'HRIS Admin' ELSE " +
				" (SELECT name FROM t_employee " +
				" WHERE pcode = eh.modifiedby) END AS ModifiedBy " +
				" FROM dbo.h_employee AS eh " +
				" WHERE (eh.SectionType = 'Educational Inforamtion') AND (eh.PCode = '" + pcode + "') " +
				" ORDER BY eh.Description, eh.ModificationDate DESC " ;
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds = new DataSet();
			da.Fill(ds);
			if (ds.Tables[0].Rows.Count>0)
			{
				dgEducation.DataSource=ds.Tables[0].DefaultView;
				dgEducation.DataBind();
			}
		}
		
		private void FillDesignationlHistory()
		{
			/*string sql="SELECT TOP 100 PERCENT Description, OldValue, NewValue, ModificationDate, PCode, CASE ModifiedBy WHEN 'Admin' THEN 'HRIS Admin' ELSE " +
				" (SELECT name FROM t_employee " +
				" WHERE pcode = eh.modifiedby) END AS ModifiedBy " +
				" FROM dbo.h_employee AS eh " +
				" WHERE (eh.SectionType = 'Functional Designation') AND (eh.PCode = '" + pcode + "') " +
				" ORDER BY eh.Description, eh.ModificationDate DESC " ;
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds = new DataSet();
			da.Fill(ds);
			if (ds.Tables[0].Rows.Count>0)
			{
				dgDesignation.DataSource=ds.Tables[0].DefaultView;
				dgDesignation.DataBind();
			}*/

			this.dgFunctionalDesignation.Visible=true;
			string getRec="select f.fdesigid fdesigid,d.designation designation,f.functionaltitle title,h._from _from,h._to _to,status=case h.isactive when '0' then 'De-Active' when '1' then 'Active'end from t_functionaldesignation f,t_designation d,t_designationhistory h where h.fdesigid=f.fdesigid And d.desigid=f.functionaldesignation And h.pcode='"+ pcode +"' order by status";
			SqlDataAdapter rd=new SqlDataAdapter(getRec,conn);
			DataSet dm=new DataSet();
			rd.Fill(dm,"Record");
			this.dgFunctionalDesignation.DataSource=dm;
			this.dgFunctionalDesignation.DataBind(); 
		}

		private void FillBondPaperHistory()
		{
			string sql="SELECT TOP 100 PERCENT Description, OldValue, NewValue, ModificationDate, PCode, CASE ModifiedBy WHEN 'Admin' THEN 'HRIS Admin' ELSE " +
				" (SELECT name FROM t_employee " +
				" WHERE pcode = eh.modifiedby) END AS ModifiedBy " +
				" FROM dbo.h_employee AS eh " +
				" WHERE (eh.SectionType like 'Bond Paper') AND (eh.PCode = '" + pcode + "') " +
				" ORDER BY eh.Description, eh.ModificationDate DESC " ;
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds = new DataSet();
			da.Fill(ds);
			if (ds.Tables[0].Rows.Count>0)
			{
				dgBondPaper.DataSource=ds.Tables[0].DefaultView;
				dgBondPaper.DataBind();
			}
		}
		private void FillCnBHistory()
		{
			string sql="SELECT Description, OldValue, NewValue, ModificationDate, PCode, CASE ModifiedBy WHEN 'Admin' THEN 'HRIS Admin' ELSE " +
				" (SELECT name FROM t_employee " +
				" WHERE pcode = eh.modifiedby) END AS ModifiedBy " +
				" FROM dbo.h_employee AS eh " +
				" WHERE (eh.SectionType = 'Compensation and Benefits') AND (eh.PCode = '" + pcode + "') " +
				" ORDER BY  eh.ModificationDate DESC " ;
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds = new DataSet();
			da.Fill(ds);
			if (ds.Tables[0].Rows.Count>0)
			{
				GeoRabtaSite.admin.Convertor c=new Convertor();
				ds=c.SetGrid(ds);
				dgCnB.DataSource=ds.Tables[0].DefaultView;
				dgCnB.DataBind();
			}
		}
		public bool ValidateAccount(string ID)
		{
			if (conn.State==ConnectionState.Closed)
				conn.Open();
			string selectQry="select * from t_password where pcode='"+Session["user_id"].ToString()+"' and (state= 1 or state=0)";
			SqlCommand cmd=new SqlCommand(selectQry,conn);
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			if(rd.HasRows)
			{
				rd.Close();
				return true;
			}
			else
			{
				rd.Close();
				return false;
			}
		}
		private void Page_Load(object sender, System.EventArgs e)
		{
			// Put user code to initialize the page here
			conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			pcode=Request.QueryString[0].ToString().Trim();
			//=====================================Authenticate Compensation Rights========================//
			if(GeoSecurity.isControlVisible(2,32,Session["user_id"].ToString(),"Compensation")==true)
			{
				if(GeoSecurity.isControlVisible(2,33,Session["user_id"].ToString(),"View Compensation & Benefits Info")==true && ValidateAccount(Session["user_id"].ToString().Trim()))
				{
					this.ViewComp.Visible=true;
				}
				else
				{
					this.ViewComp.Visible=false;
				}
			}
			else
			{ 
				SqlCommand cmd =new SqlCommand("select b.deptid from t_bumanagers b where pcode='"+Session["user_id"].ToString()+"' and b.isactive=1 and b.deptid in (select dept.deptid from t_employee e,t_designation d,t_department dept where e.desigid=d.desigid and dept.deptid=d.deptid and e.pcode='"+Request.QueryString[0].ToString().Trim()+"')",conn);
				SqlDataReader de=cmd.ExecuteReader();
				de.Read();
				if(de.HasRows)
				{
					this.ViewComp.Visible=true;
					de.Close();
					//conn.Close();
				}
				else
				{
					this.ViewComp.Visible=false;
					de.Close();
					//conn.Close();
				}
			}
			if(!IsPostBack)
			{
				if(GeoSecurity.isAllAllow("Station",Session["user_id"].ToString()))
				{}
				else
				{
					string Query="SELECT station FROM dbo.t_Employee WHERE (pcode = '"+Request.QueryString[0].ToString().Trim()+"') AND (station IN (SELECT dbo.t_RoleDemographic.rights "+
						"FROM dbo.t_usersroles INNER JOIN dbo.t_RoleDemographic ON dbo.t_usersroles.roleID = dbo.t_RoleDemographic.roleid INNER JOIN "+
						"dbo.t_demographic ON dbo.t_RoleDemographic.did = dbo.t_demographic.id "+
						"WHERE (dbo.t_demographic.dname = 'Station') AND (dbo.t_usersroles.PCode = '"+Session["user_id"].ToString()+"')))";
					SqlCommand rcm=new SqlCommand(Query,conn);
					SqlDataReader der=rcm.ExecuteReader();
					der.Read();
					if(der.HasRows)
					{
						der.Close();
					}
					else
					{
						der.Close();
						//conn.Close();
						Response.Redirect("ErrorPage.aspx");
					}
					
				}
				if(GeoSecurity.isAllAllow("Department",Session["user_id"].ToString()))
				{}
				else
				{
					string Query="SELECT dbo.t_Department.deptid FROM dbo.t_Department INNER JOIN dbo.t_Designation ON dbo.t_Department.deptid = dbo.t_Designation.deptid INNER JOIN "+
						"dbo.t_Employee ON dbo.t_Designation.desigid = dbo.t_Employee.desigid WHERE (dbo.t_Employee.pcode = '"+Request.QueryString[0].ToString().Trim()+"') AND (dbo.t_Department.deptid IN "+
						"(SELECT dbo.t_RoleDemographic.rights FROM dbo.t_usersroles INNER JOIN "+
						"dbo.t_RoleDemographic ON dbo.t_usersroles.roleID = dbo.t_RoleDemographic.roleid INNER JOIN "+
						"dbo.t_demographic ON dbo.t_RoleDemographic.did = dbo.t_demographic.id "+
						"WHERE (dbo.t_demographic.dname = 'Department') AND (dbo.t_usersroles.PCode = '"+Session["user_id"].ToString()+"')))";
					SqlCommand rcm=new SqlCommand(Query,conn);
					SqlDataReader der=rcm.ExecuteReader();
					der.Read();
					if(der.HasRows)
					{
						der.Close();
					}
					else
					{
						der.Close();
						//conn.Close();
						Response.Redirect("ErrorPage.aspx");
					}
				}
			}
			
			FillPersoanlHistory();
			FillEmploymentHistory();
			FillEducationalHistory();
			FillFamilyHistory();
			FillDesignationlHistory();
			FillBondPaperHistory();
			FillCnBHistory();
			ViewComp.Attributes.Add("onclick", "return (window.showModalDialog('popup.aspx','yyy','dialogHeight:200px;dialogWidth:300px;center:yes;status:no;')==true)");
		    conn.Close();
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.ViewComp.Click += new System.EventHandler(this.ViewComp_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		private void ViewComp_Click(object sender, System.EventArgs e)
		{
			this.dgCnB.Visible=true;
			this.ViewComp.Visible=false;
			SqlConnection con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("insert into t_userlog(userid,action,description,accessdate) values(@userid,@action,@description,@accessdate)",con);
			SqlParameter _userid=new SqlParameter("@userid",SqlDbType.Char);
			SqlParameter _action=new SqlParameter("@action",SqlDbType.VarChar);
			SqlParameter _description=new SqlParameter("@description",SqlDbType.VarChar);
			SqlParameter _accessdate=new SqlParameter("@accessdate",SqlDbType.DateTime);
			_userid.Value=Session["user_id"].ToString().Trim();
			_action.Value="View Compensation";
			_description.Value="Viewed Profile Details:Pcode="+Request.QueryString[0].ToString().Trim()+",Name="+Request.QueryString[1].ToString().Trim();
			_accessdate.Value=DateTime.Now;
			cmd.Parameters.Add(_userid);
			cmd.Parameters.Add(_action);
			cmd.Parameters.Add(_description);
			cmd.Parameters.Add(_accessdate);
			cmd.ExecuteNonQuery();
			con.Close();
			con.Dispose();
		}
	}
}
