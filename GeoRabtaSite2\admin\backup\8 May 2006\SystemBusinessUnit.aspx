<%@ Register TagPrefix="ew" Namespace="eWorld.UI" Assembly="eWorld.UI" %>
<%@ Page CodeBehind="SystemBusinessUnit.aspx.cs" Language="c#" AutoEventWireup="false" Inherits="GeoRabtaSite.GeoSBU" smartNavigation="False"%>
<%@ Register TagPrefix="uc1" TagName="myMenus" Src="myMenus.ascx" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Raabta Admin :: Strategic Business Unit</title>
		<meta name="vs_snapToGrid" content="False">
		<meta name="vs_showGrid" content="False">
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="RaabtaAdmin.css" type="text/css" rel="stylesheet">
		<link href="../Styles4.css" type="text/css" rel="stylesheet">
		<script id="clientEventHandlersJS" language="javascript">
<!--

function window_onload() {
	t=document.getElementById('txtName');
	if(t)
		t.focus();
}

//-->
		</script>
	</HEAD>
	<body dir="ltr" bottomMargin="0" leftMargin="0" topMargin="0" rightMargin="0" language="javascript"
		onload="return window_onload()">
		<form id="myForm" name="MyForm" runat="server">
			<table height="100%" cellSpacing="0" cellPadding="0" width="780" align="center" bgColor="#ffffff"
				border="0">
				<tr>
					<td vAlign="middle" align="left" height="69">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="69" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="1826">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top1.swf">
							<PARAM NAME="Src" VALUE="flash/Top1.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<embed src="flash/Top1.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="69"> </embed>
						</OBJECT>
					</td>
				</tr>
				<tr>
					<td height="10">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="10" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="265">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top2.swf">
							<PARAM NAME="Src" VALUE="flash/Top2.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<embed src="flash/Top2.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="10"> </embed>
						</OBJECT>
					</td>
				</tr>
				<TR>
					<TD class="PageTitle" height="20">Geo Raabta Admin :: Strategic Business Unit</TD>
				</TR>
				<tr>
					<td height="20" background="../images/menu-off-bg.gif">
						<uc1:myMenus id="MyMenus1" runat="server"></uc1:myMenus></td>
				</tr>
				<TR>
					<TD class="MainBG" vAlign="top"><BR>
						<TABLE class="MainFormColor" id="Table3" cellSpacing="0" cellPadding="3" width="750" align="center"
							border="0">
							<TR>
								<TD class="OrangeFormTitle" colSpan="3">Strategic Business Unit Information:</TD>
							</TR>
							<TR>
								<TD class="menubar" width="320"><STRONG>System Business Unit Name:
										<asp:RequiredFieldValidator id="RequiredFieldValidator1" runat="server" ControlToValidate="txtName" Display="Dynamic"
											ErrorMessage="* Required"></asp:RequiredFieldValidator>
										<asp:RegularExpressionValidator id="RegularExpressionValidator1" runat="server" ControlToValidate="txtName" ErrorMessage="*"
											ValidationExpression="^[ a-z A-Z 0-9 ( ),.-]*$"></asp:RegularExpressionValidator></STRONG></TD>
								<TD class="menubar" width="95"></TD>
								<TD class="menubar" width="320">
									<asp:Label id="lblSBUID" runat="server" Visible="False"></asp:Label></TD>
							</TR>
							<TR>
								<TD width="320">
									<asp:TextBox id="txtName" runat="server" MaxLength="200" Width="100%" CssClass="TextBox"></asp:TextBox></TD>
								<TD width="95"></TD>
								<TD width="320"></TD>
							</TR>
							<TR>
								<TD colSpan="3">
									<asp:Button id="btnAdd" runat="server" Width="90px" Text="Add"></asp:Button>
									<asp:Button id="cmdDelete" runat="server" Width="90px" Text="Delete" CausesValidation="False"
										Enabled="False"></asp:Button>
									<asp:Button id="cmdCancel" runat="server" Width="90px" Text="Cancel" CausesValidation="False"
										Enabled="False"></asp:Button>
									<asp:Button id="cmdSBUHead" runat="server" Width="90px" Visible="False" Text="SBU Head" CausesValidation="False"
										Enabled="False"></asp:Button></TD>
							</TR>
							<TR>
								<TD colSpan="3">
									<asp:Label id="lblMessage" runat="server" Font-Bold="True"></asp:Label></TD>
							</TR>
							<TR>
								<TD colSpan="3" id="trError" runat="server">
									<asp:Label id="lblError" runat="server" CssClass="ErrorLabel"></asp:Label></TD>
							</TR>
							<TR>
								<TD colSpan="3">
									<asp:DataGrid id="DataGrid1" runat="server" Width="100%" BorderColor="Navy" BorderWidth="1px"
										BorderStyle="None" AutoGenerateColumns="False" AllowSorting="True" GridLines="Horizontal"
										CellPadding="3" DataKeyField="sbuid">
										<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
										<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
										<AlternatingItemStyle CssClass="alternetitem"></AlternatingItemStyle>
										<ItemStyle CssClass="item"></ItemStyle>
										<HeaderStyle Font-Bold="True" ForeColor="Black" CssClass="header"></HeaderStyle>
										<Columns>
											<asp:EditCommandColumn ButtonType="LinkButton" UpdateText="Update" CancelText="Cancel" EditText="View">
												<ItemStyle Width="40px"></ItemStyle>
											</asp:EditCommandColumn>
											<asp:BoundColumn Visible="False" DataField="sbuid"></asp:BoundColumn>
											<asp:BoundColumn DataField="sbuname" HeaderText="SBU Name"></asp:BoundColumn>
										</Columns>
										<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
									</asp:DataGrid></TD>
							</TR>
							<TR>
								<TD colSpan="3">
									<asp:Label id="Label1" runat="server" Visible="False">Record Not Found</asp:Label></TD>
							</TR>
						</TABLE>
						<BR>
						<TABLE class="MainFormColor" id="Panel12" cellSpacing="0" cellPadding="3" width="750" align="center"
							border="0" runat="server">
							<TR>
								<TD class="OrangeFormTitle" colSpan="3">Strategic Business Unit Head Information:</TD>
							</TR>
							<TR>
								<TD colSpan="3">
									<asp:Button id="btnChange" runat="server" Width="184px" Text="Change SBU Head" CausesValidation="False"></asp:Button></TD>
							</TR>
							<TR>
								<TD id="Panel1" colSpan="3" runat="server">
									<TABLE class="MainFormColor" id="Panel13" cellSpacing="0" cellPadding="3" width="100%"
										align="center" border="0" runat="server">
										<TR>
											<TD class="OrangeFormTitle" colSpan="3">Select SBU Head</TD>
										</TR>
										<TR>
											<TD class="menubar" width="320"><STRONG>SBU Head:</STRONG></TD>
											<TD class="menubar" width="85"></TD>
											<TD class="menubar" width="320">
												<asp:Label id="lblSbuHeadID" runat="server" Visible="False"></asp:Label></TD>
										</TR>
										<TR>
											<TD width="320">
												<asp:DropDownList id="ddlHOD" runat="server" Width="100%"></asp:DropDownList></TD>
											<TD width="85"></TD>
											<TD width="320"></TD>
										</TR>
										<TR>
											<TD class="menubar" width="320"><STRONG>From:</STRONG></TD>
											<TD class="menubar" width="85"></TD>
											<TD class="menubar" width="320"><STRONG>
													<asp:CheckBox id="CheckBox1" runat="server" Text="To" AutoPostBack="True"></asp:CheckBox></STRONG></TD>
										</TR>
										<TR>
											<TD width="320">
												<ew:CalendarPopup id="dpFrom" runat="server">
													<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="White"></WeekdayStyle>
													<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="Yellow"></MonthHeaderStyle>
													<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
														BackColor="AntiqueWhite"></OffMonthStyle>
													<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="White"></GoToTodayStyle>
													<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="LightGoldenrodYellow"></TodayDayStyle>
													<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="Orange"></DayHeaderStyle>
													<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="LightGray"></WeekendStyle>
													<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="Yellow"></SelectedDateStyle>
													<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="White"></ClearDateStyle>
													<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="White"></HolidayStyle>
												</ew:CalendarPopup></TD>
											<TD width="85"></TD>
											<TD width="320">
												<ew:CalendarPopup id="dpTo" runat="server" Visible="False">
													<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="White"></WeekdayStyle>
													<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="Yellow"></MonthHeaderStyle>
													<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
														BackColor="AntiqueWhite"></OffMonthStyle>
													<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="White"></GoToTodayStyle>
													<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="LightGoldenrodYellow"></TodayDayStyle>
													<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="Orange"></DayHeaderStyle>
													<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="LightGray"></WeekendStyle>
													<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="Yellow"></SelectedDateStyle>
													<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="White"></ClearDateStyle>
													<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="White"></HolidayStyle>
												</ew:CalendarPopup></TD>
										</TR>
										<TR>
											<TD width="320">
												<asp:Button id="cmdSave" runat="server" Width="90px" Text="Save" CausesValidation="False"></asp:Button>
												<asp:Button id="Button1" runat="server" Width="90px" Text="Cancel" CausesValidation="False"></asp:Button></TD>
											<TD width="85"></TD>
											<TD width="320"></TD>
										</TR>
										<TR>
											<TD style="HEIGHT: 17px" width="320">
												<asp:Label id="lblHOD" runat="server" Visible="False" ForeColor="Red" Font-Bold="True"></asp:Label></TD>
											<TD style="HEIGHT: 17px" width="85"></TD>
											<TD style="HEIGHT: 17px" width="320"></TD>
										</TR>
									</TABLE>
								</TD>
							</TR>
							<TR>
								<TD colSpan="3">
									<asp:Panel id="pnlEndDateSBU" runat="server" Visible="False" CssClass="MainFormColor" Width="100%">
										<TABLE class="MainFormColor" id="Table1" cellSpacing="0" cellPadding="3" width="100%" border="0">
											<TR>
												<TD class="OrangeFormTitle">End Date is Required</TD>
											</TR>
											<TR>
												<TD><FONT color="red" size="2"><STRONG>An Active SBU Head's end date is not given. Please 
															Enter End Date to&nbsp;add a new SBU Head's record.</STRONG></FONT></TD>
											</TR>
											<TR>
												<TD><STRONG>End Date:</STRONG><BR>
													<ew:CalendarPopup id="dpEndDate" runat="server">
														<TextboxLabelStyle CssClass="textbox"></TextboxLabelStyle>
														<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></WeekdayStyle>
														<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Yellow"></MonthHeaderStyle>
														<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
															BackColor="AntiqueWhite"></OffMonthStyle>
														<ButtonStyle CssClass="button"></ButtonStyle>
														<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></GoToTodayStyle>
														<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="LightGoldenrodYellow"></TodayDayStyle>
														<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Orange"></DayHeaderStyle>
														<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="LightGray"></WeekendStyle>
														<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Yellow"></SelectedDateStyle>
														<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></ClearDateStyle>
														<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></HolidayStyle>
													</ew:CalendarPopup></TD>
											</TR>
											<TR>
												<TD>
													<asp:Button id="btnUpdateBUDate" runat="server" Width="110px" Text="Update"></asp:Button>
													<asp:Button id="Button7" runat="server" Width="110px" Text="Cancel"></asp:Button></TD>
											</TR>
										</TABLE>
									</asp:Panel></TD>
							</TR>
							<TR>
								<TD colSpan="3">
									<asp:DataGrid id="DataGrid2" runat="server" Width="100%" BorderColor="Navy" BorderWidth="1px"
										BorderStyle="None" AutoGenerateColumns="False" AllowSorting="True" GridLines="Horizontal"
										CellPadding="3" DataKeyField="sbuheadid">
										<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
										<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
										<AlternatingItemStyle CssClass="alternetitem"></AlternatingItemStyle>
										<ItemStyle CssClass="item"></ItemStyle>
										<HeaderStyle Font-Bold="True" ForeColor="Black" CssClass="header"></HeaderStyle>
										<Columns>
											<asp:BoundColumn Visible="False" DataField="sbuheadid" HeaderText="sbuheadid"></asp:BoundColumn>
											<asp:BoundColumn DataField="Name" SortExpression="Name" HeaderText="Name"></asp:BoundColumn>
											<asp:BoundColumn DataField="DateFrom" SortExpression="datefrom" HeaderText="From" DataFormatString="{0:d}"></asp:BoundColumn>
											<asp:BoundColumn DataField="dateto" SortExpression="dateto" HeaderText="To" DataFormatString="{0:d}"></asp:BoundColumn>
											<asp:BoundColumn DataField="IsActive" SortExpression="IsActive" HeaderText="IsActive"></asp:BoundColumn>
											<asp:EditCommandColumn ButtonType="LinkButton" UpdateText="Update" CancelText="Cancel" EditText="Edit"></asp:EditCommandColumn>
										</Columns>
										<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
									</asp:DataGrid></TD>
							</TR>
						</TABLE>
						<BR>
					</TD>
				</TR>
				<TR>
					<TD vAlign="middle" align="center" height="20">Copyright © 2005 Independent Media 
						Corporation <A href="http://www.geo.tv">www.geo.tv</A></TD>
				</TR>
			</table>
		</form>
	</body>
</HTML>
