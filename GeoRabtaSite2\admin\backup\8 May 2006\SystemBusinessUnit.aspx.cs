using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;
namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for GeoIdeas.
	/// </summary>
	public class GeoSBU : System.Web.UI.Page
	{
		private SqlConnection con;
		protected System.Web.UI.WebControls.Button btnAdd;
		protected System.Web.UI.WebControls.DataGrid DataGrid1;
		protected System.Web.UI.WebControls.Button cmdDelete;
		protected System.Web.UI.WebControls.Button cmdCancel;
		protected System.Web.UI.WebControls.Label lblSBUID;
		protected System.Web.UI.WebControls.TextBox txtName;
		protected System.Web.UI.WebControls.Button cmdSBUHead;
		protected System.Web.UI.WebControls.Button Button1;
		protected System.Web.UI.WebControls.Button cmdSave;
		protected System.Web.UI.WebControls.DropDownList ddlHOD;
		protected System.Web.UI.WebControls.Button btnChange;
		protected eWorld.UI.CalendarPopup dpTo;
		protected eWorld.UI.CalendarPopup dpFrom;
		protected System.Web.UI.WebControls.CheckBox CheckBox1;
		protected System.Web.UI.WebControls.Label lblSbuHeadID;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator1; 
		public static string sort;

		int WebFormID=1;
		int ProjectID=2;
		protected System.Web.UI.WebControls.Label lblError;
		protected System.Web.UI.WebControls.Label lblHOD;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator1;
		protected System.Web.UI.WebControls.Label Label1;
		protected System.Web.UI.WebControls.DataGrid DataGrid2;
		protected System.Web.UI.HtmlControls.HtmlTableCell Panel1;
		protected System.Web.UI.HtmlControls.HtmlTable Panel12;
		protected System.Web.UI.HtmlControls.HtmlTableCell trError;
		protected System.Web.UI.WebControls.Button Button7;
		protected System.Web.UI.WebControls.Button btnUpdateBUDate;
		protected System.Web.UI.WebControls.Panel pnlEndDateSBU;
		protected eWorld.UI.CalendarPopup dpEndDate;
		protected System.Web.UI.HtmlControls.HtmlTable Panel13;
		protected System.Web.UI.WebControls.Label lblMessage;
		string userId="";

		private bool IsPageAccessAllowed()
		{

			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception ex)
			{
				Response.Redirect("../Login.aspx");
			}
	
			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"View")==true)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("../Login.aspx");
				return false;
			}
		}
		private void Page_Load(object sender, System.EventArgs e)
		{
			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			trError.Visible=false;
			lblError.Text="";
			lblMessage.Text="";
			if(IsPageAccessAllowed())
			{
				con=new SqlConnection(Connection.ConnectionString);
				con.Open();
				//Page.SmartNavigation=true;
			
				if(!IsPostBack)
				{
					cmdSave.Attributes.Add("onclick","if(confirm('Are you sure to change SBU Head?')){return true}else{return false}");
					cmdDelete.Attributes.Add("onclick","if(confirm('Are you sure to delete this SBU?')){return true}else{return false}");
					BindGrid();
					//getHeads(); 
					getHOD();
					Panel1.Visible=true;
					Panel12.Visible=false;

				}

			}
			else
			{
				Response.Redirect("ErrorPage.aspx");
			}

		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.btnAdd.Click += new System.EventHandler(this.btnAdd_Click);
			this.cmdDelete.Click += new System.EventHandler(this.cmdDelete_Click);
			this.cmdCancel.Click += new System.EventHandler(this.cmdCancel_Click);
			this.cmdSBUHead.Click += new System.EventHandler(this.cmdSBUHead_Click);
			this.DataGrid1.EditCommand += new System.Web.UI.WebControls.DataGridCommandEventHandler(this.DataGrid1_EditCommand);
			this.btnChange.Click += new System.EventHandler(this.btnChange_Click);
			this.CheckBox1.CheckedChanged += new System.EventHandler(this.CheckBox1_CheckedChanged);
			this.cmdSave.Click += new System.EventHandler(this.cmdSave_Click);
			this.Button1.Click += new System.EventHandler(this.Button1_Click);
			this.btnUpdateBUDate.Click += new System.EventHandler(this.btnUpdateBUDate_Click);
			this.Button7.Click += new System.EventHandler(this.Button7_Click);
			this.DataGrid2.EditCommand += new System.Web.UI.WebControls.DataGridCommandEventHandler(this.DataGrid2_EditCommand);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion


		private void UpdateEditColoumn()
		{
			foreach( DataGridItem di in DataGrid2.Items )			
			{
				if(di.Cells[4].Text=="No")
				{
					di.Cells[5].Text="";
				}
			}

		}
		public void getHOD()
		{
			ddlHOD.Items.Clear();
			string hdept="select e.pcode,e.name from t_employee e where e.desigid=Any(select d.desigid from t_designation d,t_categorization c where c.isactive=1 And d.status=1 And c.cat_id=category And c.clevel<7And c.supervisory_role=1 And e.del=1)";
			SqlCommand cmd=new SqlCommand(hdept,con);
			SqlDataReader rd=cmd.ExecuteReader();
			ListItem i=new ListItem();
			i.Value="0";
			i.Text="Select--SBU Head";
			ddlHOD.Items.Insert(0,i);
			while(rd.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rd[0].ToString();
				itm.Text=rd[1].ToString();
				ddlHOD.Items.Add(itm); 
			}
			rd.Close();
			
		}

		private void BindGrid()
		{
			//string query="select s.sbuname Name,e.name SBUHead from t_sbu s,t_employee e where isactive='"+1+"' And e.pcode=s.sbuhead";
			string query="select sbuid,sbuname from t_sbu where isActive=1 order by sbuname";
			SqlDataAdapter rd=new SqlDataAdapter(query,con);
			DataSet ds=new DataSet();
			rd.Fill(ds,"SBU");
			DataView dv=new DataView(ds.Tables["SBU"]);
			dv.Sort=sort;
			this.DataGrid1.DataSource=dv;
			this.DataGrid1.DataBind(); 
		}

		private void btnAdd_Click(object sender, System.EventArgs e)
		{
			if (btnAdd.Text=="Add")
			{
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Add"))
				{
					//string queryInsert="insert into t_sbu values('"+this.txtName.Text+"','"+this.dropDownHeads.SelectedValue+"','"+1+"','"+1+"')";
					trError.Visible=false;
					btnAdd.CausesValidation=true;
					btnAdd.Text="Save";
					cmdCancel.Enabled=true;
				}
				else
				{
					trError.Visible=true;
					lblError.Text="You do not have right to add new SBU";
					cmdCancel.Enabled=true;
				}

				
			}
			else if (btnAdd.Text=="Save")
			{
				string verify="select sbuname from t_sbu where isactive=1 And sbuname='"+ this.txtName.Text +"'";
				SqlCommand c=new SqlCommand(verify,con);
				SqlDataReader cr=c.ExecuteReader();
				cr.Read();
				if(cr.HasRows)
				{
					trError.Visible=true;
					lblError.Visible=true; 
					lblError.Text="The name of inserting SBU have already entered";
				}
				else
				{
					cr.Close();
					string queryInsert="Insert into t_sbu(sbuname,isActive) values('" + txtName.Text + "',1)";
					SqlCommand cmd=new SqlCommand(queryInsert,con);
					cmd.ExecuteNonQuery();
					BindGrid();
					ResetForm();

					trError.Visible=true;
					lblError.Visible=true; 
					//lblError.Text="SBU has been inserted successfully";
//					Response.Write("<script language=javascript>alert('SBU has been inserted successfully');</script>");
					lblMessage.Text="SBU has been inserted successfully";
				}
			}
			else if (btnAdd.Text=="Update")
			{
				string verify="select sbuname from t_sbu where isactive=1 And sbuname='"+ this.txtName.Text +"' And sbuid not in("+ lblSBUID.Text +")";
				SqlCommand c=new SqlCommand(verify,con);
				SqlDataReader cr=c.ExecuteReader();
				cr.Read();
				if(cr.HasRows)
				{
					trError.Visible=true;
					lblError.Visible=true;
					lblError.Text="The name of inserting SBU have already entered ";
				}
				else
				{				
					cr.Close();
					string queryUpdate="update t_sbu set sbuname='" + txtName.Text + "' where sbuid="+lblSBUID.Text;
					SqlCommand cmd=new SqlCommand(queryUpdate,con);
					cmd.ExecuteNonQuery();
					BindGrid();
					ResetForm();
					trError.Visible=true;
					lblError.Visible=true; 
					//lblError.Text="Record has been Modified successfully";
//					Response.Write("<script language=javascript>alert('Record has been Modified successfully');</script>");
					lblMessage.Text="Record has been Modified successfully";
				}
			}
		}

		private void DataGrid1_SortCommand(object source, System.Web.UI.WebControls.DataGridSortCommandEventArgs e)
		{
			sort=e.SortExpression.ToString();
			BindGrid(); 
		}

		private void DataGrid1_EditCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{
			lblSBUID.Text=e.Item.Cells[1].Text;
			txtName.Text=e.Item.Cells[2].Text;
			btnAdd.Text="Update";
			cmdDelete.Enabled=true;
			cmdCancel.Enabled=true;
			cmdSBUHead.Enabled=true;
			DataGrid1.Visible=false;
			Panel12.Visible=true;
			Panel1.Visible=false;
			DataGrid2.Visible=true;
			UpdateGrid();
			UpdateEditColoumn();
			btnAdd.Enabled=GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Edit");
			cmdDelete.Enabled=GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Delete");
			if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"SBUHead"))
			{
				DataGrid2.Columns[4].Visible=true;
				btnChange.Visible=true;
				btnChange.Enabled=true;
			}
			else
			{
				DataGrid2.Columns[4].Visible=false;
				btnChange.Visible=false;
				btnChange.Enabled=false;
			}

		}

		private void ResetForm()
		{
			lblSBUID.Text="";
			txtName.Text="";
			cmdDelete.Enabled=false;
			cmdCancel.Enabled=false;
			btnAdd.Enabled=true;
			cmdSBUHead.Enabled=false;
			Panel12.Visible=false;
			btnAdd.Text="Add";
			DataGrid1.Visible=true;

		}
		private void cmdCancel_Click(object sender, System.EventArgs e)
		{
			ResetForm();
			BindGrid();
			btnAdd.CausesValidation=false;
			Panel12.Visible=false;
		}

		private void cmdDelete_Click(object sender, System.EventArgs e)
		{
			try
			{
				string verify="select deptid from t_department where status=1 And sbu='"+lblSBUID.Text+"'";
				SqlCommand c=new SqlCommand(verify,con);
				SqlDataReader cr=c.ExecuteReader();
				cr.Read();
				if(cr.HasRows)
				{
					trError.Visible=true;
					lblError.Visible=true;
					lblError.Text="Department already exist on selected SBU.Please first delete departments";
				}
				else
				{
					cr.Close();
					//SqlCommand cmd=new SqlCommand("delete from t_sbu where sbuid="+lblSBUID.Text,con);
					SqlCommand cmd = new SqlCommand("Update t_sbu set isactive=0 where sbuid="+lblSBUID.Text,con);
					cmd.ExecuteNonQuery();

					SqlCommand cmd2 = new SqlCommand("UPDATE t_sbuheads SET  isActive = 0 " +
						" WHERE (sbuid = " + lblSBUID.Text + ") AND (isActive = 1) ",con);
					
					cmd2.ExecuteNonQuery();

					SqlCommand cmd3 = new SqlCommand("UPDATE t_sbuheads " +
						" SET DateTo = GETDATE() " +
						" WHERE (sbuid = 123) AND (DateTo IS NULL)",con); 

					cmd3.ExecuteNonQuery();

					ResetForm();
					BindGrid();
					lblMessage.Text="SBU Record has been deleted successfull";
					Panel12.Visible=false;
					DataGrid1.Visible=true;
					//Response.Write("<script language=javascript>alert('SBU Record has been deleted successfull');</scipt>");
				}
			}
			catch(SqlException ex)
			{
				Response.Write(ex.Message);
			}

		}

		private void cmdSBUHead_Click(object sender, System.EventArgs e)
		{
			Panel12.Visible=true;
			Panel1.Visible=false;
			cmdSBUHead.Enabled=false;
			UpdateGrid();
		}

		private void Button1_Click(object sender, System.EventArgs e)
		{
			Panel1.Visible=false;
			btnChange.Enabled=true;
			lblSbuHeadID.Text="";
			UpdateEditColoumn();

		}


		private void UpdateGrid()
		{
			
			SqlCommand cmd = new SqlCommand(" SELECT dbo.t_sbuheads.sbuheadid, dbo.t_Employee.name, dbo.t_sbuheads.DateFrom, dbo.t_sbuheads.DateTo,  " +
				" CASE WHEN dbo.t_sbuheads.isActive = 0 THEN 'No' ELSE 'Yes' END AS IsActive " +
				" FROM dbo.t_sbuheads INNER JOIN " +
				" dbo.t_Employee ON dbo.t_sbuheads.pcode = dbo.t_Employee.pcode " +
				" WHERE (dbo.t_sbuheads.sbuid = " + lblSBUID.Text + ") Order by isActive DESC,DateFrom",con);
			SqlDataReader dr=cmd.ExecuteReader();
			DataGrid2.DataKeyField="sbuheadid";
			DataGrid2.DataSource=dr;
			DataGrid2.DataBind();
			dr.Close();
		}


		private void btnChange_Click(object sender, System.EventArgs e)
		{
			Panel1.Visible=true;
			btnChange.Enabled=false;
			UpdateEditColoumn();
			lblHOD.Text="";
			ddlHOD.SelectedIndex=0;
			cmdSave.Text="Save";
			CheckBox1.Checked=false;
			dpTo.Visible=false;

		}

		private void LinkButton1_Click(object sender, System.EventArgs e)
		{
			Panel12.Visible=false;
			cmdSBUHead.Enabled=true;
		}

		private bool IsExistSBUHEad()
		{
			string sql = " SELECT sbuheadid, dateto" +
				" FROM t_sbuheads " +
				" WHERE (isActive = 1) AND (dateto is null) AND (sbuid = " + lblSBUID.Text + ") ";

			SqlDataAdapter	da = new SqlDataAdapter(sql,con);
			DataSet ds = new DataSet();
			da.Fill(ds,"SbuHead");
			if(ds.Tables["SbuHead"].Rows.Count>0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		private void cmdSave_Click(object sender, System.EventArgs e)
		{
			
			if (cmdSave.Text=="Save")
			{
				if (lblSBUID.Text!="" && ddlHOD.SelectedValue !="0")
				{

					if (IsExistSBUHEad())
					{
						pnlEndDateSBU.Visible=true;
						Panel13.Visible=false;
						UpdateEditColoumn();
						return;
					}
				

					//					string SBUID=lblSBUID.Text; //ddlSBU2.SelectedValue.ToString();
					string pcode=ddlHOD.SelectedValue.ToString();
					//					SqlCommand cmd=new SqlCommand("update t_sbuheads set isActive=0 where sbuid="+SBUID,con);
					//					cmd.ExecuteNonQuery();
					//					cmd.Dispose();
					//					cmd=new SqlCommand("update t_sbuheads set dateto=getdate() where sbuid="+SBUID +" and dateto IS NULL",con);
					//					cmd.ExecuteNonQuery();

					SqlCommand cmd2 = new SqlCommand("Update t_sbuheads set isactive=0 where sbuid="+lblSBUID.Text,con);
					cmd2.ExecuteNonQuery();

					
					SqlCommand cmd=new SqlCommand("Insert into t_sbuheads(pcode, sbuid, datefrom, dateto) values(@p_pcode, @p_sbuid, @p_datefrom, @p_dateto)",con);
				
					SqlParameter p_pcode=new SqlParameter("@p_pcode",SqlDbType.Char,10); p_pcode.Value=pcode;
					SqlParameter p_sbuid=new SqlParameter("@p_sbuid",SqlDbType.Int); p_sbuid.Value=lblSBUID.Text;
					SqlParameter p_datefrom=new SqlParameter("@p_datefrom",SqlDbType.SmallDateTime); p_datefrom.Value=dpFrom.SelectedDate.ToShortDateString();

					SqlParameter p_dateto=new SqlParameter("@p_dateto",SqlDbType.SmallDateTime);

					if (CheckBox1.Checked)
					{
						p_dateto.Value=dpTo.SelectedDate.ToShortDateString();
					}
					else
					{
						p_dateto.Value=DBNull.Value;
					}

					cmd.Parameters.Add(p_datefrom);
					cmd.Parameters.Add(p_dateto);
					cmd.Parameters.Add(p_pcode);
					cmd.Parameters.Add(p_sbuid);
					cmd.ExecuteNonQuery();
				
					UpdateGrid();
					btnChange.Enabled=true;
					Panel1.Visible=false;
					UpdateEditColoumn();
					DataGrid2.Visible=true;
					Panel1.Visible=false;
					this.lblHOD.Visible=true;
					//this.lblHOD.Text="Record Insert Successfully";
//					Response.Write("<script language=javascript>alert('Record Insert Successfully');</script>");
					lblMessage.Text="Record Insert Successfully";
				}
				else
				{
					this.lblHOD.Visible=true;
					this.lblHOD.Text="Please Select SBU head";
					UpdateEditColoumn();
				}
			}
			else if (cmdSave.Text=="Update")
			{
				if (lblSBUID.Text!="" && ddlHOD.SelectedValue !="0")
				{
					string dateTo=" NULL ";
					if(CheckBox1.Checked==true)
					{
						dateTo="'"+dpTo.SelectedDate.ToShortDateString()+"'";
					}
					SqlCommand cmd = new SqlCommand("update t_sbuheads  set pcode='" + ddlHOD.SelectedValue.ToString() + "', datefrom='" + dpFrom.SelectedDate.ToShortDateString() + "', dateto=" + dateTo + " where sbuheadid="+lblSbuHeadID.Text,con);
					cmd.ExecuteNonQuery();
					cmdSave.Text="Save";
					UpdateGrid();
					UpdateEditColoumn();
					Panel12.Visible=true;
					Panel1.Visible=false;
					btnChange.Enabled=true;
					DataGrid2.Visible=true;
					this.lblHOD.Visible=true;
					this.lblHOD.Text="Record Update Successfully";
//					Response.Write("<script language=javascript>alert('Record Update Successfully');</script>");
					lblMessage.Text="Record Update Successfully";
				}
				else
				{
					this.lblHOD.Visible=true; 
					this.lblHOD.Text="Please Select SBU head";
					UpdateEditColoumn();
				}
			}

		}

		private void CheckBox1_CheckedChanged(object sender, System.EventArgs e)
		{
			dpTo.Visible=CheckBox1.Checked;
			UpdateEditColoumn();
		}

		private void DataGrid2_EditCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{
			//lblSbuHeadID.Text=DataGrid2.DataKeys[e.Item.ItemIndex].ToString();
			
			
			lblSbuHeadID.Text=e.Item.Cells[0].Text;
			if (e.Item.Cells[4].Text=="Yes")
			{
				SqlCommand cmd = new SqlCommand("select * from t_sbuheads where sbuheadid="+lblSbuHeadID.Text,con);
				SqlDataReader dr= cmd.ExecuteReader();
				dr.Read();
			
				if (dr.HasRows)
				{
					cmdSave.Text="Update";
					dpFrom.SelectedDate=DateTime.Parse(dr["DateFrom"].ToString());
		
					try
					{
						ddlHOD.SelectedValue=dr["pcode"].ToString();
					}
					catch(Exception ec){}
					if (dr["dateto"].ToString()=="")
					{
						CheckBox1.Checked=false;
						dpTo.Visible=false;
					}
					else
					{
						CheckBox1.Checked=true;
						dpTo.SelectedDate=DateTime.Parse(dr["dateTo"].ToString());
						dpTo.Visible=true;
					}
					dr.Close();
					Panel1.Visible=true;
					UpdateEditColoumn();
				}
				else
				{
					dr.Close();
				}


			}
			else
			{
				UpdateEditColoumn();
			}
			
		}

		private void Button7_Click(object sender, System.EventArgs e)
		{
			pnlEndDateSBU.Visible=false;
			UpdateEditColoumn();
			Panel13.Visible=true;
		}

		private void btnUpdateBUDate_Click(object sender, System.EventArgs e)
		{
			string SBUID=lblSBUID.Text; //ddlSBU2.SelectedValue.ToString();
			string pcode=ddlHOD.SelectedValue.ToString();
			SqlCommand cmd=new SqlCommand("update t_sbuheads set isActive=0 where sbuid="+SBUID,con);
			cmd.ExecuteNonQuery();
			cmd.Dispose();

			cmd=new SqlCommand("update t_sbuheads set dateto=@p_dateto where sbuid="+SBUID +" and dateto IS NULL",con);

			SqlParameter p_dateto=new SqlParameter("@p_dateto",SqlDbType.SmallDateTime);
			p_dateto.Value=dpEndDate.SelectedDate.ToShortDateString();
			cmd.Parameters.Add(p_dateto);

			cmd.ExecuteNonQuery();
			pnlEndDateSBU.Visible=false;
			UpdateGrid();
			Panel13.Visible=true;
//			Response.Write("<script language=javascript>alert('SBU Head's record update successfully');</script>");
			lblMessage.Text="SBU Head's record update successfully";
		}

		

	}
}
