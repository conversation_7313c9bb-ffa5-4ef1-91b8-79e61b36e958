using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

namespace GeoRabtaSite.aaina
{
	/// <summary>
	/// Summary description for Verification.
	/// </summary>
	public class Verification : System.Web.UI.Page
	{
		protected System.Web.UI.WebControls.TextBox txtToken;
		protected System.Web.UI.WebControls.Button cmdVerify;
		protected System.Web.UI.WebControls.Label lblMessage;
		protected System.Web.UI.WebControls.Label lblName;
		protected System.Web.UI.WebControls.Label lblPCode;
		protected System.Web.UI.WebControls.Image imgPic;
		protected System.Web.UI.WebControls.Button cmdCheck;
	
		private void Page_Load(object sender, System.EventArgs e)
		{

			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);

			isLogin();

			if (!IsPostBack)
			{
				lblName.Text = "";
				lblMessage.Text = "";
				imgPic.ImageUrl = "";
			}
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.cmdVerify.Click += new System.EventHandler(this.cmdVerify_Click);
			this.cmdCheck.Click += new System.EventHandler(this.cmdCheck_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion


		private string Encode(string pcode)
		{
			pcode = pcode.ToLower();
			string temp = "";
			int data = 0;
			if (pcode.StartsWith("p"))
				pcode = pcode.Replace("p", "1");
			if (pcode.StartsWith("c"))
				pcode = pcode.Replace("c", "2");
			if (pcode.StartsWith("r"))
				pcode = pcode.Replace("r", "3");
			if (pcode.StartsWith("mgt"))
				pcode = pcode.Replace("mgt", "4");
			if (pcode.StartsWith("t"))
				pcode = pcode.Replace("t", "5");
			data = int.Parse(pcode);

			data = data ^ 4571;
			data = data * 2;
			data = data ^ 7657;
			return data.ToString();
		}

		private string Decode(string code)
		{
			int data = 0;
			string temp;
			try
			{
				data = int.Parse(txtToken.Text);
				data = data ^ 7657;
				data = data / 2;
				data = data ^ 4571;
			}
			catch
			{
				return "";
			}
			if (data != 0)
			{
				temp = data.ToString();
				if (temp.StartsWith("1"))
					temp = "p" + temp.Substring(1, temp.Length - 1);

				else if (temp.StartsWith("2"))
					temp = "c" + temp.Substring(1, temp.Length - 1);

				else if (temp.StartsWith("3"))
					temp = "r" + temp.Substring(1, temp.Length - 1);

				else if (temp.StartsWith("4"))
					temp = "mgt" + temp.Substring(1, temp.Length - 1);
            
				else if (temp.StartsWith("5"))
					temp = "h" + temp.Substring(1, temp.Length - 1);

				else if (temp.StartsWith("6"))
					temp = "t" + temp.Substring(1, temp.Length - 1);

				else
					temp = "";
				return temp;
			}
			else
			{
				return "";
			}
		}
		private bool isIssued(string tokenno)
		{
			SqlConnection con = new SqlConnection(Connection.ConnectionStringAaina);
			SqlDataAdapter da = new SqlDataAdapter("spIsVerified", con);
			da.SelectCommand.CommandType = CommandType.StoredProcedure;
			da.SelectCommand.Parameters.Add(new SqlParameter("@tokenno", tokenno));
			DataSet ds = new DataSet();
			da.Fill(ds);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}

		private bool isFilled(string tokenno)
		{
			SqlConnection con = new SqlConnection(Connection.ConnectionStringAaina);
			SqlDataAdapter da = new SqlDataAdapter("SELECT token FROM t_responsemater WHERE     (token = @tokenno)", con);
			da.SelectCommand.Parameters.Add(new SqlParameter("@tokenno", tokenno));
			DataSet ds = new DataSet();
			da.Fill(ds);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}


		private void isLogin()
		{
			try
			{
				string userid = Session["user_id"].ToString();
				if (userid != "")
				{
					if (!GeoSecurity.isControlVisible(5,65,userid,"View Page"))
						Response.Redirect("../login.aspx");
				}
				else
				{
					Response.Redirect("../login.aspx");
				}
			}
			catch (Exception)
			{
				Response.Redirect("../login.aspx");
			}
		}


		private void cmdVerify_Click(object sender, System.EventArgs e)
		{
			if (txtToken.Text != "")
			{
				if (isIssued(txtToken.Text))
				{
					lblPCode.Text = "";
					lblName.Text = "";
					lblMessage.Text = "Give away already issued";
					imgPic.ImageUrl = "";
					return;
				}
				if (!isFilled(txtToken.Text))
				{
					lblPCode.Text = "";
					lblName.Text = "";
					lblMessage.Text = "Not Submited";
					imgPic.ImageUrl = "";
					return;
				}
				string pcode = Decode(txtToken.Text);
				lblPCode.Text = pcode;
				if (pcode != "")
				{
					SqlConnection con = new SqlConnection(Connection.ConnectionString);
					SqlDataAdapter da = new SqlDataAdapter("Select pcode, name from t_employee where pcode=@pcode and del=1", con);
					DataSet ds = new DataSet();
					da.SelectCommand.Parameters.Add(new SqlParameter("@pcode", pcode));
					da.Fill(ds);
					if (ds.Tables[0].Rows.Count > 0)
					{
						lblName.Text = ds.Tables[0].Rows[0]["name"].ToString();
						imgPic.ImageUrl = "../EmpThumbnail/" + ds.Tables[0].Rows[0]["pcode"].ToString().Trim() + ".jpg";
						lblMessage.Text="";
					}
					else
					{
						lblPCode.Text = "";
						lblName.Text = "";
						lblMessage.Text = "";
						imgPic.ImageUrl = "";
						lblMessage.Text="Error Code: 1111";
					}
				}
				else
				{
					lblPCode.Text = "";
					lblName.Text = "";
					lblMessage.Text = "";
					imgPic.ImageUrl = "";
					lblMessage.Text = "Error Code: 2222";
				}
			}
		}

		private void cmdCheck_Click(object sender, System.EventArgs e)
		{
			if (lblName.Text != "")
			{
				SqlConnection con = new SqlConnection(Connection.ConnectionStringAaina);
				con.Open();
				SqlCommand cmd = new SqlCommand("spDoVerify", con);
				cmd.CommandType = CommandType.StoredProcedure;

				cmd.Parameters.Add(new SqlParameter("@pcode", lblPCode.Text));
				cmd.Parameters.Add(new SqlParameter("@VerifiedBy", Session["user_id"].ToString()));

				cmd.ExecuteNonQuery();
				lblPCode.Text = "";
				lblName.Text = "";
				lblMessage.Text = "Give away issued";
				imgPic.ImageUrl = "";
            
			}
		}
	}
}
