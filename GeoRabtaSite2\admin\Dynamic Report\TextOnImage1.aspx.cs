using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Drawing.Imaging;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;
using System.IO;
using System.Text; 


namespace GeoRabtaSite
{
	
	public class TextOnImage1 : System.Web.UI.Page
	{
		Bitmap clipboard=new Bitmap(1,1);
		Graphics graphicImage;
		SqlConnection con; 
		Hashtable levels=new Hashtable();
		int clipwidth=1500;
		int clipheight=1500;
		int boxwidth=200;
		int boxheight=150;
		int gap=120;
		int gaplen=70;
		string requestdesig="";
		int max_no_box=0;
		int tot_cat=0;
		protected System.Web.UI.WebControls.LinkButton LkB1;
		ArrayList hotlist=new ArrayList();
		ArrayList bosseslist=new ArrayList();



		
		private void Page_Load(object sender, System.EventArgs e)
		{  
			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
//			if(IsPageAccessAllowed())
//			{
//			
//
//			}
//			else
//			{
//				Response.Redirect("../notavailable.htm");
//			}

			con=new SqlConnection(Connection.ConnectionString);
		   con.Open();
			if (File.Exists(Session["imagelink"].ToString()))
				File.Delete(Session["imagelink"].ToString());
		   string userId="";
			try
			{userId=Session["user_id"].ToString();}
			catch (Exception ex)
			{
				string s=ex.Message;
				Response.Redirect("../notavailable.htm");
			}
			string query1;  
			SqlCommand cmd=null;
			SqlDataReader dr=null;
			
			if (Request.QueryString.Count>0)
				requestdesig=Request.QueryString["id"];
			else
			{
				query1="SELECT dbo.t_FunctionalDesignation.fdesigid FROM dbo.t_FunctionalDesignation INNER JOIN dbo.t_DesignationHistory ON dbo.t_FunctionalDesignation.fdesigid = dbo.t_DesignationHistory.fdesigid WHERE (dbo.t_DesignationHistory.pcode LIKE '"+ userId +"' and dbo.t_DesignationHistory.isactive=1)";
				cmd=new SqlCommand(query1,con);
				dr=cmd.ExecuteReader();
				dr.Read();
				if (dr.HasRows) 
				{requestdesig=dr[0].ToString();
				Session["srtfdesig"]=dr[0].ToString();
				}
				else
					requestdesig="0"; 
				dr.Close(); 
			}
			

			if (!IsPostBack)
			{
				clipboard= new System.Drawing.Bitmap(clipwidth,clipheight);
				graphicImage = Graphics.FromImage( clipboard );
				graphicImage.Clear(Color.WhiteSmoke);
				graphicImage.SmoothingMode = SmoothingMode.AntiAlias;
				AdjustClipHeight();
				Createlevels();
				CreateClipboard();
				Guid gref= new Guid();
				gref=Guid.NewGuid(); 
				string ImageGUID=gref.ToString();
				//string ImageURL="C:\\"+ImageGUID+".jpg";
				string ImageURL=Server.MapPath(@"..\orgchart\"+ImageGUID+".jpg");
				//Response.ContentType="image/jpeg";
				//clipboard.Save(Response.OutputStream, ImageFormat.Jpeg);
				//Response.Write(ImageURL);
//				Response.End();
				Session["imagelink"]=ImageURL; 
				clipboard.Save(ImageURL, ImageFormat.Jpeg);
				Response.Write(GetMap(ImageGUID,ImageURL,clipboard,hotlist));
                 
				graphicImage.Dispose();
				clipboard.Dispose();
				
			}
		}
		
		public void delpic()
		{
			if (File.Exists(Session["imagelink"].ToString()))
			 File.Delete(Session["imagelink"].ToString());
		}
		public string GetMap(string ImageElementName,string ImageFileName,Bitmap clip,ArrayList hotlists)
		{
			    string sRet="";
			    int x=clip.Width;
				int y=clip.Height;
			    StringBuilder sb = new StringBuilder();
				
			    sb.Append("<img name=\"" + ImageElementName + "\" src=\"" +"../orgchart/"+ ImageElementName+".jpg" + "\" width=\"" + x.ToString());
				sb.Append("\" height=\"" + y.ToString() + "\" border=\"0\" usemap=\"#m_" + ImageElementName + "2\">" + "\n");
				sb.Append("<map name=\"m_" + ImageElementName + "2\">" + "\n");
 
				for(int u=0; u < hotlists.Count; u++)
				{
					HotSpot oLink=(HotSpot)hotlists[u]; 
					string coords= oLink.hotPoint.X.ToString()+","+ oLink.hotPoint.Y.ToString()+","+ (boxheight/2);
					sb.Append(WriteArea(coords,oLink));
				}
				sb.Append("</map>" + "\n");
				sRet = sb.ToString();
				return sRet;
		}
		
		private string WriteArea(string Coords,HotSpot oLink)
		{   string script1 = "alert('"+ oLink.OnMouseOver+"');";
			string sRet="";
			sRet = "<area shape=\"circle\" coords=\"" + Coords + "\"";
			if (oLink.Url.Length > 0) { sRet += " href=\"" + oLink.Url + "\" "; }
			if (oLink.AltTag.Length > 0) { sRet += " alt=\"" + oLink.AltTag + "\""; }
			if (oLink.Target.Length > 0) { sRet += " target=\"" + oLink.Target + "\""; }
			sRet += " onmouseover=imageInfo('"+ oLink.OnMouseOver +"');";
			sRet += ">\n";
			return sRet;
		}

		private void AdjustClipHeight()
		{
			string query1="select CLevel,Color from t_Categorization where isactive=1 order by CLevel ";
			SqlCommand cmd=new SqlCommand(query1,con);
			SqlDataReader dr=cmd.ExecuteReader();
			while(dr.Read())
			{tot_cat=tot_cat+1;}
			dr.Close();
			clipheight=((boxheight+gaplen)*tot_cat);
			clipboard = new Bitmap( clipboard, new Size( clipwidth,clipheight ) );
			graphicImage = Graphics.FromImage( clipboard );
		}

		private void Createlevels()
		{   int strippos=0;
			string query1="select CLevel,Color from t_Categorization where isactive=1 order by CLevel ";
			SqlCommand cmd=new SqlCommand(query1,con);
			SqlDataReader dr=cmd.ExecuteReader();
			while(dr.Read())
			{	graphicImage.DrawImage(CreateCategoryStripes(dr[1].ToString()),new Point( 0,strippos));
				catlevels cat=new catlevels(dr.GetInt32(0),strippos);		   
				int chekval=dr.GetInt32(0);
				levels.Add(dr.GetInt32(0),cat);
				strippos=strippos+(boxheight+gaplen);
			}
			dr.Close();
		}
		
		private Color HexToRGB(string Hex)
		{
			int nRed = Convert.ToInt32(Hex.Substring(1, 2), 16);
			int nGreen = Convert.ToInt32(Hex.Substring(3, 2), 16);
			int nBlue = Convert.ToInt32(Hex.Substring(5, 2), 16);
			return Color.FromArgb(nRed, nGreen, nBlue);
		}

		public Bitmap CreateCategoryStripes(string colorcode ) 
		{
			//Color stripcolor=Color.FromName(colorcode);
			Color stripcolor=HexToRGB(colorcode);
			Bitmap stripe = new Bitmap(1500, boxheight+gaplen );
			Graphics MyGraphics = Graphics.FromImage( stripe );
			MyGraphics.Clear(stripcolor);
			MyGraphics.Flush();
			return( stripe );
		}

		public ArrayList GetBosses(string fdesigid)
		{
		 ArrayList list=new ArrayList();
		 con=new SqlConnection(Connection.ConnectionString);
		 con.Open();
		 string query1="SELECT dbo.t_FunctionalDesignation.fdesigid, dbo.t_Categorization.CLevel, dbo.t_Designation.designation, dbo.t_FunctionalDesignation.functionaltitle"+
				" FROM dbo.t_Categorization INNER JOIN"+
				" dbo.t_Designation ON dbo.t_Categorization.cat_id = dbo.t_Designation.category INNER JOIN"+
				" dbo.t_FunctionalDesignation ON dbo.t_Designation.desigid = dbo.t_FunctionalDesignation.functionaldesignation"+
				" WHERE (dbo.t_FunctionalDesignation.fdesigid IN (SELECT sfdesigid FROM dbo.t_DirectReporting"+
				" WHERE (isactive = 1) AND (fdesigid = '"+ fdesigid +"'))) AND (dbo.t_FunctionalDesignation.isactive = 1) AND (dbo.t_Designation.status = 1) AND (dbo.t_Categorization.isactive = 1)";
			SqlCommand cmd=new SqlCommand(query1,con);
			SqlDataReader dr2=cmd.ExecuteReader();
			while(dr2.Read())
			{filldesignationdata(dr2[0].ToString(),5);}
			dr2.Close(); 
		/*string Query="SELECT dbo.t_FunctionalDesignation.fdesigid, dbo.t_Categorization.CLevel, dbo.t_Designation.designation, dbo.t_FunctionalDesignation.functionaltitle FROM dbo.t_Categorization INNER JOIN dbo.t_Designation ON dbo.t_Categorization.cat_id = dbo.t_Designation.category INNER JOIN dbo.t_FunctionalDesignation ON dbo.t_Designation.desigid = dbo.t_FunctionalDesignation.functionaldesignation WHERE (dbo.t_FunctionalDesignation.fdesigid IN (SELECT sfdesigid FROM dbo.t_DirectReporting WHERE (isactive = 1) AND (fdesigid = "+fdesigid+"))) AND (dbo.t_FunctionalDesignation.isactive = 1) AND (dbo.t_Designation.status = 1) AND (dbo.t_Categorization.isactive = 1)";
		 SqlCommand cmd=new SqlCommand(Query,con);
		 SqlDataReader rd=cmd.ExecuteReader();
			int i=0;
			while(rd.Read())
			{
			 filldesignationdata(rd[0].ToString(),3);
			 //list.Insert(i,rd[0].ToString());
			 //i++;
			}
		 rd.Close();
		 con.Close();*/
		 return list;
		}
		private void CreateClipboard()
		{   
			filldesignationdata(requestdesig,0);
            // query for direct juniors
			string query1="SELECT fdesigid FROM t_DirectReporting WHERE isactive = 1 AND sfdesigid ='"+ requestdesig +"'";
			SqlCommand cmd=new SqlCommand(query1,con);
			SqlDataReader dr2=cmd.ExecuteReader();
			while(dr2.Read())
			{filldesignationdata(dr2[0].ToString(),2);}
			dr2.Close();
			//query for dotted boss
			query1="SELECT sfdesigid FROM t_DottedLineReporting WHERE isactive = 1 AND fdesigid ='"+ requestdesig +"'";
			cmd=new SqlCommand(query1,con);
			SqlDataReader dr3=cmd.ExecuteReader();
			while(dr3.Read())
			{ filldesignationdata(dr3[0].ToString(),3);}
			dr3.Close();
			//query for dotted juniors
			query1="SELECT fdesigid FROM t_DottedLineReporting WHERE isactive = 1 AND sfdesigid = '"+ requestdesig +"'";
			cmd=new SqlCommand(query1,con);
			SqlDataReader dr4=cmd.ExecuteReader();
			while(dr4.Read())
			{filldesignationdata(dr4[0].ToString(),4);}
			dr4.Close();
			
			//query for direct boss
			//query1="SELECT sfdesigid FROM T_directreporting WHERE isactive = 1 AND fdesigid ='"+ requestdesig +"'";
		    string Boss="";
			query1="SELECT dbo.t_FunctionalDesignation.fdesigid, dbo.t_Categorization.CLevel, dbo.t_Designation.designation, dbo.t_FunctionalDesignation.functionaltitle"+
            " FROM dbo.t_Categorization INNER JOIN"+
            " dbo.t_Designation ON dbo.t_Categorization.cat_id = dbo.t_Designation.category INNER JOIN"+
            " dbo.t_FunctionalDesignation ON dbo.t_Designation.desigid = dbo.t_FunctionalDesignation.functionaldesignation"+
			" WHERE (dbo.t_FunctionalDesignation.fdesigid IN (SELECT sfdesigid FROM dbo.t_DirectReporting"+
            " WHERE (isactive = 1) AND (fdesigid = '"+ requestdesig +"'))) AND (dbo.t_FunctionalDesignation.isactive = 1) AND (dbo.t_Designation.status = 1) AND (dbo.t_Categorization.isactive = 1)";
			cmd=new SqlCommand(query1,con);
			SqlDataReader dr1=cmd.ExecuteReader();
			while(dr1.Read())
			{
				Boss=dr1[0].ToString();
				filldesignationdata(dr1[0].ToString(),1);
				SqlConnection subcon=new SqlConnection(Connection.ConnectionString); 
				subcon.Open();
				string subquery1="SELECT fdesigid FROM t_DirectReporting WHERE isactive = 1 AND sfdesigid ='"+ dr1[0].ToString() +"'";
				SqlCommand subcmd=new SqlCommand(subquery1,subcon);
				SqlDataReader subdr2=subcmd.ExecuteReader();
				int subcount=0;
				while(subdr2.Read())
				{
					int quecount=0;
					SqlConnection quecon=new SqlConnection(Connection.ConnectionString); 
					quecon.Open();
					string que1="SELECT dbo.t_FunctionalDesignation.fdesigid, dbo.t_DesignationHistory.pcode, dbo.t_Employee.name FROM dbo.t_Employee INNER JOIN dbo.t_DesignationHistory INNER JOIN dbo.t_FunctionalDesignation ON dbo.t_DesignationHistory.fdesigid = dbo.t_FunctionalDesignation.fdesigid ON dbo.t_Employee.pcode = dbo.t_DesignationHistory.pcode WHERE (dbo.t_FunctionalDesignation.fdesigid = "+ subdr2[0].ToString() +") AND (dbo.t_DesignationHistory.isactive = 1) AND (dbo.t_FunctionalDesignation.isactive = 1) AND (dbo.t_Employee.del = 1)";
					SqlCommand quecmd=new SqlCommand(que1,quecon);
					SqlDataReader quedr=quecmd.ExecuteReader();
					while(quedr.Read())
					{ quecount=quecount+1;}
					quedr.Close();
					quecon.Close();
					if (quecount==0)
					{subcount=subcount+1;}
					else if (quecount>0)
					{subcount=subcount+quecount;}

				}
				subdr2.Close();
				subcon.Close();
				/*if (subcount>1)
				{
					Bitmap bg=new Bitmap(1,1);	
					bg=CreateDesignationBox1("Team Detail\nDirect Repotees="+subcount,"Dotted Repotees=","","","","",dr1[2].ToString(),dr1[3].ToString(),1) ; 
					employeebox empdata=new employeebox(dr1.GetInt32(0),"","",5);
					empdata.pcodeid="nill";
					empdata.createpicbox(bg);
					empdata.setlevel(dr1.GetInt32(1));
					object empobj=levels[dr1.GetInt32(1)];
					catlevels lev=(catlevels)empobj;
					lev.setboxes();
					lev.createemp(empdata);
				}*/
			}
			dr1.Close();
			//GetBosses("183");
//				SqlConnection subcon=new SqlConnection(Connection.ConnectionString);
//				subcon.Open();
//				string subquery1="SELECT sfdesigid FROM T_directreporting WHERE isactive = 1 AND fdesigid ='"+ dr1[0].ToString() +"'";
//				SqlCommand subcmd=new SqlCommand(subquery1,subcon);
//				SqlDataReader subdr1=subcmd.ExecuteReader();
//				
//				while(subdr1.Read())
//				{				
//					filldesignationdata(subdr1[0].ToString(),5);
//				}
//				subdr1.Close();
//				subcon.Close();
			
			
			
			
			//delete employeebox for bosseslist if have already created
//			for(int u=0; u < bosseslist.Count; u++)
//			{
//				employeebox ebx=(employeebox)bosseslist[u];
//				object empobj=levels[ebx.level_emp];
//				catlevels lev=(catlevels)empobj;
//				int totalemp=lev.getno_of_boxes();  
//				Hashtable empdata=lev.getemployees();
//				if (totalemp>0)
//				{
//						for(int i=1;i<=totalemp;i++)
//						{ employeebox ebox=(employeebox)empdata[i]; 
//							if (ebox.pcodeid==ebx.pcodeid && ebox.fdesig==ebx.fdesig && ebox.doot_dash!=5)
//							{
//							  ebox.havechild=1;
//							   bosseslist.Add(ebox); 
//							  
//								Response.Write("in me");
//							  
//								bosseslist.Remove(ebx);  
//								object empobj1=levels[ebx.level_emp];
//								catlevels lev1=(catlevels)empobj1;
//								lev1.boxes.Remove(1);
//								lev.no_of_boxes=lev.no_of_boxes-1;
//
//							}
//						}
//				 }
//			}
			 
			
			
			
			IDictionaryEnumerator e;
			e=levels.GetEnumerator();
						
			//////////////////////////Adjusment of viewable Screen////////////////////
			while(e.MoveNext())
			{   catlevels lev=(catlevels)e.Value;
				int key=(int)e.Key;
				int clkey=lev.levelid;
				int totalemp=lev.getno_of_boxes();
				if (max_no_box < totalemp)
					max_no_box=totalemp;

			}
			clipwidth=((boxwidth+gap)* max_no_box)+gap;
			clipboard = new Bitmap( clipboard, new Size( clipwidth,clipheight ) );
			graphicImage = Graphics.FromImage( clipboard );
			////////////////////////////End of Adjusment//////////////////
			e.Reset();
//			////////////////////// Creating Corrdinates on Screen///////////////
			

			while(e.MoveNext())
			{
		        catlevels lev=(catlevels)e.Value;
				int yaxis=lev.getpoints()+(boxheight+gaplen);
				int xaxis=gap/2;
				for(int i=0;i<=max_no_box;i++)
				{
					if (i!=0)
					{
						lev.insertpoints(i,new Point(xaxis,yaxis)); 
					}
					xaxis=xaxis+(boxwidth+(gap));
					
				}

			}
			///////////////////////////end of cordinates//////////////
			e.Reset();
			/////////////////////////Placing Employee box on Screen//////////////
			while(e.MoveNext())
			{   
		        catlevels lev=(catlevels)e.Value;
				int catpos=lev.getpoints();
				int totalemp=lev.getno_of_boxes();
			
				Hashtable empdata=lev.getemployees();
				if (totalemp>0)
				{   int bakwas=gap;
					
					for(int i=1;i<=totalemp;i++)
					{
						Point TL=new Point( bakwas,catpos+(gaplen/2));
						Point TR=new Point(TL.X+boxwidth,TL.Y);
						Point BL=new Point(TL.X,TL.Y+boxheight);
						Point BR=new Point(TR.X,BL.Y);
						Point TM=new Point(TL.X+(boxwidth/2),TL.Y);
						Point BM=new Point(BL.X+(boxwidth/2),BL.Y);
						Point LM=new Point(TL.X,TL.Y+(boxheight/2));
						Point RM=new Point(TR.X,TR.Y+(boxheight/2));
						employeebox ebox=(employeebox)empdata[i];
						ebox.createdimension(TL,TR,BL,BR,TM,BM,LM,RM);
						graphicImage.DrawImage(ebox.picbox,TL);				
						bakwas=bakwas+(boxwidth+gap);
			
					}
				}
			}
			/////////////////////end of employee box on Screen/////////////
			e.Reset();

			Hashtable strpoint1=new Hashtable();
			int req_key=1;
			while(e.MoveNext())
			{
				catlevels lev=(catlevels)e.Value;
				int totalemp=lev.getno_of_boxes();
				Hashtable empdata=lev.getemployees();
				if (totalemp!=0)
				{   
					for(int i=1;i<=totalemp;i++)
					{   
						employeebox ebox=(employeebox)empdata[i];
						if (ebox.doot_dash==0)
						{   requestor req=new requestor(i,ebox.bottommiddle,ebox.topmiddle,ebox.rightmiddle,ebox.leftmiddle,(int)e.Key);
							strpoint1.Add(req_key,req);
							req_key=req_key+1;
						}
					}
				}
			}
			
			IDictionaryEnumerator e1;
			e1=strpoint1.GetEnumerator(); 
			Point poto=new Point(1,1);
			Point poto1=new Point(1,1);
			int chksome=1;
			Pen blackpen01=new Pen(Color.Salmon,5);
			blackpen01.EndCap=LineCap.ArrowAnchor;
			blackpen01.StartCap=LineCap.ArrowAnchor;
			while(e1.MoveNext())
			{
			   	requestor roq1=(requestor)e1.Value;
				if (chksome!=1)
				   graphicImage.DrawLine(blackpen01,poto1,roq1.rightmiddle);  
				poto1=roq1.leftmiddle;
			   chksome=chksome+1;	

			}
            e.Reset(); 
			while(e.MoveNext())
			{
				catlevels lev=(catlevels)e.Value;
				int totalemp=lev.getno_of_boxes();
				Hashtable empdata=lev.getemployees();
				if (totalemp!=0)
				{   
					for(int i=1;i<=totalemp;i++)
					{   
						employeebox ebox=(employeebox)empdata[i];
						
						if (ebox.doot_dash==1)
						{   requestor roq=(requestor)strpoint1[1];
						    
							catlevels lev1=(catlevels)levels[roq.levelid];
							Point POC =(Point)lev1.levelpoints[i];
							Point POconnection = new Point(POC.X,POC.Y-(boxheight+gaplen));
							Point startconn=new Point(ebox.rightmiddle.X+(gap/2),ebox.rightmiddle.Y);
							Point conn_to_box=new Point(roq.reqtop.X,roq.reqtop.Y-(gaplen/2));
							Pen blackpen1=new Pen(Color.DarkGray,5);
							blackpen1.EndCap=LineCap.ArrowAnchor;
							graphicImage.DrawLine(blackpen1,conn_to_box,roq.reqtop);
							blackpen1.EndCap=LineCap.NoAnchor;
							blackpen1.StartCap=LineCap.ArrowAnchor;
							graphicImage.DrawLine(blackpen1,startconn,ebox.rightmiddle);
							blackpen1.EndCap=LineCap.NoAnchor;
							blackpen1.StartCap=LineCap.NoAnchor;
							graphicImage.DrawLine(blackpen1 ,startconn,POconnection);
							graphicImage.DrawLine(blackpen1 ,conn_to_box,POconnection);
//							string coordinat=ebox.topleft.X.ToString()+","+ebox.topleft.X.ToString()+","+boxwidth.ToString()+","+boxheight.ToString();
							
							HotSpot hs=new HotSpot(ebox.fdesig.ToString(),"TextOnImage1.aspx?id="+ebox.fdesig.ToString(),"view OrganoGram","_self",ebox.fdesig.ToString(),new Point(ebox.bottommiddle.X,ebox.bottommiddle.Y-(boxheight/2)));
							hotlist.Add(hs);
							
						} 
						if (ebox.doot_dash==2)
						{
							requestor roq=(requestor)strpoint1[1];
							catlevels lev1=(catlevels)levels[roq.levelid];
							Point POconnection =(Point)lev1.levelpoints[i];
							Point startconn=new Point(ebox.rightmiddle.X+(gap/2),ebox.rightmiddle.Y);
							Point conn_to_box=new Point(roq.reqbottom.X,roq.reqbottom.Y+(gaplen/2));
							Pen blackpen1=new Pen(Color.Black,5);
							//blackpen1.StartCap = LineCap.ArrowAnchor;
							blackpen1.EndCap=LineCap.ArrowAnchor;
							graphicImage.DrawLine(blackpen1,roq.reqbottom,conn_to_box);
							blackpen1.EndCap=LineCap.NoAnchor;
							blackpen1.StartCap=LineCap.ArrowAnchor;
							graphicImage.DrawLine(blackpen1,ebox.rightmiddle,startconn);
							blackpen1.EndCap=LineCap.NoAnchor;
							blackpen1.StartCap=LineCap.NoAnchor;
							graphicImage.DrawLine(blackpen1 ,startconn,POconnection);
                            graphicImage.DrawLine(blackpen1 ,conn_to_box,POconnection);
//							string coordinat=ebox.topleft.X.ToString()+","+ebox.topleft.X.ToString()+","+boxwidth.ToString()+","+boxheight.ToString();
							HotSpot hs=new HotSpot(ebox.fdesig.ToString(),"TextOnImage1.aspx?id="+ebox.fdesig.ToString(),"view OrganoGram","_self",ebox.fdesig.ToString(),new Point(ebox.bottommiddle.X,ebox.bottommiddle.Y-(boxheight/2)));
							hotlist.Add(hs); 
						} 
						
						if (ebox.doot_dash==3)
						{   
							requestor roq=(requestor)strpoint1[1];
							catlevels lev1=(catlevels)levels[roq.levelid];
							Point POC =(Point)lev1.levelpoints[i];
							Point startconn=new Point(ebox.rightmiddle.X+(gap/3),ebox.rightmiddle.Y);
							Point POconnection = new Point(startconn.X,POC.Y-(boxheight+(gaplen+(gaplen/4))));
							Point conn_to_box=new Point(roq.reqtop.X-(boxwidth/4),POconnection.Y);
							Pen blackpen1=new Pen(Color.DarkGray,5);
							blackpen1.DashStyle = DashStyle.Dot;
							blackpen1.DashOffset = 40;
							blackpen1.DashCap = DashCap.Round;
							blackpen1.EndCap=LineCap.ArrowAnchor;
							graphicImage.DrawLine(blackpen1,conn_to_box,new Point(conn_to_box.X,roq.reqtop.Y));
							blackpen1.EndCap=LineCap.NoAnchor;
							blackpen1.StartCap=LineCap.ArrowAnchor;
							graphicImage.DrawLine(blackpen1,startconn,ebox.rightmiddle);
							blackpen1.EndCap=LineCap.NoAnchor;
							blackpen1.StartCap=LineCap.NoAnchor;
							graphicImage.DrawLine(blackpen1 ,startconn,POconnection);
							graphicImage.DrawLine(blackpen1 ,conn_to_box,POconnection);
							
						} 

						if (ebox.doot_dash==4)
						{   
							requestor roq=(requestor)strpoint1[1];
							catlevels lev1=(catlevels)levels[roq.levelid];
							Point POC =(Point)lev1.levelpoints[i];
							Point startconn=new Point(ebox.rightmiddle.X+(gap/3),ebox.rightmiddle.Y);
							Point POconnection = new Point(startconn.X,POC.Y+(gaplen/4));
							Point conn_to_box=new Point(roq.reqbottom.X-(boxwidth/4),POconnection.Y);
							Pen blackpen1=new Pen(Color.Black,5);
							blackpen1.DashStyle = DashStyle.Dot;
							blackpen1.DashOffset = 40;
							blackpen1.DashCap = DashCap.Round;
							blackpen1.EndCap=LineCap.ArrowAnchor;
							graphicImage.DrawLine(blackpen1,new Point(roq.reqbottom.X-(boxwidth/4),roq.reqbottom.Y),conn_to_box);
							blackpen1.EndCap=LineCap.NoAnchor;
							blackpen1.StartCap=LineCap.ArrowAnchor;
							graphicImage.DrawLine(blackpen1,ebox.rightmiddle,startconn);
							blackpen1.EndCap=LineCap.NoAnchor;
							blackpen1.StartCap=LineCap.NoAnchor;
							graphicImage.DrawLine(blackpen1 ,startconn,POconnection);
							graphicImage.DrawLine(blackpen1 ,conn_to_box,POconnection);
						}
						if (ebox.doot_dash==5)
						{   
							requestor roq=(requestor)strpoint1[1];
							catlevels lev1=(catlevels)levels[roq.levelid];
							Point POC =(Point)lev1.levelpoints[i];
							Point startconn=new Point(ebox.rightmiddle.X+(gap/3),ebox.rightmiddle.Y);
							Point POconnection = new Point(startconn.X,POC.Y-(boxheight+(gaplen+(gaplen/4))));
							Point conn_to_box=new Point(roq.reqtop.X-(boxwidth/4),POconnection.Y);
							Pen blackpen1=new Pen(Color.DarkGray,5);
							blackpen1.DashStyle = DashStyle.Dot;
							blackpen1.DashOffset = 40;
							blackpen1.DashCap = DashCap.Round;
							blackpen1.EndCap=LineCap.ArrowAnchor;
							graphicImage.DrawLine(blackpen1,conn_to_box,new Point(conn_to_box.X,roq.reqtop.Y));
							blackpen1.EndCap=LineCap.NoAnchor;
							blackpen1.StartCap=LineCap.ArrowAnchor;
							graphicImage.DrawLine(blackpen1,startconn,ebox.rightmiddle);
							blackpen1.EndCap=LineCap.NoAnchor;
							blackpen1.StartCap=LineCap.NoAnchor;
							graphicImage.DrawLine(blackpen1 ,startconn,POconnection);
							graphicImage.DrawLine(blackpen1 ,conn_to_box,POconnection);
							
						} 
					}
				}
			}
        	
		}
		
		private void filldesignationdata(string fdesig,int dot_dash)
		{   SqlConnection con1=new SqlConnection(Connection.ConnectionString);
		    con1.Open();
		    string query1="SELECT dbo.t_Categorization.CLevel, dbo.t_FunctionalDesignation.fdesigid, dbo.t_FunctionalDesignation.functionaltitle, dbo.t_Designation.designation, dbo.t_City.cityname, dbo.t_FunctionalDesignation.noofvacancies, dbo.t_Department.deptname FROM dbo.t_Categorization INNER JOIN dbo.t_Designation ON dbo.t_Categorization.cat_id = dbo.t_Designation.category INNER JOIN dbo.t_FunctionalDesignation ON dbo.t_Designation.desigid = dbo.t_FunctionalDesignation.functionaldesignation INNER JOIN dbo.t_Department ON dbo.t_Designation.deptid = dbo.t_Department.deptid INNER JOIN dbo.t_City ON dbo.t_FunctionalDesignation.station = dbo.t_City.cityid WHERE (dbo.t_FunctionalDesignation.fdesigid = "+fdesig+") AND (dbo.t_Department.status = 1) AND (dbo.t_FunctionalDesignation.isactive = 1) AND (dbo.t_Designation.status = 1) AND (dbo.t_Categorization.isactive = 1) AND (dbo.t_City.status = 1)";
			SqlCommand cmd=new SqlCommand(query1,con1);
		   SqlDataReader dr5=cmd.ExecuteReader();
           dr5.Read();
           
			if (dr5.HasRows) 
			{   GetEmployeesOnFdesig(dr5.GetInt32(1),dr5[2].ToString(),dr5[3].ToString(),dr5[4].ToString(),dr5.GetInt32(5),dr5[6].ToString(),dr5.GetInt32(0),dot_dash);
			}
		   dr5.Close();
			
		}

		
		private void GetEmployeesOnFdesig(int fdesigid,string fdesigtitle,string designation,string station,int novac,string dept,int levl,int dot_dash)
		{    
			object empobj=levels[levl];
			catlevels lev=(catlevels)empobj;
			int empcount=0;
			SqlConnection con1=new SqlConnection(Connection.ConnectionString);
			con1.Open();
			string query1="SELECT dbo.t_FunctionalDesignation.fdesigid, dbo.t_DesignationHistory.pcode, dbo.t_Employee.name, dbo.t_Employee.email_official,dbo.t_Employee.pic FROM dbo.t_Employee INNER JOIN dbo.t_DesignationHistory INNER JOIN dbo.t_FunctionalDesignation ON dbo.t_DesignationHistory.fdesigid = dbo.t_FunctionalDesignation.fdesigid ON dbo.t_Employee.pcode = dbo.t_DesignationHistory.pcode WHERE (dbo.t_FunctionalDesignation.fdesigid = "+ fdesigid +") AND (dbo.t_DesignationHistory.isactive = 1) AND (dbo.t_FunctionalDesignation.isactive = 1) AND (dbo.t_Employee.del = 1)";
			SqlCommand cmd=new SqlCommand(query1,con1);
			SqlDataReader dr=cmd.ExecuteReader();
			while(dr.Read())
			{   empcount=empcount+1;
				Bitmap bg=new Bitmap(1,1);	
                bg=CreateDesignationBox1(dr[1].ToString(),dr[2].ToString(),dr[3].ToString(),dr[4].ToString(),dept,"Station:"+station,designation,fdesigtitle,0) ; 
			    employeebox empdata=new employeebox(fdesigid,designation,fdesigtitle,dot_dash);
                empdata.pcodeid=dr[1].ToString();
				empdata.name=dr[2].ToString();
				
				empdata.createpicbox(bg);
				empdata.setlevel(levl);
				lev.setboxes();
				lev.createemp(empdata);
				if (dot_dash==5)
				{   empdata.havechild=1;
					bosseslist.Add(empdata);}
				
				
			}
			if (empcount==0)  
			{
				Bitmap bg=new Bitmap(1,1);	
				bg=CreateDesignationBox1("Total Vaccencies="+novac,"Occupied=No Employees","","",dept,station,designation,fdesigtitle,1) ; 
				employeebox empdata=new employeebox(fdesigid,designation,fdesigtitle,dot_dash);
				empdata.pcodeid="nill";
				
				empdata.createpicbox(bg);
				empdata.setlevel(levl);
				lev.setboxes();
				lev.createemp(empdata);
				if (dot_dash==5)
				{   empdata.havechild=1;
					bosseslist.Add(empdata);}
			}
			//============================Display Vacant Positions=========================//
			if(empcount>0)
			{
				int rem=novac-empcount;
				if(rem>0)
				{
					//for(int i=0;i<rem;i++)
				{
					Bitmap bg=new Bitmap(1,1);	
					bg=CreateDesignationBox1("Total Vaccencies= "+rem.ToString(),"Occupied=No Employees","","",dept,station,designation,fdesigtitle,1) ; 
					employeebox empdata=new employeebox(fdesigid,designation,fdesigtitle,dot_dash);
					empdata.pcodeid="";
					empdata.createpicbox(bg);
					empdata.setlevel(levl);
					lev.setboxes();
					lev.createemp(empdata);
				}
				}
			}
			//=============================================================================//
              
            dr.Close(); 
			
	 	}
		public Bitmap CreateDesignationBox1(string pcode,string name,string emailofficial,string pic,string dept,string station,string designation,string ftitle,int fillstatus) 
		{     
		
			Bitmap bmpImage = new Bitmap( boxwidth, boxheight );
			Bitmap picemp=new System.Drawing.Bitmap( Server.MapPath("../employee/none.jpg")  );
			if (File.Exists( Server.MapPath("../employee/"+pic)) )
			   {picemp  = new System.Drawing.Bitmap( Server.MapPath("../employee/"+pic)  );}
			Graphics MyGraphics = Graphics.FromImage( bmpImage );
			MyGraphics = Graphics.FromImage( bmpImage );
			MyGraphics.Clear( Color.White );
			MyGraphics.TextRenderingHint = TextRenderingHint.AntiAlias;
			FontFamily arialFamily = new FontFamily("Arial");
			Font arialFont = new Font( arialFamily, 7,FontStyle.Underline|FontStyle.Bold);
			Font arialFont1 = new Font( arialFamily, 6,FontStyle.Bold);
			Rectangle rect1 = new Rectangle(1, 1, boxwidth-4,boxheight-4);
			Rectangle rect2 = new Rectangle(1, 3, boxwidth-4, 35);
			//Size sz=new Size((boxwidth/2)-4,90);
			Size sz=new Size(70,90);
			Size sz123=new Size(120,90);
			Rectangle rect4=new Rectangle(new Point(3,53),sz);
			//Rectangle rect3 = new Rectangle(new Point((boxwidth/2),53), sz123);
			Rectangle rect3 = new Rectangle(new Point(75,53), sz123);
			StringFormat strFormat2 = new StringFormat();
			strFormat2.Alignment = StringAlignment.Center;
			strFormat2.LineAlignment = StringAlignment.Near;
			strFormat2.Trimming = StringTrimming.Character;
			StringFormat strFormat3 = new StringFormat();
			strFormat3.Alignment = StringAlignment.Near;
			strFormat3.LineAlignment = StringAlignment.Near;
			strFormat3.Trimming = StringTrimming.Character;
			
			MyGraphics.DrawRectangle( new Pen(Color.White), rect1);
			MyGraphics.DrawString(designation+"\n"+ftitle, arialFont,new SolidBrush(Color.Black), rect2, strFormat2);
			if (fillstatus==0)
			{
				MyGraphics.DrawImage(picemp,rect4);
				MyGraphics.DrawString(pcode+"\n"+name+"\n"+emailofficial+"\n"+dept+"\n"+station , arialFont1,new SolidBrush(Color.Black), rect3, strFormat3);
			}
			else if (fillstatus==1)
			{
				rect3 = new Rectangle(new Point(50,53), sz123);
				MyGraphics.DrawString(pcode+"\n"+name+"\n"+emailofficial+"\n"+dept+"\n"+station , arialFont1,new SolidBrush(Color.Black), rect3, strFormat3);
			}
			
			arialFont.Dispose();
			arialFont1.Dispose();
			MyGraphics.Flush();
			return( bmpImage );
		}



			#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.LkB1.Click += new System.EventHandler(this.LkB1_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		private void LkB1_Click(object sender, System.EventArgs e)
		{
			Response.Redirect("textonimage1.aspx?id="+Session["srtfdesig"].ToString());
		}
	}

	
	
}
