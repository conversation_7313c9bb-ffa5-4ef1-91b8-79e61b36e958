using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

namespace GeoRabtaSite.admin
{
	/// <summary>
	/// Summary description for GeoTasaweer.
	/// </summary>
	public class GeoTasaweer : System.Web.UI.Page
	{
		protected System.Web.UI.WebControls.Label lblAlbumID;
		protected System.Web.UI.WebControls.DataGrid dgAlbums;
		protected System.Web.UI.WebControls.DataGrid DataGrid1;
		protected System.Web.UI.WebControls.LinkButton cmdBack;
		protected System.Web.UI.WebControls.Button cmdUpdate;
		protected SqlConnection conn;
		string userId="";	
		const int WebFormID=15;
		protected System.Web.UI.HtmlControls.HtmlTable pnlAlbum;
		protected System.Web.UI.WebControls.Label lblCreatedBy;
		protected System.Web.UI.WebControls.Label lblDesignation;
		protected System.Web.UI.WebControls.Label lblDepartment;
		protected System.Web.UI.HtmlControls.HtmlTable pnlPhotos;
		protected System.Web.UI.WebControls.Label lblAlbumName;
		protected System.Web.UI.WebControls.DropDownList ddlDepartment;
		protected System.Web.UI.WebControls.Label albumSort;
		protected System.Web.UI.WebControls.Label photoSort;
		protected System.Web.UI.WebControls.Label lblPCode;
		const int ProjectID=2;

		private bool _refreshState;
		private bool _isRefresh;

		public bool IsRefresh
		{
			get
			{
				return _isRefresh;
			}
		}

		protected override void LoadViewState(object savedState)
		{
			object[] allStates = (object[]) savedState;
			base.LoadViewState(allStates[0]);
			_refreshState = (bool) allStates[1];
			try
			{
				_isRefresh = _refreshState == (bool) Session["__ISREFRESH"];
			}
			catch(Exception)
			{
				Response.Redirect("../Login.aspx");
			}

		}

		protected override object SaveViewState()
		{
			Session["__ISREFRESH"] = _refreshState;
			object[] allStates = new object[2];
			allStates[0] = base.SaveViewState();
			allStates[1] = !_refreshState;
			return allStates;
		}

	
		private void FillDepartmentsDDL()
		{
			//SqlCommand cmd = new SqlCommand("select deptid, deptname from t_department where status=1 order by deptname",conn);
			SqlCommand cmd = new SqlCommand("SELECT DISTINCT dbo.t_Department.deptid, dbo.t_Department.deptname " +
				" FROM         dbo.t_Designation INNER JOIN " +
				" dbo.t_Employee ON dbo.t_Designation.desigid = dbo.t_Employee.desigid INNER JOIN " +
				" dbo.t_Department ON dbo.t_Designation.deptid = dbo.t_Department.deptid INNER JOIN " +
				" dbo.t_album ON dbo.t_Employee.pcode = dbo.t_album.createdby " +
				" WHERE     (dbo.t_album.albumid IN " +
				" (SELECT DISTINCT dbo.t_album.albumid " +
				" FROM          dbo.t_album INNER JOIN " +
				" dbo.t_photos ON dbo.t_album.albumid = dbo.t_photos.albumid " +
				" WHERE      (dbo.t_photos.flag = 0))) AND (dbo.t_Department.status = 1) AND (dbo.t_Designation.status = 1) AND (dbo.t_Employee.del = 1)",conn);
			SqlDataReader dr=cmd.ExecuteReader();
			ddlDepartment.Items.Clear();
			ddlDepartment.Items.Add("ALL DEPARTMENTS");
			while(dr.Read())
			{
				ddlDepartment.Items.Add(new ListItem(dr[1].ToString(),dr[0].ToString()));
			}
			dr.Close();
		}


		private bool IsPageAccessAllowed()
		{
			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception ex)
			{
				string s = ex.Message;
				Response.Redirect("../Login.aspx");
			}

			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Approve")==true)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("../Login.aspx");
				return false;
			}
		}
 
		public void CheckAccept(object sender, System.EventArgs e)
		{
			foreach(DataGridItem di in DataGrid1.Items)
			{
				CheckBox cbAccept = (CheckBox)di.FindControl("chkFlag");
				CheckBox cbReject = (CheckBox)di.FindControl("cbReject");
				TextBox txtRemarks = (TextBox)di.FindControl("txtRemarks");
				if(cbAccept.Checked)
				{
					cbReject.Checked=false;
					txtRemarks.Text="";
					txtRemarks.Visible=false;
				}
			}
		}

		public void CheckReject(object sender, System.EventArgs e)
		{
			foreach(DataGridItem di in DataGrid1.Items)
			{
				CheckBox cbAccept = (CheckBox)di.FindControl("chkFlag");
				CheckBox cbReject = (CheckBox)di.FindControl("cbReject");
				TextBox txtRemarks = (TextBox)di.FindControl("txtRemarks");
				if(cbReject.Checked)
				{
					cbAccept.Checked=false;
					txtRemarks.Visible=true;
				}
				else
				{
					txtRemarks.Text="";
					txtRemarks.Visible=false;
				}
			}

		}


		private void UpdateAlbum()
		{
			string sql = "SELECT t_album.name as [Album Name], t_Employee.name AS [Employee Name], t_Designation.designation, t_Department.deptname as [Department Name], t_album.albumid, " +
				" t_Department.deptid, t_album.creationdate, t_Employee.pcode " +
				" FROM t_Designation INNER JOIN " +
				" t_Employee ON t_Designation.desigid = t_Employee.desigid INNER JOIN " +
				" t_Department ON t_Designation.deptid = t_Department.deptid INNER JOIN " +
				" t_album ON t_Employee.pcode = t_album.createdby " +
				" WHERE (t_album.albumid IN " +
				" (SELECT DISTINCT t_album.albumid " +
				" FROM t_album INNER JOIN " +
				" t_photos ON t_album.albumid = t_photos.albumid " +
				" WHERE (t_photos.flag = 0))) AND (t_Department.status = 1) AND (t_Designation.status = 1) AND (t_Employee.del = 1) And (t_album.sharedalbum=1)";

			if (ddlDepartment.SelectedIndex>0)
			{
				sql+=" and t_Department.deptid="+ddlDepartment.SelectedValue;
			}
			SqlDataAdapter cmd = new SqlDataAdapter(sql,conn);
//			SqlDataAdapter cmd=new SqlDataAdapter("SELECT dbo.t_album.albumid, dbo.t_Department.deptname AS [Department Name], dbo.t_album.name AS [Album Name], " + 
//				" dbo.t_Employee.name AS [Employee Name],dbo.t_Designation.designation " +
//				" FROM dbo.t_album INNER JOIN " +
//				" dbo.t_Employee ON dbo.t_album.createdby = dbo.t_Employee.pcode INNER JOIN " +
//				" dbo.t_Designation ON dbo.t_Employee.desigid = dbo.t_Designation.desigid INNER JOIN " +
//				" dbo.t_Department ON dbo.t_Designation.deptid = dbo.t_Department.deptid " +
//				" ORDER BY dbo.t_Department.deptname, dbo.t_Employee.name, dbo.t_album.name",conn);
			DataSet ds = new DataSet();
			cmd.Fill(ds,"Album");
			DataView dv = new DataView(ds.Tables[0]);
			dv.Sort=albumSort.Text;
			dgAlbums.DataSource=dv;
			dgAlbums.DataKeyField="albumid";
			dgAlbums.DataBind();
		}

		private void Page_Load(object sender, System.EventArgs e)
		{
			
			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			if(IsPageAccessAllowed())
			{
              
			}
			else
			{
				Response.Redirect("ErrorPage.aspx");
			}

			
			
			conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			if(!IsPostBack)
			{
				FillDepartmentsDDL();
				pnlAlbum.Visible=true;
				pnlPhotos.Visible=false;
				UpdateAlbum();
			}
			// Put user code to initialize the page here
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.dgAlbums.SortCommand += new System.Web.UI.WebControls.DataGridSortCommandEventHandler(this.dgAlbums_SortCommand);
			this.dgAlbums.SelectedIndexChanged += new System.EventHandler(this.dgAlbums_SelectedIndexChanged);
			this.DataGrid1.SortCommand += new System.Web.UI.WebControls.DataGridSortCommandEventHandler(this.DataGrid1_SortCommand);
			this.DataGrid1.SelectedIndexChanged += new System.EventHandler(this.DataGrid1_SelectedIndexChanged);
			this.cmdUpdate.Click += new System.EventHandler(this.cmdUpdate_Click);
			this.cmdBack.Click += new System.EventHandler(this.cmdBack_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

//		private void ddlDepartment_SelectedIndexChanged(object sender, System.EventArgs e)
//		{
//			if (ddlDepartment.SelectedIndex==0)
//			{
//				SqlCommand cmd=new SqlCommand("SELECT dbo.t_album.albumid, dbo.t_Department.deptname AS [Department Name], dbo.t_album.name AS [Album Name], " + 
//					" dbo.t_Employee.name AS [Employee Name],dbo.t_Designation.designation " +
//					" FROM dbo.t_album INNER JOIN " +
//					" dbo.t_Employee ON dbo.t_album.createdby = dbo.t_Employee.pcode INNER JOIN " +
//					" dbo.t_Designation ON dbo.t_Employee.desigid = dbo.t_Designation.desigid INNER JOIN " +
//					" dbo.t_Department ON dbo.t_Designation.deptid = dbo.t_Department.deptid " +
//					" ORDER BY dbo.t_Department.deptname, dbo.t_Employee.name, dbo.t_album.name",conn);
//				SqlDataReader dr=cmd.ExecuteReader();
//				dgAlbums.DataSource=dr;
//				dgAlbums.DataKeyField="albumid";
//				dgAlbums.DataBind();
//				dr.Close();
//
//			}
//			else
//			{
//				SqlCommand cmd=new SqlCommand("SELECT dbo.t_album.albumid, dbo.t_Department.deptname AS [Department Name], dbo.t_album.name AS [Album Name], " + 
//					" dbo.t_Employee.name AS [Employee Name],dbo.t_Designation.designation " +
//					" FROM dbo.t_album INNER JOIN " +
//					" dbo.t_Employee ON dbo.t_album.createdby = dbo.t_Employee.pcode INNER JOIN " +
//					" dbo.t_Designation ON dbo.t_Employee.desigid = dbo.t_Designation.desigid INNER JOIN " +
//					" dbo.t_Department ON dbo.t_Designation.deptid = dbo.t_Department.deptid " +
//					" WHERE (dbo.t_Department.deptid = " + ddlDepartment.SelectedValue.ToString() + ") " +
//					" ORDER BY dbo.t_Department.deptname, dbo.t_Employee.name, dbo.t_album.name",conn);
//				SqlDataReader dr=cmd.ExecuteReader();
//				dgAlbums.DataSource=dr;
//				dgAlbums.DataKeyField="albumid";
//				dgAlbums.DataBind();
//				dr.Close();
//
//			}
//		}
//
		private void UpdatePhotos()
		{
			SqlDataAdapter cmd=new SqlDataAdapter("SELECT picid, '<a href=# onclick=window.open(\"showphoto.aspx?photoid=' + filename + '\",\"photowindow\",\"toolbar=no,location=no,menubar=no,scrollbars=yes,resizable=yes\");><img src=../photos/' + filename + '.jpg width=100 border=0></a>' AS Photo, Title, picturedate  FROM t_photos WHERE (albumid = '{" + lblAlbumID.Text + "}')  and flag=0",conn);
			DataSet ds = new DataSet();
			cmd.Fill(ds,"Photos");

			//SqlDataReader dr=cmd.ExecuteReader();
			
			DataView dv = new DataView(ds.Tables[0]);
			dv.Sort=photoSort.Text;
			DataGrid1.DataSource=dv;
			DataGrid1.DataKeyField="picid";
			DataGrid1.DataBind();
			//dr.Close();
			//cmd.Dispose();
			string id;
			foreach( DataGridItem di in DataGrid1.Items )
			{
				id=di.Cells[0].Text;
				SqlCommand cmd2=new SqlCommand("SELECT flag FROM t_photos WHERE (picid = '{" + id + "}') ",conn);
				SqlDataReader dr=cmd2.ExecuteReader();
				dr.Read();
				CheckBox cbAccept = (CheckBox)di.FindControl("chkFlag") ;
				CheckBox cbReject =(CheckBox)di.FindControl("cbReject");

				if (dr.HasRows)
				{
					if(dr[0].ToString()=="0")
					{
						cbAccept.Checked=false;
						cbReject.Checked=false;
					}
					else if(dr[0].ToString()=="1")
					{
						cbAccept.Checked=true;
						cbReject.Checked=false;
					}
					else if (dr[0].ToString()=="2")
					{
						cbAccept.Checked=false;
						cbReject.Checked=true;
					}
					else
					{
						cbAccept.Checked=false;
						cbReject.Checked=false;
					}

				}
				else
				{
					cbAccept.Checked=false;
					cbReject.Checked=false;
				}
				dr.Close();
				cmd2.Dispose();
				
			}
			
		}

		private void dgAlbums_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			lblAlbumID.Text=dgAlbums.DataKeys[dgAlbums.SelectedIndex].ToString();
			pnlAlbum.Visible=false;
			pnlPhotos.Visible=true;
			int i;
			i=dgAlbums.SelectedIndex;
			lblCreatedBy.Text = dgAlbums.Items[i].Cells[1].Text;
			lblDesignation.Text=dgAlbums.Items[i].Cells[2].Text;
			lblDepartment.Text=dgAlbums.Items[i].Cells[3].Text;
			lblAlbumName.Text=dgAlbums.Items[i].Cells[0].Text;
			lblPCode.Text=dgAlbums.Items[i].Cells[5].Text;
			UpdatePhotos();
		}

		private void cmdBack_Click(object sender, System.EventArgs e)
		{
			pnlAlbum.Visible=true;
			pnlPhotos.Visible=false;
			dgAlbums.SelectedIndex=-1;
		}

		private void cmdUpdate_Click(object sender, System.EventArgs e)
		{
			if(_isRefresh)
			{
				pnlAlbum.Visible=true;
				pnlPhotos.Visible=false;
				dgAlbums.SelectedIndex=-1;

				FillDepartmentsDDL();
				UpdateAlbum();
				return;
			}


			string message="";
			string sql;
			string flag="0";
			SqlCommand cmd;
			string picid;
			//message = "Following Images accepted/rejected by admin <br>";
			foreach( DataGridItem di in DataGrid1.Items )
			{

				picid=di.Cells[0].Text;
				CheckBox cb  = (CheckBox)di.FindControl("chkFlag") ;
				CheckBox cb2 = (CheckBox)di.FindControl("cbReject");
				TextBox txtRemarks = (TextBox)di.FindControl("txtRemarks");
				string picname=di.Cells[2].Text;

				if (cb.Checked == false && cb2.Checked==false)
				{
					flag="0";
				}
				else if (cb.Checked==true && cb2.Checked==false)
				{
					flag="1";
					message+="<b>"+picname+"</b> is accpted<br>";
				}
				else if (cb.Checked==false && cb2.Checked==true)
				{
					flag="2";
					message+="<b>"+picname+"</b> is rejected due to: "+txtRemarks.Text+"<br>";
				}
				else if (cb.Checked==true && cb2.Checked==true)
				{
					flag="0";
				}

				//sql="update t_photos set flag = " + flag + " where picid='{" + picid + "}'";
				sql = " UPDATE dbo.t_photos " +
					" SET flag = @p_flag, comments = @p_comments "+
					" WHERE (picid = '{" + picid + "}')";
				SqlParameter p_flag=new SqlParameter("@p_flag",SqlDbType.TinyInt);
				p_flag.Value=flag;

				SqlParameter p_comments = new SqlParameter("@p_comments",SqlDbType.VarChar,2000);
				p_comments.Value=txtRemarks.Text;

				cmd=new SqlCommand(sql,conn);
				cmd.Parameters.Add(p_comments);
				cmd.Parameters.Add(p_flag);
				
				cmd.ExecuteNonQuery();
				cmd.Dispose();

				pnlAlbum.Visible=true;
				pnlPhotos.Visible=false;
				dgAlbums.SelectedIndex=-1;

				FillDepartmentsDDL();
				UpdateAlbum();
			}
			if (message.Length>0)
			{

				message = "Following Images accepted/rejected by admin <br><br>"+message;
				Bulletin.PostMessage("Picture Approval Notice",message,Session["user_id"].ToString(),7,1,Session["user_id"].ToString(),lblPCode.Text);
			}
		}

		private void ddlDepartment_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			UpdateAlbum();
		}

		private void dgAlbums_SortCommand(object source, System.Web.UI.WebControls.DataGridSortCommandEventArgs e)
		{
			//Response.Write(e.SortExpression);
			albumSort.Text=e.SortExpression;
			UpdateAlbum();
		}

		private void DataGrid1_SortCommand(object source, System.Web.UI.WebControls.DataGridSortCommandEventArgs e)
		{
			photoSort.Text=e.SortExpression;
			UpdatePhotos();
		}

		private void DataGrid1_SelectedIndexChanged(object sender, System.EventArgs e)
		{
		
		}
	}
}
