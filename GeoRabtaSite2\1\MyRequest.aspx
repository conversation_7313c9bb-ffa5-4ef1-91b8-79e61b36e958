<%@ Page CodeBehind="MyRequest.aspx.cs" Language="c#" AutoEventWireup="false" Inherits="GeoRabtaSite.MyRequest" smartNavigation="False"%>
<%@ Register TagPrefix="uc1" TagName="Organimeter" Src="Organimeter.ascx" %>
<%@ Register TagPrefix="uc1" TagName="MenuControl" Src="MenuControl.ascx" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
  <HEAD>
		<title>Geo Rabta ::</title>
		<META content="text/html; charset=utf-8" http-equiv="Content-Type">
		<meta name="GENERATOR" content="Microsoft Visual Studio .NET 7.1">
		<meta name="vs_targetSchema" content="http://schemas.microsoft.com/intellisense/ie5">
		<LINK rel="stylesheet" type="text/css" href="StyleSheet1.css">
		<style type="text/css">.style2 { FONT-WEIGHT: bold; FONT-SIZE: 16pt; COLOR: #ffffff }
	</style>
		<script language="javascript" type="text/javascript">
function OpenURL(file,docw,doch)
{
var w=screen.width;
var h=screen.height;
var l=(w-docw)/2;
var t=(h-doch)/2;
var viewimageWin=window.open(file,"codewindow","toolbar=0,scrollbars=1,location=0,statusbar=0,menubar=0,resizable=0,width="+docw+",height="+doch+",left=" + l + ",top="+t);
return false;
}
		</script>
		<script id="clientEventHandlersJS" language="javascript">
<!--

function window_onscroll() {
document.getElementById("txtX").value=document.body.scrollTop;
document.getElementById("txtY").value=document.body.scrollLeft;
}

function window_onload() {
document.body.scrollTop=document.getElementById("txtX").value;
document.body.scrollLeft=document.getElementById("txtY").value;
}

//-->
		</script>
</HEAD>
	<body onscroll="return window_onscroll()" language="javascript" onload="return window_onload()"
		bottomMargin="0" background="images\bg.jpg" leftMargin="0" rightMargin="0" bgProperties="fixed"
		topMargin="0">
		<form id="myForm" name="MyForm" runat="server">
			<table border="0" cellSpacing="0" cellPadding="0" width="1004" height="100%">
				<tr>
					<td height="200" vAlign="top" colSpan="3" align="left">
      <OBJECT id=Shockwaveflash1 
      codeBase=http://fpdownload.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,0,0 
      height=200 width=1004 align=middle 
      classid=clsid:d27cdb6e-ae6d-11cf-96b8-444553540000 VIEWASTEXT>
	<PARAM NAME="_cx" VALUE="26564">
	<PARAM NAME="_cy" VALUE="5292">
	<PARAM NAME="FlashVars" VALUE="">
	<PARAM NAME="Movie" VALUE="top.swf">
	<PARAM NAME="Src" VALUE="top.swf">
	<PARAM NAME="WMode" VALUE="Transparent">
	<PARAM NAME="Play" VALUE="-1">
	<PARAM NAME="Loop" VALUE="-1">
	<PARAM NAME="Quality" VALUE="High">
	<PARAM NAME="SAlign" VALUE="">
	<PARAM NAME="Menu" VALUE="-1">
	<PARAM NAME="Base" VALUE="">
	<PARAM NAME="AllowScriptAccess" VALUE="sameDomain">
	<PARAM NAME="Scale" VALUE="ExactFit">
	<PARAM NAME="DeviceFont" VALUE="0">
	<PARAM NAME="EmbedMovie" VALUE="0">
	<PARAM NAME="BGColor" VALUE="0066FF">
	<PARAM NAME="SWRemote" VALUE="">
	<PARAM NAME="MovieData" VALUE="">
	<PARAM NAME="SeamlessTabbing" VALUE="1">
	<PARAM NAME="Profile" VALUE="0">
	<PARAM NAME="ProfileAddress" VALUE="">
	<PARAM NAME="ProfilePort" VALUE="0">
	<PARAM NAME="AllowNetworking" VALUE="all">
	<PARAM NAME="AllowFullScreen" VALUE="false">
	<embed src="top.swf" 
      width="1004" height="200" align="middle" quality="high" 
      wmode="transparent"								bgcolor="#0066ff" scale="exactfit" 
      allowScriptAccess="sameDomain" type="application/x-shockwave-flash"
      								pluginspage="http://www.macromedia.com/go/getflashplayer" />
	</OBJECT>
					</td>
				</tr>
				<tr>
					<td style="WIDTH: 175px" vAlign="top" width="175" align="center"><uc1:menucontrol id="MenuControl1" runat="server"></uc1:menucontrol></td>
					<td style="WIDTH: 650px" bgColor="#ffffff" vAlign="top" width="650" align="left">
						<TABLE id="Table3" border="0" cellSpacing="0" cellPadding="2" width="650" bgColor="white"
							align="center">
							<TR>
								<TD bgColor="#004477" height="63" background="images/PanelTop.jpg">
									<TABLE id="Table4" border="0" cellSpacing="0" cellPadding="0" width="100%">
										<TR>
											<TD height="36" width="5%">&nbsp;</TD>
											<TD width="95%"><SPAN class="style2">My Request</SPAN></TD>
										</TR>
									</TABLE>
								</TD>
							</TR>
							<TR>
								<TD width="650">
									<P class="NormalText">
<asp:imagebutton id=ImageButton1 runat="server" ImageUrl="images\employee.gif" CausesValidation="False"></asp:imagebutton>
<asp:imagebutton id=ImageButton2 runat="server" ImageUrl="images\personal.gif" CausesValidation="False"></asp:imagebutton>
<asp:imagebutton id=ImageButton3 runat="server" ImageUrl="images\family.gif" CausesValidation="False"></asp:imagebutton>
<asp:imagebutton id=ImageButton4 runat="server" ImageUrl="images\education.gif" CausesValidation="False"></asp:imagebutton>
<asp:imagebutton id=imgTraining runat="server" ImageUrl="images\training.gif"></asp:imagebutton>
<asp:HyperLink id=hlMyExp runat="server" BorderWidth="0px" BorderStyle="None" NavigateUrl="EmpExperience.aspx" ImageUrl="images/MyExperience.gif">HyperLink</asp:HyperLink>
<asp:imagebutton id=ImageButton5 runat="server" ImageUrl="buttons/myorganogram.gif" CausesValidation="False"></asp:imagebutton>
<asp:imagebutton id=ibSalary runat="server" ImageUrl="images/payslip.gif" CausesValidation="False"></asp:imagebutton>
<asp:imagebutton id=ibMySelf runat="server" ImageUrl="Images\MeMyself.gif" CausesValidation="False" Visible="False"></asp:imagebutton>
<asp:imagebutton id=imgMyLeave runat="server" ImageUrl="images\MyLeaveBalance.gif" CausesValidation="False"></asp:imagebutton>
<asp:imagebutton id=ImgMyAttendance runat="server" ImageUrl="images\MyAttendance.gif" CausesValidation="False"></asp:imagebutton>
<asp:imagebutton id=imgMyGrievance runat="server" ImageUrl="images\mygrievance.gif"></asp:imagebutton>
<asp:HyperLink id=hlMyRequests runat="server" BorderWidth="0px" BorderStyle="None" NavigateUrl="MyRequest.aspx" ImageUrl="images/myrequests.gif">My Requests</asp:HyperLink>
<asp:panel id=Panel1 runat="server" BackImageUrl="images\tabstrip.jpg" Height="10px" Width="100%"></asp:panel>
										<uc1:organimeter id="Organimeter1" runat="server"></uc1:organimeter>
										<asp:Label id="lblPCode" runat="server"></asp:Label>
										<asp:Label id="lblEmpInfo" runat="server"></asp:Label><BR>
										<asp:panel id="pnlMyRequest" runat="server">
<asp:DataGrid id=dgMyRequest runat="server" BorderWidth="1px" Width="100%" BorderColor="Tan" BackColor="LightGoldenrodYellow" CellPadding="2" ForeColor="Black" AutoGenerateColumns="False">
												<FooterStyle BackColor="Tan"></FooterStyle>
												<SelectedItemStyle ForeColor="GhostWhite" BackColor="DarkSlateBlue"></SelectedItemStyle>
												<AlternatingItemStyle BackColor="PaleGoldenrod"></AlternatingItemStyle>
												<HeaderStyle Font-Bold="True" BackColor="Tan"></HeaderStyle>
												<Columns>
													<asp:ButtonColumn Text="Read" CommandName="Select"></asp:ButtonColumn>
													<asp:TemplateColumn>
														<ItemTemplate>
															<asp:LinkButton id="lnkCancel" runat="server" OnClick="CancelReq">Cancel Request</asp:LinkButton>
														</ItemTemplate>
													</asp:TemplateColumn>
													<asp:BoundColumn DataField="rid" HeaderText="Request ID">
														<ItemStyle HorizontalAlign="Center"></ItemStyle>
													</asp:BoundColumn>
													<asp:BoundColumn DataField="Fieldname" HeaderText="Request Detail"></asp:BoundColumn>
													<asp:BoundColumn DataField="requestforchange" HeaderText="Request for Change"></asp:BoundColumn>
													<asp:BoundColumn DataField="requestdate" HeaderText="Request Date" DataFormatString="{0:d MMM,yyyy}"></asp:BoundColumn>
													<asp:BoundColumn DataField="requesttimeline" HeaderText="Timeline to Complete" DataFormatString="{0:d MMM,yyyy}"></asp:BoundColumn>
													<asp:BoundColumn DataField="requeststatus" HeaderText="Status"></asp:BoundColumn>
													<asp:BoundColumn DataField="section" HeaderText="section"></asp:BoundColumn>
												</Columns>
												<PagerStyle HorizontalAlign="Center" ForeColor="DarkSlateBlue" BackColor="PaleGoldenrod"></PagerStyle>
											</asp:DataGrid>
										</asp:panel><asp:label id="lblmsgTitle" runat="server" Visible="False" Font-Bold="True">Message:</asp:label><BR>
										<asp:label id="lblMessage" runat="server" Visible="False"></asp:label><BR>
										<asp:textbox style="VISIBILITY: hidden" id="txtX" runat="server" Width="24px"></asp:textbox><asp:textbox style="VISIBILITY: hidden" id="txtY" runat="server" Width="24px"></asp:textbox></P>
								</TD>
							</TR>
						</TABLE>
					</td>
					<td style="WIDTH: 180px" vAlign="top" width="180" align="center"></td>
				</tr>
				<tr>
					<td class="Footer" height="20" vAlign="middle" colSpan="3" align="center"><SPAN class="Fotter">Copyright 
							© 2005 Independent Media Corporation www.geo.tv</SPAN></td>
				</tr>
			</table>
		</form>
	</body>
</HTML>
