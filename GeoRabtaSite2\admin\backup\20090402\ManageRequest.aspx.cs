using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;
using System.IO;

namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for GeoIdeas.
	/// </summary>
	public class ManageRequest : System.Web.UI.Page
	{
		SqlConnection con;
		protected System.Web.UI.HtmlControls.HtmlTable tabForm;
		


		private bool _refreshState;
		protected System.Web.UI.WebControls.Panel pnlrView;
		protected System.Web.UI.WebControls.Label lblFieldName;
		protected System.Web.UI.WebControls.Label lblcInformation;
		protected System.Web.UI.WebControls.Label lblRequestInformation;
		protected System.Web.UI.WebControls.Label lblVerificationCriteria;
		protected System.Web.UI.WebControls.Label lblRequiredByHR;
		protected System.Web.UI.WebControls.Label lblRequiredByEmp;
		protected System.Web.UI.HtmlControls.HtmlGenericControl col1;
		protected System.Web.UI.HtmlControls.HtmlTableCell col2;
		protected System.Web.UI.WebControls.Button btnAccept;
		protected System.Web.UI.WebControls.Button btnReject;
		protected System.Web.UI.WebControls.Label lblTimeline;
		protected System.Web.UI.WebControls.Label lnkView;
		protected System.Web.UI.HtmlControls.HtmlTableCell TD1;
		protected System.Web.UI.WebControls.TextBox txtMessage;
		protected System.Web.UI.WebControls.Label lblAction;
		protected System.Web.UI.WebControls.Button btnSend;
		protected System.Web.UI.WebControls.Button btnCancel;
		protected System.Web.UI.WebControls.Label lblRequestDate;
		protected System.Web.UI.WebControls.Panel pnlMyReq;
		protected System.Web.UI.WebControls.Panel pnlEdu;
		protected System.Web.UI.WebControls.Panel pnlFamily;
		protected System.Web.UI.WebControls.Button Button5;
		protected System.Web.UI.WebControls.Button Button6;
		protected System.Web.UI.WebControls.TextBox TextBox2;
		protected System.Web.UI.WebControls.Label Label11;
		protected System.Web.UI.WebControls.Button Button7;
		protected System.Web.UI.WebControls.Button Button8;
		protected System.Web.UI.WebControls.Label Label12;
		protected System.Web.UI.HtmlControls.HtmlTableCell Td4;
		protected System.Web.UI.HtmlControls.HtmlTableCell Td5;
		protected System.Web.UI.HtmlControls.HtmlTable tabEduDetail;
		protected System.Web.UI.WebControls.Label Label13;
		protected System.Web.UI.WebControls.Label Label14;
		protected System.Web.UI.WebControls.Label Label15;
		protected System.Web.UI.WebControls.Label Label16;
		protected System.Web.UI.WebControls.Label Label17;
		protected System.Web.UI.WebControls.Label Label18;
		protected System.Web.UI.WebControls.Label Label19;
		protected System.Web.UI.WebControls.Label Label20;
		protected System.Web.UI.WebControls.Label Label21;
		protected System.Web.UI.WebControls.Label Label22;
		protected System.Web.UI.WebControls.Label Label23;
		protected System.Web.UI.WebControls.Label Label24;
		protected System.Web.UI.WebControls.Label Label25;
		protected System.Web.UI.WebControls.Label Label26;
		protected System.Web.UI.WebControls.Button Button1;
		protected System.Web.UI.WebControls.Button Button2;
		protected System.Web.UI.WebControls.TextBox TextBox1;
		protected System.Web.UI.WebControls.Label Label1;
		protected System.Web.UI.WebControls.Button Button3;
		protected System.Web.UI.WebControls.Button Button4;
		protected System.Web.UI.WebControls.Label Label2;
		protected System.Web.UI.HtmlControls.HtmlTableCell Td2;
		protected System.Web.UI.HtmlControls.HtmlTableCell Td3;
		protected System.Web.UI.HtmlControls.HtmlTable tabFamilyDetail;
		protected System.Web.UI.WebControls.Label Label27;
		protected System.Web.UI.WebControls.Label Label28;
		protected System.Web.UI.WebControls.Label Label29;
		protected System.Web.UI.WebControls.Label Label30;
		protected System.Web.UI.WebControls.Label Label31;
		protected System.Web.UI.WebControls.Label Label32;
		protected System.Web.UI.WebControls.Label Label33;
		protected System.Web.UI.WebControls.Label Label34;
		protected System.Web.UI.WebControls.Label Label35;
		protected System.Web.UI.WebControls.Label Label36;
		protected System.Web.UI.WebControls.Label Label37;
		protected System.Web.UI.WebControls.Label Label40;
		protected System.Web.UI.WebControls.Label Label41;
		protected System.Web.UI.WebControls.Label Label42;
		protected System.Web.UI.WebControls.Label Label43;
		protected System.Web.UI.WebControls.Label Label44;
		protected System.Web.UI.WebControls.Label Label3;
		protected System.Web.UI.WebControls.Label Label4;
		protected System.Web.UI.WebControls.Panel pnlTraining;
		protected System.Web.UI.WebControls.Label Label7;
		protected System.Web.UI.WebControls.Label Label8;
		protected System.Web.UI.WebControls.Label Label9;
		protected System.Web.UI.WebControls.Panel pnlExp;
		protected System.Web.UI.WebControls.Label lbleCode;
		protected System.Web.UI.WebControls.Label lnkvPicture;
		protected System.Web.UI.WebControls.DataGrid dgMyGrievance;
		protected System.Web.UI.WebControls.Label lblTitle;
		protected System.Web.UI.WebControls.Label lblGrievanceType;
		protected System.Web.UI.WebControls.Label lblGrievanceSubType;
		protected System.Web.UI.WebControls.Label txtDetail;
		protected System.Web.UI.WebControls.DataGrid dgBlog;
		protected System.Web.UI.WebControls.Panel pnlbView;
		protected System.Web.UI.WebControls.TextBox txtComments;
		protected System.Web.UI.WebControls.DropDownList ddClosingCode;
		protected System.Web.UI.WebControls.LinkButton lnkRedirect;
		protected System.Web.UI.WebControls.Panel pnlInterimDate;
		protected System.Web.UI.WebControls.DropDownList ddGrievance;
		protected System.Web.UI.WebControls.DropDownList ddSubGrivance;
		protected System.Web.UI.WebControls.Panel pnlRedirect;
		protected System.Web.UI.WebControls.Button btnGrievanceSubmit;
		protected System.Web.UI.WebControls.Panel pnlgView;
		protected eWorld.UI.CalendarPopup cpInterim;
		protected System.Web.UI.WebControls.ImageButton imgRequest;
		protected System.Web.UI.WebControls.ImageButton imgReopen;
		protected System.Web.UI.WebControls.Panel pnlReOpen;
		protected System.Web.UI.WebControls.DataGrid dgReopen;
		protected System.Web.UI.WebControls.DataGrid dgMyRequest;
		protected System.Web.UI.WebControls.Panel pnlccGrievance;
		protected System.Web.UI.WebControls.DataGrid dgMyccGrievance;
		protected System.Web.UI.WebControls.Panel pnlcView;
		protected System.Web.UI.WebControls.Label txtccDetail;
		protected System.Web.UI.WebControls.Label lblccGrievanceSubType;
		protected System.Web.UI.WebControls.Label lblccGrievanceType;
		protected System.Web.UI.WebControls.Label lblccTitle;
		protected System.Web.UI.WebControls.DataGrid dgccBlog;
		protected System.Web.UI.WebControls.ImageButton imgcCGrievance;
		protected System.Web.UI.WebControls.Label lblRID;
		protected System.Web.UI.WebControls.Button cmdCancelTraining;
		protected System.Web.UI.WebControls.Button cmdSubmitTraining;
		protected System.Web.UI.WebControls.TextBox txtTrainingMsg;
		protected System.Web.UI.WebControls.Label lblTrainingMsg;
		protected System.Web.UI.WebControls.Button cmdRejTraining;
		protected System.Web.UI.WebControls.Button cmdAccTraining;
		protected System.Web.UI.WebControls.Label Label6;
		protected System.Web.UI.WebControls.Label Label10;
		protected System.Web.UI.WebControls.Label Label38;
		protected System.Web.UI.WebControls.Label Label39;
		protected System.Web.UI.WebControls.Label Label45;
		protected System.Web.UI.WebControls.Label Label46;
		protected System.Web.UI.WebControls.Label Label47;
		protected System.Web.UI.WebControls.Label Label48;
		protected System.Web.UI.WebControls.Label Label49;
		protected System.Web.UI.WebControls.Label Label50;
		protected System.Web.UI.WebControls.Label Label51;
		protected System.Web.UI.WebControls.Label Label52;
		protected System.Web.UI.WebControls.Label Label53;
		protected System.Web.UI.WebControls.Label Label54;
		protected System.Web.UI.WebControls.Label Label5;
		protected System.Web.UI.WebControls.Panel pnlTrainingDetail;
		protected System.Web.UI.HtmlControls.HtmlTable Table5;
		protected System.Web.UI.HtmlControls.HtmlTableCell Td6;
		protected System.Web.UI.HtmlControls.HtmlTableCell Td7;
		protected System.Web.UI.WebControls.Button Button9;
		protected System.Web.UI.WebControls.Button Button10;
		protected System.Web.UI.WebControls.TextBox TextBox3;
		protected System.Web.UI.WebControls.Label Label55;
		protected System.Web.UI.WebControls.Button Button11;
		protected System.Web.UI.WebControls.Button Button12;
		protected System.Web.UI.WebControls.Label Label56;
		protected System.Web.UI.WebControls.Label Label81;
		protected System.Web.UI.WebControls.Label Label80;
		protected System.Web.UI.WebControls.Label Label79;
		protected System.Web.UI.WebControls.Label Label78;
		protected System.Web.UI.WebControls.Label Label77;
		protected System.Web.UI.WebControls.Label Label76;
		protected System.Web.UI.WebControls.Label Label75;
		protected System.Web.UI.WebControls.Label Label74;
		protected System.Web.UI.WebControls.Label Label73;
		protected System.Web.UI.WebControls.Label Label72;
		protected System.Web.UI.WebControls.Label Label71;
		protected System.Web.UI.WebControls.Label Label70;
		protected System.Web.UI.WebControls.Label Label69;
		protected System.Web.UI.WebControls.Label Label57;
		protected System.Web.UI.WebControls.Label Label58;
		protected System.Web.UI.WebControls.Label Label59;
		protected System.Web.UI.WebControls.Label Label60;
		protected System.Web.UI.WebControls.Label Label61;
		protected System.Web.UI.WebControls.Label Label62;
		protected System.Web.UI.WebControls.Label Label63;
		protected System.Web.UI.WebControls.Label Label64;
		protected System.Web.UI.WebControls.Label Label65;
		protected System.Web.UI.WebControls.Label Label66;
		protected System.Web.UI.WebControls.Label Label67;
		protected System.Web.UI.WebControls.Label Label68;
		protected System.Web.UI.WebControls.Label lblReqID;
		protected System.Web.UI.WebControls.Label lblExpID;
		protected System.Web.UI.WebControls.Panel pnlExpDetail;
		protected System.Web.UI.HtmlControls.HtmlTable Table7;
		protected System.Web.UI.HtmlControls.HtmlTableCell Td8;
		protected System.Web.UI.HtmlControls.HtmlTableCell Td9;
		protected System.Web.UI.WebControls.DataGrid dgTraining;
		protected System.Web.UI.WebControls.DataGrid dgExp;
		protected System.Web.UI.WebControls.DataGrid Datagrid1;
		protected System.Web.UI.WebControls.DataGrid Datagrid2;
		private bool _isRefresh;
		string sDept;
		string sCat;
		protected System.Web.UI.WebControls.Label Label82;
		protected System.Web.UI.WebControls.Label Label83;
		protected System.Web.UI.WebControls.Label Label84;
		protected System.Web.UI.WebControls.Label Label85;
		string sStation;
		protected System.Web.UI.WebControls.ImageButton imgPulse;
        string userId=""; 
		
		private bool IsPageAccessAllowed()
		{

			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception ex)
			{
				string s=ex.Message;
				Response.Redirect("../Login.aspx");
			}
 
			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(2,56,userId,"View")==true)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("../Login.aspx");
				return false;
			}
		}
		public void FillGrievanceGrid()
		{
			
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			string conQuery="select f.fdesigid,so.specialistno from t_functionaldesignation f,t_designationhistory h,t_specialist s,t_specialistowner so where h.fdesigid=f.fdesigid and h.pcode='"+Session["user_id"].ToString()+"' and h.isactive=1 and so.s_fdesigid=f.fdesigid and s.specialistno=so.specialistno";
			SqlCommand cmd=new SqlCommand(conQuery,con);
			SqlDataReader rd=cmd.ExecuteReader();
			string getIDs="";
			string FdID="";
			bool flag=false;
			while(rd.Read())
			{
				getIDs+=rd[1].ToString()+",";
				FdID=rd[0].ToString();
			}
			rd.Close();
			if(getIDs.Length>0)
			{
				getIDs=getIDs.Remove(getIDs.Length-1,1);
			
				//conQuery="select e.grievancetitle,e.grievancedetail,e.requestdate,e.requesttimeline,e.requisitecode,e.sgid,e.requisitecode as code,e.rid as rID,em.name as requestby,Status=case e.newspmsg when 1 then 'You have an update' else ''end,f.sgrievance,g.grievance,requeststatus=case requeststatus when 1 then 'Inprocess' when 2 then 'Cancelled' when 3 then 'Resolved' when 4 then 'Re-Open' when '5' then 'Interim Closed' end from t_employeegrievancerequest e,t_grievancesubtype f,t_grievancespecialist fs,t_employee em,t_grievance g where (e.requeststatus=1 or e.requeststatus=4) and e.sgid=f.sid and f.sid=fs.sgid and fs.grievancespecialist in("+getIDs+") and em.pcode=e.requisitecode and em.del=1 and fs.specialisttype=1 and g.gid=f.gid order by e.requestdate asc";
				string orderby="order by e.requestdate asc";
				conQuery="select e.grievancetitle,e.grievancedetail,e.requestdate,e.requesttimeline,e.requisitecode,e.sgid,e.requisitecode as code,e.rid as rID,em.name as requestby,Status=case e.newspmsg when 1 then 'You have an update' else ''end,f.sgrievance,g.grievance,requeststatus=case requeststatus when 1 then 'Inprocess' when 2 then 'Cancelled' when 3 then 'Resolved' when 4 then 'Re-Open' when '5' then 'Interim Closed' end from t_employeegrievancerequest e,t_grievancesubtype f,t_grievancespecialist fs,t_employee em,t_grievance g,t_designation d,t_department dept,t_city c,t_categorization ct where ct.cat_id=d.category and em.station=c.cityid and d.deptid=dept.deptid and em.desigid=d.desigid and dept.deptid=d.deptid and (e.requeststatus=1 or e.requeststatus=4) and e.sgid=f.sid and f.sid=fs.sgid and fs.grievancespecialist in("+getIDs+") and em.pcode=e.requisitecode and em.del=1 and fs.specialisttype=1 and g.gid=f.gid";
				
				cmd=new SqlCommand("select distinct d.s_deptid from t_specialist s,t_specialistowner so,t_specialistdept d where so.specialistno=s.specialistno and s.specialistno in ("+getIDs+") and so.s_fdesigid="+FdID+" and s.specialistno=d.specialistno",con);
				rd=cmd.ExecuteReader();
				string dept="";
				while(rd.Read())
				{
					dept+=rd[0].ToString()+",";
				}
				rd.Close();
				if(dept.Length>0)
				{
					flag=true;
					dept=dept.Remove(dept.Length-1,1);
					conQuery+=" and d.deptid in ("+dept+")";
				}
				cmd=new SqlCommand("select distinct ss.s_stationid from t_specialist s,t_specialistowner so,t_specialiststation ss where so.specialistno=s.specialistno and s.specialistno in ("+getIDs+") and so.s_fdesigid="+FdID+" and s.specialistno=ss.specialistno",con);
				rd=cmd.ExecuteReader();
				string station="";
				while(rd.Read())
				{
					station+=rd[0].ToString()+",";
				}
				rd.Close();
				if(station.Length>0)
				{
					flag=true;
					station=station.Remove(station.Length-1,1);
					conQuery+=" and cityid in ("+station+")";
				}
				cmd=new SqlCommand("select distinct ss.s_catid from t_specialist s,t_specialistowner so,t_specialistcat ss where so.specialistno=s.specialistno and s.specialistno in ("+getIDs+") and so.s_fdesigid="+FdID+" and s.specialistno=ss.specialistno",con);
				rd=cmd.ExecuteReader();
				string cat="";
				while(rd.Read())
				{
					cat+=rd[0].ToString()+",";
				} 
				rd.Close();
				if(cat.Length>0)
				{
					flag=true;
					cat=cat.Remove(cat.Length-1,1);
					conQuery+=" and d.category in("+cat+")";
				}
				if(flag)
				{
					conQuery+=orderby;
					SqlDataAdapter dr=new SqlDataAdapter(conQuery,con);
					DataSet ds=new DataSet();
					dr.Fill(ds,"Fields");
					this.dgMyGrievance.DataSource=ds;
					this.dgMyGrievance.DataBind();
					SetStatus(true);
				}
			}
			con.Close();
		}
		public void FillccGrievanceGrid()
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			string conQuery="select f.fdesigid,so.specialistno from t_functionaldesignation f,t_designationhistory h,t_specialist s,t_specialistowner so where h.fdesigid=f.fdesigid and h.pcode='"+Session["user_id"].ToString()+"' and h.isactive=1 and so.s_fdesigid=f.fdesigid and s.specialistno=so.specialistno";
			SqlCommand cmd=new SqlCommand(conQuery,con);
			SqlDataReader rd=cmd.ExecuteReader();
			string getIDs="";
			string FdID="";
			while(rd.Read())
			{
				getIDs+=rd[1].ToString()+",";
				FdID=rd[0].ToString();
			}
			rd.Close();
			
			if(getIDs.Length>0)
			{
				getIDs=getIDs.Remove(getIDs.Length-1,1);
				//conQuery="select e.grievancetitle,e.grievancedetail,e.requestdate,e.requesttimeline,e.requisitecode,e.sgid,e.requisitecode as code,e.rid as rID,em.name as requestby,Status=case e.newspmsg when 1 then 'New Message(s) Found' else ''end from t_employeegrievancerequest e,t_grievancesubtype f,t_grievancespecialist fs,t_employee em where (e.requeststatus=1 or e.requeststatus=4) and e.sgid=f.sid and f.sid=fs.sgid and fs.grievancespecialist in("+getIDs+") and em.pcode=e.requisitecode and em.del=1 and fs.specialisttype=1 order by e.requestdate asc";
				//conQuery="select e.grievancetitle,e.grievancedetail,e.requestdate,e.requesttimeline,e.requisitecode,e.sgid,e.requisitecode as code,e.rid as rID,em.name as requestby,Status=case e.newspmsg when 1 then 'You have an update' else ''end,f.sgrievance,g.grievance,requeststatus=case requeststatus when 1 then 'Inprocess' when 2 then 'Cancelled' when 3 then 'Resolved' when 4 then 'Re-Open' when '5' then 'Interim Closed' end from t_employeegrievancerequest e,t_grievancesubtype f,t_grievancespecialist fs,t_employee em,t_grievance g where (e.requeststatus=1 or e.requeststatus=4) and e.sgid=f.sid and f.sid=fs.sgid and fs.grievancespecialist in("+getIDs+") and em.pcode=e.requisitecode and em.del=1 and fs.specialisttype=2 and g.gid=f.gid order by e.requestdate asc";
				string orderby="order by e.requestdate asc";
				bool flag=false;
				conQuery="select e.grievancetitle,e.grievancedetail,e.requestdate,e.requesttimeline,e.requisitecode,e.sgid,e.requisitecode as code,e.rid as rID,em.name as requestby,Status=case e.newspmsg when 1 then 'You have an update' else ''end,f.sgrievance,g.grievance,requeststatus=case requeststatus when 1 then 'Inprocess' when 2 then 'Cancelled' when 3 then 'Resolved' when 4 then 'Re-Open' when '5' then 'Interim Closed' end from t_employeegrievancerequest e,t_grievancesubtype f,t_grievancespecialist fs,t_employee em,t_grievance g,t_designation d,t_department dept,t_city c,t_categorization ct where ct.cat_id=d.category and em.station=c.cityid and d.deptid=dept.deptid and em.desigid=d.desigid and dept.deptid=d.deptid and (e.requeststatus=1 or e.requeststatus=4) and e.sgid=f.sid and f.sid=fs.sgid and fs.grievancespecialist in("+getIDs+") and em.pcode=e.requisitecode and em.del=1 and fs.specialisttype=2 and g.gid=f.gid";
				cmd=new SqlCommand("select distinct d.s_deptid from t_specialist s,t_specialistowner so,t_specialistdept d where so.specialistno=s.specialistno and s.specialistno in ("+getIDs+") and so.s_fdesigid="+FdID+" and s.specialistno=d.specialistno",con);
				rd=cmd.ExecuteReader();
				string dept="";
				while(rd.Read())
				{
					dept+=rd[0].ToString()+",";
				}
				rd.Close();
				if(dept.Length>0)
				{
					flag=true;
					dept=dept.Remove(dept.Length-1,1);
					conQuery+=" and d.deptid in ("+dept+")";
				}
				cmd=new SqlCommand("select distinct ss.s_stationid from t_specialist s,t_specialistowner so,t_specialiststation ss where so.specialistno=s.specialistno and s.specialistno in ("+getIDs+") and so.s_fdesigid="+FdID+" and s.specialistno=ss.specialistno",con);
				rd=cmd.ExecuteReader();
				string station="";
				while(rd.Read())
				{
					station+=rd[0].ToString()+",";
				}
				rd.Close();
				if(station.Length>0)
				{
					flag=true;
					station=station.Remove(station.Length-1,1);
					conQuery+=" and cityid in ("+station+")";
				}
				cmd=new SqlCommand("select distinct ss.s_catid from t_specialist s,t_specialistowner so,t_specialistcat ss where so.specialistno=s.specialistno and s.specialistno in ("+getIDs+") and so.s_fdesigid="+FdID+" and s.specialistno=ss.specialistno",con);
				rd=cmd.ExecuteReader();
				string cat="";
				while(rd.Read())
				{
					cat+=rd[0].ToString()+",";
				} 
				rd.Close();
				if(cat.Length>0)
				{
					flag=true;
					cat=cat.Remove(cat.Length-1,1);
					conQuery+=" and d.category in("+cat+")";
				}
				if(flag)
				{
					conQuery+=orderby;
					SqlDataAdapter dr=new SqlDataAdapter(conQuery,con);
					DataSet ds=new DataSet();
					dr.Fill(ds,"Fields");
					this.dgMyccGrievance.DataSource=ds;
					this.dgMyccGrievance.DataBind();
					SetGridStatus(dgMyccGrievance,5,9);
				}
			}
			con.Close();
		}

		public void FillGrievanceGridReopen()
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			string conQuery="select f.fdesigid,so.specialistno from t_functionaldesignation f,t_designationhistory h,t_specialist s,t_specialistowner so where h.fdesigid=f.fdesigid and h.pcode='"+Session["user_id"].ToString()+"' and h.isactive=1 and so.s_fdesigid=f.fdesigid and s.specialistno=so.specialistno";
			SqlCommand cmd=new SqlCommand(conQuery,con);
			SqlDataReader rd=cmd.ExecuteReader();
			string getIDs="";
			string FdID="";
			while(rd.Read())
			{
				getIDs+=rd[1].ToString()+",";
				FdID=rd[0].ToString();
			}
			rd.Close();
			if(getIDs.Length>0)
			{
				getIDs=getIDs.Remove(getIDs.Length-1,1);
			
				//conQuery="select e.grievancetitle,e.grievancedetail,e.requestdate,e.requesttimeline,e.requisitecode,e.sgid,e.requisitecode as code,e.rid as rID,em.name as requestby,Status=case e.newspmsg when 1 then 'You have an update' else ''end,f.sgrievance,g.grievance,requeststatus=case requeststatus when 1 then 'Inprocess' when 2 then 'Cancelled' when 3 then 'Resolved' when 4 then 'Re-Open' when '5' then 'Interim Closed' end from t_employeegrievancerequest e,t_grievancesubtype f,t_grievancespecialist fs,t_employee em,t_grievance g where (e.requeststatus=3 or e.requeststatus=5) and e.sgid=f.sid and f.sid=fs.sgid and fs.grievancespecialist in("+getIDs+") and em.pcode=e.requisitecode and em.del=1 and fs.specialisttype=1 and g.gid=f.gid order by e.requestdate asc";
				string orderby="order by e.requestdate asc";
				bool flag=false;
				conQuery="select e.grievancetitle,e.grievancedetail,e.requestdate,e.requesttimeline,e.requisitecode,e.sgid,e.requisitecode as code,e.rid as rID,em.name as requestby,Status=case e.newspmsg when 1 then 'You have an update' else ''end,f.sgrievance,g.grievance,requeststatus=case requeststatus when 1 then 'Inprocess' when 2 then 'Cancelled' when 3 then 'Resolved' when 4 then 'Re-Open' when '5' then 'Interim Closed' end from t_employeegrievancerequest e,t_grievancesubtype f,t_grievancespecialist fs,t_employee em,t_grievance g,t_designation d,t_department dept,t_city c,t_categorization ct where ct.cat_id=d.category and em.station=c.cityid and d.deptid=dept.deptid and em.desigid=d.desigid and dept.deptid=d.deptid and (e.requeststatus=3 or e.requeststatus=5) and e.sgid=f.sid and f.sid=fs.sgid and fs.grievancespecialist in("+getIDs+") and em.pcode=e.requisitecode and em.del=1 and fs.specialisttype=1 and g.gid=f.gid";
				cmd=new SqlCommand("select distinct d.s_deptid from t_specialist s,t_specialistowner so,t_specialistdept d where so.specialistno=s.specialistno and s.specialistno in ("+getIDs+") and so.s_fdesigid="+FdID+" and s.specialistno=d.specialistno",con);
				rd=cmd.ExecuteReader();
				string dept="";
				while(rd.Read())
				{
					dept+=rd[0].ToString()+",";
				}
				rd.Close();
				if(dept.Length>0)
				{
					flag=true;
					dept=dept.Remove(dept.Length-1,1);
					conQuery+=" and d.deptid in ("+dept+")";
				}
				cmd=new SqlCommand("select distinct ss.s_stationid from t_specialist s,t_specialistowner so,t_specialiststation ss where so.specialistno=s.specialistno and s.specialistno in ("+getIDs+") and so.s_fdesigid="+FdID+" and s.specialistno=ss.specialistno",con);
				rd=cmd.ExecuteReader();
				string station="";
				while(rd.Read())
				{
					station+=rd[0].ToString()+",";
				}
				rd.Close();
				if(station.Length>0)
				{
					flag=true;
					station=station.Remove(station.Length-1,1);
					conQuery+=" and cityid in ("+station+")";
				}
				cmd=new SqlCommand("select distinct ss.s_catid from t_specialist s,t_specialistowner so,t_specialistcat ss where so.specialistno=s.specialistno and s.specialistno in ("+getIDs+") and so.s_fdesigid="+FdID+" and s.specialistno=ss.specialistno",con);
				rd=cmd.ExecuteReader();
				string cat="";
				while(rd.Read())
				{
					cat+=rd[0].ToString()+",";
				} 
				rd.Close();
				if(cat.Length>0)
				{
					flag=true;
					cat=cat.Remove(cat.Length-1,1);
					conQuery+=" and d.category in("+cat+")";
				}
				if(flag)
				{
					conQuery+=orderby;
					SqlDataAdapter dr=new SqlDataAdapter(conQuery,con);
					DataSet ds=new DataSet();
					dr.Fill(ds,"Fields");
					this.dgReopen.DataSource=ds;
					this.dgReopen.DataBind();
					this.dgReopen.Visible=true;
				}
			}
			con.Close();
			for(int i=0;i<this.dgReopen.Items.Count;i++)
			{
				LinkButton lnk=(LinkButton)this.dgReopen.Items[i].FindControl("lblReOpen");
				lnk.Attributes.Add("onclick","return confirm('Are you sure to Re-Open?');");
			}
			//SetStatus(true);
		}

		public void OpenUp(object sender,EventArgs e)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlTransaction trans=null;
			LinkButton pLnk=(LinkButton)sender;
			for(int i=0;i<this.dgReopen.Items.Count;i++)
			{
				LinkButton cLnk=(LinkButton)dgReopen.Items[i].FindControl("lblReOpen");
				if(cLnk.Equals(pLnk))
				{
					try
					{
						trans=con.BeginTransaction();
						SqlCommand cmd=new SqlCommand("update t_employeegrievancerequest set requeststatus=1,closingcode=null,newusermsg=1 where rid="+this.dgReopen.Items[i].Cells[1].Text+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
					
						cmd=new SqlCommand("select d.designation,dept.deptname,c.cityname from t_employee e,t_designation d,t_department dept,t_city c where e.desigid=d.desigid and dept.deptid=d.deptid and e.pcode='"+Session["user_id"].ToString()+"' and e.station=c.cityid",con);
						cmd.Transaction=trans;
						SqlDataReader rd=cmd.ExecuteReader();
						rd.Read();
						string designation=rd[0].ToString();
						string department=rd[1].ToString();
						string city=rd[2].ToString();
						rd.Close();
					
						cmd=new SqlCommand("insert into t_grievanceblog(rid,comment,sendon,sendby,senderdesignation,senderstation,senderdepartment) values(@rid,@comment,getdate(),@sendby,@senderdesignation,@senderstation,@senderdepartment)",con);
						SqlParameter _rid=new SqlParameter("@rid",SqlDbType.Int);
						SqlParameter _comment=new SqlParameter("@comment",SqlDbType.Text);
						SqlParameter _sendby=new SqlParameter("@sendby",SqlDbType.VarChar);
						SqlParameter _designation=new SqlParameter("@senderdesignation",SqlDbType.Text);
						SqlParameter _station=new SqlParameter("@senderstation",SqlDbType.Text);
						SqlParameter _department=new SqlParameter("@senderdepartment",SqlDbType.Text);
						_rid.Value=this.dgReopen.Items[i].Cells[1].Text;
						string message="Grievance has been re-opened";
						_comment.Value=message;
						_sendby.Value=Session["user_id"].ToString();
						_designation.Value=designation;
						_department.Value=department;
						_station.Value=city;
						cmd.Parameters.Add(_rid);
						cmd.Parameters.Add(_comment);
						cmd.Parameters.Add(_sendby);
						cmd.Parameters.Add(_designation);
						cmd.Parameters.Add(_station);
						cmd.Parameters.Add(_department);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
					
						trans.Commit();
						con.Close();
						con.Dispose();
						FillGrievanceGridReopen();
						return;
					}
					catch (Exception ex)
					{
						Response.Write(ex.Message);
						trans.Rollback();
					}
				}
			}
		}
		public void SetStatus(bool Vals)
		{
			for(int i=0;i<this.dgMyGrievance.Items.Count;i++)
			{
				string rDate="";
				rDate=this.dgMyGrievance.Items[i].Cells[6].Text;
				string []sDate=rDate.Split(' ');
				rDate=sDate[0];
				TimeSpan Val=DateTime.Parse(rDate).Subtract(DateTime.Now);
				Label lbl=(Label)this.dgMyGrievance.Items[i].FindControl("lblgStatus");
				if(Val.Days>=0)
				{
					
					lbl.Text=this.dgMyGrievance.Items[i].Cells[13].Text;
				}
				else if(Val.Days < 0)
				{
					//lbl.Text="Exceed Timeline";
					lbl.Text="<img src=../images/aaloo.gif border=0 />";
					lbl.ToolTip="Timeline Exceed";
					lbl.ForeColor=Color.Red;
				}
				else
				{
					lbl.Text="Undefined";
				}
			}
			//this.dgMyRequest.DataBind();
		}

		public bool IsRefresh
		{
			get
			{
				return _isRefresh;
			}
		}

		protected override void LoadViewState(object savedState)
		{
			object[] allStates = (object[]) savedState;
			base.LoadViewState(allStates[0]);
			_refreshState = (bool) allStates[1];
			try
			{
				_isRefresh = _refreshState == (bool) Session["__ISREFRESH"];
			}
			catch(Exception)
			{
				Response.Redirect("../Login.aspx");
			}

		}

		protected override object SaveViewState()
		{
			Session["__ISREFRESH"] = _refreshState;
			object[] allStates = new object[2];
			allStates[0] = base.SaveViewState();
			allStates[1] = !_refreshState;
			return allStates;
		}

		private void SetGridStatus(DataGrid dg,int timelinecolumn, int msgcolumn)
		{
			for(int i=0;i<dg.Items.Count;i++)
			{
				string rDate="";
				rDate=dg.Items[i].Cells[timelinecolumn].Text;
				TimeSpan Val=DateTime.Parse(rDate).Subtract(DateTime.Now);
				if(Val.Days>=0)
				{
					dg.Items[i].Cells[msgcolumn].Text="Inprocess";
					dg.Items[i].Cells[msgcolumn].ForeColor=System.Drawing.Color.Black;
				}
				else if(Val.Days < 0)
				{
					dg.Items[i].Cells[msgcolumn].Text="<img src=../images/aaloo.gif border=0 />";
					dg.Items[i].Cells[msgcolumn].ToolTip="Timeline Exceed";
					dg.Items[i].Cells[msgcolumn].ForeColor=System.Drawing.Color.Red;

				}
				else
				{
					dg.Items[i].Cells[msgcolumn].Text="Undefined";
					dg.Items[i].Cells[msgcolumn].ForeColor=System.Drawing.Color.Gray;
				}
			}
		}

		public void FillGrid()
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			string conQuery="select f.fdesigid,so.specialistno from t_functionaldesignation f,t_designationhistory h,t_specialist s,t_specialistowner so where h.fdesigid=f.fdesigid and h.pcode='"+Session["user_id"].ToString()+"' and h.isactive=1 and so.s_fdesigid=f.fdesigid and s.specialistno=so.specialistno";
			SqlCommand cmd=new SqlCommand(conQuery,con);
			SqlDataReader rd=cmd.ExecuteReader();
			bool flag=false;
			string getIDs="";
			string FdID="";
			while(rd.Read())
			{
				getIDs+=rd[1].ToString()+",";
				FdID=rd[0].ToString();
			}
			rd.Close();
			if(getIDs.Length>0)
			{
				getIDs=getIDs.Remove(getIDs.Length-1,1);
			}
			string orderby=" order by e.requestdate asc";
			if(getIDs.Length>0)
			{
				//conQuery="select e.fieldno,e.fieldname,e.requestforchange,e.requestforchangeid,em.name as requisitecode,e.requestdate,e.requesttimeline,e.documentid,fs.sp_id,e.requisitecode as code,e.rid as rID from t_employeerequests e,t_fieldsmgt f,t_fieldmgtspecialist fs,t_employee em where e.requeststatus=1 and e.fieldno=f.f_id and f.f_id=fs.f_id and fs.sp_id in("+getIDs+") and em.pcode=e.requisitecode and em.del=1 order by e.requestdate asc";
				conQuery="select e.fieldno,e.fieldname,e.requestforchange,e.requestforchangeid,em.name as requisitecode,e.requestdate,e.requesttimeline,e.documentid,fs.sp_id,e.requisitecode as code,e.rid as rID from t_employeerequests e,t_fieldsmgt f,t_fieldmgtspecialist fs,t_employee em,t_designation d,t_department dept,t_city c,t_categorization ct where ct.cat_id=d.category and em.station=c.cityid and em.pcode=e.requisitecode and em.desigid=d.desigid and dept.deptid=d.deptid and e.requeststatus=1 and e.fieldno=f.f_id and f.f_id=fs.f_id and fs.sp_id in ("+getIDs+") and em.pcode=e.requisitecode and em.del=1";
				
				cmd=new SqlCommand("select distinct d.s_deptid from t_specialist s,t_specialistowner so,t_specialistdept d where so.specialistno=s.specialistno and s.specialistno in ("+getIDs+") and so.s_fdesigid="+FdID+" and s.specialistno=d.specialistno",con);
				rd=cmd.ExecuteReader();
				string dept="";
				while(rd.Read())
				{
					dept+=rd[0].ToString()+",";
				}
				rd.Close();
				if(dept.Length>0)
				{
					flag=true;
					dept=dept.Remove(dept.Length-1,1);
					conQuery+=" and d.deptid in ("+dept+")";
				}
				cmd=new SqlCommand("select distinct ss.s_stationid from t_specialist s,t_specialistowner so,t_specialiststation ss where so.specialistno=s.specialistno and s.specialistno in ("+getIDs+") and so.s_fdesigid="+FdID+" and s.specialistno=ss.specialistno",con);
				rd=cmd.ExecuteReader();
				string station="";
				while(rd.Read())
				{
					station+=rd[0].ToString()+",";
				}
				rd.Close();
				if(station.Length>0)
				{
					flag=true;
					station=station.Remove(station.Length-1,1);
					conQuery+=" and cityid in ("+station+")";
				}
				cmd=new SqlCommand("select distinct ss.s_catid from t_specialist s,t_specialistowner so,t_specialistcat ss where so.specialistno=s.specialistno and s.specialistno in ("+getIDs+") and so.s_fdesigid="+FdID+" and s.specialistno=ss.specialistno",con);
				rd=cmd.ExecuteReader();
				string cat="";
				while(rd.Read())
				{
					cat+=rd[0].ToString()+",";
				} 
				rd.Close();
				if(cat.Length>0)
				{
					flag=true;
					cat=cat.Remove(cat.Length-1,1);
					conQuery+=" and d.category in("+cat+")";
				}
				if(flag)
				{
					conQuery+=orderby;
					SqlDataAdapter dr=new SqlDataAdapter(conQuery,con);
					DataSet ds=new DataSet();
					dr.Fill(ds,"Fields");
					this.dgMyRequest.DataSource=ds;
					this.dgMyRequest.DataBind();
					con.Close();
					SetStatus();
				}
			}
		}


		public void SetStatus()
		{
			for(int i=0;i<this.dgMyRequest.Items.Count;i++)
			{
				string rDate="";
				rDate=this.dgMyRequest.Items[i].Cells[8].Text;
				string []sDate=rDate.Split(' ');
				rDate=sDate[0];
				TimeSpan Val=DateTime.Parse(rDate).Subtract(DateTime.Now);
				Label lbl=(Label)this.dgMyRequest.Items[i].FindControl("lblStatus");
				if(Val.Days>=0)
				{
					lbl.Text="Inprocess";
				}
				else if(Val.Days < 0)
				{
					lbl.Text="<img src=../images/aaloo.gif border=0 />";
					lbl.ToolTip="Timeline Exceed";
					//lbl.ForeColor=Color.Red;
				}
				else
				{
					lbl.Text="Undefined";
				}
			}
			//this.dgMyRequest.DataBind();
		}

		private void FillReqGrid2(DataGrid dg, string table, string field, string newMsg, string changeMsg, string removalMsg)
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			// Get FD and Specialist
			string sql="SELECT f.fdesigid, so.SpecialistNo " +
				" FROM dbo.t_FunctionalDesignation AS f INNER JOIN " +
				" dbo.t_DesignationHistory AS h ON f.fdesigid = h.fdesigid INNER JOIN " +
				" dbo.t_SpecialistOwner AS so ON f.fdesigid = so.s_fdesigid INNER JOIN " +
				" dbo.t_Specialist AS s ON so.SpecialistNo = s.SpecialistNo " +
				" WHERE (h.pcode = '"+Session["user_id"].ToString()+"') AND (h.isactive = 1)";
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			string fdid=ds.Tables[0].Rows[0]["fdesigid"].ToString();
			string getids="";
			for(int j=0;j<ds.Tables[0].Rows.Count;j++)
				getids+=ds.Tables[0].Rows[j]["SpecialistNo"].ToString()+",";
			if(getids.Length>0)
				getids=getids.Remove(getids.Length-1,1);




		}
		private void FillReqGrid(DataGrid dg, string table, string field, string newMsg, string changeMsg, string removalMsg)
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
		
			string sql2="SELECT f.fdesigid, so.SpecialistNo " +
				" FROM dbo.t_FunctionalDesignation AS f INNER JOIN " +
				" dbo.t_DesignationHistory AS h ON f.fdesigid = h.fdesigid INNER JOIN " +
				" dbo.t_SpecialistOwner AS so ON f.fdesigid = so.s_fdesigid INNER JOIN " +
				" dbo.t_Specialist AS s ON so.SpecialistNo = s.SpecialistNo " +
				" WHERE (h.pcode = '"+Session["user_id"].ToString()+"') AND (h.isactive = 1) ";

			SqlDataAdapter da=new SqlDataAdapter(sql2,conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			if(ds.Tables[0].Rows.Count>0)
			{
				string fdid=ds.Tables[0].Rows[0]["fdesigid"].ToString();
				string SpecialistNos="";
				for(int j=0;j<ds.Tables[0].Rows.Count;j++)
					SpecialistNos+="'"+ds.Tables[0].Rows[j]["SpecialistNo"].ToString()+"',";
				if(SpecialistNos.Length>0)
					SpecialistNos=SpecialistNos.Remove(SpecialistNos.Length-1,1);



				/*string sqlDept=" SELECT DISTINCT spd.s_deptid " +
					" FROM dbo.t_SpecialistOwner AS so INNER JOIN " +
					" dbo.t_specialistdept AS spd ON so.SpecialistNo = spd.SpecialistNo " +
					" WHERE (so.SpecialistNo IN " +
					" (SELECT so.SpecialistNo " +
					" FROM dbo.t_FunctionalDesignation AS f INNER JOIN " +
					" dbo.t_DesignationHistory AS h ON f.fdesigid = h.fdesigid INNER JOIN " +
					" dbo.t_SpecialistOwner AS so ON f.fdesigid = so.s_fdesigid INNER JOIN " +
					" dbo.t_Specialist AS s ON so.SpecialistNo = s.SpecialistNo " +
					" WHERE (h.pcode = '"+Session["user_id"]+"') AND (h.isactive = 1))) ";*/
			
				string sqlDept="SELECT DISTINCT d.s_deptid " +
					" FROM dbo.t_Specialist AS s INNER JOIN " +
					" dbo.t_SpecialistOwner AS so ON s.SpecialistNo = so.SpecialistNo INNER JOIN " +
					" dbo.t_specialistdept AS d ON s.SpecialistNo = d.SpecialistNo " +
					" WHERE (s.SpecialistNo IN ("+SpecialistNos+")) AND (so.s_fdesigid = "+fdid+") ";

			
				da=new SqlDataAdapter(sqlDept,conn);
				ds.Tables.Clear();
				//DataSet ds=new DataSet();
				da.Fill(ds);
				string dept="";
				for(int j=0;j<ds.Tables[0].Rows.Count;j++)
					dept+=ds.Tables[0].Rows[j]["s_deptid"].ToString()+",";
						
				if(dept.Length>0)
					dept=dept.Remove(dept.Length-1,1);
			
				ds.Tables.Clear();

				/*string sqlStation=" SELECT DISTINCT s_stationid " +
					" FROM dbo.t_specialiststation AS spd " +
					" WHERE (SpecialistNo IN " +
					" (SELECT so.SpecialistNo " +
					" FROM dbo.t_FunctionalDesignation AS f INNER JOIN " +
					" dbo.t_DesignationHistory AS h ON f.fdesigid = h.fdesigid INNER JOIN " +
					" dbo.t_SpecialistOwner AS so ON f.fdesigid = so.s_fdesigid INNER JOIN " +
					" dbo.t_Specialist AS s ON so.SpecialistNo = s.SpecialistNo " +
					" WHERE (h.pcode = '"+Session["user_id"]+"') AND (h.isactive = 1))) ";*/

				string sqlStation="SELECT DISTINCT ss.s_stationid " +
					" FROM dbo.t_Specialist AS s INNER JOIN " +
					" dbo.t_SpecialistOwner AS so ON s.SpecialistNo = so.SpecialistNo INNER JOIN " +
					" dbo.t_specialiststation AS ss ON s.SpecialistNo = ss.SpecialistNo " +
					" WHERE (s.SpecialistNo in ("+SpecialistNos+")) AND (so.s_fdesigid = "+fdid+") ";

				da=new SqlDataAdapter(sqlStation,conn);
				da.Fill(ds);

				string station="";
				for(int j=0;j<ds.Tables[0].Rows.Count;j++)
					station+=ds.Tables[0].Rows[j]["s_stationid"].ToString()+",";
			
				if(station.Length>0)
					station=station.Remove(station.Length-1,1);

				ds.Tables.Clear();

				/*string sqlCategory=" SELECT DISTINCT s_catid " +
					" FROM dbo.t_specialistcat " +
					" WHERE (SpecialistNo IN " +
					" (SELECT so.SpecialistNo " +
					" FROM dbo.t_FunctionalDesignation AS f INNER JOIN " +
					" dbo.t_DesignationHistory AS h ON f.fdesigid = h.fdesigid INNER JOIN " +
					" dbo.t_SpecialistOwner AS so ON f.fdesigid = so.s_fdesigid INNER JOIN " +
					" dbo.t_Specialist AS s ON so.SpecialistNo = s.SpecialistNo " +
					" WHERE (h.pcode = '"+Session["user_id"]+"') AND (h.isactive = 1))) ";*/
				string sqlCategory=" SELECT DISTINCT ss.s_catid " +
					" FROM dbo.t_Specialist AS s INNER JOIN " +
					" dbo.t_SpecialistOwner AS so ON s.SpecialistNo = so.SpecialistNo INNER JOIN " +
					" dbo.t_specialistcat AS ss ON s.SpecialistNo = ss.SpecialistNo " +
					" WHERE (s.SpecialistNo IN ("+SpecialistNos+")) AND (so.s_fdesigid = "+fdid+") ";

				da=new SqlDataAdapter(sqlCategory,conn);
				da.Fill(ds);
				string cat="";
				for(int j=0;j<ds.Tables[0].Rows.Count;j++)
					cat+=ds.Tables[0].Rows[j]["s_catid"].ToString()+",";
			
				if(cat.Length>0)
					cat=cat.Remove(cat.Length-1,1);
			
				if(station!="")
					station=" AND (em.station IN ("+station+")) ";

				if(dept!="")
					dept=" AND (dbo.t_Department.deptid IN ("+dept+"))";

				if(cat!="")
					cat=" AND (dbo.t_Designation.category IN ("+cat+")) ";

			
				if(!(dept=="" && cat=="" && station==""))
				{
					string sql=" SELECT er.reqid, ISNULL(er."+field+", - 1) AS "+field+", er.pcode, er.c_at, " +
						" CASE er.Addflag WHEN 1 THEN '"+newMsg+"' WHEN 2 THEN '"+changeMsg+"' WHEN 3 THEN '"+removalMsg+"' END AS AddFlag, er.AppFlag, " +
						" em.name, DATEADD(day, f.timeline, er.c_at) AS Timeline " +
						" FROM dbo.t_fieldsmgt AS f INNER JOIN " +
						" dbo."+table+" AS er ON f.f_id = er.wfid INNER JOIN " +
						" dbo.t_fieldmgtspecialist AS fs ON f.f_id = fs.f_id INNER JOIN " +
						" dbo.t_Employee AS em ON er.pcode = em.pcode INNER JOIN " +
						" dbo.t_Designation ON em.desigid = dbo.t_Designation.desigid INNER JOIN " +
						" dbo.t_Department ON dbo.t_Designation.deptid = dbo.t_Department.deptid " +
						" WHERE (er.AppFlag = 2) AND fs.sp_id IN ("+SpecialistNos+")"+ cat + station + dept ;

					ds.Tables.Clear();

			
					//conn.Open();
					da=new SqlDataAdapter(sql,conn);
					//DataSet ds=new DataSet();
					da.Fill(ds);
					dg.DataSource=ds.Tables[0].DataSet;
					dg.DataBind();
					SetGridStatus(dg,5,6);
				}
			}
		}

		private void Page_Load(object sender, System.EventArgs e)
		{
			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			if(IsPageAccessAllowed())
			{
				if (GeoSecurity.isControlVisible(2,58,Session["user_id"].ToString(),"View"))
				{
					imgPulse.Visible=true;
				}
				else
				{
				 imgPulse.Visible=false;
				}
				if(!IsPostBack)
				{
					tabEduDetail.Visible=false;
					tabFamilyDetail.Visible=false;
					FillGrid();
					//FillEduGrid();
					FillReqGrid(Datagrid2,"t_educationInfoRequest","eduinfoid","New Education","Change Education Info","Removal of Education");
					FillReqGrid(Datagrid1,"t_FamilyDetailsRequest","fdid","New Family Member","Change of Family Member Info", "Removal of Family Member");
					//FillFamilyGrid();
					//FillTrainingGrid();
					FillReqGrid(dgTraining,"cv_TrainingReq","trainingid","New Training","Change of Training Info","Removal of Training");
					FillReqGrid(dgExp,"cv_ExperinceReq","ExpID","New Experience","Change Experience Info","Removal of Experience");
					FillGrievanceGrid(); 
					GetClosingCode();
					this.btnSend.Attributes.Add("onclick","return confirm('Are You Sure To Post This Request?');");
				}
			}
			else
			{
			 Response.Redirect("ErrorPage.aspx");
			}
		}

		public void GetClosingCode()
		{
			

			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			ListItem itm0=new ListItem("Select--Closing Code","0");
			this.ddClosingCode.Items.Add(itm0);
			SqlCommand cmd=new SqlCommand("select * from t_grievanceclosingcode where isactive=1 and type=1",con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
				this.ddClosingCode.Items.Add(itm);
			}
			rd.Close();
			con.Close();
			con.Dispose();
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.imgRequest.Click += new System.Web.UI.ImageClickEventHandler(this.imgRequest_Click);
			this.imgReopen.Click += new System.Web.UI.ImageClickEventHandler(this.imgReopen_Click);
			this.imgcCGrievance.Click += new System.Web.UI.ImageClickEventHandler(this.imgcCGrievance_Click);
			this.imgPulse.Click += new System.Web.UI.ImageClickEventHandler(this.imgPulse_Click);
			this.dgMyRequest.SelectedIndexChanged += new System.EventHandler(this.dgMyRequest_SelectedIndexChanged);
			this.dgTraining.SelectedIndexChanged += new System.EventHandler(this.dgTraining_SelectedIndexChanged);
			this.Datagrid2.SelectedIndexChanged += new System.EventHandler(this.Datagrid2_SelectedIndexChanged);
			this.dgExp.SelectedIndexChanged += new System.EventHandler(this.dgExp_SelectedIndexChanged);
			this.Datagrid1.SelectedIndexChanged += new System.EventHandler(this.Datagrid1_SelectedIndexChanged);
			this.btnAccept.Click += new System.EventHandler(this.btnAccept_Click);
			this.btnReject.Click += new System.EventHandler(this.btnReject_Click);
			this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
			this.btnSend.Click += new System.EventHandler(this.btnSend_Click);
			this.dgMyGrievance.SelectedIndexChanged += new System.EventHandler(this.dgMyGrievance_SelectedIndexChanged);
			this.ddClosingCode.SelectedIndexChanged += new System.EventHandler(this.ddClosingCode_SelectedIndexChanged);
			this.lnkRedirect.Click += new System.EventHandler(this.lnkRedirect_Click);
			this.ddGrievance.SelectedIndexChanged += new System.EventHandler(this.ddGrievance_SelectedIndexChanged);
			this.ddSubGrivance.SelectedIndexChanged += new System.EventHandler(this.ddSubGrivance_SelectedIndexChanged);
			this.btnGrievanceSubmit.Click += new System.EventHandler(this.btnGrievanceSubmit_Click);
			this.Button8.Click += new System.EventHandler(this.Button8_Click);
			this.Button7.Click += new System.EventHandler(this.Button7_Click);
			this.Button5.Click += new System.EventHandler(this.Button5_Click);
			this.Button6.Click += new System.EventHandler(this.Button6_Click);
			this.Button4.Click += new System.EventHandler(this.Button4_Click);
			this.Button3.Click += new System.EventHandler(this.Button3_Click);
			this.Button1.Click += new System.EventHandler(this.Button1_Click);
			this.Button2.Click += new System.EventHandler(this.Button2_Click);
			this.cmdAccTraining.Click += new System.EventHandler(this.cmdAccTraining_Click);
			this.cmdRejTraining.Click += new System.EventHandler(this.cmdRejTraining_Click);
			this.cmdCancelTraining.Click += new System.EventHandler(this.cmdCancelTraining_Click);
			this.cmdSubmitTraining.Click += new System.EventHandler(this.cmdSubmitTraining_Click);
			this.Button11.Click += new System.EventHandler(this.Button11_Click);
			this.Button9.Click += new System.EventHandler(this.Button9_Click);
			this.Button10.Click += new System.EventHandler(this.Button10_Click);
			this.dgMyccGrievance.SelectedIndexChanged += new System.EventHandler(this.dgMyccGrievance_SelectedIndexChanged);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		private void dgMyRequest_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			// Reset Remaining Grid Selected Index
			int idx=dgMyRequest.SelectedIndex;
			dgExp.SelectedIndex=-1;
			Datagrid1.SelectedIndex=-1;
			Datagrid2.SelectedIndex=-1;
			dgTraining.SelectedIndex=-1;
			dgMyRequest.SelectedIndex=-1;
			dgMyRequest.SelectedIndex=idx;
			//////////////////////////////////
			this.Label1.Visible=false;
			TD1.Visible=false;
			this.btnAccept.Enabled=true;
			this.btnReject.Enabled=true;
			this.pnlrView.Visible=true;
			
			this.lblFieldName.Text=this.dgMyRequest.Items[this.dgMyRequest.SelectedIndex].Cells[3].Text;
			this.lblFieldName.ToolTip=this.dgMyRequest.Items[this.dgMyRequest.SelectedIndex].Cells[2].Text;
			if(this.dgMyRequest.Items[this.dgMyRequest.SelectedIndex].Cells[4].Text=="&nbsp;")
			{
				this.lblRequestInformation.Text="";
			}
			else
			{
				this.lblRequestInformation.Text=this.dgMyRequest.Items[this.dgMyRequest.SelectedIndex].Cells[4].Text;
			}
			this.lblRequestInformation.ToolTip=this.dgMyRequest.Items[this.dgMyRequest.SelectedIndex].Cells[5].Text;
			this.lblRequestDate.Text=DateTime.Parse(this.dgMyRequest.Items[this.dgMyRequest.SelectedIndex].Cells[7].Text).ToString("d MMM,yyyy");
			string fieldNo=this.dgMyRequest.Items[this.dgMyRequest.SelectedIndex].Cells[2].Text;
			if(fieldNo=="43")
			{
				lnkvPicture.Visible=true;
				this.lnkvPicture.Attributes.Add("onclick","window.showModalDialog('ViewDocuments.aspx?docID=123&pcode="+this.dgMyRequest.Items[this.dgMyRequest.SelectedIndex].Cells[12].Text+"','dialogWidth:900px; dialogHeight:800px;status:no')");
			}
			else
			{
				lnkvPicture.Visible=false;
			}
			this.lblcInformation.ToolTip=this.dgMyRequest.Items[this.dgMyRequest.SelectedIndex].Cells[10].Text;
			this.lbleCode.Text=this.dgMyRequest.Items[this.dgMyRequest.SelectedIndex].Cells[12].Text;
			this.lblRequestDate.ToolTip=this.dgMyRequest.Items[this.dgMyRequest.SelectedIndex].Cells[1].Text;
			string docID=this.dgMyRequest.Items[this.dgMyRequest.SelectedIndex].Cells[9].Text;
			if(docID=="&nbsp;")
			{
				col1.Visible=false;
				col2.Visible=false;
				this.lnkView.Attributes.Clear();
			}
			else
			{
				col1.Visible=true;
				col2.Visible=true;
				this.lnkView.Attributes.Add("onclick","window.showModalDialog('ViewDocuments.aspx?docID="+docID+"','dialogWidth:900px; dialogHeight:800px;status:no')");
				this.Label1.Text=docID; 
			}
			
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			string conQuery="select * from t_fieldsmgt where f_id="+fieldNo+"";
			SqlCommand cmd=new SqlCommand(conQuery,con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				this.lblVerificationCriteria.Text=rd[4].ToString();
				this.lblTimeline.Text=rd[5].ToString()+" Day(s)";
			}
			rd.Close();
			cmd=new SqlCommand("select d.documentname from t_documentsrequired d,t_fieldmgtdocEmp f where f.documentbyemployee=d.d_id and f.f_id="+fieldNo+"",con);
			rd=cmd.ExecuteReader();
			string message="<ul class='niceList'>";
			string Val="";
			while(rd.Read())
			{
				//message+="<STRONG>/STRONG> "+rd[0].ToString()+"<br>";
				Val+="<li> "+rd[0].ToString()+"</li>";
			}
			message+=Val;
			message+="</ul>";
			rd.Close();
			if(Val.Length>0)
			{
				this.lblRequiredByEmp.Text=message;
			}
			else
			{
				this.lblRequiredByEmp.Text="None";
			}
			cmd=new SqlCommand("select d.documentname from t_documentsrequired d,t_fieldmgtdocHR f where f.documentbyHR=d.d_id and f.f_id="+fieldNo+"",con);
			rd=cmd.ExecuteReader();
			message="<ul class='niceList'>";
			Val="";
			while(rd.Read())
			{
				//message+="<STRONG>/STRONG> "+rd[0].ToString()+"<br>";
				Val+="<li> "+rd[0].ToString()+"</li>";
			}
			message+=Val;
			message+="</ul>";
			rd.Close();
			if(Val.Length>0)
			{
				this.lblRequiredByHR.Text=message;
			}
			else
			{
				this.lblRequiredByHR.Text="None";
			}
			con.Close();
			CurrentStatus(fieldNo,this.dgMyRequest.Items[this.dgMyRequest.SelectedIndex].Cells[12].Text,this.dgMyRequest.Items[this.dgMyRequest.SelectedIndex].Cells[1].Text);
		}

		public void DocReqFromHR(string wfid, Label lblHRDoc, Label lblEmpDoc)
		{
			string sql=" SELECT d.documentname " +
				" FROM dbo.t_documentsrequired AS d INNER JOIN " +
				" dbo.t_fieldmgtdocHR AS f ON d.d_id = f.documentbyHR " +
				" WHERE (f.f_id = "+wfid+") ";
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			string tt="";
			for(int j=0;j<ds.Tables[0].Rows.Count;j++)
				tt+="<li>"+ds.Tables[0].Rows[j][0].ToString()+"</li>";
			if(tt!="")
				tt="<ul>"+tt+"</ul>";
			else
				tt="None";

			lblHRDoc.Text=tt;

			sql=" select d.documentname from t_documentsrequired d,t_fieldmgtdocEmp f where f.documentbyemployee=d.d_id and f.f_id="+wfid+"";
			da=new SqlDataAdapter(sql,conn);
			ds.Tables.Clear();
			da.Fill(ds);

			tt="";
			for(int j=0;j<ds.Tables[0].Rows.Count;j++)
				tt+="<li>"+ds.Tables[0].Rows[j][0].ToString()+"</li>";
			if(tt!="")
				tt="<ul>"+tt+"</ul>";
			else
				tt="None";

			lblEmpDoc.Text=tt;

			conn.Close();
			
		}

		public void CurrentStatus(string Val,string Pcode,string requestID)
		{
			string cVal;
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=null;
			SqlDataReader rd=null;
			switch(Val)
			{
				case "7":
					cmd=new SqlCommand("select name from t_employee where pcode='"+Pcode+"'",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					cVal=rd[0].ToString();
					rd.Close();
					this.lblcInformation.Text=cVal;
					break;
				case "6":
					cmd=new SqlCommand("select d.desigid,d.designation,dept.deptname from t_designation d,t_employee e,t_department dept where d.desigid=e.desigid and e.pcode='"+Pcode+"' and dept.deptid=d.deptid",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					this.lblcInformation.ToolTip=rd[0].ToString();
					this.lblcInformation.Text=rd[1].ToString()+"("+rd[2].ToString()+")";
					rd.Close();
					break;
				case "10":
					cmd=new SqlCommand("select dept.deptid,dept.deptname from t_designation d,t_employee e,t_department dept where d.desigid=e.desigid and e.pcode='"+Pcode+"' and dept.deptid=d.deptid",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					this.lblcInformation.ToolTip=rd[0].ToString();
					this.lblcInformation.Text=rd[1].ToString();
					rd.Close();
					break;
				case "8":
					cmd=new SqlCommand("select c.cityid,c.cityname from t_employee e,t_city c where pcode='"+Pcode+"' and e.station=c.cityid",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					this.lblcInformation.ToolTip=rd[0].ToString();
					this.lblcInformation.Text=rd[1].ToString();
					rd.Close();
					break;
				case "9":
					cmd=new SqlCommand("select dateofjoin from t_employee where pcode='"+Pcode+"'",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						try
						{
							this.lblcInformation.Text=DateTime.Parse(rd[0].ToString()).ToString("d,MMM,yyyy");
						}
						catch {this.lblcInformation.Text="invalid date";}
					}
					rd.Close();
					break;
				case "11":
					cmd=new SqlCommand("select extension from t_employee where pcode='"+Pcode+"'",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					this.lblcInformation.Text=rd[0].ToString();
					rd.Close();
					break;
				case "13":
					cmd=new SqlCommand("select eobino from t_employee where pcode='"+Pcode+"'",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					this.lblcInformation.Text=rd[0].ToString();
					rd.Close();
					break;
				case "16":
					cmd=new SqlCommand("select dateofbirth from t_employee where pcode='"+Pcode+"'",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						try
						{
							this.lblcInformation.Text=DateTime.Parse(rd[0].ToString()).ToString("d,MMM,yyyy");
						}
						catch {this.lblcInformation.Text="invalid date";}
					}
					rd.Close();
					break;
				case "24":
					cmd=new SqlCommand("select fathername from t_employee where pcode='"+Pcode+"'",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						try
						{
							this.lblcInformation.Text=rd[0].ToString();
						}
						catch {this.lblcInformation.Text="invalid date";}
					}
					rd.Close();
					break;
				case "17":
					cmd=new SqlCommand("select gender=case gender when 1 then 'Male' when 2 then 'Female' else 'Not Defined' end from t_employee where pcode='"+Pcode+"'",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						try
						{
							this.lblcInformation.Text=rd[0].ToString();
						}
						catch {this.lblcInformation.Text="invalid data";}
					}
					rd.Close();
					break;
				case "18":
					cmd=new SqlCommand("select bloodgroup=case bloodgroup when 1 then 'A+' when 2 then 'A-' when 3 then 'B+' when 4 then 'B-' when 5 then 'AB+' when 6 then 'AB-' when 7 then 'O+' when 8 then 'O-' else 'Not Defined' end from t_employee where pcode='"+Pcode+"'",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						try
						{
							this.lblcInformation.Text=rd[0].ToString();
						}
						catch {this.lblcInformation.Text="invalid data";}
					}
					rd.Close();
					break;
				case "26":
					cmd=new SqlCommand("select religion=case religion when 1 then 'Islam' when 2 then 'Christianity' when 3 then 'Buddhism' when 4 then 'Zoroastrian' when 5 then 'Jewish' when 6 then 'Hiduism' when 7 then 'Others' else 'Not Defined' end from t_employee where pcode='"+Pcode+"'",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						try
						{
							this.lblcInformation.Text=rd[0].ToString();
						}
						catch {this.lblcInformation.Text="invalid data";}
					}
					rd.Close();
					break;
				case "37":
					cmd=new SqlCommand("select nick from t_employee where pcode='"+Pcode+"'",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						try
						{
							this.lblcInformation.Text=rd[0].ToString();
						}
						catch {this.lblcInformation.Text="invalid data";}
					}
					rd.Close();
					break;
				case "22":
					cmd=new SqlCommand("select address from t_employee where pcode='"+Pcode+"'",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						try
						{
							this.lblcInformation.Text=rd[0].ToString();
						}
						catch {this.lblcInformation.Text="invalid data";}
					}
					rd.Close();
					break;
				case "25":
					cmd=new SqlCommand("select maritalstatus=case maritialstatus when 1 then 'Single' when 2 then 'Married' when 3 then 'Divorced' when 4 then 'Widow' when 5 then 'Separated' else 'Not Defined' end from t_employee where pcode='"+Pcode+"'",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						try
						{
							this.lblcInformation.Text=rd[0].ToString();
						}
						catch {this.lblcInformation.Text="invalid data";}
					}
					rd.Close();
					break;
				case "29":
					cmd=new SqlCommand("select email_personal from t_employee where pcode='"+Pcode+"'",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						try
						{
							this.lblcInformation.Text=rd[0].ToString();
						}
						catch {this.lblcInformation.Text="invalid data";}
					}
					rd.Close();
					break;
				case "34":
					cmd=new SqlCommand("select ntnno from t_employee where pcode='"+Pcode+"'",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						try
						{
							this.lblcInformation.Text=rd[0].ToString();
						}
						catch {this.lblcInformation.Text="invalid data";}
					}
					rd.Close();
					break;
				case "27":
					cmd=new SqlCommand("select telephone from t_employee where pcode='"+Pcode+"'",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						try
						{
							this.lblcInformation.Text=rd[0].ToString();
						}
						catch {this.lblcInformation.Text="invalid data";}
					}
					rd.Close();
					break;
				case "28":
					cmd=new SqlCommand("select mobile from t_employee where pcode='"+Pcode+"'",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						try
						{
							this.lblcInformation.Text=rd[0].ToString();
						}
						catch {this.lblcInformation.Text="invalid data";}
					}
					rd.Close();
					break;
				case "35":
					cmd=new SqlCommand("select Nationality=case cast(e.nationality as varchar) when e.nationality then (select c.nationality from t_country c where e.nationality=c.countryid)end from t_employee e where pcode='"+Pcode+"'",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						try
						{
							this.lblcInformation.Text=rd[0].ToString();
						}
						catch {this.lblcInformation.Text="invalid data";}
					}
					rd.Close();
					break;
				case "36":
					cmd=new SqlCommand("select Nationality=case cast(e.nationality2 as varchar) when e.nationality2 then (select c.nationality from t_country c where e.nationality2=c.countryid)end from t_employee e where pcode='"+Pcode+"'",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						try
						{
							this.lblcInformation.Text=rd[0].ToString();
						}
						catch {this.lblcInformation.Text="invalid data";}
					}
					rd.Close();
					break;
				case "19":
					cmd=new SqlCommand("select passportno from t_employee where pcode='"+Pcode+"'",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						try
						{
							this.lblcInformation.Text=rd[0].ToString();
						}
						catch {this.lblcInformation.Text="invalid data";}
					}
					rd.Close();
					break;
				case "30":
					cmd=new SqlCommand("select kin from t_employee where pcode='"+Pcode+"'",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						try
						{
							this.lblcInformation.Text=rd[0].ToString();
						}
						catch {this.lblcInformation.Text="invalid data";}
					}
					rd.Close();
					break;
				case "21":
					cmd=new SqlCommand("select nic_new from t_employee where pcode='"+Pcode+"'",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						try
						{
							this.lblcInformation.Text=rd[0].ToString();
						}
						catch {this.lblcInformation.Text="invalid data";}
					}
					rd.Close();
					break;
				case "20":
					cmd=new SqlCommand("select nic_old from t_employee where pcode='"+Pcode+"'",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						try
						{
							this.lblcInformation.Text=rd[0].ToString();
						}
						catch {this.lblcInformation.Text="invalid data";}
					}
					rd.Close();
					break;
				case "32":
					cmd=new SqlCommand("select bankaccountno from t_employee where pcode='"+Pcode+"'",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						try
						{
							this.lblcInformation.Text=rd[0].ToString();
						}
						catch {this.lblcInformation.Text="invalid data";}
					}
					rd.Close();
					break;
				case "33":
					cmd=new SqlCommand("select bankaccountdetails from t_employee where pcode='"+Pcode+"'",con);
					rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						try
						{
							this.lblcInformation.Text=rd[0].ToString();
						}
						catch {this.lblcInformation.Text="invalid data";}
					}
					rd.Close();
					break;
			}
			con.Close();
		}

		private void Accept(Button ab, Button rb, Label act, TextBox msg, Button submit, string field)
		{
			ab.Enabled=false;
			rb.Enabled=false;
			//TD1.Visible=true;
			act.Text="Acceptance Message To Member TeamGEO";
			msg.Text="";
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select replyonaccept from t_fieldsmgt where f_id="+field,con);
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			if(rd.HasRows)
			{
				msg.Text=rd[0].ToString();
			}
			rd.Close();
			con.Close();
			submit.Enabled=true;

		}

		/*
		private void btnAccept_Click(object sender, System.EventArgs e)
		{
			this.btnAccept.Enabled=false;
			this.btnReject.Enabled=false;
			//TD1.Visible=true;
			this.lblAction.Text="Acceptance Message To Member TeamGEO";
			this.txtMessage.Text="";
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select replyonaccept from t_fieldsmgt where f_id="+this.lblFieldName.ToolTip+"",con);
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			if(rd.HasRows)
			{
				this.txtMessage.Text=rd[0].ToString();
			}
			rd.Close();
			con.Close();

		}*/


		private void btnAccept_Click(object sender, System.EventArgs e)
		{
			this.btnAccept.Enabled=false;
			this.btnReject.Enabled=false;
			TD1.Visible=true;
			this.lblAction.Text="Acceptance Message To Member TeamGEO";
			this.txtMessage.Text="";
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select replyonaccept from t_fieldsmgt where f_id="+this.lblFieldName.ToolTip+"",con);
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			if(rd.HasRows)
			{
				this.txtMessage.Text=rd[0].ToString();
			}
			rd.Close();
			con.Close();
		}

		private void btnCancel_Click(object sender, System.EventArgs e)
		{
			this.btnAccept.Enabled=true;
			this.btnReject.Enabled=true;
			TD1.Visible=false;
			this.lblAction.Text="";
			this.txtMessage.Text="";
			this.pnlrView.Visible=false;
			ResetPanels();
		}



		private void Rejection(Button ab, Button rb, Label ac, TextBox msg,Button submit, string fn)
		{
			ab.Enabled=false;
			rb.Enabled=false;
			ac.Text="Rejection Message To Member TeamGEO";
			msg.Text="";
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select replyonreject from t_fieldsmgt where f_id="+fn,con);
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			if(rd.HasRows)
			{
				msg.Text=rd[0].ToString();
			}
			rd.Close();
			con.Close();
			submit.Enabled=true;
		}

		/*private void btnReject_Click(object sender, System.EventArgs e)
		{
			Rejection(btnAccept,btnReject,lblAction,txtMessage, btnSend, lblFieldName.ToolTip.ToString());

//			this.btnAccept.Enabled=false;
//			this.btnReject.Enabled=false;
//			TD1.Visible=true;
//			this.lblAction.Text="Rejection Message To Member TeamGEO";
//			this.txtMessage.Text="";
//			con=new SqlConnection(Connection.ConnectionString);
//			con.Open();
//			SqlCommand cmd=new SqlCommand("select replyonreject from t_fieldsmgt where f_id="+this.lblFieldName.ToolTip+"",con);
//			SqlDataReader rd=cmd.ExecuteReader();
//			rd.Read();
//			if(rd.HasRows)
//			{
//				this.txtMessage.Text=rd[0].ToString();
//			}
//			rd.Close();
//			con.Close();
		}
*/
		private void btnReject_Click(object sender, System.EventArgs e)
		{
			this.btnAccept.Enabled=false;
			this.btnReject.Enabled=false;
			TD1.Visible=true;
			this.lblAction.Text="Rejection Message To Member TeamGEO";
			this.txtMessage.Text="";
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select replyonreject from t_fieldsmgt where f_id="+this.lblFieldName.ToolTip+"",con);
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			if(rd.HasRows)
			{
				this.txtMessage.Text=rd[0].ToString();
			}
			rd.Close();
			con.Close();
		}

		private void Datagrid2_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			

			// Reset Remaining Grid Selected Index
			int idx=Datagrid2.SelectedIndex;
			dgExp.SelectedIndex=-1;
			Datagrid1.SelectedIndex=-1;
			Datagrid2.SelectedIndex=-1;
			dgTraining.SelectedIndex=-1;
			dgMyRequest.SelectedIndex=-1;
			Datagrid2.SelectedIndex=idx;
			//////////////////////////////////
			
			//pnlMyReq.Visible=false;
			pnlEdu.Visible=true;
			pnlFamily.Visible=false;
			pnlTraining.Visible=false;
			pnlExp.Visible=false;


			Button8.Enabled=true;
			Button7.Enabled=true;
			Label7.Text="";
			TextBox2.Text="";
			Button6.Enabled=false;
			Button5.Enabled=true;

			tabEduDetail.Visible=true;
			Label13.Text=Datagrid2.SelectedItem.Cells[1].Text;
			string sql=" SELECT er.reqid,er.filename, er.eduinfoid, er.pcode, CASE er.degree WHEN - 1 THEN otherdegree ELSE "+
				" (SELECT dd.degree "+
				" FROM t_degree dd "+
				" WHERE dd.id = er.degree) END AS Degree, er.degree AS DegreeID, CASE institute WHEN - 1 THEN otherinstitute ELSE "+
				" (SELECT ii.institute_name "+
				" FROM t_institute ii "+
				" WHERE ii.id = er.institute) END AS Institute, er.institute AS InstituteID, er.DurationFrom, "+
				" CASE er.durationTo WHEN - 1 THEN 'In Progress' ELSE CAST(er.durationTo AS varchar) END AS DurationTo, er.result, er.majors, er.achievements, "+
				" er.isactive, er.createdby, er.createddate, er.modifiedby, er.c_at, "+
				" CASE er.Addflag WHEN 1 THEN 'Request for new Educational Degree' WHEN 2 THEN 'Request for Change' WHEN 3 THEN 'Request for Remvoe' END AS "+
				" AddFlag, er.AppFlag, er.AppRejDate, er.RejCode, er.AppRejBy, em.name, DATEADD(d, f.timeline, er.c_at) AS Timeline, f.vcriteria, f.vrequired, f.tooltip, "+
				" f.replyatrequest, f.replyonaccept, f.replyonreject, dsg.designation, dpt.deptname "+
				" FROM dbo.t_fieldsmgt AS f INNER JOIN "+
				" dbo.t_educationInfoRequest AS er ON f.f_id = er.wfid INNER JOIN "+
				" dbo.t_fieldmgtspecialist AS fs ON f.f_id = fs.f_id INNER JOIN "+
				" dbo.t_Employee AS em ON er.pcode = em.pcode INNER JOIN "+
				" dbo.t_Designation AS dsg ON em.desigid = dsg.desigid INNER JOIN "+
				" dbo.t_Department AS dpt ON dsg.deptid = dpt.deptid "+
				" WHERE (er.reqid = "+Label13.Text+")";

			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			SetText(ds,Label14,"AddFlag");	//			Label14.Text=Datagrid2.SelectedItem.Cells[3].Text;
			SetText(ds,Label15,"pcode");	//			Label15.Text=ds.Tables[0].Rows[0]["pcode"].ToString();
			SetText(ds,Label16,"name");		//			Label16.Text=ds.Tables[0].Rows[0]["name"].ToString();
			SetText(ds,Label17,"designation");	//		Label17.Text=ds.Tables[0].Rows[0]["designation"].ToString()+" ["+ds.Tables[0].Rows[0]["deptname"].ToString()+"]";
			SetText(ds,Label3,"deptname");
			SetText(ds,Label18,"degree");//				Label18.Text=ds.Tables[0].Rows[0]["degree"].ToString();
			SetText(ds,Label19,"institute");	//		Label19.Text=ds.Tables[0].Rows[0]["institute"].ToString();
			SetText(ds,Label20,"majors");//				Label20.Text=ds.Tables[0].Rows[0]["majors"].ToString();
			SetText(ds,Label4,"result");
			string docID=ds.Tables[0].Rows[0]["filename"].ToString();
			docID=docID.Replace(".jpg","");

			Label21.Text=ds.Tables[0].Rows[0]["DurationFrom"].ToString()+" to ";
			if(ds.Tables[0].Rows[0]["DurationTo"].ToString()=="-1")
				Label21.Text+="In progress";
			else
				Label21.Text+=ds.Tables[0].Rows[0]["DurationTo"].ToString();
			SetText(ds,Label22,"achievements");//			Label22.Text=ds.Tables[0].Rows[0]["achievements"].ToString();
			Label23.Text=DateTime.Parse(ds.Tables[0].Rows[0]["timeline"].ToString()).ToString("d MMM,yyyy");
			SetText(ds,Label24,"vcriteria");//			Label24.Text=ds.Tables[0].Rows[0]["vcriteria"].ToString();
			
			string af=ds.Tables[0].Rows[0]["addflag"].ToString();
			
			if(af=="Request for Change")
			{
				string sql2=" SELECT eduinfoid, CASE degree WHEN - 1 THEN otherdegree ELSE "+
					" (SELECT d .degree "+
					" FROM t_degree d "+
					" WHERE d .id = ed.degree) END AS Degree, CASE institute WHEN - 1 THEN otherinstitute ELSE "+
					" (SELECT ii.institute_name "+
					" FROM t_institute ii "+
					" WHERE ii.id = ed.institute) END AS Instiute, DurationFrom, CASE ed.DurationTo WHEN - 1 THEN 'In Progress' ELSE CAST(DurationTo AS varchar) "+
					" END AS DurationTo, result, majors, achievements, isactive "+
					" FROM dbo.t_educationInfo AS ed "+
					" WHERE (eduinfoid = "+ds.Tables[0].Rows[0]["eduinfoid"].ToString()+")";
				SqlDataAdapter da2=new SqlDataAdapter(sql2,conn);
				da.Fill(ds,"edu");
				Label18.Text=ds.Tables[0].Rows[0]["degree"].ToString()+"<br><font color=gray>"+
					ds.Tables[1].Rows[0]["degree"].ToString()+"</font>";

				Label19.Text=ds.Tables[0].Rows[0]["institute"].ToString()+"<br><font color=gray>"+
					ds.Tables[1].Rows[0]["institute"].ToString()+"</font>";

				Label20.Text=ds.Tables[0].Rows[0]["majors"].ToString()+"<br><font color=gray>"+
					ds.Tables[1].Rows[0]["majors"].ToString()+"</font>";

				Label22.Text=ds.Tables[0].Rows[0]["achievements"].ToString()+"<br><font color=gray>"+
					ds.Tables[1].Rows[0]["achievements"].ToString()+"</font>";

				Label21.Text=ds.Tables[0].Rows[0]["DurationFrom"].ToString()+" to ";
				if(ds.Tables[0].Rows[0]["DurationTo"].ToString()=="-1")
					Label21.Text+="In progress";
				else
					Label21.Text+=ds.Tables[0].Rows[0]["DurationTo"].ToString();


				Label21.Text+="<br/><font color=gray>"+ds.Tables[1].Rows[0]["DurationFrom"].ToString()+ " to " + ds.Tables[1].Rows[0]["DurationTo"].ToString()+"</font>";
				Button8.Enabled=true;
				Button7.Enabled=true;
				Label11.Text="";
				TextBox2.Text="";
				Button6.Enabled=false;

				if(docID=="")
				{
					Label12.Attributes.Clear();
					Label12.Text="&nbsp;";
					
				}
				else
				{
					Label12.Attributes.Add("onclick","window.showModalDialog('ViewDocuments.aspx?docID="+docID+"','dialogWidth:900px; dialogHeight:800px;status:no')");
					Label12.Text="View";
				}

				DocReqFromHR("38",Label25, Label26);

			}
			else if (af=="Request for new Educational Degree")
			{
				//Label21.Text+="<br/><font color=gray>"+ds.Tables[1].Rows[0]["DurationFrom"].ToString()+ " to " + ds.Tables[1].Rows[0]["DurationTo"].ToString()+"</font>";
				Button8.Enabled=true;
				Button7.Enabled=true;
				Label11.Text="";
				TextBox2.Text="";
				Button6.Enabled=false;

				if(docID=="")
				{
					Label12.Attributes.Clear();
					Label12.Text="&nbsp;";
					
				}
				else
				{
					Label12.Attributes.Add("onclick","window.showModalDialog('ViewDocuments.aspx?docID="+docID+"','dialogWidth:900px; dialogHeight:800px;status:no')");
					Label12.Text="View";
				}

				DocReqFromHR("38",Label25, Label26);

			}
		}

		private void Button8_Click(object sender, System.EventArgs e)
		{
			Accept(Button8,Button7,Label11,TextBox2,Button6,"38");
		}

		private void Button7_Click(object sender, System.EventArgs e)
		{
			Rejection(Button8,Button7,Label11,TextBox2,Button6,"38");
		}

		private void SetParameterValue(SqlCommand cmd, DataSet ds, string field, SqlDbType d)
		{
			SqlParameter _p=new SqlParameter("@"+field,d);
			_p.Value=ds.Tables[0].Rows[0][field].ToString();
			cmd.Parameters.Add(_p);
		}
		private void SetParameterValue(SqlCommand cmd, DataSet ds, string field)
		{
			cmd.Parameters.Add("@"+field,ds.Tables[0].Rows[0][field].ToString());
		}
		private void UpdateEduRequest(string reqid,string status)
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			SqlCommand cmd=new SqlCommand("update t_educationInfoRequest set AppFlag="+status+",AppRejDate=Getdate(),AppRejBy='"+Session["user_id"].ToString()+"' where reqid="+reqid,conn);
			cmd.ExecuteNonQuery();
			conn.Close();
		}

		private void UpdateRequest4Accept(SqlConnection conn, SqlTransaction tran, string tablename,string reqid, string status)
		{
			//SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			//conn.Open();
			SqlCommand cmd=new SqlCommand("update "+tablename+" set AppFlag="+status+",AppRejDate=Getdate(),AppRejBy='"+Session["user_id"].ToString()+"' where reqid="+reqid,conn);
			cmd.Transaction=tran;
			cmd.ExecuteNonQuery();
		}
		private void UpdateFamilyRequest(string reqid, string status)
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			SqlCommand cmd=new SqlCommand("update t_FamilyDetailsRequest set AppFlag="+status+",AppRejDate=Getdate(),AppRejBy='"+Session["user_id"].ToString()+"' where reqid="+reqid,conn);
			cmd.ExecuteNonQuery();
			conn.Close();
		}
		private void AcceptRejectFamilyRequest(string wfid)
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();

			SqlTransaction tran=conn.BeginTransaction();

			string wf="";
			if(Label34.Text=="Son" || Label34.Text=="Daughter")
				wf="41";
			else
				wf="40";

			string sql=" SELECT reqid, fdid, pcode, name, relationship, maritialstatus, gender, occupation, dob, isactive, Dependent, c_at, Addflag, wfid, AppFlag, AppRejDate, RejCode, "+
				" AppRejBy "+
				" FROM dbo.t_FamilyDetailsRequest "+
				" WHERE (reqid = "+Label27.Text+")";
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.SelectCommand.Transaction=tran;
			da.Fill(ds);
			string pcode=ds.Tables[0].Rows[0]["pcode"].ToString();
			string fdis=ds.Tables[0].Rows[0]["fdid"].ToString();

			//SqlDataAdapter da=new SqlDataAdapter("select replyonaccept,replyonreject from t_fieldsmgt where f_id="+wf,conn);
			//DataSet ds=new DataSet();
			//da.Fill(ds);
			//string replyonaccept=ds.Tables[0].Rows[0]["replyonaccept"].ToString();
			//string replyonreject=ds.Tables[0].Rows[0]["replyonreject"].ToString();
			//ds.Tables.Clear();

			if(Label1.Text=="Acceptance Message To Member TeamGEO")
			{
				if(Label28.Text=="Request for new Family Member")
				{
					SqlCommand cmd=new SqlCommand("insert into t_FamilyDetails(pcode,name,relationship,maritialstatus,gender,occupation,dob,isactive,Dependent,createdby,createddate) " +
						" values(@pcode,@name,@relationship,@maritialstatus,@gender,@occupation,@dob,1,@Dependent,'"+Session["user_id"].ToString()+"',getdate())",conn);
					cmd.Transaction=tran;
					SetParameterValue(cmd,ds,"pcode");
					SetParameterValue(cmd,ds,"name");
					SetParameterValue(cmd,ds,"relationship");
					SetParameterValue(cmd,ds,"maritialstatus");
					SetParameterValue(cmd,ds,"gender");
					SetParameterValue(cmd,ds,"occupation");
					SetParameterValue(cmd,ds,"dob");
					SetParameterValue(cmd,ds,"Dependent");
					cmd.ExecuteNonQuery();
					UpdateFamilyRequest(ds.Tables[0].Rows[0]["reqid"].ToString(),"1");
					Bulletin.PostReqMessage(conn,tran,TextBox1.Text,Session["user_id"].ToString(),pcode,3,"Family","t_FamilyDetailsRequest");
					tran.Commit();
				}
				else if (Label28.Text=="Request for Change")
				{
					SqlCommand cmd=new SqlCommand("update t_FamilyDetails set pcode=@pcode, name=@name,relationship=@relationship,maritialstatus=@maritialstatus,gender=@gender,occupation=@occupation,dob=@dob,Dependent=@Dependent, modifiedby='"+Session["user_id"].ToString()+"' where fdid=@fdid",conn);
					cmd.Transaction=tran;
					SetParameterValue(cmd,ds,"fdid");
					SetParameterValue(cmd,ds,"pcode");
					SetParameterValue(cmd,ds,"name");
					SetParameterValue(cmd,ds,"relationship");
					SetParameterValue(cmd,ds,"maritialstatus");
					SetParameterValue(cmd,ds,"gender");
					SetParameterValue(cmd,ds,"occupation");
					SetParameterValue(cmd,ds,"dob");
					SetParameterValue(cmd,ds,"Dependent");
					cmd.ExecuteNonQuery();
					UpdateFamilyRequest(ds.Tables[0].Rows[0]["reqid"].ToString(),"1");
					Bulletin.PostReqMessage(conn,tran,TextBox1.Text,Session["user_id"].ToString(),pcode,3,"Family","t_FamilyDetailsRequest");
					tran.Commit();

				}
				else if (Label28.Text=="Request for Remvoe")
				{
					SqlCommand cmd=new SqlCommand("update t_FamilyDetails set isactive=0, modifiedby='"+Session["user_id"].ToString()+"' where fdid=@fdid",conn);
					cmd.Transaction=tran;
					SetParameterValue(cmd,ds,"fdid");
					cmd.ExecuteNonQuery();
					UpdateFamilyRequest(ds.Tables[0].Rows[0]["reqid"].ToString(),"1");
					Bulletin.PostReqMessage(conn,tran,TextBox1.Text,Session["user_id"].ToString(),pcode,3,"Family","t_FamilyDetailsRequest");
					tran.Commit();

				}
			}
			else if (Label1.Text=="Rejection Message To Member TeamGEO")
			{
				UpdateFamilyRequest(Label27.Text,"0");
				Bulletin.PostReqMessage(conn,tran,TextBox1.Text,Session["user_id"].ToString(),pcode,4,"Family","t_FamilyDetailsRequest");
				tran.Commit();

			}

		}
		
		private void AcceptRejectEduRequest(string wfid)
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();

			SqlTransaction tran=conn.BeginTransaction();

			//SqlDataAdapter da=new SqlDataAdapter("select replyonaccept,replyonreject from t_fieldsmgt where f_id="+wfid,conn);
			//da.SelectCommand.Transaction=tran;

			DataSet ds=new DataSet();
			//da.Fill(ds);

			//string replyonaccept=ds.Tables[0].Rows[0]["replyonaccept"].ToString();
			//string replyonreject=ds.Tables[0].Rows[0]["replyonreject"].ToString();
			//ds.Tables.Clear();

			SqlDataAdapter da=new SqlDataAdapter(" SELECT reqid, eduinfoid, pcode, degree, institute, DurationFrom, DurationTo, result, majors, achievements, isactive, OtherDegree, OtherInstitute, OtherMajors, "+
				" type, createdby, createddate, modifiedby, c_at, Addflag, wfid, AppFlag, AppRejDate, RejCode, AppRejBy "+
				" FROM dbo.t_educationInfoRequest "+
				" WHERE (reqid = "+Label13.Text+")",conn);
			da.SelectCommand.Transaction=tran;
			da.Fill(ds);
			string pcode=ds.Tables[0].Rows[0]["pcode"].ToString();
			string eduinfoid=ds.Tables[0].Rows[0]["eduinfoid"].ToString();

			if(Label11.Text=="Acceptance Message To Member TeamGEO")
			{
				if(Label14.Text=="Request for new Educational Degree")
				{
					SqlCommand cmd=new SqlCommand("insert into dbo.t_educationInfo(pcode,degree,institute,DurationFrom,DurationTo,result,majors,achievements,isactive,OtherDegree,OtherInstitute,createdby,createddate) "+
						" values(@pcode,@degree,@institute,@DurationFrom,@DurationTo,@result,@majors,@achievements,1,@OtherDegree,@OtherInstitute,@createdby,getdate())",conn);
					//cmd.Parameters.Add("pcode",ds.Tables[0].Rows[0]["pcode"].ToString());
					SetParameterValue(cmd,ds,"pcode");
					SetParameterValue(cmd,ds,"degree");
					SetParameterValue(cmd,ds,"institute");
					SetParameterValue(cmd,ds,"DurationFrom");
					SetParameterValue(cmd,ds,"DurationTo");
					SetParameterValue(cmd,ds,"result");
					SetParameterValue(cmd,ds,"majors");
					SetParameterValue(cmd,ds,"achievements");
					SetParameterValue(cmd,ds,"OtherDegree");
					SetParameterValue(cmd,ds,"OtherInstitute");
					cmd.Transaction=tran;
					cmd.Parameters.Add("@createdby",Session["user_id"].ToString());
					int res=cmd.ExecuteNonQuery();
					UpdateEduRequest(ds.Tables[0].Rows[0]["reqid"].ToString(),"1");
					Bulletin.PostReqMessage(conn,tran,TextBox2.Text,Session["user_id"].ToString(),pcode,3,"Education","t_educationInfoRequest");
					tran.Commit();

					

				}
				if(Label14.Text=="Request for Change")
				{
					SqlCommand cmd=new SqlCommand("update t_educationInfo set pcode=@pcode,degree=@degree,institute=@institute,DurationFrom=@DurationFrom,DurationTo=@DurationTo,result=@result,majors=@majors,achievements=@achievements,OtherDegree=@OtherDegree,OtherInstitute=@OtherInstitute,modifiedby=@modifiedby where eduinfoid=@eduinfoid",conn);
					cmd.Transaction=tran;
					//cmd.Parameters.Add("pcode",ds.Tables[0].Rows[0]["pcode"].ToString());
					SetParameterValue(cmd,ds,"eduinfoid");
					SetParameterValue(cmd,ds,"pcode");
					SetParameterValue(cmd,ds,"degree");
					SetParameterValue(cmd,ds,"institute");
					SetParameterValue(cmd,ds,"DurationFrom");
					SetParameterValue(cmd,ds,"DurationTo");
					SetParameterValue(cmd,ds,"result");
					SetParameterValue(cmd,ds,"majors");
					SetParameterValue(cmd,ds,"achievements");
					SetParameterValue(cmd,ds,"OtherDegree");
					SetParameterValue(cmd,ds,"OtherInstitute");
					cmd.Parameters.Add("@modifiedby",Session["user_id"].ToString());
					int res=cmd.ExecuteNonQuery();
					UpdateEduRequest(ds.Tables[0].Rows[0]["reqid"].ToString(),"1");
					Bulletin.PostReqMessage(conn,tran,TextBox2.Text,Session["user_id"].ToString(),pcode,3,"Education","t_educationInfoRequest");
					tran.Commit();

				}
				if(Label14.Text=="Request for Remvoe" )
				{
					SqlCommand cmd=new SqlCommand("update t_educationInfo set isactive=0 where eduinfoid="+eduinfoid,conn);
					//cmd.Parameters.Add("pcode",ds.Tables[0].Rows[0]["pcode"].ToString());
					cmd.Transaction=tran;
					int res=cmd.ExecuteNonQuery();
					UpdateEduRequest(ds.Tables[0].Rows[0]["reqid"].ToString(),"1");
					//Bulletin.PostMessage("Remove Education Info",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Label15.Text);
					Bulletin.PostReqMessage(conn,tran,TextBox2.Text,Session["user_id"].ToString(),pcode,3,"Education","t_educationInfoRequest");
					tran.Commit();
				}
			}
			else if (Label11.Text=="Rejection Message To Member TeamGEO")
			{

				UpdateEduRequest(Label13.Text,"0");
				//Bulletin.PostMessage("Reject Education Info Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Label15.Text);
				Bulletin.PostReqMessage(conn,tran,TextBox2.Text,Session["user_id"].ToString(),pcode,4,"Education","t_educationInfoRequest");
				tran.Commit();
			}
		}
		/*private void btnSend_Click(object sender, System.EventArgs e)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlTransaction trans=con.BeginTransaction();
			SqlCommand cmd=null;
			SqlDataReader rd=null;
			
			string Pcode=this.lblcInformation.ToolTip.Trim();
			string ID=this.lblRequestDate.ToolTip.Trim();
			cmd=new SqlCommand("select replyonaccept from t_fieldsmgt where f_id="+lblFieldName.ToolTip+"",con);
			cmd.Transaction=trans;
			rd=cmd.ExecuteReader();
			rd.Read();
			string replyonaccept=rd[0].ToString();
			rd.Close();
			
			try
			{
				if(this.lblFieldName.ToolTip=="7")
				{
					//Name//
					cmd=new SqlCommand("update t_employee set name=@name,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _name=new SqlParameter("@name",SqlDbType.VarChar);
					_name.Value=this.lblRequestInformation.Text;
					cmd.Parameters.Add(_name);
					cmd.Transaction=trans;
					//trans.Commit();
					cmd.ExecuteNonQuery();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
				}
				if(this.lblFieldName.ToolTip=="10")
				{
					//Department//
				}
				if(this.lblFieldName.ToolTip=="6")
				{
					//Designation
				}
				if(this.lblFieldName.ToolTip=="8")
				{
					//Station
					cmd=new SqlCommand("update t_employee set station=@station,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _station=new SqlParameter("@station",SqlDbType.Int);
					_station.Value=this.lblRequestInformation.ToolTip;
					cmd.Parameters.Add(_station);
					cmd.Transaction=trans;
					//trans.Commit();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
				}
				if(this.lblFieldName.ToolTip=="9")
				{
					//Date of Join
					cmd=new SqlCommand("update t_employee set dateofjoin=@dateofjoin,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _dateofjoin=new SqlParameter("@dateofjoin",SqlDbType.DateTime);
					_dateofjoin.Value=this.lblRequestInformation.ToolTip;
					cmd.Parameters.Add(_dateofjoin);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					//trans.Commit();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
				}
				if(this.lblFieldName.ToolTip=="11")
				{
					//Extension
					cmd=new SqlCommand("update t_employee set extension=@extension,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _extension=new SqlParameter("@extension",SqlDbType.VarChar);
					_extension.Value=this.lblRequestInformation.Text;
					cmd.Parameters.Add(_extension);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					//trans.Commit();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
				}
				if(this.lblFieldName.ToolTip=="31")
				{
					//Email (Official)
					cmd=new SqlCommand("update t_employee set email_official=@email_official,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _email=new SqlParameter("@email_official",SqlDbType.VarChar);
					_email.Value=this.lblRequestInformation.Text;
					cmd.Parameters.Add(_email);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					//trans.Commit();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
				}
				if(this.lblFieldName.ToolTip=="12")
				{
					//SESSI
					cmd=new SqlCommand("update t_employee set sessino=@sessino,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _sessi=new SqlParameter("@sessino",SqlDbType.VarChar);
					_sessi.Value=this.lblRequestInformation.Text;
					cmd.Parameters.Add(_sessi);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					//trans.Commit();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
				}
				if(this.lblFieldName.ToolTip=="13")
				{
					//EOBI
					cmd=new SqlCommand("update t_employee set eobino=@eobino,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _eobi=new SqlParameter("@eobino",SqlDbType.VarChar);
					_eobi.Value=this.lblRequestInformation.Text;
					cmd.Parameters.Add(_eobi);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					//trans.Commit();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
				}
				if(this.lblFieldName.ToolTip=="14")
				{
					//Workstation Address
					cmd=new SqlCommand("update t_employee set operatingcity=@operatingcity,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _workstation=new SqlParameter("@operatingcity",SqlDbType.VarChar);
					_workstation.Value=this.lblRequestInformation.Text;
					cmd.Parameters.Add(_workstation);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					//trans.Commit();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
				}

				//=============================Personal Info========================//
				if(this.lblFieldName.ToolTip=="16")
				{
					//Date of Birth
					cmd=new SqlCommand("update t_employee set dateofbirth=@dateofbirth,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _dateofbirth=new SqlParameter("@dateofbirth",SqlDbType.DateTime);
					_dateofbirth.Value=this.lblRequestInformation.Text;
					cmd.Parameters.Add(_dateofbirth);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
					
				}
				if(this.lblFieldName.ToolTip=="24")
				{
					//Father Name
					cmd=new SqlCommand("update t_employee set fathername=@fathername,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _fathername=new SqlParameter("@fathername",SqlDbType.VarChar);
					_fathername.Value=this.lblRequestInformation.Text;
					cmd.Parameters.Add(_fathername);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
				}
				if(this.lblFieldName.ToolTip=="17")
				{
					//Gender
					cmd=new SqlCommand("update t_employee set gender=@gender,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _gender=new SqlParameter("@gender",SqlDbType.TinyInt);
					_gender.Value=this.lblRequestInformation.ToolTip;
					cmd.Parameters.Add(_gender);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
					
				}
				if(this.lblFieldName.ToolTip=="18")
				{
					//BloodGroup
					cmd=new SqlCommand("update t_employee set bloodgroup=@bloodgroup,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _bloodgroup=new SqlParameter("@bloodgroup",SqlDbType.TinyInt);
					_bloodgroup.Value=this.lblRequestInformation.ToolTip;
					cmd.Parameters.Add(_bloodgroup);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
					
				}
				if(this.lblFieldName.ToolTip=="26")
				{
					//Religion
					cmd=new SqlCommand("update t_employee set religion=@religion,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _religion=new SqlParameter("@religion",SqlDbType.Int);
					_religion.Value=this.lblRequestInformation.ToolTip;
					cmd.Parameters.Add(_religion);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
					
				}
				if(this.lblFieldName.ToolTip=="37")
				{
					//Nick
					cmd=new SqlCommand("update t_employee set nick=@nick,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _nick=new SqlParameter("@nick",SqlDbType.VarChar);
					_nick.Value=this.lblRequestInformation.Text;
					cmd.Parameters.Add(_nick);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
					
				}
				if(this.lblFieldName.ToolTip=="22")
				{
					//Address
					cmd=new SqlCommand("update t_employee set address=@address,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _address=new SqlParameter("@address",SqlDbType.VarChar);
					_address.Value=this.lblRequestInformation.Text;
					cmd.Parameters.Add(_address);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
					
				}
				if(this.lblFieldName.ToolTip=="25")
				{
					//Marital Status
					cmd=new SqlCommand("update t_employee set maritialstatus=@maritialstatus,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _maritialstatus=new SqlParameter("@maritialstatus",SqlDbType.TinyInt);
					_maritialstatus.Value=this.lblRequestInformation.ToolTip;
					cmd.Parameters.Add(_maritialstatus);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
					
				}
				if(this.lblFieldName.ToolTip=="29")
				{
					//Email (Personal)
					cmd=new SqlCommand("update t_employee set email_personal=@email_personal,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _emailpersonal=new SqlParameter("@email_personal",SqlDbType.VarChar);
					_emailpersonal.Value=this.lblRequestInformation.Text;
					cmd.Parameters.Add(_emailpersonal);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
					
				}
				if(this.lblFieldName.ToolTip=="34")
				{
					//NTN#
					cmd=new SqlCommand("update t_employee set ntnno=@ntnno,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _ntnno=new SqlParameter("@ntnno",SqlDbType.VarChar);
					_ntnno.Value=this.lblRequestInformation.Text;
					cmd.Parameters.Add(_ntnno);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
					
				}
				if(this.lblFieldName.ToolTip=="27")
				{
					//Telephone #(Home)
					cmd=new SqlCommand("update t_employee set telephone=@telephone,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _telephone=new SqlParameter("@telephone",SqlDbType.VarChar);
					_telephone.Value=this.lblRequestInformation.Text;
					cmd.Parameters.Add(_telephone);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
					
				}
				if(this.lblFieldName.ToolTip=="28")
				{
					//Mobile #
					cmd=new SqlCommand("update t_employee set mobile=@mobile,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _mobile=new SqlParameter("@mobile",SqlDbType.VarChar);
					_mobile.Value=this.lblRequestInformation.Text;
					cmd.Parameters.Add(_mobile);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
					
				}
				if(this.lblFieldName.ToolTip=="35")
				{
					//Nationality
					cmd=new SqlCommand("update t_employee set nationality=@nationality,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _nationality=new SqlParameter("@nationality",SqlDbType.Int);
					_nationality.Value=this.lblRequestInformation.ToolTip;
					cmd.Parameters.Add(_nationality);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
					
				}
				if(this.lblFieldName.ToolTip=="36")
				{
					//Nationality (Secondary)
					cmd=new SqlCommand("update t_employee set nationality2=@nationality2,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _nationality2=new SqlParameter("@nationality",SqlDbType.Int);
					_nationality2.Value=this.lblRequestInformation.ToolTip;
					cmd.Parameters.Add(_nationality2);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
				
				}
				if(this.lblFieldName.ToolTip=="19")
				{
					//Passport
					cmd=new SqlCommand("update t_employee set passportno=@passportno,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _passportno=new SqlParameter("@passportno",SqlDbType.VarChar);
					_passportno.Value=this.lblRequestInformation.Text;
					cmd.Parameters.Add(_passportno);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
					
				}
				if(this.lblFieldName.ToolTip=="30")
				{
					//Next of Kin
					cmd=new SqlCommand("update t_employee set kin=@kin,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _kin=new SqlParameter("@kin",SqlDbType.VarChar);
					_kin.Value=this.lblRequestInformation.Text;
					cmd.Parameters.Add(_kin);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
				
				}
				if(this.lblFieldName.ToolTip=="21")
				{
					//NIC (New)
					cmd=new SqlCommand("update t_employee set nic_new=@nic_new,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _nicnew=new SqlParameter("@nic_new",SqlDbType.VarChar);
					_nicnew.Value=this.lblRequestInformation.Text;
					cmd.Parameters.Add(_nicnew);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
					
				}
				if(this.lblFieldName.ToolTip=="20")
				{
					//NIC (Old)
					cmd=new SqlCommand("update t_employee set nic_old=@nic_old,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _nicold=new SqlParameter("@nic_old",SqlDbType.VarChar);
					_nicold.Value=this.lblRequestInformation.Text;
					cmd.Parameters.Add(_nicold);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
					
				}
				if(this.lblFieldName.ToolTip=="32")
				{
					//Bank Account #
					cmd=new SqlCommand("update t_employee set bankaccountno=@bankaccountno,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _bankaccountno=new SqlParameter("@bankaccountno",SqlDbType.VarChar);
					_bankaccountno.Value=this.lblRequestInformation.Text;
					cmd.Parameters.Add(_bankaccountno);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
				
				}
				if(this.lblFieldName.ToolTip=="33")
				{
					//Bank Account Detail
					cmd=new SqlCommand("update t_employee set bankaccountdetails=@bankaccountdetails,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
					SqlParameter _bankaccountdetails=new SqlParameter("@bankaccountdetails",SqlDbType.VarChar);
					_bankaccountdetails.Value=this.lblRequestInformation.Text;
					cmd.Parameters.Add(_bankaccountdetails);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=3 where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					trans.Commit();
					con.Close();
					Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
					
				}
				//==================================================================//
				this.btnAccept.Enabled=true;
				this.btnReject.Enabled=true;
				TD1.Visible=false;
				this.lblAction.Text="";
				this.txtMessage.Text="";
				this.pnlrView.Visible=false;
			}
			catch(Exception ex)
			{
				Response.Write(ex.Message);
			}
			con.Close();
			con.Dispose();
		}
*/
		private void btnSend_Click(object sender, System.EventArgs e)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlTransaction trans=con.BeginTransaction();
			SqlCommand cmd=null;
			SqlDataReader rd=null;
			string Pcode=this.lbleCode.Text.Trim();
			string ID=this.lblRequestDate.ToolTip.Trim();
			if(this.lblAction.Text=="Acceptance Message To Member TeamGEO")
			{
				//				cmd=new SqlCommand("select replyonaccept from t_fieldsmgt where f_id="+lblFieldName.ToolTip+"",con);
				//				cmd.Transaction=trans;
				//				rd=cmd.ExecuteReader();
				//				rd.Read();
				//				string replyonaccept=rd[0].ToString();
				//				rd.Close();
				cmd=new SqlCommand("select e.fieldno,s.sp_id from t_employeerequests e,t_fieldmgtspecialist s,t_specialistowner sp where e.rid="+ID+" and e.fieldno=s.f_id and sp.SpecialistNo=s.sp_id and sp.s_fdesigid =(select f.fdesigid from t_functionaldesignation f,t_designationhistory h where f.fdesigid=h.fdesigid and h.isactive=1 and h.pcode='"+Session["user_id"].ToString()+"')",con);
				cmd.Transaction=trans;
				rd=cmd.ExecuteReader();
				rd.Read();
				string SpecialistNo=rd[1].ToString();
				rd.Close();
				string replyonaccept=this.txtMessage.Text;
				try
				{
					if(this.lblFieldName.ToolTip=="7")
					{
						//Name//
						cmd=new SqlCommand("update t_employee set name=@name,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _name=new SqlParameter("@name",SqlDbType.VarChar);
						_name.Value=this.lblRequestInformation.Text;
						cmd.Parameters.Add(_name);
						cmd.Transaction=trans;
						//trans.Commit();
						cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						  
						FillGrid();
					}
					if(this.lblFieldName.ToolTip=="10")
					{
						//Department//
						string updateRec="update t_designationhistory set isactive=0,_to=getdate(),modifiedby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"' and isactive=1";
						cmd=new SqlCommand(updateRec,con);
						cmd.Transaction=trans;
						int i=cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employee set desigid=@desigid,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _desigid=new SqlParameter("@desigid",SqlDbType.VarChar);
						_desigid.Value=this.lblRequestInformation.ToolTip;
						cmd.Parameters.Add(_desigid);
						cmd.Transaction=trans;
						i=cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						FillGrid();
						
					}
					if(this.lblFieldName.ToolTip=="6")
					{
						//Designation
						string updateRec="update t_designationhistory set isactive=0,_to=getdate(),modifiedby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"' and isactive=1";
						cmd=new SqlCommand(updateRec,con);
						cmd.Transaction=trans;
						int i=cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employee set desigid=@desigid,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _desigid=new SqlParameter("@desigid",SqlDbType.VarChar);
						_desigid.Value=this.lblRequestInformation.ToolTip;
						cmd.Parameters.Add(_desigid);
						cmd.Transaction=trans;
						i=cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
						/*string updateRec="update t_designationhistory set isactive=0,_to=getdate() where pcode='"+Pcode+"'";
						SqlCommand cmd=new SqlCommand(updateRec,con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employee set desigid=@desigid,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _desigid=new SqlParameter("@desigid",SqlDbType.VarChar);
						_desigid.Value=this.lblRequestInformation.ToolTip;
						cmd.Parameters.Add(_desigid);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						HRISService.Employee s=new GeoRabtaSite.HRISService.Employee();
						GeoRabtaSite.admin.TISIntegrator tis=new GeoRabtaSite.admin.TISIntegrator();
						string output=s.UpdateDesignation(Pcode,this.lblRequestInformation.Text);
						if(output!="Ok")
						{
						 trans.Rollback();
						 return;
						}
						else
						{
							if(tis.updateEmployee(this.txtEmpInfoCode.Text,this.txtEmpInfoName.Text,this.RadioButtonList1.SelectedItem.Text,mStatus,1,this.CalenderDOJ.SelectedDate.ToShortDateString(),this.CalenderCOD.SelectedDate.ToShortDateString(),"",Int32.Parse(this.ddOpertingCity.SelectedItem.Value),Int32.Parse(this.ddDepartment.SelectedItem.Value),this.ddDesignation.SelectedItem.Text,0))
							{
						  
							}
							else
							{
								trans.Rollback();
								return;
							}
						}
						trans.Commit();*/
					}
					if(this.lblFieldName.ToolTip=="8")
					{
						//Station
						cmd=new SqlCommand("update t_employee set station=@station,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _station=new SqlParameter("@station",SqlDbType.Int);
						_station.Value=this.lblRequestInformation.ToolTip;
						cmd.Parameters.Add(_station);
						cmd.Transaction=trans;
						//trans.Commit();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
					}
					if(this.lblFieldName.ToolTip=="9")
					{
						//Date of Join
						cmd=new SqlCommand("update t_employee set dateofjoin=@dateofjoin,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _dateofjoin=new SqlParameter("@dateofjoin",SqlDbType.DateTime);
						_dateofjoin.Value=this.lblRequestInformation.ToolTip;
						cmd.Parameters.Add(_dateofjoin);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						//trans.Commit();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
					}
					if(this.lblFieldName.ToolTip=="11")
					{
						//Extension
						cmd=new SqlCommand("update t_employee set extension=@extension,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _extension=new SqlParameter("@extension",SqlDbType.VarChar);
						_extension.Value=this.lblRequestInformation.Text;
						cmd.Parameters.Add(_extension);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						//trans.Commit();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
					}
					if(this.lblFieldName.ToolTip=="31")
					{
						//Email (Official)
						cmd=new SqlCommand("update t_employee set email_official=@email_official,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _email=new SqlParameter("@email_official",SqlDbType.VarChar);
						_email.Value=this.lblRequestInformation.Text;
						cmd.Parameters.Add(_email);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						//trans.Commit();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
					}
					if(this.lblFieldName.ToolTip=="12")
					{
						//SESSI
						cmd=new SqlCommand("update t_employee set sessino=@sessino,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _sessi=new SqlParameter("@sessino",SqlDbType.VarChar);
						_sessi.Value=this.lblRequestInformation.Text;
						cmd.Parameters.Add(_sessi);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						//trans.Commit();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
					}
					if(this.lblFieldName.ToolTip=="13")
					{
						//EOBI
						cmd=new SqlCommand("update t_employee set eobino=@eobino,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _eobi=new SqlParameter("@eobino",SqlDbType.VarChar);
						_eobi.Value=this.lblRequestInformation.Text;
						cmd.Parameters.Add(_eobi);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						//trans.Commit();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
					}
					if(this.lblFieldName.ToolTip=="14")
					{
						//Workstation Address
						cmd=new SqlCommand("update t_employee set operatingcity=@operatingcity,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _workstation=new SqlParameter("@operatingcity",SqlDbType.VarChar);
						_workstation.Value=this.lblRequestInformation.Text;
						cmd.Parameters.Add(_workstation);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						//trans.Commit();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
					}

					//=============================Personal Info========================//
					if(this.lblFieldName.ToolTip=="16")
					{
						//Date of Birth
						cmd=new SqlCommand("update t_employee set dateofbirth=@dateofbirth,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _dateofbirth=new SqlParameter("@dateofbirth",SqlDbType.DateTime);
						_dateofbirth.Value=this.lblRequestInformation.Text;
						cmd.Parameters.Add(_dateofbirth);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
					
					}
					if(this.lblFieldName.ToolTip=="24")
					{
						//Father Name
						cmd=new SqlCommand("update t_employee set fathername=@fathername,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _fathername=new SqlParameter("@fathername",SqlDbType.VarChar);
						_fathername.Value=this.lblRequestInformation.Text;
						cmd.Parameters.Add(_fathername);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
					}
					if(this.lblFieldName.ToolTip=="17")
					{
						//Gender
						cmd=new SqlCommand("update t_employee set gender=@gender,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _gender=new SqlParameter("@gender",SqlDbType.TinyInt);
						_gender.Value=this.lblRequestInformation.ToolTip;
						cmd.Parameters.Add(_gender);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
					
					}
					if(this.lblFieldName.ToolTip=="18")
					{
						//BloodGroup
						cmd=new SqlCommand("update t_employee set bloodgroup=@bloodgroup,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _bloodgroup=new SqlParameter("@bloodgroup",SqlDbType.TinyInt);
						_bloodgroup.Value=this.lblRequestInformation.ToolTip;
						cmd.Parameters.Add(_bloodgroup);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
					
					}
					if(this.lblFieldName.ToolTip=="26")
					{
						//Religion
						cmd=new SqlCommand("update t_employee set religion=@religion,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _religion=new SqlParameter("@religion",SqlDbType.Int);
						_religion.Value=this.lblRequestInformation.ToolTip;
						cmd.Parameters.Add(_religion);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
					
					}
					if(this.lblFieldName.ToolTip=="37")
					{
						//Nick
						cmd=new SqlCommand("update t_employee set nick=@nick,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _nick=new SqlParameter("@nick",SqlDbType.VarChar);
						_nick.Value=this.lblRequestInformation.Text;
						cmd.Parameters.Add(_nick);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
					
					}
					if(this.lblFieldName.ToolTip=="22")
					{
						//Address
						cmd=new SqlCommand("update t_employee set address=@address,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _address=new SqlParameter("@address",SqlDbType.VarChar);
						_address.Value=this.lblRequestInformation.Text;
						cmd.Parameters.Add(_address);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
					
					}
					if(this.lblFieldName.ToolTip=="25")
					{
						//Marital Status
						cmd=new SqlCommand("update t_employee set maritialstatus=@maritialstatus,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _maritialstatus=new SqlParameter("@maritialstatus",SqlDbType.TinyInt);
						_maritialstatus.Value=this.lblRequestInformation.ToolTip;
						cmd.Parameters.Add(_maritialstatus);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
					
					}
					if(this.lblFieldName.ToolTip=="29")
					{
						//Email (Personal)
						cmd=new SqlCommand("update t_employee set email_personal=@email_personal,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _emailpersonal=new SqlParameter("@email_personal",SqlDbType.VarChar);
						_emailpersonal.Value=this.lblRequestInformation.Text;
						cmd.Parameters.Add(_emailpersonal);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
					
					}
					if(this.lblFieldName.ToolTip=="34")
					{
						//NTN#
						cmd=new SqlCommand("update t_employee set ntnno=@ntnno,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _ntnno=new SqlParameter("@ntnno",SqlDbType.VarChar);
						_ntnno.Value=this.lblRequestInformation.Text;
						cmd.Parameters.Add(_ntnno);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
					
					}
					if(this.lblFieldName.ToolTip=="27")
					{
						//Telephone #(Home)
						cmd=new SqlCommand("update t_employee set telephone=@telephone,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _telephone=new SqlParameter("@telephone",SqlDbType.VarChar);
						_telephone.Value=this.lblRequestInformation.Text;
						cmd.Parameters.Add(_telephone);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
					
					}
					if(this.lblFieldName.ToolTip=="28")
					{
						//Mobile #
						cmd=new SqlCommand("update t_employee set mobile=@mobile,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _mobile=new SqlParameter("@mobile",SqlDbType.VarChar);
						_mobile.Value=this.lblRequestInformation.Text;
						cmd.Parameters.Add(_mobile);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						FillGrid();
					
					}
					if(this.lblFieldName.ToolTip=="35")
					{
						//Nationality
						cmd=new SqlCommand("update t_employee set nationality=@nationality,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _nationality=new SqlParameter("@nationality",SqlDbType.Int);
						_nationality.Value=this.lblRequestInformation.ToolTip;
						cmd.Parameters.Add(_nationality);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
					
					}
					if(this.lblFieldName.ToolTip=="36")
					{
						//Nationality (Secondary)
						cmd=new SqlCommand("update t_employee set nationality2=@nationality2,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _nationality2=new SqlParameter("@nationality",SqlDbType.Int);
						_nationality2.Value=this.lblRequestInformation.ToolTip;
						cmd.Parameters.Add(_nationality2);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
				
					}
					if(this.lblFieldName.ToolTip=="19")
					{
						//Passport
						cmd=new SqlCommand("update t_employee set passportno=@passportno,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _passportno=new SqlParameter("@passportno",SqlDbType.VarChar);
						_passportno.Value=this.lblRequestInformation.Text;
						cmd.Parameters.Add(_passportno);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
					
					}
					if(this.lblFieldName.ToolTip=="30")
					{
						//Next of Kin
						cmd=new SqlCommand("update t_employee set kin=@kin,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _kin=new SqlParameter("@kin",SqlDbType.VarChar);
						_kin.Value=this.lblRequestInformation.Text;
						cmd.Parameters.Add(_kin);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
				
					}
					if(this.lblFieldName.ToolTip=="21")
					{
						//NIC (New)
						cmd=new SqlCommand("update t_employee set nic_new=@nic_new,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _nicnew=new SqlParameter("@nic_new",SqlDbType.VarChar);
						_nicnew.Value=this.lblRequestInformation.Text;
						cmd.Parameters.Add(_nicnew);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
					
					}
					if(this.lblFieldName.ToolTip=="20")
					{
						//NIC (Old)
						cmd=new SqlCommand("update t_employee set nic_old=@nic_old,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _nicold=new SqlParameter("@nic_old",SqlDbType.VarChar);
						_nicold.Value=this.lblRequestInformation.Text;
						cmd.Parameters.Add(_nicold);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
					
					}
					if(this.lblFieldName.ToolTip=="32")
					{
						//Bank Account #
						cmd=new SqlCommand("update t_employee set bankaccountno=@bankaccountno,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _bankaccountno=new SqlParameter("@bankaccountno",SqlDbType.VarChar);
						_bankaccountno.Value=this.lblRequestInformation.Text;
						cmd.Parameters.Add(_bankaccountno);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
				
					}
					if(this.lblFieldName.ToolTip=="33")
					{
						//Bank Account Detail
						cmd=new SqlCommand("update t_employee set bankaccountdetails=@bankaccountdetails,profilemodifyby='"+Session["user_id"].ToString()+"' where pcode='"+Pcode+"'",con);
						SqlParameter _bankaccountdetails=new SqlParameter("@bankaccountdetails",SqlDbType.VarChar);
						_bankaccountdetails.Value=this.lblRequestInformation.Text;
						cmd.Parameters.Add(_bankaccountdetails);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						
						FillGrid();
					
					}


					if(this.lblFieldName.ToolTip=="43")
					{
						//Employee Picture
						string filepath=Server.MapPath(@"..\employee\");
						string filename=lbleCode.Text.Trim() + ".jpg";
						string path=filepath + filename;
						File.Copy(Server.MapPath(@"..\tempemployee\"+this.Label1.Text+".jpg"),path,true);
						File.Delete(Server.MapPath(@"..\tempemployee\"+this.Label1.Text+".jpg"));
						//FileToUpload.PostedFile.SaveAs(filepath + filename);
						CreateThumbnail(filename); 
						cmd=new SqlCommand("update t_employeerequests set requeststatus=3,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						Bulletin.PostMessage(con,trans,replyonaccept,Session["user_id"].ToString(),Pcode,3,ID);
						trans.Commit();
						con.Close();
						//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonaccept,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
						FillGrid(); 
					}
					//==================================================================//
					this.btnAccept.Enabled=true;
					this.btnReject.Enabled=true;
					TD1.Visible=false;
					this.lblAction.Text="";
					this.txtMessage.Text="";
					this.pnlrView.Visible=false;
					ResetPanels();
				}
				catch(Exception ex)
				{
					Response.Write(ex.Message);
				}
			}
			else
			{
				//				cmd=new SqlCommand("select replyonreject from t_fieldsmgt where f_id="+lblFieldName.ToolTip+"",con);
				//				cmd.Transaction=trans;
				//				rd=cmd.ExecuteReader();
				//				rd.Read();
				//				string replyonreject=rd[0].ToString();
				//				rd.Close();
				string replyonreject=this.txtMessage.Text;
				try
				{
					//==================================================================//
					cmd=new SqlCommand("select e.fieldno,s.sp_id from t_employeerequests e,t_fieldmgtspecialist s,t_specialistowner sp where e.rid="+ID+" and e.fieldno=s.f_id and sp.SpecialistNo=s.sp_id and sp.s_fdesigid =(select f.fdesigid from t_functionaldesignation f,t_designationhistory h where f.fdesigid=h.fdesigid and h.isactive=1 and h.pcode='"+Session["user_id"].ToString()+"')",con);
					cmd.Transaction=trans;
					rd=cmd.ExecuteReader();
					rd.Read();
					string SpecialistNo=rd[1].ToString();
					rd.Close();
					cmd=new SqlCommand("update t_employeerequests set requeststatus=4,specialistno='"+SpecialistNo+"',rspcecialistcode='"+Session["user_id"].ToString()+"',rhandledate=getdate() where rID="+ID+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					Bulletin.PostMessage(con,trans,replyonreject,Session["user_id"].ToString(),Pcode,4,ID);
					trans.Commit();
					con.Close();
					//Bulletin.PostMessage("Change of "+lblFieldName.Text+" Request",replyonreject,Session["user_id"].ToString(),7,2,Session["user_id"].ToString(),Pcode);
					FillGrid();
					//==================================================================//
					this.btnAccept.Enabled=true;
					this.btnReject.Enabled=true;
					TD1.Visible=false;
					this.lblAction.Text="";
					this.txtMessage.Text="";
					this.pnlrView.Visible=false;
				}
				catch(Exception ex)
				{
					Response.Write(ex.Message);
				}
			}
			con.Close();
			con.Dispose();
		}
		private void Button5_Click(object sender, System.EventArgs e)
		{
			tabEduDetail.Visible=false;
			Datagrid2.SelectedIndex=-1;
			ResetPanels();
		}

		private void CreateThumbnail(string file)
		{
			System.Drawing.Image ObjImage;
			System.Drawing.Image Objthumbnail;
			string Path=Server.MapPath(@"..\employee\"+file); 
			ObjImage=System.Drawing.Image.FromFile(Path);
			Objthumbnail=ObjImage.GetThumbnailImage(100,120,null,System.IntPtr.Zero);
			Response.ContentType="image/jpeg";
			//Objthumbnail.Save(Response.OutputStream,System.Drawing.Imaging.ImageFormat.Jpeg);
			Path=Server.MapPath(@"..\");
			try
			{
				Objthumbnail.Save(Path+"\\EmpThumbnail\\"+file,System.Drawing.Imaging.ImageFormat.Jpeg);
			}
			catch(Exception ex)
			{
				
				Response.Write(ex.Message);
			}
			ObjImage.Dispose();
			Objthumbnail.Dispose();
		}

		private void Datagrid1_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			// Reset Remaining Grid Selected Index
			int idx=Datagrid1.SelectedIndex;
			dgExp.SelectedIndex=-1;
			Datagrid1.SelectedIndex=-1;
			Datagrid2.SelectedIndex=-1;
			dgTraining.SelectedIndex=-1;
			dgMyRequest.SelectedIndex=-1;
			Datagrid1.SelectedIndex=idx;
			//////////////////////////////////

			//pnlMyReq.Visible=false;
			pnlEdu.Visible=false;
			pnlFamily.Visible=true;
			pnlTraining.Visible=false;
			pnlExp.Visible=false;

			
			Button4.Enabled=true;
			Button3.Enabled=true;
			Label1.Text="";
			TextBox1.Text="";
			Button2.Enabled=false;
			Button1.Enabled=true;

			tabFamilyDetail.Visible=true;
			Label27.Text=Datagrid1.SelectedItem.Cells[1].Text;
			string sql=" SELECT er.filename, er.reqid, er.pcode, er.isactive, er.c_at, "+
				" CASE er.Addflag WHEN 1 THEN 'Request for new Family Member' WHEN 2 THEN 'Request for Change' WHEN 3 THEN 'Request for Remvoe' END AS AddFlag, "+
				" er.AppFlag, em.name, DATEADD(day, f.timeline, er.c_at) AS Timeline, er.name AS fm_name, "+
				" CASE er.relationship WHEN 2 THEN 'Husband' WHEN 3 THEN 'Wife' WHEN 4 THEN 'Son' WHEN 5 THEN 'Daughter' ELSE '' END AS fm_relationship, "+
				" CASE er.maritialstatus WHEN 1 THEN 'Single' WHEN 2 THEN 'Married' WHEN 3 THEN 'Divorced' WHEN 4 THEN 'Widow' WHEN 5 THEN 'Separated' END "+
				" AS [Marital Status], "+
				" CASE er.relationship WHEN 2 THEN 'Male' WHEN 4 THEN 'Male' WHEN 3 THEN 'Female' WHEN 5 THEN 'Female' ELSE '' END AS Expr1, "+
				" er.occupation, er.dob, CASE er.Dependent WHEN 1 THEN 'Yes' WHEN 2 THEN 'No' END AS Expr2, dept.deptname, dsg.designation, f.vrequired, "+
				" f.vcriteria, f.replyatrequest, f.replyonaccept, f.replyonreject, f.tooltip, "+
				" CASE er.Dependent WHEN 1 THEN 'Yes' WHEN 0 THEN 'No' ELSE '' END AS Dependent "+
				" FROM dbo.t_fieldsmgt AS f INNER JOIN "+
				" dbo.t_FamilyDetailsRequest AS er ON f.f_id = er.wfid INNER JOIN "+
				" dbo.t_fieldmgtspecialist AS fs ON f.f_id = fs.f_id INNER JOIN "+
				" dbo.t_Employee AS em ON er.pcode = em.pcode INNER JOIN "+
				" dbo.t_Designation AS dsg ON em.desigid = dsg.desigid INNER JOIN "+
				" dbo.t_Department AS dept ON dsg.deptid = dept.deptid "+
				" WHERE (er.AppFlag = 2) AND (er.reqid = "+Label27.Text+")";

			

			
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			string docID=ds.Tables[0].Rows[0]["filename"].ToString();
			docID=docID.Replace(".jpg","");

			SetText(ds,Label27,"reqid");
			SetText(ds,Label28,"AddFlag");
			SetText(ds,Label29,"pcode");
			SetText(ds,Label30,"name");
			SetText(ds,Label31,"designation");
			SetText(ds,Label32,"deptname");
			SetText(ds,Label33,"fm_name");
			SetText(ds,Label34,"fm_relationship");
			SetText(ds,Label35,"Marital Status");
			SetText(ds,Label36,"occupation");
			SetText(ds,Label37,"Dependent");
			SetText(ds,Label40,"c_at");	Label40.Text=DateTime.Parse(Label40.Text).ToString("d MMM, yyyy h:m:s");
			SetText(ds,Label41,"timeline"); Label41.Text=DateTime.Parse(Label41.Text).ToString("d MMM, yyyy");
			SetText(ds,Label42,"vcriteria");

			if(docID=="")
			{
				Label2.Attributes.Clear();
				Label2.Text="&nbsp;";
					
			}
			else
			{
				Label2.Text="View";
				Label2.Attributes.Add("onclick","window.showModalDialog('ViewDocuments.aspx?docID="+docID+"','dialogWidth:900px; dialogHeight:800px;status:no')");
			}
			

			string wfid="";
			if(Label34.Text=="Son" || Label34.Text=="Daughter")
				wfid="41";
			else
				wfid="40";
			DocReqFromHR(wfid,Label43,Label44);
		}
		private void SetText(DataSet ds,Label lb, string field)
		{
			lb.Text=ds.Tables[0].Rows[0][field].ToString();
			if(lb.Text=="")
				lb.Text="&nbsp;";
		}

		private void Button4_Click(object sender, System.EventArgs e)
		{
			Accept(Button4,Button3,Label1,TextBox1,Button2,"38");
		}

		private void Button3_Click(object sender, System.EventArgs e)
		{
			Rejection(Button4,Button3,Label1,TextBox1,Button2,"38");
		}

		private void Button6_Click(object sender, System.EventArgs e)
		{
			AcceptRejectEduRequest(Label13.Text);
			tabEduDetail.Visible=false;
			FillReqGrid(Datagrid2,"t_educationInfoRequest","eduinfoid","New Education","Change Education Info","Removal of Education");
			ResetPanels();
		}

		private void ResetPanels()
		{
			//pnlMyReq.Visible=true;
			dgExp.SelectedIndex=-1;
			Datagrid1.SelectedIndex=-1;
			Datagrid2.SelectedIndex=-1;
			dgTraining.SelectedIndex=-1;
			dgMyRequest.SelectedIndex=-1;
			pnlEdu.Visible=true;
			pnlFamily.Visible=true;
			pnlTraining.Visible=true;
			pnlExp.Visible=true;
		}
		private void Button1_Click(object sender, System.EventArgs e)
		{
			tabFamilyDetail.Visible=false;
			Datagrid1.SelectedIndex=-1;
			ResetPanels();
			

		}

		private void Button2_Click(object sender, System.EventArgs e)
		{
			AcceptRejectFamilyRequest(Label27.Text);
			tabFamilyDetail.Visible=false;
			Datagrid1.SelectedIndex=-1;
			FillReqGrid(Datagrid1,"t_FamilyDetailsRequest","fdid","New Family Member","Change of Family Member Info", "Removal of Family Member");
			ResetPanels();
		}

		private void dgTraining_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			
			// Reset Remaining Grid Selected Index
			int idx=dgTraining.SelectedIndex;
			dgExp.SelectedIndex=-1;
			Datagrid1.SelectedIndex=-1;
			Datagrid2.SelectedIndex=-1;
			dgTraining.SelectedIndex=-1;
			dgMyRequest.SelectedIndex=-1;
			dgTraining.SelectedIndex=idx;
			//////////////////////////////////
			
			//pnlMyReq.Visible=false;
			pnlEdu.Visible=false;
			pnlFamily.Visible=false;
			pnlTraining.Visible=true;
			pnlExp.Visible=false;

			
			cmdAccTraining.Enabled=true;
			cmdRejTraining.Enabled=true;
			lblTrainingMsg.Text="";
			txtTrainingMsg.Text="";
			cmdSubmitTraining.Enabled=false;
			cmdCancelTraining.Enabled=true;

			pnlTrainingDetail.Visible=true;
			Label54.Text=dgTraining.SelectedItem.Cells[1].Text;
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);

			string sql=" SELECT er.filename, er.reqid, er.trainingid, er.PCode, er.TrainingTitle, CASE er.TrainingField WHEN - 1 THEN er.trainingfieldother ELSE "+
				" (SELECT tf.trainingfield "+
				" FROM cv_trainingfields tf "+
				" WHERE tf.trainingfieldid = er.trainingfield) END AS TrainingField, dbo.Month_Name(er.TrainingMonth) + ' ' + CAST(er.TrainingYear AS varchar) "+
				" AS TrainingMonthYear, CAST(er.Duration AS varchar) "+
				" + CASE DurationUnit WHEN 'M' THEN ' Months' WHEN 'D' THEN ' Days' WHEN 'Y' THEN ' Years' ELSE '' END AS Duration, er.InstituteName, er.c_at, "+
				" CASE er.Addflag WHEN 1 THEN 'New Training' WHEN 2 THEN 'Change Training Info' WHEN 3 THEN 'Remove Training Info' END AS AddFlag, er.wfid, "+
				" er.AppFlag, er.AppRejDate, er.RejCode, er.AppRejBy, emp.name, dsg.designation, dpt.deptname, dbo.cv_country.cname, "+
				" CASE er.City WHEN - 1 THEN er.CityOther ELSE "+
				" (SELECT c.cityname "+
				" FROM cv_city c "+
				" WHERE c.cityid = er.city) END AS City, CASE er.TrainingInGeo WHEN 1 THEN 'Yes' WHEN 0 THEN 'No' ELSE '' END AS TrainingInGeo "+
				" FROM dbo.cv_TrainingReq AS er INNER JOIN "+
				" dbo.t_Employee AS emp ON er.PCode = emp.pcode INNER JOIN "+
				" dbo.t_Designation AS dsg ON emp.desigid = dsg.desigid INNER JOIN "+
				" dbo.t_Department AS dpt ON dsg.deptid = dpt.deptid INNER JOIN "+
				" dbo.cv_country ON er.Country = dbo.cv_country.countryid "+
				" WHERE (er.reqid = "+Label54.Text+")";


			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			string docID=ds.Tables[0].Rows[0]["filename"].ToString();
			docID=docID.Replace(".jpg","");

			SetText(ds,Label5,"trainingid");
			SetText(ds,Label52,"pcode");
			SetText(ds,Label53,"AddFlag");
			SetText(ds,Label51,"name");
			SetText(ds,Label48,"TrainingTitle");
			SetText(ds,Label49,"deptname");
			SetText(ds,Label50,"designation");
			SetText(ds,Label47,"TrainingField");
			SetText(ds,Label46,"TrainingMonthYear");
			SetText(ds,Label45,"Duration");
			SetText(ds,Label39,"InstituteName");
			SetText(ds,Label38,"cname");
			SetText(ds,Label10,"city");

			if(docID=="")
			{
				Label6.Attributes.Clear();
				Label6.Text="&nbsp;";
					
			}
			else
			{
				Label6.Attributes.Add("onclick","window.showModalDialog('ViewDocuments.aspx?docID="+docID+"','dialogWidth:900px; dialogHeight:800px;status:no')");
				Label6.Text="View";
			}

			DocReqFromHR("39",Label82,Label83);
		}

		private void cmdAccTraining_Click(object sender, System.EventArgs e)
		{
			Accept(cmdAccTraining,cmdRejTraining,lblTrainingMsg,txtTrainingMsg,cmdSubmitTraining,"39");
		}

		private void cmdRejTraining_Click(object sender, System.EventArgs e)
		{
			Rejection(cmdAccTraining,cmdRejTraining,lblTrainingMsg,txtTrainingMsg,cmdSubmitTraining,"39");
		}

		private void cmdSubmitTraining_Click(object sender, System.EventArgs e)
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();

			SqlTransaction tran=conn.BeginTransaction();

			string sql=" SELECT reqid, trainingid, PCode, TrainingTitle, TrainingField, TrainingFieldOther, TrainingMonth, TrainingYear, Duration, DurationUnit, InstituteName, Country, "+
				" City, CityOther, TrainingInGeo, c_at, Addflag, wfid, AppFlag, AppRejDate, RejCode, AppRejBy "+
				" FROM dbo.cv_TrainingReq "+
				" WHERE (reqid = "+Label54.Text+")";

			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			da.SelectCommand.Transaction=tran;
			DataSet ds=new DataSet();
			da.Fill(ds);
			string trainingid=ds.Tables[0].Rows[0]["trainingid"].ToString();
			string pcode=ds.Tables[0].Rows[0]["pcode"].ToString();

			if(lblTrainingMsg.Text=="Acceptance Message To Member TeamGEO")
			{
				string sqlcmd="";
				if(Label53.Text=="New Training")
				{
					sqlcmd="insert into cv_training(PCode,TrainingTitle,TrainingField,TrainingFieldOther,TrainingMonth,TrainingYear,Duration,DurationUnit,InstituteName,Country,City,CityOther,TrainingInGeo) "+
						" values(@PCode,@TrainingTitle,@TrainingField,@TrainingFieldOther,@TrainingMonth,@TrainingYear,@Duration,@DurationUnit,@InstituteName,@Country,@City,@CityOther,@TrainingInGeo)";

				}
				else if(Label53.Text=="Change Training Info")
				{
					sqlcmd="update cv_training set PCode=@PCode,TrainingTitle=@TrainingTitle,TrainingField=@TrainingField,TrainingFieldOther=@TrainingFieldOther,TrainingMonth=@TrainingMonth,TrainingYear=@TrainingYear,Duration=@Duration,DurationUnit=@DurationUnit,InstituteName=@InstituteName,Country=@Country,City=@City,CityOther=@CityOther,TrainingInGeo=@TrainingInGeo where trainingid="+Label54.Text;
				}

				else if (Label53.Text=="Remove Training Info")
				{
					sqlcmd="update cv_training set isactive=0 where trainingid="+Label5.Text;
					SqlCommand cmd2=new SqlCommand(sqlcmd,conn);
					cmd2.Transaction=tran;
					cmd2.ExecuteNonQuery();
					pnlTrainingDetail.Visible=false;
					dgTraining.SelectedIndex=0;
					UpdateRequest4Accept(conn,tran,"cv_TrainingReq",Label54.Text,"1");
					Bulletin.PostReqMessage(conn,tran,txtTrainingMsg.Text,Session["user_id"].ToString(),pcode,3,"Training","cv_TrainingReq");
					tran.Commit();
					FillReqGrid(dgTraining,"cv_TrainingReq","trainingid","New Training","Change of Training Info","Removal of Training");
					pnlTrainingDetail.Visible=false;
					return;
				}

				SqlCommand cmd=new SqlCommand(sqlcmd,conn);
				cmd.Transaction=tran;
				SetParameterValue(cmd,ds,"PCode");
				SetParameterValue(cmd,ds,"TrainingTitle");
				SetParameterValue(cmd,ds,"TrainingField");
				SetParameterValue(cmd,ds,"TrainingFieldOther");
				SetParameterValue(cmd,ds,"TrainingMonth");
				SetParameterValue(cmd,ds,"TrainingYear");
				SetParameterValue(cmd,ds,"Duration");
				SetParameterValue(cmd,ds,"DurationUnit");
				SetParameterValue(cmd,ds,"InstituteName");
				SetParameterValue(cmd,ds,"Country");
				SetParameterValue(cmd,ds,"City");
				SetParameterValue(cmd,ds,"CityOther");
				SetParameterValue(cmd,ds,"TrainingInGeo");
				cmd.ExecuteNonQuery();
				pnlTrainingDetail.Visible=false;
				dgTraining.SelectedIndex=0;
				UpdateRequest4Accept(conn,tran,"cv_TrainingReq",Label54.Text,"1");
				Bulletin.PostReqMessage(conn,tran,txtTrainingMsg.Text,Session["user_id"].ToString(),pcode,3,"Training","cv_TrainingReq");
				tran.Commit();
				FillReqGrid(dgTraining,"cv_TrainingReq","trainingid","New Training","Change of Training Info","Removal of Training");
				pnlTrainingDetail.Visible=false;
				ResetPanels();
			}
			else if(lblTrainingMsg.Text=="Rejection Message To Member TeamGEO")
			{
				UpdateRequest4Accept(conn,tran, "cv_TrainingReq",Label54.Text,"0");
				Bulletin.PostReqMessage(conn,tran,txtTrainingMsg.Text,Session["user_id"].ToString(),pcode,3,"Training","cv_TrainingReq");
				tran.Commit();
				FillReqGrid(dgTraining,"cv_TrainingReq","trainingid","New Training","Change of Training Info","Removal of Training");
				pnlTrainingDetail.Visible=false;
				ResetPanels();
			}
		}

		private void dgExp_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			// Reset Remaining Grid Selected Index
			int idx=dgExp.SelectedIndex;
			dgExp.SelectedIndex=-1;
			Datagrid1.SelectedIndex=-1;
			Datagrid2.SelectedIndex=-1;
			dgTraining.SelectedIndex=-1;
			dgMyRequest.SelectedIndex=-1;
			dgExp.SelectedIndex=idx;
			//////////////////////////////////
			
			//pnlMyReq.Visible=false;
			pnlEdu.Visible=false;
			pnlFamily.Visible=false;
			pnlTraining.Visible=false;
			pnlExp.Visible=true;

			
			Button12.Enabled=true;
			Button11.Enabled=true;
			Label55.Text="";
			TextBox3.Text="";
			Button10.Enabled=false;
			Button9.Enabled=true;

			string id=dgExp.SelectedItem.Cells[1].Text;
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();

			string sql=" SELECT er.filename, er.reqid, er.PCode, er.c_at, " +
				" CASE er.Addflag WHEN 1 THEN 'Request for new Experience' WHEN 2 THEN 'Request for Change' WHEN 3 THEN 'Request for Remvoe' END AS " +
				" AddFlag, er.AppFlag, em.name, DATEADD(day, f.timeline, er.c_at) AS Timeline, " +
				" CASE er.AreaofExperience WHEN - 1 THEN er.AreaofExperienceOther ELSE " +
				" (SELECT expfield " +
				" FROM cv_experincefields " +
				" WHERE expfieldid = er.AreaofExperience) END AS AreaofExperience, er.CompanyName, er.Position, er.Department, " +
				" dbo.Month_Name(er.ServedFromMonth) + ' ' + CAST(er.ServedFromYear AS varchar) AS ServedFrom, " +
				" CASE er.ServedTillMonth WHEN -1 THEN 'Still Working' ELSE dbo.month_name(er.ServedTillMonth) + ' ' + CAST(er.servedtillyear AS varchar) " +
				" END AS ServedTill, " +
				" CASE er.Subordinate WHEN 0 THEN 'None' WHEN 1 THEN '1 to 5' WHEN 2 THEN '6 to 10' WHEN 3 THEN '11 to 15' WHEN 4 THEN '16 to 20' WHEN 5 THEN " +
				" '21 to 30' WHEN 6 THEN '31 to 45' WHEN 7 THEN '46 to 60' WHEN 8 THEN 'Above 60' END AS Expr1, er.ContactPerson, er.OrganizationEmail, " +
				" er.OrganizationAddress, er.OrganizationPhone, er.MajorResponsibilities, er.Achivements, er.SalarySymb + '. ' + er.LastSalary AS LastSalary, " +
				" er.MobileSymb + '. ' + er.MobileEntitlement AS MobileEntitlement, er.Petrol + ' liters' AS FuelEntilement, " +
				" CASE er.CarConveyance WHEN 0 THEN 'None' WHEN 1 THEN 'Company Maintained Car' WHEN 2 THEN 'Conveyance Allowance' WHEN 3 THEN 'Leased Car' " +
				" WHEN 4 THEN 'Pick & Drop' END AS CarEntitlement, er.CarDescription, er.ResonToLeave, em.pcode AS Expr2, dsg.designation, dpt.deptname, " +
				" er.ReportingTo " +
				" FROM dbo.t_fieldsmgt AS f INNER JOIN " +
				" dbo.cv_ExperinceReq AS er ON f.f_id = er.wfid INNER JOIN " +
				" dbo.t_fieldmgtspecialist AS fs ON f.f_id = fs.f_id INNER JOIN " +
				" dbo.t_Employee AS em ON er.PCode = em.pcode INNER JOIN " +
				" dbo.t_Designation AS dsg ON em.desigid = dsg.desigid INNER JOIN " +
				" dbo.t_Department AS dpt ON dsg.deptid = dpt.deptid " +
				" WHERE er.reqid = "+id+"";
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.Fill(ds,"req");
			SetText(ds,lblReqID,"reqid");
			SetText(ds,Label68,"AddFlag");
			SetText(ds,Label67,"pcode");
			SetText(ds,Label66,"name");
			//SetText(ds,Label,"");
			SetText(ds,Label65,"designation");
			SetText(ds,Label64,"deptname");
			SetText(ds,Label63,"AreaofExperience");
			SetText(ds,Label62,"CompanyName");
			SetText(ds,Label61,"Position");
			SetText(ds,Label60,"Department");
			SetText(ds,Label59,"ServedFrom");
			SetText(ds,Label58,"ServedTill");
			SetText(ds,Label57,"ReportingTo");
			SetText(ds,Label69,"Expr1");
			SetText(ds,Label70,"ContactPerson");
			SetText(ds,Label71,"OrganizationEmail");
			SetText(ds,Label72,"OrganizationAddress");
			SetText(ds,Label73,"OrganizationPhone");
			SetText(ds,Label74,"MajorResponsibilities");
			SetText(ds,Label75,"Achivements");
			SetText(ds,Label76,"LastSalary");
			SetText(ds,Label77,"MobileEntitlement");
			SetText(ds,Label78,"FuelEntilement");
			SetText(ds,Label79,"CarEntitlement");
			SetText(ds,Label80,"CarDescription");
			SetText(ds,Label81,"ResonToLeave");
			string docID=ds.Tables[0].Rows[0]["filename"].ToString();
			docID=docID.Replace(".jpg","");
			if(docID=="")
			{
				Label56.Attributes.Clear();
				Label56.Text="&nbsp;";
					
			}
			else
			{
				Label56.Attributes.Add("onclick","window.showModalDialog('ViewDocuments.aspx?docID="+docID+"','dialogWidth:900px; dialogHeight:800px;status:no')");
				Label56.Text="View";
			}

			pnlExpDetail.Visible=true;
			conn.Close();
			DocReqFromHR("42",Label84,Label85);

		}

		private void Button12_Click(object sender, System.EventArgs e)
		{
			Accept(Button12,Button11,Label55,TextBox3,Button10,"42");
		}

		private void Button11_Click(object sender, System.EventArgs e)
		{
			Rejection(Button12,Button11,Label55,TextBox3,Button10,"43");
		}

		private void Button10_Click(object sender, System.EventArgs e)
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			SqlTransaction tran=conn.BeginTransaction();

			string sql=" SELECT reqid, ExpID, PCode, AreaofExperience, AreaOfExperienceOther, CompanyName, Position, Department, ServedFromMonth, ServedFromYear, " +
				" ServedTillMonth, ServedTillYear, ReportingTo, Subordinate, OrganizationAddress, OrganizationPhone, ContactPerson, OrganizationEmail, " +
				" MajorResponsibilities, Achivements, LastSalary, MobileEntitlement, Petrol, CarConveyance, CarDescription, ResonToLeave, cdate, SalarySymb, " +
				" MobileSymb, c_at, Addflag, wfid, AppFlag, AppRejDate, RejCode, AppRejBy " +
				" FROM dbo.cv_ExperinceReq " +
				" WHERE (reqid = "+lblReqID.Text+") ";
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			da.SelectCommand.Transaction=tran;
			DataSet ds=new DataSet();
			da.Fill(ds);

			string sqlInsert="";
			string expid=ds.Tables[0].Rows[0]["ExpID"].ToString();
			string pcode=ds.Tables[0].Rows[0]["pcode"].ToString();


			if(Label55.Text=="Acceptance Message To Member TeamGEO")
			{

				if(Label68.Text=="Request for new Experience")
				{
					sqlInsert=" INSERT INTO cv_Experince (PCode ,AreaofExperience ,AreaOfExperienceOther ,CompanyName ,Position ,Department ,ServedFromMonth ,ServedFromYear ,ServedTillMonth ,ServedTillYear ,ReportingTo ,Subordinate ,OrganizationAddress ,OrganizationPhone ,ContactPerson ,OrganizationEmail ,MajorResponsibilities ,Achivements ,LastSalary ,MobileEntitlement ,Petrol ,CarConveyance ,CarDescription ,ResonToLeave ,cdate ,SalarySymb ,MobileSymb) " +
						" VALUES (@PCode ,@AreaofExperience ,@AreaOfExperienceOther ,@CompanyName ,@Position ,@Department ,@ServedFromMonth ,@ServedFromYear ,@ServedTillMonth ,@ServedTillYear ,@ReportingTo ,@Subordinate ,@OrganizationAddress ,@OrganizationPhone ,@ContactPerson ,@OrganizationEmail ,@MajorResponsibilities ,@Achivements ,@LastSalary ,@MobileEntitlement ,@Petrol ,@CarConveyance ,@CarDescription ,@ResonToLeave ,getdate() ,@SalarySymb ,@MobileSymb) ";
				}
				else if (Label68.Text=="Request for Change")
				{
					sqlInsert=" UPDATE cv_Experince SET PCode = @PCode, AreaofExperience = @AreaofExperience, " +
						" AreaOfExperienceOther = @AreaOfExperienceOther, CompanyName = @CompanyName, " +
						" Position = @Position, Department = @Department, ServedFromMonth = @ServedFromMonth, " +
						" ServedFromYear = @ServedFromYear, ServedTillMonth = @ServedTillMonth, ServedTillYear = " +
						" @ServedTillYear, ReportingTo = @ReportingTo, Subordinate = @Subordinate, " +
						" OrganizationAddress = @OrganizationAddress, OrganizationPhone = @OrganizationPhone, " +
						" ContactPerson = @ContactPerson, OrganizationEmail = @OrganizationEmail, " +
						" MajorResponsibilities = @MajorResponsibilities, Achivements = @Achivements, LastSalary = " +
						" @LastSalary, MobileEntitlement = @MobileEntitlement, Petrol = @Petrol, CarConveyance = " +
						" @CarConveyance, CarDescription = @CarDescription, ResonToLeave = @ResonToLeave, " +
						" SalarySymb = @SalarySymb, MobileSymb = @MobileSymb WHERE ExpID = " +expid;

				}
				else if(Label68.Text=="Request for Remvoe")
				{
					string sql2="update cv_Experince set isactive=0, mdate=getdate(), mby='"+Session["user_id"].ToString()+"' where expid="+expid;
					SqlCommand cmd2=new SqlCommand(sql2,conn);
					cmd2.Transaction=tran;
					cmd2.ExecuteNonQuery();
					Bulletin.PostReqMessage(conn,tran,TextBox3.Text,Session["user_id"].ToString(),pcode,3,"Experince","cv_ExperinceReq");
					UpdateRequest4Accept(conn,tran,"cv_Experincereq",lblReqID.Text,"1");
					tran.Commit();
					pnlExpDetail.Visible=false;
					FillReqGrid(dgExp,"cv_ExperinceReq","ExpID","New Experience","Change Experience Info","Removal of Experience");;
					dgExp.SelectedIndex=-1;
					return;
				}

				SqlParameter _ExpID = new SqlParameter("@ExpID",SqlDbType.Int);
				SqlParameter _PCode = new SqlParameter("@PCode",SqlDbType.Char, 10);
				SqlParameter _AreaofExperience = new SqlParameter("@AreaofExperience",SqlDbType.Int);
				SqlParameter _AreaOfExperienceOther = new SqlParameter("@AreaOfExperienceOther",SqlDbType.VarChar, 200);
				SqlParameter _CompanyName = new SqlParameter("@CompanyName",SqlDbType.VarChar, 200);
				SqlParameter _Position = new SqlParameter("@Position",SqlDbType.VarChar, 200);
				SqlParameter _Department = new SqlParameter("@Department",SqlDbType.VarChar, 200);
				SqlParameter _ServedFromMonth = new SqlParameter("@ServedFromMonth",SqlDbType.Int);
				SqlParameter _ServedFromYear = new SqlParameter("@ServedFromYear",SqlDbType.Int);
				SqlParameter _ServedTillMonth = new SqlParameter("@ServedTillMonth",SqlDbType.Int);
				SqlParameter _ServedTillYear = new SqlParameter("@ServedTillYear",SqlDbType.Int);
				SqlParameter _ReportingTo = new SqlParameter("@ReportingTo",SqlDbType.VarChar, 200);
				SqlParameter _Subordinate = new SqlParameter("@Subordinate",SqlDbType.Int);
				SqlParameter _OrganizationAddress = new SqlParameter("@OrganizationAddress",SqlDbType.VarChar, 500);
				SqlParameter _OrganizationPhone = new SqlParameter("@OrganizationPhone",SqlDbType.VarChar, 100);
				SqlParameter _ContactPerson = new SqlParameter("@ContactPerson",SqlDbType.VarChar, 100);
				SqlParameter _OrganizationEmail = new SqlParameter("@OrganizationEmail",SqlDbType.VarChar, 200);
				SqlParameter _MajorResponsibilities = new SqlParameter("@MajorResponsibilities",SqlDbType.VarChar, 1000);
				SqlParameter _Achivements = new SqlParameter("@Achivements",SqlDbType.VarChar, 1000);
				SqlParameter _LastSalary = new SqlParameter("@LastSalary",SqlDbType.VarChar, 100);
				SqlParameter _MobileEntitlement = new SqlParameter("@MobileEntitlement",SqlDbType.VarChar, 100);
				SqlParameter _Petrol = new SqlParameter("@Petrol",SqlDbType.VarChar, 50);
				SqlParameter _CarConveyance = new SqlParameter("@CarConveyance",SqlDbType.Int);
				SqlParameter _CarDescription = new SqlParameter("@CarDescription",SqlDbType.VarChar, 100);
				SqlParameter _ResonToLeave = new SqlParameter("@ResonToLeave",SqlDbType.VarChar, 1000);
				SqlParameter _SalarySymb = new SqlParameter("@SalarySymb",SqlDbType.VarChar, 5);
				SqlParameter _MobileSymb = new SqlParameter("@MobileSymb",SqlDbType.VarChar, 5);


				_PCode.Value = ds.Tables[0].Rows[0]["PCode"].ToString();
				_AreaofExperience.Value = ds.Tables[0].Rows[0]["AreaofExperience"].ToString();
				_AreaOfExperienceOther.Value = ds.Tables[0].Rows[0]["AreaOfExperienceOther"].ToString();
				_CompanyName.Value = ds.Tables[0].Rows[0]["CompanyName"].ToString();
				_Position.Value = ds.Tables[0].Rows[0]["Position"].ToString();
				_Department.Value = ds.Tables[0].Rows[0]["Department"].ToString();
				_ServedFromMonth.Value = ds.Tables[0].Rows[0]["ServedFromMonth"].ToString();
				_ServedFromYear.Value = ds.Tables[0].Rows[0]["ServedFromYear"].ToString();
				_ServedTillMonth.Value = ds.Tables[0].Rows[0]["ServedTillMonth"].ToString();
				if(ds.Tables[0].Rows[0]["ServedTillMonth"].ToString()=="0")
					_ServedTillYear.Value = DBNull.Value;
				else
					_ServedTillYear.Value = ds.Tables[0].Rows[0]["ServedTillYear"].ToString();
				_ReportingTo.Value = ds.Tables[0].Rows[0]["ReportingTo"].ToString();
				_Subordinate.Value = ds.Tables[0].Rows[0]["Subordinate"].ToString();
				_OrganizationAddress.Value = ds.Tables[0].Rows[0]["OrganizationAddress"].ToString();
				_OrganizationPhone.Value = ds.Tables[0].Rows[0]["OrganizationPhone"].ToString();
				_ContactPerson.Value = ds.Tables[0].Rows[0]["ContactPerson"].ToString();
				_OrganizationEmail.Value = ds.Tables[0].Rows[0]["OrganizationEmail"].ToString();
				_MajorResponsibilities.Value = ds.Tables[0].Rows[0]["MajorResponsibilities"].ToString();
				_Achivements.Value = ds.Tables[0].Rows[0]["Achivements"].ToString();
				_LastSalary.Value = ds.Tables[0].Rows[0]["LastSalary"].ToString();
				_MobileEntitlement.Value = ds.Tables[0].Rows[0]["MobileEntitlement"].ToString();
				_Petrol.Value = ds.Tables[0].Rows[0]["Petrol"].ToString();
				_CarConveyance.Value = ds.Tables[0].Rows[0]["CarConveyance"].ToString();
				_CarDescription.Value = ds.Tables[0].Rows[0]["CarDescription"].ToString();
				_ResonToLeave.Value = ds.Tables[0].Rows[0]["ResonToLeave"].ToString();
				
				_SalarySymb.Value = ds.Tables[0].Rows[0]["SalarySymb"].ToString();
				_MobileSymb.Value = ds.Tables[0].Rows[0]["MobileSymb"].ToString();

				SqlCommand cmd=new SqlCommand(sqlInsert,conn);
				cmd.Transaction=tran;
				cmd.Parameters.Add(_PCode);
				cmd.Parameters.Add(_AreaofExperience);
				cmd.Parameters.Add(_AreaOfExperienceOther);
				cmd.Parameters.Add(_CompanyName);
				cmd.Parameters.Add(_Position);
				cmd.Parameters.Add(_Department);
				cmd.Parameters.Add(_ServedFromMonth);
				cmd.Parameters.Add(_ServedFromYear);
				cmd.Parameters.Add(_ServedTillMonth);
				cmd.Parameters.Add(_ServedTillYear);
				cmd.Parameters.Add(_ReportingTo);
				cmd.Parameters.Add(_Subordinate);
				cmd.Parameters.Add(_OrganizationAddress);
				cmd.Parameters.Add(_OrganizationPhone);
				cmd.Parameters.Add(_ContactPerson);
				cmd.Parameters.Add(_OrganizationEmail);
				cmd.Parameters.Add(_MajorResponsibilities);
				cmd.Parameters.Add(_Achivements);
				cmd.Parameters.Add(_LastSalary);
				cmd.Parameters.Add(_MobileEntitlement);
				cmd.Parameters.Add(_Petrol);
				cmd.Parameters.Add(_CarConveyance);
				cmd.Parameters.Add(_CarDescription);
				cmd.Parameters.Add(_ResonToLeave);
				cmd.Parameters.Add(_SalarySymb);
				cmd.Parameters.Add(_MobileSymb);

				cmd.ExecuteNonQuery();
				UpdateRequest4Accept(conn,tran,"cv_Experincereq",lblReqID.Text,"1");
				Bulletin.PostReqMessage(conn,tran,TextBox3.Text,Session["user_id"].ToString(),pcode,3,"Experience","cv_ExperinceReq");
				tran.Commit();
				FillReqGrid(dgExp,"cv_ExperinceReq","ExpID","New Experience","Change Experience Info","Removal of Experience");;
				pnlExpDetail.Visible=false;
				dgExp.SelectedIndex=-1;
				ResetPanels();
			}
			else if (Label55.Text=="Rejection Message To Member TeamGEO")
			{
				UpdateRequest4Accept(conn,tran,"cv_ExperinceReq",lblReqID.Text,"0");
				Bulletin.PostReqMessage(conn,tran,TextBox3.Text,Session["user_id"].ToString(),pcode,4,"Experince","cv_ExperinceReq");
				tran.Commit();
				pnlExpDetail.Visible=false;
				FillReqGrid(dgExp,"cv_ExperinceReq","ExpID","New Experience","Change Experience Info","Removal of Experience");;
				dgExp.SelectedIndex=-1;
				FillReqGrid(dgExp,"cv_ExperinceReq","ExpID","New Experience","Change Experience Info","Removal of Experience");;
				ResetPanels();
			}
		}

		private void Button9_Click(object sender, System.EventArgs e)
		{
			pnlExpDetail.Visible=false;
			ResetPanels();
		}


		public void GetGrievance()
		{
			this.ddGrievance.Items.Clear();
			ListItem itm0=new ListItem("Select--Grievance","0");
			this.ddGrievance.Items.Add(itm0);
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select * from t_grievance where isactive=1",con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
				this.ddGrievance.Items.Add(itm);
			}
			rd.Close();
			con.Close();
			con.Dispose();
		}


		private void lnkRedirect_Click(object sender, System.EventArgs e)
		{
			GetGrievance();
			this.ddClosingCode.SelectedIndex=0;
			this.ddClosingCode.Enabled=false;
			this.pnlRedirect.Visible=true;
			this.pnlInterimDate.Visible=false;
		}

		public void GetGrievanceSubType(string Val)
		{
			this.ddSubGrivance.Items.Clear();
			ListItem itm0=new ListItem("Select--Grievance","0");
			this.ddSubGrivance.Items.Add(itm0);
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select s.sid,s.sgrievance,g.grievance from t_grievancesubtype s,t_grievance g where g.gid=s.gid and g.gid="+Val+"",con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
				this.ddSubGrivance.Items.Add(itm);
			}
			rd.Close();
			con.Close();
			con.Dispose();
		}

		private void ddGrievance_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			GetGrievanceSubType(this.ddGrievance.SelectedItem.Value);
		}

		private void ddSubGrivance_SelectedIndexChanged(object sender, System.EventArgs e)
		{
		
		}

		private void btnGrievanceSubmit_Click(object sender, System.EventArgs e)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlTransaction trans=con.BeginTransaction();
			SqlCommand cmd=null;
			SqlDataReader rd=null;
			try
			{
				if(this.ddClosingCode.SelectedValue!="0")
				{
					string conQuery="";
					if(this.pnlInterimDate.Visible)
					{
						conQuery="update t_employeegrievancerequest set specialistcode='"+Session["user_id"].ToString()+"',requesthandledate=getdate(),closingcode="+this.ddClosingCode.SelectedItem.Value+",requeststatus=5,interimclosedate='"+cpInterim.SelectedDate.ToString()+"' where rid="+this.lblTitle.ToolTip+"";
					}
					else
					{
						conQuery="update t_employeegrievancerequest set specialistcode='"+Session["user_id"].ToString()+"',requesthandledate=getdate(),closingcode="+this.ddClosingCode.SelectedItem.Value+",requeststatus=3 where rid="+this.lblTitle.ToolTip+"";
					}
					cmd=new SqlCommand(conQuery,con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
				}
				if(this.txtComments.Text.Length>0 && this.pnlRedirect.Visible==false)
				{
					cmd=new SqlCommand("select d.designation,dept.deptname,c.cityname from t_employee e,t_designation d,t_department dept,t_city c where e.desigid=d.desigid and dept.deptid=d.deptid and e.pcode='"+Session["user_id"].ToString()+"' and e.station=c.cityid",con);
					cmd.Transaction=trans;
					rd=cmd.ExecuteReader();
					rd.Read();
					string designation=rd[0].ToString();
					string department=rd[1].ToString();
					string city=rd[2].ToString();
					rd.Close();
					cmd=new SqlCommand("insert into t_grievanceblog(rid,comment,sendon,sendby,senderdesignation,senderstation,senderdepartment) values(@rid,@comment,getdate(),@sendby,@senderdesignation,@senderstation,@senderdepartment)",con);
					SqlParameter _rid=new SqlParameter("@rid",SqlDbType.Int);
					SqlParameter _comment=new SqlParameter("@comment",SqlDbType.Text);
					SqlParameter _sendby=new SqlParameter("@sendby",SqlDbType.VarChar);
					SqlParameter _designation=new SqlParameter("@senderdesignation",SqlDbType.Text);
					SqlParameter _station=new SqlParameter("@senderstation",SqlDbType.Text);
					SqlParameter _department=new SqlParameter("@senderdepartment",SqlDbType.Text);
					_rid.Value=this.lblTitle.ToolTip;
					_comment.Value=this.txtComments.Text;
					_sendby.Value=Session["user_id"].ToString();
					_designation.Value=designation;
					_department.Value=department;
					_station.Value=city;
					cmd.Parameters.Add(_rid);
					cmd.Parameters.Add(_comment);
					cmd.Parameters.Add(_sendby);
					cmd.Parameters.Add(_designation);
					cmd.Parameters.Add(_station);
					cmd.Parameters.Add(_department);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					cmd=new SqlCommand("update t_employeegrievancerequest set newusermsg=1 where rid="+this.lblTitle.ToolTip+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
				}
				if(this.pnlRedirect.Visible)
				{
					cmd=new SqlCommand("select d.designation,dept.deptname,c.cityname from t_employee e,t_designation d,t_department dept,t_city c where e.desigid=d.desigid and dept.deptid=d.deptid and e.pcode='"+Session["user_id"].ToString()+"' and e.station=c.cityid",con);
					cmd.Transaction=trans;
					rd=cmd.ExecuteReader();
					rd.Read();
					string designation=rd[0].ToString();
					string department=rd[1].ToString();
					string city=rd[2].ToString();
					rd.Close();
					cmd=new SqlCommand("insert into t_grievanceblog(rid,comment,sendon,sendby,senderdesignation,senderstation,senderdepartment) values(@rid,@comment,getdate(),@sendby,@senderdesignation,@senderstation,@senderdepartment)",con);
					SqlParameter _rid=new SqlParameter("@rid",SqlDbType.Int);
					SqlParameter _comment=new SqlParameter("@comment",SqlDbType.Text);
					SqlParameter _sendby=new SqlParameter("@sendby",SqlDbType.VarChar);
					SqlParameter _designation=new SqlParameter("@senderdesignation",SqlDbType.Text);
					SqlParameter _station=new SqlParameter("@senderstation",SqlDbType.Text);
					SqlParameter _department=new SqlParameter("@senderdepartment",SqlDbType.Text);
					_rid.Value=this.lblTitle.ToolTip;
					string message="";
					if(this.txtComments.Text.Length>0)
					{
						message=this.txtComments.Text+"<br><br>";
						message+="Grievance has been re-directed";
					}
					else
					{
						message="Grievance has been re-directed";
					}
					_comment.Value=message;
					_sendby.Value=Session["user_id"].ToString();
					_designation.Value=designation;
					_department.Value=department;
					_station.Value=city;
					cmd.Parameters.Add(_rid);
					cmd.Parameters.Add(_comment);
					cmd.Parameters.Add(_sendby);
					cmd.Parameters.Add(_designation);
					cmd.Parameters.Add(_station);
					cmd.Parameters.Add(_department);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					cmd=new SqlCommand("update t_employeegrievancerequest set newusermsg=1,sgid="+this.ddSubGrivance.SelectedItem.Value+",closingcode=null where rid="+this.lblTitle.ToolTip+"",con);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
				}
				//Bulletin.PostMessage("Grievance Request:"+this.lblTitle.Text,"Grievance Request:"+this.lblTitle.Text+" has been answered",Session["user_id"].ToString(),7,2,"Geo Raabta",this.txtDetail.ToolTip,con,trans);
				trans.Commit();
				this.pnlgView.Visible=false;
				this.txtComments.Text="";
				FillGrievanceGrid();
			}
			catch(Exception ex)
			{
				Response.Write(ex.Message);
				trans.Rollback();
			}
			con.Close();
			con.Dispose();
		}

		public void GetComments(string ID)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlDataAdapter dr=new SqlDataAdapter("select g.comment,e.pcode,e.name as sendby,g.senderdesignation,g.senderdepartment,g.sendon from t_grievanceblog g,t_employee e where e.pcode=g.sendby and rid="+ID+"order by g.sendon",con);
			DataSet ds=new DataSet();
			dr.Fill(ds,"Record");
			dgBlog.DataSource=ds;
			dgBlog.DataBind();
			dgBlog.Visible=true;
			this.pnlbView.Visible=true;
			con.Close();
			con.Dispose();
		}
		public void GetComments(string ID,bool flag)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlDataAdapter dr=new SqlDataAdapter("select g.comment,e.pcode,e.name as sendby,g.senderdesignation,g.senderdepartment,g.sendon from t_grievanceblog g,t_employee e where e.pcode=g.sendby and rid="+ID+"order by g.sendon",con);
			DataSet ds=new DataSet();
			dr.Fill(ds,"Record");
			dgccBlog.DataSource=ds;
			dgccBlog.DataBind();
			dgccBlog.Visible=true;
			this.pnlcView.Visible=true;
			con.Close();
			con.Dispose();
		}


		private void dgMyGrievance_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			this.pnlRedirect.Visible=false;
			this.pnlInterimDate.Visible=false;
			this.pnlgView.Visible=true;
			this.ddClosingCode.Enabled=true;
			this.txtComments.Text="";
			
			this.lblRID.Text=this.dgMyGrievance.Items[this.dgMyGrievance.SelectedIndex].Cells[1].Text;
			this.lblTitle.Text=this.dgMyGrievance.Items[this.dgMyGrievance.SelectedIndex].Cells[2].Text;
			this.txtDetail.Text=this.dgMyGrievance.Items[this.dgMyGrievance.SelectedIndex].Cells[3].Text;
			this.lblTitle.ToolTip=this.dgMyGrievance.Items[this.dgMyGrievance.SelectedIndex].Cells[1].Text;
			this.txtDetail.ToolTip=this.dgMyGrievance.Items[this.dgMyGrievance.SelectedIndex].Cells[8].Text;
			this.lblGrievanceType.Text=this.dgMyGrievance.Items[this.dgMyGrievance.SelectedIndex].Cells[12].Text;
			this.lblGrievanceSubType.Text=this.dgMyGrievance.Items[this.dgMyGrievance.SelectedIndex].Cells[11].Text;
			
			this.pnlbView.Visible=true;
			GetComments(this.lblTitle.ToolTip);
			string Val=this.dgMyGrievance.Items[this.dgMyGrievance.SelectedIndex].Cells[10].Text;
			if(Val !="&nbsp;")
			{
				con=new SqlConnection(Connection.ConnectionString);
				con.Open();
				SqlTransaction trans=con.BeginTransaction();
				SqlCommand cmd=new SqlCommand("update t_employeegrievancerequest set newspmsg=0 where rid="+this.lblTitle.ToolTip+"",con);
				cmd.Transaction=trans;
				cmd.ExecuteNonQuery();
				trans.Commit();
				con.Close();
				con.Dispose();
				FillGrievanceGrid();
			}
			string sVal=this.dgMyGrievance.Items[this.dgMyGrievance.SelectedIndex].Cells[13].Text;
			if(sVal=="Interim Closed")
			{
				this.txtComments.Enabled=false;
				this.lnkRedirect.Enabled=false;
			}
			else
			{
				this.txtComments.Enabled=true;
				this.lnkRedirect.Enabled=true;
			}
		}

		private void ddClosingCode_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select interimclose from t_grievanceclosingcode where cid="+this.ddClosingCode.SelectedItem.Value+"",con);
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			if(rd.HasRows)
			{
				if(rd[0].ToString()=="1")
				{
					this.pnlInterimDate.Visible=true;
				}
				else
				{
					this.pnlInterimDate.Visible=false;
				}
			}
			else
			{
				this.pnlInterimDate.Visible=false;
			}
			rd.Close();
			con.Close();
			con.Dispose();
		}

		private void imgRequest_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			this.pnlbView.Visible=true;
			this.pnlEdu.Visible=true;
			this.pnlExp.Visible=true;
			this.pnlFamily.Visible=true;
			this.pnlMyReq.Visible=true;
			this.pnlTraining.Visible=true;
			this.pnlReOpen.Visible=false;
			this.pnlccGrievance.Visible=false;
			FillGrievanceGrid();

		}

		private void imgReopen_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			this.pnlbView.Visible=false;
			this.pnlEdu.Visible=false;
			this.pnlExp.Visible=false;
			this.pnlFamily.Visible=false;
			this.pnlMyReq.Visible=false;
			this.pnlTraining.Visible=false;
			this.pnlccGrievance.Visible=false;
			this.pnlReOpen.Visible=true;
			FillGrievanceGridReopen();
		}

		private void imgcCGrievance_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			this.pnlbView.Visible=false;
			this.pnlEdu.Visible=false;
			this.pnlExp.Visible=false;
			this.pnlFamily.Visible=false;
			this.pnlMyReq.Visible=false;
			this.pnlTraining.Visible=false;
			this.pnlccGrievance.Visible=false;
			this.pnlReOpen.Visible=false;
			this.pnlccGrievance.Visible=true;
			this.pnlcView.Visible=false;
			FillccGrievanceGrid();
			
		}

		private void dgMyccGrievance_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			this.lblccTitle.Text=this.dgMyccGrievance.Items[this.dgMyccGrievance.SelectedIndex].Cells[1].Text;
			this.txtccDetail.Text=this.dgMyccGrievance.Items[this.dgMyccGrievance.SelectedIndex].Cells[2].Text;
			this.lblccTitle.ToolTip=this.dgMyccGrievance.Items[this.dgMyccGrievance.SelectedIndex].Cells[6].Text;
			this.txtccDetail.ToolTip=this.dgMyccGrievance.Items[this.dgMyccGrievance.SelectedIndex].Cells[8].Text;
			this.lblccGrievanceType.Text=this.dgMyccGrievance.Items[this.dgMyccGrievance.SelectedIndex].Cells[12].Text;
			this.lblccGrievanceSubType.Text=this.dgMyccGrievance.Items[this.dgMyccGrievance.SelectedIndex].Cells[11].Text;
			this.pnlcView.Visible=true;
			GetComments(this.lblccTitle.ToolTip,true);
		}

		private void cmdCancelTraining_Click(object sender, System.EventArgs e)
		{
			ResetPanels();
			Table5.Visible=false;
		}

		private void imgPulse_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("PulseQueuelist.aspx");
		}
	}
}