using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Configuration;
using System.Data.SqlClient;
namespace GeoRabtaSite.admin
{
	/// <summary>
	/// Summary description for _default.
	/// </summary>
	public class RequestDetail : System.Web.UI.Page
	{
		protected Anthem.DataList clientDataList;
		protected Anthem.DataList serviceDataList;
		protected System.Web.UI.WebControls.Label txtReqID;
		protected System.Web.UI.WebControls.Label lblType;
		protected System.Web.UI.WebControls.Label lblTitle;
		protected System.Web.UI.WebControls.Label lblDesc;
		protected System.Web.UI.WebControls.Label lblDept;
		protected System.Web.UI.WebControls.Label lblReqGenDate;
		protected System.Web.UI.WebControls.Label lblTimeline;
		protected System.Web.UI.WebControls.Label lblPreReq;
		protected Anthem.LinkButton lblLockTimeline;
		protected Anthem.Panel pnlLockTimeStatus;
		protected Anthem.DropDownList ddTimelineStatus;
		protected Anthem.TextBox txtChangeTimelineReason;
		protected Anthem.Button btnLockTimeline;
		protected Anthem.LinkButton lblChangeTimeline;
		protected Anthem.Panel pnlChangeTimeline;
		protected Anthem.Panel Panel2;
		protected Anthem.DropDownList ddAdditionalDays;
		protected Anthem.TextBox txtReasonforDays;
		protected Anthem.Button btnAddDays;
		protected Anthem.Label lblRemain;
		protected Anthem.CheckBox chkLocked;
		protected Anthem.LinkButton lnkServiceAgent;
		protected Anthem.LinkButton lnkClientAgent;
		protected Anthem.Panel pnlClientAgents;
		protected System.Web.UI.WebControls.CheckBoxList chkClientAgents;
		protected Anthem.Panel pnlServiceAgent;
		protected System.Web.UI.WebControls.CheckBoxList chkServiceAgents;
		protected Anthem.DataList dlBlog;
		protected Anthem.LinkButton lnkClientToggle;
		protected Anthem.Panel pnlToggleClient;
		protected Anthem.LinkButton lnkServiceToggle;
		protected Anthem.Panel pnlToggleService;
		protected Anthem.Button btnChangeClient;
		protected Anthem.Button btnChangeService;
		protected Anthem.TextBox txtReply;
		protected Anthem.DropDownList ddActionCode;
		protected Anthem.Panel pnlActionCode;
		protected Anthem.Button btnReply;
		protected Anthem.Button btnReOpen;
		protected eWorld.UI.CalendarPopup CalendarPopup1;
		protected Anthem.TextBox txtReplica;
        SqlConnection con;
		private void Page_Load(object sender, System.EventArgs e)
		{
			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			try
			{
				string userId=Session["user_id"].ToString();
				if(userId=="")
				{
				 Response.Redirect("../login.aspx");
				}
			}
			catch
			{
				Response.Redirect("../login.aspx");
			}
			if(!IsPostBack)
			{
			 GetRequestDetail(Request.QueryString[0].ToString());
			 ServiceAgent();
			 ClientAgent();
			 UpdateBlog();
			 GetActionCode();
			}
			// Put user code to initialize the page here
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.btnLockTimeline.Click += new System.EventHandler(this.btnLockTimeline_Click);
			this.lblChangeTimeline.Click += new System.EventHandler(this.lblChangeTimeline_Click);
			this.btnAddDays.Click += new System.EventHandler(this.btnAddDays_Click);
			this.lnkClientToggle.Click += new System.EventHandler(this.lnkClientToggle_Click);
			this.lnkClientAgent.Click += new System.EventHandler(this.lnkClientAgent_Click);
			this.btnChangeClient.Click += new System.EventHandler(this.btnChangeClient_Click);
			this.lnkServiceToggle.Click += new System.EventHandler(this.lnkServiceToggle_Click);
			this.lnkServiceAgent.Click += new System.EventHandler(this.lnkServiceAgent_Click);
			this.btnChangeService.Click += new System.EventHandler(this.btnChangeService_Click);
			this.ddActionCode.SelectedIndexChanged += new System.EventHandler(this.ddActionCode_SelectedIndexChanged);
			this.btnReply.Click += new System.EventHandler(this.btnReply_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		public void ClientAgent()
		{
		 con=new SqlConnection(Connection.ConnectionString);
		 string Query="SELECT RTRIM(dbo.t_Employee.pcode) AS pcode, dbo.t_Employee.name "+
         "FROM dbo.t_Employee INNER JOIN "+
         "dbo.t_DesignationHistory ON dbo.t_Employee.pcode = dbo.t_DesignationHistory.pcode INNER JOIN "+
         "dbo.t_ClientRequest INNER JOIN "+
         "dbo.t_ClientRequestSpec ON dbo.t_ClientRequest.crId = dbo.t_ClientRequestSpec.crId INNER JOIN "+
         "dbo.t_SpecialistOwner ON dbo.t_ClientRequestSpec.crSpNo = dbo.t_SpecialistOwner.SpecialistNo ON "+ 
         "dbo.t_DesignationHistory.fdesigid = dbo.t_SpecialistOwner.s_fdesigid "+
         "WHERE (dbo.t_ClientRequest.crId = "+Request.QueryString[0].ToString()+") AND (dbo.t_ClientRequestSpec.crSpType = 2) AND (dbo.t_DesignationHistory.isactive = 1) AND (dbo.t_Employee.del = 1)";
		 SqlDataAdapter dr=new SqlDataAdapter(Query,con);
		 DataSet ds=new DataSet();
		 dr.Fill(ds,"Records");
		 this.clientDataList.DataSource=ds;
		 this.clientDataList.DataBind();
		}
		public void ServiceAgent()
		{
			con=new SqlConnection(Connection.ConnectionString);
			string Query="SELECT RTRIM(dbo.t_Employee.pcode) AS pcode, dbo.t_Employee.name "+
				"FROM dbo.t_Employee INNER JOIN "+
				"dbo.t_DesignationHistory ON dbo.t_Employee.pcode = dbo.t_DesignationHistory.pcode INNER JOIN "+
				"dbo.t_ClientRequest INNER JOIN "+
				"dbo.t_ClientRequestSpec ON dbo.t_ClientRequest.crId = dbo.t_ClientRequestSpec.crId INNER JOIN "+
				"dbo.t_SpecialistOwner ON dbo.t_ClientRequestSpec.crSpNo = dbo.t_SpecialistOwner.SpecialistNo ON "+ 
				"dbo.t_DesignationHistory.fdesigid = dbo.t_SpecialistOwner.s_fdesigid "+
				"WHERE (dbo.t_ClientRequest.crId = "+Request.QueryString[0].ToString()+") AND (dbo.t_ClientRequestSpec.crSpType = 1) AND (dbo.t_DesignationHistory.isactive = 1) AND (dbo.t_Employee.del = 1)";
			SqlDataAdapter dr=new SqlDataAdapter(Query,con);
			DataSet ds=new DataSet();
			dr.Fill(ds,"Records");
			this.serviceDataList.DataSource=ds;
			this.serviceDataList.DataBind();
		}
		public void GetRequestDetail(string reqNo)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			string Query="SELECT dbo.t_ClientRequest.crId, dbo.t_CommunicationFlow.cfReqDetail AS [Pre-req], dbo.t_ClientRequest.crTitle, dbo.t_ClientRequest.crDes,dbo.t_ClientRequest.crCstatus, "+ 
				"dbo.t_Department.deptname, dbo.t_ClientRequest.crTimeline, "+
				"CASE dbo.t_ClientRequest.crIsTimelinelocked WHEN 1 THEN 'Locked' WHEN 2 THEN 'Open' END AS [Timeline Status], dbo.t_HRClientFunction.fType, "+
				"dbo.t_ClientRequest.crReqdate "+
				"FROM dbo.t_ClientRequest INNER JOIN "+
				"dbo.t_HRClientFunction ON dbo.t_ClientRequest.crType = dbo.t_HRClientFunction.fId INNER JOIN "+
				"dbo.t_CommunicationFlow ON dbo.t_HRClientFunction.fId = dbo.t_CommunicationFlow.cfReqType INNER JOIN "+
				"dbo.t_Department ON dbo.t_ClientRequest.crDept = dbo.t_Department.deptid "+
				"WHERE (dbo.t_ClientRequest.crId = "+reqNo+")";
			SqlCommand cmd=new SqlCommand(Query,con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				this.txtReqID.Text=rd["crId"].ToString();
				this.lblType.Text=rd["fType"].ToString();
				this.lblPreReq.Text=rd["Pre-req"].ToString();
				this.lblTitle.Text=rd["crTitle"].ToString();
				this.lblDesc.Text=rd["crDes"].ToString();
				this.lblDept.Text=rd["deptname"].ToString();
				this.lblReqGenDate.Text=DateTime.Parse(rd["crReqdate"].ToString()).ToString("dd MMM,yyyy hh:mm:ss:tt");
				this.lblTimeline.Text=rd["crTimeline"].ToString();
				DateTime today=DateTime.Now;
				DateTime req=DateTime.Parse(rd["crReqdate"].ToString());
				TimeSpan remain=today-req;
				double remVal=double.Parse(lblTimeline.Text);
				double goneDays=Math.Round(remain.TotalDays,0);
				double Val=remVal-goneDays;
				lblRemain.Text=Val.ToString();
				lblRemain.UpdateAfterCallBack=true;
				if(rd["Timeline Status"].ToString()=="Locked")
				{
					this.chkLocked.Checked=true;
					this.lblChangeTimeline.Enabled=false;
				}
				else
				{
				 this.chkLocked.Checked=false;
				 this.lblChangeTimeline.Enabled=true;
				}
				if(rd["crCstatus"].ToString()=="3" || rd["crCstatus"].ToString()=="5")
				{
				 this.ddActionCode.Enabled=false;
				 this.ddActionCode.UpdateAfterCallBack=true;
				 btnReply.Enabled=false;
				 btnReOpen.Enabled=true;
				}
			}
			rd.Close();
			con.Close();
		  
		}
		public void lblLockTimeline_Click(object sender,EventArgs e)
		{
		 System.Threading.Thread.Sleep(1000);
		 pnlLockTimeStatus.Visible=!pnlLockTimeStatus.Visible;
		 pnlLockTimeStatus.UpdateAfterCallBack=true;
			if(this.chkLocked.Checked)
			{
				this.ddTimelineStatus.SelectedValue="1";
				this.ddTimelineStatus.UpdateAfterCallBack=true;
			}
			else
			{
				this.ddTimelineStatus.SelectedValue="2";
				this.ddTimelineStatus.UpdateAfterCallBack=true;
			}
		}

		public void btnLockTimeline_Click(object sender, System.EventArgs e)
		{
			System.Threading.Thread.Sleep(1000);
			if(this.ddTimelineStatus.SelectedValue=="1")
			{
				this.lblChangeTimeline.Enabled=false;
				lblChangeTimeline.UpdateAfterCallBack=true;
				this.chkLocked.Checked=true;
				this.chkLocked.UpdateAfterCallBack=true;
				AddTimelineComm(Request.QueryString[0].ToString(),txtChangeTimelineReason.Text,"8",Session["user_id"].ToString());
			    UpdateReqTimelineStatus(Request.QueryString[0].ToString(),"1");
			}
			else
			{
			 this.chkLocked.Checked=false;
			 this.lblChangeTimeline.Enabled=true;
			 lblChangeTimeline.UpdateAfterCallBack=true;
			 this.chkLocked.UpdateAfterCallBack=true;
			 AddTimelineComm(Request.QueryString[0].ToString(),txtChangeTimelineReason.Text,"9",Session["user_id"].ToString());
			 UpdateReqTimelineStatus(Request.QueryString[0].ToString(),"2");
			}
			UpdateBlog();
		}
		public void AddTimelineComm(string crId,string comm,string code,string postby)
		{
		 con=new SqlConnection(Connection.ConnectionString);
		 con.Open();
		 SqlCommand cmd=new SqlCommand("insert into t_clientcommunication(crId,commdate,Communication,actioncode,postby) values (@crId,getdate(),@Communication,@actioncode,@postby)",con);
		 SqlParameter _crId=new SqlParameter("@crId",SqlDbType.Int);
		 SqlParameter _com=new SqlParameter("@Communication",SqlDbType.VarChar);
		 SqlParameter _code=new SqlParameter("@actioncode",SqlDbType.Int);
		 SqlParameter _pcode=new SqlParameter("@postby",SqlDbType.VarChar);
		 _crId.Value=crId;
		 _com.Value=comm;
		 _code.Value=code;
		 _pcode.Value=postby;
		 cmd.Parameters.Add(_crId);
		 cmd.Parameters.Add(_com);
		 cmd.Parameters.Add(_code);
		 cmd.Parameters.Add(_pcode);
		 cmd.ExecuteNonQuery();
		 con.Close();
		}
		private void UpdateBlog()
		{
			SqlConnection con=new SqlConnection(Connection.ConnectionString);
			SqlDataAdapter da = new SqlDataAdapter("spGetCommunications", con);
			da.SelectCommand.Parameters.Add("@crid", Request.QueryString[0].ToString());
			da.SelectCommand.CommandType = CommandType.StoredProcedure;
			DataSet ds = new DataSet();
			da.Fill(ds);
			dlBlog.DataSource = ds.Tables[0].DefaultView;
			dlBlog.DataBind();
			dlBlog.UpdateAfterCallBack=true;
			con.Dispose();
		}
		public void UpdateReqTimeline(string crId)
		{
		 con=new SqlConnection(Connection.ConnectionString);
		 con.Open();
		 SqlCommand cmd=new SqlCommand("update t_clientrequest set crtimeline=@timeline where crId="+crId+"",con);
		 SqlParameter _timeline=new SqlParameter("@timeline",SqlDbType.Int);
		 double Val=double.Parse(lblTimeline.Text);
		 Val+=double.Parse(ddAdditionalDays.SelectedValue);
		 _timeline.Value=Val;
		 cmd.Parameters.Add(_timeline);
		 cmd.ExecuteNonQuery();
		 con.Close();
		}
		public void UpdateReqTimelineStatus(string crId,string status)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("update t_clientrequest set crIsTimelinelocked=@timeline where crId="+crId+"",con);
			SqlParameter _timeline=new SqlParameter("@timeline",SqlDbType.Int);
			_timeline.Value=status;
			cmd.Parameters.Add(_timeline);
			cmd.ExecuteNonQuery();
			con.Close();
		}
		public void lblChangeTimeline_Click(object sender, System.EventArgs e)
		{
			System.Threading.Thread.Sleep(1000);
			pnlChangeTimeline.Visible=!pnlChangeTimeline.Visible;
			pnlChangeTimeline.UpdateAfterCallBack=true;
		}

		public void btnAddDays_Click(object sender, System.EventArgs e)
		{
			System.Threading.Thread.Sleep(1000);
			double Val=double.Parse(lblRemain.Text);
			Val+=double.Parse(ddAdditionalDays.SelectedItem.Value);
			lblRemain.Text=Val.ToString();
			lblRemain.UpdateAfterCallBack=true;
			AddTimelineComm(Request.QueryString[0].ToString(),txtReasonforDays.Text,"10",Session["user_id"].ToString());
            UpdateReqTimeline(Request.QueryString[0].ToString()); 
            UpdateBlog(); 
		}

		public void lnkClientAgent_Click(object sender, System.EventArgs e)
		{
			System.Threading.Thread.Sleep(1000);
			this.chkClientAgents.Items.Clear();
			GetAgentType(2,3,2);
			pnlClientAgents.Visible=!pnlClientAgents.Visible;
			pnlClientAgents.UpdateAfterCallBack=true;
			if(pnlClientAgents.Visible)
			{
				this.btnChangeClient.Visible=true;
				btnChangeClient.UpdateAfterCallBack=true;
			}
			else
			{
				this.btnChangeClient.Visible=false;
				btnChangeClient.UpdateAfterCallBack=true;
			}
			SetDefaultClientAgents();
		}
		public void GetAgentType(int type,int moduleType,int AddVal)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			string Query="SELECT dbo.t_Specialist.SpecialistNo + '   ' + dbo.GetEmpBySpNo(dbo.t_Specialist.SpecialistNo) AS [Specialist Agent], "+
				"dbo.t_Specialist.SpecialistNo "+
				"FROM dbo.t_Specialist INNER JOIN "+
				"dbo.t_SpecialistType ON dbo.t_Specialist.SpType = dbo.t_SpecialistType.tId "+
				"WHERE (dbo.t_Specialist.isactive = 1) AND (dbo.t_SpecialistType.tActive = 1) AND (dbo.t_Specialist.SpType = "+type+") AND (dbo.t_Specialist.SpecialistNo IN "+
				"(SELECT SpecialistNo "+
				"FROM dbo.t_specialistdept AS d "+
				"WHERE(SpecialistNo = SpecialistNo) AND (SpModuleId ="+moduleType+")))ORDER BY dbo.t_Specialist.SpecialistID";
			SqlCommand cmd=new SqlCommand(Query,con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem(rd[0].ToString(),rd[1].ToString());
				if(AddVal==1)
				{
					chkServiceAgents.Items.Add(itm);
				}
				if(AddVal==2)
				{
					chkClientAgents.Items.Add(itm);
					
				}
			}
			rd.Close();
			con.Close();
			con.Dispose();
		}

		public void lnkServiceAgent_Click(object sender, System.EventArgs e)
		{
			System.Threading.Thread.Sleep(1000);
			this.chkServiceAgents.Items.Clear();
			GetAgentType(1,3,1);
			pnlServiceAgent.Visible=!pnlServiceAgent.Visible;
			pnlServiceAgent.UpdateAfterCallBack=true;
			if(pnlServiceAgent.Visible)
			{
				btnChangeService.Visible=true;
				btnChangeService.UpdateAfterCallBack=true;
			}
			else
			{
				btnChangeService.Visible=false;
				btnChangeService.UpdateAfterCallBack=true;
			}
			SetDefaultServiceAgents();
		}

		public void lnkClientToggle_Click(object sender, System.EventArgs e)
		{
			System.Threading.Thread.Sleep(1000);
			pnlToggleClient.Visible=!this.pnlToggleClient.Visible;
			pnlToggleClient.UpdateAfterCallBack=true;
			if(!pnlClientAgents.Visible)
			{
				pnlClientAgents.Visible=false;
				pnlClientAgents.UpdateAfterCallBack=true;
			}
			this.btnChangeClient.Visible=false;
			btnChangeClient.UpdateAfterCallBack=true;
		}

		public void lnkServiceToggle_Click(object sender, System.EventArgs e)
		{
			System.Threading.Thread.Sleep(1000);
			pnlToggleService.Visible=!this.pnlToggleService.Visible;
			pnlToggleService.UpdateAfterCallBack=true;
			if(!pnlServiceAgent.Visible)
			{
				pnlServiceAgent.Visible=false;
				pnlServiceAgent.UpdateAfterCallBack=true;
			}
			btnChangeService.Visible=false;
			btnChangeService.UpdateAfterCallBack=true;
		}
		public void SetDefaultClientAgents()
		{
			for(int i=0;i<this.chkClientAgents.Items.Count;i++)
			{
				this.chkClientAgents.Items[i].Selected=false;
			}
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			string Query="SELECT RTRIM(dbo.t_Employee.pcode) AS pcode, dbo.t_Employee.name,dbo.t_SpecialistOwner.SpecialistNo "+
				"FROM dbo.t_Employee INNER JOIN "+
				"dbo.t_DesignationHistory ON dbo.t_Employee.pcode = dbo.t_DesignationHistory.pcode INNER JOIN "+
				"dbo.t_ClientRequest INNER JOIN "+
				"dbo.t_ClientRequestSpec ON dbo.t_ClientRequest.crId = dbo.t_ClientRequestSpec.crId INNER JOIN "+
				"dbo.t_SpecialistOwner ON dbo.t_ClientRequestSpec.crSpNo = dbo.t_SpecialistOwner.SpecialistNo ON "+ 
				"dbo.t_DesignationHistory.fdesigid = dbo.t_SpecialistOwner.s_fdesigid "+
				"WHERE (dbo.t_ClientRequest.crId = "+Request.QueryString[0].ToString()+") AND (dbo.t_ClientRequestSpec.crSpType = 2) AND (dbo.t_DesignationHistory.isactive = 1) AND (dbo.t_Employee.del = 1)";
			SqlCommand cmd=new SqlCommand(Query,con);
			SqlDataReader rd=cmd.ExecuteReader();
			ArrayList list=new ArrayList();	
			while(rd.Read())
			{
				list.Add(rd[2].ToString());
			}
			rd.Close();
			con.Close();
			con.Dispose();
			for(int i=0;i<list.Count;i++)
			{
				string spVal=(string)list[i];
				ListItem itm=chkClientAgents.Items.FindByValue(spVal);
				if(itm !=null)
				{
					int indx=chkClientAgents.Items.IndexOf(itm);
					this.chkClientAgents.Items[indx].Selected=true;
				}
			}
		}

		public void SetDefaultServiceAgents()
		{
			for(int i=0;i<this.chkServiceAgents.Items.Count;i++)
			{
				this.chkServiceAgents.Items[i].Selected=false;
			}
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			string Query="SELECT RTRIM(dbo.t_Employee.pcode) AS pcode, dbo.t_Employee.name,dbo.t_SpecialistOwner.SpecialistNo "+
				"FROM dbo.t_Employee INNER JOIN "+
				"dbo.t_DesignationHistory ON dbo.t_Employee.pcode = dbo.t_DesignationHistory.pcode INNER JOIN "+
				"dbo.t_ClientRequest INNER JOIN "+
				"dbo.t_ClientRequestSpec ON dbo.t_ClientRequest.crId = dbo.t_ClientRequestSpec.crId INNER JOIN "+
				"dbo.t_SpecialistOwner ON dbo.t_ClientRequestSpec.crSpNo = dbo.t_SpecialistOwner.SpecialistNo ON "+ 
				"dbo.t_DesignationHistory.fdesigid = dbo.t_SpecialistOwner.s_fdesigid "+
				"WHERE (dbo.t_ClientRequest.crId = "+Request.QueryString[0].ToString()+") AND (dbo.t_ClientRequestSpec.crSpType = 1) AND (dbo.t_DesignationHistory.isactive = 1) AND (dbo.t_Employee.del = 1)";
			SqlCommand cmd=new SqlCommand(Query,con);
			SqlDataReader rd=cmd.ExecuteReader();
			ArrayList list=new ArrayList();	
			while(rd.Read())
			{
				list.Add(rd[2].ToString());
			}
			rd.Close();
			con.Close();
			con.Dispose();
			for(int i=0;i<list.Count;i++)
			{
				string spVal=(string)list[i];
				ListItem itm=chkServiceAgents.Items.FindByValue(spVal);
				if(itm !=null)
				{
					int indx=chkServiceAgents.Items.IndexOf(itm);
					this.chkServiceAgents.Items[indx].Selected=true;
				}
			}
		}

		public void btnChangeClient_Click(object sender, System.EventArgs e)
		{
		   for(int i=0;i<this.chkClientAgents.Items.Count;i++)
			{
			   if(this.chkClientAgents.Items[i].Selected)
			   {
				   if(!IsExist(Request.QueryString[0].ToString(),chkClientAgents.Items[i].Value,"2"))
				   {
					   string RequestNo=GetRequestNo(Request.QueryString[0].ToString());
					   InsertRequestSubDetail(Request.QueryString[0].ToString(),RequestNo,chkClientAgents.Items[i].Value,"2");
				   }
			   }
			   else
			   {
			    DeleteSubDetail(Request.QueryString[0].ToString(),chkClientAgents.Items[i].Value,"2");
			   }
			}
           ClientAgent();
		}
		private void btnChangeService_Click(object sender, System.EventArgs e)
		{
			for(int i=0;i<this.chkServiceAgents.Items.Count;i++)
			{
				if(this.chkServiceAgents.Items[i].Selected)
				{
					if(!IsExist(Request.QueryString[0].ToString(),chkServiceAgents.Items[i].Value,"1"))
					{
						string RequestNo=GetRequestNo(Request.QueryString[0].ToString());
						InsertRequestSubDetail(Request.QueryString[0].ToString(),RequestNo,chkServiceAgents.Items[i].Value,"1");
					}
					
				}
				else
				{
				  DeleteSubDetail(Request.QueryString[0].ToString(),chkServiceAgents.Items[i].Value,"1");
				}
			}
			ServiceAgent();
		}
		public void InsertRequestSubDetail(string crId,string reqNo,string crSpNo,string crSpType)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("insert into t_clientrequestspec (crId,RequestNo,crSpNo,crSpType) values (@crId,@RequestNo,@crSpNo,@crSpType)",con);
			SqlParameter _crId=new SqlParameter("@crId",SqlDbType.Int);
			SqlParameter _reqNo=new SqlParameter("@RequestNo",SqlDbType.Int);
			SqlParameter _crSpNo=new SqlParameter("@crSpNo",SqlDbType.VarChar);
			SqlParameter _crSpType=new SqlParameter("@crSpType",SqlDbType.Int);
			_crId.Value=crId;
			_reqNo.Value=reqNo;
			_crSpNo.Value=crSpNo;
			_crSpType.Value=crSpType;
			cmd.Parameters.Add(_crId);
			cmd.Parameters.Add(_reqNo);
			cmd.Parameters.Add(_crSpNo);
			cmd.Parameters.Add(_crSpType);
			cmd.ExecuteNonQuery();
			con.Close();
		}
		public void DeleteSubDetail(string crId,string crSpNo,string crSpType)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
		    SqlCommand cmd=new SqlCommand("delete from t_clientrequestspec where crid="+crId+" and crSpNo='"+crSpNo+"' and crSpType="+crSpType+"",con);
			cmd.ExecuteNonQuery();
		    con.Close();
		}
		public string GetRequestNo(string crId)
		{
		 con=new SqlConnection(Connection.ConnectionString);
		 con.Open();
		 SqlCommand cmd=new SqlCommand("select requestno from t_clientrequest where crId="+crId+"",con);
		 SqlDataReader rd=cmd.ExecuteReader();
		 rd.Read();
		 string Val=rd[0].ToString();
		 rd.Close();
		 con.Close();
		 return Val;
		}
		public bool IsExist(string crId,string SpNo,string SpType)
		{
		 con=new SqlConnection(Connection.ConnectionString);
		 con.Open();
		 SqlCommand cmd=new SqlCommand("select * from t_clientrequestspec where crId="+crId+" and crSpNo='"+SpNo+"' and crSpType="+SpType+"",con);
		 SqlDataReader rd=cmd.ExecuteReader();
		 rd.Read();
		 bool flag=false;
			if(rd.HasRows)
			{
				flag=true;
			}
			else
			{
			 flag=false;
			}
		 rd.Close();
		 con.Close();
		 return flag;
		}
		public void GetActionCode()
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select acid,action from t_actioncodes where (agenttype=2 or agenttype=3) and acid!=4  order by action",con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
		     ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
 		     this.ddActionCode.Items.Add(itm);
			}
			ddActionCode.UpdateAfterCallBack=true;
			rd.Close();
			con.Close();
		}

		public void btnReply_Click(object sender, System.EventArgs e)
		{
			string code="";
			string filecode="";
			string filename="";
			if(this.ddActionCode.SelectedValue!="0")
		    {
			 code=this.ddActionCode.SelectedValue;
			 updateRequestStatus(ddActionCode.SelectedValue);
			}
			AddCommunication(Request.QueryString[0].ToString(),txtReply.Text,code,Session["user_id"].ToString(),filecode,filename);
			this.txtReply.Text="";
			this.ddActionCode.SelectedIndex=-1;
			this.pnlActionCode.Visible=false;
			this.pnlActionCode.UpdateAfterCallBack=true;
			UpdateBlog();
		}
		public void updateRequestStatus(string status)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("update t_clientrequest set crCstatus=@status where crId="+Request.QueryString[0].ToString()+"",con);
			SqlParameter _status=new SqlParameter("@status",SqlDbType.Int);
			_status.Value=status;
			cmd.Parameters.Add(_status);
			cmd.ExecuteNonQuery();
			con.Close();
		}
		private void ddActionCode_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			if(this.ddActionCode.SelectedValue=="5")
			{
				this.pnlActionCode.Visible=true;
				this.pnlActionCode.UpdateAfterCallBack=true;
			}
			else
			{
				this.pnlActionCode.Visible=false;
				this.pnlActionCode.UpdateAfterCallBack=true;
			}
		}
		public void AddCommunication(string crId,string comm,string code,string postby,string filecode,string filename)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("insert into t_clientcommunication(crId,commdate,Communication,actioncode,postby,filecode,filename) values (@crId,getdate(),@Communication,@actioncode,@postby,@filecode,@filename)",con);
			SqlParameter _crId=new SqlParameter("@crId",SqlDbType.Int);
			SqlParameter _com=new SqlParameter("@Communication",SqlDbType.VarChar);
			SqlParameter _code=new SqlParameter("@actioncode",SqlDbType.Int);
			SqlParameter _pcode=new SqlParameter("@postby",SqlDbType.VarChar);
			SqlParameter _fcode=new SqlParameter("@filecode",SqlDbType.VarChar);
			SqlParameter _fname=new SqlParameter("@filename",SqlDbType.VarChar);
			_crId.Value=crId;
			_com.Value=comm;
			if(code.Length>0)
			{
				_code.Value=code;
			}
			else
			{
			 _code.Value=DBNull.Value;
			}
			_pcode.Value=postby;
			if(filecode.Length>0)
			{
			 _fcode.Value=filecode;
			}
			else
			{
			 _fcode.Value=DBNull.Value;
			}
			if(filename.Length>0)
			{
			 _fname.Value=filename;
			}
			else
			{
			_fname.Value=DBNull.Value;
			}
			cmd.Parameters.Add(_crId);
			cmd.Parameters.Add(_com);
			cmd.Parameters.Add(_code);
			cmd.Parameters.Add(_pcode);
		    cmd.Parameters.Add(_fcode);
			cmd.Parameters.Add(_fname);
			cmd.ExecuteNonQuery();
			con.Close();
		}
	}
}
