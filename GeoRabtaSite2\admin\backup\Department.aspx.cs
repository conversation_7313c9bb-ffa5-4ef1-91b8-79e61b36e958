using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;
namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for GeoIdeas.
	/// </summary>
	public class GeoDepartment : System.Web.UI.Page
	{
	    
		private SqlConnection con;
		private static DataSet ds=new DataSet();
		protected System.Web.UI.WebControls.DataGrid DataGrid1;
		protected System.Web.UI.WebControls.Button Button1;
		protected System.Web.UI.WebControls.Button Button2;
		protected eWorld.UI.CalendarPopup CalendarPopup1;
		protected System.Web.UI.WebControls.CheckBox CheckBox2;
		protected eWorld.UI.CalendarPopup CalendarPopup2;
		protected System.Web.UI.WebControls.DropDownList DropDownList1;
		
		protected System.Web.UI.WebControls.Button Button3;
		protected System.Web.UI.WebControls.Label Label6;
		protected System.Web.UI.WebControls.DataGrid DataGrid3;
		protected System.Web.UI.HtmlControls.HtmlTable Table3;
		protected System.Web.UI.WebControls.Button cmdCancel2;
		protected System.Web.UI.WebControls.Button cmdSave2;
		protected System.Web.UI.WebControls.CheckBox CheckBox3;
		protected System.Web.UI.WebControls.Panel Panel4;
		protected System.Web.UI.WebControls.Button Button6;
		protected System.Web.UI.WebControls.Label Label7;
		protected System.Web.UI.WebControls.DataGrid DataGrid4;
		protected System.Web.UI.HtmlControls.HtmlTable Table6;
		
		public static string sort;

		int WebFormID=2;
		int ProjectID=2;
		protected System.Web.UI.WebControls.Label lblError;
		protected System.Web.UI.WebControls.LinkButton LinkButton1;
		protected System.Web.UI.WebControls.Label Label2;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator1;
		protected System.Web.UI.WebControls.Label txtDeptId;
		protected System.Web.UI.WebControls.Label Label4;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator2;
		protected System.Web.UI.WebControls.TextBox txtName;
		protected System.Web.UI.WebControls.DropDownList txtBusinessUnit;
		protected System.Web.UI.WebControls.Button btnQueryButton;
		protected System.Web.UI.WebControls.Button btnCancel;
		protected System.Web.UI.WebControls.Button cmdCancel;
		protected System.Web.UI.WebControls.Button cmdSave;
		protected eWorld.UI.CalendarPopup dpTo;
		protected System.Web.UI.WebControls.CheckBox CheckBox1;
		protected eWorld.UI.CalendarPopup dpFrom;
		protected System.Web.UI.WebControls.Label lblbumangerid;
		protected System.Web.UI.WebControls.DropDownList ddlBUManager;
		protected System.Web.UI.WebControls.Panel pnlBUManagerForm;
		protected System.Web.UI.WebControls.Button btnChange;
		protected System.Web.UI.WebControls.Label Label5;
		protected System.Web.UI.WebControls.DataGrid DataGrid2;
		protected System.Web.UI.WebControls.Panel pnlBUManager;
		protected System.Web.UI.HtmlControls.HtmlTable Table4;
		protected System.Web.UI.WebControls.Button Button4;
		protected System.Web.UI.WebControls.Button Button5;
		protected eWorld.UI.CalendarPopup dpTo2;
		protected System.Web.UI.WebControls.CheckBox cbTo2;
		protected eWorld.UI.CalendarPopup dpFrom2;
		protected System.Web.UI.WebControls.Label lblHODID;
		protected System.Web.UI.WebControls.DropDownList ddlHOD2;
		protected System.Web.UI.WebControls.Panel pnlHODForm;
		protected System.Web.UI.WebControls.Button cmdChangeHOD;
		protected System.Web.UI.WebControls.Label Label8;
		protected System.Web.UI.WebControls.DataGrid DataGrid5;
		protected System.Web.UI.WebControls.Panel pnlHOD;
		protected System.Web.UI.HtmlControls.HtmlTable Table5;
		protected System.Web.UI.HtmlControls.HtmlTableCell tdError;
		protected System.Web.UI.HtmlControls.HtmlTable tabNewForm;
		protected System.Web.UI.WebControls.Button cmdDelete;
		string userId="";

		private bool IsPageAccessAllowed()
		{

			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception ex)
			{
				string s=ex.Message;
				Response.Redirect("../Login.aspx");
			}
		
			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"View")==true)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("../Login.aspx");
				return false;
			}
		}

		private void GetSupervisoryRoleEmp()
		{
			ddlBUManager.Items.Clear();
			ddlHOD2.Items.Clear();
			string hdept="select e.pcode,e.name from t_employee e where e.desigid=Any(select d.desigid from t_designation d,t_categorization c where c.isactive=1 And d.status=1 And c.cat_id=category And c.clevel<=7And c.supervisory_role=1 And e.del=1)";
			SqlCommand cmd=new SqlCommand(hdept,con);
			SqlDataReader rd=cmd.ExecuteReader();
			ListItem i=new ListItem();
			i.Value="0";
			i.Text="Select";
			ddlBUManager.Items.Insert(0,i);
			ddlHOD2.Items.Insert(0,i);
			while(rd.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rd[0].ToString();
				itm.Text=rd[1].ToString();
				ddlBUManager.Items.Add(itm); 
				ddlHOD2.Items.Add(itm);
			}
			rd.Close();
		}

		

		private void Page_Load(object sender, System.EventArgs e)
		{
		
			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			tdError.Visible=false;
			if(IsPageAccessAllowed())
			{
				tdError.Visible=false;
				con=new SqlConnection(Connection.ConnectionString);
				con.Open();	
				//Page.SmartNavigation=true;

				if(!IsPostBack)
				{
					
					cmdSave.Attributes.Add("onclick","return confirm('Are you sure to change Current BU Manager?');");
					BindGrid();
					getSbu();
					getDept();
					pnlBUManager.Visible=false;
					pnlHOD.Visible=false;
					//Table6.Visible=false;
					GetSupervisoryRoleEmp();
					
					//DataGrid1.Columns[1].Visible=GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Delete");
					
					if (GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Add"))
						LinkButton1.Visible=true;
					else
						LinkButton1.Visible=false;

					tabNewForm.Visible=false;
				}
			}
			else
			{
				Response.Redirect("ErrorPage.aspx");
			}
			

			
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.btnQueryButton.Click += new System.EventHandler(this.btnQueryButton_Click);
			this.cmdDelete.Click += new System.EventHandler(this.cmdDelete_Click);
			this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
			this.LinkButton1.Click += new System.EventHandler(this.LinkButton1_Click);
			this.DataGrid1.EditCommand += new System.Web.UI.WebControls.DataGridCommandEventHandler(this.DataGrid1_EditCommand);
			this.DataGrid1.SortCommand += new System.Web.UI.WebControls.DataGridSortCommandEventHandler(this.DataGrid1_SortCommand);
			this.DataGrid1.DeleteCommand += new System.Web.UI.WebControls.DataGridCommandEventHandler(this.DataGrid1_DeleteCommand);
			this.btnChange.Click += new System.EventHandler(this.btnChange_Click);
			this.ddlBUManager.SelectedIndexChanged += new System.EventHandler(this.ddlBUManager_SelectedIndexChanged);
			this.CheckBox1.CheckedChanged += new System.EventHandler(this.CheckBox1_CheckedChanged);
			this.cmdSave.Click += new System.EventHandler(this.cmdSave_Click);
			this.cmdCancel.Click += new System.EventHandler(this.cmdCancel_Click);
			this.cmdChangeHOD.Click += new System.EventHandler(this.cmdChangeHOD_Click);
			this.cbTo2.CheckedChanged += new System.EventHandler(this.cbTo2_CheckedChanged);
			this.Button5.Click += new System.EventHandler(this.Button5_Click);
			this.Button4.Click += new System.EventHandler(this.Button4_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		private void btnQueryButton_Click(object sender, System.EventArgs e)
		{
//			tdError.Visible=false;
//			if (btnQueryButton.Text=="New Department")
//			{
//				if (GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Add"))
//				{
//					Session["IsTrue"]="false";
//					btnQueryButton.Text="Save";
//					btnCancel.Enabled=true;
//					txtDeptId.Text="";
//					ddDivisionOf.SelectedValue="0";
//					//txtBusinessUnit.Text="";
//					txtBusinessUnit.SelectedIndex=0;
//					txtName.Text="";
//					btnQueryButton.CausesValidation=true;
//				}
//				else
//				{
//					tdError.Visible=true;
//					lblError.Text="You do not have right to add new department";
//				}
//
//			}
//			else 
			if (btnQueryButton.Text=="Save")
			{
				if (GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Add"))
				{
					string str="insert into t_department values('"+txtName.Text+"','"+Convert.ToInt32(txtBusinessUnit.SelectedValue)+"','0','1')"; 
					SqlCommand c=new SqlCommand(str,con);
					int i=c.ExecuteNonQuery();
					BindGrid();
				
				
					btnQueryButton.Text="New Department";
					btnQueryButton.CausesValidation=false;
				}
				else
				{
					lblError.Text="You do not have right to add new department";
					tdError.Visible=true;
				}
			}
			else if (btnQueryButton.Text=="Update")
			{
				if (GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Edit"))
				{
					string str="update t_department set deptname='"+txtName.Text+"', sbu='"+Convert.ToInt32(txtBusinessUnit.SelectedValue)+"',subdept='0',status='1' where deptid='"+Int32.Parse(txtDeptId.Text)+"'"; 
					SqlCommand c=new SqlCommand(str,con);
					int i=c.ExecuteNonQuery();
					BindGrid();
					btnQueryButton.Text="New Department";
					btnCancel.Enabled=false;
				
					//Label1.Visible=false;
					txtDeptId.Text="";
					//ddDivisionOf.SelectedValue="0";
					txtName.Text="";
					btnQueryButton.CausesValidation=false;
				}
				else
				{
					tdError.Visible=false;
					lblError.Text="You do not have right to update department information";
				}
			}

			
		}

		private void btnCancel_Click(object sender, System.EventArgs e)
		{
			LinkButton1.Visible=true;
			
			//Label1.Visible=false;
			txtDeptId.Text="";
			txtBusinessUnit.SelectedIndex=0;
			//ddDivisionOf.SelectedValue="0";
			txtName.Text="";
			DataGrid1.Visible=true;
			pnlBUManager.Visible=false;
			pnlHOD.Visible=false;
			btnQueryButton.CausesValidation=false;
			btnQueryButton.Enabled=true;
			tabNewForm.Visible=false;
			pnlBUManager.Visible=false;
			pnlHOD.Visible=false;
			btnCancel.Enabled=true;
		}

		public void BindGrid()
		{
          
			//string str="select d.deptid as DeptId,d.deptname as DeptName,s.sbuname as SystemBusinessUnit,dept.deptname as DivisionOf from t_department d,t_department dept,t_sbu s where d.status ='"+1+"' And d.subdept=dept.deptid And s.sbuid=d.sbu";
			//string str="select d.deptid DeptId,d.deptname DeptName,s.sbuname SBU,Division=case d.subdept when d.subdept then (select dept.deptname from t_department dept where dept.deptid=d.subdept And dept.status='1') end  from t_department d,t_sbu s where d.status ='1'And s.sbuid=d.sbu";
			string str="select d.deptid DeptId,d.deptname DeptName,s.sbuname SBU,Division=case d.subdept when d.subdept then (select dept.deptname from t_department dept where dept.deptid=d.subdept And dept.status='1') end  from t_department d,t_sbu s where d.status ='1'And s.sbuid=d.sbu ";
			SqlDataAdapter ord=new SqlDataAdapter(str,con);
			ds=new DataSet();
			ord.Fill(ds,"Department");
			DataView dv=new DataView(ds.Tables["Department"]);
			dv.Sort=sort;
			DataGrid1.DataSource=dv;
			DataGrid1.DataBind();

			//			foreach( DataGridItem di in DataGrid1.Items )			
			//			{
			//				if(di.Cells[3].Text=="No")
			//				{
			//					di.Cells[4].Text="";
			//				}
			//			}
		}

		private void getSbu()
		{
			string query="select sbuid,sbuname from t_sbu where isactive='"+1+"'";
			SqlCommand cmd=new SqlCommand(query,con);
			SqlDataReader rd=cmd.ExecuteReader();
			txtBusinessUnit.Items.Add("");
			while(rd.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rd["sbuid"].ToString();
				itm.Text=rd["sbuname"].ToString();
				txtBusinessUnit.Items.Add(itm); 
			}
			rd.Close();
		}

		public void getDept()
		{
			string query="select deptid,deptname from t_department where status='"+1+"'";
			SqlCommand cmd=new SqlCommand(query,con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rd[0].ToString();
				itm.Text=rd[1].ToString();
//				ddDivisionOf.Items.Add(itm); 
			}
			rd.Close();
         
		}

		private void DataGrid1_EditCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{
			tabNewForm.Visible=true;
			LinkButton1.Visible=false;
			btnQueryButton.Text="Update";
			btnCancel.Enabled=true;
			
			txtDeptId.Enabled=false;
			//Label1.Visible=true;
			Session["IsTrue"]="true";
 
			string str="select deptid as DeptId,deptname as DeptName,sbu as SBU,subdept as SubDeptId from t_department where deptid=@Id";
			SqlCommand cmd=new SqlCommand(str,con);
			cmd.Parameters.Add(new SqlParameter("@Id",SqlDbType.VarChar,20));
			cmd.Parameters["@Id"].Value=DataGrid1.DataKeys[e.Item.ItemIndex];
			SqlDataReader rd=cmd.ExecuteReader();
			string deptId="";
			string name="";
			string masterid="";
			string des="";
			
			while(rd.Read())
			{
				deptId=rd[0].ToString(); 
				name=rd[1].ToString();
				masterid=rd[2].ToString();
				des=rd[3].ToString();
				
				//Table6.Visible=true;
				DataGrid1.Visible=false;
				
				pnlBUManager.Visible=true;
				pnlBUManagerForm.Visible=false;
				btnChange.Enabled=true;

				pnlHOD.Visible=true;
				pnlHODForm.Visible=false;
				cmdChangeHOD.Enabled=true;


				//Panel4.Visible=false;
		
			}

			txtDeptId.Text=deptId;
			rd.Close();
			UpdateGrid();
			UpdateGridHOD();
			txtName.Text=name;
			if(des.Length>0)
			{
			//	ddDivisionOf.SelectedValue=des;
			}
			txtBusinessUnit.SelectedValue=masterid;

			if (GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Edit"))
			{
				btnQueryButton.Enabled=true;
			}
			else
			{
				btnQueryButton.Enabled=false;
			}

			if (GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"HOD"))
			{
				DataGrid5.Columns[4].Visible=true;
				cmdChangeHOD.Visible=true;
			}
			else
			{
				DataGrid5.Columns[4].Visible=false;
				cmdChangeHOD.Visible=false;
			}

			if (GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"BUMang"))
			{
				DataGrid2.Columns[4].Visible=true;
				btnChange.Visible=true;
			}
			else
			{
				DataGrid2.Columns[4].Visible=false;
				btnChange.Visible=false;
			}
			
			cmdDelete.Visible=GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Delete");
		}

		private void DataGrid1_DeleteCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{
			
			string str="update t_department set status='"+0+"' where deptid=@Id";
			SqlCommand cm=new SqlCommand(str,con);
			cm.Parameters.Add(new SqlParameter("@Id",SqlDbType.VarChar,20));
			cm.Parameters["@Id"].Value=DataGrid1.DataKeys[e.Item.ItemIndex]; 
			cm.ExecuteNonQuery();
			DataGrid1.EditItemIndex=-1;
			BindGrid();
			
			
		}

		private void DataGrid1_PageIndexChanged(object source, System.Web.UI.WebControls.DataGridPageChangedEventArgs e)
		{
			DataGrid1.CurrentPageIndex = e.NewPageIndex ;
			DataGrid1.DataSource = ds.Tables[0].DefaultView ;
			DataGrid1.DataBind();
		}

		private void DataGrid1_SortCommand(object source, System.Web.UI.WebControls.DataGridSortCommandEventArgs e)
		{
			sort=e.SortExpression.ToString();
			BindGrid();
		}

		private void btnChange_Click(object sender, System.EventArgs e)
		{
			btnChange.Enabled=false;
			ddlBUManager.SelectedIndex=0;
			CheckBox1.Checked=false;
			dpTo.Visible=false;
			pnlBUManager.Visible=true;
			pnlBUManagerForm.Visible=true;
			UpdateGrid();
		}

		private void cmdCancel_Click(object sender, System.EventArgs e)
		{
			pnlBUManagerForm.Visible=false;
			btnChange.Enabled=true;
			cmdSave.Text="Save";
			pnlBUManager.Visible=true;
			pnlBUManagerForm.Visible=false;
			

		}

		private void CheckBox1_CheckedChanged(object sender, System.EventArgs e)
		{
			dpTo.Visible=CheckBox1.Checked;
		}

		private void UpdateGrid()
		{
			string deptid=txtDeptId.Text; //ddlDepartments.SelectedValue.ToString();
			string sql="SELECT dbo.t_BUManagers.bumangerid, dbo.t_BUManagers.bumangerid, dbo.t_Employee.name, dbo.t_BUManagers.dateFrom, dbo.t_BUManagers.dateTo, " +
				" CASE WHEN dbo.t_BUManagers.isActive = 0 THEN 'No' ELSE 'Yes' END AS isActive " +
				" FROM dbo.t_BUManagers INNER JOIN " +
				" dbo.t_Employee ON dbo.t_BUManagers.PCode = dbo.t_Employee.pcode " +
				" WHERE (dbo.t_BUManagers.deptid = " + deptid + ")" +
				"ORDER BY isactive DESC, datefrom DESC";
			SqlCommand cmd = new SqlCommand(sql,con);
			SqlDataReader dr=cmd.ExecuteReader();
			DataGrid2.DataSource=dr;
			DataGrid2.DataKeyField="bumangerid";
			DataGrid2.DataBind();
			dr.Close();

			foreach( DataGridItem di in DataGrid2.Items )			
			{
				if(di.Cells[3].Text=="No")
				{
					di.Cells[4].Text="";
				}
			}
		}


		private void UpdateHODEditColoum()
		{
			foreach( DataGridItem di in DataGrid5.Items )			
			{
				if(di.Cells[3].Text=="No")
				{
					di.Cells[4].Text="";
				}
			}
		}
		private void UpdateGridHOD()
		{
			string deptid=txtDeptId.Text; //ddlDepartments.SelectedValue.ToString();
			string sql="SELECT dbo.t_headofdept.h_id, dbo.t_Employee.name, dbo.t_headofdept.hfrom as DateFrom, dbo.t_headofdept.hto as DateTo, " +
				" CASE WHEN dbo.t_headofdept.isActive = 0 THEN 'No' ELSE 'Yes' END AS isActive " +
				" FROM dbo.t_headofdept INNER JOIN " +
				" dbo.t_Employee ON dbo.t_headofdept.PCode = dbo.t_Employee.pcode " +
				" WHERE (dbo.t_headofdept.deptid = " + deptid + ")" + 
				"ORDER BY isactive DESC, hfrom DESC";
			SqlCommand cmd = new SqlCommand(sql,con);
			SqlDataReader dr=cmd.ExecuteReader();
			DataGrid5.DataSource=dr;
			DataGrid5.DataKeyField="h_id";
			DataGrid5.DataBind();
			dr.Close();

			UpdateHODEditColoum();


		}
		private void cmdSave_Click(object sender, System.EventArgs e)
		{
			pnlBUManager.Visible=true;
			pnlBUManagerForm.Visible=true;
			pnlHOD.Visible=true;
			DataGrid1.Visible=false;

			if (cmdSave.Text=="Save")
			{
				if (ddlBUManager.SelectedIndex>0)
				{
					string deptid=txtDeptId.Text; //ddlDepartments.SelectedValue.ToString();
					string pcode=ddlBUManager.SelectedValue.ToString();
					SqlCommand cmd=new SqlCommand("update t_BUManagers set isActive=0 where deptid="+deptid,con);
					cmd.ExecuteNonQuery();
					cmd.Dispose();
					cmd=new SqlCommand("update t_BUManagers set dateto=getdate() where deptid="+deptid + " and dateto is null",con);
					cmd.ExecuteNonQuery();
					cmd.Dispose();
					cmd=new SqlCommand("Insert into t_BUManagers(pcode, deptid, datefrom, dateto) values(@p_pcode, @p_deptid, @p_datefrom, @p_dateto)",con);
					SqlParameter p_pcode=new SqlParameter("@p_pcode",SqlDbType.Char,10); p_pcode.Value=pcode;
					SqlParameter p_deptid=new SqlParameter("@p_deptid",SqlDbType.Int); p_deptid.Value=deptid;
					SqlParameter p_datefrom=new SqlParameter("@p_datefrom",SqlDbType.SmallDateTime); p_datefrom.Value=dpFrom.SelectedDate.ToShortDateString();
				
					SqlParameter p_dateto=new SqlParameter("@p_dateto",SqlDbType.SmallDateTime); 
					if(CheckBox1.Checked)
					{
						p_dateto.Value=dpTo.SelectedDate.ToShortDateString();
					}
					else
					{
						p_dateto.Value=DBNull.Value;
					}
					cmd.Parameters.Add(p_datefrom);
					cmd.Parameters.Add(p_dateto);
					cmd.Parameters.Add(p_pcode);
					cmd.Parameters.Add(p_deptid);
					cmd.ExecuteNonQuery();
					UpdateGrid();
					pnlBUManagerForm.Visible=false;
					btnChange.Enabled=true;
				}
				else
				{
					Response.Write(ddlBUManager.SelectedValue.ToString());
				}
				
			}
			else if (cmdSave.Text=="Update")
			{
				string id=lblbumangerid.Text;
				string pcode=ddlBUManager.SelectedValue.ToString();
				string dateFrom = dpFrom.SelectedDate.ToShortDateString();
				string dateTo=" NULL ";
				if (CheckBox1.Checked==true)
				{
					dateTo = "'"+dpTo.SelectedDate.ToShortDateString()+"'";
				}

				SqlCommand cmd = new SqlCommand("update t_BUManagers set PCode='" + pcode + "', datefrom='" + dateFrom + "', dateto=" + dateTo + " where bumangerid="+lblbumangerid.Text,con);
				cmd.ExecuteNonQuery();
				cmdSave.Text="Save";
				pnlBUManager.Visible=true;
				pnlBUManagerForm.Visible=false;
				btnChange.Enabled=true;
				UpdateGrid();
			}
		}

		private void Button4_Click(object sender, System.EventArgs e)
		{
			pnlHODForm.Visible=false;
			cmdChangeHOD.Enabled=true;
			Button5.Text="Save";
			ddlHOD2.SelectedIndex=0;
			UpdateGridHOD();
		}

		private void cmdChangeHOD_Click(object sender, System.EventArgs e)
		{
			pnlHODForm.Visible=true;
			cmdChangeHOD.Enabled=false;
			ddlHOD2.SelectedIndex=0;
			dpTo2.Visible=false;
			cbTo2.Checked=false;
			UpdateHODEditColoum();
		}

		private void cbTo2_CheckedChanged(object sender, System.EventArgs e)
		{
			dpTo2.Visible=cbTo2.Checked;
		}

		private void Button5_Click(object sender, System.EventArgs e)
		{
			
			if (Button5.Text=="Save")
			{
				if (txtDeptId.Text!="" && ddlHOD2.SelectedIndex>0) 
				{
					string deptid=txtDeptId.Text; //ddlDepartments.SelectedValue.ToString();
					string pcode=ddlHOD2.SelectedValue.ToString();
					SqlCommand cmd=new SqlCommand("update t_headofdept set isactive=0 where deptid="+deptid,con);
					cmd.ExecuteNonQuery();
					cmd.Dispose();
					cmd=new SqlCommand("update t_headofdept set hto=getdate() where deptid="+deptid + " and hto is null",con);
					cmd.ExecuteNonQuery();
					cmd.Dispose();
					cmd=new SqlCommand("Insert into t_headofdept(pcode, deptid, hfrom, hto) values(@p_pcode, @p_deptid, @p_hfrom, @p_hto)",con);
					SqlParameter p_pcode=new SqlParameter("@p_pcode",SqlDbType.Char,10); p_pcode.Value=pcode;
					SqlParameter p_deptid=new SqlParameter("@p_deptid",SqlDbType.Int); p_deptid.Value=deptid;
					SqlParameter p_hfrom=new SqlParameter("@p_hfrom",SqlDbType.SmallDateTime); p_hfrom.Value=dpFrom2.SelectedDate.ToShortDateString();
				
					SqlParameter p_hto=new SqlParameter("@p_hto",SqlDbType.SmallDateTime); 
					if(cbTo2.Checked)
					{
						p_hto.Value=dpTo2.SelectedDate.ToShortDateString();
					}
					else
					{
						p_hto.Value=DBNull.Value;
					}
					cmd.Parameters.Add(p_hfrom);
					cmd.Parameters.Add(p_hto);
					cmd.Parameters.Add(p_pcode);
					cmd.Parameters.Add(p_deptid);
					cmd.ExecuteNonQuery();
					UpdateGridHOD();
					pnlHODForm.Visible=false;
					cmdChangeHOD.Enabled=true;
				
				}
			}
			else if (Button5.Text=="Update")
			{
				string id=lblHODID.Text;
				string pcode=ddlHOD2.SelectedValue.ToString();
				string dateFrom = dpFrom2.SelectedDate.ToShortDateString();
				string dateTo=" NULL ";
				
				if (cbTo2.Checked==true)
				{
					dateTo = "'"+dpTo2.SelectedDate.ToShortDateString()+"'";
				}

				SqlCommand cmd = new SqlCommand("update t_headofdept set PCode='" + pcode + "', hfrom='" + dateFrom + "', hto=" + dateTo + " where h_id="+lblHODID.Text,con);
				cmd.ExecuteNonQuery();
				Button5.Text="Save";
				pnlHODForm.Visible=false;
				cmdChangeHOD.Enabled=true;
				UpdateGridHOD();
			}
		}

		private void DataGrid2_EditCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{
			string id=DataGrid2.DataKeys[0].ToString();
			//Response.Write(id);
			cmdSave.Text="Update";
			lblbumangerid.Text=id;
			SqlCommand cmd = new SqlCommand("select * from t_BUManagers where bumangerid="+lblbumangerid.Text,con);
			SqlDataReader dr=cmd.ExecuteReader();
			dr.Read();
			if (dr.HasRows)
			{
				btnQueryButton.Text="Update";
				//DataGrid2.Visible=false;
				pnlBUManager.Visible=true;
				pnlBUManagerForm.Visible=true;
				ddlBUManager.SelectedValue=dr["pcode"].ToString();
				dpFrom.SelectedDate=DateTime.Parse(dr["datefrom"].ToString());
				btnChange.Enabled=false;
				if(dr["dateto"].ToString()=="")
				{
					CheckBox1.Checked=false;
					dpTo.Visible=false;
				}
				else
				{
					CheckBox1.Checked=true;
					dpTo.Visible=true;
					dpTo.SelectedDate=DateTime.Parse(dr["dateto"].ToString());

				}
				dr.Close();
			}
			else
			{
				dr.Close();
			}

			foreach( DataGridItem di in DataGrid2.Items )			
			{
				if(di.Cells[3].Text=="No")
				{
					di.Cells[4].Text="";
				}
			}

		}

		private void ddlHOD2_SelectedIndexChanged(object sender, System.EventArgs e)
		{
		
		}

		private void ddlBUManager_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			pnlBUManager.Visible=true;
			pnlHOD.Visible=true;
			pnlBUManagerForm.Visible=true;

			
		}

		private void DataGrid5_EditCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{
			UpdateHODEditColoum();

			string id=DataGrid5.DataKeys[0].ToString();
			lblHODID.Text=id;
			//Response.Write(id);
			Button5.Text="Update";
			SqlCommand cmd = new SqlCommand("select * from t_headofdept where h_id="+lblHODID.Text,con);
			SqlDataReader dr=cmd.ExecuteReader();
			dr.Read();
			if (dr.HasRows)
			{
				//DataGrid2.Visible=false;
				pnlHODForm.Visible=true;
				cmdChangeHOD.Enabled=false;
				ddlHOD2.SelectedValue=dr["pcode"].ToString();
				dpFrom.SelectedDate=DateTime.Parse(dr["hfrom"].ToString());
				cmdChangeHOD.Enabled=false;
				if(dr["hto"].ToString()=="")
				{
					cbTo2.Checked=false;
					dpTo2.Visible=false;
				}
				else
				{
					cbTo2.Checked=true;
					dpTo2.Visible=true;
					dpTo2.SelectedDate=DateTime.Parse(dr["hto"].ToString());
				}
				dr.Close();
			}
			else
			{
				dr.Close();
			}
		}

		private void LinkButton1_Click(object sender, System.EventArgs e)
		{
			tabNewForm.Visible=true;
			txtDeptId.Text="";
			txtName.Text="";
			txtBusinessUnit.SelectedIndex=0;
			LinkButton1.Visible=false;
			pnlBUManager.Visible=false;
			pnlHOD.Visible=false;
			btnQueryButton.Text="Save";
			cmdDelete.Visible=false;

		}



		private void cmdDelete_Click(object sender, System.EventArgs e)
		{
			string sql = "SELECT DISTINCT dbo.t_Department.deptid, dbo.t_Designation.status " + 
				" FROM dbo.t_Department INNER JOIN " +
				" dbo.t_Designation ON dbo.t_Department.deptid = dbo.t_Designation.deptid " +
				" WHERE     (dbo.t_Department.deptid = " + txtDeptId.Text + ") AND (dbo.t_Designation.status = 1) " ;
				
			SqlDataAdapter	 da = new SqlDataAdapter(sql,con);
			DataSet ds = new DataSet();
			da.Fill(ds,"DeptDesg");
			if (ds.Tables[0].Rows.Count>0)
			{
				tdError.Visible=true;
				lblError.Text="You can not delete this department. Designation exist for this department<br>"+
					"First remove the designation then delete department";
			}
			else
			{
			
				string str="update t_department set status=0 where deptid=@Id";
				SqlCommand cm=new SqlCommand(str,con);
				cm.Parameters.Add(new SqlParameter("@Id",SqlDbType.VarChar,20));
				cm.Parameters["@Id"].Value=txtDeptId.Text;
				cm.ExecuteNonQuery();
				DataGrid1.EditItemIndex=-1;
				BindGrid();
			}
		}



	}
}
