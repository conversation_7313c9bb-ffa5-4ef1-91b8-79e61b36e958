<%@ Register TagPrefix="uc1" TagName="myMenus" Src="myMenus.ascx" %>
<%@ Page CodeBehind="NewEmployee.aspx.cs" Language="c#" AutoEventWireup="false" Inherits="GeoRabtaSite.NewEmployeInfo" smartNavigation="False"%>
<%@ Register TagPrefix="ew" Namespace="eWorld.UI" Assembly="eWorld.UI" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Rabata Admin :: User Management :: New Employee</title>
		<META content="text/html; charset=utf-8" http-equiv="Content-Type">
		<meta name="GENERATOR" content="Microsoft Visual Studio .NET 7.1">
		<meta name="vs_targetSchema" content="http://schemas.microsoft.com/intellisense/ie5">
		<LINK rel="stylesheet" type="text/css" href="../Styles5.css">
		<LINK rel="stylesheet" type="text/css" href="RaabtaAdmin.css">
		<script src="../client.js"></script>
		<script language="javascript">
		    var highlightColor='blue';
			var highlighText='white';

			var normalColor='white';
			var normalText='black';
			
			var idx=-1;
			var totalItems=200;

function PopupCenter(url, title, w, h) {
    // Fixes dual-screen position                         Most browsers      Firefox
    var dualScreenLeft = window.screenLeft != undefined ? window.screenLeft : screen.left;
    var dualScreenTop = window.screenTop != undefined ? window.screenTop : screen.top;

    var width = window.innerWidth ? window.innerWidth : document.documentElement.clientWidth ? document.documentElement.clientWidth : screen.width;
    var height = window.innerHeight ? window.innerHeight : document.documentElement.clientHeight ? document.documentElement.clientHeight : screen.height;

    var left = ((width / 2) - (w / 2)) + dualScreenLeft;
    var top = ((height / 2) - (h / 2)) + dualScreenTop;
    var newWindow = window.open(url, title, 'scrollbars=yes, width=' + w + ', height=' + h + ', top=' + top + ', left=' + left);

    // Puts focus on the newWindow
    if (window.focus) {
        newWindow.focus();
    }
}
function validateContract()
{
 var duration=document.getElementById("ddDuration");
 if(duration.value==0)
   {
    alert('Please select duration');
    event.retrurnValue=false;
    return false;
   }
  else
 	{
 	var start = document.getElementById("cpContarctStart");
	var sDate = new Date(start.value);
 	var end = document.getElementById("cpContractExpiry");
    var eDate=new Date(end.value);
    if(eDate.getTime() < sDate.getTime())
    {
    alert('Contract start date must be greater than its Expiry');
    event.retrurnValue=false;
    return false;
    }
    else
    {
	event.returnValue=true;
	return true;
	}
  }
}
function checkEmpType()
{
 var v=document.getElementById("txtEmpInfoTOE");
 if(v.value!=2)
 {
    alert('This option is available for only Contractual employees');
    event.retrurnValue=false;
    return false;
 }
 else
 {
    event.returnValue=true;
	return true;
 }
}
function checkValue()
{
	// add attribute to command button and call this function like this
	// btnSave.attributes.add("onclick","return checkValue();");
	var d = document.getElementById("calendarDOB");
	var dateOfBirth= new Date(d.value);
	//var dateOfBirth = new Date("25/2/1955");
	var sDate=document.getElementById("txtServerDate");
	var serverDate = new Date(sDate.value);
	var dif=new Date(serverDate-dateOfBirth);
	var age = dif.getFullYear()-1970;
	var FileToUpload=document.getElementById("FileToUpload");
	//alert(age);
	//window.document.title="Age is " + age.toSring();
	var c;

     if (FileToUpload.value=="")
	{
		alert('Please attach employee photograph');
		//FileToUpload.focus();
		event.returnValue=false;
		return false;
	}
	else if (age<18)
	{
		//return confirm("Employee is under 18. Are you sure to continue with this age?");
		if(confirm("Employee is under 18. Are you sure to continue with this age?"))
		{
			event.returnValue=true;
			return true;
		}
		else
		{
			event.returnValue=false;
			return false;
		}
	}	
	else if(age >60)
	{
		var status=document.getElementById("txtHidden");
		if(status.value==1)
		{
			alert("Employee Age is above 60 therefore you cannot allocate him/her Permanent Status. Please re-assign employment type other than Permanent");
			event.returnValue=false;
			return false;
		 }
	}	
	//else
		{
		event.returnValue=true;
		return true ;
		}
	}
	
	function checkValue2()
	{
	  if(checkValue())
	  {
	  return confirm('Are You Sure To Refer This Record?');
	  }
	  else
	  {
	   return false;
	  }
	}
	function CheckOfficalEmail()
	{
		txtEmailOffical2 = window.document.getElementById("txtEmpInfoEmail2");
		txtLoginID = window.document.getElementById("txtLoginID");
		
		if(txtEmailOffical2.value=="" && txtLoginID.value=="" )
		{
			if (confirm("Offical email is not provided.\n\nAre you sure to continue to create Login ID without offical email address?"))
			{
				var a = prompt("Please enter new login id","");
				if (a=="")
				{
					event.returnValue=false;
					return false;
					
				}
				else
				{
					txtLoginID.value = a;
					event.returnValue=true;
					return true;
				}
			}
			else
			{
				event.returnValue=false;
				return false;
			}
		}
		else
		{
			event.returnValue=true;
			return true;
		}
	}
function window_onscroll() {
document.getElementById("txtX").value=document.body.scrollTop;
document.getElementById("txtY").value=document.body.scrollLeft;
}

function window_onload() {
document.body.scrollTop=document.getElementById("txtX").value;
document.body.scrollLeft=document.getElementById("txtY").value;
}
function checkNum()
{
   var ddBox=document.getElementById("ddProbabtion");
   if(ddBox.value==-2)
   { 
   var str= document.getElementById("txtProbabtionOther");
   //if(isNaN(str.value)==true)
   if (event.keyCode <48 || event.keyCode >57)
   {
    alert("Please enter only numeric values");
    str.value="";
    str.focus();
    return false;  
   } 
 }
}
function CheckVacancy()
{
 var Val=document.getElementById('lblFV');
 if(Val.innerHTML=='0')
 {
  alert('You can not assign selected functional designation because there is no vacant position on it');
  document.getElementById('ddFDesignation').focus();
  event.retrurnValue=false;
  return false;
 }
 else if (Val.innerHTML=='')
 {
  alert('Please select Functional Designation');
  document.getElementById('ddFDesignation').focus();
  event.retrurnValue=false;
  return false;
 }
 else
 {
    event.retrurnValue=true;
    return true;
 }
}
//==================Get Ex-Employee Profile=======================//
function GetExEmployee(e)
			{
				var divMain=document.getElementById("divMain");
				var ddMdept=document.getElementById("ddDepartment");
				var tr=document.getElementById("tr"+idx);	
				if(tr != null)
				{
					
					tr.style.background='white';
					tr.cells(0).style.color='black';
				}
				var desg=document.getElementById("txtName").value;
				if (desg!=null)
				{
					if (desg=="")
					{
						divMain.style.visibility="hidden";
						//var btnQueryButton=document.getElementById("btnUpdate")
						//if(btnQueryButton!=null) 
							//btnQueryButton.disabled=false;
						
						var ddMdept=document.getElementById("ddDepartment");
						ddMdept.style.visibility="visible";
						
					}
				}
				var eKey=false;
				kc=e.keyCode;
				if (kc==13)
				{
					if (idx>-1)
					{
						var td=document.getElementById('td'+idx);
						var txtDesg=document.getElementById("txtName");
						txtDesg.value=td.innerText;
						txtDesg.focus();
						
						//var btnQueryButton=document.getElementById("btnUpdate")
						//if(btnQueryButton!=null) 
							//btnQueryButton.disabled=false;
						var ddMdept=document.getElementById("ddDepartment");
						ddMdept.style.visibility="visible";
						//=========Get Profile Values through Key selection==========//
						var tr2=document.getElementById("tr"+idx);
						var cat_id=tr2.cells(1).innerHTML;
						var isDH=tr2.cells(2).innerHTML;
						var catafterDH=tr2.cells(3).innerHTML;
						var refIds=document.getElementById("txtRefCode");
						var profileIds=catafterDH+'#'+isDH+'#'+cat_id
						idx=-1;
						divMain.style.visibility="hidden";
						refIds.value=profileIds;
						__doPostBack("txtRefCode", "TextChanged");
				       //============================================================//
				     }
				}
				
				if(kc==38)
				{
					eKey=true;
					if(idx==-1)
						idx=totalItems-1
					else
						idx--;
				}
				if(kc==40)
				{
					eKey=true;
					// down;
					if(idx==totalItems)
						idx=-1;
					else
						idx++;
				}

				var tr=document.getElementById("tr"+idx);	
				if(tr != null)
				{
					tr.style.background='blue';
					tr.cells(0).style.color='white';
				}

				if ((kc>=48 && kc<=57) || (kc>=65 && kc<=90) || kc==32 || kc==8 || kc==46 )
				{
					
					idx=-1;
					var desg=document.getElementById("txtName");
					if (desg.value!="")
					{
					    //=============sbu -1 = only exited=========//
						var desgParameter="id="+desg.value+"&sbu=-1";
						//alert(desgParameter);
						SearchProfile('../searchprofile.aspx',desgParameter);
						
					}
					else
					{
						divMain.style.visibility="hidden";
						//var btnQueryButton=document.getElementById("btnUpdate")
						//if(btnQueryButton!=null) 
							//btnQueryButton.disabled=false;
						var ddMdept=document.getElementById("ddDepartment");
						ddMdept.style.visibility="visible";
					}
				}
				if(desg.value=="")
				{
					divMain.style.visibility="hidden";
					//var btnQueryButton=document.getElementById("btnUpdate")
					//if(btnQueryButton!=null) 
						//btnQueryButton.disabled=false;
					var ddMdept=document.getElementById("ddDepartment");
					ddMdept.style.visibility="visible";					
				}
			}
function disableEnterKey(e)
			{
			    var key;
			    if(window.event)
					key = window.event.keyCode;     //IE
				else
					key = e.which;     //firefox
				if(key == 13)
					return false;
				else
					return true;
			}
function CloseIt()
			{
				
				var divMain = document.getElementById("divMain");
				divMain.style.visibility="hidden";
								
				var td=document.getElementById("td"+idx);
				var tr=document.getElementById("tr"+idx);
				var txtDesg=document.getElementById("txtName");
				var ddMdept=document.getElementById("ddDepartment");
				if(txtDesg!=null)
				{
					txtDesg.value=td.innerText;
					txtDesg.focus();
				}
				
				//var btnQueryButton=document.getElementById("btnUpdate")
				//if(btnQueryButton!=null) 
					//btnQueryButton.disabled=false;
				if(ddMdept!=null)
					ddMdept.style.visibility="visible";
				var cat_id=tr.cells(1).innerHTML;
				var isDH=tr.cells(2).innerHTML;
				var catafterDH=tr.cells(3).innerHTML;
				var refIds=document.getElementById("txtRefCode");
				var profileIds=catafterDH+'#'+isDH+'#'+cat_id
				divMain.style.visibility="hidden";
				refIds.value=profileIds;
				__doPostBack("txtRefCode", "TextChanged");
				
			}
	function SelectIt(i)
			{
				var td=document.getElementById("td"+i);
				var tr=document.getElementById("tr"+i);
				var txtDesg=document.getElementById("txtName");
				var ddMdept=document.getElementById("ddDepartment");
				if(txtDesg!=null)
				{
					txtDesg.value=td.innerText;
					txtDesg.focus();
				}
				
				//var btnQueryButton=document.getElementById("btnUpdate")
				//if(btnQueryButton!=null) 
					//btnQueryButton.disabled=false;
				if(ddMdept!=null)
					ddMdept.style.visibility="visible";
				var cat_id=tr.cells(1).innerHTML;
				var isDH=tr.cells(2).innerHTML;
				var catafterDH=tr.cells(3).innerHTML;
				var refIds=document.getElementById("txtRefCode");
				var profileIds=catafterDH+'#'+isDH+'#'+cat_id
				divMain.style.visibility="hidden";
				refIds.value=profileIds;
				__doPostBack("txtRefCode", "TextChanged");	
			}
	function findPosX(obj)
     {
	var curleft = 0;
	if(obj.offsetParent)
		while(1) 
		{
			curleft += obj.offsetLeft;
			if(!obj.offsetParent)
				break;
			obj = obj.offsetParent;
        }
    else if(obj.x)
        curleft += obj.x;
    return curleft;
}

function findPosY(obj)
{
	var curtop = 0;
	if(obj.offsetParent)
		while(1)
		{
			curtop += obj.offsetTop;
			if(!obj.offsetParent)
				break;
			obj = obj.offsetParent;
		}
	else if(obj.y)
	curtop += obj.y;
	return curtop;
}
function Highlight(i)
			{
				var k=idx;
				var tr=document.getElementById("tr"+k);	
				if(tr != null)
				{
					
					tr.style.background='white';
					tr.cells(0).style.color='black';
					
				}
				var tr=document.getElementById("tr"+i);	
				if(tr != null)
				{
					tr.style.background='blue';
					tr.cells(0).style.color='white';
					idx=i;
				}
			}
//===========================================================================================================//
		</script>
		<script id="clientEventHandlersJS" language="javascript">
<!--
function window_onscroll() {
}
function window_onload() {
}

//-->
		</script>
	</HEAD>
	<body onscroll="return window_onscroll()" language="javascript" onload="return window_onload()"
		bottomMargin="0" leftMargin="0" rightMargin="0" bgProperties="fixed" topMargin="0">
		<form id="myForm" name="MyForm" runat="server">
			<DIV style="POSITION: absolute; BORDER-BOTTOM-COLOR: gray; BORDER-RIGHT-WIDTH: 1px; BORDER-TOP-COLOR: gray; WIDTH: 312px; FONT-FAMILY: Verdana; BORDER-TOP-WIDTH: 1px; BORDER-BOTTOM-WIDTH: 1px; HEIGHT: 284px; VISIBILITY: hidden; COLOR: #4d4d4d; BORDER-RIGHT-COLOR: gray; FONT-SIZE: 12px; OVERFLOW: auto; BORDER-LEFT-COLOR: gray; BORDER-LEFT-WIDTH: 1px; TOP: 256px; FONT-WEIGHT: normal; LEFT: 464px"
				id="divMain"><SPAN id="txtHint"></SPAN></DIV>
			<TABLE id="Table1" border="0" cellSpacing="0" cellPadding="0" width="780" bgColor="white"
				align="center" height="100%">
				<tr>
					<td height="69" vAlign="middle" background="../images/orangestrip2.jpg" align="left">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" width="780" height="69">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="1826">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top1.swf">
							<PARAM NAME="Src" VALUE="flash/Top1.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<embed src="flash/Top1.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="69"> </embed>
						</OBJECT>
					</td>
				</tr>
				<tr>
					<td height="10">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" width="780" height="10">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="265">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top2.swf">
							<PARAM NAME="Src" VALUE="flash/Top2.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<embed src="flash/Top2.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="10"> </embed>
						</OBJECT>
					</td>
				</tr>
				<TR>
					<TD class="PageTitle" height="20">Geo Rabata Admin :: User Management :: New 
						Employee</TD>
				</TR>
				<tr>
					<td height="20" background="../images/menu-off-bg.gif"><uc1:mymenus id="MyMenus1" runat="server"></uc1:mymenus></td>
				</tr>
				<TR>
					<TD class="MainBG" vAlign="top" align="left">
						<TABLE id="Table2" border="0" cellSpacing="0" cellPadding="0" width="100%" align="center">
							<TR>
								<TD vAlign="top" align="left">
									<table border="0" cellSpacing="0" cellPadding="2" width="100%" bgColor="white" align="center">
										<tr>
											<td width="780"><asp:imagebutton id="ImageButton1" runat="server" ImageUrl="..\images\employee.gif" CausesValidation="False"></asp:imagebutton><asp:imagebutton id="ImageButton2" runat="server" ImageUrl="..\images\personal.gif" CausesValidation="False"></asp:imagebutton><asp:imagebutton id="ImageButton3" runat="server" ImageUrl="..\images\family.gif" CausesValidation="False"
													Visible="False"></asp:imagebutton><asp:imagebutton id="ImageButton4" runat="server" ImageUrl="..\images\education.gif" CausesValidation="False"
													Visible="False"></asp:imagebutton><asp:imagebutton id="ImageButton5" runat="server" ImageUrl="..\buttons\account.gif" CausesValidation="False"
													Visible="False"></asp:imagebutton><asp:imagebutton id="ImageButton6" runat="server" ImageUrl="..\buttons\bondpaper.gif" CausesValidation="False"
													Visible="False"></asp:imagebutton><asp:imagebutton id="ImageButton7" runat="server" ImageUrl="..\buttons\tabFunctionalDesignation.gif"
													CausesValidation="False" Visible="False"></asp:imagebutton><asp:imagebutton id="ImgCompensation" runat="server" ImageUrl="../buttons/CompensationBenefits.gif"
													Visible="False"></asp:imagebutton><asp:imagebutton id="ImgRelative" runat="server" ImageUrl="../images/relativeinfo.gif" Visible="False"></asp:imagebutton><asp:imagebutton style="Z-INDEX: 0" id="imgContract" runat="server" ImageUrl="../images/contract.gif"
													Visible="False"></asp:imagebutton><asp:panel id="Panel1" runat="server" Width="100%" BackImageUrl="..\images\tabstrip.jpg"></asp:panel><asp:image id="Image1" runat="server" Visible="False" Height="110px"></asp:image><asp:panel id="pnlPersonal" runat="server" Visible="False" Width="100%">
													<asp:Panel id="pnlPersonalUPdate" runat="server" Width="100%">
														<STRONG>
															<BR>
															<TABLE id="Table11" class="MainFormColor" border="0" cellSpacing="0" cellPadding="3" width="750"
																align="center">
																<TR>
																	<TD class="OrangeFormTitle" colSpan="3">Personal Inforamtion</TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320">
																		<asp:Label id="Label9" runat="server" Font-Size="Small" Font-Bold="True" Font-Names="Courier New">*</asp:Label><STRONG>Date 
																			Of Birth: </STRONG>
																		<asp:RequiredFieldValidator id="RequiredFieldValidator2" runat="server" ControlToValidate="calendarDOB" ErrorMessage="Please Select Date"
																			Display="Dynamic">*</asp:RequiredFieldValidator>
																		<asp:CompareValidator id="CompareValidator2" runat="server" ControlToValidate="calendarDOB" ErrorMessage="Date of birth must be less  than date of  join"
																			Display="Dynamic" Type="Date" ControlToCompare="CalenderDOJ" Operator="LessThan">*</asp:CompareValidator>
																		<asp:TextBox style="VISIBILITY: hidden" id="txtHidden" runat="server" CssClass="textbox"></asp:TextBox></TD>
																	<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
																	<TD class="menubar" vAlign="top" width="320">
																		<asp:Label id="Label10" runat="server" Font-Size="Small" Font-Bold="True" Font-Names="Courier New">*</asp:Label><STRONG>Father 
																			Name: </STRONG>
																		<asp:RequiredFieldValidator id="RequiredFieldValidator3" runat="server" ControlToValidate="txtFName" ErrorMessage="Please Enter Father's Name"
																			Display="Dynamic">*</asp:RequiredFieldValidator>
																		<asp:RegularExpressionValidator id="RegularExpressionValidator6" runat="server" ControlToValidate="txtFName" ErrorMessage="Enter Only Alphabets"
																			Display="Dynamic" ValidationExpression="^[a-z A-Z.-]+[(a-z A-Z)]*$">*</asp:RegularExpressionValidator></TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<ew:CalendarPopup id="calendarDOB" runat="server" EnableHideDropDown="True" Nullable="True">
																			<TextboxLabelStyle CssClass="TextBox"></TextboxLabelStyle>
																			<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></WeekdayStyle>
																			<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Yellow"></MonthHeaderStyle>
																			<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																				BackColor="AntiqueWhite"></OffMonthStyle>
																			<ButtonStyle CssClass="Button"></ButtonStyle>
																			<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></GoToTodayStyle>
																			<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="LightGoldenrodYellow"></TodayDayStyle>
																			<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Orange"></DayHeaderStyle>
																			<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="LightGray"></WeekendStyle>
																			<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Yellow"></SelectedDateStyle>
																			<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></ClearDateStyle>
																			<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></HolidayStyle>
																		</ew:CalendarPopup></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320">
																		<asp:TextBox id="txtFName" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"
																			MaxLength="1000"></asp:TextBox></TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>Gender:</STRONG></TD>
																	<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>Blood Group:</STRONG></TD>
																</TR>
																<TR>
																	<TD style="HEIGHT: 19px" vAlign="top" width="320">
																		<asp:RadioButtonList id="RadioButtonList1" runat="server" Font-Size="10px" RepeatDirection="Horizontal"
																			RepeatLayout="Flow">
																			<asp:ListItem Value="1" Selected="True">Male</asp:ListItem>
																			<asp:ListItem Value="2">Female</asp:ListItem>
																		</asp:RadioButtonList></TD>
																	<TD style="HEIGHT: 19px" vAlign="top" width="95"></TD>
																	<TD style="HEIGHT: 23px" vAlign="top" width="320">
																		<asp:DropDownList id="ddBloodGrp" runat="server" Visible="False" Width="100%" CssClass="textbox">
																			<asp:ListItem Value="0">Select Blood Group</asp:ListItem>
																			<asp:ListItem Value="1">A+</asp:ListItem>
																			<asp:ListItem Value="2">A-</asp:ListItem>
																			<asp:ListItem Value="3">B+</asp:ListItem>
																			<asp:ListItem Value="4">B-</asp:ListItem>
																			<asp:ListItem Value="5">AB+</asp:ListItem>
																			<asp:ListItem Value="6">AB-</asp:ListItem>
																			<asp:ListItem Value="7">O+</asp:ListItem>
																			<asp:ListItem Value="8">O-</asp:ListItem>
																		</asp:DropDownList></TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>Religion:</STRONG></TD>
																	<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>Nick Name: </STRONG>
																	</TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<asp:DropDownList id="ddReligion" runat="server" Width="100%" CssClass="textbox">
																			<asp:ListItem Value="0">Select--Religion</asp:ListItem>
																			<asp:ListItem Value="1">Islam</asp:ListItem>
																			<asp:ListItem Value="2">Christianity</asp:ListItem>
																			<asp:ListItem Value="3">Buddhism</asp:ListItem>
																			<asp:ListItem Value="4">Zoroastrian</asp:ListItem>
																			<asp:ListItem Value="5">Jewish</asp:ListItem>
																			<asp:ListItem Value="6">Hinduism</asp:ListItem>
																			<asp:ListItem Value="7">Others</asp:ListItem>
																		</asp:DropDownList></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320">
																		<asp:TextBox id="txtNick" runat="server" Width="100%" CssClass="textbox" autocomploete="off"
																			MaxLength="100"></asp:TextBox></TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320">
																		<asp:Label id="Label11" runat="server" Font-Size="Small" Font-Bold="True" Font-Names="Courier New">*</asp:Label><STRONG>Address:
																		</STRONG>
																		<asp:RequiredFieldValidator id="RequiredFieldValidator6" runat="server" ControlToValidate="txtAddress" ErrorMessage="Please Enter Residential Address"
																			Display="Dynamic">*</asp:RequiredFieldValidator></TD>
																	<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>Marital Status:</STRONG></TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<asp:TextBox id="txtAddress" runat="server" Width="100%" CssClass="TextBox" MaxLength="2000"
																			TextMode="MultiLine"></asp:TextBox></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320">
																		<asp:DropDownList id="ddStatus" runat="server" Width="100%" CssClass="textbox">
																			<asp:ListItem Value="0">Select--Status</asp:ListItem>
																			<asp:ListItem Value="1">Single</asp:ListItem>
																			<asp:ListItem Value="2">Married</asp:ListItem>
																			<asp:ListItem Value="3">Divorced</asp:ListItem>
																			<asp:ListItem Value="4">Widow</asp:ListItem>
																			<asp:ListItem Value="5">Separated</asp:ListItem>
																		</asp:DropDownList></TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>Email (Personal): </STRONG>
																		<asp:RegularExpressionValidator id="RegularExpressionValidator9" runat="server" ControlToValidate="txtEmail" ErrorMessage="Enter email in correct format<br><EMAIL>"
																			Display="Dynamic" ValidationExpression="\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*">*</asp:RegularExpressionValidator></TD>
																	<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>NTN No:</STRONG></TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<asp:TextBox id="txtEmail" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"
																			MaxLength="1000"></asp:TextBox></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320">
																		<asp:TextBox id="txtNTN" runat="server" Width="100%" CssClass="TextBox" autocomploete="off" MaxLength="50"></asp:TextBox></TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>Residential Tel #: </STRONG>
																		<asp:RegularExpressionValidator id="RegularExpressionValidator7" runat="server" ControlToValidate="txtContactNo"
																			ErrorMessage="Alphabets not allowed in residential telephone" Display="Dynamic" ValidationExpression="^[ 0-9-,]*$">*</asp:RegularExpressionValidator></TD>
																	<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>Mobile #: </STRONG>
																		<asp:RegularExpressionValidator id="RegularExpressionValidator8" runat="server" ControlToValidate="txtMobile" ErrorMessage="Alphabets not allowed"
																			Display="Dynamic" ValidationExpression="^[0-9 ,]*$">*</asp:RegularExpressionValidator></TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<asp:TextBox id="txtContactNo" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"
																			MaxLength="50"></asp:TextBox></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320">
																		<asp:TextBox id="txtMobile" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"
																			MaxLength="50"></asp:TextBox></TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>Nationality: </STRONG>
																		<asp:CustomValidator id="CustomValidator1" runat="server" ControlToValidate="ddNationality" ErrorMessage="Please select nationality">*</asp:CustomValidator></TD>
																	<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>Nationality 2:</STRONG></TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<asp:DropDownList id="ddNationality" runat="server" Width="100%" CssClass="textbox" AutoPostBack="True">
																			<asp:ListItem Value="0">Select--Nationality</asp:ListItem>
																		</asp:DropDownList></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320">
																		<asp:DropDownList id="ddNationality2" runat="server" Width="100%" CssClass="textbox" AutoPostBack="True"
																			Enabled="False"></asp:DropDownList></TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>Passport #:
																			<asp:RequiredFieldValidator id="RequiredFieldValidator5" runat="server" ControlToValidate="txtPassport" ErrorMessage="Please enter passportno"
																				Display="Dynamic" Enabled="False">*</asp:RequiredFieldValidator></STRONG></TD>
																	<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>Next Of Kin:</STRONG></TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<asp:TextBox id="txtPassport" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"
																			MaxLength="100"></asp:TextBox></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320">
																		<asp:TextBox id="txtKin" runat="server" Width="100%" CssClass="TextBox" autocomploete="off" MaxLength="1000"></asp:TextBox></TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320">
																		<asp:Panel id="row1" runat="server" Width="304px" Height="8px">
																			<asp:Label id="Label16" runat="server" Font-Size="Small" Font-Bold="True" Font-Names="Courier New">*</asp:Label>
																			<STRONG>NIC #(New):</STRONG>
																			<asp:RequiredFieldValidator id="RequiredFieldValidator4" runat="server" ControlToValidate="txtNewNic" ErrorMessage="Please enter your New NIC"
																				Display="Dynamic" Enabled="False">*</asp:RequiredFieldValidator>
																			<asp:RegularExpressionValidator id="RegularExpressionValidator1" runat="server" ControlToValidate="txtNewNic" ErrorMessage="Please enter your  New NIC in correct format<br>example:12345-1234567-1"
																				Display="Dynamic" ValidationExpression="^\d{5}[-]\d{7}[-]\d{1}$">*</asp:RegularExpressionValidator>
																			<STRONG></STRONG>
																		</asp:Panel></TD>
																	<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
																	<TD class="menubar" vAlign="top" width="320">
																		<asp:Panel id="row3" runat="server">
																			<STRONG>NIC #(Old):</STRONG>
																			<asp:RegularExpressionValidator id="RegularExpressionValidator2" runat="server" ControlToValidate="txtOldNic" ErrorMessage="Please enter NIC in correct format<br>example:123-12-12345"
																				Display="Dynamic" ValidationExpression="^\d{3}[-]\d{2}[-]\d{6}$">*</asp:RegularExpressionValidator>
																		</asp:Panel></TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<asp:Panel id="row2" runat="server">
																			<ew:MaskedTextBox id="txtNewNic" runat="server" Width="100%" CssClass="textbox" Mask="99999-9999999-9"></ew:MaskedTextBox>
																		</asp:Panel></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320">
																		<asp:Panel id="row4" runat="server">
																			<asp:TextBox id="txtOldNic" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"></asp:TextBox>
																		</asp:Panel></TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>Bank Account No:</STRONG></TD>
																	<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>Account Details:</STRONG></TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<asp:TextBox id="txtBankAcctNo" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"
																			MaxLength="100"></asp:TextBox></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320">
																		<asp:TextBox id="txtAccountDetails" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"
																			MaxLength="1000" TextMode="MultiLine"></asp:TextBox></TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>Picture Upload</STRONG></TD>
																	<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>Compensatory Off:</STRONG></TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320"><INPUT onbeforeeditfocus="return false" style="WIDTH: 240px; HEIGHT: 16px" id="FileToUpload"
																			oncontextmenu="return false" class="TextBox" onkeypress="return false" type="file" runat="server"></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320">
																		<asp:DropDownList id="rdCompansatory" runat="server" Width="100%" CssClass="textbox" Enabled="False">
																			<asp:ListItem Value="-1">Select--Compensatory</asp:ListItem>
																			<asp:ListItem Value="1">Yes</asp:ListItem>
																			<asp:ListItem Value="0">No</asp:ListItem>
																		</asp:DropDownList></TD>
																</TR>
																<TR>
																	<TD id="rowNIC" vAlign="top" colSpan="3" runat="server">
																		<asp:DataGrid id="dgNIC" runat="server" Visible="False" Width="100%" AutoGenerateColumns="False"
																			GridLines="Horizontal" CellPadding="3" BackColor="White" BorderWidth="1px" BorderColor="Navy"
																			BorderStyle="Solid">
																			<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
																			<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
																			<AlternatingItemStyle CssClass="alternetitem"></AlternatingItemStyle>
																			<ItemStyle CssClass="item"></ItemStyle>
																			<HeaderStyle Font-Bold="True" ForeColor="Black" CssClass="header"></HeaderStyle>
																			<Columns>
																				<asp:BoundColumn DataField="pcode" HeaderText="PCode"></asp:BoundColumn>
																				<asp:BoundColumn DataField="name" HeaderText="Name"></asp:BoundColumn>
																				<asp:BoundColumn DataField="designation" HeaderText="Designation"></asp:BoundColumn>
																				<asp:BoundColumn DataField="department" HeaderText="Department"></asp:BoundColumn>
																				<asp:BoundColumn DataField="active" HeaderText="Status"></asp:BoundColumn>
																				<asp:TemplateColumn>
																					<ItemTemplate>
																						<asp:Button id="btnNIC" onclick="Proceed" runat="server" CssClass="button" Text="Select"></asp:Button>
																					</ItemTemplate>
																				</asp:TemplateColumn>
																				<asp:BoundColumn Visible="False" DataField="del"></asp:BoundColumn>
																			</Columns>
																			<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
																		</asp:DataGrid>
																		<asp:Label id="lblEmp" runat="server" Font-Bold="True" ForeColor="DarkOrange"></asp:Label></TD>
																</TR>
																<TR>
																	<TD vAlign="top" colSpan="3" align="center">
																		<asp:label id="lblVacancy" runat="server" Visible="False" Font-Bold="True" ForeColor="DarkOrange">Record Insert Successfully</asp:label></TD>
																</TR>
																<TR>
																	<TD vAlign="top" colSpan="3">
																		<asp:Button id="btnUpdate" runat="server" Font-Bold="True" Text="Save"></asp:Button>
																		<asp:TextBox style="VISIBILITY: hidden" id="txtServerDate" runat="server" autocomploete="off"></asp:TextBox></TD>
																</TR>
																<TR>
																	<TD vAlign="top" colSpan="3">
																		<asp:LinkButton id="lnkNewEmployee" runat="server">Add Another Employee</asp:LinkButton></TD>
																</TR>
															</TABLE>
														</STRONG>
													</asp:Panel>
												</asp:panel><asp:panel id="pnlEmployeeInfo" runat="server" Width="100%"><STRONG><FONT size="3"><BR>
															<TABLE id="Table4" class="MainFormColor" border="0" cellSpacing="0" cellPadding="3" width="750"
																align="center">
																<TR>
																	<TD class="OrangeFormTitle" colSpan="3">Employee Inforamtion</TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>Position Type:</STRONG></TD>
																	<TD class="menubar" vAlign="top" width="95"></TD>
																	<TD class="menubar" vAlign="top" width="320">
																		<asp:Label style="Z-INDEX: 0" id="lblRefProfile" runat="server" Font-Bold="True">*Reference Profile</asp:Label>
																		<asp:RequiredFieldValidator style="Z-INDEX: 0" id="RequiredFieldValidator21" runat="server" ControlToValidate="txtName"
																			ErrorMessage="Provide Reference Profile">*</asp:RequiredFieldValidator>
																		<asp:TextBox style="Z-INDEX: 0; DISPLAY: none" id="txtRefCode" runat="server" Width="76px" CssClass="textbox"
																			AutoPostBack="True"></asp:TextBox></TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<asp:DropDownList style="Z-INDEX: 0" id="ddPosType" runat="server" Width="100%" CssClass="textbox"
																			AutoPostBack="True">
																			<asp:ListItem Value="0">Select--Position</asp:ListItem>
																			<asp:ListItem Value="1">New</asp:ListItem>
																			<asp:ListItem Value="2">Replacement</asp:ListItem>
																		</asp:DropDownList></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320">
																		<asp:textbox style="Z-INDEX: 0" id="txtName" tabIndex="1" runat="server" Width="320px" CssClass="textbox"
																			MaxLength="1000" Enabled="False" autocomplete="off"></asp:textbox></TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320">
																		<asp:Label id="Label1" runat="server" Font-Size="Small" Font-Bold="True" Font-Names="Courier New">*</asp:Label><STRONG>Employee 
																			Code:</STRONG>
																	</TD>
																	<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
																	<TD class="menubar" vAlign="top" width="320">
																		<asp:Label id="Label2" runat="server" Font-Size="Small" Font-Bold="True" Font-Names="Courier New">*</asp:Label><STRONG>Name</STRONG>
																		<asp:RequiredFieldValidator id="RequiredFieldValidator8" runat="server" ControlToValidate="txtEmpInfoName" ErrorMessage="Please Enter Name"
																			Display="Dynamic">*</asp:RequiredFieldValidator>
																		<asp:RegularExpressionValidator id="RegularExpressionValidator3" runat="server" ControlToValidate="txtEmpInfoName"
																			ErrorMessage="Enter Only Alphabet In Name" Display="Dynamic" ValidationExpression="^[a-z A-Z.-]+[(a-z A-Z)]*$">*</asp:RegularExpressionValidator></TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<asp:TextBox id="txtEmpInfoCode" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"
																			MaxLength="10" Enabled="False"></asp:TextBox></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320">
																		<asp:TextBox id="txtEmpInfoName" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"
																			MaxLength="1000"></asp:TextBox></TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320" colSpan="3">
																		<asp:Label id="Label14" runat="server" Font-Size="Small" Font-Bold="True" Font-Names="Courier New">*</asp:Label><STRONG>Network
																			<asp:RequiredFieldValidator id="RequiredFieldValidator12" runat="server" ControlToValidate="ddNetwork" ErrorMessage="Please Select Appropriate Network"
																				Display="Dynamic" InitialValue="0">*</asp:RequiredFieldValidator></STRONG></TD>
																</TR>
																<TR>
																	<TD style="HEIGHT: 16px" vAlign="top" width="320" colSpan="3">
																		<asp:DropDownList id="ddNetwork" runat="server" Width="312px" CssClass="TextBox" AutoPostBack="True">
																			<asp:ListItem Value="0">Select -- Network</asp:ListItem>
																		</asp:DropDownList></TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320">
																		<asp:Label id="Label3" runat="server" Font-Size="Small" Font-Bold="True" Font-Names="Courier New">*</asp:Label><STRONG>Department:</STRONG>
																		<asp:CustomValidator id="CustomValidator4" runat="server" ControlToValidate="ddDepartment" ErrorMessage="Please Select Department"
																			Display="Dynamic" OnServerValidate="TextValidate">*</asp:CustomValidator></TD>
																	<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
																	<TD class="menubar" vAlign="top" width="320">
																		<asp:Label id="Label4" runat="server" Font-Size="Small" Font-Bold="True" Font-Names="Courier New">*</asp:Label><STRONG>Designation:</STRONG>
																		<asp:CustomValidator id="CustomValidator2" runat="server" ControlToValidate="ddDesignation" ErrorMessage="Please Select Designation"
																			Display="Dynamic">*</asp:CustomValidator></TD>
																</TR>
																<TR>
																	<TD style="HEIGHT: 17px" vAlign="top" width="320">
																		<asp:DropDownList id="ddDepartment" runat="server" Width="100%" CssClass="TextBox" AutoPostBack="True">
																			<asp:ListItem Value="0">Select -- Department</asp:ListItem>
																		</asp:DropDownList></TD>
																	<TD style="HEIGHT: 17px" vAlign="top" width="95"></TD>
																	<TD style="HEIGHT: 17px" vAlign="top" width="320">
																		<asp:DropDownList id="ddDesignation" runat="server" Width="100%" CssClass="textbox" AutoPostBack="True">
																			<asp:ListItem Value="0">Select--Designation</asp:ListItem>
																		</asp:DropDownList></TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320">
																		<asp:Label id="Label13" runat="server" Font-Size="Small" Font-Bold="True" Font-Names="Courier New">*</asp:Label><STRONG>Functional 
																			Designation (Vacant&nbsp;Position(s)-&gt;
																			<asp:Label id="lblFV" runat="server"></asp:Label>)&nbsp;</STRONG></TD>
																	<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
																	<TD class="menubar" vAlign="top" width="320">
																		<asp:Label id="Label5" runat="server" Font-Size="Small" Font-Bold="True" Font-Names="Courier New">*</asp:Label><STRONG>Station:</STRONG>
																		<asp:CustomValidator id="CustomValidator3" runat="server" ControlToValidate="ddOpertingCity" ErrorMessage="Please Select Station"
																			Display="Dynamic">*</asp:CustomValidator></TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<asp:DropDownList id="ddFDesignation" runat="server" Width="100%" CssClass="textbox" AutoPostBack="True">
																			<asp:ListItem Value="0">Select--Functional Designation</asp:ListItem>
																		</asp:DropDownList></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320">
																		<asp:DropDownList id="ddOpertingCity" runat="server" Width="100%" CssClass="textbox">
																			<asp:ListItem Value="0">Select--Operating Station</asp:ListItem>
																		</asp:DropDownList></TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>Employment Status:(IF Employee 
																			is&nbsp;Expected&nbsp;To Join&nbsp;THEN Must Give His/Her Tentative Date of 
																			Join)</STRONG></TD>
																	<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>Tentative Date of Join: </STRONG>
																		<asp:RequiredFieldValidator id="RequiredFieldValidator11" runat="server" ControlToValidate="CTDOJ" ErrorMessage="Please Select Tentative Date of Joining"
																			Display="Dynamic">*</asp:RequiredFieldValidator></TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<asp:DropDownList id="ddeStatus" runat="server" Width="160px" CssClass="textbox" AutoPostBack="True">
																			<asp:ListItem Value="1" Selected="True">Expected To Join</asp:ListItem>
																			<asp:ListItem Value="0">Serving</asp:ListItem>
																		</asp:DropDownList></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320">
																		<ew:CalendarPopup id="CTDOJ" runat="server" Width="208px" EnableHideDropDown="True" Nullable="True"
																			AutoPostBack="True">
																			<TextboxLabelStyle CssClass="TextBox"></TextboxLabelStyle>
																			<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></WeekdayStyle>
																			<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Yellow"></MonthHeaderStyle>
																			<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																				BackColor="AntiqueWhite"></OffMonthStyle>
																			<ButtonStyle CssClass="Button"></ButtonStyle>
																			<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></GoToTodayStyle>
																			<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="LightGoldenrodYellow"></TodayDayStyle>
																			<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Orange"></DayHeaderStyle>
																			<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="LightGray"></WeekendStyle>
																			<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Yellow"></SelectedDateStyle>
																			<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></ClearDateStyle>
																			<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></HolidayStyle>
																		</ew:CalendarPopup></TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320">
																		<asp:Label id="Label6" runat="server" Font-Size="Small" Font-Bold="True" Font-Names="Courier New">*</asp:Label><STRONG>Date 
																			Of Joining:</STRONG>
																		<asp:RequiredFieldValidator id="RequiredFieldValidator9" runat="server" ControlToValidate="CalenderDOJ" ErrorMessage="Please Select Date Of Join"
																			Display="Dynamic">*</asp:RequiredFieldValidator></TD>
																	<TD class="menubar" vAlign="top" width="95"></TD>
																	<TD class="menubar" vAlign="top" width="320">
																		<asp:Label id="Label12" runat="server" Font-Size="Small" Font-Bold="True" Font-Names="Courier New">*</asp:Label><STRONG>Probabtion/Confirmation 
																			Period (in Months)</STRONG></TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<ew:CalendarPopup id="CalenderDOJ" runat="server" Width="208px" EnableHideDropDown="True" Nullable="True"
																			AutoPostBack="True" Text="...">
																			<TextboxLabelStyle CssClass="TextBox"></TextboxLabelStyle>
																			<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></WeekdayStyle>
																			<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Yellow"></MonthHeaderStyle>
																			<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																				BackColor="AntiqueWhite"></OffMonthStyle>
																			<ButtonStyle CssClass="Button"></ButtonStyle>
																			<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></GoToTodayStyle>
																			<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="LightGoldenrodYellow"></TodayDayStyle>
																			<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Orange"></DayHeaderStyle>
																			<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="LightGray"></WeekendStyle>
																			<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Yellow"></SelectedDateStyle>
																			<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></ClearDateStyle>
																			<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></HolidayStyle>
																		</ew:CalendarPopup></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320">
																		<asp:DropDownList id="ddProbabtion" runat="server" Width="328px" CssClass="textbox" AutoPostBack="True">
																			<asp:ListItem Value="-1">Select--Probabtion Period</asp:ListItem>
																			<asp:ListItem Value="0">0</asp:ListItem>
																			<asp:ListItem Value="3">3</asp:ListItem>
																			<asp:ListItem Value="6">6</asp:ListItem>
																			<asp:ListItem Value="12">12</asp:ListItem>
																			<asp:ListItem Value="-2">Other</asp:ListItem>
																		</asp:DropDownList><BR>
																		<asp:TextBox id="txtProbabtionOther" runat="server" Visible="False" Width="96px" CssClass="TextBox"
																			autocomploete="off" MaxLength="50" AutoPostBack="True"></asp:TextBox></TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>
																			<asp:Label id="Label7" runat="server" Font-Size="Small" Font-Bold="True" Font-Names="Courier New">*</asp:Label>Confirmation 
																			Due&nbsp;Date:</STRONG> <STRONG>
																			<asp:RequiredFieldValidator id="RequiredFieldValidator7" runat="server" ControlToValidate="CalenderCOD" ErrorMessage="Please Select Due Date Of Confirmation"
																				Display="Dynamic">*</asp:RequiredFieldValidator></STRONG></TD>
																	<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>Confirmation Date:
																			<asp:CompareValidator id="CompareValidator1" runat="server" ControlToValidate="calenderFinalConfirmation"
																				ErrorMessage="Confirmation date must be greater than or equal to date of join" Display="Dynamic" Type="Date"
																				ControlToCompare="CalenderDOJ" Operator="GreaterThanEqual">*</asp:CompareValidator></STRONG></TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<ew:CalendarPopup id="CalenderCOD" runat="server" Width="208px" EnableHideDropDown="True" Nullable="True"
																			Enabled="False">
																			<TextboxLabelStyle CssClass="TextBox"></TextboxLabelStyle>
																			<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></WeekdayStyle>
																			<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Yellow"></MonthHeaderStyle>
																			<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																				BackColor="AntiqueWhite"></OffMonthStyle>
																			<ButtonStyle CssClass="Button"></ButtonStyle>
																			<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></GoToTodayStyle>
																			<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="LightGoldenrodYellow"></TodayDayStyle>
																			<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Orange"></DayHeaderStyle>
																			<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="LightGray"></WeekendStyle>
																			<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Yellow"></SelectedDateStyle>
																			<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></ClearDateStyle>
																			<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></HolidayStyle>
																		</ew:CalendarPopup></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320">
																		<ew:CalendarPopup id="calenderFinalConfirmation" runat="server" Width="208px" EnableHideDropDown="True"
																			Nullable="True" CellPadding="50px" DisplayOffsetY="-70" CellSpacing="10px" CalendarLocation="Top">
																			<TextboxLabelStyle CssClass="textbox"></TextboxLabelStyle>
																			<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></WeekdayStyle>
																			<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Yellow"></MonthHeaderStyle>
																			<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																				BackColor="AntiqueWhite"></OffMonthStyle>
																			<ButtonStyle CssClass="button"></ButtonStyle>
																			<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></GoToTodayStyle>
																			<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="LightGoldenrodYellow"></TodayDayStyle>
																			<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Orange"></DayHeaderStyle>
																			<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="LightGray"></WeekendStyle>
																			<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Yellow"></SelectedDateStyle>
																			<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></ClearDateStyle>
																			<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></HolidayStyle>
																		</ew:CalendarPopup></TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320">
																		<asp:Label id="Label8" runat="server" Font-Size="Small" Font-Bold="True" Font-Names="Courier New">*</asp:Label><STRONG>Employment 
																			Type:</STRONG>
																		<asp:CustomValidator id="CustomValidator5" runat="server" ControlToValidate="txtEmpInfoTOE" ErrorMessage="Please Select Employee Type"
																			Display="Dynamic">*</asp:CustomValidator></TD>
																	<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>Email Official:</STRONG> <STRONG>
																			<asp:RegularExpressionValidator id="RegularExpressionValidator4" runat="server" ControlToValidate="txtEmpInfoEmail"
																				ErrorMessage="Please enter email in correct formatexample:<EMAIL>" Display="Dynamic" ValidationExpression="\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*">*</asp:RegularExpressionValidator></STRONG></TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<asp:DropDownList id="txtEmpInfoTOE" runat="server" Width="100%" CssClass="textbox">
																			<asp:ListItem Value="0">Select--Employment</asp:ListItem>
																			<asp:ListItem Value="1">Permanent</asp:ListItem>
																			<asp:ListItem Value="2">Contractual</asp:ListItem>
																			<asp:ListItem Value="3">Retainer</asp:ListItem>
																			<asp:ListItem Value="4">Honorary</asp:ListItem>
																			<asp:ListItem Value="5">Talent</asp:ListItem>
																		</asp:DropDownList></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320">
																		<asp:TextBox id="txtEmpInfoEmail" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"
																			MaxLength="1000"></asp:TextBox></TD>
																</TR>
																<TR>
																	<TD style="HEIGHT: 20px" class="menubar" vAlign="top" width="320"><STRONG>EOBI #:</STRONG></TD>
																	<TD style="HEIGHT: 20px" class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>SESSI #:</STRONG></TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<asp:TextBox id="txtEmpInfoEOBI" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"
																			MaxLength="50"></asp:TextBox></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320">
																		<asp:TextBox id="txtEmpInfoSESSI" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"
																			MaxLength="50"></asp:TextBox></TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>Employee Working&nbsp;Location:</STRONG></TD>
																	<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
																	<TD class="menubar" vAlign="top" width="320"><STRONG><STRONG>Insurance Code:</STRONG></STRONG></TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<asp:TextBox id="txtEmpLocation" runat="server" Width="100%" CssClass="textbox" autocomploete="off"
																			TextMode="MultiLine"></asp:TextBox></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320">
																		<asp:TextBox id="txtEmpInfoInsurance" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"
																			MaxLength="50"></asp:TextBox></TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>New Joining:</STRONG></TD>
																	<TD class="menubar" vAlign="top" width="95"></TD>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>Extension #:</STRONG> <STRONG>
																			<asp:RegularExpressionValidator id="RegularExpressionValidator5" runat="server" ControlToValidate="txtEmpInfoExtension"
																				ErrorMessage="Enter Only Numeric Values In Extension" Display="Dynamic" ValidationExpression="^[0-9 ,]*$">*</asp:RegularExpressionValidator></STRONG></TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<asp:DropDownList id="ddNewJoiner" runat="server" Width="80px" CssClass="textbox">
																			<asp:ListItem Value="0">No</asp:ListItem>
																			<asp:ListItem Value="1" Selected="True">Yes</asp:ListItem>
																		</asp:DropDownList></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320">
																		<asp:TextBox id="txtEmpInfoExtension" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"
																			MaxLength="50"></asp:TextBox></TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<asp:DropDownList id="ddNICcity" runat="server" Visible="False" Width="100%" CssClass="textbox">
																			<asp:ListItem Value="0">Select--Reporting City</asp:ListItem>
																		</asp:DropDownList></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320">
																		<ew:CalendarPopup id="InterimCommitment" runat="server" Visible="False" Width="208px" Nullable="True">
																			<TextboxLabelStyle CssClass="TextBox"></TextboxLabelStyle>
																			<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></WeekdayStyle>
																			<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Yellow"></MonthHeaderStyle>
																			<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																				BackColor="AntiqueWhite"></OffMonthStyle>
																			<ButtonStyle CssClass="Button"></ButtonStyle>
																			<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></GoToTodayStyle>
																			<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="LightGoldenrodYellow"></TodayDayStyle>
																			<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Orange"></DayHeaderStyle>
																			<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="LightGray"></WeekendStyle>
																			<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Yellow"></SelectedDateStyle>
																			<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></ClearDateStyle>
																			<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></HolidayStyle>
																		</ew:CalendarPopup></TD>
																</TR>
															</TABLE>
														</FONT></STRONG>
												</asp:panel><asp:panel id="pnlFamilyInfo" runat="server" Visible="False" Width="100%"><STRONG><FONT size="3"><BR>
															<TABLE id="Table6" class="MainFormColor" border="0" cellSpacing="0" cellPadding="3" width="750"
																align="center">
																<TR>
																	<TD class="OrangeFormTitle" colSpan="3">Family Inforamtion</TD>
																</TR>
																<TR>
																	<TD vAlign="top" colSpan="3">
																		<asp:DataGrid id="DataGrid1" runat="server" Visible="False" Width="100%" AutoGenerateColumns="False"
																			GridLines="Horizontal" CellPadding="3" BorderWidth="1px" BorderColor="Navy" BorderStyle="Solid">
																			<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
																			<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
																			<AlternatingItemStyle CssClass="alternetitem"></AlternatingItemStyle>
																			<ItemStyle CssClass="item"></ItemStyle>
																			<HeaderStyle Font-Bold="True" ForeColor="Black" CssClass="header"></HeaderStyle>
																			<Columns>
																				<asp:BoundColumn DataField="name" HeaderText="Name"></asp:BoundColumn>
																				<asp:BoundColumn DataField="relationship" HeaderText="RelationShip"></asp:BoundColumn>
																				<asp:BoundColumn DataField="maritialstatus" HeaderText="MaritialStatus"></asp:BoundColumn>
																				<asp:BoundColumn Visible="False" DataField="gender" HeaderText="Gender"></asp:BoundColumn>
																				<asp:BoundColumn Visible="False" DataField="occupation" HeaderText="Occupation"></asp:BoundColumn>
																				<asp:BoundColumn Visible="False" DataField="DOB" HeaderText="Date Of Birth" DataFormatString="{0:d}"></asp:BoundColumn>
																				<asp:BoundColumn DataField="dependent" HeaderText="Dependent"></asp:BoundColumn>
																				<asp:ButtonColumn Text="Edit" CommandName="Select"></asp:ButtonColumn>
																				<asp:BoundColumn Visible="False" DataField="pcode"></asp:BoundColumn>
																				<asp:BoundColumn Visible="False" DataField="fdid"></asp:BoundColumn>
																				<asp:TemplateColumn>
																					<ItemTemplate>
																						<asp:LinkButton id="delFamily" runat="server" CausesValidation="False" OnClick="dFamily">Delete</asp:LinkButton>
																					</ItemTemplate>
																				</asp:TemplateColumn>
																			</Columns>
																			<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
																		</asp:DataGrid>
																		<asp:Label id="lblRecord" runat="server" Font-Bold="True">No Record Found</asp:Label></TD>
																</TR>
															</TABLE>
														</FONT></STRONG><STRONG>
														<asp:Panel id="Panel2" runat="server" Width="100%">
															<BR>
															<TABLE id="Table16" class="MainFormColor" border="0" cellSpacing="0" cellPadding="3" width="750"
																align="center">
																<TR>
																	<TD class="OrangeFormTitle" colSpan="3">Add Family Information:</TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320">Name Of Relative:
																		<asp:Label id="lblSpouse" runat="server" ForeColor="Red">Please enter name of relative</asp:Label></TD>
																	<TD class="menubar" vAlign="top" width="95"></TD>
																	<TD class="menubar" vAlign="top" width="320">Data Of Birth:</TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<asp:TextBox id="txtFamilyName" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"
																			MaxLength="1000"></asp:TextBox></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320">
																		<ew:CalendarPopup id="CalendarPopup1" runat="server" EnableHideDropDown="True" SelectedDate="2007-04-24">
																			<TextboxLabelStyle CssClass="TextBox"></TextboxLabelStyle>
																			<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></WeekdayStyle>
																			<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Yellow"></MonthHeaderStyle>
																			<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																				BackColor="AntiqueWhite"></OffMonthStyle>
																			<ButtonStyle CssClass="Button"></ButtonStyle>
																			<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></GoToTodayStyle>
																			<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="LightGoldenrodYellow"></TodayDayStyle>
																			<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Orange"></DayHeaderStyle>
																			<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="LightGray"></WeekendStyle>
																			<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="Yellow"></SelectedDateStyle>
																			<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></ClearDateStyle>
																			<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																				BackColor="White"></HolidayStyle>
																		</ew:CalendarPopup></TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320">Relationship:
																		<asp:CustomValidator id="CustomValidator8" runat="server" ControlToValidate="rdRelation" ErrorMessage="Please select relationship"
																			Enabled="False">*</asp:CustomValidator></TD>
																	<TD class="menubar" vAlign="top" width="95"></TD>
																	<TD class="menubar" vAlign="top" width="320"><STRONG>Marital</STRONG> Status:
																		<asp:CustomValidator id="CustomValidator6" runat="server" ControlToValidate="rdStatus" ErrorMessage="Please select maritial status"
																			Enabled="False">*</asp:CustomValidator></TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<asp:DropDownList id="rdRelation" runat="server" Width="100%" CssClass="textbox">
																			<asp:ListItem Value="0">Select--Relation</asp:ListItem>
																			<asp:ListItem Value="2">Husband</asp:ListItem>
																			<asp:ListItem Value="3">Wife</asp:ListItem>
																			<asp:ListItem Value="4">Son</asp:ListItem>
																			<asp:ListItem Value="5">Daughter</asp:ListItem>
																		</asp:DropDownList></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320">
																		<asp:DropDownList id="rdStatus" runat="server" Width="100%" CssClass="textbox">
																			<asp:ListItem Value="0">Select--Marital  Status</asp:ListItem>
																			<asp:ListItem Value="1">Single</asp:ListItem>
																			<asp:ListItem Value="2">Married</asp:ListItem>
																			<asp:ListItem Value="3">Divorced</asp:ListItem>
																			<asp:ListItem Value="4">Widow</asp:ListItem>
																			<asp:ListItem Value="5">Separated</asp:ListItem>
																		</asp:DropDownList></TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320">Dependent:</TD>
																	<TD class="menubar" vAlign="top" width="95"></TD>
																	<TD class="menubar" vAlign="top" width="320">Gender:
																		<asp:CustomValidator id="CustomValidator7" runat="server" ControlToValidate="rdFamilyGender" ErrorMessage="Please select gender"
																			Enabled="False">*</asp:CustomValidator></TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<asp:DropDownList id="ddDependent" runat="server" Width="100%" CssClass="textbox">
																			<asp:ListItem Value="0">Select--Option</asp:ListItem>
																			<asp:ListItem Value="1">Yes</asp:ListItem>
																			<asp:ListItem Value="2">No</asp:ListItem>
																		</asp:DropDownList></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320">
																		<asp:DropDownList id="rdFamilyGender" runat="server" Width="100%" CssClass="textbox">
																			<asp:ListItem Value="0">Select--Gender</asp:ListItem>
																			<asp:ListItem Value="1">Male</asp:ListItem>
																			<asp:ListItem Value="2">Female</asp:ListItem>
																		</asp:DropDownList></TD>
																</TR>
																<TR>
																	<TD class="menubar" vAlign="top" width="320">Occupation Of Spouse:</TD>
																	<TD class="menubar" vAlign="top" width="95"></TD>
																	<TD class="menubar" vAlign="top" width="320"></TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<asp:TextBox id="txtOccupation" runat="server" Width="100%" CssClass="textbox" autocomploete="off"
																			MaxLength="1000"></asp:TextBox></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320"></TD>
																</TR>
																<TR>
																	<TD vAlign="top" width="320">
																		<asp:Button id="btnNew" runat="server" Text="Add New"></asp:Button>
																		<asp:Button id="btnFupdate" runat="server" Enabled="False" Text="Update"></asp:Button></TD>
																	<TD vAlign="top" width="95"></TD>
																	<TD vAlign="top" width="320"></TD>
																</TR>
															</TABLE>
														</asp:Panel></STRONG></asp:panel><asp:panel id="pnlEducation" runat="server" Visible="False" Width="100%"><STRONG><BR>
														<TABLE id="Table8" class="MainFormColor" border="0" cellSpacing="0" cellPadding="3" width="750"
															align="center">
															<TR>
																<TD class="OrangeFormTitle" colSpan="3">Educational Information</TD>
															</TR>
															<TR>
																<TD vAlign="top" colSpan="3">
																	<asp:DataGrid id="DataGrid3" runat="server" Visible="False" Width="100%" AutoGenerateColumns="False"
																		GridLines="Horizontal" CellPadding="3" BorderWidth="1px" BorderColor="Navy" BorderStyle="Solid"
																		AllowSorting="True">
																		<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
																		<AlternatingItemStyle CssClass="alternetitem"></AlternatingItemStyle>
																		<ItemStyle CssClass="item"></ItemStyle>
																		<HeaderStyle Font-Bold="True" CssClass="header"></HeaderStyle>
																		<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
																		<Columns>
																			<asp:BoundColumn DataField="degree" SortExpression="degree" HeaderText="Degree"></asp:BoundColumn>
																			<asp:BoundColumn DataField="institute" SortExpression="institute" HeaderText="Institute"></asp:BoundColumn>
																			<asp:BoundColumn Visible="False" DataField="durationfrom" SortExpression="durationfrom" HeaderText="Duration From"
																				DataFormatString="{0:d}"></asp:BoundColumn>
																			<asp:BoundColumn Visible="False" DataField="durationto" SortExpression="durationto" HeaderText="Duration To"
																				DataFormatString="{0:d}"></asp:BoundColumn>
																			<asp:BoundColumn DataField="result" SortExpression="result" HeaderText="Result"></asp:BoundColumn>
																			<asp:BoundColumn DataField="majors" SortExpression="majors" HeaderText="Majors"></asp:BoundColumn>
																			<asp:BoundColumn Visible="False" DataField="achievements" SortExpression="achievements" HeaderText="Distinction"></asp:BoundColumn>
																			<asp:ButtonColumn Text="Edit" CommandName="Select"></asp:ButtonColumn>
																			<asp:BoundColumn Visible="False" DataField="eduinfoid"></asp:BoundColumn>
																			<asp:BoundColumn Visible="False" DataField="type"></asp:BoundColumn>
																			<asp:TemplateColumn>
																				<ItemTemplate>
																					<asp:LinkButton id="delEducation" onclick="dEducation" runat="server" CausesValidation="False">Delete</asp:LinkButton>
																				</ItemTemplate>
																			</asp:TemplateColumn>
																		</Columns>
																		<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
																	</asp:DataGrid>
																	<asp:Label id="lblEdBoRecord" runat="server" Font-Bold="True">No Record Found</asp:Label></TD>
															</TR>
														</TABLE>
													</STRONG><STRONG>
														<TABLE id="Table9" class="MainFormColor" border="0" cellSpacing="0" cellPadding="3" width="750"
															align="center">
															<TR>
																<TD class="OrangeFormTitle" colSpan="3">Add Educational Information</TD>
															</TR>
															<TR>
																<TD class="menubar" vAlign="top" width="320"><STRONG>Degree:
																		<asp:CustomValidator id="CustomValidator9" runat="server" ControlToValidate="txtDegree" ErrorMessage="Please select degree"
																			Enabled="False">*</asp:CustomValidator>
																		<asp:RequiredFieldValidator id="RequiredFieldValidator14" runat="server" ControlToValidate="txtOtherDegree"
																			ErrorMessage="Please enter degree" Enabled="False">*</asp:RequiredFieldValidator></STRONG></TD>
																<TD class="menubar" vAlign="top" width="95"></TD>
																<TD class="menubar" vAlign="top" width="320"><STRONG>Institute:
																		<asp:CustomValidator id="CustomValidator10" runat="server" Visible="True" ControlToValidate="txtInstitute"
																			ErrorMessage="Please select institute" Enabled="False">*</asp:CustomValidator>
																		<asp:RequiredFieldValidator id="RequiredFieldValidator15" runat="server" ControlToValidate="txtOtherInstitute"
																			ErrorMessage="Please enter institute" Enabled="False">*</asp:RequiredFieldValidator></STRONG></TD>
															</TR>
															<TR>
																<TD vAlign="top" width="320">
																	<asp:DropDownList id="txtDegree" runat="server" Width="100%" CssClass="textbox" AutoPostBack="True">
																		<asp:ListItem Value="0">Select--Degree</asp:ListItem>
																	</asp:DropDownList></TD>
																<TD vAlign="top" width="95"></TD>
																<TD vAlign="top" width="320">
																	<asp:DropDownList id="txtInstitute" runat="server" Width="100%" CssClass="textbox" AutoPostBack="True">
																		<asp:ListItem Value="0">Select--Institute</asp:ListItem>
																	</asp:DropDownList></TD>
															</TR>
															<TR>
																<TD vAlign="top" width="320">
																	<asp:TextBox id="txtOtherDegree" runat="server" Visible="False" Width="100%" CssClass="textbox"
																		autocomploete="off"></asp:TextBox></TD>
																<TD vAlign="top" width="95"></TD>
																<TD vAlign="top" width="320">
																	<asp:TextBox id="txtOtherInstitute" runat="server" Visible="False" Width="100%" CssClass="textbox"
																		autocomploete="off"></asp:TextBox></TD>
															</TR>
															<TR>
																<TD class="menubar" vAlign="top" width="320"><STRONG>Result:
																		<asp:CustomValidator id="CustomValidator11" runat="server" ControlToValidate="txtResult" ErrorMessage="Please select result"
																			Enabled="False">*</asp:CustomValidator></STRONG></TD>
																<TD class="menubar" vAlign="top" width="95"></TD>
																<TD class="menubar" vAlign="top" width="320"><STRONG>Majors:</STRONG></TD>
															</TR>
															<TR>
																<TD vAlign="top" width="320">
																	<asp:DropDownList id="txtResult" runat="server" Width="100%" CssClass="textbox">
																		<asp:ListItem Value="0">Select--Results</asp:ListItem>
																		<asp:ListItem Value="A Grade">A Grade</asp:ListItem>
																		<asp:ListItem Value="B Grade">B Grade</asp:ListItem>
																		<asp:ListItem Value="C Grade">C Grade</asp:ListItem>
																		<asp:ListItem Value="D Grade">D Grade</asp:ListItem>
																		<asp:ListItem Value="F Grade">F Grade</asp:ListItem>
																		<asp:ListItem Value="2.1">2.1</asp:ListItem>
																		<asp:ListItem Value="2.2">2.2</asp:ListItem>
																		<asp:ListItem Value="2.3">2.3</asp:ListItem>
																		<asp:ListItem Value="2.4">2.4</asp:ListItem>
																		<asp:ListItem Value="2.5">2.5</asp:ListItem>
																		<asp:ListItem Value="2.6">2.6</asp:ListItem>
																		<asp:ListItem Value="2.7">2.7</asp:ListItem>
																		<asp:ListItem Value="2.8">2.8</asp:ListItem>
																		<asp:ListItem Value="2.9">2.9</asp:ListItem>
																		<asp:ListItem Value="3.0">3.0</asp:ListItem>
																		<asp:ListItem Value="3.1">3.1</asp:ListItem>
																		<asp:ListItem Value="3.2">3.2</asp:ListItem>
																		<asp:ListItem Value="3.3">3.3</asp:ListItem>
																		<asp:ListItem Value="3.4">3.4</asp:ListItem>
																		<asp:ListItem Value="3.5">3.5</asp:ListItem>
																		<asp:ListItem Value="3.6">3.6</asp:ListItem>
																		<asp:ListItem Value="3.7">3.7</asp:ListItem>
																		<asp:ListItem Value="3.8">3.8</asp:ListItem>
																		<asp:ListItem Value="3.9">3.9</asp:ListItem>
																		<asp:ListItem Value="4.0">4.0</asp:ListItem>
																		<asp:ListItem Value="Passed">Passed</asp:ListItem>
																		<asp:ListItem Value="Fail">Fail</asp:ListItem>
																		<asp:ListItem Value="1st Division">1st Division</asp:ListItem>
																		<asp:ListItem Value="2nd Division">2nd Division</asp:ListItem>
																		<asp:ListItem Value="3rd Division">3rd Division</asp:ListItem>
																		<asp:ListItem Value="In Progress">In Progress</asp:ListItem>
																	</asp:DropDownList></TD>
																<TD vAlign="top" width="95"></TD>
																<TD vAlign="top" width="320">
																	<asp:TextBox id="txtOtherMajors" runat="server" Visible="False" Width="100%" CssClass="textbox"
																		autocomploete="off"></asp:TextBox></TD>
															</TR>
															<TR>
																<TD class="menubar" vAlign="top" width="320"><STRONG>Duration From:
																		<asp:CustomValidator id="CustomValidator13" runat="server" ControlToValidate="DurationFrm" ErrorMessage="Please select duration from"
																			Enabled="False">*</asp:CustomValidator></STRONG></TD>
																<TD class="menubar" vAlign="top" width="95"></TD>
																<TD class="menubar" vAlign="top" width="320"><STRONG>Duration To:
																		<asp:CustomValidator id="CustomValidator14" runat="server" ControlToValidate="DurationTo" ErrorMessage="Please select duration to"
																			Enabled="False">*</asp:CustomValidator></STRONG></TD>
															</TR>
															<TR>
																<TD vAlign="top" width="320">
																	<asp:DropDownList id="DurationFrm" runat="server" Width="240px" CssClass="textbox">
																		<asp:ListItem Value="0">Select--Start Date</asp:ListItem>
																	</asp:DropDownList></TD>
																<TD vAlign="top" width="95"></TD>
																<TD vAlign="top" width="320">
																	<asp:DropDownList id="DurationTo" runat="server" Width="240px" CssClass="textbox">
																		<asp:ListItem Value="0">Select--End Date</asp:ListItem>
																	</asp:DropDownList></TD>
															</TR>
															<TR>
																<TD class="menubar" vAlign="top" colSpan="3"><STRONG>Distinction: </STRONG>
																</TD>
															</TR>
															<TR>
																<TD vAlign="top" colSpan="3">
																	<asp:TextBox id="txtAchievements" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"
																		TextMode="MultiLine" Rows="3"></asp:TextBox></TD>
															</TR>
															<TR>
																<TD vAlign="top" colSpan="3">
																	<asp:Button id="btnEdNew" runat="server" Text="Add New"></asp:Button>
																	<asp:Button id="btnEdUpdate" runat="server" Enabled="False" Text="Update"></asp:Button>
																	<asp:Button id="btnECancel" runat="server" Text="Cancel"></asp:Button></TD>
															</TR>
														</TABLE>
													</STRONG>
												</asp:panel><asp:panel id="Panel3" runat="server" Visible="False" Width="100%">&nbsp; 
                  <TABLE id="Table7" class="MainFormColor" border="0" cellSpacing="0" cellPadding="3" width="750"
														align="center">
														<TR>
															<TD class="OrangeFormTitle" colSpan="3">Employee's System Account:</TD>
														</TR>
														<TR>
															<TD class="menubar" vAlign="top" width="320"><STRONG>Employee Code:</STRONG></TD>
															<TD class="menubar" vAlign="top" width="95"></TD>
															<TD class="menubar" vAlign="top" width="320"><STRONG>Employee Name:</STRONG></TD>
														</TR>
														<TR>
															<TD vAlign="top" width="320">
																<asp:label id="lblPCode_2" runat="server"></asp:label></TD>
															<TD vAlign="top" width="95"></TD>
															<TD vAlign="top" width="320">
																<asp:label id="lblName_2" runat="server"></asp:label></TD>
														</TR>
														<TR>
															<TD class="menubar" vAlign="top" width="320"><STRONG>Designation:</STRONG></TD>
															<TD class="menubar" vAlign="top" width="95"></TD>
															<TD class="menubar" vAlign="top" width="320"><STRONG>Department:</STRONG></TD>
														</TR>
														<TR>
															<TD vAlign="top" width="320">
																<asp:label id="lblDesg_2" runat="server"></asp:label></TD>
															<TD vAlign="top" width="95"></TD>
															<TD vAlign="top" width="320">
																<asp:label id="lblDept_2" runat="server"></asp:label></TD>
														</TR>
														<TR>
															<TD class="menubar" vAlign="top" width="320"><STRONG>Official Email:</STRONG></TD>
															<TD class="menubar" vAlign="top" width="95"></TD>
															<TD class="menubar" vAlign="top" width="320"></TD>
														</TR>
														<TR>
															<TD vAlign="top" width="320">
																<asp:label id="lblEmailOfficial_2" runat="server"></asp:label></TD>
															<TD vAlign="top" width="95"></TD>
															<TD vAlign="top" width="320"></TD>
														</TR>
														<TR>
															<TD class="menubar" vAlign="top" width="320"><STRONG>Login ID:
																	<asp:requiredfieldvalidator id="Requiredfieldvalidator1" runat="server" ControlToValidate="txtUserID" ErrorMessage="* Required"
																		Display="Dynamic"></asp:requiredfieldvalidator>
																	<asp:linkbutton id="lbCheckAvailability" runat="server" CausesValidation="False">Check Availability</asp:linkbutton>
																	<asp:Label id="lblAvailable" runat="server"></asp:Label></STRONG></TD>
															<TD class="menubar" vAlign="top" width="95"></TD>
															<TD class="menubar" vAlign="top" width="320"><STRONG>
																	<asp:label id="lblADExist_2" runat="server" Visible="False"></asp:label></STRONG></TD>
														</TR>
														<TR>
															<TD vAlign="top" width="320">
																<asp:textbox id="txtUserID" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"></asp:textbox></TD>
															<TD vAlign="top" width="95"></TD>
															<TD vAlign="top" width="320"></TD>
														</TR>
														<TR>
															<TD vAlign="top" width="320">
																<asp:CheckBox id="chkActiveAccount" runat="server" Text="Active Account" Checked="True"></asp:CheckBox></TD>
															<TD vAlign="top" width="95"></TD>
															<TD vAlign="top" width="320"></TD>
														</TR>
														<TR>
															<TD vAlign="top" width="320">
																<asp:Button id="cmdCreate" runat="server" Text="Create Account"></asp:Button></TD>
															<TD vAlign="top" width="95">
																<asp:TextBox style="VISIBILITY: hidden" id="txtPassword" runat="server" Width="100%"></asp:TextBox></TD>
															<TD vAlign="top" width="320">
																<asp:TextBox style="VISIBILITY: hidden" id="txtEmpInfoEmail2" runat="server" autocomploete="off"></asp:TextBox></TD>
														</TR>
														<TR>
															<TD vAlign="top" colSpan="3">
																<asp:label id="lblError" runat="server" Font-Bold="True"></asp:label></TD>
														</TR>
														<TR>
															<TD vAlign="top" colSpan="3" align="center">
																<asp:label id="lblAccountMessage_2" runat="server" Font-Bold="True" ForeColor="DarkOrange"></asp:label></TD>
														</TR>
														<TR>
															<TD class="OrangeFormTitle" vAlign="top" colSpan="3">ROLES</TD>
														</TR>
														<TR>
															<TD vAlign="top" colSpan="3">
																<asp:datagrid id="dgRoles" runat="server" Width="100%" AutoGenerateColumns="False" GridLines="Horizontal"
																	CellPadding="3" BorderWidth="1px" BorderColor="Navy" BorderStyle="Solid" AllowSorting="True">
																	<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
																	<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
																	<AlternatingItemStyle CssClass="alternetitem"></AlternatingItemStyle>
																	<ItemStyle CssClass="item"></ItemStyle>
																	<HeaderStyle Font-Bold="True" ForeColor="Black" CssClass="header"></HeaderStyle>
																	<Columns>
																		<asp:BoundColumn Visible="False" DataField="RoleID" SortExpression="RoleID" HeaderText="RoleID"></asp:BoundColumn>
																		<asp:BoundColumn DataField="roleName" SortExpression="roleName" HeaderText="Role Name">
																			<ItemStyle HorizontalAlign="Left" VerticalAlign="Bottom"></ItemStyle>
																		</asp:BoundColumn>
																		<asp:TemplateColumn>
																			<ItemStyle HorizontalAlign="Center" Width="40px" VerticalAlign="Middle"></ItemStyle>
																			<ItemTemplate>
																				<asp:CheckBox id="chkSelectRole" runat="server"></asp:CheckBox>
																			</ItemTemplate>
																		</asp:TemplateColumn>
																	</Columns>
																	<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
																</asp:datagrid></TD>
														</TR>
														<TR>
															<TD vAlign="top" colSpan="3">
																<asp:Button id="cmdUpdate" runat="server" Text="Update Roles"></asp:Button></TD>
														</TR>
														<TR>
															<TD vAlign="top" colSpan="3" align="center">
																<asp:label id="lblRoleMessage_2" runat="server" Font-Bold="True" ForeColor="DarkOrange"></asp:label></TD>
														</TR>
													</TABLE></asp:panel><asp:panel id="pnlBondPaper" runat="server" Visible="False" Width="100%"><BR>
													<TABLE id="Table3" class="MainFormColor" border="0" cellSpacing="0" cellPadding="3" width="750"
														align="center">
														<TR>
															<TD class="OrangeFormTitle" colSpan="3">Employee's Bond Paper Information</TD>
														</TR>
														<TR>
															<TD class="menubar" vAlign="top" width="320">Signing Date:</TD>
															<TD class="menubar" vAlign="top" width="95"></TD>
															<TD class="menubar" vAlign="top" width="320">End Date:</TD>
														</TR>
														<TR>
															<TD vAlign="top" width="320">
																<ew:CalendarPopup id="SignDate" runat="server">
																	<TextboxLabelStyle CssClass="TextBox"></TextboxLabelStyle>
																	<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></WeekdayStyle>
																	<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="Yellow"></MonthHeaderStyle>
																	<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																		BackColor="AntiqueWhite"></OffMonthStyle>
																	<ButtonStyle CssClass="Button"></ButtonStyle>
																	<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></GoToTodayStyle>
																	<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="LightGoldenrodYellow"></TodayDayStyle>
																	<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="Orange"></DayHeaderStyle>
																	<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="LightGray"></WeekendStyle>
																	<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="Yellow"></SelectedDateStyle>
																	<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></ClearDateStyle>
																	<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></HolidayStyle>
																</ew:CalendarPopup></TD>
															<TD vAlign="top" width="95"></TD>
															<TD vAlign="top" width="320">
																<ew:CalendarPopup id="EndDate" runat="server">
																	<TextboxLabelStyle CssClass="TextBox"></TextboxLabelStyle>
																	<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></WeekdayStyle>
																	<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="Yellow"></MonthHeaderStyle>
																	<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																		BackColor="AntiqueWhite"></OffMonthStyle>
																	<ButtonStyle CssClass="Button"></ButtonStyle>
																	<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></GoToTodayStyle>
																	<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="LightGoldenrodYellow"></TodayDayStyle>
																	<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="Orange"></DayHeaderStyle>
																	<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="LightGray"></WeekendStyle>
																	<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="Yellow"></SelectedDateStyle>
																	<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></ClearDateStyle>
																	<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></HolidayStyle>
																</ew:CalendarPopup>
																<asp:CompareValidator id="CompareValidator3" runat="server" ControlToValidate="EndDate" ErrorMessage="End Date Must be greater than signing date"
																	Type="Date" ControlToCompare="EffectiveDate" Operator="GreaterThan">*</asp:CompareValidator></TD>
														</TR>
														<TR>
															<TD vAlign="top" width="320">
																<asp:Button id="btnInsertBond" runat="server" Width="56px" Text="Insert"></asp:Button></TD>
															<TD vAlign="top" width="95"></TD>
															<TD vAlign="top" width="320"></TD>
														</TR>
														<TR>
															<TD vAlign="top" colSpan="3">
																<asp:Label id="lblBond" runat="server"></asp:Label></TD>
														</TR>
													</TABLE>
												</asp:panel><asp:panel id="pnlFunctionalDesignation" runat="server" Visible="False" Width="100%"><BR>
													<TABLE id="Table5" class="MainFormColor" border="0" cellSpacing="0" cellPadding="3" width="750"
														align="center">
														<TR>
															<TD class="OrangeFormTitle" colSpan="3">Functional Designation</TD>
														</TR>
														<TR>
															<TD class="menubar" vAlign="top" width="320"><STRONG>Designation:</STRONG></TD>
															<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
															<TD class="menubar" vAlign="top" width="320"><STRONG>Employee Pcode:</STRONG></TD>
														</TR>
														<TR>
															<TD vAlign="top" width="320">
																<asp:Label id="lblEmpDesignation" runat="server" Font-Bold="True"></asp:Label></TD>
															<TD vAlign="top" width="95"></TD>
															<TD vAlign="top" width="320">
																<asp:Label id="lblFuncPcode" runat="server" Font-Bold="True"></asp:Label></TD>
														</TR>
														<TR>
															<TD class="menubar" vAlign="top" width="320"><STRONG>Functional Designation: </STRONG>
																<asp:CustomValidator id="CustomValidator12" runat="server" ControlToValidate="ddFunctionalDesignation"
																	ErrorMessage="Please select any Functional Designation" Enabled="False">*</asp:CustomValidator></TD>
															<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
															<TD class="menubar" vAlign="top" width="320"><STRONG>Total Vacancies:</STRONG></TD>
														</TR>
														<TR>
															<TD vAlign="top" width="320">
																<asp:DropDownList id="ddFunctionalDesignation" runat="server" Width="100%" CssClass="textbox" AutoPostBack="True"></asp:DropDownList></TD>
															<TD vAlign="top" width="95"></TD>
															<TD vAlign="top" width="320">
																<asp:Label id="lblFunctionalVacancy" runat="server" Font-Bold="True"></asp:Label></TD>
														</TR>
														<TR>
															<TD class="menubar" vAlign="top" width="320"><STRONG>Effective Date:</STRONG></TD>
															<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
															<TD class="menubar" vAlign="top" width="320"><STRONG></STRONG></TD>
														</TR>
														<TR>
															<TD vAlign="top" width="320">
																<ew:CalendarPopup id="EffectiveDate" runat="server" Width="190px">
																	<TextboxLabelStyle CssClass="TextBox"></TextboxLabelStyle>
																	<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></WeekdayStyle>
																	<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="Yellow"></MonthHeaderStyle>
																	<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																		BackColor="AntiqueWhite"></OffMonthStyle>
																	<ButtonStyle CssClass="Button"></ButtonStyle>
																	<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></GoToTodayStyle>
																	<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="LightGoldenrodYellow"></TodayDayStyle>
																	<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="Orange"></DayHeaderStyle>
																	<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="LightGray"></WeekendStyle>
																	<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="Yellow"></SelectedDateStyle>
																	<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></ClearDateStyle>
																	<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></HolidayStyle>
																</ew:CalendarPopup></TD>
															<TD vAlign="top" width="95"></TD>
															<TD vAlign="top" width="320"></TD>
														</TR>
														<TR>
															<TD vAlign="top" width="320">
																<asp:Button id="btnAddFunctional" runat="server" Width="56px" Text="Add"></asp:Button>
																<asp:Button id="btnFunctionalCancel" runat="server" CausesValidation="False" Text="Cancel"></asp:Button></TD>
															<TD vAlign="top" width="95"></TD>
															<TD vAlign="top" width="320"></TD>
														</TR>
														<TR>
															<TD vAlign="top" width="320">
																<asp:label id="lblFunMsg" runat="server" Visible="False" Font-Bold="True" ForeColor="DarkOrange"></asp:label></TD>
															<TD vAlign="top" width="95"></TD>
															<TD vAlign="top" width="320"></TD>
														</TR>
													</TABLE>
												</asp:panel><asp:panel style="Z-INDEX: 0" id="pnlCompensation" runat="server" Visible="False" Width="100%">
                  <TABLE id="Table10" class="MainFormColor" border="0" cellSpacing="0" cellPadding="3" width="750"
														align="center">
														<TR>
															<TD style="WIDTH: 319px" class="OrangeFormTitle" colSpan="3">Employee Compensation</TD>
														</TR>
														<TR>
															<TD style="WIDTH: 356px; HEIGHT: 18px" class="menubar"><STRONG>Mobile Entitlement:
																	<asp:RegularExpressionValidator id="RegularExpressionValidator10" runat="server" ControlToValidate="txtMobileEntitlement"
																		ErrorMessage="Numeric Values Only" ValidationExpression="^[0-9]*$">Numeric Values Only</asp:RegularExpressionValidator>
																	<asp:RequiredFieldValidator id="RequiredFieldValidator18" runat="server" ControlToValidate="txtMobileEntitlement"
																		ErrorMessage="Please enter mobile entitlement" Display="Dynamic"></asp:RequiredFieldValidator></STRONG></TD>
															<TD style="WIDTH: 94px; HEIGHT: 18px" class="menubar"></TD>
															<TD style="HEIGHT: 18px" class="menubar"><STRONG>Petrol:
																	<asp:RegularExpressionValidator id="RegularExpressionValidator11" runat="server" ControlToValidate="txtPetrolEntitlement"
																		ErrorMessage="Numeric Values Only" ValidationExpression="^[0-9]*$">Numeric Values Only</asp:RegularExpressionValidator>
																	<asp:RequiredFieldValidator id="RequiredFieldValidator19" runat="server" ControlToValidate="txtPetrolEntitlement"
																		ErrorMessage="Please enter petrol entitlement" Display="Dynamic"></asp:RequiredFieldValidator></STRONG></TD>
														</TR>
														<TR>
															<TD style="WIDTH: 356px; HEIGHT: 13px">
																<asp:TextBox id="txtMobileEntitlement" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"
																	MaxLength="1000">0</asp:TextBox></TD>
															<TD style="WIDTH: 94px; HEIGHT: 13px"></TD>
															<TD style="HEIGHT: 13px">
																<asp:TextBox id="txtPetrolEntitlement" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"
																	MaxLength="1000">0</asp:TextBox></TD>
														</TR>
														<TR>
															<TD style="WIDTH: 356px" class="menubar"><STRONG>Car/Conveyance:</STRONG></TD>
															<TD style="WIDTH: 94px" class="menubar"></TD>
															<TD class="menubar"><STRONG>Car/Conveyance Description:</STRONG></TD>
														</TR>
														<TR>
															<TD style="WIDTH: 356px">
																<asp:DropDownList id="ddCarConveyance" runat="server" Width="100%" CssClass="textbox" AutoPostBack="True">
																	<asp:ListItem Value="0">Select--Car/Conveyance</asp:ListItem>
																	<asp:ListItem Value="1">Company Maintained Car</asp:ListItem>
																	<asp:ListItem Value="2">Conveyance Allowance</asp:ListItem>
																	<asp:ListItem Value="3">Leased Car</asp:ListItem>
																	<asp:ListItem Value="4">Pick &amp; Drop</asp:ListItem>
																</asp:DropDownList></TD>
															<TD style="WIDTH: 94px"></TD>
															<TD>
																<asp:TextBox id="txtCarDescription" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"
																	MaxLength="1000" TextMode="MultiLine"></asp:TextBox></TD>
														</TR>
														<TR>
															<TD style="WIDTH: 356px" class="menubar"><STRONG>Gross Salary:
																	<asp:RegularExpressionValidator id="RegularExpressionValidator12" runat="server" ControlToValidate="txtGrossSal"
																		ErrorMessage="Numeric Values Only" Display="Dynamic" ValidationExpression="^[0-9]*$">Numeric Values Only</asp:RegularExpressionValidator>
																	<asp:RequiredFieldValidator id="RequiredFieldValidator10" runat="server" ControlToValidate="txtGrossSal" ErrorMessage="Please Enter Employee Salary"
																		Display="Dynamic">Please Enter Employee Salary</asp:RequiredFieldValidator></STRONG></TD>
															<TD style="WIDTH: 94px" class="menubar"></TD>
															<TD class="menubar"><STRONG>Basic Salary:
																	<asp:RegularExpressionValidator id="RegularExpressionValidator14" runat="server" ControlToValidate="txtBasicSal"
																		ErrorMessage="Numeric Values Only" ValidationExpression="^[0-9]*$">Numeric Values Only</asp:RegularExpressionValidator></STRONG></TD>
														</TR>
														<TR>
															<TD style="WIDTH: 356px">
																<asp:TextBox id="txtGrossSal" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"
																	MaxLength="1000" AutoPostBack="True">0</asp:TextBox></TD>
															<TD style="WIDTH: 94px"></TD>
															<TD>
																<asp:TextBox id="txtBasicSal" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"
																	MaxLength="1000" Enabled="False">0</asp:TextBox></TD>
														</TR>
														<TR>
															<TD style="WIDTH: 356px" class="menubar"><STRONG>House Rent:
																	<asp:RegularExpressionValidator id="RegularExpressionValidator13" runat="server" ControlToValidate="txtHouseRent"
																		ErrorMessage="Numeric Values Only" ValidationExpression="^[0-9]*$">Numeric Values Only</asp:RegularExpressionValidator></STRONG></TD>
															<TD style="WIDTH: 94px" class="menubar"></TD>
															<TD class="menubar"><STRONG>Utility:
																	<asp:RegularExpressionValidator id="RegularExpressionValidator15" runat="server" ControlToValidate="txtUtility"
																		ErrorMessage="Numeric Values Only" ValidationExpression="^[0-9]*$">Numeric Values Only</asp:RegularExpressionValidator></STRONG></TD>
														</TR>
														<TR>
															<TD style="WIDTH: 356px">
																<asp:TextBox id="txtHouseRent" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"
																	MaxLength="1000" Enabled="False">0</asp:TextBox></TD>
															<TD style="WIDTH: 94px"></TD>
															<TD>
																<asp:TextBox id="txtUtility" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"
																	MaxLength="1000" Enabled="False">0</asp:TextBox></TD>
														</TR>
														<TR>
															<TD style="WIDTH: 368px" class="menubar"><STRONG>Others:</STRONG></TD>
															<TD style="WIDTH: 94px" class="menubar"></TD>
															<TD class="menubar"></TD>
														</TR>
														<TR>
															<TD style="WIDTH: 368px">
																<asp:TextBox id="txtEmpInfoOthers" runat="server" Width="100%" CssClass="TextBox" autocomploete="off"
																	MaxLength="1000" TextMode="MultiLine"></asp:TextBox></TD>
															<TD style="WIDTH: 94px"></TD>
															<TD></TD>
														</TR>
														<TR>
															<TD style="WIDTH: 319px" colSpan="3">
																<asp:Button id="btnAddCompensation" runat="server" Width="56px" Text="Add"></asp:Button>
																<asp:Button id="btnCancelCompensation" runat="server" CausesValidation="False" Text="Cancel"></asp:Button></TD>
														</TR>
														<TR>
															<TD colSpan="3" align="center">
																<asp:label id="lblComMsg" runat="server" Visible="False" Font-Bold="True" ForeColor="DarkOrange"></asp:label></TD>
														</TR>
													</TABLE>&nbsp;</asp:panel>
												<asp:panel style="Z-INDEX: 0" id="pnlContract" runat="server" Visible="False" Width="100%">
													<TABLE id="Table15" class="MainFormColor" border="0" cellSpacing="0" cellPadding="3" width="750"
														align="center">
														<TR>
															<TD class="OrangeFormTitle" colSpan="5">Employee Contract</TD>
														</TR>
														<TR>
															<TD class="menubar" width="280"><STRONG>Start Date:
																	<asp:Label style="Z-INDEX: 0" id="lblLastExpiry" runat="server" Visible="False" Width="96px"></asp:Label>
																	<asp:Label style="Z-INDEX: 0" id="lblStatus" runat="server" Visible="False" Width="52px"></asp:Label></STRONG></TD>
															<TD class="menubar" width="280"><STRONG>Length of Duration:
																	<asp:Label style="Z-INDEX: 0" id="lblLastContractId" runat="server" Visible="False" Width="52px"></asp:Label></STRONG></TD>
															<TD class="menubar" width="280"><STRONG>Expiray Date:</STRONG></TD>
														</TR>
														<TR>
															<TD>
																<ew:CalendarPopup style="Z-INDEX: 0" id="cpContarctStart" runat="server" Width="160px" AutoPostBack="True"
																	AllowArbitraryText="False">
																	<TextboxLabelStyle CssClass="textbox"></TextboxLabelStyle>
																	<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></WeekdayStyle>
																	<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="Yellow"></MonthHeaderStyle>
																	<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																		BackColor="AntiqueWhite"></OffMonthStyle>
																	<ButtonStyle CssClass="button"></ButtonStyle>
																	<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></GoToTodayStyle>
																	<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="LightGoldenrodYellow"></TodayDayStyle>
																	<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="Orange"></DayHeaderStyle>
																	<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="LightGray"></WeekendStyle>
																	<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="Yellow"></SelectedDateStyle>
																	<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></ClearDateStyle>
																	<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></HolidayStyle>
																</ew:CalendarPopup></TD>
															<TD>
																<asp:DropDownList style="Z-INDEX: 0" id="ddDuration" runat="server" Width="215px" CssClass="textbox"
																	AutoPostBack="True">
																	<asp:ListItem Value="0">Select--Duration</asp:ListItem>
																</asp:DropDownList></TD>
															<TD>
																<ew:CalendarPopup style="Z-INDEX: 0" id="cpContractExpiry" runat="server" Width="160px" Nullable="True"
																	ShowClearDate="True">
																	<TextboxLabelStyle CssClass="textbox"></TextboxLabelStyle>
																	<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></WeekdayStyle>
																	<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="Yellow"></MonthHeaderStyle>
																	<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																		BackColor="AntiqueWhite"></OffMonthStyle>
																	<ButtonStyle CssClass="button"></ButtonStyle>
																	<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></GoToTodayStyle>
																	<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="LightGoldenrodYellow"></TodayDayStyle>
																	<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="Orange"></DayHeaderStyle>
																	<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="LightGray"></WeekendStyle>
																	<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="Yellow"></SelectedDateStyle>
																	<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></ClearDateStyle>
																	<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></HolidayStyle>
																</ew:CalendarPopup></TD>
														</TR>
														<TR>
															<TD class="menubar" colSpan="3"><STRONG>Remarks:</STRONG></TD>
														</TR>
														<TR>
															<TD colSpan="3">
																<asp:TextBox style="Z-INDEX: 0" id="txtCRemarks" runat="server" Width="100%" CssClass="TextBox"
																	MaxLength="1000" TextMode="MultiLine"></asp:TextBox></TD>
														</TR>
														<TR>
															<TD id="tdContract" colSpan="3" runat="server"><STRONG>Serving Status:<BR>
																	<asp:DropDownList style="Z-INDEX: 0" id="ddServingStatus" runat="server" Width="182px" CssClass="textbox"></asp:DropDownList></STRONG></TD>
														</TR>
														<TR>
															<TD colSpan="3">
																<asp:Button style="Z-INDEX: 0" id="btnCNew" runat="server" Width="56px" Enabled="False" Text="New"></asp:Button>
																<asp:Button style="Z-INDEX: 0" id="btnCRenew" runat="server" Width="56px" Enabled="False" Text="Renew"></asp:Button><BR>
																<asp:Panel style="Z-INDEX: 0" id="pnlNotification" runat="server" Visible="False" Height="60px"
																	BorderWidth="1px" BorderColor="#FFC0C0" BorderStyle="Solid">
																	<STRONG><FONT style="Z-INDEX: 0" color="#ff3300" size="2"><FONT size="3">Warning!<BR>
																			</FONT>
																			<asp:Label style="Z-INDEX: 0" id="lblWarning" runat="server"></asp:Label><BR>
																			<asp:Button style="Z-INDEX: 0" id="btnOK" runat="server" Enabled="False" Text="OK"></asp:Button>
																			<asp:Button style="Z-INDEX: 0" id="btnNotifyCancel" runat="server" Text="Cancel"></asp:Button></FONT></STRONG></asp:Panel></TD>
														</TR>
														<TR>
															<TD class="OrangeFormTitle" colSpan="3">Contract History</TD>
														</TR>
														<TR>
															<TD colSpan="3">
																<asp:DataGrid style="Z-INDEX: 0" id="dgContractHistory" runat="server" Width="100%" AutoGenerateColumns="False"
																	GridLines="Horizontal" CellPadding="3" BackColor="White" BorderWidth="1px" BorderColor="Navy"
																	BorderStyle="Solid">
																	<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
																	<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
																	<AlternatingItemStyle CssClass="AlternetItem"></AlternatingItemStyle>
																	<ItemStyle ForeColor="#4A3C8C" CssClass="Item"></ItemStyle>
																	<HeaderStyle Font-Bold="True" ForeColor="#F7F7F7" CssClass="header"></HeaderStyle>
																	<Columns>
																		<asp:BoundColumn Visible="False" DataField="c_Id"></asp:BoundColumn>
																		<asp:BoundColumn DataField="c_Start" HeaderText="Start (date)"></asp:BoundColumn>
																		<asp:BoundColumn DataField="c_Expiry" HeaderText="Expiry (date)"></asp:BoundColumn>
																		<asp:BoundColumn DataField="Duration" HeaderText="Duration (in Years)"></asp:BoundColumn>
																		<asp:BoundColumn DataField="Status" HeaderText="Status"></asp:BoundColumn>
																		<asp:BoundColumn DataField="ServingStaus" HeaderText="Serving Status"></asp:BoundColumn>
																		<asp:BoundColumn DataField="c_Remarks" HeaderText="Remarks"></asp:BoundColumn>
																		<asp:BoundColumn DataField="LastModify" HeaderText="Last Modified"></asp:BoundColumn>
																		<asp:BoundColumn DataField="LastModifyBy" HeaderText="Last Modified By"></asp:BoundColumn>
																		<asp:BoundColumn Visible="False" DataField="DurationID"></asp:BoundColumn>
																		<asp:BoundColumn Visible="False" DataField="StatusID"></asp:BoundColumn>
																		<asp:BoundColumn Visible="False" DataField="ServingID"></asp:BoundColumn>
																		<asp:TemplateColumn>
																			<ItemTemplate>
																				<asp:LinkButton id="lnkDisable" runat="server">De-Active</asp:LinkButton>
																			</ItemTemplate>
																		</asp:TemplateColumn>
																	</Columns>
																	<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
																</asp:DataGrid></TD>
														</TR>
													</TABLE>
												</asp:panel><BR>
												<asp:validationsummary id="ValidationSummary1" runat="server" Height="28px" Font-Bold="True" ShowMessageBox="True"></asp:validationsummary><asp:textbox style="VISIBILITY: hidden" id="txtX" runat="server" Width="40px"></asp:textbox><asp:textbox style="VISIBILITY: hidden" id="txtY" runat="server" Width="32px"></asp:textbox></td>
										</tr>
									</table>
								</TD>
							</TR>
						</TABLE>
					</TD>
				</TR>
				<TR>
					<TD height="20" vAlign="middle" align="center">Copyright © 2005 Independent Media 
						Corporation <A href="http://www.geo.tv">www.geo.tv</A></TD>
				</TR>
			</TABLE>
		</form>
	</body>
</HTML>
