<%@ Register TagPrefix="uc1" TagName="myMenus" Src="myMenus.ascx" %>
<%@ Register TagPrefix="ew" Namespace="eWorld.UI" Assembly="eWorld.UI" %>
<%@ Page CodeBehind="Department.aspx.cs" Language="c#" AutoEventWireup="false" Inherits="GeoRabtaSite.GeoDepartment" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Raabata Admin :: Departments</title>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="RaabtaAdmin.css" type="text/css" rel="stylesheet">
		<LINK href="../Styles4.css" type="text/css" rel="stylesheet">
		<script language="javascript" id="clientEventHandlersJS">
<!--

function window_onload() {
	t=document.getElementById('txtName');
	if(t)
		t.focus();
}

//-->
		</script>
	</HEAD>
	<body language="javascript" dir="ltr" bottomMargin="0" leftMargin="0" topMargin="0" onload="return window_onload()"
		rightMargin="0">
		<form id="myForm" name="MyForm" runat="server">
			<table height="100%" cellSpacing="0" cellPadding="0" width="780" align="center" bgColor="#ffffff"
				border="0">
				<tr>
					<td vAlign="middle" align="left" height="69">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="69" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="1826">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top1.swf">
							<PARAM NAME="Src" VALUE="flash/Top1.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<embed src="flash/Top1.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="69"> </embed>
						</OBJECT>
					</td>
				</tr>
				<tr>
					<td height="10">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="10" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="265">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top2.swf">
							<PARAM NAME="Src" VALUE="flash/Top2.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<embed src="flash/Top2.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="10"> </embed>
						</OBJECT>
					</td>
				</tr>
				<TR>
					<TD class="PageTitle" height="20">Geo Raabata Admin :: Departments</TD>
				</TR>
				<tr>
					<td class="MenuBar" height="20"><uc1:mymenus id="MyMenus1" runat="server"></uc1:mymenus></td>
				</tr>
				<tr>
					<td class="MainBG" vAlign="top"><BR>
						<TABLE class="MainFormColor" id="tabNewForm" cellSpacing="0" cellPadding="3" width="750"
							align="center" border="0" runat="server">
							<TR>
								<TD class="OrangeFormTitle" colSpan="3">Department Information Form</TD>
							</TR>
							<TR>
								<TD class="MenuBar" width="320"><asp:label id="Label2" runat="server" Font-Bold="True">Department Name</asp:label><asp:requiredfieldvalidator id="RequiredFieldValidator1" runat="server" ControlToValidate="txtName" Display="Dynamic"
										ErrorMessage="* Required"></asp:requiredfieldvalidator><asp:label id="txtDeptId" runat="server" Visible="False"></asp:label></TD>
								<TD class="MenuBar" width="60"></TD>
								<TD class="MenuBar" width="320"><asp:label id="Label4" runat="server" Font-Bold="True">Strategic Business Unit</asp:label><asp:requiredfieldvalidator id="RequiredFieldValidator2" runat="server" ControlToValidate="txtBusinessUnit"
										Display="Dynamic" ErrorMessage="* Required"></asp:requiredfieldvalidator></TD>
							</TR>
							<TR>
								<TD width="320"><asp:textbox id="txtName" tabIndex="1" runat="server" CssClass="textbox" Width="100%"></asp:textbox></TD>
								<TD width="60"></TD>
								<TD width="320"><asp:dropdownlist id="txtBusinessUnit" runat="server" CssClass="textbox" Width="100%"></asp:dropdownlist></TD>
							</TR>
							<TR>
								<TD class="MenuBar" id="tdError" colSpan="3" runat="server"><asp:label id="lblError" runat="server" CssClass="ErrorLabel"></asp:label></TD>
							</TR>
							<TR>
								<TD colSpan="3"><asp:button id="btnQueryButton" tabIndex="4" runat="server" Width="110px" CausesValidation="False"
										Text="Save"></asp:button><asp:button id="cmdDelete" runat="server" Width="110px" Text="Delete"></asp:button><asp:button id="btnCancel" tabIndex="5" runat="server" Width="110px" CausesValidation="False"
										Text="Cancel"></asp:button></TD>
							</TR>
						</TABLE>
						<BR>
						<TABLE id="Table9" cellSpacing="0" cellPadding="3" width="750" align="center" border="0">
							<TR>
								<TD><asp:linkbutton id="LinkButton1" runat="server">Add New Department</asp:linkbutton></TD>
							</TR>
							<TR>
								<TD><asp:datagrid id="DataGrid1" runat="server" Width="100%" DataKeyField="deptId" AutoGenerateColumns="False"
										AllowSorting="True" GridLines="Horizontal" BorderColor="Navy" BorderWidth="1px" BackColor="White"
										CellPadding="3" BorderStyle="Solid" DESIGNTIMEDRAGDROP="735" PageSize="5">
										<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
										<AlternatingItemStyle CssClass="AlternetItem"></AlternatingItemStyle>
										<ItemStyle ForeColor="#4A3C8C" CssClass="Item"></ItemStyle>
										<HeaderStyle Font-Bold="True" ForeColor="#F7F7F7" CssClass="header"></HeaderStyle>
										<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
										<Columns>
											<asp:BoundColumn Visible="False" DataField="DeptId" SortExpression="DeptId" HeaderText="Dept ID"></asp:BoundColumn>
											<asp:BoundColumn DataField="DeptName" SortExpression="DeptName" HeaderText="Department Name"></asp:BoundColumn>
											<asp:BoundColumn DataField="SBU" SortExpression="SBU" HeaderText="SBU"></asp:BoundColumn>
											<asp:BoundColumn Visible="False" DataField="Division" SortExpression="Division" HeaderText="Division"></asp:BoundColumn>
											<asp:EditCommandColumn ButtonType="LinkButton" UpdateText="Update" CancelText="Cancel" EditText="Detail">
												<ItemStyle HorizontalAlign="Center" Width="80px" VerticalAlign="Middle"></ItemStyle>
											</asp:EditCommandColumn>
										</Columns>
										<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
									</asp:datagrid></TD>
							</TR>
							<TR>
								<TD><asp:panel id="pnlBUManager" runat="server">
										<TABLE id="Table4" cellSpacing="0" cellPadding="3" width="100%" bgColor="aliceblue" border="0"
											runat="server">
											<TR>
												<TD class="OrangeFormTitle" colSpan="3"><STRONG>Business Unit Managers:</STRONG></TD>
											</TR>
											<TR>
												<TD colSpan="3">
													<asp:DataGrid id="DataGrid2" runat="server" Width="100%" BorderStyle="Solid" CellPadding="3" BackColor="White"
														BorderWidth="1px" BorderColor="Navy" GridLines="Horizontal" AllowSorting="True" AutoGenerateColumns="False"
														DataKeyField="bumangerid">
														<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
														<AlternatingItemStyle CssClass="AlternetItem"></AlternatingItemStyle>
														<ItemStyle ForeColor="#4A3C8C" CssClass="Item"></ItemStyle>
														<HeaderStyle Font-Bold="True" ForeColor="#F7F7F7" CssClass="header"></HeaderStyle>
														<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
														<Columns>
															<asp:BoundColumn DataField="Name" SortExpression="Name" HeaderText="Name"></asp:BoundColumn>
															<asp:BoundColumn DataField="DateFrom" SortExpression="datefrom" HeaderText="From" DataFormatString="{0:d}"></asp:BoundColumn>
															<asp:BoundColumn DataField="dateto" SortExpression="dateto" HeaderText="To" DataFormatString="{0:d}"></asp:BoundColumn>
															<asp:BoundColumn DataField="IsActive" SortExpression="IsActive" HeaderText="Is Active"></asp:BoundColumn>
															<asp:EditCommandColumn ButtonType="LinkButton" UpdateText="Update" CancelText="Cancel" EditText="Edit"></asp:EditCommandColumn>
															<asp:BoundColumn Visible="False" DataField="bumangerid" HeaderText="bumangerid"></asp:BoundColumn>
														</Columns>
														<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
													</asp:DataGrid>
													<asp:Label id="Label5" runat="server" Visible="False">Record Not Found</asp:Label></TD>
											</TR>
											<TR>
												<TD width="350" colSpan="3">
													<asp:Button id="btnChange" runat="server" Width="180px" Text="Change HR BU Manager"></asp:Button></TD>
											</TR>
											<TR>
												<TD colSpan="3">
													<asp:Panel id="pnlBUManagerForm" runat="server">
														<P>
															<TABLE class="MainFormColor" id="Table2" cellSpacing="0" cellPadding="3" width="100%" border="0">
																<TR>
																	<TD class="OrangeFormTitle" width="350" colSpan="3">BU Manger Information:</TD>
																</TR>
																<TR>
																	<TD class="MenuBar" width="350"><STRONG>BU Manager:</STRONG></TD>
																	<TD class="MenuBar" width="100"></TD>
																	<TD class="MenuBar" width="350"></TD>
																</TR>
																<TR>
																	<TD width="350">
																		<asp:DropDownList id="ddlBUManager" runat="server" Width="100%"></asp:DropDownList></TD>
																	<TD width="100"></TD>
																	<TD width="350">
																		<asp:Label id="lblbumangerid" runat="server" Visible="False"></asp:Label></TD>
																</TR>
																<TR>
																	<TD class="MenuBar" width="350"><STRONG>From:</STRONG></TD>
																	<TD class="MenuBar" width="100"></TD>
																	<TD class="MenuBar" width="350">
																		<asp:CheckBox id="CheckBox1" runat="server" Text="To" AutoPostBack="True"></asp:CheckBox></TD>
																</TR>
																<TR>
																	<TD width="350"><WEEKDAYSTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small">
																			<ew:CalendarPopup id="dpFrom" runat="server">
																				<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></WeekdayStyle>
																				<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="Yellow"></MonthHeaderStyle>
																				<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																					BackColor="AntiqueWhite"></OffMonthStyle>
																				<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></GoToTodayStyle>
																				<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="LightGoldenrodYellow"></TodayDayStyle>
																				<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="Orange"></DayHeaderStyle>
																				<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="LightGray"></WeekendStyle>
																				<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="Yellow"></SelectedDateStyle>
																				<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></ClearDateStyle>
																				<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></HolidayStyle>
																			</ew:CalendarPopup>
																		</WEEKDAYSTYLE><MONTHHEADERSTYLE BackColor="Yellow" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></MONTHHEADERSTYLE><OFFMONTHSTYLE BackColor="AntiqueWhite" ForeColor="Gray" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></OFFMONTHSTYLE><GOTOTODAYSTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></GOTOTODAYSTYLE><TODAYDAYSTYLE BackColor="LightGoldenrodYellow" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></TODAYDAYSTYLE><DAYHEADERSTYLE BackColor="Orange" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></DAYHEADERSTYLE><WEEKENDSTYLE BackColor="LightGray" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></WEEKENDSTYLE><SELECTEDDATESTYLE BackColor="Yellow" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></SELECTEDDATESTYLE><CLEARDATESTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></CLEARDATESTYLE><HOLIDAYSTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></HOLIDAYSTYLE></TD>
																	<TD width="100"></TD>
																	<TD width="350"><WEEKDAYSTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small">
																			<ew:CalendarPopup id="dpTo" runat="server" Visible="False">
																				<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></WeekdayStyle>
																				<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="Yellow"></MonthHeaderStyle>
																				<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																					BackColor="AntiqueWhite"></OffMonthStyle>
																				<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></GoToTodayStyle>
																				<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="LightGoldenrodYellow"></TodayDayStyle>
																				<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="Orange"></DayHeaderStyle>
																				<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="LightGray"></WeekendStyle>
																				<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="Yellow"></SelectedDateStyle>
																				<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></ClearDateStyle>
																				<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></HolidayStyle>
																			</ew:CalendarPopup>
																		</WEEKDAYSTYLE><MONTHHEADERSTYLE BackColor="Yellow" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></MONTHHEADERSTYLE><OFFMONTHSTYLE BackColor="AntiqueWhite" ForeColor="Gray" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></OFFMONTHSTYLE><GOTOTODAYSTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></GOTOTODAYSTYLE><TODAYDAYSTYLE BackColor="LightGoldenrodYellow" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></TODAYDAYSTYLE><DAYHEADERSTYLE BackColor="Orange" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></DAYHEADERSTYLE><WEEKENDSTYLE BackColor="LightGray" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></WEEKENDSTYLE><SELECTEDDATESTYLE BackColor="Yellow" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></SELECTEDDATESTYLE><CLEARDATESTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></CLEARDATESTYLE><HOLIDAYSTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></HOLIDAYSTYLE></TD>
																</TR>
																<TR>
																	<TD colSpan="3">
																		<asp:Button id="cmdSave" runat="server" Width="110px" Text="Save"></asp:Button>
																		<asp:Button id="cmdCancel" runat="server" Width="110px" Text="Cancel"></asp:Button></TD>
																</TR>
															</TABLE>
														</P>
													</asp:Panel></TD>
											</TR>
										</TABLE>
									</asp:panel></TD>
							</TR>
							<TR>
								<TD><asp:panel id="pnlHOD" runat="server">
										<TABLE id="Table5" cellSpacing="0" cellPadding="3" width="100%" bgColor="aliceblue" border="0"
											runat="server">
											<TR>
												<TD class="OrangeFormTitle" colSpan="3"><STRONG>HOD</STRONG></TD>
											</TR>
											<TR>
												<TD colSpan="3">
													<asp:DataGrid id="DataGrid5" runat="server" Width="100%" BorderStyle="Solid" CellPadding="3" BackColor="White"
														BorderWidth="1px" BorderColor="Navy" GridLines="Horizontal" AllowSorting="True" AutoGenerateColumns="False"
														DataKeyField="h_id">
														<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
														<AlternatingItemStyle CssClass="AlternetItem"></AlternatingItemStyle>
														<ItemStyle ForeColor="#4A3C8C" CssClass="Item"></ItemStyle>
														<HeaderStyle Font-Bold="True" ForeColor="#F7F7F7" CssClass="header"></HeaderStyle>
														<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
														<Columns>
															<asp:BoundColumn DataField="Name" SortExpression="Name" HeaderText="Name"></asp:BoundColumn>
															<asp:BoundColumn DataField="DateFrom" SortExpression="datefrom" HeaderText="From" DataFormatString="{0:d}"></asp:BoundColumn>
															<asp:BoundColumn DataField="dateto" SortExpression="dateto" HeaderText="To" DataFormatString="{0:d}"></asp:BoundColumn>
															<asp:BoundColumn DataField="IsActive" SortExpression="IsActive" HeaderText="Is Active"></asp:BoundColumn>
															<asp:EditCommandColumn ButtonType="LinkButton" UpdateText="Update" CancelText="Cancel" EditText="Edit"></asp:EditCommandColumn>
															<asp:BoundColumn Visible="False" DataField="h_id" SortExpression="h_id" HeaderText="h_id"></asp:BoundColumn>
														</Columns>
														<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
													</asp:DataGrid>
													<asp:Label id="Label8" runat="server" Visible="False">Record Not Found</asp:Label></TD>
											</TR>
											<TR>
												<TD width="350" colSpan="3">
													<asp:Button id="cmdChangeHOD" runat="server" Width="180px" Text="Change HOD"></asp:Button></TD>
											</TR>
											<TR>
												<TD colSpan="3">
													<asp:Panel id="pnlHODForm" runat="server" DESIGNTIMEDRAGDROP="167">
														<P>
															<TABLE class="MainFormColor" id="Table7" cellSpacing="0" cellPadding="3" width="100%" border="0">
																<TR>
																	<TD class="OrangeFormTitle" width="350" colSpan="3">HOD Information:</TD>
																</TR>
																<TR>
																	<TD class="MenuBar" width="350"><STRONG>Head Of Department:</STRONG></TD>
																	<TD class="MenuBar" width="100"></TD>
																	<TD class="MenuBar" width="350">
																		<asp:Label id="lblHODID" runat="server" Visible="False"></asp:Label></TD>
																</TR>
																<TR>
																	<TD width="350">
																		<asp:DropDownList id="ddlHOD2" runat="server" Width="100%" AutoPostBack="True"></asp:DropDownList></TD>
																	<TD width="100"></TD>
																	<TD width="350"></TD>
																</TR>
																<TR>
																	<TD class="MenuBar" width="350"><STRONG>From:</STRONG></TD>
																	<TD class="MenuBar" width="100"></TD>
																	<TD class="MenuBar" width="350">
																		<asp:CheckBox id="cbTo2" runat="server" Text="To" AutoPostBack="True"></asp:CheckBox></TD>
																</TR>
																<TR>
																	<TD width="350"><WEEKDAYSTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small">
																			<ew:CalendarPopup id="dpFrom2" runat="server">
																				<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></WeekdayStyle>
																				<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="Yellow"></MonthHeaderStyle>
																				<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																					BackColor="AntiqueWhite"></OffMonthStyle>
																				<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></GoToTodayStyle>
																				<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="LightGoldenrodYellow"></TodayDayStyle>
																				<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="Orange"></DayHeaderStyle>
																				<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="LightGray"></WeekendStyle>
																				<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="Yellow"></SelectedDateStyle>
																				<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></ClearDateStyle>
																				<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></HolidayStyle>
																			</ew:CalendarPopup>
																		</WEEKDAYSTYLE><MONTHHEADERSTYLE BackColor="Yellow" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></MONTHHEADERSTYLE><OFFMONTHSTYLE BackColor="AntiqueWhite" ForeColor="Gray" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></OFFMONTHSTYLE><GOTOTODAYSTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></GOTOTODAYSTYLE><TODAYDAYSTYLE BackColor="LightGoldenrodYellow" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></TODAYDAYSTYLE><DAYHEADERSTYLE BackColor="Orange" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></DAYHEADERSTYLE><WEEKENDSTYLE BackColor="LightGray" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></WEEKENDSTYLE><SELECTEDDATESTYLE BackColor="Yellow" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></SELECTEDDATESTYLE><CLEARDATESTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></CLEARDATESTYLE><HOLIDAYSTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></HOLIDAYSTYLE></TD>
																	<TD width="100"></TD>
																	<TD width="350"><WEEKDAYSTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small">
																			<ew:CalendarPopup id="dpTo2" runat="server" Visible="False">
																				<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></WeekdayStyle>
																				<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="Yellow"></MonthHeaderStyle>
																				<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																					BackColor="AntiqueWhite"></OffMonthStyle>
																				<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></GoToTodayStyle>
																				<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="LightGoldenrodYellow"></TodayDayStyle>
																				<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="Orange"></DayHeaderStyle>
																				<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="LightGray"></WeekendStyle>
																				<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="Yellow"></SelectedDateStyle>
																				<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></ClearDateStyle>
																				<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																					BackColor="White"></HolidayStyle>
																			</ew:CalendarPopup>
																		</WEEKDAYSTYLE><MONTHHEADERSTYLE BackColor="Yellow" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></MONTHHEADERSTYLE><OFFMONTHSTYLE BackColor="AntiqueWhite" ForeColor="Gray" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></OFFMONTHSTYLE><GOTOTODAYSTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></GOTOTODAYSTYLE><TODAYDAYSTYLE BackColor="LightGoldenrodYellow" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></TODAYDAYSTYLE><DAYHEADERSTYLE BackColor="Orange" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></DAYHEADERSTYLE><WEEKENDSTYLE BackColor="LightGray" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></WEEKENDSTYLE><SELECTEDDATESTYLE BackColor="Yellow" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></SELECTEDDATESTYLE><CLEARDATESTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></CLEARDATESTYLE><HOLIDAYSTYLE BackColor="White" ForeColor="Black" Font-Names="Verdana,Helvetica,Tahoma,Arial"
																			Font-Size="XX-Small"></HOLIDAYSTYLE></TD>
																</TR>
																<TR>
																	<TD colSpan="3">
																		<asp:Button id="Button5" runat="server" Width="110px" Text="Save"></asp:Button>
																		<asp:Button id="Button4" runat="server" Width="110px" Text="Cancel"></asp:Button></TD>
																</TR>
															</TABLE>
														</P>
													</asp:Panel></TD>
											</TR>
										</TABLE>
									</asp:panel></TD>
							</TR>
						</TABLE>
						</FONT></STRONG></td>
				</tr>
				<tr>
					<td vAlign="middle" align="center" height="20">Copyright &copy; 2005 Independent Media 
						Corporation <A href="http://www.geo.tv">www.geo.tv</A></td>
				</tr>
			</table>
		</form>
	</body>
</HTML>
