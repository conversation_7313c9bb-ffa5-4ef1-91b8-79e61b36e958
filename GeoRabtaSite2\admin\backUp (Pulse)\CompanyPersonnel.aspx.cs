using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;
namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for CompanyPersonnelProfile.
	/// </summary>
	public class EmployeeTrackingSystems : System.Web.UI.Page
	{
		SqlConnection con;
		DataSet ds=new DataSet();
		//public static int status=0;
		//public static string sql="";
		public static string sortVar="";
		const int WebFormID=23;
		const int ProjectID=2;
		protected System.Web.UI.WebControls.Button btnSpecial;
		protected MagicAjax.UI.Controls.AjaxPanel AjaxPanel1;
		protected System.Web.UI.WebControls.DropDownList ddLocation;
		protected System.Web.UI.WebControls.TextBox txtAdName;
		protected System.Web.UI.WebControls.DropDownList ddDepartment;
		protected System.Web.UI.WebControls.DropDownList ddDesignation;
		protected System.Web.UI.WebControls.DropDownList ddFunctionalDesignation;
		protected System.Web.UI.WebControls.TextBox TextBox1;
		protected System.Web.UI.WebControls.Panel Panel7;
		protected System.Web.UI.WebControls.DropDownList ddAge;
		protected System.Web.UI.WebControls.Label Label6;
		protected System.Web.UI.WebControls.DropDownList ddBloodGrp;
		protected System.Web.UI.WebControls.Label Label7;
		protected System.Web.UI.WebControls.DropDownList ddDOJ;
		protected System.Web.UI.WebControls.Label Label8;
		protected System.Web.UI.WebControls.DropDownList ddCategory;
		protected System.Web.UI.WebControls.Label Label9;
		protected System.Web.UI.WebControls.DropDownList ddGender;
		protected System.Web.UI.WebControls.DropDownList ddReportingStation;
		protected System.Web.UI.WebControls.TextBox txtPcode;
		static string userId="";

		private bool IsPageAccessAllowed()
		{
				
			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception ex)
			{
				string s=ex.Message;
				Response.Redirect("../Login.aspx");
			}
		
			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(ProjectID,32,userId,"Edit")==true || GeoSecurity.isControlVisible(ProjectID,32,userId,"Family")==true || GeoSecurity.isControlVisible(ProjectID,32,userId,"EducationalInfo")==true || GeoSecurity.isControlVisible(ProjectID,33,userId,"ViewAll")==true ) 
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("../Login.aspx");
				return false;
			}
		}
		private void Page_Load(object sender, System.EventArgs e)
		{
			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			if(IsPageAccessAllowed())
			{
				
				if(GeoSecurity.isControlVisible(ProjectID,32,userId,"SpecialCriteria")==false)
				{
					this.btnSpecial.Enabled=false;
				}
			}
			else
			{
				Response.Redirect("ErrorPage.aspx");
			}
			con=new SqlConnection(Connection.ConnectionString);
		    //btnSpecial.Attributes["ajaxcall"] = "async";  
			if(!IsPostBack)
			{
				this.ddDepartment.Attributes["ajaxcall"]= "async";
				this.TextBox1.Text="0";
				getDesignation();
				getAll();
				getCategory();
				txtPcode.Attributes.Add("onkeyup","getValues();");
				ddLocation.Attributes.Add("onchange","getValues();");
				txtAdName.Attributes.Add("onkeyup","getValues();");
				ddDepartment.Attributes.Add("onchange","getValues();");
				ddDesignation.Attributes.Add("onchange","getValues();");
				ddFunctionalDesignation.Attributes.Add("onchange","getValues();");
                  
				ddAge.Attributes.Add("onchange","getValues();");
				ddBloodGrp.Attributes.Add("onchange","getValues();");
				ddDOJ.Attributes.Add("onclick","getValues();");
				ddCategory.Attributes.Add("onchange","getValues();");
				ddGender.Attributes.Add("onchange","getValues();");
				ddReportingStation.Attributes.Add("onchange","getValues();");	 
				
			}
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}

		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.ddDepartment.SelectedIndexChanged += new System.EventHandler(this.ddDepartment_SelectedIndexChanged);
			this.ddDesignation.SelectedIndexChanged += new System.EventHandler(this.ddDesignation_SelectedIndexChanged);
			this.btnSpecial.Click += new System.EventHandler(this.btnSpecial_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion


		public void getAll()
		{

			//this.Panel2.Visible=true;
			con.Open();
			//string query="select e.pcode, e.name from t_employee e,t_department d,t_designation des where d.deptid=des.deptid And e.desigid=des.desigid and des.deptid =any(select deptid from t_employee e2,t_designation d2 where e2.pcode='"+Session["user_id"].ToString()+"' And e2.desigid=d2.desigid)";
			//string query2="select distinct(des.desigid),des.designation from t_employee e,t_department d,t_designation des where d.deptid=des.deptid And e.desigid=des.desigid and des.deptid =any(select deptid from t_employee e2,t_designation d2 where e2.pcode='"+Session["user_id"].ToString()+"' And e2.desigid=d2.desigid)";
			
			//string query2="select distinct(des.desigid),des.designation from t_designation des where des.status=1 order by des.designation";
			string query2="select des.designation,des.designation from t_designation des where des.status=1 group by des.designation order by des.designation";
			SqlCommand cmd2=new SqlCommand(query2,con);
			SqlDataReader rd2=cmd2.ExecuteReader();
			int j=1;
			while(rd2.Read())
			{
				ListItem itm2=new ListItem();
				itm2.Value=rd2[0].ToString();
				itm2.Text=rd2[1].ToString();
				this.ddDesignation.Items.Insert(j,itm2);
				j++;
			}
			rd2.Close();

			string getCity="select cityid,cityname from t_City where status=1 order by cityname";
			SqlCommand cCity=new SqlCommand(getCity,con);
			SqlDataReader rCity=cCity.ExecuteReader();
			int b=1;	
			while(rCity.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rCity[0].ToString();
				itm.Text=rCity[1].ToString();
				this.ddLocation.Items.Insert(b,itm);
				this.ddReportingStation.Items.Add(itm);
				b++; 
			}
			rCity.Close();
			con.Close();
		}

		private void ddDesignation_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			con.Open();
			this.ddFunctionalDesignation.Items.Clear();
			ListItem item=new ListItem();
			item.Value="0";
			item.Text="Select--Functional Designation";
			this.ddFunctionalDesignation.Items.Add(item);
			string getRecords="";
			if(this.ddDepartment.SelectedValue=="0")
			{
				getRecords="select f.fdesigid,f.functionaltitle from t_functionaldesignation f,t_designation d where f.isactive=1 And d.status=1 And f.functionaldesignation=d.desigid And d.designation='"+ this.ddDesignation.SelectedValue.ToString() +"'";
			}
			else
			{
			 getRecords="select f.fdesigid,f.functionaltitle from t_functionaldesignation f,t_designation d,t_department dept where f.isactive=1 And d.status=1 And dept.deptid=d.deptid And f.functionaldesignation=d.desigid And d.designation='"+ this.ddDesignation.SelectedValue.ToString() +"'";
			}
			SqlCommand cmd=new SqlCommand(getRecords,con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rd[0].ToString();
				itm.Text=rd[1].ToString();
				this.ddFunctionalDesignation.Items.Add(itm);
			}
			rd.Close();
			con.Close();

		}

		private void ddDepartment_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			con.Open();
			this.ddDesignation.Items.Clear();
			ListItem item=new ListItem();
			item.Value="0";
			item.Text="Select--Designation";
			this.ddDesignation.Items.Add(item);
			string getRecord="select d.desigid,d.designation from t_designation d,t_department dept where dept.status=1 And d.status=1 And d.deptid=dept.deptid And dept.deptid='"+ this.ddDepartment.SelectedValue.ToString() +"'";
			SqlCommand cmd=new SqlCommand(getRecord,con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem();
				//itm.Value=rd[0].ToString();
				itm.Value=rd[1].ToString();
				itm.Text=rd[1].ToString();
				this.ddDesignation.Items.Add(itm);
			}
			rd.Close();
			con.Close();
		}

		public void getDesignation()
		{
			con.Open();
			string getRecord="select deptid,deptname from t_department where status=1 order by deptname";
			SqlCommand cmd=new SqlCommand(getRecord,con); 
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rd[0].ToString();
				itm.Text=rd[1].ToString();
				this.ddDepartment.Items.Add(itm); 
			}
			rd.Close(); 
			con.Close(); 
		}

		private void btnSpecial_Click(object sender, System.EventArgs e)
		{
			if (Panel7.Visible==true)
			{
				Panel7.Visible=false;
				ddAge.SelectedIndex=0;
				ddBloodGrp.SelectedIndex=0;
				ddDOJ.SelectedIndex=0;
				ddCategory.SelectedIndex=0;
				this.ddReportingStation.SelectedIndex=0;
				this.ddGender.SelectedIndex=0;
				this.TextBox1.Text="0";
			}
			else
			{
				Panel7.Visible=true;
				this.ddReportingStation.Visible=true;
				this.TextBox1.Text="1";
			}
			
		}

		public void getCategory()
		{
			con.Open();
			string getRec="select cat_id,cat_name from t_categorization where isactive=1 order by clevel";
			SqlCommand cmd=new SqlCommand(getRec,con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rd[0].ToString();
				itm.Text=rd[1].ToString();
				this.ddCategory.Items.Add(itm); 
			}
			rd.Close();
			con.Close(); 
		}
		public void Count(object sender,EventArgs e)
		{
		 this.txtAdName.Text="Your Are Successfull";
		}
        
	}
}