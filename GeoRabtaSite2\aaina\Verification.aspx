<%@ Page language="c#" Codebehind="Verification.aspx.cs" AutoEventWireup="false" Inherits="GeoRabtaSite.aaina.Verification" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<HTML>
	<HEAD>
		<title>Verification</title>
		<meta name="GENERATOR" Content="Microsoft Visual Studio .NET 7.1">
		<meta name="CODE_LANGUAGE" Content="C#">
		<meta name="vs_defaultClientScript" content="JavaScript">
		<meta name="vs_targetSchema" content="http://schemas.microsoft.com/intellisense/ie5">
	</HEAD>
	<body MS_POSITIONING="GridLayout">
		<form id="Form1" method="post" runat="server">
			<asp:TextBox ID="txtToken" runat="server" /><br>
			<asp:Button ID="cmdVerify" runat="server" Text="Verify" /><br>
			<asp:Label ID="lblMessage" runat="server" Text="" />
			<br>
			<asp:Label ID="lblName" runat="server" />
			<asp:Label ID="lblPCode" runat="server" Visible="False" />
			<br>
			<asp:Image ID="imgPic" runat="server" Width="80px" /><br>
			<asp:Button ID="cmdCheck" runat="server" Text="Check" />
		</form>
	</body>
</HTML>
