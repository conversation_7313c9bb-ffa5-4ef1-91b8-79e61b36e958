<%@ Register TagPrefix="ew" Namespace="eWorld.UI" Assembly="eWorld.UI" %>
<%@ Page language="c#" Codebehind="RequestDetail.aspx.cs" AutoEventWireup="false" Inherits="GeoRabtaSite.admin.RequestDetail" %>
<%@ Register TagPrefix="anthem" Namespace="Anthem" Assembly="Anthem" %>
<%@ Register TagPrefix="uc1" TagName="myMenus" Src="myMenus.ascx" %>
<%@ Register TagPrefix="uc1" TagName="AdminUserControl" Src="../AdminUserControl.ascx" %>
<%@ Register TagPrefix="uc1" TagName="LoginUser" Src="../LoginUser.ascx" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Rabta ::</title>
		<META content="text/html; charset=utf-8" http-equiv="Content-Type">
		<meta name="GENERATOR" content="Microsoft Visual Studio .NET 7.1">
		<meta name="vs_targetSchema" content="http://schemas.microsoft.com/intellisense/ie5">
		<LINK rel="stylesheet" type="text/css" href="RaabtaAdmin.css">
		<LINK rel="stylesheet" type="text/css" href="../Styles4.css">
		<LINK rel="stylesheet" type="text/css" href="css/redmond/jquery-ui-1.8.2.custom.css">
		<style type="text/css">.style1 {
	FONT-SIZE: 8px; FONT-WEIGHT: bold
}
.hid {
	DISPLAY: none
}
TH {
	FONT-SIZE: 12px
}
		</style>
		<style title="currentStyle" type="text/css">@import url( ../media/css/demos.css );
		</style>
		</STYLE>
		<style type="text/css">#lnkClientToggle {
	POSITION: relative; PADDING-BOTTOM: 0.4em; PADDING-LEFT: 20px; PADDING-RIGHT: 1em; TEXT-DECORATION: none; PADDING-TOP: 0.4em
}
#lnkClientToggle SPAN.ui-icon {
	POSITION: absolute; MARGIN: -12px 5px 0px 0px; TOP: 50%; LEFT: 0.2em
}
UL#icons {
	PADDING-BOTTOM: 0px; MARGIN: 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; PADDING-TOP: 0px
}
UL#icons LI {
	POSITION: relative; PADDING-BOTTOM: 4px; LIST-STYLE-TYPE: none; MARGIN: 2px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; FLOAT: left; CURSOR: pointer; PADDING-TOP: 4px
}
UL#icons SPAN.ui-icon {
	MARGIN: 0px 4px; FLOAT: left
}
#lnkServiceToggle {
	POSITION: relative; PADDING-BOTTOM: 0.4em; PADDING-LEFT: 20px; PADDING-RIGHT: 1em; TEXT-DECORATION: none; PADDING-TOP: 0.4em
}
#lnkServiceToggle SPAN.ui-icon {
	POSITION: absolute; MARGIN: -12px 5px 0px 0px; TOP: 50%; LEFT: 0.2em
}
		</style>
		<script language="javascript" type="text/javascript" src="../media/js/jquery.js"></script>
		<script language="javascript" type="text/javascript" src="../media/js/jquery.dataTables.js"></script>
		<script type="text/javascript" src="../jscripts/tiny_mce/tiny_mce.js"></script>
		<script type="text/javascript" charset="utf-8">
			$(document).ready(function() {
				$('#example').dataTable();
				//hover states on the static widgets
				$('#lnkClientToggle, ul#icons li').hover(
					function() { $(this).addClass('ui-state-hover'); }, 
					function() { $(this).removeClass('ui-state-hover'); }
				);
				$('#lnkServiceToggle, ul#icons li').hover(
					function() { $(this).addClass('ui-state-hover'); }, 
					function() { $(this).removeClass('ui-state-hover'); }
				);

			} );
		</script>
		<script type="text/javascript" src="../jscripts/tiny_mce/tiny_mce.js"></script>
		<script type="text/javascript">
	   	tinyMCE.init({
		// General options
		mode : "textareas",
		theme : "advanced",
		//plugins : "safari,pagebreak,style,layer,table,save,advhr,advimage,advlink,emotions,iespell,inlinepopups,insertdatetime,preview,media,searchreplace,print,contextmenu,paste,directionality,fullscreen,noneditable,visualchars,nonbreaking,xhtmlxtras,template",
		plugins : "safari,pagebreak,table,advhr,emotions,iespell,insertdatetime,searchreplace,print,contextmenu,paste,directionality,noneditable,nonbreaking",
		// Theme options
		theme_advanced_buttons1 : "save,newdocument,|,bold,italic,underline,strikethrough,|,justifyleft,justifycenter,justifyright,justifyfull,styleselect,formatselect,fontselect,fontsizeselect",
		theme_advanced_buttons2 : "cut,copy,paste,pastetext,pasteword,|,search,replace,|,bullist,numlist,|,outdent,indent,blockquote,|,undo,redo,|,link,unlink,anchor,image,cleanup,help,code,|,insertdate,inserttime,preview,|,forecolor,backcolor",
		theme_advanced_buttons3 : "tablecontrols,|,hr,removeformat,visualaid,|,sub,sup,|,charmap,emotions,iespell,media,advhr,|,print,|,ltr,rtl,|,fullscreen",
		//theme_advanced_buttons4 : "insertlayer,moveforward,movebackward,absolute,|,styleprops,|,cite,abbr,acronym,del,ins,attribs,|,visualchars,nonbreaking,template,pagebreak",
		theme_advanced_toolbar_location : "top",
		theme_advanced_toolbar_align : "left",
		//theme_advanced_statusbar_location : "bottom",
		theme_advanced_resizing : false,

		// Example content CSS (should be your site CSS)
		content_css : "css/content.css",

		// Drop lists for link/image/media/template dialogs
		template_external_list_url : "lists/template_list.js",
		external_link_list_url : "lists/link_list.js",
		external_image_list_url : "lists/image_list.js",
		media_external_list_url : "lists/media_list.js",

		// Replace values for the template plugin
		template_replace_values : {
			username : "Some User",
			staffid : "991234"
		}
	});
		</script>
		<!-- /TinyMCE -->
		<script type="text/javascript">
		function ShowDialog(Page)
		{
		 window.showModelessDialog(Page,'','dialogWidth:750px');
		} 
		</script>
		<script type="text/javascript">
	 
        $(document).ready(function()
        {  
        var k=$("#txtSerVal").val();
        //disableCB('chkServiceAgents',k);
        
        var k=$("#txtClientVal").val();
        //disableCB('chkClientAgents',k);
        
        //$('#txtServiceAgent').keyup(function(){SearchCBL('txtServiceAgent','chkServiceAgents');});
        $('#txtClientAgent').keyup(function(){SearchCBL('txtClientAgent','chkClientAgents');});
        });
    function EnableAll()
        {
    $('input[type="checkbox"]').each(function(index) 
    {
        $(this).attr("disabled", "");
    });
}

function SearchCBL(TextBox, CBL)
 {
    var aa = $('#'+TextBox).val();
    aa=aa.toLowerCase();
    if(aa=="")
        {
        $('label[for*="'+CBL+'"]').each(function(index) {
            $(this).show();
            $('#'+$(this).attr('for')).show()
          });
         }
    else
    {
        $('label[for*="'+CBL+'"]').each(function(index) {
        if($(this).html().toLowerCase().indexOf(aa)!=-1)
        {
            $(this).show();
            $('#'+$(this).attr('for')).show()
        }
        else
        {
            $(this).hide();
            $('#'+$(this).attr('for')).hide('slow')
        }
        });
  }
}
		</script>
	</HEAD>
	<body dir="ltr" bottomMargin="0" leftMargin="0" rightMargin="0" topMargin="0">
		<form id="myForm" name="MyForm" runat="server">
			<table border="0" cellSpacing="0" cellPadding="0" width="750" bgColor="#ffffff" align="center"
				height="100%">
				<TBODY>
					<tr>
						<td height="69" vAlign="middle" align="left">
							<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
								classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" width="780" height="69">
								<PARAM NAME="_cx" VALUE="20637">
								<PARAM NAME="_cy" VALUE="1825">
								<PARAM NAME="FlashVars" VALUE="">
								<PARAM NAME="Movie" VALUE="flash/Top1.swf">
								<PARAM NAME="Src" VALUE="flash/Top1.swf">
								<PARAM NAME="WMode" VALUE="Window">
								<PARAM NAME="Play" VALUE="-1">
								<PARAM NAME="Loop" VALUE="-1">
								<PARAM NAME="Quality" VALUE="High">
								<PARAM NAME="SAlign" VALUE="">
								<PARAM NAME="Menu" VALUE="-1">
								<PARAM NAME="Base" VALUE="">
								<PARAM NAME="AllowScriptAccess" VALUE="">
								<PARAM NAME="Scale" VALUE="ShowAll">
								<PARAM NAME="DeviceFont" VALUE="0">
								<PARAM NAME="EmbedMovie" VALUE="0">
								<PARAM NAME="BGColor" VALUE="">
								<PARAM NAME="SWRemote" VALUE="">
								<PARAM NAME="MovieData" VALUE="">
								<PARAM NAME="SeamlessTabbing" VALUE="1">
								<PARAM NAME="Profile" VALUE="0">
								<PARAM NAME="ProfileAddress" VALUE="">
								<PARAM NAME="ProfilePort" VALUE="0">
								<PARAM NAME="AllowNetworking" VALUE="all">
								<PARAM NAME="AllowFullScreen" VALUE="false">
								<embed src="flash/Top1.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
									type="application/x-shockwave-flash" width="780" height="69"> </embed>
							</OBJECT>
						</td>
					</tr>
					<tr>
						<td height="10">
							<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
								classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" width="780" height="10">
								<PARAM NAME="_cx" VALUE="20637">
								<PARAM NAME="_cy" VALUE="264">
								<PARAM NAME="FlashVars" VALUE="">
								<PARAM NAME="Movie" VALUE="flash/Top2.swf">
								<PARAM NAME="Src" VALUE="flash/Top2.swf">
								<PARAM NAME="WMode" VALUE="Window">
								<PARAM NAME="Play" VALUE="-1">
								<PARAM NAME="Loop" VALUE="-1">
								<PARAM NAME="Quality" VALUE="High">
								<PARAM NAME="SAlign" VALUE="">
								<PARAM NAME="Menu" VALUE="-1">
								<PARAM NAME="Base" VALUE="">
								<PARAM NAME="AllowScriptAccess" VALUE="">
								<PARAM NAME="Scale" VALUE="ShowAll">
								<PARAM NAME="DeviceFont" VALUE="0">
								<PARAM NAME="EmbedMovie" VALUE="0">
								<PARAM NAME="BGColor" VALUE="">
								<PARAM NAME="SWRemote" VALUE="">
								<PARAM NAME="MovieData" VALUE="">
								<PARAM NAME="SeamlessTabbing" VALUE="1">
								<PARAM NAME="Profile" VALUE="0">
								<PARAM NAME="ProfileAddress" VALUE="">
								<PARAM NAME="ProfilePort" VALUE="0">
								<PARAM NAME="AllowNetworking" VALUE="all">
								<PARAM NAME="AllowFullScreen" VALUE="false">
								<embed src="flash/Top2.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
									type="application/x-shockwave-flash" width="780" height="10"> </embed>
							</OBJECT>
						</td>
					</tr>
					<TR>
						<TD class="PageTitle" height="20">Geo Raabta Admin :: Client Request Detail</TD>
					</TR>
					<tr>
						<td height="20" background="../images/menu-off-bg.gif"></td>
					</tr>
					<tr>
						<td class="MainBG" vAlign="top" align="left">
							<P>&nbsp;</P>
							<fieldset style="Z-INDEX: 0"><legend style="Z-INDEX: 0">Request Detail</legend><label><b>
										<TABLE id="Table1" border="0" cellSpacing="1" cellPadding="1" width="100%">
											<TR>
												<TD style="Z-INDEX: 0" class="info"><STRONG>Request #:</STRONG><BR>
													<asp:label style="Z-INDEX: 0" id="txtReqID" Runat="server"></asp:label></TD>
											</TR>
											<TR>
												<TD style="Z-INDEX: 0" class="Info"><STRONG>Type:</STRONG><BR>
													<asp:label style="Z-INDEX: 0" id="lblType" Runat="server"></asp:label></TD>
											</TR>
											<TR>
												<TD style="Z-INDEX: 0" class="info"><STRONG>Pre-requisite:</STRONG><BR>
													<asp:label style="Z-INDEX: 0" id="lblPreReq" Runat="server"></asp:label></TD>
											</TR>
											<TR>
												<TD style="Z-INDEX: 0" class="info"><STRONG>Title:</STRONG><BR>
													<asp:label style="Z-INDEX: 0" id="lblTitle" Runat="server"></asp:label></TD>
											</TR>
											<TR>
												<TD style="Z-INDEX: 0" class="info"><STRONG>Description:</STRONG><BR>
													<asp:label style="Z-INDEX: 0" id="lblDesc" Runat="server"></asp:label></TD>
											</TR>
											<TR>
												<TD style="Z-INDEX: 0" class="info"><STRONG>Department:</STRONG><BR>
													<asp:label style="Z-INDEX: 0" id="lblDept" Runat="server"></asp:label></TD>
											</TR>
											<TR>
												<TD style="Z-INDEX: 0" class="info"><STRONG>Request Generate Date:</STRONG><BR>
													<asp:label style="Z-INDEX: 0" id="lblReqGenDate" Runat="server"></asp:label></TD>
											</TR>
										</TABLE>
									</b>&nbsp;</label></fieldset>
							<fieldset style="Z-INDEX: 0"><legend>Request Timeline</legend><label><b>
										<TABLE id="Table2" border="0" cellSpacing="1" cellPadding="1" width="100%">
											<TR>
												<TD style="Z-INDEX: 0" class="info"><STRONG>Timeline Locked:
														<anthem:checkbox style="Z-INDEX: 0" id="chkLocked" runat="server" Enabled="False"></anthem:checkbox></STRONG><anthem:linkbutton id="lblLockTimeline" onclick="lblLockTimeline_Click" runat="server" TextDuringCallBack='<img src="../tiny_red.gif" border=0 />'>Change Timeline Status</anthem:linkbutton><BR>
													<anthem:panel id="pnlLockTimeStatus" runat="server" Width="385px" Height="94px" Visible="False"
														CssClass="Info">
														<TABLE id="Table3" border="0" cellSpacing="1" cellPadding="1" width="100%">
															<TR>
																<TD><STRONG>Status:<BR>
																		<anthem:DropDownList id="ddTimelineStatus" runat="server" Width="216px" CssClass="textbox">
																			<asp:ListItem Value="1">Locked</asp:ListItem>
																			<asp:ListItem Value="2">Open</asp:ListItem>
																		</anthem:DropDownList></STRONG></TD>
															</TR>
															<TR>
																<TD><STRONG>Reason:<BR>
																		<anthem:TextBox id="txtChangeTimelineReason" runat="server" Width="370px" Height="86px" TextMode="MultiLine"></anthem:TextBox></STRONG></TD>
															</TR>
															<TR>
																<TD>
																	<anthem:Button id="btnLockTimeline" runat="server" TextDuringCallBack="Please wait.." CssClass="fbtab"
																		Text="Save Changes" EnabledDuringCallBack="false"></anthem:Button></TD>
															</TR>
														</TABLE>
													</anthem:panel></TD>
											</TR>
											<TR>
												<TD style="Z-INDEX: 0; HEIGHT: 52px" class="info"><STRONG>Timeline(Day(s)):</STRONG>
													<anthem:linkbutton style="Z-INDEX: 0" id="lblChangeTimeline" runat="server" TextDuringCallBack='<img src="../tiny_red.gif" border=0 />'>Change Timeline </anthem:linkbutton><BR>
													<asp:label style="Z-INDEX: 0" id="lblTimeline" Runat="server"></asp:label><BR>
													<anthem:panel style="Z-INDEX: 0" id="pnlChangeTimeline" runat="server" Width="704px" Visible="False">
														<P>
															<TABLE id="Table4" border="0" cellSpacing="1" cellPadding="1" width="100%">
																<TR>
																	<TD><STRONG>Additional Day(s):<BR>
																			<anthem:DropDownList id="ddAdditionalDays" runat="server" Width="104px" CssClass="textbox">
																				<asp:ListItem Value="1">1</asp:ListItem>
																				<asp:ListItem Value="2">2</asp:ListItem>
																				<asp:ListItem Value="3">3</asp:ListItem>
																				<asp:ListItem Value="4">4</asp:ListItem>
																				<asp:ListItem Value="5">5</asp:ListItem>
																				<asp:ListItem Value="6">6</asp:ListItem>
																				<asp:ListItem Value="7">7</asp:ListItem>
																				<asp:ListItem Value="8">8</asp:ListItem>
																				<asp:ListItem Value="9">9</asp:ListItem>
																				<asp:ListItem Value="10">10</asp:ListItem>
																				<asp:ListItem Value="11">11</asp:ListItem>
																				<asp:ListItem Value="12">12</asp:ListItem>
																				<asp:ListItem Value="13">13</asp:ListItem>
																				<asp:ListItem Value="14">14</asp:ListItem>
																				<asp:ListItem Value="15">15</asp:ListItem>
																			</anthem:DropDownList></STRONG></TD>
																</TR>
																<TR>
																	<TD><STRONG>Reason:<BR>
																			<anthem:TextBox style="Z-INDEX: 0" id="txtReasonforDays" runat="server" Width="370px" Height="86px"
																				TextMode="MultiLine"></anthem:TextBox></STRONG></TD>
																</TR>
																<TR>
																	<TD>
																		<anthem:Button style="Z-INDEX: 0" id="btnAddDays" runat="server" TextDuringCallBack="Please wait.."
																			CssClass="fbtab" Text="Save Changes" EnabledDuringCallBack="false"></anthem:Button></TD>
																</TR>
															</TABLE>
														</P>
													</anthem:panel><anthem:panel id="Panel2" runat="server"></anthem:panel></TD>
											</TR>
											<TR>
												<TD style="Z-INDEX: 0; HEIGHT: 28px" class="info"><STRONG>Remaining Timeline(Day(s)):<BR>
													</STRONG>
													<anthem:label id="lblRemain" runat="server"></anthem:label></TD>
											</TR>
										</TABLE>
									</b>&nbsp;</label></fieldset>
							<div class="info"><anthem:linkbutton id="lnkClientToggle" runat="server" TextDuringCallBack='<img src="../tiny_red.gif" border=0 />'
									CssClass="ui-state-default ui-corner-all"><span class="ui-icon ui-icon-newwin"></span>Show/Hide Client Agent &nbsp;<br><br></anthem:linkbutton></div>
							<anthem:panel id="pnlToggleClient" runat="server" Visible="False">
								<FIELDSET id="fieldSetClientAgent"><LEGEND>Client Agent(s)</LEGEND>
									<DIV style="Z-INDEX: 0; WIDTH: 99.38%; HEIGHT: 136px; OVERFLOW: auto" class="info" ms_positioning="FlowLayout">
										<anthem:datalist style="Z-INDEX: 0" id="clientDataList" runat="server" RepeatColumns="2" RepeatDirection="Horizontal"
											AutoUpdateAfterCallBack="True">
											<ItemTemplate>
												<table border="0" cellpadding="2" cellspacing="0" class="info">
													<tr>
														<td align="center"><img src='..\EmpThumbnail\<%#DataBinder.Eval(Container.DataItem,"pcode")%>.jpg' height=100 width=75></td>
													</tr>
													<tr>
														<td nowrap><%#DataBinder.Eval(Container.DataItem,"name")%></td>
													</tr>
												</table>
											</ItemTemplate>
										</anthem:datalist></DIV>
									<anthem:linkbutton style="Z-INDEX: 0" id="lnkClientAgent" runat="server" TextDuringCallBack='<img src="../tiny_red.gif" border=0 />'>Add/Change Client Agent</anthem:linkbutton><BR>
									<anthem:panel style="Z-INDEX: 0" id="pnlClientAgents" runat="server" Height="184px" Visible="False"
										CssClass="info">
										<STRONG>Search:&nbsp; </STRONG><INPUT style="WIDTH: 264px; HEIGHT: 16px" id="txtClientAgent" class="textbox" onkeyup="SearchCBL('txtClientAgent','chkClientAgents');"
											size="38">
										<BR>
										<DIV style="Z-INDEX: 0; WIDTH: 100%; HEIGHT: 96px; OVERFLOW: auto" id="frm" ms_positioning="FlowLayout">
											<asp:CheckBoxList style="Z-INDEX: 0" id="chkClientAgents" runat="server" Width="97%"></asp:CheckBoxList></DIV>
									</anthem:panel>
									<anthem:Button style="Z-INDEX: 0" id="btnChangeClient" runat="server" TextDuringCallBack="Please wait.."
										Visible="False" CssClass="fbtab" Text="Save Changes" EnabledDuringCallBack="false"></anthem:Button></FIELDSET>
							</anthem:panel>
							<div class="info"><anthem:linkbutton style="Z-INDEX: 0" id="lnkServiceToggle" runat="server" TextDuringCallBack='<img src="../tiny_red.gif" border=0 />'
									CssClass="ui-state-default ui-corner-all"><span class="ui-icon ui-icon-newwin"></span>Show/Hide Service Agent &nbsp;<br><br></anthem:linkbutton></div>
							<anthem:panel id="pnlToggleService" runat="server" Visible="False">
								<FIELDSET><LEGEND>Service Agent(s)</LEGEND>
									<DIV style="Z-INDEX: 0; WIDTH: 99.38%; HEIGHT: 136px; OVERFLOW: auto" class="info" ms_positioning="FlowLayout">
										<anthem:datalist style="Z-INDEX: 0" id="serviceDataList" runat="server" RepeatColumns="3" RepeatDirection="Horizontal"
											AutoUpdateAfterCallBack="True">
											<ItemTemplate>
												<table border="0" cellpadding="2" cellspacing="0" class="info">
													<tr>
														<td align="center"><img src='..\EmpThumbnail\<%#DataBinder.Eval(Container.DataItem,"pcode")%>.jpg' height=100 width=75></td>
													</tr>
													<tr>
														<td nowrap><%#DataBinder.Eval(Container.DataItem,"name")%></td>
													</tr>
												</table>
											</ItemTemplate>
										</anthem:datalist></DIV>
									<anthem:linkbutton id="lnkServiceAgent" runat="server" TextDuringCallBack='<img src="../tiny_red.gif" border=0 />'>Add/Change Service Agent</anthem:linkbutton><BR>
									<anthem:panel style="Z-INDEX: 0" id="pnlServiceAgent" runat="server" Visible="False" CssClass="info">
										<STRONG>Search:&nbsp; </STRONG><INPUT style="WIDTH: 264px; HEIGHT: 16px" id="txtServiceAgent" class="textbox" onkeyup="SearchCBL('txtServiceAgent','chkServiceAgents');"
											size="38" name="Text1">
										<BR>
										<DIV style="WIDTH: 100%; HEIGHT: 120px; OVERFLOW: auto" id="frm" ms_positioning="FlowLayout">
											<asp:CheckBoxList style="Z-INDEX: 0" id="chkServiceAgents" runat="server" Width="100%"></asp:CheckBoxList></DIV>
										<anthem:Button style="Z-INDEX: 0" id="btnChangeService" runat="server" TextDuringCallBack="Please wait.."
											Visible="False" CssClass="fbtab" Text="Save Changes" EnabledDuringCallBack="false"></anthem:Button>
									</anthem:panel></FIELDSET>
							</anthem:panel>
							<fieldset><legend>Communication Log</legend>
								<div class="blogblock"><anthem:datalist id="dlBlog" runat="server" AutoUpdateAfterCallBack="True">
										<ItemTemplate>
											<div class="blogcomms">
												<div style="float: left; margin-right: 52px; width: 160px;">
													<div class="name"><%# DataBinder.Eval(Container.DataItem,"name") %></div>
													<div class="ddate"><%# DataBinder.Eval(Container.DataItem,"postdate")%></div>
													<div class="action"><%# DataBinder.Eval(Container.DataItem,"actioncode") %></div>
												</div>
												<div class="blog">
													<img class="blogimg" src="images/comment.png" />
													<div class="blogtext"><%# DataBinder.Eval(Container.DataItem,"communication") %></div>
												</div>
											</div>
										</ItemTemplate>
									</anthem:datalist></div>
								<br>
							</fieldset>
							<fieldset><legend>Reply</legend><anthem:textbox style="Z-INDEX: 0" id="txtReply" runat="server" Width="724px" Height="148px" TextMode="MultiLine"
									AutoUpdateAfterCallBack="True"></anthem:textbox><BR>
								<STRONG>Action Code:<BR>
									<anthem:dropdownlist style="Z-INDEX: 0" id="ddActionCode" runat="server" Width="264px" CssClass="textbox"
										AutoPostBack="True" AutoCallBack="True">
										<asp:ListItem Value="0">Select--Action Code</asp:ListItem>
									</anthem:dropdownlist><anthem:textbox id="txtReplica" runat="server" Width="352px"></anthem:textbox><BR>
									<anthem:panel style="Z-INDEX: 0" id="pnlActionCode" runat="server" Width="721px" Height="32px"
										Visible="False" AutoUpdateAfterCallBack="True">Request Closed Till Date: 
<ew:CalendarPopup id="CalendarPopup1" runat="server">
											<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
												BackColor="White"></WeekdayStyle>
											<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
												BackColor="Yellow"></MonthHeaderStyle>
											<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
												BackColor="AntiqueWhite"></OffMonthStyle>
											<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
												BackColor="White"></GoToTodayStyle>
											<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
												BackColor="LightGoldenrodYellow"></TodayDayStyle>
											<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
												BackColor="Orange"></DayHeaderStyle>
											<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
												BackColor="LightGray"></WeekendStyle>
											<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
												BackColor="Yellow"></SelectedDateStyle>
											<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
												BackColor="White"></ClearDateStyle>
											<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
												BackColor="White"></HolidayStyle>
										</ew:CalendarPopup></anthem:panel><BR>
									<anthem:button style="Z-INDEX: 0" id="btnReply" runat="server" TextDuringCallBack="Please wait.."
										CssClass="fbtab" Text="Post Reply" EnabledDuringCallBack="false" AutoUpdateAfterCallBack="True"></anthem:button><anthem:button style="Z-INDEX: 0" id="btnReOpen" runat="server" Enabled="False" TextDuringCallBack="Please wait.."
										CssClass="fbtab" Text="Re-Open" EnabledDuringCallBack="false" AutoUpdateAfterCallBack="True"></anthem:button></STRONG></fieldset>
						</td>
					</tr>
					<TR>
						<TD height="20" vAlign="middle" align="center">Copyright © 2010 Independent Media 
							Corporation <A href="http://www.geo.tv">www.geo.tv</A></TD>
					</TR>
				</TBODY>
			</table>
		</form>
	</body>
</HTML>
