<%@ Register TagPrefix="ew" Namespace="eWorld.UI" Assembly="eWorld.UI" %>
<%@ Page CodeBehind="RequestUserProfile.aspx.cs" Language="c#" AutoEventWireup="false" Inherits="GeoRabtaSite.RequestEmployeInfo" smartNavigation="False" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Rabta Admin :: Request User Profile </title>
		<meta content="False" name="vs_showGrid">
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="RaabtaAdmin.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body bottomMargin="0" leftMargin="0" topMargin="0" rightMargin="0">
		<form id="myForm" name="MyForm" runat="server">
			<table height="100%" cellSpacing="0" cellPadding="0" width="780" align="center" border="0">
				<tr>
					<td vAlign="top" align="left" height="69">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="69" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="1826">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top1.swf">
							<PARAM NAME="Src" VALUE="flash/Top1.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top1.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="69"> </embed>
						</OBJECT>
					</td>
				</tr>
				<tr>
					<td vAlign="top" align="left" height="10">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="10" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="265">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top2.swf">
							<PARAM NAME="Src" VALUE="flash/Top2.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top2.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="10"> </embed>
						</OBJECT>
					</td>
				</tr>
				<TR>
					<TD class="pagetitle" vAlign="middle" align="left" height="20">Geo Raabta Admin :: 
						Request User Profile</TD>
				</TR>
				<tr>
					<td class="mainbg" vAlign="top" align="left">
						<TABLE id="Table34" cellSpacing="0" cellPadding="0" width="100%" border="0">
							<TR>
								<TD><asp:imagebutton id="ImageButton1" runat="server" DESIGNTIMEDRAGDROP="2397" ImageUrl="..\images\employee.gif"></asp:imagebutton><asp:imagebutton id="ImageButton2" runat="server" DESIGNTIMEDRAGDROP="2398" ImageUrl="..\images\personal.gif"></asp:imagebutton><asp:imagebutton id="ImageButton3" runat="server" ImageUrl="..\images\family.gif"></asp:imagebutton><asp:imagebutton id="ImageButton4" runat="server" DESIGNTIMEDRAGDROP="2400" ImageUrl="..\images\education.gif"></asp:imagebutton></TD>
							</TR>
							<TR>
								<TD background="..\images\tabstrip.jpg" height="14"></TD>
							</TR>
							<TR>
								<TD vAlign="middle" height="110">&nbsp;
									<BR>
									<TABLE id="Table1" cellSpacing="0" cellPadding="3" width="750" align="center" border="0">
										<TR>
											<TD><asp:image id="Image1" runat="server" Height="100px"></asp:image>&nbsp;
												<asp:label id="lblEmployeeNameTitle" runat="server" Font-Names="Verdana" Font-Size="Large"
													Font-Bold="True"></asp:label></TD>
										</TR>
									</TABLE>
								</TD>
							</TR>
						</TABLE>
						<asp:panel id="pnlPersonal" runat="server" Visible="False" Width="100%">
							<P><STRONG>
									<TABLE id="Table38" cellSpacing="0" cellPadding="3" width="750" align="center" border="0">
										<TR>
											<TD class="OrangeFormTitle">Personal Information</TD>
										</TR>
									</TABLE>
									<TABLE class="mainformcolor" id="Table42" cellSpacing="0" cellPadding="3" width="750" align="center"
										border="0">
										<TR>
											<TD class="menubar" colSpan="2"><STRONG>Date Of Birth:</STRONG></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="menubar" colSpan="2"><STRONG>Father Name:</STRONG></TD>
										</TR>
										<TR>
											<TD class="actual" width="120">Actual:</TD>
											<TD class="actual" width="300">
												<asp:Label id="lblName" runat="server"></asp:Label></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="actual" width="120">Actual:</TD>
											<TD class="actual" width="300">
												<asp:Label id="lblFName" runat="server"></asp:Label></TD>
										</TR>
										<TR>
											<TD class="Requested" width="120">Requested:</TD>
											<TD class="Requested" width="300">
												<asp:Label id="Label1" runat="server"></asp:Label></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="Requested" width="120">Requested:</TD>
											<TD class="Requested" width="300">
												<asp:Label id="Label2" runat="server" Width="100%"></asp:Label></TD>
										</TR>
										<TR>
											<TD class="Action" width="120">Action:</TD>
											<TD class="Action" width="300">
												<asp:RadioButtonList id="rdDOB" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
													<asp:ListItem Value="0">Accept</asp:ListItem>
													<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
												</asp:RadioButtonList></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="Action" width="120">Action:</TD>
											<TD class="Action" width="300">
												<asp:RadioButtonList id="rdFname" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
													<asp:ListItem Value="0">Accept</asp:ListItem>
													<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
												</asp:RadioButtonList></TD>
										</TR>
										<TR>
											<TD class="menubar" colSpan="2"><STRONG>Gender:</STRONG></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="menubar" colSpan="2"><STRONG>Blood Group:</STRONG></TD>
										</TR>
										<TR>
											<TD class="actual" width="120">Actual:</TD>
											<TD class="actual" width="300">
												<asp:Label id="lblGender" runat="server"></asp:Label></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="actual" width="120">Actual:</TD>
											<TD class="actual" width="300">
												<asp:Label id="lblBloodGrp" runat="server"></asp:Label></TD>
										</TR>
										<TR>
											<TD class="Requested" width="120">Requested:</TD>
											<TD class="Requested" width="300">
												<asp:Label id="Label3" runat="server" Width="100%"></asp:Label></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="Requested" width="120">Requested:</TD>
											<TD class="Requested" width="300">
												<asp:Label id="Label4" runat="server" Width="100%"></asp:Label></TD>
										</TR>
										<TR>
											<TD class="Action" width="120">Action:</TD>
											<TD class="Action" width="300">
												<asp:RadioButtonList id="rdGender" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
													<asp:ListItem Value="0">Accept</asp:ListItem>
													<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
												</asp:RadioButtonList></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="Action" width="120">Action:</TD>
											<TD class="Action" width="300">
												<asp:RadioButtonList id="rdBloodGrp" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
													<asp:ListItem Value="0">Accept</asp:ListItem>
													<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
												</asp:RadioButtonList></TD>
										</TR>
										<TR>
											<TD class="menubar" width="120" colSpan="2"><STRONG>Religion:</STRONG></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="menubar" width="120" colSpan="2"><STRONG>Passport No:</STRONG></TD>
										</TR>
										<TR>
											<TD class="actual" width="120">Actual:</TD>
											<TD class="actual" width="300">
												<asp:Label id="lblReligion" runat="server"></asp:Label></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="actual" width="120">Actual:</TD>
											<TD class="actual" width="300">
												<asp:Label id="lblPassport" runat="server"></asp:Label></TD>
										</TR>
										<TR>
											<TD class="Requested" width="120">Requested:</TD>
											<TD class="Requested" width="300">
												<asp:Label id="Label5" runat="server"></asp:Label></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="Requested" width="120">Requested:</TD>
											<TD class="Requested" width="300">
												<asp:Label id="Label6" runat="server" Width="100%"></asp:Label></TD>
										</TR>
										<TR>
											<TD class="Action" width="120">Action:</TD>
											<TD class="Action" width="300">
												<asp:RadioButtonList id="rdReligion" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
													<asp:ListItem Value="0">Accept</asp:ListItem>
													<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
												</asp:RadioButtonList></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="Action" width="120">Action:</TD>
											<TD class="Action" width="300">
												<asp:RadioButtonList id="rdPassport" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
													<asp:ListItem Value="0">Accept</asp:ListItem>
													<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
												</asp:RadioButtonList></TD>
										</TR>
										<TR>
											<TD class="menubar" width="120"><STRONG>Address:</STRONG></TD>
											<TD class="menubar" width="300"></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="menubar" width="120"><STRONG>Maritial Status: </STRONG>
											</TD>
											<TD class="menubar" width="300"></TD>
										</TR>
										<TR>
											<TD class="actual" width="120">Actual:</TD>
											<TD class="actual" width="300">
												<asp:Label id="lblAddress" runat="server"></asp:Label></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="actual" width="120">Actual:</TD>
											<TD class="actual" width="300">
												<asp:Label id="lblMaritialStat" runat="server"></asp:Label></TD>
										</TR>
										<TR>
											<TD class="Requested" width="120">Requested:</TD>
											<TD class="Requested" width="300">
												<asp:Label id="Label7" runat="server"></asp:Label></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="Requested" width="120">Requested:</TD>
											<TD class="Requested" width="300">
												<asp:Label id="Label8" runat="server"></asp:Label></TD>
										</TR>
										<TR>
											<TD class="Action" width="120">Action:</TD>
											<TD class="Action" width="300">
												<asp:RadioButtonList id="rdAddress" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
													<asp:ListItem Value="0">Accept</asp:ListItem>
													<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
												</asp:RadioButtonList></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="Action" width="120">Action:</TD>
											<TD class="Action" width="300">
												<asp:RadioButtonList id="rdMaritialStat" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
													<asp:ListItem Value="0">Accept</asp:ListItem>
													<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
												</asp:RadioButtonList></TD>
										</TR>
										<TR>
											<TD class="menubar" width="120" colSpan="2"><STRONG>Email (Personal):</STRONG></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="menubar" width="120" colSpan="2"><STRONG>NTN No</STRONG></TD>
										</TR>
										<TR>
											<TD class="actual" width="120">Actual:</TD>
											<TD class="actual" width="300">
												<asp:Label id="lblEmail" runat="server"></asp:Label></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="actual" width="120">Actual:</TD>
											<TD class="actual" width="300">
												<asp:Label id="lblNTN" runat="server" Font-Bold="True"></asp:Label></TD>
										</TR>
										<TR>
											<TD class="Requested" width="120">Requested:</TD>
											<TD class="Requested" width="300">
												<asp:Label id="Label9" runat="server"></asp:Label></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="Requested" width="120">Requested:</TD>
											<TD class="Requested" width="300">
												<asp:Label id="Label10" runat="server"></asp:Label></TD>
										</TR>
										<TR>
											<TD class="Action" width="120">Action:</TD>
											<TD class="Action" width="300">
												<asp:RadioButtonList id="rdEmail" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
													<asp:ListItem Value="0">Accept</asp:ListItem>
													<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
												</asp:RadioButtonList></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="Action" width="120">Action:</TD>
											<TD class="Action" width="300">
												<asp:RadioButtonList id="rdNTN" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
													<asp:ListItem Value="0">Accept</asp:ListItem>
													<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
												</asp:RadioButtonList></TD>
										</TR>
										<TR>
											<TD class="menubar" width="120" colSpan="2"><STRONG>Contact #:</STRONG></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="menubar" width="120" colSpan="2"><STRONG>Mobile #:</STRONG></TD>
										</TR>
										<TR>
											<TD class="actual" width="120">Actual:</TD>
											<TD class="actual" width="300">
												<asp:Label id="lblcontactNo" runat="server"></asp:Label></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="actual" width="120">Actual:</TD>
											<TD class="actual" width="300">
												<asp:Label id="lblMobileNo" runat="server" Font-Bold="True"></asp:Label></TD>
										</TR>
										<TR>
											<TD class="Requested" width="120">Requested:</TD>
											<TD class="Requested" width="300">
												<asp:Label id="Label11" runat="server"></asp:Label></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="Requested" width="120">Requested:</TD>
											<TD class="Requested" width="300">
												<asp:Label id="Label12" runat="server"></asp:Label></TD>
										</TR>
										<TR>
											<TD class="Action" width="120">Action:</TD>
											<TD class="Action" width="300">
												<asp:RadioButtonList id="rdContact" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
													<asp:ListItem Value="0">Accept</asp:ListItem>
													<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
												</asp:RadioButtonList></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="Action" width="120">Action:</TD>
											<TD class="Action" width="300">
												<asp:RadioButtonList id="rdMobile" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
													<asp:ListItem Value="0">Accept</asp:ListItem>
													<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
												</asp:RadioButtonList></TD>
										</TR>
										<TR>
											<TD class="menubar" width="120" colSpan="2"><STRONG>NIC #(New):</STRONG></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="menubar" width="120" colSpan="2"><STRONG>NIC (Old): </STRONG>
											</TD>
										</TR>
										<TR>
											<TD class="actual" width="120">Actual:</TD>
											<TD class="actual" width="300">
												<asp:Label id="lblNICNew" runat="server"></asp:Label></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="actual" width="120">Actual:</TD>
											<TD class="actual" width="300">
												<asp:Label id="lblNICOld" runat="server" Font-Bold="True"></asp:Label></TD>
										</TR>
										<TR>
											<TD class="Requested" width="120">Requested:</TD>
											<TD class="Requested" width="300">
												<asp:Label id="Label13" runat="server"></asp:Label></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="Requested" width="120">Requested:</TD>
											<TD class="Requested" width="300">
												<asp:Label id="Label14" runat="server"></asp:Label></TD>
										</TR>
										<TR>
											<TD class="Action" width="120">Action:</TD>
											<TD class="Action" width="300">
												<asp:RadioButtonList id="rdNICNew" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
													<asp:ListItem Value="0">Accept</asp:ListItem>
													<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
												</asp:RadioButtonList></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="Action" width="120">Action:</TD>
											<TD class="Action" width="300">
												<asp:RadioButtonList id="rdNICOld" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
													<asp:ListItem Value="0">Accept</asp:ListItem>
													<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
												</asp:RadioButtonList></TD>
										</TR>
										<TR>
											<TD class="menubar" colSpan="2"><STRONG>Kin:</STRONG></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="menubar" colSpan="2"><STRONG>Bank Account No:</STRONG></TD>
										</TR>
										<TR>
											<TD class="actual" width="120">Actual:</TD>
											<TD class="actual" width="300">
												<asp:Label id="lblKin" runat="server"></asp:Label></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="actual" width="120">Actual:</TD>
											<TD class="actual" width="300">
												<asp:Label id="lblBankAcctNo" runat="server" Font-Bold="True"></asp:Label></TD>
										</TR>
										<TR>
											<TD class="Requested" width="120">Requested:</TD>
											<TD class="Requested" width="300">
												<asp:Label id="Label15" runat="server"></asp:Label></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="Requested" width="120">Requested:</TD>
											<TD class="Requested" width="300">
												<asp:Label id="Label16" runat="server"></asp:Label></TD>
										</TR>
										<TR>
											<TD class="Action" width="120">Action:</TD>
											<TD class="Action" width="300">
												<asp:RadioButtonList id="rdKin" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
													<asp:ListItem Value="0">Accept</asp:ListItem>
													<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
												</asp:RadioButtonList></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="Action" width="120">Action:</TD>
											<TD class="Action" width="300">
												<asp:RadioButtonList id="rdBnkNo" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
													<asp:ListItem Value="0">Accept</asp:ListItem>
													<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
												</asp:RadioButtonList></TD>
										</TR>
										<TR>
											<TD class="menubar" colSpan="2"><STRONG>Account Details:</STRONG></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="menubar" colSpan="2"><STRONG>Requested Photo:</STRONG></TD>
										</TR>
										<TR>
											<TD class="actual" width="120">Actual:</TD>
											<TD class="actual" width="300">
												<asp:Label id="lblAccountDetails" runat="server" Font-Bold="True"></asp:Label></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="actual" width="120">Actual:</TD>
											<TD class="actual" width="300">
												<asp:Button id="btnOriginalImg" runat="server" Width="155px" Text="View Original Image" CssClass="textbox"></asp:Button></TD>
										</TR>
										<TR>
											<TD class="Requested" width="120">Requested:</TD>
											<TD class="Requested" width="300">
												<asp:Label id="Label17" runat="server"></asp:Label></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="Requested" width="120">Requested:</TD>
											<TD class="Requested" width="300">
												<asp:Button id="btnRequestImg" runat="server" Width="155px" Text="View Requested Image" CssClass="textbox"></asp:Button></TD>
										</TR>
										<TR>
											<TD class="action" width="120">Action:</TD>
											<TD class="action" width="300">
												<asp:RadioButtonList id="rdAcctDetails" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
													<asp:ListItem Value="0">Accept</asp:ListItem>
													<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
												</asp:RadioButtonList></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="action" width="120">Action:</TD>
											<TD class="action" width="300">
												<asp:RadioButtonList id="rdoRequestPhoto" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
													<asp:ListItem Value="0">Accept</asp:ListItem>
													<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
												</asp:RadioButtonList></TD>
										</TR>
										<TR>
											<TD class="menubar" width="120" colSpan="2"><STRONG>Nick:</STRONG></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="menubar" width="120" colSpan="2"><STRONG>Nationality 1:</STRONG></TD>
										</TR>
										<TR>
											<TD class="actual" width="120">Actual:</TD>
											<TD class="actual" width="300">
												<asp:Label id="lblNick" runat="server" Font-Bold="True"></asp:Label></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="actual" width="120">Actual:</TD>
											<TD class="actual" width="300">
												<asp:Label id="lblNationality" runat="server" Font-Bold="True"></asp:Label></TD>
										</TR>
										<TR>
											<TD class="Requested" width="120">Requested:</TD>
											<TD class="Requested" width="300">
												<asp:Label id="Label26" runat="server"></asp:Label></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="Requested" width="120">Requested:</TD>
											<TD class="Requested" width="300">
												<asp:Label id="Label28" runat="server"></asp:Label></TD>
										</TR>
										<TR>
											<TD class="action" width="120">Action:</TD>
											<TD class="action" width="300">
												<asp:RadioButtonList id="rdoNick" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
													<asp:ListItem Value="0">Accept</asp:ListItem>
													<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
												</asp:RadioButtonList></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD class="action" width="120">Action:</TD>
											<TD class="action" width="300">
												<asp:RadioButtonList id="rdoNationality" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
													<asp:ListItem Value="0">Accept</asp:ListItem>
													<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
												</asp:RadioButtonList></TD>
										</TR>
										<TR>
											<TD class="menubar" colSpan="2"><STRONG>Nationality 2:</STRONG></TD>
											<TD style="WIDTH: 31px" width="31"></TD>
											<TD width="120"></TD>
											<TD width="300"></TD>
										</TR>
										<TR>
											<TD class="actual" width="120">Actual:</TD>
											<TD class="actual" width="300">
												<asp:Label id="lblNationality2" runat="server" Font-Bold="True"></asp:Label></TD>
											<TD style="WIDTH: 31px" width="38"></TD>
											<TD width="120"></TD>
											<TD width="300"></TD>
										</TR>
										<TR>
											<TD class="Requested" width="120">Requested:</TD>
											<TD class="Requested" width="300">
												<asp:Label id="Label29" runat="server"></asp:Label></TD>
											<TD style="WIDTH: 38px" width="38"></TD>
											<TD width="120"></TD>
											<TD width="300"></TD>
										</TR>
										<TR>
											<TD class="action" width="120">Action:</TD>
											<TD class="action" width="300">
												<asp:RadioButtonList id="rdoNationality2" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
													<asp:ListItem Value="0">Accept</asp:ListItem>
													<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
												</asp:RadioButtonList></TD>
											<TD style="WIDTH: 38px" width="38"></TD>
											<TD width="120"></TD>
											<TD width="300"></TD>
										</TR>
									</TABLE>
								</STRONG>
							</P>
						</asp:panel><asp:panel id="pnlEmployeeInfo" runat="server"><STRONG>
								<TABLE id="Table3" cellSpacing="0" cellPadding="3" width="750" align="center" border="0">
									<TR>
										<TD class="OrangeFormTitle">Employee Information:</TD>
									</TR>
								</TABLE>
								<TABLE class="mainformcolor" id="Table4" cellSpacing="0" cellPadding="3" width="750" align="center"
									border="0">
									<TR>
										<TD class="menubar" colSpan="2"><STRONG>Employee Code:</STRONG></TD>
										<TD width="50"></TD>
										<TD class="menubar" colSpan="2"><STRONG>Name:</STRONG></TD>
									</TR>
									<TR>
										<TD class="actual" width="120">Actual:</TD>
										<TD class="actual" width="300">
											<asp:Label id="lblEmpInfoCode" runat="server"></asp:Label></TD>
										<TD width="50"></TD>
										<TD class="actual" width="120">Actual:</TD>
										<TD class="actual" width="300">
											<asp:Label id="lblEmpInfoName" runat="server" DESIGNTIMEDRAGDROP="5176"></asp:Label></TD>
									</TR>
									<TR>
										<TD class="Requested" width="120">Requested:</TD>
										<TD class="Requested" width="300">
											<asp:Label id="Label18" runat="server" DESIGNTIMEDRAGDROP="5164"></asp:Label></TD>
										<TD width="50"></TD>
										<TD class="Requested" width="120">Requested:</TD>
										<TD class="Requested" width="300">
											<asp:Label id="Label19" runat="server"></asp:Label></TD>
									</TR>
									<TR>
										<TD class="action" width="120">Action:</TD>
										<TD class="action" width="300">
											<asp:RadioButtonList id="rdoEmpCode" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
												<asp:ListItem Value="0">Accept</asp:ListItem>
												<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
											</asp:RadioButtonList></TD>
										<TD width="50"></TD>
										<TD class="action" width="120">Action:</TD>
										<TD class="action" width="300">
											<asp:RadioButtonList id="rdoEmpName" runat="server" DESIGNTIMEDRAGDROP="5182" RepeatDirection="Horizontal"
												RepeatLayout="Flow">
												<asp:ListItem Value="0">Accept</asp:ListItem>
												<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
											</asp:RadioButtonList></TD>
									</TR>
									<TR>
										<TD class="menubar" colSpan="2"><STRONG>Department</STRONG></TD>
										<TD width="50"></TD>
										<TD class="menubar" colSpan="2"><STRONG>Designation:</STRONG></TD>
									</TR>
									<TR>
										<TD class="actual" width="120">Actual:</TD>
										<TD class="actual" width="300">
											<asp:Label id="lblEmpInfoDept" runat="server"></asp:Label></TD>
										<TD width="50"></TD>
										<TD class="actual" width="120">Actual:</TD>
										<TD class="actual" width="300">
											<asp:Label id="lblEmpInfoDesignation" runat="server"></asp:Label></TD>
									</TR>
									<TR>
										<TD class="Requested" width="120">Requested:</TD>
										<TD class="Requested" width="300">
											<asp:Label id="Label21" runat="server" DESIGNTIMEDRAGDROP="5201"></asp:Label></TD>
										<TD width="50"></TD>
										<TD class="Requested" width="120">Requested:</TD>
										<TD class="Requested" width="300">
											<asp:Label id="Label23" runat="server" DESIGNTIMEDRAGDROP="5216"></asp:Label></TD>
									</TR>
									<TR>
										<TD class="action" width="120">Action:</TD>
										<TD class="action" width="300">
											<asp:RadioButtonList id="rdoDepartment" runat="server" DESIGNTIMEDRAGDROP="5204" RepeatDirection="Horizontal"
												RepeatLayout="Flow">
												<asp:ListItem Value="0">Accept</asp:ListItem>
												<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
											</asp:RadioButtonList></TD>
										<TD width="50"></TD>
										<TD class="action" width="120">Action:</TD>
										<TD class="action" width="300">
											<asp:RadioButtonList id="rdoDesignation" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
												<asp:ListItem Value="0">Accept</asp:ListItem>
												<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
											</asp:RadioButtonList></TD>
									</TR>
									<TR>
										<TD class="menubar" colSpan="2"><STRONG>Functional Designation:</STRONG></TD>
										<TD width="50"></TD>
										<TD class="menubar" width="120" colSpan="2"><STRONG>Date Of Join</STRONG></TD>
									</TR>
									<TR>
										<TD class="actual" width="120">Actual:</TD>
										<TD class="actual" width="300">
											<asp:Label id="lblEmpInfoFunctionalDesignation" runat="server" DESIGNTIMEDRAGDROP="5237"></asp:Label></TD>
										<TD width="50"></TD>
										<TD class="actual" width="120">Actual:</TD>
										<TD class="actual" width="300">
											<asp:Label id="lblEmpInfoDOJ" runat="server"></asp:Label></TD>
									</TR>
									<TR>
										<TD class="Requested" width="120">Requested:</TD>
										<TD class="Requested" width="300">
											<asp:Label id="Label22" runat="server"></asp:Label></TD>
										<TD width="50"></TD>
										<TD class="Requested" width="120">Requested:</TD>
										<TD class="Requested" width="300">
											<asp:Label id="Label24" runat="server" DESIGNTIMEDRAGDROP="5256"></asp:Label></TD>
									</TR>
									<TR>
										<TD class="action" width="120">Action:</TD>
										<TD class="action" width="300">
											<asp:RadioButtonList id="rdoFunctionalDesignation" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
												<asp:ListItem Value="0">Accept</asp:ListItem>
												<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
											</asp:RadioButtonList></TD>
										<TD width="50"></TD>
										<TD class="action" width="120">Action:</TD>
										<TD class="action" width="300">
											<asp:RadioButtonList id="rdoDOJ" runat="server" DESIGNTIMEDRAGDROP="5259" RepeatDirection="Horizontal"
												RepeatLayout="Flow">
												<asp:ListItem Value="0">Accept</asp:ListItem>
												<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
											</asp:RadioButtonList></TD>
									</TR>
									<TR>
										<TD class="menubar" width="120"><STRONG>Exit #:</STRONG></TD>
										<TD class="menubar" width="300"></TD>
										<TD width="50"></TD>
										<TD class="menubar" width="120"><STRONG>Station:</STRONG></TD>
										<TD class="menubar" width="300"></TD>
									</TR>
									<TR>
										<TD class="actual" width="120">Actual:</TD>
										<TD class="actual" width="300">
											<asp:Label id="lblEmpInfoExtension" runat="server" DESIGNTIMEDRAGDROP="5275"></asp:Label></TD>
										<TD width="50"></TD>
										<TD class="actual" width="120">Actual:</TD>
										<TD class="actual" width="300">
											<asp:Label id="lblEmpInfoCity" runat="server"></asp:Label></TD>
									</TR>
									<TR>
										<TD class="Requested" width="120">Requested:</TD>
										<TD class="Requested" width="300">
											<asp:Label id="Label20" runat="server"></asp:Label></TD>
										<TD width="50"></TD>
										<TD class="Requested" width="120">Requested:</TD>
										<TD class="Requested" width="300">
											<asp:Label id="Label25" runat="server"></asp:Label></TD>
									</TR>
									<TR>
										<TD class="action" style="HEIGHT: 3px" width="120">Action:</TD>
										<TD class="action" style="HEIGHT: 3px" width="300">
											<asp:RadioButtonList id="rdoExtension" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
												<asp:ListItem Value="0">Accept</asp:ListItem>
												<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
											</asp:RadioButtonList></TD>
										<TD style="HEIGHT: 3px" width="50"></TD>
										<TD class="action" style="HEIGHT: 3px" width="120">Action:</TD>
										<TD class="action" style="HEIGHT: 3px" width="300">
											<asp:RadioButtonList id="rdoStation" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
												<asp:ListItem Value="0">Accept</asp:ListItem>
												<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
											</asp:RadioButtonList></TD>
									</TR>
									<TR>
										<TD class="menubar" width="120" colSpan="2"><STRONG>Email Official:</STRONG></TD>
										<TD width="50"></TD>
										<TD class="menubar" width="120" colSpan="2"><STRONG>Type Of Employemt:</STRONG></TD>
									</TR>
									<TR>
										<TD class="actual" width="120">Actual:</TD>
										<TD class="actual" width="300">
											<asp:Label id="lblEmpInfoEmail" runat="server"></asp:Label></TD>
										<TD width="50"></TD>
										<TD class="actual" width="120">Actual:</TD>
										<TD class="actual" width="300">
											<asp:Label id="lblEmpInfoTOE" runat="server" DESIGNTIMEDRAGDROP="5330"></asp:Label></TD>
									</TR>
									<TR>
										<TD class="Requested" width="120">Requested:</TD>
										<TD class="Requested" width="300">
											<asp:Label id="Label32" runat="server" DESIGNTIMEDRAGDROP="5318"></asp:Label></TD>
										<TD width="50"></TD>
										<TD class="Requested" width="120">Requested:</TD>
										<TD class="Requested" width="300">
											<asp:Label id="Label30" runat="server" DESIGNTIMEDRAGDROP="5333"></asp:Label></TD>
									</TR>
									<TR>
										<TD class="action" width="120">Action:</TD>
										<TD class="action" width="300">
											<asp:RadioButtonList id="rdoEmailOffical" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
												<asp:ListItem Value="0">Accept</asp:ListItem>
												<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
											</asp:RadioButtonList></TD>
										<TD width="50"></TD>
										<TD class="action" width="120">Action:</TD>
										<TD class="action" width="300">
											<asp:RadioButtonList id="rdoTOE" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
												<asp:ListItem Value="0">Accept</asp:ListItem>
												<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
											</asp:RadioButtonList></TD>
									</TR>
									<TR>
										<TD class="menubar" width="120" colSpan="2"><STRONG>SESSI No:</STRONG></TD>
										<TD width="50"></TD>
										<TD class="menubar" width="120" colSpan="2"><STRONG>EOBI No:</STRONG></TD>
									</TR>
									<TR>
										<TD class="actual" style="HEIGHT: 17px" width="120">Actual:</TD>
										<TD class="actual" style="HEIGHT: 17px" width="300">
											<asp:Label id="lblEmpInfoSessi" runat="server" DESIGNTIMEDRAGDROP="5351"></asp:Label></TD>
										<TD style="HEIGHT: 17px" width="50"></TD>
										<TD class="actual" style="HEIGHT: 17px" width="120">Actual:</TD>
										<TD class="actual" style="HEIGHT: 17px" width="300">
											<asp:Label id="lblEmpInfoEObi" runat="server"></asp:Label></TD>
									</TR>
									<TR>
										<TD class="Requested" width="120">Requested:</TD>
										<TD class="Requested" width="300">
											<asp:Label id="Label34" runat="server"></asp:Label></TD>
										<TD width="50"></TD>
										<TD class="Requested" width="120">Requested:</TD>
										<TD class="Requested" width="300">
											<asp:Label id="Label33" runat="server" DESIGNTIMEDRAGDROP="5368"></asp:Label></TD>
									</TR>
									<TR>
										<TD class="action" width="120">Action:</TD>
										<TD class="action" width="300">
											<asp:RadioButtonList id="rdoSessi" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
												<asp:ListItem Value="0">Accept</asp:ListItem>
												<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
											</asp:RadioButtonList></TD>
										<TD width="50"></TD>
										<TD class="action" width="120">Action:</TD>
										<TD class="action" width="300">
											<asp:RadioButtonList id="rdoEOBINO" runat="server" DESIGNTIMEDRAGDROP="5371" RepeatDirection="Horizontal"
												RepeatLayout="Flow">
												<asp:ListItem Value="0">Accept</asp:ListItem>
												<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
											</asp:RadioButtonList></TD>
									</TR>
									<TR>
										<TD class="menubar" width="120" colSpan="2"><STRONG>Employee Location:</STRONG></TD>
										<TD width="50"></TD>
										<TD width="120" colSpan="2"></TD>
									</TR>
									<TR>
										<TD class="actual" width="120">Actual</TD>
										<TD class="actual" width="300">
											<asp:Label id="lblEmpLocation" runat="server"></asp:Label></TD>
										<TD width="50"></TD>
										<TD width="120"></TD>
										<TD width="300"></TD>
									</TR>
									<TR>
										<TD class="Requested" width="120">Requested</TD>
										<TD class="Requested" width="300">
											<asp:Label id="lblRempLocation" runat="server"></asp:Label></TD>
										<TD width="50"></TD>
										<TD width="120"></TD>
										<TD width="300"></TD>
									</TR>
									<TR>
										<TD class="action" width="120">Action</TD>
										<TD class="action" width="300">
											<asp:RadioButtonList id="rdoLocation" runat="server" RepeatDirection="Horizontal" RepeatLayout="Flow">
												<asp:ListItem Value="0">Accept</asp:ListItem>
												<asp:ListItem Value="1" Selected="True">Reject</asp:ListItem>
											</asp:RadioButtonList></TD>
										<TD width="50"></TD>
										<TD width="120"></TD>
										<TD width="300"></TD>
									</TR>
								</TABLE>
							</STRONG><STRONG></STRONG>
						</asp:panel><asp:panel id="pnlFamilyInfo" runat="server" Visible="False"><STRONG>
								<TABLE class="mainformcolor" id="Table5" cellSpacing="0" cellPadding="3" width="750" align="center"
									border="0">
									<TR>
										<TD class="OrangeFormTitle">Family Information:</TD>
									</TR>
									<TR>
										<TD class="menubar"><STRONG>Actual</STRONG></TD>
									</TR>
									<TR>
										<TD>
											<asp:DataGrid id="DataGrid1" runat="server" Width="100%" Visible="False" AutoGenerateColumns="False"
												GridLines="Horizontal" CellPadding="3" BorderWidth="1px" BorderColor="Navy" AllowSorting="True"
												BorderStyle="Solid">
												<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
												<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
												<AlternatingItemStyle CssClass="AlternetItem"></AlternatingItemStyle>
												<ItemStyle ForeColor="#4A3C8C" CssClass="item"></ItemStyle>
												<HeaderStyle Font-Bold="True" ForeColor="Black" CssClass="header"></HeaderStyle>
												<Columns>
													<asp:BoundColumn DataField="name" SortExpression="name" HeaderText="Name"></asp:BoundColumn>
													<asp:BoundColumn DataField="relationship" SortExpression="relationship" HeaderText="RelationShip"></asp:BoundColumn>
													<asp:BoundColumn DataField="maritialstatus" SortExpression="maritialstatus" HeaderText="MaritialStatus"></asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="gender" SortExpression="gender" HeaderText="Gender"></asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="occupation" SortExpression="occupation" HeaderText="Occupation"></asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="DOB" HeaderText="Date Of Birth" DataFormatString="{0:d}"></asp:BoundColumn>
													<asp:BoundColumn DataField="dependent" SortExpression="dependent" HeaderText="Dependent"></asp:BoundColumn>
													<asp:ButtonColumn Visible="False" Text="Edit" CommandName="Select"></asp:ButtonColumn>
													<asp:BoundColumn Visible="False" DataField="pcode"></asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="fdid"></asp:BoundColumn>
												</Columns>
												<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
											</asp:DataGrid></TD>
									</TR>
									<TR>
										<TD class="menubar"><STRONG>Requested:</STRONG></TD>
									</TR>
									<TR>
										<TD>
											<asp:DataGrid id="DataGrid2" runat="server" Width="100%" Visible="False" AutoGenerateColumns="False"
												GridLines="Horizontal" CellPadding="3" BorderWidth="1px" BorderColor="Navy" AllowSorting="True"
												BorderStyle="Solid">
												<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
												<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
												<AlternatingItemStyle CssClass="AlternetItem"></AlternatingItemStyle>
												<ItemStyle ForeColor="#4A3C8C" CssClass="item"></ItemStyle>
												<HeaderStyle Font-Bold="True" ForeColor="Black" CssClass="header"></HeaderStyle>
												<Columns>
													<asp:BoundColumn DataField="name" SortExpression="name" HeaderText="Name"></asp:BoundColumn>
													<asp:BoundColumn DataField="relationship" SortExpression="relationship" HeaderText="RelationShip"></asp:BoundColumn>
													<asp:BoundColumn DataField="maritialstatus" SortExpression="maritialstatus" HeaderText="MaritialStatus"></asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="gender" SortExpression="gender" HeaderText="Gender"></asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="occupation" SortExpression="occupation" HeaderText="Occupation"></asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="DOB" HeaderText="Date Of Birth" DataFormatString="{0:d}"></asp:BoundColumn>
													<asp:BoundColumn DataField="dependent" SortExpression="dependent" HeaderText="Dependent"></asp:BoundColumn>
													<asp:ButtonColumn Text="Accept" CommandName="Select"></asp:ButtonColumn>
													<asp:BoundColumn Visible="False" DataField="fdid"></asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="tempfd_id"></asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="isnew"></asp:BoundColumn>
													<asp:TemplateColumn>
														<ItemTemplate>
															<asp:LinkButton id="famReject" onclick="famChange" runat="server">Reject</asp:LinkButton>
														</ItemTemplate>
													</asp:TemplateColumn>
												</Columns>
												<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
											</asp:DataGrid>
											<asp:Label id="lblRecord" runat="server" Font-Bold="True">No Record Found</asp:Label></TD>
									</TR>
									<TR>
										<TD><STRONG>
												<asp:Panel id="Panel2" runat="server">
													<TABLE class="MainFormColor" id="Table6" cellSpacing="0" cellPadding="3" width="100%" align="center"
														border="0">
														<TR>
															<TD class="OrangeFormTitle" colSpan="3">Update Family Information</TD>
														</TR>
														<TR>
															<TD class="menubar" vAlign="top" width="320"><STRONG>Name Of Relative: </STRONG>
															</TD>
															<TD vAlign="top" width="95"><STRONG><STRONG></STRONG></STRONG></TD>
															<TD class="menubar" vAlign="top" width="320"><STRONG>Relationship:</STRONG></TD>
														</TR>
														<TR>
															<TD vAlign="top" width="320">
																<asp:TextBox id="txtFamilyName" runat="server" Width="100%" CssClass="TextBox"></asp:TextBox></TD>
															<TD vAlign="top" width="95"></TD>
															<TD vAlign="top" width="320">
																<asp:DropDownList id="rdRelation" runat="server" Width="100%" CssClass="textbox">
																	<asp:ListItem Value="0">Select--Relationship</asp:ListItem>
																	<asp:ListItem Value="2">Husband</asp:ListItem>
																	<asp:ListItem Value="3">Wife</asp:ListItem>
																	<asp:ListItem Value="4">Son</asp:ListItem>
																	<asp:ListItem Value="5">Daughter</asp:ListItem>
																</asp:DropDownList></TD>
														</TR>
														<TR>
															<TD class="menubar" vAlign="top" width="320"><STRONG>Maritial Status:</STRONG></TD>
															<TD vAlign="top" width="95"><STRONG><STRONG></STRONG></STRONG></TD>
															<TD class="menubar" vAlign="top" width="320"><STRONG>Data Of Birth:</STRONG></TD>
														</TR>
														<TR>
															<TD vAlign="top" width="320">
																<asp:DropDownList id="rdStatus" runat="server" Width="100%" CssClass="textbox">
																	<asp:ListItem Value="0">Select--Marital  Status</asp:ListItem>
																	<asp:ListItem Value="1">Single</asp:ListItem>
																	<asp:ListItem Value="2">Married</asp:ListItem>
																	<asp:ListItem Value="3">Divorced</asp:ListItem>
																	<asp:ListItem Value="4">Widow</asp:ListItem>
																	<asp:ListItem Value="5">Separated</asp:ListItem>
																</asp:DropDownList></TD>
															<TD vAlign="top" width="95"></TD>
															<TD vAlign="top" width="320">
																<ew:CalendarPopup id="CalendarPopup1" runat="server">
																	<TextboxLabelStyle CssClass="textbox"></TextboxLabelStyle>
																	<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></WeekdayStyle>
																	<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="Yellow"></MonthHeaderStyle>
																	<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																		BackColor="AntiqueWhite"></OffMonthStyle>
																	<ButtonStyle CssClass="button"></ButtonStyle>
																	<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></GoToTodayStyle>
																	<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="LightGoldenrodYellow"></TodayDayStyle>
																	<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="Orange"></DayHeaderStyle>
																	<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="LightGray"></WeekendStyle>
																	<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="Yellow"></SelectedDateStyle>
																	<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></ClearDateStyle>
																	<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></HolidayStyle>
																</ew:CalendarPopup></TD>
														</TR>
														<TR>
															<TD class="menubar" vAlign="top" width="320"><STRONG>Dependent:</STRONG></TD>
															<TD vAlign="top" width="95"><STRONG><STRONG></STRONG></STRONG></TD>
															<TD class="menubar" vAlign="top" width="320"><STRONG>Gender:</STRONG></TD>
														</TR>
														<TR>
															<TD vAlign="top" width="320">
																<asp:DropDownList id="ddDependent" runat="server" Width="100%" CssClass="textbox">
																	<asp:ListItem Value="0">Select--Option</asp:ListItem>
																	<asp:ListItem Value="1">Yes</asp:ListItem>
																	<asp:ListItem Value="2">No</asp:ListItem>
																</asp:DropDownList></TD>
															<TD vAlign="top" width="95"></TD>
															<TD vAlign="top" width="320">
																<asp:DropDownList id="rdFamilyGender" runat="server" Width="100%" CssClass="textbox">
																	<asp:ListItem Value="0">Select--Gender</asp:ListItem>
																	<asp:ListItem Value="1">Male</asp:ListItem>
																	<asp:ListItem Value="2">Female</asp:ListItem>
																</asp:DropDownList></TD>
														</TR>
														<TR>
															<TD class="menubar" vAlign="top" width="320"><STRONG>Occupation Of Spouse:</STRONG></TD>
															<TD vAlign="top" width="95"><STRONG></STRONG></TD>
															<TD class="menubar" vAlign="top" width="320"><STRONG></STRONG></TD>
														</TR>
														<TR>
															<TD vAlign="top" width="320">
																<asp:TextBox id="txtOccupation" runat="server" Width="100%" CssClass="textbox"></asp:TextBox></TD>
															<TD vAlign="top" width="95"></TD>
															<TD vAlign="top" width="320"></TD>
														</TR>
														<TR>
															<TD vAlign="top" colSpan="3">
																<asp:Button id="btnNew" runat="server" Text="Add New" Enabled="False"></asp:Button>
																<asp:Button id="btnFupdate" runat="server" Visible="False" Text="Update"></asp:Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
																<asp:LinkButton id="lnkClose" runat="server">Close</asp:LinkButton>&nbsp;&nbsp;&nbsp;
															</TD>
														</TR>
													</TABLE>
												</asp:Panel></STRONG></TD>
									</TR>
								</TABLE>
							</STRONG>
						</asp:panel><asp:panel id="pnlEducation" runat="server" Visible="False" dir="ltr"><STRONG>
								<TABLE class="mainformcolor" id="Table8" cellSpacing="0" cellPadding="3" width="750" align="center"
									border="0">
									<TR>
										<TD class="OrangeFormTitle">Educational Information:</TD>
									</TR>
									<TR>
										<TD class="menubar"><STRONG>Actual:</STRONG></TD>
									</TR>
									<TR>
										<TD>
											<asp:DataGrid id="DataGrid3" runat="server" Width="100%" Visible="False" AutoGenerateColumns="False"
												GridLines="Horizontal" CellPadding="3" BorderWidth="1px" BorderColor="Navy" AllowSorting="True"
												BorderStyle="None">
												<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
												<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
												<AlternatingItemStyle CssClass="alternetitem"></AlternatingItemStyle>
												<ItemStyle CssClass="item"></ItemStyle>
												<HeaderStyle Font-Bold="True" ForeColor="Black" CssClass="header"></HeaderStyle>
												<Columns>
													<asp:BoundColumn DataField="degree" SortExpression="degree" HeaderText="Degree"></asp:BoundColumn>
													<asp:BoundColumn DataField="institute" SortExpression="institute" HeaderText="Institute"></asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="durationfrom" SortExpression="duration" HeaderText="Duration From"
														DataFormatString="{0:d}"></asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="durationto" SortExpression="yearpassing" HeaderText="Duration To"
														DataFormatString="{0:d}"></asp:BoundColumn>
													<asp:BoundColumn DataField="result" SortExpression="result" HeaderText="Result"></asp:BoundColumn>
													<asp:BoundColumn DataField="majors" SortExpression="majors" HeaderText="Majors"></asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="achievements" SortExpression="achievements" HeaderText="Distinction"></asp:BoundColumn>
													<asp:ButtonColumn Visible="False" Text="Edit" CommandName="Select"></asp:ButtonColumn>
												</Columns>
												<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
											</asp:DataGrid></TD>
									</TR>
									<TR>
										<TD class="menubar"><STRONG>Requested:</STRONG></TD>
									</TR>
									<TR>
										<TD>
											<asp:DataGrid id="DataGrid4" runat="server" Width="100%" Visible="False" AutoGenerateColumns="False"
												GridLines="Horizontal" CellPadding="3" BorderWidth="1px" BorderColor="Navy" AllowSorting="True"
												BorderStyle="None">
												<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
												<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
												<AlternatingItemStyle CssClass="alternetitem"></AlternatingItemStyle>
												<ItemStyle ForeColor="#4A3C8C" CssClass="item"></ItemStyle>
												<HeaderStyle Font-Bold="True" ForeColor="Black" CssClass="header"></HeaderStyle>
												<Columns>
													<asp:BoundColumn DataField="degree" SortExpression="degree" HeaderText="Degree"></asp:BoundColumn>
													<asp:BoundColumn DataField="institute" SortExpression="institute" HeaderText="Institute"></asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="durationfrom" SortExpression="duration" HeaderText="Duration From"
														DataFormatString="{0:d}"></asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="durationto" SortExpression="yearpassing" HeaderText="Duration To"
														DataFormatString="{0:d}"></asp:BoundColumn>
													<asp:BoundColumn DataField="result" SortExpression="result" HeaderText="Result"></asp:BoundColumn>
													<asp:BoundColumn DataField="majors" SortExpression="majors" HeaderText="Majors"></asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="achievements" SortExpression="achievements" HeaderText="Distinction"></asp:BoundColumn>
													<asp:ButtonColumn Text="Accept" CommandName="Select"></asp:ButtonColumn>
													<asp:BoundColumn Visible="False" DataField="eduinfoid"></asp:BoundColumn>
													<asp:BoundColumn Visible="False" DataField="p_id"></asp:BoundColumn>
													<asp:TemplateColumn>
														<ItemTemplate>
															<asp:LinkButton id="eduReject" onclick="eduChange" runat="server">Reject</asp:LinkButton>
														</ItemTemplate>
													</asp:TemplateColumn>
													<asp:BoundColumn DataField="type"></asp:BoundColumn>
												</Columns>
												<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
											</asp:DataGrid>
											<asp:Label id="lblEdBoRecord" runat="server" Font-Bold="True">No Record Found</asp:Label></TD>
									</TR>
									<TR>
										<TD>
											<TABLE class="MainFormColor" id="Table9" cellSpacing="0" cellPadding="3" width="100%" align="center"
												border="0">
												<TR>
													<TD class="OrangeFormTitle" colSpan="3">Update Educational Information:</TD>
												</TR>
												<TR>
													<TD class="menubar" vAlign="top" width="320"><STRONG>Degree: </STRONG>
													</TD>
													<TD vAlign="top" width="95"></TD>
													<TD class="menubar" vAlign="top" width="320"><STRONG>Institute:</STRONG></TD>
												</TR>
												<TR>
													<TD vAlign="top" width="320">
														<asp:DropDownList id="txtDegree" runat="server" Width="100%" CssClass="textbox">
															<asp:ListItem Value="0">Select--Degree</asp:ListItem>
														</asp:DropDownList></TD>
													<TD vAlign="top" width="95"></TD>
													<TD vAlign="top" width="320">
														<asp:DropDownList id="txtInstitute" runat="server" Width="100%" CssClass="textbox">
															<asp:ListItem Value="0">Select--Institute</asp:ListItem>
														</asp:DropDownList></TD>
												</TR>
												<TR>
													<TD vAlign="top" width="320">
														<asp:TextBox id="txtOtherDegree" runat="server" Width="240px" Visible="False" CssClass="textbox"></asp:TextBox>
														<asp:Button id="btnAdd" runat="server" Text="Add" CssClass="button"></asp:Button></TD>
													<TD vAlign="top" width="95"></TD>
													<TD vAlign="top" width="320">
														<asp:TextBox id="txtOtherInstitute" runat="server" Width="239px" Visible="False" CssClass="textbox"></asp:TextBox>
														<asp:Button id="btnInstituteAdd" runat="server" Text="Add" CssClass="button"></asp:Button></TD>
												</TR>
												<TR>
													<TD vAlign="top" width="320">
														<asp:Label id="lblDegree" runat="server">Add above degree in database press add button</asp:Label></TD>
													<TD vAlign="top" width="95"></TD>
													<TD vAlign="top" width="320">
														<asp:Label id="lblInstitute" runat="server">Add above Institute in database press add button</asp:Label></TD>
												</TR>
												<TR>
													<TD class="menubar" vAlign="top" width="320"><STRONG>Duration From:</STRONG></TD>
													<TD vAlign="top" width="95"></TD>
													<TD class="menubar" vAlign="top" width="320"><STRONG>Duration To:</STRONG></TD>
												</TR>
												<TR>
													<TD vAlign="top" width="320">
														<asp:DropDownList id="DurationFrom" runat="server" Width="100%" CssClass="textbox">
															<asp:ListItem Value="0">Select--Start Date</asp:ListItem>
														</asp:DropDownList></TD>
													<TD vAlign="top" width="95"></TD>
													<TD vAlign="top" width="320">
														<asp:DropDownList id="DurationTo" runat="server" Width="100%" CssClass="textbox">
															<asp:ListItem Value="0">Select--End Date</asp:ListItem>
														</asp:DropDownList></TD>
												</TR>
												<TR>
													<TD class="menubar" vAlign="top" width="320"><STRONG>Result:</STRONG></TD>
													<TD vAlign="top" width="95"></TD>
													<TD class="menubar" vAlign="top" width="320"><STRONG>Majors:</STRONG></TD>
												</TR>
												<TR>
													<TD vAlign="top" width="320">
														<asp:DropDownList id="txtResult" runat="server" Width="100%" CssClass="textbox">
															<asp:ListItem Value="0">Select--Results</asp:ListItem>
															<asp:ListItem Value="A+ Grade">A+ Grade</asp:ListItem>
															<asp:ListItem Value="A Grade">A Grade</asp:ListItem>
															<asp:ListItem Value="B Grade">B Grade</asp:ListItem>
															<asp:ListItem Value="C Grade">C Grade</asp:ListItem>
															<asp:ListItem Value="D Grade">D Grade</asp:ListItem>
															<asp:ListItem Value="F Grade">F Grade</asp:ListItem>
															<asp:ListItem Value="2.1">2.1</asp:ListItem>
															<asp:ListItem Value="2.2">2.2</asp:ListItem>
															<asp:ListItem Value="2.3">2.3</asp:ListItem>
															<asp:ListItem Value="2.4">2.4</asp:ListItem>
															<asp:ListItem Value="2.5">2.5</asp:ListItem>
															<asp:ListItem Value="2.6">2.6</asp:ListItem>
															<asp:ListItem Value="2.7">2.7</asp:ListItem>
															<asp:ListItem Value="2.8">2.8</asp:ListItem>
															<asp:ListItem Value="2.9">2.9</asp:ListItem>
															<asp:ListItem Value="3.0">3.0</asp:ListItem>
															<asp:ListItem Value="3.1">3.1</asp:ListItem>
															<asp:ListItem Value="3.2">3.2</asp:ListItem>
															<asp:ListItem Value="3.3">3.3</asp:ListItem>
															<asp:ListItem Value="3.4">3.4</asp:ListItem>
															<asp:ListItem Value="3.5">3.5</asp:ListItem>
															<asp:ListItem Value="3.6">3.6</asp:ListItem>
															<asp:ListItem Value="3.7">3.7</asp:ListItem>
															<asp:ListItem Value="3.8">3.8</asp:ListItem>
															<asp:ListItem Value="3.9">3.9</asp:ListItem>
															<asp:ListItem Value="4.0">4.0</asp:ListItem>
															<asp:ListItem Value="Passed">Passed</asp:ListItem>
															<asp:ListItem Value="Fail">Fail</asp:ListItem>
															<asp:ListItem Value="Result Awaited">Result Awaited</asp:ListItem>
														</asp:DropDownList></TD>
													<TD vAlign="top" width="95"></TD>
													<TD vAlign="top" width="320">
														<asp:TextBox id="txtOtherMajors" runat="server" Width="100%" Visible="False" CssClass="textbox"></asp:TextBox></TD>
												</TR>
												<TR>
													<TD class="menubar" vAlign="top" colSpan="3"><STRONG>Distinction:</STRONG></TD>
												</TR>
												<TR>
													<TD vAlign="top" colSpan="3">
														<asp:TextBox id="txtAchievements" runat="server" Width="100%" CssClass="TextBox" TextMode="MultiLine"
															Rows="3"></asp:TextBox></TD>
												</TR>
												<TR>
													<TD vAlign="top" colSpan="3">
														<asp:Button id="btnEdNew" runat="server" Visible="False" Text="Add New" Enabled="False"></asp:Button>
														<asp:Button id="btnEdUpdate" runat="server" Text="Update"></asp:Button>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
														&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;
														<asp:LinkButton id="lnkEclose" runat="server">Close</asp:LinkButton></TD>
												</TR>
											</TABLE>
										</TD>
									</TR>
								</TABLE>
							</STRONG>
						</asp:panel>&nbsp;<asp:button id="btnUpdate" runat="server" Font-Bold="True" Text="Update"></asp:button><asp:button id="btnReject" runat="server" Font-Bold="True" Text="Reject"></asp:button><BR>
						&nbsp;&nbsp;<asp:label id="lblMsg" runat="server" Font-Bold="True" Visible="False"></asp:label>
					</td>
				</tr>
				<tr>
					<td vAlign="middle" align="center" height="20">Copyright © 2005 Independent Media 
						Corporation <A href="http://www.geo.tv">www.geo.tv</A></td>
				</tr>
			</table>
		</form>
	</body>
</HTML>
