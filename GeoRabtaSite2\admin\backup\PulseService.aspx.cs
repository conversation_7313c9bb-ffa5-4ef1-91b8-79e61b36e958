using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Xml;
using System.Data.SqlClient;


namespace GeoRabtaSite.admin
{
	/// <summary>
	/// Summary description for PulseService.
	/// </summary>
	public class PulseService : System.Web.UI.Page
	{
		
		private string [] months={"Jan", "Feb", "Mar", "Apr", "May","Jun","Jul","Aug","Sep","Oct", "Nov", "Dec"};
		private void GetMonths()
		{
			XmlTextWriter xtw=new XmlTextWriter(Response.OutputStream,System.Text.Encoding.UTF8);
			xtw.Formatting=Formatting.Indented;
			Response.ContentType="text/xml";
			xtw.WriteStartDocument();
			xtw.WriteStartElement("monthdata");

			int k=0;
			for(int j=0;j<12;j++)
			{
				k=j+1;
				xtw.WriteStartElement("month");
				xtw.WriteStartElement("monthno");	xtw.WriteString(k.ToString());	xtw.WriteEndElement();
				xtw.WriteStartElement("monthname");	xtw.WriteString(months[j]);	xtw.WriteEndElement();
				xtw.WriteEndElement();
			}
			

			xtw.WriteEndElement();
			xtw.Flush();
			xtw.Close();
			Response.End();

		}


		private void GetDept()
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			SqlDataAdapter da=new SqlDataAdapter("SELECT DISTINCT TOP (100) PERCENT dbo.t_Department.deptid, dbo.t_Department.deptname " +
				" FROM dbo.t_Pulse INNER JOIN " +
				" dbo.t_Department ON dbo.t_Pulse.deptid = dbo.t_Department.deptid " +
				" ORDER BY dbo.t_Department.deptname",conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			XmlTextWriter xtw=new XmlTextWriter(Response.OutputStream,System.Text.Encoding.UTF8);
			xtw.Formatting=Formatting.Indented;
			Response.ContentType="text/xml";
			xtw.WriteStartDocument();
			xtw.WriteStartElement("departments");

			
			xtw.WriteStartElement("deparment");
			xtw.WriteStartElement("deptid");	xtw.WriteString("");	xtw.WriteEndElement();
			xtw.WriteStartElement("deptname");	xtw.WriteString("All Departments");	xtw.WriteEndElement();
			xtw.WriteEndElement();

			for(int j=0;j<ds.Tables[0].Rows.Count;j++)
			{
				xtw.WriteStartElement("deparment");
				xtw.WriteStartElement("deptid");	xtw.WriteString(ds.Tables[0].Rows[j]["deptid"].ToString());	xtw.WriteEndElement();
				xtw.WriteStartElement("deptname");	xtw.WriteString(ds.Tables[0].Rows[j]["deptname"].ToString());	xtw.WriteEndElement();
				xtw.WriteEndElement();
			}
			

			xtw.WriteEndElement();
			xtw.Flush();
			xtw.Close();
			Response.End();
		}

		private void GetStation()
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			
			SqlDataAdapter da=new SqlDataAdapter("SELECT DISTINCT TOP (100) PERCENT dbo.t_City.cityid, dbo.t_City.cityname " +
				" FROM dbo.t_Pulse INNER JOIN " +
				" dbo.t_City ON dbo.t_Pulse.station = dbo.t_City.cityid " +
				" ORDER BY dbo.t_City.cityname",conn);

			DataSet ds=new DataSet();
			da.Fill(ds);
			XmlTextWriter xtw=new XmlTextWriter(Response.OutputStream,System.Text.Encoding.UTF8);
			xtw.Formatting=Formatting.Indented;
			Response.ContentType="text/xml";
			xtw.WriteStartDocument();
			xtw.WriteStartElement("stations");

			xtw.WriteStartElement("station");
			xtw.WriteStartElement("stationid");	xtw.WriteString("");	xtw.WriteEndElement();
			xtw.WriteStartElement("stationname");	xtw.WriteString("All Stations");	xtw.WriteEndElement();
			xtw.WriteEndElement();
			
			for(int j=0;j<ds.Tables[0].Rows.Count;j++)
			{
				xtw.WriteStartElement("station");
				xtw.WriteStartElement("stationid");	xtw.WriteString(ds.Tables[0].Rows[j]["cityid"].ToString());	xtw.WriteEndElement();
				xtw.WriteStartElement("stationname");	xtw.WriteString(ds.Tables[0].Rows[j]["cityname"].ToString());	xtw.WriteEndElement();
				xtw.WriteEndElement();
			}
			

			xtw.WriteEndElement();
			xtw.Flush();
			xtw.Close();
			Response.End();
		}

		private void GetData()
		{
			string dfrom=Request.QueryString["dfrom"]+"";
			string dto=Request.QueryString["dto"]+"";
			string deptid=Request.QueryString["deptid"]+"";
			string station=Request.QueryString["station"]+"";
			if(deptid!="")
				deptid=" AND (deptid = "+deptid+") ";
			if(station!="")
				station=" AND (station = "+station+") ";

			string sql=" SELECT     TOP (100) PERCENT YEAR(pulsecreateon) * 100 + MONTH(pulsecreateon) AS YearMonth, COUNT(pid) AS PulseCount, '' as YM " +
				" FROM         dbo.t_Pulse " + 
				" WHERE     (YEAR(pulsecreateon) * 100 + MONTH(pulsecreateon) BETWEEN "+dfrom+" AND "+dto+") "+station+deptid +
				" GROUP BY YEAR(pulsecreateon) * 100 + MONTH(pulsecreateon) " +
				" ORDER BY YearMonth ";

			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet("mis");
			da.Fill(ds,"result");

			string yearmonth="";
			string ym="";
			int temp=0;
			for(int j=0;j<ds.Tables[0].Rows.Count;j++)
			{
				yearmonth=ds.Tables[0].Rows[j]["yearmonth"].ToString();
				ym=yearmonth.Substring(4,2);
				temp=int.Parse(ym);
				ym=months[temp-1]+", "+yearmonth.Substring(0,4);
				ds.Tables[0].Rows[j]["ym"]=ym;
			}
			ds.WriteXml(Response.OutputStream);
			Response.End();
			//			XmlTextWriter xtw=new XmlTextWriter(Response.OutputStream,System.Text.Encoding.UTF8);
			//			xtw.Formatting=Formatting.Indented;
			//			Response.ContentType="text/xml";
			//			xtw.WriteStartDocument();
			//			xtw.WriteStartElement("mis");
			//			xtw.WriteEndElement();
			//			for(int j=0;j<ds.Tables[0].Rows.Count;j++)
			//			{
			//				xtw.WriteStartElement("rr");
			//				xtw.WriteStartElement("month");	xtw.WriteString(ds.Tables[0].Rows[j]["yearmonth"].ToString());	xtw.WriteEndElement();
			//				xtw.WriteStartElement("count");	xtw.WriteString(ds.Tables[0].Rows[j]["PulseCount"].ToString());	xtw.WriteEndElement();
			//				xtw.WriteEndElement();
			//			}
			//			xtw.Flush();
			//			xtw.Close();
			//			Response.End();

			
			
		}

		private void GetPulseCatData()
		{
			string dfrom=Request.QueryString["dfrom"]+"";
			string dto=Request.QueryString["dto"]+"";

			string deptid=Request.QueryString["deptid"]+"";
			string station=Request.QueryString["station"]+"";

			if(deptid!="")
				deptid=" AND (pls.deptid = "+deptid+") ";
			if(station!="")
				station=" AND (pls.station = "+station+") ";


			string sql="SELECT     TOP (100) PERCENT COUNT(pls.pid) AS pulsecount, pc.pulsecategory,pc.pulseid " +
				" FROM         dbo.t_Pulse AS pls INNER JOIN " +
				"                       dbo.t_PulseCategory AS pc ON pls.pulsecategory = pc.pulseid " +
				" WHERE     (YEAR(pls.pulsecreateon) * 100 + MONTH(pls.pulsecreateon) BETWEEN "+dfrom+" AND "+dto+") " + station + deptid+
				" GROUP BY pc.pulsecategory, pc.pulseid " +
				" ORDER BY pc.pulsecategory ";

			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet("pulsecategories");
			da.Fill(ds,"result");
			ds.WriteXml(Response.OutputStream);
			Response.End();

			
		}


		private void GetAction()
		{
			string dfrom=Request.QueryString["dfrom"]+"";
			string dto=Request.QueryString["dto"]+"";

			string deptid=Request.QueryString["deptid"]+"";
			string station=Request.QueryString["station"]+"";

			if(deptid!="")
				deptid=" AND (deptid = "+deptid+") ";
			if(station!="")
				station=" AND (station = "+station+") ";

			string sql=" SELECT     COUNT(pid) AS pulsecount,currentactioncode, Action " +
				" FROM         dbo.v_PulseAction "+
				" WHERE     (YearMonth BETWEEN "+dfrom+" AND "+dto+") "+deptid+station +
				" GROUP BY currentactioncode,Action ";

			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet("pulseActions");
			da.Fill(ds,"actions");
			ds.WriteXml(Response.OutputStream);
			Response.End();
			
		}

		private void GetPulseIniData()
		{
			string dfrom=Request.QueryString["dfrom"]+"";
			string dto=Request.QueryString["dto"]+"";

			string deptid=Request.QueryString["deptid"]+"";
			string station=Request.QueryString["station"]+"";

			if(deptid!="")
				deptid=" AND (pls.deptid = "+deptid+") ";
			if(station!="")
				station=" AND (pls.station = "+station+") ";

			string sql=" SELECT TOP (100) PERCENT COUNT(pls.pid) AS pulsecount, emp.name as pname,emp.pcode "+
				" FROM dbo.t_Pulse AS pls INNER JOIN " +
				" dbo.t_Employee AS emp ON pls.pcode = emp.pcode " +
				" WHERE (YEAR(pls.pulsecreateon) * 100 + MONTH(pls.pulsecreateon) BETWEEN "+dfrom+" AND "+dto+") "+station+" " +deptid+
				" GROUP BY emp.name,emp.pcode " +
				" ORDER BY emp.name; ";

			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet("pulseInitiator");
			da.Fill(ds,"result");
			ds.WriteXml(Response.OutputStream);
			Response.End();
			
		}

		private void ShowPluses()
		{

			string dfrom=Request.QueryString["dfrom"]+"";
			string dto=Request.QueryString["dto"]+"";

			string deptid=Request.QueryString["deptid"]+"";
			string station=Request.QueryString["station"]+"";
			string filter=Request.QueryString["filter"]+"";
			string key=Request.QueryString["key"]+"";

			if(filter!="" && key!="")
				filter=" AND (pls."+filter+" = '"+key+"') ";
			if(filter!="" && key=="")
				filter=" AND (pls."+filter+" is null) ";

			if(deptid!="")
				deptid=" AND (pls.deptid="+deptid+") ";

			if(station!="")
				station=" AND (pls.station="+station+") ";
			string sql="SELECT pls.pid, emp.name, pls.title, pls.description, dpt.deptname, pls.pulsecreateon, pls.currentstatus, pls.currentactioncode, pls.closedby, pls.closeddate, " +
				" c.pulsecategory, YEAR(pls.pulsecreateon) * 100 + MONTH(pls.pulsecreateon) AS yearmont, dbo.t_City.cityname " +
				" FROM dbo.t_Pulse AS pls INNER JOIN " +
				" dbo.t_Employee AS emp ON pls.pcode = emp.pcode INNER JOIN " +
				" dbo.t_Department AS dpt ON pls.deptid = dpt.deptid INNER JOIN " +
				" dbo.t_PulseCategory AS c ON pls.pulsecategory = c.pulseid INNER JOIN " +
				" dbo.t_City ON pls.station = dbo.t_City.cityid " +
				" WHERE (YEAR(pls.pulsecreateon) * 100 + MONTH(pls.pulsecreateon) BETWEEN "+dfrom+" AND "+dto+") " + filter + station+deptid;

			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet("pulsedata");
			da.Fill(ds,"pulses");
			ds.WriteXml(Response.OutputStream);
			Response.End();
				
		}

		private void ShowBlog()
		{
			string pulseid=Request.QueryString["pulseid"]+"";
			if(pulseid!="")
			{
				SqlConnection conn=new SqlConnection(Connection.ConnectionString);
				conn.Open();
				string sql="SELECT TOP (100) PERCENT pulseid, progressblog AS blog, CASE psendby WHEN 'Raabta' THEN 'Raabta' ELSE " +
					" (SELECT name " +
					" FROM t_employee " +
					" WHERE pcode = psendby) END AS psendby, pdate, CASE WHEN actioncode IS NULL THEN '' ELSE " +
					" (SELECT actioncode " +
					" FROM dbo.t_PulseActionCode AS ac " +
					" WHERE (ac.actionid = pb.actioncode)) END AS Action, interimcloseddate " +
					" FROM dbo.t_PulseProgressBlog AS pb " +
					" WHERE (pulseid = '"+pulseid+"') " +
					" ORDER BY pdate ";
				SqlDataAdapter da=new SqlDataAdapter(sql,conn);
				DataSet ds=new DataSet("blogs");
				da.Fill(ds,"blog");
				//string temp="";
				//for(int j=0;j<ds.Tables[0].Rows.Count;j++)
				//{
				//	temp+="<b>"+ds.Tables[0].Rows[j]["psendby"].ToString()+"</b>  ("+DateTime.Parse(ds.Tables[0].Rows[j]["pdate"].ToString()).ToString("d MMM, yyyy hh:mm")+")<br>";
				//	temp+=ds.Tables[0].Rows[j]["blog"].ToString()+"<br>";
				//	temp+="<i>"+ds.Tables[0].Rows[j]["action"].ToString()+"</i><br><br>";
				//
				//}
				ds.WriteXml(Response.OutputStream);
				Response.End();

			}
			else
			{
				Response.Write("");
				Response.End();
			}
		}

		private void Page_Load(object sender, System.EventArgs e)
		{
			if(Session["user_id"]!="")
			{
				// Put user code to initialize the page here
				string tag=Request.QueryString["tag"]+"";
				if(tag=="month")
				{
					GetMonths();
				}
				if(tag=="getdept")
				{
					GetDept();
				}
				if(tag=="getstation")
				{
					GetStation();
				}
				if(tag=="data")
				{
					GetData();
				}
				if(tag=="cat")
				{
					GetPulseCatData();
				}
				if(tag=="Initiator")
				{
					GetPulseIniData();
				}
				if(tag=="pulses")
				{
					ShowPluses();
				}
				if(tag=="blog")
				{
					ShowBlog();
				}
				if(tag=="action")
				{
					GetAction();
				}
			}
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion
	}
}
