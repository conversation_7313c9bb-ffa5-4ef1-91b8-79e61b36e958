using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for EmpEducation.
	/// </summary>
	public class EmpEducation : System.Web.UI.Page
	{
		protected System.Web.UI.WebControls.DataGrid dgEducation;
		protected System.Web.UI.WebControls.LinkButton lbAddNewEdu;
		protected System.Web.UI.WebControls.DataGrid dgEducationReq;
		protected System.Web.UI.WebControls.Panel pnlEducation;
		protected System.Web.UI.WebControls.TextBox txtX;
		protected System.Web.UI.WebControls.Label lblInvisiblity;
		protected System.Web.UI.WebControls.Button btnECancel;
		protected System.Web.UI.WebControls.Button btnEduDel;
		protected System.Web.UI.WebControls.Button btnEdNew;
		protected System.Web.UI.WebControls.TextBox txtAchievements;
		protected System.Web.UI.WebControls.DropDownList DurationTo;
		protected System.Web.UI.WebControls.DropDownList DurationFrom;
		protected System.Web.UI.WebControls.TextBox txtOtherMajors;
		protected System.Web.UI.WebControls.DropDownList txtResult;
		protected System.Web.UI.WebControls.TextBox txtOtherInstitute;
		protected System.Web.UI.WebControls.DropDownList txtInstitute;
		protected System.Web.UI.WebControls.TextBox txtOtherDegree;
		protected System.Web.UI.WebControls.DropDownList txtDegree;
		protected System.Web.UI.WebControls.Label lbleduinfoid;
		protected System.Web.UI.WebControls.Panel Panel3;
		protected System.Web.UI.HtmlControls.HtmlTable updateEducation;
		protected System.Web.UI.HtmlControls.HtmlTableCell dropDown;
		protected System.Web.UI.HtmlControls.HtmlInputFile fileEdu;
		protected System.Web.UI.WebControls.TextBox TextBox1;
		protected System.Web.UI.WebControls.TextBox txtY;
		protected System.Web.UI.WebControls.Label lblMsg;
		protected System.Web.UI.WebControls.Label lblEmpInfo;
		protected System.Web.UI.WebControls.Label lblPCode;
		protected System.Web.UI.WebControls.Label lblEduToolTip;
		protected System.Web.UI.WebControls.Panel Panel1;
		protected System.Web.UI.WebControls.HyperLink hlMyRequests;
		protected System.Web.UI.WebControls.ImageButton imgMyGrievance;
		protected System.Web.UI.WebControls.ImageButton ImgMyAttendance;
		protected System.Web.UI.WebControls.ImageButton imgMyLeave;
		protected System.Web.UI.WebControls.ImageButton ibMySelf;
		protected System.Web.UI.WebControls.ImageButton ibSalary;
		protected System.Web.UI.WebControls.ImageButton ImageButton5;
		protected System.Web.UI.WebControls.HyperLink hlMyExp;
		protected System.Web.UI.WebControls.ImageButton imgTraining;
		protected System.Web.UI.WebControls.ImageButton ImageButton4;
		protected System.Web.UI.WebControls.ImageButton ImageButton3;
		protected System.Web.UI.WebControls.ImageButton ImageButton2;
		protected System.Web.UI.WebControls.ImageButton ImageButton1;
		protected System.Data.SqlClient.SqlConnection conn;
	

		public string GetTooltip(int ID)
		{
			SqlConnection con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select tooltip from t_fieldsmgt where f_id="+ID+"",con);
			string message="";
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				message=rd[0].ToString();	
			}
			message+="<br>";
			message=HttpUtility.HtmlEncode(message);
			message=message.Replace("\r\n","<br/>");
			rd.Close();
			cmd=new SqlCommand("select d.documentname from t_documentsrequired d,t_fieldmgtdocEmp f where f.documentbyemployee=d.d_id and f.f_id="+ID+"",con);
			rd=cmd.ExecuteReader();
			message+="<ul>";
			while(rd.Read())
			{
				//message+="<STRONG>/STRONG> "+rd[0].ToString()+"<br>";
				message+="<li>"+HttpUtility.HtmlEncode(rd[0].ToString()).Replace("\r\n","<br/>")+"</li>";
			}
			message+="</ul>";
			rd.Close();
			con.Close();
			//Response.Write(message);
			return message;
		}

		private void FillDDL(DropDownList ddl, string table, string val, string key, string condition, string title, SqlConnection con)
		{
			if (condition!="")
				condition="where "+condition;
			string sql="select "+key+","+val+" from "+table+" "+condition+" order by "+val;
			SqlDataAdapter da=new SqlDataAdapter(sql,con);
			DataSet ds=new DataSet();
			da.Fill(ds);
			ddl.Items.Clear();
			ddl.Items.Add("Select - "+title);
			for(int j=0;j<ds.Tables[0].Rows.Count;j++)
			{
				ddl.Items.Add(new ListItem(ds.Tables[0].Rows[j][val].ToString(),
					ds.Tables[0].Rows[j][key].ToString()));
			}
			
		}
		private void lbAddNewEdu_Click(object sender, System.EventArgs e)
		{
			ResetEduForm();
			lbAddNewEdu.Visible=false;
			Panel3.Visible=true;
			dgEducation.SelectedIndex=-1;
			dgEducationReq.SelectedIndex=-1;
			lbleduinfoid.Text="";
			pnlEducation.Visible=false;
		}

		private string GetTimeline(string wfid)
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			string sql=" SELECT timeline " +
				" FROM dbo.t_fieldsmgt " +
				" WHERE (f_id = "+wfid+") ";
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			return ds.Tables[0].Rows[0][0].ToString();

		}


		private string GetReplyMsg(string type, string wfid)
		{
			string sql="SELECT "+type+" FROM dbo.t_fieldsmgt WHERE (f_id = "+wfid+")";
			SqlConnection conn2=new SqlConnection(Connection.ConnectionString);
			conn2.Open();
			SqlDataAdapter da=new SqlDataAdapter(sql,conn2);
			DataSet ds=new DataSet();
			da.Fill(ds);
			
			conn2.Close();
			conn2.Dispose();
			return ds.Tables[0].Rows[0][type].ToString();
		}

		private void SaveEduRequest()
		{
			string replyatrequest=GetReplyMsg("replyatrequest","38");
			string sql="";

			string gid=Guid.NewGuid().ToString();
			string fid=gid+".jpg";
			gid=fid;

			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			SqlTransaction tran=conn.BeginTransaction();

			if(isEduInfoRequested(lbleduinfoid.Text))
			{
				string ssql="update t_EducationInforequest set AppFlag=4 where eduinfoid=" + lbleduinfoid.Text + " and AppFlag=2 and pcode='"+Session["user_id"].ToString()+"'";
				SqlCommand cmd2=new SqlCommand(ssql,conn);
				cmd2.Transaction=tran;
				cmd2.ExecuteNonQuery();
				Bulletin.PostReqMessage(conn,tran,"Request Canceled By User","GEO Raabta",Session["user_id"].ToString(),2,"Education","t_educationInfoRequest");
			}

//			if(lbleduinfoid.Text!="")
//			{
//				SqlCommand cmd2=new SqlCommand("UPDATE t_educationInfoRequest SET AppFlag=0,AppRejDate = getdate(), AppRejBy = '"+Session["user_id"].ToString()+"' where pcode = '"+Session["user_id"].ToString()+"' and Addflag = 2 and eduinfoid = "+lbleduinfoid.Text ,conn);
//				cmd2.Transaction=tran;
//				cmd2.ExecuteNonQuery();
//				Bulletin.PostReqMessage(conn,tran,replyatrequest,"GEO Raabta",Session["user_id"].ToString(),2,"Education","t_educationInfoRequest");
//			}
			
			//try
			//{
			//	cmd2.ExecuteNonQuery();
			//}
			//catch (Exception)
			//{
			//}

			sql=" INSERT INTO t_educationInfoRequest(eduinfoid, pcode, degree, institute, DurationFrom, " +
				" DurationTo, result, majors, achievements, isactive, OtherDegree, OtherInstitute, OtherMajors, " +
				" type, createdby, createddate, modifiedby, c_at, Addflag, wfid, AppFlag, AppRejDate, RejCode, " +
				" AppRejBy, requesttimeline, filename) VALUES (@eduinfoid, @pcode, @degree, " +
				" @institute, @DurationFrom, @DurationTo, @result, @majors, @achievements, 1, " +
				" @OtherDegree, @OtherInstitute, null, null, null, getdate(), " +
				" null, getdate(), @Addflag, 38, 2, null, null, null, " +
				" @requesttimeline, @filename) ";

			SqlCommand cmd=new SqlCommand(sql,conn);
			cmd.Transaction=tran;

			cmd.Parameters.Add(new SqlParameter("@pcode",Session["user_id"].ToString()));
			cmd.Parameters.Add(new SqlParameter("@degree",txtDegree.SelectedValue));
			cmd.Parameters.Add(new SqlParameter("@institute",txtInstitute.SelectedValue));
			cmd.Parameters.Add(new SqlParameter("@DurationFrom",DurationFrom.SelectedValue));
			cmd.Parameters.Add(new SqlParameter("@DurationTo",DurationTo.SelectedValue));
			cmd.Parameters.Add(new SqlParameter("@result",txtResult.SelectedValue));
			cmd.Parameters.Add(new SqlParameter("@majors",txtOtherMajors.Text.Trim()));
			cmd.Parameters.Add(new SqlParameter("@achievements",txtAchievements.Text.Trim()));
			cmd.Parameters.Add(new SqlParameter("@otherdegree",txtOtherDegree.Text.Trim()));
			cmd.Parameters.Add(new SqlParameter("@OtherInstitute",txtOtherInstitute.Text.Trim()));
			cmd.Parameters.Add(new SqlParameter("@requesttimeline",GetTimeline("38")));

			SqlParameter _eduinfoid=new SqlParameter("@eduinfoid",SqlDbType.Int);
			SqlParameter _filename=new SqlParameter("@filename",SqlDbType.VarChar);
			SqlParameter _addflag=new SqlParameter("@addflag",SqlDbType.TinyInt);
			if(lbleduinfoid.Text=="")
			{
				_eduinfoid.Value=DBNull.Value;
				_addflag.Value=1;
			}
			else
			{
				_eduinfoid.Value=lbleduinfoid.Text;
				_addflag.Value=2;
			}

			if(fileEdu.Name!="")
				_filename.Value=fid;
			else
				_filename.Value=DBNull.Value;

			cmd.Parameters.Add(_eduinfoid);
			cmd.Parameters.Add(_filename);
			cmd.Parameters.Add(_addflag);

			cmd.ExecuteNonQuery();
			string ID=GetMaxReqID("t_educationInfoRequest",conn,tran);
			Bulletin.PostReqMessage(conn,tran,replyatrequest,"GEO Raabta",Session["user_id"].ToString(),1,"Education","t_educationInfoRequest");
			tran.Commit();
			tran.Dispose();

			GetEduInfo();
			Panel3.Visible=false;
			lbAddNewEdu.Visible=true;
				
			fileEdu.PostedFile.SaveAs(Server.MapPath("tempEmployee/"+"/"+fid));
			ResetEduForm();
			lblMsg.Visible=true;
			lblMsg.Text=GetReplyMessage("38");


		}

		private string GetReplyMessage(string wfid)
		{
			string sql="select replyatrequest from t_fieldsmgt where f_id="+wfid;
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			conn.Close();
			conn.Dispose();
			if(ds.Tables[0].Rows.Count>0)
				return ds.Tables[0].Rows[0][0].ToString();
			else
				return "";

		}
		private string GetMaxReqID(string table, SqlConnection conn, SqlTransaction tran)
		{
			SqlDataAdapter da=new SqlDataAdapter("select max(reqid) as maxreqid from "+table,conn);
			da.SelectCommand.Transaction=tran;
			DataSet ds=new DataSet();
			da.Fill(ds);
			return ds.Tables[0].Rows[0]["maxreqid"].ToString();
		}
		private void btnEdNew_Click_old(object sender, System.EventArgs e)
		{
			string sql="";

			string gid=Guid.NewGuid().ToString();
			string fid=gid+".jpg";
			gid=fid;

			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();

			if(isEduInfoRequested(lbleduinfoid.Text))
			{
				string ssql="update t_EducationInforequest set AppFlag=4 where eduinfoid=" + lbleduinfoid.Text + " and AppFlag=2";
				SqlCommand cmd2=new SqlCommand(ssql,conn);
				cmd2.ExecuteNonQuery();
			}

			sql="insert into t_educationinforequest(eduinfoid,pcode,degree,institute,DurationFrom,DurationTo,result,majors,achievements,isactive,OtherDegree,OtherInstitute,createdby,createddate,c_at,Addflag,wfid,AppFlag,filename,requesttimeline)" +
				" values(@eduinfoid,@pcode,@degree,@institute,@DurationFrom,@DurationTo,@result,@majors,@achievements,1,@OtherDegree,@OtherInstitute,'"+Session["user_id"].ToString()+"',getdate(),getdate(),@Addflag,@wfid,@AppFlag,@filename,DATEADD(d, @days, GETDATE()))";


			SqlCommand cmd=new SqlCommand(sql,conn);
			SqlParameter _eduinfoid=new SqlParameter("@eduinfoid",SqlDbType.Int);
			SqlParameter _pcode=new SqlParameter("@pcode",SqlDbType.VarChar,10);
			SqlParameter _degree=new SqlParameter("@degree",SqlDbType.Int);
			SqlParameter _institute=new SqlParameter("@institute",SqlDbType.Int);
			SqlParameter _DurationFrom=new SqlParameter("@DurationFrom",SqlDbType.Int);
			SqlParameter _DurationTo=new SqlParameter("@DurationTo",SqlDbType.Int);
			SqlParameter _result=new SqlParameter("@result",SqlDbType.VarChar,100);
			SqlParameter _majors=new SqlParameter("@majors",SqlDbType.VarChar,100);
			SqlParameter _OtherDegree=new SqlParameter("@OtherDegree",SqlDbType.VarChar,100);
			SqlParameter _OtherInstitute=new SqlParameter("@OtherInstitute",SqlDbType.VarChar,100);
			SqlParameter _createddate=new SqlParameter("@createddate",SqlDbType.VarChar,10);
			SqlParameter _Addflag=new SqlParameter("@Addflag",SqlDbType.TinyInt);
			SqlParameter _wfid=new SqlParameter("@wfid",SqlDbType.Int);
			SqlParameter _AppFlag=new SqlParameter("@AppFlag",SqlDbType.TinyInt);
			SqlParameter _achievements=new SqlParameter("@achievements",SqlDbType.Text);
			SqlParameter _filename=new SqlParameter("@filename",SqlDbType.VarChar);
			SqlParameter _days=new SqlParameter("@days",SqlDbType.Int);


			if(lbleduinfoid.Text=="")
				_eduinfoid.Value=DBNull.Value;
			else
				_eduinfoid.Value=lbleduinfoid.Text;
			_pcode.Value=Session["user_id"].ToString();
			_degree.Value=txtDegree.SelectedValue;
			_institute.Value=txtInstitute.SelectedValue;
			_DurationFrom.Value=DurationFrom.SelectedValue;
			_DurationTo.Value=DurationTo.SelectedValue;
			_result.Value=txtResult.SelectedValue;
			_majors.Value=txtOtherMajors.Text.Trim();
			_OtherDegree.Value=txtOtherDegree.Text.Trim();
			_OtherInstitute.Value=txtOtherInstitute.Text.Trim();
			_achievements.Value=txtAchievements.Text;
			_filename.Value=fid;
			_days.Value=GetTimeline("38");

			if(lbleduinfoid.Text=="")
				_Addflag.Value=1;	// new
			else
				_Addflag.Value=2;	// change

			_AppFlag.Value=2;	// pending
			_wfid.Value=38;		// work flow
				
			cmd.Parameters.Add(_eduinfoid);
			cmd.Parameters.Add(_pcode);
			cmd.Parameters.Add(_institute);
			cmd.Parameters.Add(_DurationFrom);
			cmd.Parameters.Add(_DurationTo);
			cmd.Parameters.Add(_result);
			cmd.Parameters.Add(_majors);
			cmd.Parameters.Add(_OtherDegree);
			cmd.Parameters.Add(_OtherInstitute);
			cmd.Parameters.Add(_Addflag);
			cmd.Parameters.Add(_wfid);
			cmd.Parameters.Add(_AppFlag);
			cmd.Parameters.Add(_degree);
			cmd.Parameters.Add(_achievements);
			cmd.Parameters.Add(_filename);
			cmd.Parameters.Add(_days);
			
			cmd.ExecuteNonQuery();

			GetEduInfo();
			Panel3.Visible=false;
			lbAddNewEdu.Visible=true;
				
			fileEdu.PostedFile.SaveAs(Server.MapPath("tempEmployee/"+"/"+fid));
			ResetEduForm();
		}

		private bool isEduInfoRequested(string eduinfoid)
		{
			if (eduinfoid!="")
			{
				string sql="SELECT reqid FROM dbo.t_EducationInfoRequest WHERE (AppFlag = 2) AND (eduinfoid= "+eduinfoid+")";
				SqlConnection conn=new SqlConnection(Connection.ConnectionString);
				SqlDataAdapter da=new SqlDataAdapter(sql,conn);
				DataSet ds=new DataSet();
				da.Fill(ds);
				if(ds.Tables[0].Rows.Count>0)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				return false;
			}

		}

		private void dgEducation_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			lbAddNewEdu.Visible=false;
			pnlEducation.Visible=false;
			//ResetEduForm();
			lbleduinfoid.Text=dgEducation.SelectedItem.Cells[0].Text;
			btnEdNew.Attributes.Clear();
			if(isEduInfoRequested(lbleduinfoid.Text))
			{
				btnEdNew.Attributes.Add("onclick","return confirm('A pending request already exist for this education do you want to resend it?');");
				btnEduDel.Attributes.Add("onclick","return confirm('A pending request already exist for this education do you want to resend it for removeal?');");
			}
			else
			{
				btnEduDel.Attributes.Add("onclick","return confirm('Are you sure to remove this education information?');");
			}
			string sql="SELECT eduinfoid, pcode, degree, institute, DurationFrom, DurationTo, result, majors, achievements, isactive, OtherDegree, OtherInstitute, OtherMajors, type, " +
				" createdby, createddate, modifiedby " +
				" FROM dbo.t_educationInfo " +
				" WHERE (eduinfoid = "+lbleduinfoid.Text+")";

			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			if(ds.Tables[0].Rows.Count>0)
			{
				//txtOtherDegree.Visible=false;
				SetDDL(txtDegree,ds.Tables[0].Rows[0]["degree"].ToString());
				if(txtDegree.SelectedValue=="-1")
				{
					//txtOtherDegree.Visible=true;
					txtOtherDegree.Text=ds.Tables[0].Rows[0]["OtherDegree"].ToString();
				}
				//txtOtherInstitute.Visible=false;
				SetDDL(txtInstitute,ds.Tables[0].Rows[0]["institute"].ToString());
				if(txtInstitute.SelectedValue=="-1")
				{
					//txtOtherInstitute.Visible=true;
					txtOtherInstitute.Text=ds.Tables[0].Rows[0]["OtherInstitute"].ToString();
				}

				SetDDL(txtResult,ds.Tables[0].Rows[0]["result"].ToString());
				txtOtherMajors.Text=ds.Tables[0].Rows[0]["majors"].ToString();
				SetDDL(DurationFrom,ds.Tables[0].Rows[0]["DurationFrom"].ToString());
				SetDDL(DurationTo, ds.Tables[0].Rows[0]["DurationTo"].ToString());
				txtAchievements.Text=ds.Tables[0].Rows[0]["achievements"].ToString();
				Panel3.Visible=true;
				lbAddNewEdu.Visible=false;
				btnEduDel.Visible=true;

			}
			else
			{

			}
		}

		
		private void SetDDL(DropDownList ddl, string val)
		{
			ddl.SelectedIndex=0;
			for(int j=0;j<ddl.Items.Count;j++)
			{
				if(ddl.Items[j].Value==val)
				{
					ddl.SelectedIndex=j;
					break;
				}
			}
		}


		private void GetEduInfo()
		{
			string sql="SELECT eduinfoid, pcode, CASE degree WHEN - 1 THEN otherdegree ELSE " +
				" (SELECT d .degree " +
				" FROM t_degree d " +
				" WHERE d .id = ei.degree) END AS Degree, CASE institute WHEN - 1 THEN OtherInstitute ELSE " +
				" (SELECT institute_name " +
				" FROM t_institute i " +
				" WHERE i.id = ei.institute) END AS Institute, degree AS Expr1, majors, result, " +
				" DurationFrom, CASE durationto WHEN - 1 THEN 'In Progress' ELSE CAST(durationto AS varchar) END AS DurationTo " +
				" FROM dbo.t_educationInfo AS ei " +
				" WHERE (isactive = 1) AND (pcode = '"+Session["user_id"].ToString()+"') ";

			SqlConnection conn=new SqlConnection(Connection.ConnectionString);

			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			dgEducation.DataSource=ds.Tables[0].DefaultView;
			dgEducation.DataBind();

			sql="SELECT eduinfoid, pcode, CASE degree WHEN - 1 THEN otherdegree ELSE " +
				" (SELECT d .degree "+
				" FROM t_degree d " +
				" WHERE d .id = ei.degree) END AS Degree, CASE institute WHEN - 1 THEN OtherInstitute ELSE " +
				" (SELECT institute_name " +
				" FROM t_institute i " +
				" WHERE i.id = ei.institute) END AS Institute, degree AS Expr1, majors, result, DurationFrom, " +
				" CASE durationto WHEN - 1 THEN 'In Progress' ELSE CAST(durationto AS varchar) END AS DurationTo, " +
				" CASE addflag WHEN 1 THEN 'New Education Info' WHEN 2 THEN 'Changes' WHEN 3 THEN 'Request for Remove' END AS AddFlag, reqid "+
				" FROM dbo.t_educationInfoRequest AS ei " +
				" WHERE (isactive = 1) AND (AppFlag = 2) AND (pcode = '"+Session["user_id"].ToString()+"')";
			da=new SqlDataAdapter(sql,conn);
			da.Fill(ds,"req");
			dgEducationReq.DataSource=ds.Tables["req"].DefaultView;
			dgEducationReq.DataBind();

			for(int j=0;j<dgEducationReq.Items.Count;j++)
			{
				LinkButton lb=(LinkButton)dgEducationReq.Items[j].FindControl("lbCancelEdu");
				lb.Attributes.Add("onclick","return confirm('are you sure to cancel this request?');");
			}

		}


		public void CancelEduReq(object sender, EventArgs e)
		{
			LinkButton rLink=(LinkButton)sender;
			for(int i=0;i<this.dgEducationReq.Items.Count;i++)
			{
				LinkButton lnk=(LinkButton)this.dgEducationReq.Items[i].FindControl("lbCancelEdu");
				if(lnk.Equals(rLink))
				{
					string reqid=dgEducationReq.Items[i].Cells[0].Text;
					string sql="Update t_EducationInfoRequest set appflag=4, AppRejDate=getdate() where reqid="+reqid;
					SqlConnection conn=new SqlConnection(Connection.ConnectionString);
					conn.Open();
					SqlTransaction tran=conn.BeginTransaction();
					SqlCommand cmd=new SqlCommand(sql,conn);
					cmd.Transaction=tran;
					cmd.ExecuteNonQuery();
					//Bulletin.PostMessage(conn,tran,"Request Cancel By User","GEO Raabta",Session["user_id"].ToString(),reqid, "Education");
					Bulletin.PostReqMessage(conn,tran,"Request Canceled by User","GEO Raabta",Session["User_ID"].ToString(),2,reqid,"Education");
					tran.Commit();
					tran.Dispose();
					conn.Close();
					GetEduInfo();
					break;
				}
			}
			ResetEduForm();
			
		}

		public void getYears()
		{
			int startYear=1900;
			int endYear=DateTime.Today.Year;
			for(int i=endYear;i>=startYear;i--)
			{
				ListItem itm=new ListItem(i.ToString(),i.ToString());
				ListItem item=new ListItem(i.ToString(),i.ToString());
				this.DurationFrom.Items.Add(itm);
				this.DurationTo.Items.Add(item); 
			}
			ListItem itm2=new ListItem("In Progress","-1");
			this.DurationTo.Items.Insert(1,itm2);
		}

		private void Page_Load(object sender, System.EventArgs e)
		{
			conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			lblMsg.Visible=false;
			if(!IsPostBack)
			{
				lblEduToolTip.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Education</font></b><br/><br/>"+GetTooltip(43)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				lblEduToolTip.Attributes.Add("onmouseout","UnTip()");
				ResetEduForm();
				GetEduInfo();
				getYears();
				FillDDL(txtDegree,"t_degree","degree","id","isactive=1","Select Degree",conn);
				FillDDL(txtInstitute,"t_institute","institute_name","id","isactive=1","Select Institute",conn);
				txtDegree.Items.Add(new ListItem("Other","-1"));
				txtInstitute.Items.Add(new ListItem("Other","-1"));
				txtDegree.Attributes.Add("onchange","ShowOther('txtDegree','-1','divOtherDegree','txtOtherDegree');");
				txtInstitute.Attributes.Add("onchange","ShowOther('txtInstitute','-1','divOtherInstitute','txtOtherInstitute');");
				ImageButton5.Attributes.Add("onclick","window.open('admin/organogram.htm','_blank','resizable=yes,menubar=no,scrollbars=yes,top=0,status=no');return false;");
				this.imgMyLeave.Attributes.Add("onclick","return OpenURL('MyLeaveBalance.aspx',650,300);");
				this.ImgMyAttendance.Attributes.Add("onclick","return OpenURL('MyAttendance.aspx',700,400);");
				ibMySelf.Attributes.Add("onclick","window.open('myinfo.aspx','MyProfile','toolbar=no,statusbar=no,addressbar=no,scrollbars=yes,resizable=no,width=700,height=650');return false;");
				
				//this.Image1.ImageUrl=@"employee\"+Session["user_id"].ToString()+".jpg";
				if(Request.QueryString.Count>0)
				{
					this.lblPCode.Text=Request.QueryString[0].ToString();
					this.lblEmpInfo.Text=Request.QueryString[1].ToString();
				}
			}
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.ImageButton1.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton1_Click);
			this.ImageButton2.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton2_Click);
			this.ImageButton3.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton3_Click);
			this.ImageButton4.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton4_Click);
			this.imgTraining.Click += new System.Web.UI.ImageClickEventHandler(this.imgTraining_Click);
			this.imgMyGrievance.Click += new System.Web.UI.ImageClickEventHandler(this.imgMyGrievance_Click);
			this.dgEducation.SelectedIndexChanged += new System.EventHandler(this.dgEducation_SelectedIndexChanged);
			this.lbAddNewEdu.Click += new System.EventHandler(this.lbAddNewEdu_Click);
			this.dgEducationReq.SelectedIndexChanged += new System.EventHandler(this.dgEducationReq_SelectedIndexChanged);
			this.btnEdNew.Click += new System.EventHandler(this.btnEdNew_Click);
			this.btnEduDel.Click += new System.EventHandler(this.btnEduDel_Click);
			this.btnECancel.Click += new System.EventHandler(this.btnECancel_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		private void ResetEduForm()
		{
			txtDegree.SelectedIndex=0;
			txtOtherDegree.Text="";
			//txtOtherDegree.Visible=false;
			txtInstitute.SelectedIndex=0;
			txtOtherInstitute.Text="";
			//txtOtherInstitute.Visible=false;
			txtResult.SelectedIndex=0;
			txtOtherMajors.Text="";
			DurationFrom.SelectedIndex=0;
			DurationTo.SelectedIndex=0;
			txtAchievements.Text="";
			dgEducation.SelectedIndex=-1;
			dgEducationReq.SelectedIndex=-1;
			pnlEducation.Visible=true;
			Panel3.Visible=false;

		}
		private void btnECancel_Click(object sender, System.EventArgs e)
		{
			Panel3.Visible=false;
			ResetEduForm();
			pnlEducation.Visible=true;
			lbAddNewEdu.Visible=true;

		}

		private void dgEducationReq_SelectedIndexChanged(object sender, System.EventArgs e)
		{
		
		}

		private void SetParameterValue(SqlCommand cmd, DataSet ds, string field)
		{
			cmd.Parameters.Add("@"+field,ds.Tables[0].Rows[0][field].ToString());
		}

		private void btnEduDel_Click(object sender, System.EventArgs e)
		{
			string replyatrequest=GetReplyMsg("replyatrequest","38");	
			
			string sql="";
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();

			SqlTransaction tran=conn.BeginTransaction();

				
			sql=" SELECT eduinfoid, pcode, degree, institute, DurationFrom, DurationTo, result, majors, achievements, isactive, OtherDegree, OtherInstitute, OtherMajors, type, "+
				" createdby, createddate, modifiedby "+
				" FROM dbo.t_educationInfo "+
				" WHERE (eduinfoid = "+lbleduinfoid.Text+")";
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			da.SelectCommand.Transaction=tran;

			DataSet ds=new DataSet();
			da.Fill(ds);

			
			if(isEduInfoRequested(lbleduinfoid.Text))
			{
				string ssql="update t_EducationInforequest set AppFlag=4 where eduinfoid=" + lbleduinfoid.Text + " and AppFlag=2 and pcode='"+Session["user_id"].ToString()+"'";
				SqlCommand cmd2=new SqlCommand(ssql,conn);
				cmd2.Transaction=tran;
				cmd2.ExecuteNonQuery();
				Bulletin.PostReqMessage(conn,tran,"Request Canceled By User","GEO Raabta",Session["user_id"].ToString(),2,"Education","t_educationInfoRequest");
			}

			sql="insert into t_educationinforequest(eduinfoid,pcode,degree,institute,DurationFrom,DurationTo,result,majors,achievements,isactive,OtherDegree,OtherInstitute,createdby,createddate,c_at,Addflag,wfid,AppFlag)" +
				" values(@eduinfoid,@pcode,@degree,@institute,@DurationFrom,@DurationTo,@result,@majors,@achievements,1,@OtherDegree,@OtherInstitute,'"+Session["user_id"].ToString()+"',getdate(),getdate(),3,@wfid,2)";

			SqlCommand cmd=new SqlCommand(sql,conn);
			cmd.Transaction=tran;

			SetParameterValue(cmd,ds,"eduinfoid");
			SetParameterValue(cmd,ds,"pcode");
			SetParameterValue(cmd,ds,"degree");
			SetParameterValue(cmd,ds,"DurationFrom");
			SetParameterValue(cmd,ds,"DurationTo");
			SetParameterValue(cmd,ds,"result");
			SetParameterValue(cmd,ds,"majors");
			SetParameterValue(cmd,ds,"achievements");
			SetParameterValue(cmd,ds,"OtherDegree");
			SetParameterValue(cmd,ds,"OtherInstitute");
			SetParameterValue(cmd,ds,"createddate");
			SetParameterValue(cmd,ds,"institute");

			SqlParameter _wfid=new SqlParameter("@wfid",SqlDbType.Int);

			_wfid.Value=38;		// work flow
			cmd.Parameters.Add(_wfid);
			cmd.ExecuteNonQuery();
			Bulletin.PostReqMessage(conn,tran,replyatrequest,"GEO Raabta",Session["user_id"].ToString(),1,"Education","t_educationInfoRequest");
			tran.Commit();
			GetEduInfo();
			Panel3.Visible=false;
			lbAddNewEdu.Visible=true;
			dgEducation.SelectedIndex=-1;
			ResetEduForm();
		}

		private void imgEmp_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("UserProfile.aspx?id=1");
		}

		private void imgPersonal_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("UserProfile.aspx?id=2");
		}

		private void imgFamily_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("EmpFamily.aspx?pcode="+this.lblPCode.Text+"&name="+this.lblEmpInfo.Text);
		}

		private void imgEducation_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("EmpEducation.aspx?pcode="+this.lblPCode.Text+"&name="+this.lblEmpInfo.Text);
		}

		private void imgMyGrievance_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("MyGrievance.aspx?pcode="+lblPCode.Text+"&name="+lblEmpInfo.Text);
		}

		private void imgTraining_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("EmpTraining.aspx?pcode="+this.lblPCode.Text+"&name="+this.lblEmpInfo.Text);
		}

		private void btnEdNew_Click(object sender, System.EventArgs e)
		{
			SaveEduRequest();
		}

		private void ImgMyAttendance_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
		
		}



		private void ImageButton3_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("empfamily.aspx");
		}

		private void ImageButton4_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("empeducation.aspx");
		}

		private void ImageButton1_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("UserProfile.aspx?id=1");
		}

		private void ImageButton2_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("UserProfile.aspx?id=2");
		}
		private void ImageButton5_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			//pnlPaySlip.Visible=false;
			//Response.Write("<script language = Javascript >var win=window.showModalDialog('admin/textonimage1.aspx','','dialogHeight:900px;dialogWidth:800px;center:yes')</script"); 
			Response.Write("<script language = Javascript >win=window.open('admin/orgamogram.htm','_blank','resizable=yes,menubar=no,scrollbars=yes,top=0,status=0',true).focus();</script>");

		}




	}
}
