using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for GeoIdeas.
	/// </summary>
	public class CreateSpecialist : System.Web.UI.Page
	{
		SqlConnection con;
		protected System.Web.UI.HtmlControls.HtmlTable tdFrom2;	// 
		string userId="";
		private bool _refreshState;
		protected System.Web.UI.WebControls.Panel Panel2;
		protected System.Web.UI.WebControls.CheckBoxList chkOwnerDept;
		protected System.Web.UI.WebControls.CheckBoxList chkOwnerCat;
		protected System.Web.UI.WebControls.Panel Panel5;
		protected System.Web.UI.WebControls.CheckBoxList chkOwnerStation;
		protected System.Web.UI.WebControls.Panel Panel6;
		protected System.Web.UI.WebControls.TextBox txtY;
		protected System.Web.UI.WebControls.TextBox txtX;
		protected System.Web.UI.WebControls.Panel Panel3;
		protected System.Web.UI.WebControls.Panel Panel4;
		protected System.Web.UI.WebControls.Panel Panel8;
		protected System.Web.UI.WebControls.Panel Panel1;
		protected System.Web.UI.WebControls.Panel pnlMain;
		protected System.Web.UI.WebControls.TextBox txtTitle;
		protected System.Web.UI.WebControls.CheckBoxList chkDepartment;
		protected System.Web.UI.WebControls.CheckBoxList chkDesignation;
		protected System.Web.UI.WebControls.Button cmdUpdateDesg;
		protected System.Web.UI.WebControls.Button Button2;
		protected System.Web.UI.WebControls.CheckBoxList chkFunctionalDesignation;
		protected System.Web.UI.WebControls.CheckBoxList chkOwnerList;
		protected System.Web.UI.WebControls.Button Button3;
		protected System.Web.UI.WebControls.Button Button4;
		protected System.Web.UI.WebControls.DropDownList ddDept;
		protected System.Web.UI.WebControls.DropDownList ddTimeslot;
		protected System.Web.UI.WebControls.Label lblSpecialistNo;
		protected System.Web.UI.WebControls.Label lblMsg;
		protected System.Web.UI.WebControls.Button cmdCancel;
		protected System.Web.UI.WebControls.Button btnSave;
		protected System.Web.UI.WebControls.LinkButton lnkDynamics;
		protected System.Web.UI.WebControls.Panel pnlSub;
		protected System.Web.UI.WebControls.DropDownList chkModule;
		protected System.Web.UI.WebControls.Button btnCancelDynamics;
		protected System.Web.UI.WebControls.Button btnSaveDynamics;
		protected System.Web.UI.WebControls.DropDownList chkSpType;
		protected System.Web.UI.WebControls.Label Label1;
		private bool _isRefresh;
		public bool IsRefresh
		{
			get
			{
				return _isRefresh;
			}
		}
		protected override void LoadViewState(object savedState)
		{
			object[] allStates = (object[]) savedState;
			base.LoadViewState(allStates[0]);
			_refreshState = (bool) allStates[1];
			try
			{
				_isRefresh = _refreshState == (bool) Session["__ISREFRESH"];
			}
			catch(Exception)
			{
				Response.Redirect("../Login.aspx");
			}
		}
		protected override object SaveViewState()
		{
			Session["__ISREFRESH"] = _refreshState;
			object[] allStates = new object[2];
			allStates[0] = base.SaveViewState();
			allStates[1] = !_refreshState;
			return allStates;
		}
		private bool IsPageAccessAllowed()
		{
			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception ex)
			{
				string s=ex.Message;
				Response.Redirect("../Login.aspx");
			}
		
			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(2,53,userId,"View")==true)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("../Login.aspx");
				return false;
			}
		}
		public void GetDepartment()
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select dept.deptid,dept.deptname+'('+n.name+')' from t_department dept,t_sbu s,t_network n where dept.status=1 and s.sbuid=dept.sbu and n.nID=s.networkID order by deptname",con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
				this.chkDepartment.Items.Add(itm);
				this.ddDept.Items.Add(itm);
				this.chkOwnerDept.Items.Add(itm);
			}
			rd.Close();
			con.Close();
			con.Dispose();
		}
		public void GetRequestModule()
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select mId,mName from t_RequestModules where mActive=1 order by mId",con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
				this.chkModule.Items.Add(itm);
			}
			rd.Close();
			con.Close();
			con.Dispose();
		}
		public void GetSpecialistType()
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select tId,tType from t_SpecialistType where tActive=1 order by tId",con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
				this.chkSpType.Items.Add(itm);
			}
			rd.Close();
			con.Close();
			con.Dispose();
		}
		public void GetDesignations()
		{
			this.chkDesignation.Items.Clear();
			string ID="";
			for(int i=0;i<this.chkDepartment.Items.Count;i++)
			{
				if(this.chkDepartment.Items[i].Selected)
				{
					ID+=this.chkDepartment.Items[i].Value+",";
				}
			}
			if(ID.Length>0)
			{
				ID=ID.Remove(ID.Length-1,1);
 
				con=new SqlConnection(Connection.ConnectionString);
				con.Open();
				//SqlCommand cmd=new SqlCommand("select d.desigid,d.designation from t_designation d,t_department dept where dept.deptid=d.deptid and dept.deptid in("+ID+") and d.status=1 order by d.designation",con);
				SqlCommand cmd=new SqlCommand("SELECT TOP (100) PERCENT desg.desigid, desg.designation + ' <font color=gray>[' + dept.deptname + ']</font>' AS Expr1, dept.deptname " +
					" FROM dbo.t_Department AS dept INNER JOIN " +
					" dbo.t_Designation AS desg ON dept.deptid = desg.deptid " +
					" WHERE (desg.status = 1) AND (dept.deptid IN ("+ID+")) " +
					" ORDER BY desg.designation,dept.deptname ",con);
				SqlDataReader rd=cmd.ExecuteReader();
				while(rd.Read())
				{
					ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
					this.chkDesignation.Items.Add(itm);
				}
				rd.Close();
				con.Close();
				con.Dispose();
			}
		}
		public void GetFDesignations()
		{
			this.chkFunctionalDesignation.Items.Clear();
			string ID="";
			for(int i=0;i<this.chkDesignation.Items.Count;i++)
			{
				if(this.chkDesignation.Items[i].Selected)
				{
					ID+=this.chkDesignation.Items[i].Value+",";
				}
			}
			if(ID.Length>0)
			{
				ID=ID.Remove(ID.Length-1,1);
			
				con=new SqlConnection(Connection.ConnectionString);
				con.Open();
				string Query="select f.fdesigid,f.functionaltitle,dbo.GetEmpByDesg(f.fdesigid) as [EmpRecords] from t_functionaldesignation f,t_designation d where d.desigid=f.functionaldesignation and d.desigid in("+ID+") and f.isactive=1 order by f.functionaltitle";
				SqlCommand cmd=new SqlCommand(Query,con);
				SqlDataReader rd=cmd.ExecuteReader();
				while(rd.Read())
				{
					string Val=" <font color=gray>[" + rd["EmpRecords"].ToString()+"]</font>";
					ListItem itm=new ListItem(rd[1].ToString()+Val,rd[0].ToString());
					this.chkFunctionalDesignation.Items.Add(itm);
				}
				rd.Close();
				con.Close();
				con.Dispose();
			}
		}
		public void CopyFD()
		{
			bool found=false;
			//this.chkOwnerList.Items.Clear();
			for(int i=0;i<this.chkFunctionalDesignation.Items.Count;i++)
			{
				if(this.chkFunctionalDesignation.Items[i].Selected)
				{
					ListItem itm=this.chkFunctionalDesignation.Items[i];
					found=false;
					for(int k=0;k<chkOwnerList.Items.Count;k++)
					{
						if(itm.Value==chkOwnerList.Items[k].Value)
						{
							found=true;
							break;
						}
					}
					if(!found)
					{
						this.chkOwnerList.Items.Add(itm);
					}
					itm.Selected=false;
				}
			}
		}

		public void RemoveFD()
		{
			//System.Collections.ArrayList _list=new ArrayList();
			for(int i=0;i<this.chkOwnerList.Items.Count;)
			{
				if(this.chkOwnerList.Items[i].Selected)
				{
					//_list.Add(i);
					this.chkOwnerList.Items.RemoveAt(i);
					i--;
				}
				i++;
			}
		}
		public void GetCategory()
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select cat_id,cat_name from t_categorization where isactive=1 order by clevel",con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
				this.chkOwnerCat.Items.Add(itm);
			}
			rd.Close();
			con.Close();
			con.Dispose();
		}
		
		public void GetStation()
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select cityid,cityname from t_city where status=1 order by cityname",con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
				this.chkOwnerStation.Items.Add(itm);
			}
			con.Close();
			con.Dispose();
		}

		public string GetSpecialistID(SqlTransaction trans)
		{
			string Val="";
			int newVal=0;
			SqlCommand cmd=new SqlCommand("SELECT isnull (MAX(CAST(SpecialistNo AS int)) + 1,1) AS Expr1 " +
				" FROM dbo.t_Specialist " +
				" ORDER BY MAX(CAST(SpecialistNo AS int)) DESC",con);
			cmd.Transaction=trans;
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			if(rd.HasRows)
			{
				newVal=Int32.Parse(rd[0].ToString());
				//Val+=newVal.ToString();
			}
			else
			{
				newVal=0;
				//Val+=newVal.ToString();
			}
			rd.Close();
			
			for(int i=0;i<4-newVal.ToString().Length;i++)
			{
				Val+="0";
			}
			Val+=newVal;
			return Val;
		}
		private void Page_Load(object sender, System.EventArgs e)
		{
			lblMsg.Visible=false;
			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			if(IsPageAccessAllowed())
			{
			
			}
			else
			{
				Response.Redirect("ErrorPage.aspx");
			}
			if(!IsPostBack)
			{
				GetDepartment();
				GetCategory();
				GetStation();
				GetRequestModule();
				GetSpecialistType();
				pnlSub.Visible=false;
				lnkDynamics.Enabled=false;
				btnSave.Attributes.Add("onclick","return Func();");
			}
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.chkDepartment.SelectedIndexChanged += new System.EventHandler(this.chkDepartment_SelectedIndexChanged);
			this.cmdUpdateDesg.Click += new System.EventHandler(this.cmdUpdateDesg_Click);
			this.Button2.Click += new System.EventHandler(this.Button2_Click);
			this.Button3.Click += new System.EventHandler(this.Button3_Click);
			this.Button4.Click += new System.EventHandler(this.Button4_Click);
			this.cmdCancel.Click += new System.EventHandler(this.cmdCancel_Click);
			this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
			this.lnkDynamics.Click += new System.EventHandler(this.lnkDynamics_Click);
			this.btnCancelDynamics.Click += new System.EventHandler(this.btnCancelDynamics_Click);
			this.btnSaveDynamics.Click += new System.EventHandler(this.btnSaveDynamics_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		private void chkDepartment_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			//GetFDesignations();
		}

		private void lnkAddFD_Click(object sender, System.EventArgs e)
		{
			
		}
		private void lnkRemFD_Click(object sender, System.EventArgs e)
		{
			
		}
		private void btnSave_Click(object sender, System.EventArgs e)
		{
			
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlTransaction trans=con.BeginTransaction();
			try
			{
					
				SqlCommand cmd=new SqlCommand("insert into t_specialist(title,specialistno,stimeslot,sdepartment,isactive,cby,cdate,sptype) values(@title,@specialistno,@stimeslot,@sdepartment,@isactive,@cby,getdate(),@sptype)",con);
				SqlParameter _sno=new SqlParameter("@specialistno",SqlDbType.VarChar);
				SqlParameter _stime=new SqlParameter("@stimeslot",SqlDbType.VarChar);
				SqlParameter _sdept=new SqlParameter("@sdepartment",SqlDbType.Int);
				SqlParameter _isactive=new SqlParameter("@isactive",SqlDbType.Int);
				SqlParameter _cby=new SqlParameter("@cby",SqlDbType.VarChar,10);
				SqlParameter _title=new SqlParameter("@title",SqlDbType.VarChar,500);
                SqlParameter _sptype=new SqlParameter("@sptype",SqlDbType.Int);
				string Value=GetSpecialistID(trans);
				this.lblSpecialistNo.Text=Value;
				_sno.Value=Value;
				_stime.Value=this.ddTimeslot.SelectedItem.Text.ToString();
				_sdept.Value=this.ddDept.SelectedValue.ToString();
				_isactive.Value=1;
				_cby.Value=Session["user_id"].ToString();
				_title.Value=txtTitle.Text.Trim();
                _sptype.Value=chkSpType.SelectedItem.Value;
				cmd.Parameters.Add(_sno);
				cmd.Parameters.Add(_stime);
				cmd.Parameters.Add(_sdept);
				cmd.Parameters.Add(_isactive);
				cmd.Parameters.Add(_cby);
				cmd.Parameters.Add(_title);
				cmd.Parameters.Add(_sptype);
				cmd.Transaction=trans;
				int z=cmd.ExecuteNonQuery();
				for(int i=0;i<this.chkOwnerList.Items.Count;i++)
				{
					cmd=new SqlCommand("insert into t_specialistowner (specialistno,s_fdesigid) values(@specialistno,@s_fdesigid)",con);
					SqlParameter _spno=new SqlParameter("@specialistno",SqlDbType.VarChar);
					SqlParameter _fdesigid=new SqlParameter("@s_fdesigid",SqlDbType.Int);
					_spno.Value=lblSpecialistNo.Text;
					_fdesigid.Value=this.chkOwnerList.Items[i].Value;
					cmd.Parameters.Add(_spno);
					cmd.Parameters.Add(_fdesigid);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
				}
				trans.Commit();
				con.Close();
				con.Dispose();
				lblMsg.Visible=true;
				lblMsg.Text="New specialist "+lblSpecialistNo.Text+" has been created successfully";
				btnSave.Enabled=false;
				lnkDynamics.Enabled=true;
				ddDept.Enabled=false;
				chkSpType.Enabled=false;
			}
			catch(Exception ex)
			{
				trans.Rollback();
				Response.Write(ex.Message);
			}
			finally
			{
				con.Close();
				con.Dispose();
			}	
		}
		private void btnDesignationAll_Click(object sender, System.EventArgs e)
		{
		
		}
		private void btnDepartAll_Click(object sender, System.EventArgs e)
		{
		
		}
		public void ResetSetting()
		{
			txtTitle.Text="";
			this.chkDesignation.Items.Clear();
			this.chkFunctionalDesignation.Items.Clear();
			this.chkOwnerList.Items.Clear();
			this.ddDept.SelectedIndex=0;
			this.ddTimeslot.SelectedIndex=0;
			this.lblSpecialistNo.Text="";
			for(int i=0;i<this.chkDepartment.Items.Count;i++)
			{
				this.chkDepartment.Items[i].Selected=false;
				this.chkOwnerDept.Items[i].Selected=false;
			}
			for(int i=0;i<this.chkOwnerCat.Items.Count;i++)
			{
				this.chkOwnerCat.Items[i].Selected=false;
			}
			for(int i=0;i<this.chkOwnerStation.Items.Count;i++)
			{
				this.chkOwnerStation.Items[i].Selected=false;
			}
			btnSave.Enabled=true;
			lnkDynamics.Enabled=false;
			pnlSub.Visible=false;
			chkSpType.SelectedIndex=-1;
			ddDept.Enabled=true;
			chkSpType.Enabled=true;
		}
		private void cmdCancel_Click(object sender, System.EventArgs e)
		{
			ResetSetting();			
		}
		private void cmdUpdateDesg_Click(object sender, System.EventArgs e)
		{
			GetDesignations();
		}

		private void Button2_Click(object sender, System.EventArgs e)
		{
			GetFDesignations();
		}

		private void Button3_Click(object sender, System.EventArgs e)
		{
			CopyFD();
		}

		private void Button4_Click(object sender, System.EventArgs e)
		{
			RemoveFD();
		}

		private void lnkDynamics_Click(object sender, System.EventArgs e)
		{
			pnlSub.Visible=true;
			if(chkSpType.SelectedValue=="2" || chkSpType.SelectedValue=="5")
			{
				Panel5.Enabled=false;
				Panel6.Enabled=false;
			}
			else
			{
				Panel5.Enabled=true;
				Panel6.Enabled=true;
			}

		}

		private void btnSaveDynamics_Click(object sender, System.EventArgs e)
		{
			string deptList="";
			string catList="";
			string statList="";
			ArrayList _refList=new ArrayList();
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlTransaction trans=con.BeginTransaction();
			try
			{
				SqlCommand cm=new SqlCommand("delete from t_specialistdept where specialistno='"+lblSpecialistNo.Text+"' and SpModuleId="+chkModule.SelectedItem.Value+"",con);
				cm.Transaction=trans;
				cm.ExecuteNonQuery();
				cm=new SqlCommand("delete from t_specialistcat where specialistno='"+lblSpecialistNo.Text+"' and SpModuleId="+chkModule.SelectedItem.Value+"",con);
				cm.Transaction=trans;
				cm.ExecuteNonQuery();
				cm=new SqlCommand("delete from t_specialiststation where specialistno='"+lblSpecialistNo.Text+"' and SpModuleId="+chkModule.SelectedItem.Value+"",con);
				cm.Transaction=trans;
				cm.ExecuteNonQuery();
				cm=new SqlCommand("delete from t_specialistmodule where spno='"+lblSpecialistNo.Text+"' and SpModuleId="+chkModule.SelectedItem.Value+"",con);
				cm.Transaction=trans;
				cm.ExecuteNonQuery();
				cm=new SqlCommand("insert into t_specialistmodule (spno,spmoduleid) values ('"+lblSpecialistNo.Text+"',"+chkModule.SelectedValue+")",con);
				cm.Transaction=trans;
				cm.ExecuteNonQuery();
				for(int i=0;i<this.chkOwnerDept.Items.Count;i++)
				{
					if(this.chkOwnerDept.Items[i].Selected)
					{
						deptList+=this.chkOwnerDept.Items[i].Value+",";
						SqlCommand cmd=new SqlCommand("insert into t_specialistdept (s_deptid,specialistno,SpModuleId) values(@s_deptid,@specialistno,@SpModuleId)",con);
						SqlParameter _dept=new SqlParameter("@s_deptid",SqlDbType.Int);
						SqlParameter _spno2=new SqlParameter("@specialistno",SqlDbType.VarChar);
						SqlParameter _modType=new SqlParameter("@SpModuleId",SqlDbType.Int);
						_dept.Value=this.chkOwnerDept.Items[i].Value;
						_spno2.Value=lblSpecialistNo.Text;
						_modType.Value=chkModule.SelectedItem.Value;
						cmd.Parameters.Add(_dept);
						cmd.Parameters.Add(_spno2);
						cmd.Parameters.Add(_modType);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
					}
				}
				for(int i=0;i<this.chkOwnerCat.Items.Count;i++)
				{
					if(this.chkOwnerCat.Items[i].Selected)
					{
						catList+=this.chkOwnerCat.Items[i].Value+",";
						SqlCommand cmd=new SqlCommand("insert into t_specialistcat(s_catid,specialistno,SpModuleId) values(@s_catid,@specialistno,@SpModuleId)",con);
						SqlParameter _cat=new SqlParameter("@s_catid",SqlDbType.Int);
						SqlParameter _spno3=new SqlParameter("@specialistno",SqlDbType.VarChar);
						SqlParameter _modType=new SqlParameter("@SpModuleId",SqlDbType.Int);
						_spno3.Value=lblSpecialistNo.Text;
						_cat.Value=this.chkOwnerCat.Items[i].Value;
						_modType.Value=chkModule.SelectedItem.Value;
						cmd.Parameters.Add(_spno3);
						cmd.Parameters.Add(_cat);
						cmd.Parameters.Add(_modType);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
					}
				}
				for(int i=0;i<this.chkOwnerStation.Items.Count;i++)
				{
					if(this.chkOwnerStation.Items[i].Selected)
					{
						statList+=this.chkOwnerStation.Items[i].Value+",";
						SqlCommand cmd=new SqlCommand("insert into t_specialiststation(s_stationid,specialistno,SpModuleId) values(@s_stationid,@specialistno,@SpModuleId)",con);
						SqlParameter _station=new SqlParameter("@s_stationid",SqlDbType.Int);
						SqlParameter _spno4=new SqlParameter("@specialistno",SqlDbType.VarChar);
						SqlParameter _modType=new SqlParameter("@SpModuleId",SqlDbType.Int);
						_spno4.Value=lblSpecialistNo.Text;
						_station.Value=this.chkOwnerStation.Items[i].Value;
						_modType.Value=chkModule.SelectedItem.Value;
						cmd.Parameters.Add(_spno4);
						cmd.Parameters.Add(_station);
						cmd.Parameters.Add(_modType);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
					}
				}
				if(this.chkModule.SelectedValue=="3" && this.chkSpType.SelectedValue=="1")
				{
					if(deptList.Length>0)
					{
						deptList=deptList.Remove(deptList.Length-1,1);
					}
					if(catList.Length>0)
					{
						catList=catList.Remove(catList.Length-1,1);
					}
					if(statList.Length>0)
					{
						statList=statList.Remove(statList.Length-1,1);
					}
					//=======================Add New Specialist Request====================//
					string Query="SELECT dbo.t_ClientRequest.crId, dbo.t_ClientRequest.RequestNo, dbo.t_ClientRequest.crDept, dbo.t_Employee.pcode, dbo.t_Employee.name, "+
						"dbo.t_Categorization.cat_name, dbo.t_City.cityname "+
						"FROM dbo.t_ClientRequest INNER JOIN "+
						"dbo.t_ClientRequestSpec ON dbo.t_ClientRequest.crId = dbo.t_ClientRequestSpec.crId INNER JOIN "+
						"dbo.t_SpecialistOwner ON dbo.t_ClientRequestSpec.crSpNo = dbo.t_SpecialistOwner.SpecialistNo INNER JOIN "+
						"dbo.t_SpecialistModule ON dbo.t_SpecialistOwner.SpecialistNo = dbo.t_SpecialistModule.spno INNER JOIN "+
						"dbo.t_DesignationHistory ON dbo.t_SpecialistOwner.s_fdesigid = dbo.t_DesignationHistory.fdesigid INNER JOIN "+
						"dbo.t_Employee ON dbo.t_DesignationHistory.pcode = dbo.t_Employee.pcode INNER JOIN "+
						"dbo.t_Designation ON dbo.t_Employee.desigid = dbo.t_Designation.desigid INNER JOIN "+
						"dbo.t_Categorization ON dbo.t_Designation.category = dbo.t_Categorization.cat_id INNER JOIN "+
						"dbo.t_City ON dbo.t_Employee.station = dbo.t_City.cityid "+
						"WHERE (dbo.t_ClientRequest.crDept IN ("+deptList+")) AND (dbo.t_ClientRequestSpec.crSpType = 2) AND (dbo.t_SpecialistModule.SpModuleId = 3) AND "+
						"(dbo.t_DesignationHistory.isactive = 1) AND (dbo.t_Categorization.isactive = 1) AND (dbo.t_Designation.status = 1) AND "+
						"(dbo.t_Categorization.cat_id IN ("+catList+")) AND (dbo.t_City.cityid IN ("+statList+")) AND (dbo.t_City.status = 1)";
					SqlDataAdapter dr=new SqlDataAdapter(Query,con);
					dr.SelectCommand.Transaction=trans;
					DataSet ds=new DataSet("Requests");
					dr.Fill(ds);
					for(int i=0;i<ds.Tables[0].Rows.Count;i++)
					{
						InsertRequestSubDetail(con,trans,ds.Tables[0].Rows[i]["crId"].ToString(),ds.Tables[0].Rows[i]["RequestNo"].ToString(),lblSpecialistNo.Text,"1");
					}
					//=====================================================================//
				}
				trans.Commit();
				this.Label1.Text="Specialist dimension information has been successfully saved";
				this.Label1.Visible=true;
			}
			catch(Exception ex)
			{
				trans.Rollback();
				Response.Write(ex.Message);
			}
			finally
			{
				con.Close();
				con.Dispose();
			}	
		}
		public void InsertRequestSubDetail(SqlConnection con,SqlTransaction trans,string crId,string reqNo,string crSpNo,string crSpType)
		{
			SqlCommand cmd=new SqlCommand("insert into t_clientrequestspec (crId,RequestNo,crSpNo,crSpType) values (@crId,@RequestNo,@crSpNo,@crSpType)",con);
			SqlParameter _crId=new SqlParameter("@crId",SqlDbType.Int);
			SqlParameter _reqNo=new SqlParameter("@RequestNo",SqlDbType.Int);
			SqlParameter _crSpNo=new SqlParameter("@crSpNo",SqlDbType.VarChar);
			SqlParameter _crSpType=new SqlParameter("@crSpType",SqlDbType.Int);
			_crId.Value=crId;
			_reqNo.Value=reqNo;
			_crSpNo.Value=crSpNo;
			_crSpType.Value=crSpType;
			cmd.Parameters.Add(_crId);
			cmd.Parameters.Add(_reqNo);
			cmd.Parameters.Add(_crSpNo);
			cmd.Parameters.Add(_crSpType);
			cmd.Transaction=trans;
			cmd.ExecuteNonQuery();
		}
		private void btnCancelDynamics_Click(object sender, System.EventArgs e)
		{
			this.Label1.Visible=false;
			for(int i=0;i<this.chkDepartment.Items.Count;i++)
			{
				this.chkDepartment.Items[i].Selected=false;
				this.chkOwnerDept.Items[i].Selected=false;
			}
			for(int i=0;i<this.chkOwnerCat.Items.Count;i++)
			{
				this.chkOwnerCat.Items[i].Selected=false;
			}
			for(int i=0;i<this.chkOwnerStation.Items.Count;i++)
			{
				this.chkOwnerStation.Items[i].Selected=false;
			}
			this.chkModule.SelectedIndex=-1;
		}	
	}
}
