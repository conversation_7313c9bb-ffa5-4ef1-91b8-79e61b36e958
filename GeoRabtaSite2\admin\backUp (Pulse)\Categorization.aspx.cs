using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;
namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for GeoIdeas.
	/// </summary>
	public class GeoCategory : System.Web.UI.Page
	{
		SqlConnection con;
		private static DataSet ds;
		protected System.Web.UI.WebControls.Label Label2;
		protected System.Web.UI.WebControls.TextBox txtCatName;
		protected System.Web.UI.WebControls.Label Label4;
		protected System.Web.UI.WebControls.TextBox txtCatCriteria;
		protected System.Web.UI.WebControls.Label Label3;
		protected System.Web.UI.WebControls.TextBox txtCatLevel;
		protected System.Web.UI.WebControls.Label Label5;
		protected System.Web.UI.WebControls.TextBox txtCatColor;
		protected System.Web.UI.WebControls.Label Label8;
		protected System.Web.UI.WebControls.DropDownList ddParentCat;
		protected System.Web.UI.WebControls.Label Label6;
		protected System.Web.UI.WebControls.TextBox txtPositive;
		protected System.Web.UI.WebControls.Label Label9;
		protected System.Web.UI.WebControls.DropDownList ddSRole;
		protected eWorld.UI.NumericBox txtLevel;
		protected System.Web.UI.WebControls.Button btnCancel;
		protected System.Web.UI.WebControls.Button cmdDelete;
		protected ZmodemControls.ColourPicker ColourPicker1;
		protected System.Web.UI.WebControls.Label Label10;
		protected System.Web.UI.WebControls.RadioButtonList rblCategory;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator1;
		protected System.Web.UI.WebControls.Panel Panel1;
		protected System.Web.UI.WebControls.Label Label1;
		protected System.Web.UI.WebControls.TextBox txtCatId;
		protected System.Web.UI.WebControls.TextBox txtColor;
		protected System.Web.UI.WebControls.Button btnQueryButton;
		protected System.Web.UI.WebControls.Label Label7;
		const int WebFormID=3;
		const int ProjectID=2;
		static string userId="";
		protected System.Web.UI.WebControls.Label Label11;
		protected System.Web.UI.HtmlControls.HtmlTable pnlCategoryForm;
		protected eWorld.UI.CollapsablePanel CollapsablePanel1;
		protected System.Web.UI.WebControls.LinkButton Button1;
		protected System.Web.UI.WebControls.DataGrid DataGrid2;
		protected System.Web.UI.WebControls.TextBox txtX;
		protected System.Web.UI.WebControls.TextBox txtY;
		protected System.Web.UI.WebControls.Label Label12;
		protected System.Web.UI.WebControls.ValidationSummary ValidationSummary1;
		public static string sort="Id";

		private bool _refreshState;
		private bool _isRefresh;

		public bool IsRefresh
		{
			get
			{
				return _isRefresh;
			}
		}

		protected override void LoadViewState(object savedState)
		{
			object[] allStates = (object[]) savedState;
			base.LoadViewState(allStates[0]);
			_refreshState = (bool) allStates[1];
			try
			{
				_isRefresh = _refreshState == (bool) Session["__ISREFRESH"];
			}
			catch(Exception)
			{
				Response.Redirect("../Login.aspx");
			}

		}

		protected override object SaveViewState()
		{
			Session["__ISREFRESH"] = _refreshState;
			object[] allStates = new object[2];
			allStates[0] = base.SaveViewState();
			allStates[1] = !_refreshState;
			return allStates;
		}



		private bool IsPageAccessAllowed()
		{

			
			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception ex)
			{
				string s=ex.Message;
				Response.Redirect("../Login.aspx");
			}
		
			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"View")==true)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("../Login.aspx");
				return false;
			}
		}

		private void Page_Load(object sender, System.EventArgs e)
		{
			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			con = new SqlConnection(Connection.ConnectionString);
			Label12.Visible=false;
			btnQueryButton.Enabled=true;
			cmdDelete.Enabled=true;
			con.Open();

			if(IsPageAccessAllowed())
			{
				Label7.Visible=false;
				
				
				//Page.SmartNavigation=true;
				if(!IsPostBack)
				{
					//ColourPicker1.Visible=false;
					FillParentCategoryDDL();
					BindGrid();
					cmdDelete.Attributes.Add("onclick","if(confirm('Are you sure to delete this Record?')){return true}else{return false}");
					ResetForm();
					pnlCategoryForm.Visible=false;
				}
				
				Button1.Visible=GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Add");
				//DataGrid2.Columns[1].Visible=GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Edit");
				//cmdDelete.Visible=GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Delete");
				//cmdDelete.Enabled=GeoSecurity.isControlEnable(ProjectID,WebFormID,userId,"Delete");
			}
			else
			{
				Response.Redirect("ErrorPage.aspx");
			}

		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.ddParentCat.SelectedIndexChanged += new System.EventHandler(this.ddParentCat_SelectedIndexChanged);
			this.rblCategory.SelectedIndexChanged += new System.EventHandler(this.rblCategory_SelectedIndexChanged);
			this.btnQueryButton.Click += new System.EventHandler(this.btnQueryButton_Click);
			this.cmdDelete.Click += new System.EventHandler(this.cmdDelete_Click);
			this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
			this.Button1.Click += new System.EventHandler(this.Button1_Click);
			this.DataGrid2.EditCommand += new System.Web.UI.WebControls.DataGridCommandEventHandler(this.DataGrid2_EditCommand);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		private string GetNewLevel()
		{
			SqlCommand cmd = new SqlCommand("SELECT MAX(CLevel) + 1 AS NewCLevel FROM dbo.t_Categorization",con);
			SqlDataReader dr=cmd.ExecuteReader();
			dr.Read();
			string temp="";
			if (dr.HasRows)
			{
				temp=dr["NewCLevel"].ToString();
			}
			else
			{
				temp="1";
			}
			dr.Close();
			return temp;
		}
		private void btnQueryButton_Click(object sender, System.EventArgs e)
		{
			if (_isRefresh)
			{
				BindGrid();
				Label10.Visible=false;
				ResetForm();
				FillParentCategoryDDL();
				pnlCategoryForm.Visible=false;
				Button1.Visible=true;
				btnQueryButton.Text="New Category";
				return;
			}
			

			if (btnQueryButton.Text=="Update")
			{
				if (txtLevel.Text!="0")
				{
					SqlCommand cmd = new SqlCommand("select cLevel from t_Categorization where cLevel="+txtLevel.Text + " and cat_id<>"+txtCatId.Text ,con);
					SqlDataReader dr=cmd.ExecuteReader();
					dr.Read();
					if (dr.HasRows)
					{
						dr.Close();
						Label10.Visible=true;
						Response.Write("<script language=javascript>alert('" + Label10.Text + "');</script>");
						return;
					}
					else
					{
						dr.Close();

						if (ddSRole.Enabled && ddSRole.SelectedIndex==0)
						{
							Label7.Visible=true;
							Response.Write("<script language=javascript>alert('" + Label7.Text + "');</script>");
							return;
						}
						txtColor.Text=ColourPicker1.SelectedColour.ToString();
						//string str="update t_categorization set cat_name='"+this.txtCatName.Text+"',cat_level='"+this.txtCatLevel.Text+"',cat_criteria='"+this.txtCatCriteria.Text+"',cat_color='"+this.txtCatColor.Text+"',cat_positive='"+this.txtPositive.Text+"',cat_parent='"+Convert.ToInt32(this.ddParentCat.SelectedValue.ToString())+"',supervisory_role='"+Convert.ToInt32(this.ddSRole.SelectedValue.ToString())+"',isactive="+  rblCategory.SelectedValue.ToString() +", CLevel=" + txtLevel.Text + ", Color='" + txtColor.Text + "'   where cat_id='"+Int32.Parse(this.txtCatId.Text)+"'"; 


						string p="";
						
						if (ddParentCat.SelectedIndex==0) 
							p=" NULL ";
						else
							p=ddParentCat.SelectedValue.ToString();

/*						string str="UPDATE dbo.t_Categorization " +
							"SET cat_name = '" + txtCatName.Text + "', cat_level = '" + txtCatLevel.Text + "', cat_criteria = '" + txtCatCriteria.Text.Trim() + "', cat_color = '" + txtCatColor.Text + "', cat_positive = '" + txtPositive.Text + "', cat_parent = " + p + ", supervisory_role = " + ddSRole.SelectedValue + ", " +
							" isactive = " + rblCategory.SelectedValue + ", CLevel = " + txtLevel.Text.Trim() + ", Color = '" + ColourPicker1.SelectedColour.ToString() + "' " +
							"WHERE (cat_id = " + txtCatId.Text + ")";
*/
						string str="UPDATE    dbo.t_Categorization "+
							" SET cat_criteria = @p_cat_criteria, cat_color = @p_cat_color, cat_positive = @p_cat_positive, cat_parent = @p_cat_parent, supervisory_role = @p_supervisory_role, isactive = @p_isactive, CLevel = @p_CLevel, Color = @p_Color "+
							" WHERE (cat_id =@p_cat_id) ";


						SqlParameter p_cat_name        =new SqlParameter("@p_cat_name",SqlDbType.VarChar,50);
						SqlParameter p_cat_level       =new SqlParameter("@p_cat_level",SqlDbType.VarChar,1000);
						SqlParameter p_cat_criteria    =new SqlParameter("@p_cat_criteria",SqlDbType.Text);
						SqlParameter p_cat_color       =new SqlParameter("@p_cat_color",SqlDbType.VarChar,50);
						SqlParameter p_cat_positive    =new SqlParameter("@p_cat_positive",SqlDbType.Text);
						SqlParameter p_cat_parent      =new SqlParameter("@p_cat_parent",SqlDbType.Int);
						SqlParameter p_supervisory_role=new SqlParameter("@p_supervisory_role", SqlDbType.TinyInt);
						SqlParameter p_isactive        =new SqlParameter("@p_isactive",SqlDbType.TinyInt);
						SqlParameter p_CLevel          =new SqlParameter("@p_CLevel",SqlDbType.Int);
						SqlParameter p_Color           =new SqlParameter("@p_Color",SqlDbType.Int);
						SqlParameter p_cat_id          =new SqlParameter("@p_cat_id",SqlDbType.Int);

						p_cat_name.Value=txtCatName.Text.Trim();
						p_cat_level.Value=txtCatLevel.Text.Trim();
						p_cat_criteria.Value=txtCatCriteria.Text.Trim();
						p_cat_color.Value=txtCatColor.Text.Trim();
						p_cat_positive.Value=txtPositive.Text.Trim();
						p_supervisory_role.Value=ddSRole.SelectedValue;
						p_isactive.Value=rblCategory.SelectedValue;;
						p_CLevel.Value=txtLevel.Text.Trim();
						p_Color.Value=txtColor.Text;
						p_cat_id.Value=txtCatId.Text;

						if (ddParentCat.SelectedIndex==0) 
							p_cat_parent.Value=DBNull.Value;
						else
							p_cat_parent.Value=ddParentCat.SelectedValue.ToString();

						SqlCommand c=new SqlCommand(str,con);
						c.Parameters.Add(p_cat_color);
						c.Parameters.Add(p_cat_criteria);
						c.Parameters.Add(p_cat_level);
						c.Parameters.Add(p_cat_name);
						c.Parameters.Add(p_cat_parent);
						c.Parameters.Add(p_cat_positive);
						c.Parameters.Add(p_CLevel);
						c.Parameters.Add(p_Color);
						c.Parameters.Add(p_isactive);
						c.Parameters.Add(p_supervisory_role);
						c.Parameters.Add(p_cat_id);

						//SqlCommand c=new SqlCommand(str,con);
						int i=c.ExecuteNonQuery();
						BindGrid();
						Label10.Visible=false;
						ResetForm();
						FillParentCategoryDDL();
						pnlCategoryForm.Visible=false;
						Button1.Visible=true;


					}
						

				}
				else
				{
						
					//txtColor.Text=ColourPicker1.SelectedColour.ToString();
					//string str="update t_categorization set " + " cat_name='"+this.txtCatName.Text+"',cat_level='"+this.txtCatLevel.Text+"',cat_criteria='"+this.txtCatCriteria.Text+"',cat_color='"+this.txtCatColor.Text+"',cat_positive='"+this.txtPositive.Text+"',cat_parent='"+Convert.ToInt32(this.ddParentCat.SelectedValue.ToString())+"',supervisory_role='"+Convert.ToInt32(this.ddSRole.SelectedValue.ToString())+"',isactive="+  rblCategory.SelectedValue.ToString() +", CLevel=" + txtLevel.Text + ", Color='" + txtColor.Text + "'   where cat_id='"+Int32.Parse(this.txtCatId.Text)+"'"; 
					if (ddSRole.Enabled && ddSRole.SelectedIndex==0)
					{
						Label7.Visible=true;
						return;
					}
/*					string p="";
						
					if (ddParentCat.SelectedIndex==0) 
						p=" NULL ";
					else
						p=ddParentCat.SelectedValue.ToString(); */

/*					string str="UPDATE dbo.t_Categorization " +
						" SET cat_name = '" + txtCatName.Text + "', 
						      cat_level = '" + txtCatLevel.Text + "', 
							  cat_criteria = '" + txtCatCriteria.Text.Trim() + "', 
							  cat_color = '" + txtCatColor.Text + "', 
							  cat_positive = '" + txtPositive.Text + "', 
							  cat_parent = " + p + ", 
							  supervisory_role = " + ddSRole.SelectedValue + ", " +
						"     isactive = " + rblCategory.SelectedValue.ToString() + ", 
						      CLevel = " + txtLevel.Text.Trim() + ", 
							  Color = '" + ColourPicker1.SelectedColour.ToString() + "' " +
						" WHERE (cat_id = " + txtCatId.Text + ")";
*/

					string str="UPDATE    dbo.t_Categorization "+
						" SET cat_criteria = @p_cat_criteria, cat_color = @p_cat_color, cat_positive = @p_cat_positive, cat_parent = @p_cat_parent, supervisory_role = @p_supervisory_role, isactive = @p_isactive, CLevel = @p_CLevel, Color = @p_Color "+
						" WHERE (cat_id =@p_cat_id) ";


					SqlParameter p_cat_name        =new SqlParameter("@p_cat_name",SqlDbType.VarChar,50);
					SqlParameter p_cat_level       =new SqlParameter("@p_cat_level",SqlDbType.VarChar,1000);
					SqlParameter p_cat_criteria    =new SqlParameter("@p_cat_criteria",SqlDbType.Text);
					SqlParameter p_cat_color       =new SqlParameter("@p_cat_color",SqlDbType.VarChar,50);
					SqlParameter p_cat_positive    =new SqlParameter("@p_cat_positive",SqlDbType.Text);
					SqlParameter p_cat_parent      =new SqlParameter("@p_cat_parent",SqlDbType.Int);
					SqlParameter p_supervisory_role=new SqlParameter("@p_supervisory_role", SqlDbType.TinyInt);
					SqlParameter p_isactive        =new SqlParameter("@p_isactive",SqlDbType.TinyInt);
					SqlParameter p_CLevel          =new SqlParameter("@p_CLevel",SqlDbType.Int);
					SqlParameter p_Color           =new SqlParameter("@p_Color",SqlDbType.Int);
					SqlParameter p_cat_id          =new SqlParameter("@p_cat_id",SqlDbType.Int);

					p_cat_name.Value=txtCatName.Text.Trim();
					p_cat_level.Value=txtCatLevel.Text.Trim();
					p_cat_criteria.Value=txtCatCriteria.Text.Trim();
					p_cat_color.Value=txtCatColor.Text.Trim();
					p_cat_positive.Value=txtPositive.Text.Trim();
					p_supervisory_role.Value=ddSRole.SelectedValue;
					p_isactive.Value=rblCategory.SelectedValue;;
					p_CLevel.Value=txtLevel.Text.Trim();
					p_Color.Value=txtColor.Text;
					p_cat_id.Value=txtCatId.Text;

					if (ddParentCat.SelectedIndex==0) 
						p_cat_parent.Value=DBNull.Value;
					else
						p_cat_parent.Value=ddParentCat.SelectedValue.ToString();

					SqlCommand c=new SqlCommand(str,con);
					c.Parameters.Add(p_cat_color);
					c.Parameters.Add(p_cat_criteria);
					c.Parameters.Add(p_cat_level);
					c.Parameters.Add(p_cat_name);
					c.Parameters.Add(p_cat_parent);
					c.Parameters.Add(p_cat_positive);
					c.Parameters.Add(p_CLevel);
					c.Parameters.Add(p_Color);
					c.Parameters.Add(p_isactive);
					c.Parameters.Add(p_supervisory_role);
					c.Parameters.Add(p_cat_id);


					//Response.Write(str);
					//Response.End();
					//SqlCommand c=new SqlCommand(str,con);
					int i=c.ExecuteNonQuery();
					BindGrid();
					Label10.Visible=false;
					ResetForm();
					FillParentCategoryDDL();
					pnlCategoryForm.Visible=false;
					Button1.Visible=true;
					Response.Write("<script language=javascript>alert('Category Information has been updated successfully.');</script>");

				}

				this.btnQueryButton.Text="New Category";
				this.btnCancel.Enabled=false;
				this.txtCatId.Visible=false;
				this.Label1.Visible=false;
					
				this.txtCatId.Text="";
				this.txtCatColor.Text="";
				this.txtCatCriteria.Text="";
				this.txtCatLevel.Text="";
				this.txtCatName.Text="";
				this.txtPositive.Text="";
				this.ddParentCat.SelectedIndex=0;
				this.ddSRole.SelectedIndex=0;
				txtColor.Text="";
				txtLevel.Text="";

				return;
			}


			if (btnQueryButton.Text=="Save")
			{
				
				if(!this.txtCatId.Visible && !this.Label1.Visible)
				{
					
					if (txtLevel.Text!="0")
					{
						SqlCommand cmd = new SqlCommand("select cLevel from t_Categorization where cLevel="+txtLevel.Text,con);
						SqlDataReader dr=cmd.ExecuteReader();
						dr.Read();
						if (dr.HasRows)
						{
							dr.Close();
							Label10.Visible=true;
							return;
						}
						else
						{
							if(ddSRole.Enabled && ddSRole.SelectedIndex==0)
							{
								Label7.Visible=true;
								dr.Close();
								return;
							}
							dr.Close();
							txtColor.Text=ColourPicker1.SelectedColour.ToString();
							/// string str="insert into t_categorization values('"+this.txtCatName.Text+"','"+
							/// this.txtCatLevel.Text+"','"+
							/// this.txtCatCriteria.Text+"','"+
							/// this.txtCatColor.Text+"','"+
							/// this.txtPositive.Text+"','"+Convert.ToInt32(
							/// this.ddParentCat.SelectedValue.ToString())+"','"+Convert.ToInt32(
							/// this.ddSRole.SelectedValue.ToString())+"',"+ 
							/// rblCategory.SelectedValue.ToString()  +"," + 
							/// txtLevel.Text + ",'" + 
							/// txtColor.Text + "')"; 
							string str="INSERT INTO dbo.t_Categorization " +
								" (cat_name, cat_level, cat_criteria, cat_color, cat_positive, cat_parent, supervisory_role, isactive, CLevel, Color) " +
								" VALUES (@p_cat_name, @p_cat_level, @p_cat_criteria, @p_cat_color, @p_cat_positive, @p_cat_parent, @p_supervisory_role, @p_isactive, @p_CLevel, @p_Color) ";

							SqlParameter p_cat_name= new SqlParameter("@p_cat_name",SqlDbType.VarChar, 50); p_cat_name.Value=txtCatName.Text.Trim();
							SqlParameter p_cat_level= new SqlParameter("@p_cat_level",SqlDbType.VarChar, 1000); p_cat_level.Value=txtCatLevel.Text.Trim();
							SqlParameter p_cat_criteria= new SqlParameter("@p_cat_criteria",SqlDbType.Text); p_cat_criteria.Value=txtCatCriteria.Text.Trim();
							SqlParameter p_cat_color= new SqlParameter("@p_cat_color",SqlDbType.VarChar, 50); p_cat_color.Value=txtCatColor.Text.Trim();
							SqlParameter p_cat_positive= new SqlParameter("@p_cat_positive",SqlDbType.Text); p_cat_positive.Value=txtPositive.Text.Trim();
							SqlParameter p_cat_parent= new SqlParameter("@p_cat_parent",SqlDbType.Int); p_cat_parent.Value=ddParentCat.SelectedValue;
							SqlParameter p_supervisory_role= new SqlParameter("@p_supervisory_role",SqlDbType.TinyInt); p_supervisory_role.Value=ddSRole.SelectedValue;
							SqlParameter p_isactive= new SqlParameter("@p_isactive",SqlDbType.TinyInt); p_isactive.Value=rblCategory.SelectedValue;
							SqlParameter p_CLevel= new SqlParameter("@p_CLevel",SqlDbType.Int, 4); p_CLevel.Value=txtLevel.Text;
							SqlParameter p_Color= new SqlParameter("@p_Color",SqlDbType.VarChar, 50); p_Color.Value=txtColor.Text;


							SqlCommand c=new SqlCommand(str,con);
							
							c.Parameters.Add(p_cat_name);
							c.Parameters.Add(p_cat_level);
							c.Parameters.Add(p_cat_criteria);
							c.Parameters.Add(p_cat_color);
							c.Parameters.Add(p_cat_positive);
							c.Parameters.Add(p_cat_parent);
							c.Parameters.Add(p_supervisory_role);
							c.Parameters.Add(p_isactive);
							c.Parameters.Add(p_CLevel);
							c.Parameters.Add(p_Color);


							int i=c.ExecuteNonQuery();
							BindGrid();
							Label10.Visible=false;
							ResetForm();
							FillParentCategoryDDL();
							this.btnQueryButton.Text="New Category";
							pnlCategoryForm.Visible=false;
							Button1.Visible=true;

						}

					}
					else
					{
						if (ddSRole.Enabled && ddSRole.SelectedIndex==0)
						{
							Label7.Visible=true;
							return;
						}
						
						txtColor.Text=ColourPicker1.SelectedColour.ToString();
						/* string str=
						 * "insert into t_categorization 
						 * values('"+this.txtCatName.Text+
						 *     "','"+this.txtCatLevel.Text+"','"+
						 *           this.txtCatCriteria.Text+"','"+
						 *           this.txtCatColor.Text+"','"+
						 *           this.txtPositive.Text+"','"+
						 *           Convert.ToInt32(this.ddParentCat.SelectedValue.ToString())+"','"+
						 *           Convert.ToInt32(this.ddSRole.SelectedValue.ToString())+"',"+ 
						 *           rblCategory.SelectedValue.ToString()  +"," + 
						 *           txtLevel.Text + ",'" + txtColor.Text + "')"; */
						
						string sql="INSERT INTO dbo.t_Categorization " +
							" (cat_name, cat_level, cat_criteria, cat_color, cat_positive, cat_parent, supervisory_role, isactive, CLevel, Color) " +
							" VALUES (@p_cat_name, @p_cat_level, @p_cat_criteria, @p_cat_color, @p_cat_positive, @p_cat_parent, @p_supervisory_role, @p_isactive, @p_CLevel, @p_Color) ";
						
						SqlParameter p_cat_name        =new SqlParameter("@p_cat_name",SqlDbType.VarChar,50);
						SqlParameter p_cat_level       =new SqlParameter("@p_cat_level",SqlDbType.VarChar,1000);
						SqlParameter p_cat_criteria    =new SqlParameter("@p_cat_criteria",SqlDbType.Text);
						SqlParameter p_cat_color       =new SqlParameter("@p_cat_color",SqlDbType.VarChar,50);
						SqlParameter p_cat_positive    =new SqlParameter("@p_cat_positive",SqlDbType.Text);
						SqlParameter p_cat_parent      =new SqlParameter("@p_cat_parent",SqlDbType.Int);
						SqlParameter p_supervisory_role=new SqlParameter("@p_supervisory_role", SqlDbType.TinyInt);
						SqlParameter p_isactive        =new SqlParameter("@p_isactive",SqlDbType.TinyInt);
						SqlParameter p_CLevel          =new SqlParameter("@p_CLevel",SqlDbType.Int);
						SqlParameter p_Color           =new SqlParameter("@p_Color",SqlDbType.Int);


						p_cat_name.Value=txtCatName.Text.Trim();
						p_cat_level.Value=txtCatLevel.Text.Trim();
						p_cat_criteria.Value=txtCatCriteria.Text.Trim();
						p_cat_color.Value=txtCatColor.Text.Trim();
						p_cat_positive.Value=txtPositive.Text.Trim();
						p_cat_parent.Value=ddParentCat.SelectedValue;
						p_supervisory_role.Value=ddSRole.SelectedValue;
						p_isactive.Value=rblCategory.SelectedValue;;
						p_CLevel.Value=txtLevel.Text.Trim();
						p_Color.Value=txtColor.Text;

						SqlCommand c=new SqlCommand(sql,con);
						c.Parameters.Add(p_cat_color);
						c.Parameters.Add(p_cat_criteria);
						c.Parameters.Add(p_cat_level);
						c.Parameters.Add(p_cat_name);
						c.Parameters.Add(p_cat_parent);
						c.Parameters.Add(p_cat_positive);
						c.Parameters.Add(p_CLevel);
						c.Parameters.Add(p_Color);
						c.Parameters.Add(p_isactive);
						c.Parameters.Add(p_supervisory_role);

						int i=c.ExecuteNonQuery();
						BindGrid();
						Label10.Visible=false;
						ResetForm();
						FillParentCategoryDDL();
						this.btnQueryButton.Text="New Category";
						pnlCategoryForm.Visible=false;
						Button1.Visible=true;


					}
				}
					//if(this.txtCatId.Visible)
			}
			//
		}

		private void ResetForm()
		{
			this.btnQueryButton.Text="New Category";
			this.btnCancel.Enabled=false;
			this.txtCatId.Visible=false;
			this.Label1.Visible=false;
			this.txtCatId.Text="";
			this.txtCatColor.Text="";
			this.txtCatCriteria.Text="";
			this.txtCatLevel.Text="";
			this.txtCatName.Text="";
			this.txtPositive.Text="";
			this.ddParentCat.SelectedIndex=0;
			this.ddSRole.SelectedValue="0";
			txtLevel.Text="";
			txtColor.Text="";
			cmdDelete.Enabled=false;

		}
		private void btnCancel_Click(object sender, System.EventArgs e)
		{
			ResetForm();
			pnlCategoryForm.Visible=false;
			Button1.Visible=true;
			Button1.Visible=GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Add");
			BindGrid();
		}

		public void FillParentCategoryDDL()
		{

			string getCat="SELECT TOP 100 PERCENT cat_id, cat_name " +
				" FROM dbo.t_Categorization " + 
				" WHERE (isactive = 2) " +
				" ORDER BY cat_name " ;

			//string getCat="select cat_id,cat_name from t_categorization where isactive='"+1+"'";
			//string getCat="select cat_id,cat_name from t_categorization where isactive=2";
			SqlCommand cmd=new SqlCommand(getCat,con);
			SqlDataReader rd=cmd.ExecuteReader();
			ddParentCat.Items.Clear();
			ddParentCat.Items.Add(new ListItem("Select Parent Category","0"));
			while(rd.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rd[0].ToString();
				itm.Text=rd[1].ToString();
				this.ddParentCat.Items.Add(itm);
			}
			rd.Close();
		}

		public void BindGrid()
		{
			//string getData="select c.cat_id Id,c.cat_name Name,c.cat_level Level,SupervisoryRole=case c.supervisory_role when '1' then 'Yes' when 2 then 'No' End from t_categorization c,t_categorization c2 where c.isactive<>0 And c.cat_parent=c2.cat_id order by "+sort;
			string getData="SELECT case when clevel=0 then 'Super Category' else cast(clevel as varchar) end as clevel2, cat_id as id, cat_name as Name, cat_criteria as Level,  SupervisoryRole=case supervisory_role when '1' then 'Yes' when 2 then 'No' End  " +
				"  FROM dbo.t_Categorization  " +
				"  WHERE (isactive <> 0) " +
				" order by clevel";

			SqlDataAdapter rd=new SqlDataAdapter(getData,con);
			ds=new DataSet();
			rd.Fill(ds,"Category");


			this.DataGrid2.DataSource=ds.Tables[0].DefaultView;
			DataGrid2.DataBind();
		}

		private void DataGrid2_EditCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{


			ddSRole.Enabled=true;
			
			this.btnQueryButton.Text="Save";
			this.btnCancel.Enabled=true;
			this.txtCatId.Visible=true;
			this.txtCatId.Enabled=false;
			this.Label1.Visible=true;
			Session["IsTrue"]="true";
 
			string str="select cat_id,cat_name,cat_level,cat_criteria,cat_color,cat_positive,cat_parent,supervisory_role,CLevel, Color,isactive from t_categorization where cat_id=@Id";
			SqlCommand cmd=new SqlCommand(str,con);
			cmd.Parameters.Add(new SqlParameter("@Id",SqlDbType.VarChar,20));
			cmd.Parameters["@Id"].Value=e.Item.Cells[0].Text;  //this.DataGrid2.DataKeys[e.Item.ItemIndex];
			SqlDataReader rd=cmd.ExecuteReader();
			
			while(rd.Read())
			{
				this.txtCatId.Text=rd[0].ToString();
				this.txtCatName.Text=rd[1].ToString();
				this.txtCatLevel.Text=rd[2].ToString();
				this.txtCatCriteria.Text=rd[3].ToString();
				this.txtCatColor.Text=rd[4].ToString();
				this.txtPositive.Text=rd[5].ToString();

				if (rd["isactive"].ToString()=="1")
				{
					rblCategory.SelectedIndex=0;
					txtLevel.Enabled=true;
					ddParentCat.Enabled=true;
					CollapsablePanel1.Visible=true;
					ddSRole.Enabled=true;
				}
				else if (rd["isactive"].ToString()=="2")
				{
					rblCategory.SelectedIndex=1;
					txtLevel.Text="0";
					txtLevel.Enabled=false;
					ddParentCat.SelectedIndex=0;
					ddParentCat.Enabled=false;
					CollapsablePanel1.Visible=false;
					ddSRole.Enabled=false;
					ddSRole.SelectedIndex=0;
				}
				else
				{
					rblCategory.SelectedIndex=0;
					txtLevel.Enabled=true;
					ddParentCat.Enabled=true;
					CollapsablePanel1.Visible=true;
					ddSRole.Enabled=true;
				}
				int i=0;
				ddParentCat.SelectedIndex=0;
				for(i=1;i<ddParentCat.Items.Count;i++)
				{
					if(ddParentCat.Items[i].Value==rd[6].ToString())
					{
						ddParentCat.SelectedIndex=i;
						break;
					}
				}
				
				//this.ddParentCat.SelectedValue=;
				this.ddSRole.SelectedValue=rd[7].ToString();
				txtLevel.Text=rd[8].ToString();
				txtColor.Text=rd[9].ToString();
				ColourPicker1.SelectedColour=txtColor.Text;
				cmdDelete.Enabled=true;
				btnCancel.Enabled=true;
				pnlCategoryForm.Visible=true;
				btnQueryButton.Text="Update";
				Button1.Visible=false;
				if (ddSRole.SelectedIndex==0)
				{
					ddSRole.Enabled=false;
				}
			}
			rd.Close();
			btnQueryButton.Enabled=GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Edit");
			cmdDelete.Enabled=GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Delete");

			
		}

		private void DataGrid2_DeleteCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{

		}

		private void DataGrid2_SortCommand(object source, System.Web.UI.WebControls.DataGridSortCommandEventArgs e)
		{
			sort=e.SortExpression.ToString();
			BindGrid(); 
		}

		private void ddParentCat_SelectedIndexChanged(object sender, System.EventArgs e)
		{
		
		}

		private void cmdDelete_Click(object sender, System.EventArgs e)
		{
			if (_isRefresh)
			{
				this.DataGrid2.EditItemIndex=-1;
				BindGrid();
				ResetForm();
				pnlCategoryForm.Visible=false;
				Button1.Visible=true;
				return;
			}

			DataSet ds = new DataSet();

			SqlDataAdapter da = new SqlDataAdapter("SELECT     COUNT(dbo.t_Designation.desigid) AS NoOfDesg " +
				" FROM         dbo.t_Categorization INNER JOIN " +
				" dbo.t_Designation ON dbo.t_Categorization.cat_id = dbo.t_Designation.category " +
				" WHERE     (dbo.t_Categorization.cat_id = " + txtCatId.Text + ") AND (dbo.t_Categorization.isactive = 1) AND (dbo.t_Designation.status = 1) ",con);
			
			
			da.Fill(ds,"NoOfDesg");
			if (ds.Tables[0].Rows[0][0].ToString()=="0")
			{

				SqlDataAdapter daP = new SqlDataAdapter("select clevel, cat_id, cat_name,  cat_parent,isactive from dbo.t_Categorization where isactive<>0 and cat_parent=6 ",con);
				daP.Fill(ds,"Parent");

				if (ds.Tables["Parent"].Rows.Count>0)
				{
					Label12.Visible=true;
					Response.Write("<script language=javascript>alert('You can not delete this category, Please first delete related designation or sub category')</scritp>");
				}
				else
				{
					string str="update t_categorization set isactive=0 where cat_id=@Id";
					SqlCommand cm=new SqlCommand(str,con);
					cm.Parameters.Add(new SqlParameter("@Id",SqlDbType.VarChar,20));
					cm.Parameters["@Id"].Value= txtCatId.Text; //this.DataGrid2.DataKeys[e.Item.ItemIndex]; 
					cm.ExecuteNonQuery();
					this.DataGrid2.EditItemIndex=-1;
					BindGrid();
			
					ResetForm();
					pnlCategoryForm.Visible=false;
					Button1.Visible=true;
					Response.Write("<script language=javascript>alert('Category Infomation Has Been Removed');</script>");
				}
			}
			else
			{
				Label12.Visible=true;
				Response.Write("<script language=javascript>alert('" + Label12.Text + "');</script>");
			}
		}

		private void LinkButton1_Click(object sender, System.EventArgs e)
		{
			if(ColourPicker1.Visible)
				ColourPicker1.Visible=false;
			else
				ColourPicker1.Visible=true;
		}

		private void rblCategory_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			if (rblCategory.SelectedValue=="2")
			{
				txtLevel.Text="0";
				txtLevel.Enabled=false;
				ddParentCat.SelectedIndex=0;
				ddParentCat.Enabled=false;
				ddSRole.Enabled=false;
				CollapsablePanel1.Visible=false;
			}
			else
			{
				txtLevel.Text=GetNewLevel();
				txtLevel.Enabled=true;
				ddParentCat.Enabled=true;
				ddSRole.Enabled=true;
				CollapsablePanel1.Visible=true;
				
			}
		}

		private void Button1_Click(object sender, System.EventArgs e)
		{
			Session["IsTrue"]="false";
			this.btnQueryButton.Text="Save";
			this.btnCancel.Enabled=true;
			this.txtCatId.Text="";
			this.txtCatColor.Text="";
			this.txtCatCriteria.Text="";
			this.txtCatLevel.Text="";
			this.txtCatName.Text="";
			this.txtPositive.Text="";
			this.ddParentCat.SelectedIndex=0;
			this.ddSRole.SelectedIndex=0;
			ColourPicker1.SelectedColour="#FFFFFF";
			txtLevel.Text="";
			btnCancel.Enabled=true;
			cmdDelete.Enabled=false;
			txtColor.Text="";
			txtLevel.Text=GetNewLevel();
				
			
			pnlCategoryForm.Visible=true;
			Button1.Visible=false;
			btnQueryButton.Text="Save";
			btnCancel.Enabled=true;
		}
	}
}
