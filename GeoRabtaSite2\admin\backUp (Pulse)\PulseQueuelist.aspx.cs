using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;
namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for GeoIdeas.
	/// </summary>
	public class PulseQueueList: System.Web.UI.Page
	{
		protected System.Web.UI.WebControls.ImageButton ImageButton1;
		protected System.Web.UI.WebControls.ImageButton ImageButton2;
		protected System.Web.UI.WebControls.DataGrid dgLivePulse;
		protected System.Web.UI.WebControls.Panel pnlMain1;
		protected System.Web.UI.WebControls.Label lblPulseID;
		protected System.Web.UI.WebControls.Label lblCreateon;
		protected System.Web.UI.WebControls.Label lblType;
		protected System.Web.UI.WebControls.Label lblTitle;
		protected System.Web.UI.WebControls.Label lblPcode;
		protected System.Web.UI.WebControls.Label lblName;
		protected System.Web.UI.WebControls.Label lblDepartment;
		protected System.Web.UI.WebControls.Label lblDesignation;
		protected System.Web.UI.WebControls.Label lblpDepartment;
		protected System.Web.UI.WebControls.Label lblpStation;
		protected System.Web.UI.WebControls.Label lblDescription;
		protected System.Web.UI.WebControls.Panel pnlSubMain1;
		protected System.Web.UI.WebControls.Label lblhPulseID;
		protected System.Web.UI.WebControls.DataGrid dgPulseBlog;
		protected System.Web.UI.WebControls.Panel pnlSubMain2;
		protected System.Web.UI.WebControls.Image Image1;
		protected System.Web.UI.WebControls.TextBox txtBlog;
		protected System.Web.UI.WebControls.DropDownList ddActionCode;
		protected eWorld.UI.CalendarPopup calInterimDate;
		protected System.Web.UI.WebControls.Label Label1;
		protected System.Web.UI.WebControls.CheckBoxList chkReportingList;
		protected System.Web.UI.WebControls.Button btnSend;
		protected System.Web.UI.WebControls.Panel pnlSubMain3;
		protected System.Web.UI.HtmlControls.HtmlTable tabForm;
		protected System.Web.UI.HtmlControls.HtmlTableCell r1;
		protected System.Web.UI.HtmlControls.HtmlTableCell r2;
		protected System.Web.UI.WebControls.Label lbltag;
		protected System.Web.UI.WebControls.Label lblsendmsg;
		protected System.Web.UI.WebControls.Panel Panel1;
		protected System.Web.UI.WebControls.DataGrid dgDeferred;
		protected System.Web.UI.WebControls.Panel pnlMain2;
		protected System.Web.UI.HtmlControls.HtmlTableCell rmain;
		protected System.Web.UI.HtmlControls.HtmlTableCell rmain1;
		protected System.Web.UI.HtmlControls.HtmlTableCell rdes;
		protected System.Web.UI.WebControls.TextBox txtX;
		protected System.Web.UI.WebControls.TextBox txtY;
		protected System.Web.UI.WebControls.ImageButton imgBack;
        SqlConnection con;
		protected System.Web.UI.WebControls.Label Label2;
		protected System.Web.UI.WebControls.ImageButton imgNew;
		string userId="";
		private bool IsPageAccessAllowed()
		{

			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception ex)
			{
				string s=ex.Message;
				Response.Redirect("../Login.aspx");
			}
 
			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(2,58,userId,"View")==true)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("../Login.aspx");
				return false;
			}
		}
		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		private void InitializeComponent()
		{
			this.ImageButton1.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton1_Click);
			this.ImageButton2.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton2_Click);
			this.imgNew.Click += new System.Web.UI.ImageClickEventHandler(this.imgNew_Click);
			this.imgBack.Click += new System.Web.UI.ImageClickEventHandler(this.imgBack_Click);
			this.dgLivePulse.SelectedIndexChanged += new System.EventHandler(this.dgLivePulse_SelectedIndexChanged);
			this.dgDeferred.SelectedIndexChanged += new System.EventHandler(this.dgDeferred_SelectedIndexChanged);
			this.ddActionCode.SelectedIndexChanged += new System.EventHandler(this.ddActionCode_SelectedIndexChanged);
			this.btnSend.Click += new System.EventHandler(this.btnSend_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
#endregion
		private void Page_Load(object sender, System.EventArgs e)
		{
			if(IsPageAccessAllowed())
			{
				if (GeoSecurity.isControlVisible(2,59,Session["user_id"].ToString(),"View"))
				{
					imgNew.Visible=true;
				}
				else
				{
					imgNew.Visible=false;
				}
				if(!IsPostBack)
				{
					GetMyPulse(Session["user_id"].ToString());
					GetActionCode();
					r1.Visible=false;
					r2.Visible=false;
					rmain.Visible=false;
					rmain1.Visible=true;
					rdes.Visible=false;
					btnSend.Attributes.Add("onclick","return ValidatePage();");
					ListItem itm=this.ddActionCode.Items.FindByText("Re-Open");
					this.ddActionCode.Items.Remove(itm);
					this.Label2.Text="Current";
				}
			}
			else
			{
			 Response.Redirect("ErrorPage.aspx");
			}
		}

		public void GetMyPulse(string pcode)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlDataAdapter dr=new SqlDataAdapter("select '<img src=../empthumbnail/' + e.pic + ' width=55>' as pic,e.name as [Pulse Initiate By],p.pid as [Pulse ID],p.title,p.description,p.pulsecreateon,[Current Status]=case p.currentstatus when 0 then 'InActive' when 1 then 'In Process' when 2 then 'Closed' when 3 then 'Re-Open' when 4 then 'Interim Closed'end,[TaskSheet]=case (case p.pid when p.pid then(select count(pulseid) from t_pulsetasksheet where addto='"+Session["user_id"].ToString()+"' and pulseid=p.pid) end) when 0 then 'No' else 'Yes' end,Blog=case p.pid when p.pid then(select '('+cast(count(*) as varchar)+')' from t_pulseprogressblog where p.pid=pulseid)end,c.pulsecategory as pulsetype,e.pcode,ides=case p.sendto when p.sendto then(select d.designation from t_designation d where d.desigid=e.desigid)end,idept=case p.sendto when p.sendto then(select dept.deptname from t_department dept,t_designation d where e.desigid=d.desigid and dept.deptid=d.deptid)end,pdept=case p.deptid when p.deptid then(select dept.deptname from t_department dept where dept.deptid=p.deptid)end,pcity=case p.station when p.station then(select c.cityname from t_city c where c.cityid=p.station)end,CASE p.pid WHEN p.pid THEN (SELECT TOP (1) PERCENT CASE pg.psendby WHEN 'Raabta ' THEN 'Raabta, ' + CAST(pg.pdate AS varchar) ELSE (SELECT     e.name + ', ' + CAST(pg.pdate AS varchar) FROM t_employee e WHERE e.pcode = pg.psendby) END AS Sendby FROM dbo.t_PulseProgressBlog AS pg WHERE  (pg.pulseid = p.pid) ORDER BY pg.pdate DESC) END AS [Last Update by] from t_pulse p,t_pulsehandler h,t_employee e,t_pulsecategory c where (h.psendto='"+Session["user_id"].ToString()+"')  and e.pcode=p.pcode and (p.currentstatus=1 or p.currentstatus=3) and h.pulseid=p.pid and p.pulsecategory=c.pulseid AND (h.isactive = 1) order by p.pulsecreateon",con);
			DataSet ds=new DataSet();
			dr.Fill(ds,"Records");
			this.dgLivePulse.DataSource=ds;
			this.dgLivePulse.DataBind();
			con.Close();
			con.Dispose();
		}
		public void GetMyPulse(string pcode,bool flag)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlDataAdapter dr=new SqlDataAdapter("select '<img src=../empthumbnail/' + e.pic + ' width=55>' as pic,e.name as [Pulse Initiate By],p.pid as [Pulse ID],p.title,p.description,p.pulsecreateon,[Current Status]=case p.currentstatus when 0 then 'InActive' when 1 then 'In Process' when 2 then 'Closed' when 3 then 'Re-Open' when 4 then 'Interim Closed'end,[TaskSheet]=case (case p.pid when p.pid then(select count(pulseid) from t_pulsetasksheet where addto='"+Session["user_id"].ToString()+"' and pulseid=p.pid) end) when 0 then 'No' else 'Yes' end,Blog=case p.pid when p.pid then(select '('+cast(count(*) as varchar)+')' from t_pulseprogressblog where p.pid=pulseid)end,c.pulsecategory as pulsetype,e.pcode,ides=case p.sendto when p.sendto then(select d.designation from t_designation d where d.desigid=e.desigid)end,idept=case p.sendto when p.sendto then(select dept.deptname from t_department dept,t_designation d where e.desigid=d.desigid and dept.deptid=d.deptid)end,pdept=case p.deptid when p.deptid then(select dept.deptname from t_department dept where dept.deptid=p.deptid)end,pcity=case p.station when p.station then(select c.cityname from t_city c where c.cityid=p.station)end from t_pulse p,t_pulsehandler h,t_employee e,t_pulsecategory c where (h.psendto='"+Session["user_id"].ToString()+"')  and e.pcode=p.pcode and (p.currentstatus=2 or p.currentstatus=4) and h.pulseid=p.pid and p.pulsecategory=c.pulseid AND (h.isactive = 1) order by p.pulsecreateon",con);
			DataSet ds=new DataSet();
			dr.Fill(ds,"Records");
			this.dgDeferred.DataSource=ds;
			this.dgDeferred.DataBind();
			this.dgDeferred.Visible=true;
			con.Close();
			con.Dispose();
		}
		public void GetActionCode()
		{
		 this.ddActionCode.Items.Clear();
		 this.ddActionCode.Items.Add(new ListItem("Select--Action Code","0"));
		 con=new SqlConnection(Connection.ConnectionString);
		 con.Open();
		 SqlCommand cmd=new SqlCommand("select actionid,actioncode from t_pulseactioncode where isactive=1 order by actioncode",con);
		 SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
			 ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
			 this.ddActionCode.Items.Add(itm);
			}
		 rd.Close();
		 con.Close();
		}
		public void GetActionCode(bool flag)
		{
			this.ddActionCode.Items.Clear();
			this.ddActionCode.Items.Add(new ListItem("Select--Action Code","0"));
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select actionid,actioncode from t_pulseactioncode where isactive=1 and actionid in (1,9,11) order by actioncode",con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
				this.ddActionCode.Items.Add(itm);
			}
			rd.Close();
			con.Close();
		}
		public void GetPulseBlog(string ID)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			//SqlDataAdapter dr=new SqlDataAdapter("select '<img src=../empthumbnail/' + rtrim(b.psendby) + '.jpg width=55>' as pic,b.progressblog,[Send By]=case b.psendby when 'Raabta' then 'Raabta' else (select e.name from t_employee e where e.pcode=b.psendby)end,b.pdate,[Action Code]=case COALESCE (b.actioncode,0) when 0 then '' else (select a.actioncode from t_pulseactioncode a where a.actionid=b.actioncode) end from t_pulseprogressblog b where b.pulseid='"+ID+"' and isactive=1 order by pdate asc",con);
			SqlDataAdapter dr=new SqlDataAdapter("select b.progressblog,[Send By]=case b.psendby when 'Raabta' then 'Raabta' else (select e.name from t_employee e where e.pcode=b.psendby)end,b.pdate,[Action Code]=case COALESCE (b.actioncode,0) when 0 then '' else (select a.actioncode from t_pulseactioncode a where a.actionid=b.actioncode) end from t_pulseprogressblog b where b.pulseid='"+ID+"' and isactive=1 order by pdate asc",con);
			DataSet ds=new DataSet();
			dr.Fill(ds,"Records");
			this.dgPulseBlog.DataSource=ds;
			this.dgPulseBlog.DataBind();
			this.dgPulseBlog.Visible=true;
			con.Close();
			con.Dispose();
			for(int i=0;i<this.dgPulseBlog.Items.Count;i++)
			{
				if(this.dgPulseBlog.Items[i].Cells[4].Text.Equals("Add to Issue & Task Sheet"))
				{
					Button grdBox=(Button)dgPulseBlog.Items[i].FindControl("btnSelect");
					grdBox.Visible=true;
				}
				else if(this.dgPulseBlog.Items[i].Cells[4].Text.Equals("Advice Require from BU Heads"))
				{
					Button grdBox=(Button)dgPulseBlog.Items[i].FindControl("btnSelect");
					grdBox.Visible=true;
				}
				else if(this.dgPulseBlog.Items[i].Cells[4].Text.Equals("Send to MyLine Manager"))
				{
					Button grdBox=(Button)dgPulseBlog.Items[i].FindControl("btnSelect");
					grdBox.Visible=true;
				}
				else if(this.dgPulseBlog.Items[i].Cells[4].Text.Equals("Send to MyTeam"))
				{
					Button grdBox=(Button)dgPulseBlog.Items[i].FindControl("btnSelect");
					grdBox.Visible=true;
				}
				else
				{
					Button grdBox=(Button)dgPulseBlog.Items[i].FindControl("btnSelect");
					grdBox.Visible=false;
				}
			}
		}
		public void getMe(object sender,EventArgs e)
		{
			Button btn=new Button();
			btn=(Button)sender; 
			if(btn.Text=="+")
			{
				Button grdBox=new Button();	
				for(int i=0;i<this.dgDeferred.Items.Count;i++)
				{
					grdBox=(Button)this.dgDeferred.Items[i].FindControl("btnSel");
					if(grdBox.Equals(btn))
					{
						btn.Text="-";
						DataGrid dgGrid=new DataGrid();
						dgGrid=(DataGrid)this.dgDeferred.Items[i].FindControl("dgStatus");
						dgGrid.Visible=true;
						con=new SqlConnection(Connection.ConnectionString);
						con.Open();
						//string Query="SELECT e.name, s.schangeddate,CASE s.statuslog WHEN 1 THEN 'Active' WHEN 0 THEN 'InActive' WHEN 2 THEN 'Closed' WHEN 3 THEN 'Re-Open' WHEN 4 THEN 'Commit Future Date' END AS status FROM dbo.t_PulseStatusLog AS s INNER JOIN "+
                        //"dbo.t_Employee AS e ON s.schangedby = e.pcode WHERE (s.pulseid = '"+this.dgDeferred.Items[i].Cells[1].Text.Trim()+"') AND (s.isactive = 1) ORDER BY s.schangeddate";
						string Query="select name=case s.schangedby when 'Raabta' then 'Raabta' else (select e.name from t_employee e where e.pcode=s.schangedby)end,s.schangeddate,CASE s.statuslog WHEN 1 THEN 'Active' WHEN 0 THEN 'InActive' WHEN 2 THEN 'Closed' WHEN 3 THEN 'Re-Open' WHEN 4 THEN 'Commit Future Date' END as status from t_pulsestatuslog s where s.pulseid='"+this.dgDeferred.Items[i].Cells[1].Text.Trim()+"' order by s.schangeddate";
						SqlDataAdapter rd=new SqlDataAdapter(Query,con);
						DataSet ds=new DataSet();
						rd.Fill(ds,"Record");
						dgGrid.DataSource=ds;
						dgGrid.DataBind(); 
						if(dgGrid.Items.Count<=0)
						{
							dgGrid.Visible=false;
						}
						else
						{
							dgGrid.Visible=true;
						}
						con.Close();
					}
				}
			}
			else
			{
				btn.Text="+";
				Button grdBox=new Button();	
				for(int i=0;i<this.dgDeferred.Items.Count;i++)
				{
					grdBox=(Button)this.dgDeferred.Items[i].FindControl("btnSel");
					if(grdBox.Equals(btn))
					{
						DataGrid dgGrid=new DataGrid();
						dgGrid=(DataGrid)this.dgDeferred.Items[i].FindControl("dgStatus");
						dgGrid.Visible=false;
					}
				}
			}
		}
		public void getRecords(object sender,EventArgs e)
		{
			Button btn=new Button();
			btn=(Button)sender; 
			if(btn.Text=="+")
			{
				Button grdBox=new Button();	
				for(int i=0;i<this.dgPulseBlog.Items.Count;i++)
				{
					grdBox=(Button)this.dgPulseBlog.Items[i].FindControl("btnSelect");
					if(grdBox.Equals(btn))
					{
						btn.Text="-";
						DataGrid dgGrid=new DataGrid();
						dgGrid=(DataGrid)this.dgPulseBlog.Items[i].FindControl("dgRecord1");
						dgGrid.Visible=true;
						con=new SqlConnection(Connection.ConnectionString);
						con.Open();
						string fillGrid="";
						if(this.dgPulseBlog.Items[i].Cells[4].Text=="Add to Issue & Task Sheet")
						{
							fillGrid="select e.name from t_employee e,t_pulsetasksheet s where e.pcode=s.addto and s.addon='"+this.dgPulseBlog.Items[i].Cells[3].Text.Trim()+"' and isactive=1";	
						}
						if(this.dgPulseBlog.Items[i].Cells[4].Text=="Advice Require from BU Heads" || this.dgPulseBlog.Items[i].Cells[4].Text=="Send to MyLine Manager" || this.dgPulseBlog.Items[i].Cells[4].Text=="Send to MyTeam")
						{
							fillGrid="select e.name from t_employee e,t_pulsehandler s where e.pcode=s.psendto and s.psenddate='"+this.dgPulseBlog.Items[i].Cells[3].Text.Trim()+"' and isactive=1";	
						}
						SqlDataAdapter rd=new SqlDataAdapter(fillGrid,con);
						DataSet ds=new DataSet();
						rd.Fill(ds,"Record");
						dgGrid.DataSource=ds;
						dgGrid.DataBind(); 
						if(dgGrid.Items.Count<=0)
						{
							dgGrid.Visible=false;
						}
						else
						{
							dgGrid.Visible=true;
						}
						con.Close(); 
					}
				}
			}
			else
			{
				btn.Text="+";
				Button grdBox=new Button();	
				for(int i=0;i<this.dgPulseBlog.Items.Count;i++)
				{
					grdBox=(Button)this.dgPulseBlog.Items[i].FindControl("btnSelect");
					if(grdBox.Equals(btn))
					{
						DataGrid dgGrid=new DataGrid();
						dgGrid=(DataGrid)this.dgPulseBlog.Items[i].FindControl("dgRecord1");
						dgGrid.Visible=false;
					}
				}
			}
		}
		private void dgLivePulse_SelectedIndexChanged(object sender, System.EventArgs e)
		{
		 Reset();
		 this.lblPulseID.Text=this.dgLivePulse.Items[this.dgLivePulse.SelectedIndex].Cells[1].Text;
		 this.lblhPulseID.Text=lblPulseID.Text;
		 this.lblName.Text=this.dgLivePulse.Items[this.dgLivePulse.SelectedIndex].Cells[3].Text;
		 this.lblTitle.Text=this.dgLivePulse.Items[this.dgLivePulse.SelectedIndex].Cells[4].Text;
		 this.lblType.Text=this.dgLivePulse.Items[this.dgLivePulse.SelectedIndex].Cells[5].Text;
		 this.lblDescription.Text=this.dgLivePulse.Items[this.dgLivePulse.SelectedIndex].Cells[6].Text;
		 this.lblCreateon.Text=this.dgLivePulse.Items[this.dgLivePulse.SelectedIndex].Cells[7].Text;
		 this.lblPcode.Text=this.dgLivePulse.Items[this.dgLivePulse.SelectedIndex].Cells[11].Text;
		 this.lblDesignation.Text=this.dgLivePulse.Items[this.dgLivePulse.SelectedIndex].Cells[12].Text;
		 this.lblDepartment.Text=this.dgLivePulse.Items[this.dgLivePulse.SelectedIndex].Cells[13].Text;
		 this.lblpDepartment.Text=this.dgLivePulse.Items[this.dgLivePulse.SelectedIndex].Cells[14].Text;
		 this.lblpStation.Text=this.dgLivePulse.Items[this.dgLivePulse.SelectedIndex].Cells[15].Text;
		 this.pnlSubMain1.Visible=true;
		 GetPulseBlog(this.lblPulseID.Text);
		 this.pnlSubMain2.Visible=true;
		 this.pnlSubMain3.Visible=true;
		}
		public void Reset()
		{
			this.lblCreateon.Text="";
			this.lblDepartment.Text="";
			this.lblDescription.Text="";
			this.lblDesignation.Text="";
			this.lblName.Text="";
			this.lblPcode.Text="";
			this.lblpDepartment.Text="";
			this.lblpStation.Text="";
			this.lblPulseID.Text="";
			this.lblhPulseID.Text="";
			this.lblTitle.Text="";
			this.lblType.Text="";
			r1.Visible=false;
			r2.Visible=false;
		}

		private void ddActionCode_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			this.lblsendmsg.Text="";
			r1.Visible=false;
			r2.Visible=false;
			if(this.ddActionCode.SelectedItem.Value=="7")
			{
				this.chkReportingList.Items.Clear();
				MyTeam team=new MyTeam();
				ArrayList _list=team.getDirectTeam(Session["user_id"].ToString());
				ArrayList tempList=new ArrayList();
				for(int j=0;j<_list.Count;j++)
				{
					string member=(string)_list[j];
					string []memberStr=member.Split('^');
					tempList.Add(memberStr[2]);
				}
				tempList.Sort();
				for(int k=0;k<tempList.Count;k++)
				{
					string members=(string)tempList[k];
					for(int z=0;z<_list.Count;z++)
					{
						string member=(string)_list[z];
						string []memberStr=member.Split('^');
						if(memberStr[2].Equals(members))
						{
							ListItem itm=new ListItem(members,memberStr[0]);
							this.chkReportingList.Items.Add(itm);
						}
					}			  
				}
				this.Label1.Text="My Team";
				r2.Visible=true;
				this.lbltag.Text="Based on Organogram";
			}
			else if(this.ddActionCode.SelectedItem.Value=="8")
			{
				this.chkReportingList.Items.Clear();
				MyTeam team=new MyTeam();
				ArrayList _list=team.getBoss(Session["user_id"].ToString(),1);
				ArrayList tempList=new ArrayList();
				for(int j=0;j<_list.Count;j++)
				{
					string member=(string)_list[j];
					string []memberStr=member.Split('^');
					tempList.Add(memberStr[2]);
				}
				tempList.Sort();
				for(int k=0;k<tempList.Count;k++)
				{
					string members=(string)tempList[k];
					for(int z=0;z<_list.Count;z++)
					{
						string member=(string)_list[z];
						string []memberStr=member.Split('^');
						if(memberStr[2].Equals(members))
						{
							ListItem itm=new ListItem(members,memberStr[0]);
							this.chkReportingList.Items.Add(itm);
						}
					}			  
				}
				r2.Visible=true;
				this.Label1.Text="My Line Manager(s)";
				this.lbltag.Text="Based on Organogram";
			}
			else if(this.ddActionCode.SelectedItem.Value=="2")
			{
				r1.Visible=true;
				r2.Visible=false;
			}
			else if(this.ddActionCode.SelectedItem.Value=="3")
			{
				this.chkReportingList.Items.Clear();
				MyTeam team=new MyTeam();
				ArrayList _list=team.getDirectTeam(Session["user_id"].ToString());
				ArrayList tempList=new ArrayList();
				for(int j=0;j<_list.Count;j++)
				{
					string member=(string)_list[j];
					string []memberStr=member.Split('^');
					tempList.Add(memberStr[2]);
				}
				tempList.Sort();
				for(int k=0;k<tempList.Count;k++)
				{
					string members=(string)tempList[k];
					for(int z=0;z<_list.Count;z++)
					{
						string member=(string)_list[z];
						string []memberStr=member.Split('^');
						if(memberStr[2].Equals(members))
						{
							ListItem itm=new ListItem(members,memberStr[0]);
							this.chkReportingList.Items.Add(itm);
						}
					}			  
				}
				this.Label1.Text="My Team";
				r2.Visible=true;
				this.lbltag.Text="Based on Organogram";
			}
			else if (this.ddActionCode.SelectedItem.Value=="4")
			{
				this.chkReportingList.Items.Clear();
				con=new SqlConnection(Connection.ConnectionString);
				con.Open();
				SqlCommand cmd=new SqlCommand("SELECT dbo.t_Employee.pcode, dbo.t_Designation.designation, dbo.t_Employee.del, dbo.t_Employee.name "+
					"FROM dbo.t_Employee INNER JOIN "+
					"dbo.t_Designation ON dbo.t_Employee.desigid = dbo.t_Designation.desigid "+
					"WHERE (dbo.t_Designation.desigid =2642 ) AND (dbo.t_Employee.del = 1)",con);
				SqlDataReader rd=cmd.ExecuteReader();
				while(rd.Read())
				{
					ListItem itm=new ListItem(rd[3].ToString(),rd[0].ToString());
					this.chkReportingList.Items.Add(itm);
				}
				rd.Close();
				con.Close();
				this.Label1.Text="CEO-GEO Network";
				this.lbltag.Text="Based on Raabta";
				r2.Visible=true;
			}
			else if (this.ddActionCode.SelectedItem.Value=="5")
			{
				this.chkReportingList.Items.Clear();
				con=new SqlConnection(Connection.ConnectionString);
				con.Open();
				SqlCommand cmd=new SqlCommand("SELECT dbo.t_Employee.pcode, dbo.t_Designation.designation, dbo.t_Employee.del, dbo.t_Employee.name "+
					"FROM dbo.t_Employee INNER JOIN "+
					"dbo.t_Designation ON dbo.t_Employee.desigid = dbo.t_Designation.desigid "+
					"WHERE (dbo.t_Designation.desigid =2001 ) AND (dbo.t_Employee.del = 1)",con);
				SqlDataReader rd=cmd.ExecuteReader();
				while(rd.Read())
				{
					ListItem itm=new ListItem(rd[3].ToString(),rd[0].ToString());
					this.chkReportingList.Items.Add(itm);
				}
				rd.Close();
				con.Close();
				this.Label1.Text="COO-GEO Network";
				this.lbltag.Text="Based on Raabta";
				r2.Visible=true;
			}
			else if(this.ddActionCode.SelectedItem.Value=="6")
			{
			 string Query="SELECT DISTINCT dbo.t_Employee.pcode, dbo.t_Employee.name FROM dbo.t_Employee INNER JOIN dbo.t_sbuheads ON dbo.t_Employee.pcode = dbo.t_sbuheads.pcode "+
             "WHERE (dbo.t_Employee.del = 1) order by dbo.t_Employee.name";
				this.chkReportingList.Items.Clear();
				con=new SqlConnection(Connection.ConnectionString);
				con.Open();
				SqlCommand cmd=new SqlCommand(Query,con);
				SqlDataReader rd=cmd.ExecuteReader();
				while(rd.Read())
				{
					ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
					this.chkReportingList.Items.Add(itm);
				}
				rd.Close();
				con.Close();
				this.Label1.Text="SBU Heads-GEO Network";
				this.lbltag.Text="Based on Raabta SBU Head";
				r2.Visible=true;
			}
		}

		private void btnSend_Click(object sender, System.EventArgs e)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlTransaction trans=con.BeginTransaction();
			string datetime=DateTime.Now.ToString("dd MMM,yyyy h:mm tt");
			if(this.ddActionCode.SelectedItem.Value=="0")
			{
				SqlCommand cmd=new SqlCommand("insert into t_pulseprogressblog (pulseid,progressblog,psendby,pdate,actioncode,interimcloseddate,isactive) values(@pulseid,@progressblog,@psendby,@pdate,@actioncode,@interimcloseddate,@isactive)",con);
				SqlParameter _ppid=new SqlParameter("@pulseid",SqlDbType.VarChar);
				SqlParameter _blog=new SqlParameter("@progressblog",SqlDbType.Text);
				SqlParameter _psend=new SqlParameter("@psendby",SqlDbType.Char);
				SqlParameter _pdate=new SqlParameter("@pdate",SqlDbType.SmallDateTime);
				SqlParameter _acode=new SqlParameter("@actioncode",SqlDbType.Int);
				SqlParameter _iclose=new SqlParameter("@interimcloseddate",SqlDbType.SmallDateTime);
				SqlParameter _active=new SqlParameter("@isactive",SqlDbType.Int);
				cmd.Transaction=trans;
				_ppid.Value=lblhPulseID.Text.Trim();
				_blog.Value=this.txtBlog.Text;
				_psend.Value=Session["user_id"].ToString();
				_pdate.Value=datetime;
				_acode.Value=DBNull.Value;
				_iclose.Value=DBNull.Value;
				_active.Value=1;
				cmd.Parameters.Add(_ppid);
				cmd.Parameters.Add(_blog);
				cmd.Parameters.Add(_psend);
				cmd.Parameters.Add(_pdate);
				cmd.Parameters.Add(_acode);
				cmd.Parameters.Add(_iclose);
				cmd.Parameters.Add(_active);
				cmd.ExecuteNonQuery();
				trans.Commit();
				this.lblsendmsg.Visible=true;
				this.lblsendmsg.Text="New Blog has been sent successfully";
				GetPulseBlog(lblhPulseID.Text.Trim());
				GetMyPulse(Session["user_id"].ToString());
				this.ddActionCode.SelectedIndex=0;
				this.txtBlog.Text="";
				r1.Visible=false;
				r2.Visible=false;
				
			}
			else if(this.ddActionCode.SelectedItem.Value=="10")
			{
				SqlCommand cmd=new SqlCommand("insert into t_pulseprogressblog (pulseid,progressblog,psendby,pdate,actioncode,interimcloseddate,isactive) values(@pulseid,@progressblog,@psendby,@pdate,@actioncode,@interimcloseddate,@isactive)",con);
				SqlParameter _ppid=new SqlParameter("@pulseid",SqlDbType.VarChar);
				SqlParameter _blog=new SqlParameter("@progressblog",SqlDbType.Text);
				SqlParameter _psend=new SqlParameter("@psendby",SqlDbType.Char);
				SqlParameter _pdate=new SqlParameter("@pdate",SqlDbType.SmallDateTime);
				SqlParameter _acode=new SqlParameter("@actioncode",SqlDbType.Int);
				SqlParameter _iclose=new SqlParameter("@interimcloseddate",SqlDbType.SmallDateTime);
				SqlParameter _active=new SqlParameter("@isactive",SqlDbType.Int);
				cmd.Transaction=trans;
				_ppid.Value=lblhPulseID.Text.Trim();
				_blog.Value=this.txtBlog.Text+"<Font color=orange><b><br><br>System Generated Message: Pulse Acknowledged</b></Font>";
				_psend.Value=Session["user_id"].ToString();
				_pdate.Value=datetime;
				_acode.Value=this.ddActionCode.SelectedItem.Value;
				_iclose.Value=DBNull.Value;
				_active.Value=1;
				cmd.Parameters.Add(_ppid);
				cmd.Parameters.Add(_blog);
				cmd.Parameters.Add(_psend);
				cmd.Parameters.Add(_pdate);
				cmd.Parameters.Add(_acode);
				cmd.Parameters.Add(_iclose);
				cmd.Parameters.Add(_active);
				cmd.ExecuteNonQuery();

				cmd=new SqlCommand("update t_pulse set currentactioncode="+this.ddActionCode.SelectedItem.Value+" where pid='"+lblhPulseID.Text+"'",con);
				cmd.Transaction=trans;
				cmd.ExecuteNonQuery();
				trans.Commit();
				this.lblsendmsg.Visible=true;
				this.lblsendmsg.Text="New Blog has been sent successfully";
				GetPulseBlog(lblhPulseID.Text.Trim());
				GetMyPulse(Session["user_id"].ToString());
				this.ddActionCode.SelectedIndex=0;
				this.txtBlog.Text="";
				r1.Visible=false;
				r2.Visible=false;
			}
			else if(this.ddActionCode.SelectedItem.Value=="3")
			{
				string name="";
				SqlCommand cmd=null;
				for(int i=0;i<this.chkReportingList.Items.Count;i++)
				{
					if(this.chkReportingList.Items[i].Selected)
					{
						SqlConnection conn=new SqlConnection(Connection.ConnectionString);
						conn.Open();
						ListItem itm=chkReportingList.Items[i];
						cmd=new SqlCommand("select * from t_pulsetasksheet where addto='"+itm.Value+"' and isactive=1 and pulseid='"+lblhPulseID.Text.Trim()+"'",conn);
						name+=itm.Text+",";
						SqlDataReader rd=cmd.ExecuteReader();
						rd.Read();
						if(!rd.HasRows)
						{
							rd.Close();
							cmd=new SqlCommand("insert into t_pulsetasksheet(pulseid,addby,addto,addon,isactive) values(@pulseid,@addby,@addto,@addon,@isactive)",con);
							SqlParameter _id=new SqlParameter("@pulseid",SqlDbType.VarChar);
							SqlParameter _addby=new SqlParameter("@addby",SqlDbType.Char);
							SqlParameter _addto=new SqlParameter("@addto",SqlDbType.VarChar);
							SqlParameter _addon=new SqlParameter("@addon",SqlDbType.SmallDateTime);
							SqlParameter _isact=new SqlParameter("@isactive",SqlDbType.Int);
							cmd.Transaction=trans;
							_id.Value=lblhPulseID.Text;
							_addby.Value=Session["user_id"].ToString().Trim();
							_addto.Value=itm.Value.Trim();
							_addon.Value=datetime;
							_isact.Value=1;
							cmd.Parameters.Add(_id);
							cmd.Parameters.Add(_addby);
							cmd.Parameters.Add(_addto);
							cmd.Parameters.Add(_addon);
							cmd.Parameters.Add(_isact);
							cmd.ExecuteNonQuery();
						}
						conn.Close();
					}
				}		
				cmd=new SqlCommand("insert into t_pulseprogressblog (pulseid,progressblog,psendby,pdate,actioncode,interimcloseddate,isactive) values(@pulseid,@progressblog,@psendby,@pdate,@actioncode,@interimcloseddate,@isactive)",con);
				SqlParameter _ppid=new SqlParameter("@pulseid",SqlDbType.VarChar);
				SqlParameter _blog=new SqlParameter("@progressblog",SqlDbType.Text);
				SqlParameter _psend=new SqlParameter("@psendby",SqlDbType.Char);
				SqlParameter _pdate=new SqlParameter("@pdate",SqlDbType.SmallDateTime);
				SqlParameter _acode=new SqlParameter("@actioncode",SqlDbType.Int);
				SqlParameter _iclose=new SqlParameter("@interimcloseddate",SqlDbType.SmallDateTime);
				SqlParameter _active=new SqlParameter("@isactive",SqlDbType.Int);
				cmd.Transaction=trans;
				name=name.Remove(name.Length-1,1);
				_ppid.Value=lblhPulseID.Text.Trim();
				_blog.Value=this.txtBlog.Text+"<Font color=orange><b><br><br>System Generated Message: Pulse Added to Issue & Task Sheet</b></Font><br><Font color=blue><b>"+name+"</b></Font>";
				_psend.Value=Session["user_id"].ToString();
				_pdate.Value=datetime;
				_acode.Value=this.ddActionCode.SelectedItem.Value;
				_iclose.Value=DBNull.Value;
				_active.Value=1;
				cmd.Parameters.Add(_ppid);
				cmd.Parameters.Add(_blog);
				cmd.Parameters.Add(_psend);
				cmd.Parameters.Add(_pdate);
				cmd.Parameters.Add(_acode);
				cmd.Parameters.Add(_iclose);
				cmd.Parameters.Add(_active);
				cmd.ExecuteNonQuery();

				cmd=new SqlCommand("update t_pulse set currentactioncode="+this.ddActionCode.SelectedItem.Value+",isaddtotasksheet=1 where pid='"+lblhPulseID.Text+"'",con);
				cmd.Transaction=trans;
				cmd.ExecuteNonQuery();

				
				trans.Commit();
				this.lblsendmsg.Visible=true;
				this.lblsendmsg.Text="New Blog has been sent successfully";
				GetPulseBlog(lblhPulseID.Text.Trim());
				GetMyPulse(Session["user_id"].ToString());
				this.ddActionCode.SelectedIndex=0;
				this.txtBlog.Text="";
				r1.Visible=false;
				r2.Visible=false;
			}
			else if(this.ddActionCode.SelectedItem.Value=="2")
			{
				SqlCommand cmd=new SqlCommand("insert into t_pulseprogressblog (pulseid,progressblog,psendby,pdate,actioncode,interimcloseddate,isactive) values(@pulseid,@progressblog,@psendby,@pdate,@actioncode,@interimcloseddate,@isactive)",con);
				SqlParameter _ppid=new SqlParameter("@pulseid",SqlDbType.VarChar);
				SqlParameter _blog=new SqlParameter("@progressblog",SqlDbType.Text);
				SqlParameter _psend=new SqlParameter("@psendby",SqlDbType.Char);
				SqlParameter _pdate=new SqlParameter("@pdate",SqlDbType.SmallDateTime);
				SqlParameter _acode=new SqlParameter("@actioncode",SqlDbType.Int);
				SqlParameter _iclose=new SqlParameter("@interimcloseddate",SqlDbType.SmallDateTime);
				SqlParameter _active=new SqlParameter("@isactive",SqlDbType.Int);
				cmd.Transaction=trans;
				_ppid.Value=lblhPulseID.Text.Trim();
				_blog.Value=this.txtBlog.Text+"<Font color=orange><b><br><br>System Generated Message: Future Date Committed: Date "+this.calInterimDate.SelectedDate.Date.ToString("d MMM,yyyy")+"</b></Font>";
				_psend.Value=Session["user_id"].ToString();
				_pdate.Value=datetime;
				_acode.Value=this.ddActionCode.SelectedItem.Value;
				_iclose.Value=DBNull.Value;
				_active.Value=1;
				cmd.Parameters.Add(_ppid);
				cmd.Parameters.Add(_blog);
				cmd.Parameters.Add(_psend);
				cmd.Parameters.Add(_pdate);
				cmd.Parameters.Add(_acode);
				cmd.Parameters.Add(_iclose);
				cmd.Parameters.Add(_active);
				cmd.ExecuteNonQuery();

				cmd=new SqlCommand("update t_pulse set currentactioncode="+this.ddActionCode.SelectedItem.Value+",currentstatus=4,interimcloseddate='"+this.calInterimDate.SelectedDate.Date.ToString("d MMM,yyyy")+"' where pid='"+lblhPulseID.Text+"'",con);
				cmd.Transaction=trans;
				cmd.ExecuteNonQuery();

				cmd=new SqlCommand("insert into t_pulsestatuslog(pulseid,statuslog,schangedby,schangeddate,isactive) values(@pulseid,@statuslog,@schangedby,@schangeddate,@isactive)",con);
				SqlParameter _ppidd=new SqlParameter("@pulseid",SqlDbType.VarChar);
				SqlParameter _slog=new SqlParameter("@statuslog",SqlDbType.Int);
				SqlParameter _sby=new SqlParameter("@schangedby",SqlDbType.Char);
				SqlParameter _sdate=new SqlParameter("@schangeddate",SqlDbType.SmallDateTime);
				SqlParameter _sact=new SqlParameter("@isactive",SqlDbType.Int);
				_ppidd.Value=lblhPulseID.Text.Trim();
				_slog.Value=4;
				_sby.Value=Session["user_id"].ToString();
				_sdate.Value=datetime;
				_sact.Value=1;
				cmd.Transaction=trans;
				cmd.Parameters.Add(_ppidd);
				cmd.Parameters.Add(_slog);
				cmd.Parameters.Add(_sby);
				cmd.Parameters.Add(_sdate);
				cmd.Parameters.Add(_sact);
				cmd.ExecuteNonQuery();

				trans.Commit();
				this.lblsendmsg.Visible=true;
				this.lblsendmsg.Text="New Blog has been sent successfully";
				GetPulseBlog(lblhPulseID.Text.Trim());
				GetMyPulse(Session["user_id"].ToString());
				this.ddActionCode.SelectedIndex=0;
				this.txtBlog.Text="";
				r1.Visible=false;
				r2.Visible=false;
			}
			else if(this.ddActionCode.SelectedItem.Value=="4" || this.ddActionCode.SelectedItem.Value=="5" || this.ddActionCode.SelectedItem.Value=="6" || this.ddActionCode.SelectedItem.Value=="8" || this.ddActionCode.SelectedItem.Value=="7")
			{
				string Msg="Pulse Routed To ";
				string name="";
				SqlCommand cmd=null;
				if(this.ddActionCode.SelectedItem.Value=="4")
				{
					Msg+="CEO";
				}
				if(this.ddActionCode.SelectedItem.Value=="5")
				{
					Msg+="COO";
				}
				if(this.ddActionCode.SelectedItem.Value=="6")
				{
					Msg+="SBU Head(s)";
				}
				if(this.ddActionCode.SelectedItem.Value=="8")
				{
					Msg+="Line Manager(s)";
				}
				if(this.ddActionCode.SelectedItem.Value=="7")
				{
					Msg+="Team";
				}
				SqlConnection conn=new SqlConnection(Connection.ConnectionString);
				conn.Open();
				for(int i=0;i<this.chkReportingList.Items.Count;i++)
				{
					if(this.chkReportingList.Items[i].Selected)
					{
						ListItem itm=chkReportingList.Items[i];
						cmd=new SqlCommand("update t_pulsehandler set isactive=0 where pulseid='"+lblhPulseID.Text.Trim()+"' and psendby='"+Session["user_id"].ToString()+"' and psendto='"+itm.Value+"'",conn);
						cmd.ExecuteNonQuery();
					}
				}
				conn.Close();
				for(int i=0;i<this.chkReportingList.Items.Count;i++)
				{
					if(this.chkReportingList.Items[i].Selected)
					{
						name+=this.chkReportingList.Items[i].Text+",";
						cmd=new SqlCommand("insert into t_pulsehandler (pulseid,psendby,psendto,psenddate,isactive) values(@pulseid,@psendby,@psendto,@psenddate,@isactive)",con);
						SqlParameter _pulseid=new SqlParameter("@pulseid",SqlDbType.VarChar);
						SqlParameter _psby=new SqlParameter("@psendby",SqlDbType.Char);
						SqlParameter _psto=new SqlParameter("@psendto",SqlDbType.Char);
						SqlParameter _psdate=new SqlParameter("@psenddate",SqlDbType.SmallDateTime);
						SqlParameter _isactive=new SqlParameter("@isactive",SqlDbType.Int);
						_pulseid.Value=lblhPulseID.Text.Trim();
						_psby.Value=Session["user_id"].ToString().Trim();
						_psto.Value=this.chkReportingList.Items[i].Value.Trim();
						_isactive.Value=1;
						_psdate.Value=datetime;
						cmd.Transaction=trans;
						cmd.Parameters.Add(_pulseid);
						cmd.Parameters.Add(_psby);
						cmd.Parameters.Add(_psto);
						cmd.Parameters.Add(_psdate);
						cmd.Parameters.Add(_isactive);
						cmd.ExecuteNonQuery();
					}
				}
				cmd=new SqlCommand("insert into t_pulseprogressblog (pulseid,progressblog,psendby,pdate,actioncode,interimcloseddate,isactive) values(@pulseid,@progressblog,@psendby,@pdate,@actioncode,@interimcloseddate,@isactive)",con);
				SqlParameter _ppid=new SqlParameter("@pulseid",SqlDbType.VarChar);
				SqlParameter _blog=new SqlParameter("@progressblog",SqlDbType.Text);
				SqlParameter _psend=new SqlParameter("@psendby",SqlDbType.Char);
				SqlParameter _pdate=new SqlParameter("@pdate",SqlDbType.SmallDateTime);
				SqlParameter _acode=new SqlParameter("@actioncode",SqlDbType.Int);
				SqlParameter _iclose=new SqlParameter("@interimcloseddate",SqlDbType.SmallDateTime);
				SqlParameter _active=new SqlParameter("@isactive",SqlDbType.Int);
				cmd.Transaction=trans;
				name=name.Remove(name.Length-1,1);
				_ppid.Value=lblhPulseID.Text.Trim();
				_blog.Value=this.txtBlog.Text+"<Font color=orange><b><br><br>System Generated Message: "+Msg+"</b></Font><br><Font color=blue><b>"+name+"</b></Font>";
				_psend.Value=Session["user_id"].ToString();
				_pdate.Value=datetime;
				_acode.Value=this.ddActionCode.SelectedItem.Value;
				_iclose.Value=DBNull.Value;
				_active.Value=1;
				cmd.Parameters.Add(_ppid);
				cmd.Parameters.Add(_blog);
				cmd.Parameters.Add(_psend);
				cmd.Parameters.Add(_pdate);
				cmd.Parameters.Add(_acode);
				cmd.Parameters.Add(_iclose);
				cmd.Parameters.Add(_active);
				cmd.ExecuteNonQuery();

				cmd=new SqlCommand("update t_pulse set currentactioncode="+this.ddActionCode.SelectedItem.Value+" where pid='"+lblhPulseID.Text+"'",con);
				cmd.Transaction=trans;
				cmd.ExecuteNonQuery();

				
				trans.Commit();
				this.lblsendmsg.Visible=true;
				this.lblsendmsg.Text="New Blog has been sent successfully";
				GetPulseBlog(lblhPulseID.Text.Trim());
				GetMyPulse(Session["user_id"].ToString());
				this.ddActionCode.SelectedIndex=0;
				this.txtBlog.Text="";
				r1.Visible=false;
				r2.Visible=false;
			}
			else if(this.ddActionCode.SelectedItem.Value=="1" || this.ddActionCode.SelectedItem.Value=="11")
			{
				SqlCommand cmd=new SqlCommand("insert into t_pulseprogressblog (pulseid,progressblog,psendby,pdate,actioncode,interimcloseddate,isactive) values(@pulseid,@progressblog,@psendby,@pdate,@actioncode,@interimcloseddate,@isactive)",con);
				SqlParameter _ppid=new SqlParameter("@pulseid",SqlDbType.VarChar);
				SqlParameter _blog=new SqlParameter("@progressblog",SqlDbType.Text);
				SqlParameter _psend=new SqlParameter("@psendby",SqlDbType.Char);
				SqlParameter _pdate=new SqlParameter("@pdate",SqlDbType.SmallDateTime);
				SqlParameter _acode=new SqlParameter("@actioncode",SqlDbType.Int);
				SqlParameter _iclose=new SqlParameter("@interimcloseddate",SqlDbType.SmallDateTime);
				SqlParameter _active=new SqlParameter("@isactive",SqlDbType.Int);
				cmd.Transaction=trans;
				_ppid.Value=lblhPulseID.Text.Trim();
				_blog.Value=this.txtBlog.Text+"<Font color=orange><b><br><br>System Generated Message: Pulse Closed</b></Font>";
				_psend.Value=Session["user_id"].ToString();
				_pdate.Value=datetime;
				_acode.Value=this.ddActionCode.SelectedItem.Value;
				_iclose.Value=DBNull.Value;
				_active.Value=1;
				cmd.Parameters.Add(_ppid);
				cmd.Parameters.Add(_blog);
				cmd.Parameters.Add(_psend);
				cmd.Parameters.Add(_pdate);
				cmd.Parameters.Add(_acode);
				cmd.Parameters.Add(_iclose);
				cmd.Parameters.Add(_active);
				cmd.ExecuteNonQuery();

				cmd=new SqlCommand("update t_pulse set currentactioncode="+this.ddActionCode.SelectedItem.Value+",currentstatus=2,closeddate='"+datetime+"',closedby='"+Session["user_id"].ToString()+"' where pid='"+lblhPulseID.Text+"'",con);
				cmd.Transaction=trans;
				cmd.ExecuteNonQuery();

				cmd=new SqlCommand("insert into t_pulsestatuslog(pulseid,statuslog,schangedby,schangeddate,isactive) values(@pulseid,@statuslog,@schangedby,@schangeddate,@isactive)",con);
				SqlParameter _ppidd=new SqlParameter("@pulseid",SqlDbType.VarChar);
				SqlParameter _slog=new SqlParameter("@statuslog",SqlDbType.Int);
				SqlParameter _sby=new SqlParameter("@schangedby",SqlDbType.Char);
				SqlParameter _sdate=new SqlParameter("@schangeddate",SqlDbType.SmallDateTime);
				SqlParameter _sact=new SqlParameter("@isactive",SqlDbType.Int);
				_ppidd.Value=lblhPulseID.Text.Trim();
				_slog.Value=2;
				_sby.Value=Session["user_id"].ToString();
				_sdate.Value=datetime;
				_sact.Value=1;
				cmd.Transaction=trans;
				cmd.Parameters.Add(_ppidd);
				cmd.Parameters.Add(_slog);
				cmd.Parameters.Add(_sby);
				cmd.Parameters.Add(_sdate);
				cmd.Parameters.Add(_sact);
				cmd.ExecuteNonQuery();

				trans.Commit();
				this.lblsendmsg.Visible=true;
				this.lblsendmsg.Text="New Blog has been sent successfully";
				GetPulseBlog(lblhPulseID.Text.Trim());
				GetMyPulse(Session["user_id"].ToString());
				GetMyPulse(Session["user_id"].ToString(),true);
				this.ddActionCode.SelectedIndex=0;
				this.txtBlog.Text="";
				r1.Visible=false;
				r2.Visible=false;
			}
			else if(this.ddActionCode.SelectedItem.Value=="9")
			{
				SqlCommand cmd=new SqlCommand("insert into t_pulseprogressblog (pulseid,progressblog,psendby,pdate,actioncode,interimcloseddate,isactive) values(@pulseid,@progressblog,@psendby,@pdate,@actioncode,@interimcloseddate,@isactive)",con);
				SqlParameter _ppid=new SqlParameter("@pulseid",SqlDbType.VarChar);
				SqlParameter _blog=new SqlParameter("@progressblog",SqlDbType.Text);
				SqlParameter _psend=new SqlParameter("@psendby",SqlDbType.Char);
				SqlParameter _pdate=new SqlParameter("@pdate",SqlDbType.SmallDateTime);
				SqlParameter _acode=new SqlParameter("@actioncode",SqlDbType.Int);
				SqlParameter _iclose=new SqlParameter("@interimcloseddate",SqlDbType.SmallDateTime);
				SqlParameter _active=new SqlParameter("@isactive",SqlDbType.Int);
				cmd.Transaction=trans;
				_ppid.Value=lblhPulseID.Text.Trim();
				_blog.Value=this.txtBlog.Text+"<Font color=orange><b><br><br>System Generated Message: Pulse Re-Opened</b></Font>";
				_psend.Value=Session["user_id"].ToString();
				_pdate.Value=datetime;
				_acode.Value=this.ddActionCode.SelectedItem.Value;
				_iclose.Value=DBNull.Value;
				_active.Value=1;
				cmd.Parameters.Add(_ppid);
				cmd.Parameters.Add(_blog);
				cmd.Parameters.Add(_psend);
				cmd.Parameters.Add(_pdate);
				cmd.Parameters.Add(_acode);
				cmd.Parameters.Add(_iclose);
				cmd.Parameters.Add(_active);
				cmd.ExecuteNonQuery();

				cmd=new SqlCommand("update t_pulse set currentactioncode="+this.ddActionCode.SelectedItem.Value+",currentstatus=3 where pid='"+lblhPulseID.Text+"'",con);
				cmd.Transaction=trans;
				cmd.ExecuteNonQuery();

				cmd=new SqlCommand("insert into t_pulsestatuslog(pulseid,statuslog,schangedby,schangeddate,isactive) values(@pulseid,@statuslog,@schangedby,@schangeddate,@isactive)",con);
				SqlParameter _ppidd=new SqlParameter("@pulseid",SqlDbType.VarChar);
				SqlParameter _slog=new SqlParameter("@statuslog",SqlDbType.Int);
				SqlParameter _sby=new SqlParameter("@schangedby",SqlDbType.Char);
				SqlParameter _sdate=new SqlParameter("@schangeddate",SqlDbType.SmallDateTime);
				SqlParameter _sact=new SqlParameter("@isactive",SqlDbType.Int);
				_ppidd.Value=lblhPulseID.Text.Trim();
				_slog.Value=3;
				_sby.Value=Session["user_id"].ToString();
				_sdate.Value=datetime;
				_sact.Value=1;
				cmd.Transaction=trans;
				cmd.Parameters.Add(_ppidd);
				cmd.Parameters.Add(_slog);
				cmd.Parameters.Add(_sby);
				cmd.Parameters.Add(_sdate);
				cmd.Parameters.Add(_sact);
				cmd.ExecuteNonQuery();

				trans.Commit();
				this.lblsendmsg.Visible=true;
				this.lblsendmsg.Text="New Blog has been sent successfully";
				GetPulseBlog(lblhPulseID.Text.Trim());
				GetMyPulse(Session["user_id"].ToString(),true);
				this.ddActionCode.SelectedIndex=0;
				this.txtBlog.Text="";
				r1.Visible=false;
				r2.Visible=false;
			}
			con.Close();
		}

		private void ImageButton1_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			GetMyPulse(Session["user_id"].ToString());
			this.pnlMain1.Visible=true;
			rmain.Visible=false;
			rmain1.Visible=true;
			this.pnlSubMain1.Visible=false;
			this.pnlSubMain2.Visible=false;
			this.pnlSubMain3.Visible=false;
			GetActionCode();
			ListItem itm=this.ddActionCode.Items.FindByText("Re-Open");
			this.ddActionCode.Items.Remove(itm);
			this.Label2.Text="Current";
		}

		private void ImageButton2_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			GetMyPulse(Session["user_id"].ToString(),true);
			this.pnlMain1.Visible=false;
			this.pnlMain2.Visible=true;
			this.pnlSubMain1.Visible=false;
			this.pnlSubMain2.Visible=false;
			this.pnlSubMain3.Visible=false;
			rmain.Visible=true;
			rmain1.Visible=false;
			GetActionCode(true);
			this.Label2.Text="Resolved / Future Committed";

		}

		private void dgDeferred_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			Reset();
			this.lblPulseID.Text=this.dgDeferred.Items[this.dgDeferred.SelectedIndex].Cells[1].Text;
			this.lblhPulseID.Text=lblPulseID.Text;
			this.lblName.Text=this.dgDeferred.Items[this.dgDeferred.SelectedIndex].Cells[3].Text;
			this.lblTitle.Text=this.dgDeferred.Items[this.dgDeferred.SelectedIndex].Cells[4].Text;
			this.lblType.Text=this.dgDeferred.Items[this.dgDeferred.SelectedIndex].Cells[5].Text;
			this.lblDescription.Text=this.dgDeferred.Items[this.dgDeferred.SelectedIndex].Cells[6].Text;
			this.lblCreateon.Text=this.dgDeferred.Items[this.dgDeferred.SelectedIndex].Cells[7].Text;
			this.lblPcode.Text=this.dgDeferred.Items[this.dgDeferred.SelectedIndex].Cells[11].Text;
			this.lblDesignation.Text=this.dgDeferred.Items[this.dgDeferred.SelectedIndex].Cells[12].Text;
			this.lblDepartment.Text=this.dgDeferred.Items[this.dgDeferred.SelectedIndex].Cells[13].Text;
			this.lblpDepartment.Text=this.dgDeferred.Items[this.dgDeferred.SelectedIndex].Cells[14].Text;
			this.lblpStation.Text=this.dgDeferred.Items[this.dgDeferred.SelectedIndex].Cells[15].Text;
			this.pnlSubMain1.Visible=true;
			GetPulseBlog(this.lblPulseID.Text);
			this.pnlSubMain2.Visible=true;
			this.pnlSubMain3.Visible=true;
			if(this.dgDeferred.Items[this.dgDeferred.SelectedIndex].Cells[8].Text=="Closed")
			{
				ListItem itm=this.ddActionCode.Items.FindByText("Close Pulse with Solution");
				this.ddActionCode.Items.Remove(itm);
				itm=this.ddActionCode.Items.FindByText("Close Pulse without Solution");
				this.ddActionCode.Items.Remove(itm);
			}
			else
			{
			 GetActionCode(true);
			}
		}

		private void imgBack_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("ManageRequest.aspx");
		}

		private void imgNew_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("Pulse.aspx");
		}
	}
}
