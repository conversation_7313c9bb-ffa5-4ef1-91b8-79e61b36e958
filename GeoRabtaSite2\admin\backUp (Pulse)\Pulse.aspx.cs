using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;
namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for GeoIdeas.
	/// </summary>
	public class Pulse: System.Web.UI.Page
	{
		protected System.Web.UI.HtmlControls.HtmlTable tabForm;
		protected System.Web.UI.WebControls.TextBox txtTitle;
		protected System.Web.UI.WebControls.TextBox txtDescription;
		protected System.Web.UI.WebControls.DropDownList ddType;
		protected System.Web.UI.WebControls.DropDownList ddDept;
		protected System.Web.UI.WebControls.DropDownList ddStation;
		protected System.Web.UI.WebControls.DropDownList ddSendTo;
		protected System.Web.UI.HtmlControls.HtmlTableCell r3;
		protected System.Web.UI.WebControls.Label Label1;
		protected System.Web.UI.WebControls.CheckBoxList chkReportingList;
		protected System.Web.UI.HtmlControls.HtmlTableCell r2;
		protected System.Web.UI.HtmlControls.HtmlTableCell r1;
		protected System.Web.UI.WebControls.Button btnSend;
		protected System.Web.UI.WebControls.Button btnNew;
		protected System.Web.UI.WebControls.DropDownList ddAddToTS;
		protected System.Web.UI.WebControls.Label Label2;
		protected System.Web.UI.WebControls.ImageButton imgBack;
	    SqlConnection con; 
		string userId="";
		private bool IsPageAccessAllowed()
		{

			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception ex)
			{
				string s=ex.Message;
				Response.Redirect("../Login.aspx");
			}
 
			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(2,59,userId,"View")==true)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("../Login.aspx");
				return false;
			}
		}
		private void Page_Load(object sender, System.EventArgs e)
		{
			if(IsPageAccessAllowed())
			{
				if(!IsPostBack)
				{
					r1.Visible=false;
					r2.Visible=false;
					r3.Visible=false;
					GetStation();
					GetDepartment();
					GetPulse();
					btnSend.Attributes.Add("onclick","return ValidatePage();");
				}
			}
			else
			{
			 Response.Redirect("ErrorPage.aspx");
			}
		}

		public void GetStation()
		{
		 con=new SqlConnection(Connection.ConnectionString);
		 con.Open();
		 SqlCommand cmd=new SqlCommand("select cityid,cityname from t_city where status=1 order by cityname",con);
		 SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
			 ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
			 this.ddStation.Items.Add(itm);
			}
		 rd.Close();
		 con.Close();
		}
		public void GetDepartment()
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select deptid,deptname from t_department where status=1 order by deptname",con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
				this.ddDept.Items.Add(itm);
			}
			rd.Close();
			con.Close();
		}
		public void GetPulse()
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select pulseid,pulsecategory from t_pulsecategory where isactive=1 order by pulsecategory",con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
				this.ddType.Items.Add(itm);
			}
			rd.Close();
			con.Close();
		}
		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.ddSendTo.SelectedIndexChanged += new System.EventHandler(this.ddSendTo_SelectedIndexChanged);
			this.btnSend.Click += new System.EventHandler(this.btnSend_Click);
			this.btnNew.Click += new System.EventHandler(this.btnNew_Click);
			this.imgBack.Click += new System.Web.UI.ImageClickEventHandler(this.imgBack_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		private void ddSendTo_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			if(this.ddSendTo.SelectedItem.Value=="1")
			{
				r2.Visible=true;
				r3.Visible=true;
				this.chkReportingList.Items.Clear();
				MyTeam team=new MyTeam();
				ArrayList _list=team.getDirectTeam(Session["user_id"].ToString());
				ArrayList tempList=new ArrayList();
				for(int j=0;j<_list.Count;j++)
				{
					string member=(string)_list[j];
					string []memberStr=member.Split('^');
					tempList.Add(memberStr[2]);
				}
				tempList.Sort();
				for(int k=0;k<tempList.Count;k++)
				{
					string members=(string)tempList[k];
					for(int z=0;z<_list.Count;z++)
					{
						string member=(string)_list[z];
						string []memberStr=member.Split('^');
						if(memberStr[2].Equals(members))
						{
							ListItem itm=new ListItem(members,memberStr[0]);
							this.chkReportingList.Items.Add(itm);
						}
					}			  
				}
				this.Label1.Text="My Team";
				r1.Visible=true;
			}
			else if(this.ddSendTo.SelectedItem.Value=="2")
			{
				r2.Visible=false;
				r3.Visible=false;
				this.chkReportingList.Items.Clear();
				MyTeam team=new MyTeam();
				ArrayList _list=team.getBoss(Session["user_id"].ToString(),1);
				ArrayList tempList=new ArrayList();
				for(int j=0;j<_list.Count;j++)
				{
					string member=(string)_list[j];
					string []memberStr=member.Split('^');
					tempList.Add(memberStr[2]);
				}
				tempList.Sort();
				for(int k=0;k<tempList.Count;k++)
				{
					string members=(string)tempList[k];
					for(int z=0;z<_list.Count;z++)
					{
						string member=(string)_list[z];
						string []memberStr=member.Split('^');
						if(memberStr[2].Equals(members))
						{
							ListItem itm=new ListItem(members,memberStr[0]);
							this.chkReportingList.Items.Add(itm);
						}
					}			  
				}
				r1.Visible=true;
				this.Label1.Text="My Line Manager(s)";
			}
			else if(this.ddSendTo.SelectedItem.Value=="3")
			{
				r2.Visible=true;
				r3.Visible=true;
				this.chkReportingList.Items.Clear();
				MyTeam team=new MyTeam();
				ArrayList _list=team.getDirectTeam(Session["user_id"].ToString());
				ArrayList _list2=team.getBoss(Session["user_id"].ToString(),1);
//				for(int i=0;i<_list2.Count;i++)
//				{
//				 _list.Add(_list2[i]);
//				}
				ArrayList tempList=new ArrayList();
				for(int j=0;j<_list2.Count;j++)
				{
					string member=(string)_list2[j];
					string []memberStr=member.Split('^');
					tempList.Add(memberStr[2]);
				}
				tempList.Sort();
				for(int k=0;k<tempList.Count;k++)
				{
					string members=(string)tempList[k];
					for(int z=0;z<_list2.Count;z++)
					{
						string member=(string)_list2[z];
						string []memberStr=member.Split('^');
						if(memberStr[2].Equals(members))
						{
							ListItem itm=new ListItem(members+"(Line Manager)",memberStr[0]);
							this.chkReportingList.Items.Add(itm);
						}
					}			  
				}
				tempList.Clear();
				for(int j=0;j<_list.Count;j++)
				{
					string member=(string)_list[j];
					string []memberStr=member.Split('^');
					tempList.Add(memberStr[2]);
				}
				tempList.Sort();
				for(int k=0;k<tempList.Count;k++)
				{
					string members=(string)tempList[k];
					for(int z=0;z<_list.Count;z++)
					{
						string member=(string)_list[z];
						string []memberStr=member.Split('^');
						if(memberStr[2].Equals(members))
						{
							ListItem itm=new ListItem(members+"(Team)",memberStr[0]);
							this.chkReportingList.Items.Add(itm);
						}
					}			  
				}
				r1.Visible=true;
				this.Label1.Text="My Line Manager(s) & My Team";
     		}
			else
			{
				r2.Visible=false;
				r3.Visible=false;
				r1.Visible=false;
				this.Label1.Text="";
			}

		}

		private void btnNew_Click(object sender, System.EventArgs e)
		{
			this.txtTitle.Text="";
			this.ddType.SelectedIndex=0;
			this.txtDescription.Text="";
			this.ddDept.SelectedIndex=0;
			this.ddStation.SelectedIndex=0;
			this.ddSendTo.SelectedIndex=0;
			this.chkReportingList.Items.Clear();
			this.ddAddToTS.SelectedIndex=0;
			this.btnSend.Enabled=true;
			r2.Visible=false;
			r3.Visible=false;
			r1.Visible=false;
			this.Label1.Text="";
			this.Label2.Visible=false;
			this.Label2.Text="";
		}

		private void btnSend_Click(object sender, System.EventArgs e)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlTransaction trans=con.BeginTransaction();
			try
			{
			 string pid=GenerateID();
			 SqlCommand cmd=new SqlCommand("insert into t_pulse(pid,pcode,title,description,pulsecategory,sendto,deptid,station,pulsecreateon,currentstatus,currentactioncode,closedby,isaddtotasksheet,closeddate,interimcloseddate) values(@pid,@pcode,@title,@description,@pulsecategory,@sendto,@deptid,@station,@pulsecreateon,@currentstatus,@currentactioncode,@closedby,@isaddtotasksheet,@closeddate,@interimcloseddate)",con);
			 cmd.Transaction=trans;
			 SqlParameter _pid=new SqlParameter("@pid",SqlDbType.VarChar);
			 SqlParameter _pcode=new SqlParameter("@pcode",SqlDbType.Char);
			 SqlParameter _title=new SqlParameter("@title",SqlDbType.VarChar);
			 SqlParameter _des=new SqlParameter("@description",SqlDbType.Text);
			 SqlParameter _pc=new SqlParameter("@pulsecategory",SqlDbType.Int);
			 SqlParameter _sto=new SqlParameter("@sendto",SqlDbType.Int);
			 SqlParameter _deptid=new SqlParameter("@deptid",SqlDbType.Int);
			 SqlParameter _station=new SqlParameter("@station",SqlDbType.Int);
			 SqlParameter _create=new SqlParameter("@pulsecreateon",SqlDbType.SmallDateTime);
			 SqlParameter _cstatus=new SqlParameter("@currentstatus",SqlDbType.Int);
			 SqlParameter _caction=new SqlParameter("@currentactioncode",SqlDbType.Int);
			 SqlParameter _closeby=new SqlParameter("@closedby",SqlDbType.Char);
			 SqlParameter _its=new SqlParameter("@isaddtotasksheet",SqlDbType.Int);
			 SqlParameter _closedate=new SqlParameter("@closeddate",SqlDbType.SmallDateTime);
			 SqlParameter _interim=new SqlParameter("@interimcloseddate",SqlDbType.SmallDateTime);
			 _pid.Value=pid;
			 _pcode.Value=Session["user_id"].ToString().Trim();
			 _title.Value=this.txtTitle.Text;
			 _des.Value=this.txtDescription.Text;
			 _pc.Value=this.ddType.SelectedItem.Value;
			 _sto.Value=this.ddSendTo.SelectedItem.Value;
			 _deptid.Value=this.ddDept.SelectedItem.Value;
			 _station.Value=this.ddStation.SelectedItem.Value;
			 string datetime=DateTime.Now.ToString("dd MMM,yyyy h:mm tt");
			 _create.Value=datetime;
			 _cstatus.Value=1;
			 _caction.Value=DBNull.Value;
			 _closeby.Value=DBNull.Value;
			 _its.Value=this.ddAddToTS.SelectedItem.Value;
			 _closedate.Value=DBNull.Value;
			 _interim.Value=DBNull.Value;
			 cmd.Parameters.Add(_pid);
			 cmd.Parameters.Add(_pcode);
			 cmd.Parameters.Add(_title);
			 cmd.Parameters.Add(_des);
			 cmd.Parameters.Add(_pc);
			 cmd.Parameters.Add(_sto);
			 cmd.Parameters.Add(_deptid);
             cmd.Parameters.Add(_station);
			 cmd.Parameters.Add(_create);
			 cmd.Parameters.Add(_cstatus);
			 cmd.Parameters.Add(_caction);
			 cmd.Parameters.Add(_closeby);
			 cmd.Parameters.Add(_its);
			 cmd.Parameters.Add(_closedate);
			 cmd.Parameters.Add(_interim);
			 cmd.ExecuteNonQuery();
				for(int i=0;i<this.chkReportingList.Items.Count;i++)
				{
					if(this.chkReportingList.Items[i].Selected)
					{
					 cmd=new SqlCommand("insert into t_pulsehandler (pulseid,psendby,psendto,psenddate,isactive) values(@pulseid,@psendby,@psendto,@psenddate,@isactive)",con);
					 SqlParameter _pulseid=new SqlParameter("@pulseid",SqlDbType.VarChar);
					 SqlParameter _psby=new SqlParameter("@psendby",SqlDbType.Char);
					 SqlParameter _psto=new SqlParameter("@psendto",SqlDbType.Char);
					 SqlParameter _psdate=new SqlParameter("@psenddate",SqlDbType.SmallDateTime);
					 SqlParameter _isactive=new SqlParameter("@isactive",SqlDbType.Int);
					 _pulseid.Value=pid;
					 _psby.Value=Session["user_id"].ToString().Trim();
					 _psto.Value=this.chkReportingList.Items[i].Value.Trim();
					 _isactive.Value=1;
					 _psdate.Value=datetime;
					  cmd.Transaction=trans;
					 cmd.Parameters.Add(_pulseid);
					 cmd.Parameters.Add(_psby);
					 cmd.Parameters.Add(_psto);
					 cmd.Parameters.Add(_psdate);
					 cmd.Parameters.Add(_isactive);
					 cmd.ExecuteNonQuery();
					}
				}
				//============================Send Pulse To Initiator Itself====================//
				cmd=new SqlCommand("insert into t_pulsehandler (pulseid,psendby,psendto,psenddate,isactive) values(@pulseid,@psendby,@psendto,@psenddate,@isactive)",con);
				SqlParameter _pulseidd=new SqlParameter("@pulseid",SqlDbType.VarChar);
				SqlParameter _psbyd=new SqlParameter("@psendby",SqlDbType.Char);
				SqlParameter _pstod=new SqlParameter("@psendto",SqlDbType.Char);
				SqlParameter _psdated=new SqlParameter("@psenddate",SqlDbType.SmallDateTime);
				SqlParameter _isactived=new SqlParameter("@isactive",SqlDbType.Int);
				_pulseidd.Value=pid;
				_psbyd.Value=Session["user_id"].ToString().Trim();
				_pstod.Value=Session["user_id"].ToString().Trim();
				_isactived.Value=1;
				_psdated.Value=datetime;
				cmd.Transaction=trans;
				cmd.Parameters.Add(_pulseidd);
				cmd.Parameters.Add(_psbyd);
				cmd.Parameters.Add(_pstod);
				cmd.Parameters.Add(_psdated);
				cmd.Parameters.Add(_isactived);
				cmd.ExecuteNonQuery();
				//============================================================================//
				if(ddAddToTS.SelectedValue=="1")
				{
					for(int i=0;i<this.chkReportingList.Items.Count;i++)
					{
						if(this.chkReportingList.Items[i].Selected)
						{
							if(this.ddSendTo.SelectedItem.Value=="3")
							{
								ListItem itm=chkReportingList.Items[i];
								int indx=itm.Text.IndexOf("(");
								int indxe=itm.Text.Length-indx;
								string txt=itm.Text.Substring(indx,indxe);
								if(txt.Equals("(Team)"))
								{
									cmd=new SqlCommand("insert into t_pulsetasksheet(pulseid,addby,addto,addon,isactive) values(@pulseid,@addby,@addto,@addon,@isactive)",con);
									SqlParameter _id=new SqlParameter("@pulseid",SqlDbType.VarChar);
									SqlParameter _addby=new SqlParameter("@addby",SqlDbType.Char);
									SqlParameter _addto=new SqlParameter("@addto",SqlDbType.VarChar);
									SqlParameter _addon=new SqlParameter("@addon",SqlDbType.SmallDateTime);
									SqlParameter _isact=new SqlParameter("@isactive",SqlDbType.Int);
									cmd.Transaction=trans;
									_id.Value=pid;
									_addby.Value=Session["user_id"].ToString().Trim();
									_addto.Value=itm.Value.Trim();
									_addon.Value=datetime;
									_isact.Value=1;
									cmd.Parameters.Add(_id);
									cmd.Parameters.Add(_addby);
									cmd.Parameters.Add(_addto);
									cmd.Parameters.Add(_addon);
									cmd.Parameters.Add(_isact);
									cmd.ExecuteNonQuery();
								}
							}
							else
							{
								ListItem itm=chkReportingList.Items[i];
								cmd=new SqlCommand("insert into t_pulsetasksheet(pulseid,addby,addto,addon,isactive) values(@pulseid,@addby,@addto,@addon,@isactive)",con);
								SqlParameter _id=new SqlParameter("@pulseid",SqlDbType.VarChar);
								SqlParameter _addby=new SqlParameter("@addby",SqlDbType.Char);
								SqlParameter _addto=new SqlParameter("@addto",SqlDbType.VarChar);
								SqlParameter _addon=new SqlParameter("@addon",SqlDbType.SmallDateTime);
								SqlParameter _isact=new SqlParameter("@isactive",SqlDbType.Int);
								cmd.Transaction=trans;
								_id.Value=pid;
								_addby.Value=Session["user_id"].ToString().Trim();
								_addto.Value=itm.Value.Trim();
								_addon.Value=datetime;
								_isact.Value=1;
								cmd.Parameters.Add(_id);
								cmd.Parameters.Add(_addby);
								cmd.Parameters.Add(_addto);
								cmd.Parameters.Add(_addon);
								cmd.Parameters.Add(_isact);
								cmd.ExecuteNonQuery();
							}
						}
					}
				}
				cmd=new SqlCommand("insert into t_pulseprogressblog (pulseid,progressblog,psendby,pdate,actioncode,interimcloseddate,isactive) values(@pulseid,@progressblog,@psendby,@pdate,@actioncode,@interimcloseddate,@isactive)",con);
				SqlParameter _ppid=new SqlParameter("@pulseid",SqlDbType.VarChar);
				SqlParameter _blog=new SqlParameter("@progressblog",SqlDbType.Text);
				SqlParameter _psend=new SqlParameter("@psendby",SqlDbType.Char);
				SqlParameter _pdate=new SqlParameter("@pdate",SqlDbType.SmallDateTime);
				SqlParameter _acode=new SqlParameter("@actioncode",SqlDbType.Int);
				SqlParameter _iclose=new SqlParameter("@interimcloseddate",SqlDbType.SmallDateTime);
				SqlParameter _active=new SqlParameter("@isactive",SqlDbType.Int);
                cmd.Transaction=trans;
                _ppid.Value=pid;
				_blog.Value="<b>Pulse : </b>"+this.txtDescription.Text+"<br>"+"<Font color=orange><b><br>System Generated Message: New Pulse Has Been Generated for your Persual</b></Font>";
				_psend.Value="Raabta";
				_pdate.Value=datetime;
				_acode.Value=DBNull.Value;
				_iclose.Value=DBNull.Value;
				_active.Value=1;
				cmd.Parameters.Add(_ppid);
				cmd.Parameters.Add(_blog);
				cmd.Parameters.Add(_psend);
				cmd.Parameters.Add(_pdate);
				cmd.Parameters.Add(_acode);
				cmd.Parameters.Add(_iclose);
				cmd.Parameters.Add(_active);
				cmd.ExecuteNonQuery();

				trans.Commit();
				this.Label2.Visible=true;
				this.Label2.Text="Pulse has been generated successfully";
				this.btnSend.Enabled=false;

			}
			catch(Exception ex)
			{
			    trans.Rollback();
				Response.Write("Exception Occured: "+ex.Message);
			}
			con.Close();
			
		}
		public string GenerateID()
		{
		 SqlConnection conn=new SqlConnection(Connection.ConnectionString);
		 conn.Open();
		 SqlCommand cmd=new SqlCommand("SELECT MAX(CAST(SUBSTRING(pid, 4, LEN(pid)) AS int) + 1) AS PulseID FROM dbo.t_Pulse",conn);
		 SqlDataReader rd=cmd.ExecuteReader();
		 rd.Read();
			if(rd.HasRows && rd[0].ToString()!="")
			{
				string ID=rd[0].ToString();
				rd.Close();
				conn.Close();
				return "PL-"+ID;
			}
			else
			{
			 rd.Close();
			 conn.Close();
			 return "PL-1";
			}
		}

		private void imgBack_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("PulseQueuelist.aspx");
		}
	}
}
