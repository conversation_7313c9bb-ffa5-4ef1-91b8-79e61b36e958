<%@ Page language="c#" Codebehind="EmpFamily.aspx.cs" AutoEventWireup="false" Inherits="GeoRabtaSite.EmpFamily" %>
<%@ Register TagPrefix="ew" Namespace="eWorld.UI" Assembly="eWorld.UI" %>
<%@ Register TagPrefix="uc1" TagName="Organimeter" Src="Organimeter.ascx" %>
<%@ Register TagPrefix="uc1" TagName="MenuControl" Src="MenuControl.ascx" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Rabta :: User Profile</title>
		<meta content="text/html; charset=utf-8" http-equiv="Content-Type">
		<meta name="GENERATOR" content="Microsoft Visual Studio .NET 7.1">
		<meta name="vs_targetSchema" content="http://schemas.microsoft.com/intellisense/ie5">
		<LINK rel="stylesheet" type="text/css" href="StyleSheet1.css">
		<style type="text/css">.style2 { FONT-WEIGHT: bold; FONT-SIZE: 16pt; COLOR: #ffffff }
	A:link { COLOR: white }
		</style>
		<script type="text/javascript" src="jquery-1.2.6.js"></script>
		<script language="javascript" type="text/javascript" src="jquery.MetaData.js"></script>
		<script type="text/javascript" src="documentation.js"></script>
		<script language="javascript" type="text/javascript" src="jquery.MultiFile.js"></script>
		<script language="javascript" type="text/javascript" src="jquery.blockUI.js"></script>
		<script language="javascript">
String.prototype.trim = function() {
	return this.replace(/^\s+|\s+$/g,"");
}
function OpenURL(file,docw,doch)
{
var w=screen.width;
var h=screen.height;
var l=(w-docw)/2;
var t=(h-doch)/2;
var viewimageWin=window.open(file,"codewindow","toolbar=0,scrollbars=1,location=0,statusbar=0,menubar=0,resizable=0,width="+docw+",height="+doch+",left=" + l + ",top="+t);
return false;
}

function validateFamily()
{
	var txtFamilyName=document.getElementById("txtFamilyName");
	var rdRelation=document.getElementById("rdRelation");
	var rdStatus=document.getElementById("rdStatus");
	var ddDependent=document.getElementById("ddDependent");
	var fileFamily=document.getElementById('fileFamily');
	
	if(txtFamilyName.value.trim()=="")
	{
		alert("Family member name is required");
		txtFamilyName.focus();
		return false;
	}
	
	if(rdRelation.selectedIndex==0)
	{
		alert("Please select relationship");
		rdRelation.focus();
		return false;
	}
	
	if(rdStatus.selectedIndex==0)
	{
		alert("Please select Marital status");
		rdStatus.focus();
		return false;
	}
	if(ddDependent.selectedIndex==0)
	{
		alert("Please selecte dependancy");
		ddDependent.focus();
		return false;
	}
	if(fileFamily.value=="")
	{
		alert("Please attache document with your required");
		fileFamily.focus();
		return false;
	}
	return true;

}
		
	
function PopUpSlip()
{
	var w = 600, h = 350;
	if (document.all) 
	{
		w = document.body.clientWidth;
		h = document.body.clientHeight;
	}
	else if (document.layers) 
	{
		w = window.innerWidth;
		h = window.innerHeight;
    }
	var popW = 800, popH = 375;
	var leftPos = (w-popW)/2, topPos = (h-popH)/2;
	window.open('Report.aspx','MyPaySlip','width=' + popW + ',height='+popH+',top='+topPos+ ',left='+leftPos)
	//window.open('Report.aspx','MyPaySlip','status:no,toolbar:no,scrollbars:no,width=800');
	return false; 
}
	
function OpenURL(file,docw,doch)
{
	var w=screen.width;
	var h=screen.height;
	var l=(w-docw)/2;
	var t=(h-doch)/2;
	var viewimageWin=window.open(file,"codewindow","toolbar=0,scrollbars=1,location=0,statusbar=0,menubar=0,resizable=0,width="+docw+",height="+doch+",left=" + l + ",top="+t);
	return false;
}
		</script>
		<script id="clientEventHandlersJS" language="javascript" type="text/javascript">
<!--

function window_onload() 
{
	document.body.scrollTop =document.getElementById("txtX").value;
	document.body.scrollLeft=document.getElementById("txtY").value;
}

function window_onscroll() 
{
	document.getElementById("txtX").value=document.body.scrollTop;
	document.getElementById("txtY").value=document.body.scrollLeft;
}

//-->
		</script>
	</HEAD>
	<body onscroll="return window_onscroll()" oncontextmenu="return false" language="javascript"
		onselectstart="return false" ondrag="return false" onload="return window_onload()"
		bottomMargin="0" background="images\bg.jpg" leftMargin="0" rightMargin="0" bgProperties="fixed"
		topMargin="0">
		<script type="text/javascript" src="wz_tooltip.js"></script>
		<form id="myForm" runat="server">
			<table id="Table1" border="0" cellSpacing="0" cellPadding="0" width="1004" height="100%">
				<tr>
					<td style="HEIGHT: 200px">
						<OBJECT id="Shockwaveflash1" codeBase="http://fpdownload.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,0,0"
							height="200" width="1004" align="middle" classid="clsid:d27cdb6e-ae6d-11cf-96b8-444553540000"
							VIEWASTEXT>
							<PARAM NAME="_cx" VALUE="26564">
							<PARAM NAME="_cy" VALUE="5292">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="top.swf">
							<PARAM NAME="Src" VALUE="top.swf">
							<PARAM NAME="WMode" VALUE="Transparent">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="sameDomain">
							<PARAM NAME="Scale" VALUE="ExactFit">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="0066FF">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="top.swf" width="1004" height="200" align="middle" quality="high" wmode="transparent"
								bgcolor="#0066ff" scale="exactfit" allowscriptaccess="sameDomain" type="application/x-shockwave-flash"
								pluginspage="http://www.macromedia.com/go/getflashplayer" />
						</OBJECT>
					</td>
				</tr>
				<tr>
					<td vAlign="top" align="center">
						<table id="Table2" border="0" cellSpacing="0" cellPadding="0" width="100%">
							<tr>
								<td style="WIDTH: 175px" vAlign="top" align="center"><uc1:menucontrol id="MenuControl1" runat="server"></uc1:menucontrol></td>
								<td style="WIDTH: 650px; BACKGROUND-COLOR: #ffffff" vAlign="top" align="left">
									<table style="BACKGROUND-COLOR: white" border="0" cellSpacing="0" cellPadding="2" width="100%"
										align="center">
										<tr>
											<td bgColor="#004477" height="65" background="images/PanelTop.jpg">
												<table border="0" cellSpacing="0" cellPadding="4" width="650">
													<tr>
														<td style="WIDTH: 40px"></td>
														<td><span class="style2">User Profile</span>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td>
												<asp:imagebutton id="ImageButton1" runat="server" ImageUrl="images\employee.gif" CausesValidation="False"></asp:imagebutton>
												<asp:imagebutton id="ImageButton2" runat="server" ImageUrl="images\personal.gif" CausesValidation="False"></asp:imagebutton>
												<asp:imagebutton id="ImageButton3" runat="server" ImageUrl="images\family.gif" CausesValidation="False"></asp:imagebutton>
												<asp:imagebutton id="ImageButton4" runat="server" ImageUrl="images\education.gif" CausesValidation="False"></asp:imagebutton>
												<asp:imagebutton id="imgTraining" runat="server" ImageUrl="images\training.gif"></asp:imagebutton>
												<asp:HyperLink id="hlMyExp" runat="server" BorderWidth="0px" NavigateUrl="EmpExperience.aspx" BorderStyle="None"
													ImageUrl="images/MyExperience.gif">HyperLink</asp:HyperLink>
												<asp:imagebutton id="ImageButton5" runat="server" ImageUrl="buttons/myorganogram.gif" CausesValidation="False"></asp:imagebutton>
												<asp:imagebutton id="ibSalary" runat="server" ImageUrl="images/payslip.gif" CausesValidation="False"></asp:imagebutton>
												<asp:imagebutton id="ibMySelf" runat="server" ImageUrl="Images\MeMyself.gif" CausesValidation="False"
													Visible="False"></asp:imagebutton>
												<asp:imagebutton id="imgMyLeave" runat="server" ImageUrl="images\MyLeaveBalance.gif" CausesValidation="False"></asp:imagebutton>
												<asp:imagebutton id="ImgMyAttendance" runat="server" ImageUrl="images\MyAttendance.gif" CausesValidation="False"></asp:imagebutton>
												<asp:imagebutton id="imgMyGrievance" runat="server" ImageUrl="images\mygrievance.gif"></asp:imagebutton>
												<asp:HyperLink id="hlMyRequests" runat="server" BorderWidth="0px" NavigateUrl="MyRequest.aspx"
													BorderStyle="None" ImageUrl="images/myrequests.gif">My Requests</asp:HyperLink>
												<asp:panel id="Panel1" runat="server" BackImageUrl="images\tabstrip.jpg" Height="10px" Width="100%"></asp:panel><uc1:organimeter id="Organimeter1" runat="server"></uc1:organimeter><asp:label id="Label2" runat="server" Visible="False"></asp:label><asp:label id="lblPCode" runat="server" Visible="False" Font-Size="Medium" Font-Bold="True"></asp:label><asp:label id="lblEmpInfo" runat="server" Visible="False" Font-Size="Medium" Font-Bold="True"></asp:label><asp:label id="Label1" runat="server" Visible="False"></asp:label><asp:label id="lblFDID" runat="server" Visible="False"></asp:label><br>
												<asp:panel id="pnlFamilyInfo" runat="server">
													<TABLE id="Table14" cellSpacing="0" cellPadding="3" width="100%" border="0">
														<TR>
															<TD class="PanelTitle">Family Information
															</TD>
														</TR>
														<TR>
															<TD class="FormColor1">
																<asp:DataGrid id="dgFamily" runat="server" BorderWidth="1px" Width="100%" ForeColor="Black" BackColor="LightGoldenrodYellow"
																	CellPadding="2" BorderColor="Tan" AutoGenerateColumns="False">
																	<FooterStyle BackColor="Tan"></FooterStyle>
																	<SelectedItemStyle ForeColor="GhostWhite" BackColor="DarkSlateBlue"></SelectedItemStyle>
																	<AlternatingItemStyle BackColor="PaleGoldenrod"></AlternatingItemStyle>
																	<HeaderStyle Font-Bold="True" BackColor="Tan"></HeaderStyle>
																	<Columns>
																		<asp:BoundColumn Visible="False" DataField="fdid"></asp:BoundColumn>
																		<asp:BoundColumn DataField="name" HeaderText="Name"></asp:BoundColumn>
																		<asp:BoundColumn DataField="relationship" HeaderText="RelationShip"></asp:BoundColumn>
																		<asp:BoundColumn DataField="MaritalStatus" HeaderText="MaritialStatus"></asp:BoundColumn>
																		<asp:BoundColumn Visible="False" DataField="occupation" HeaderText="Occupation"></asp:BoundColumn>
																		<asp:BoundColumn Visible="False" DataField="DOB" SortExpression="DOB" HeaderText="Date Of Birth"
																			DataFormatString="{0:d}"></asp:BoundColumn>
																		<asp:BoundColumn DataField="dependent" SortExpression="dependent" HeaderText="Dependent"></asp:BoundColumn>
																		<asp:ButtonColumn Text="Select" CommandName="Select"></asp:ButtonColumn>
																		<asp:BoundColumn Visible="False" DataField="pcode"></asp:BoundColumn>
																		<asp:TemplateColumn Visible="False">
																			<ItemTemplate>
																				<asp:Label ID="lblId" runat="server"></asp:Label>
																			</ItemTemplate>
																		</asp:TemplateColumn>
																	</Columns>
																	<PagerStyle HorizontalAlign="Center" ForeColor="DarkSlateBlue" BackColor="PaleGoldenrod"></PagerStyle>
																</asp:DataGrid>
																<asp:Label id="lblMsg" runat="server" Visible="False" Font-Bold="True" Font-Size="Small" ForeColor="White">Your Request has been successfully posted...</asp:Label></TD>
														</TR>
														<TR>
															<TD class="FormColor1">
																<asp:LinkButton id="LinkButton2" runat="server">Add New Family Member</asp:LinkButton></TD>
														</TR>
													</TABLE>
													<TABLE id="Table15" cellSpacing="0" cellPadding="3" width="100%" border="0">
													</TABLE>
													<TABLE id="Table6" cellSpacing="0" cellPadding="3" width="100%" border="0">
														<TR>
															<TD class="FormColor1">
																<asp:DataGrid id="dgFamilyReq" runat="server" BorderWidth="1px" Visible="False" Width="100%" ForeColor="Black"
																	BackColor="LightGoldenrodYellow" CellPadding="2" BorderColor="Tan" AutoGenerateColumns="False">
																	<FooterStyle BackColor="Tan"></FooterStyle>
																	<SelectedItemStyle ForeColor="GhostWhite" BackColor="DarkSlateBlue"></SelectedItemStyle>
																	<AlternatingItemStyle BackColor="PaleGoldenrod"></AlternatingItemStyle>
																	<HeaderStyle Font-Bold="True" BackColor="Tan"></HeaderStyle>
																	<Columns>
																		<asp:BoundColumn Visible="False" DataField="reqid" HeaderText="reqid"></asp:BoundColumn>
																		<asp:BoundColumn Visible="False" DataField="fdid"></asp:BoundColumn>
																		<asp:BoundColumn DataField="name" HeaderText="Name"></asp:BoundColumn>
																		<asp:BoundColumn DataField="relationship" HeaderText="RelationShip"></asp:BoundColumn>
																		<asp:BoundColumn DataField="MaritalStatus" HeaderText="MaritialStatus"></asp:BoundColumn>
																		<asp:BoundColumn Visible="False" DataField="occupation" HeaderText="Occupation"></asp:BoundColumn>
																		<asp:BoundColumn Visible="False" DataField="DOB" SortExpression="DOB" HeaderText="Date Of Birth"
																			DataFormatString="{0:d}"></asp:BoundColumn>
																		<asp:BoundColumn DataField="dependent" SortExpression="dependent" HeaderText="Dependent"></asp:BoundColumn>
																		<asp:BoundColumn DataField="AddFlag" HeaderText="Requested For"></asp:BoundColumn>
																		<asp:BoundColumn DataField="RequestedOn" HeaderText="Requested On" DataFormatString="{0:d}"></asp:BoundColumn>
																		<asp:BoundColumn Visible="False" DataField="pcode"></asp:BoundColumn>
																		<asp:TemplateColumn>
																			<ItemTemplate>
																				<asp:LinkButton id="lbFCancelRequest" onclick="RemoveFmailyRequest" runat="server" ForeColor="Black">Cancel Request</asp:LinkButton>
																			</ItemTemplate>
																		</asp:TemplateColumn>
																	</Columns>
																	<PagerStyle HorizontalAlign="Center" ForeColor="DarkSlateBlue" BackColor="PaleGoldenrod"></PagerStyle>
																</asp:DataGrid></TD>
														</TR>
													</TABLE>
												</asp:panel><asp:panel dir="ltr" id="Panel2" runat="server" Visible="False">
													<TABLE id="Table16" cellSpacing="0" cellPadding="3" width="100%" border="0">
														<TR>
															<TD class="PanelTitle">Update Family Information
															</TD>
														</TR>
													</TABLE>
													<TABLE id="Table3" cellSpacing="0" cellPadding="2" width="100%" border="0">
														<TR>
															<TD class="FormColor1" style="WIDTH: 196px" vAlign="top" align="right"><STRONG>Name: </STRONG>
															</TD>
															<TD class="FormColor1" vAlign="top" align="left">
																<asp:TextBox id="txtFamilyName" runat="server" Width="350px" CssClass="TextBox" MaxLength="50"></asp:TextBox></TD>
														</TR>
														<TR>
															<TD class="FormColor1" style="WIDTH: 196px" vAlign="top" align="right"><STRONG>Date Of 
																	Birth: </STRONG>
															</TD>
															<TD class="FormColor1" vAlign="top" align="left">
																<ew:CalendarPopup id="CalendarPopup1" runat="server">
																	<TextboxLabelStyle CssClass="TextBox"></TextboxLabelStyle>
																	<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></WeekdayStyle>
																	<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="Yellow"></MonthHeaderStyle>
																	<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																		BackColor="AntiqueWhite"></OffMonthStyle>
																	<ButtonStyle CssClass="Button"></ButtonStyle>
																	<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></GoToTodayStyle>
																	<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="LightGoldenrodYellow"></TodayDayStyle>
																	<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="Orange"></DayHeaderStyle>
																	<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="LightGray"></WeekendStyle>
																	<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="Yellow"></SelectedDateStyle>
																	<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></ClearDateStyle>
																	<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																		BackColor="White"></HolidayStyle>
																</ew:CalendarPopup><STRONG>
																	<asp:Label id="lblInvisible" runat="server" CssClass="Invisible"></asp:Label></STRONG></TD>
														</TR>
														<TR>
															<TD class="FormColor1" style="WIDTH: 196px" vAlign="top" align="right"><STRONG>Relationship:</STRONG>
															</TD>
															<TD class="FormColor1" vAlign="top" align="left">
																<asp:DropDownList id="rdRelation" runat="server" Width="350px" CssClass="textbox">
																	<asp:ListItem Value="0">Select--Relationship</asp:ListItem>
																	<asp:ListItem Value="2">Husband</asp:ListItem>
																	<asp:ListItem Value="3">Wife</asp:ListItem>
																	<asp:ListItem Value="4">Son</asp:ListItem>
																	<asp:ListItem Value="5">Daughter</asp:ListItem>
																</asp:DropDownList></TD>
														</TR>
														<TR>
															<TD class="FormColor1" style="WIDTH: 196px" vAlign="top" align="right"><STRONG>Maritial 
																	Status:</STRONG>
															</TD>
															<TD class="FormColor1" vAlign="top" align="left">
																<asp:DropDownList id="rdStatus" runat="server" Width="350px" CssClass="textbox">
																	<asp:ListItem Value="0">Select--Marital  Status</asp:ListItem>
																	<asp:ListItem Value="1">Single</asp:ListItem>
																	<asp:ListItem Value="2">Married</asp:ListItem>
																	<asp:ListItem Value="3">Divorced</asp:ListItem>
																	<asp:ListItem Value="4">Widow</asp:ListItem>
																	<asp:ListItem Value="5">Separated</asp:ListItem>
																</asp:DropDownList></TD>
														</TR>
														<TR>
															<TD class="FormColor1" style="WIDTH: 196px" vAlign="top" align="right"><STRONG>Dependent:</STRONG>
															</TD>
															<TD class="FormColor1" vAlign="top" align="left">
																<asp:DropDownList id="ddDependent" runat="server" Width="350px" CssClass="textbox">
																	<asp:ListItem Value="0">Select--Dependent</asp:ListItem>
																	<asp:ListItem Value="1">Yes</asp:ListItem>
																	<asp:ListItem Value="2">No</asp:ListItem>
																</asp:DropDownList></TD>
														</TR>
														<TR>
															<TD class="FormColor1" style="WIDTH: 196px" vAlign="top" align="right"><STRONG>Occupation:</STRONG>
															</TD>
															<TD class="FormColor1" vAlign="top" align="left">
																<asp:TextBox id="txtOccupation" runat="server" Width="350px" CssClass="textbox" MaxLength="100"
																	Rows="3" TextMode="MultiLine"></asp:TextBox></TD>
														</TR>
														<TR>
															<TD class="FormColor1" style="WIDTH: 196px" vAlign="top" align="right">
																<asp:Label id="lblFamilyFile" runat="server" Font-Bold="True">Attached Document: </asp:Label></TD>
															<TD class="FormColor1" vAlign="top" align="left"><INPUT id="fileFamily" style="BORDER-RIGHT: gray 1px solid; BORDER-TOP: gray 1px solid; FONT-SIZE: 10px; BORDER-LEFT: gray 1px solid; WIDTH: 350px; BORDER-BOTTOM: gray 1px solid"
																	type="file" size="39" name="File2" runat="server">
															</TD>
														</TR>
														<TR>
															<TD class="FormColor1" style="WIDTH: 196px" vAlign="top" align="right"></TD>
															<TD class="FormColor1" vAlign="top" align="left">Spouse
																<asp:Label id="lblSpouseTooltip" style="CURSOR: hand" runat="server" Font-Bold="True" Font-Size="Small"
																	ForeColor="Yellow" Font-Underline="True">?</asp:Label><BR>
																Children
																<asp:Label id="lblChildrenToolTip" style="CURSOR: hand" runat="server" Font-Bold="True" Font-Size="Small"
																	ForeColor="Yellow" Font-Underline="True">?</asp:Label></TD>
														</TR>
														<TR>
															<TD class="FormColor1" vAlign="top" align="left" colSpan="2">
																<asp:Button id="btnNew" runat="server" Width="80px" Text="Save"></asp:Button>
																<asp:Button id="btnFupdate" runat="server" CausesValidation="False" Width="80px" Text="Cancel"></asp:Button>
																<asp:Button id="cmdFDelete" runat="server" Visible="False" Width="80px" Text="Delete"></asp:Button>
																<asp:Label id="lblFGender" runat="server"></asp:Label></TD>
														</TR>
													</TABLE>
												</asp:panel><asp:textbox style="VISIBILITY: hidden" id="TextBox1" runat="server"></asp:textbox><asp:textbox style="VISIBILITY: hidden" id="txtX" runat="server" Width="24px"></asp:textbox><asp:textbox style="VISIBILITY: hidden" id="txtY" runat="server" Width="24px"></asp:textbox></td>
										</tr>
									</table>
								</td>
								<td style="WIDTH: 180px" vAlign="top" align="center"></td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td class="Fotter" height="20" align="center">Copyright © 2005 Independent Media 
						Corporation www.geo.tv<br>
					</td>
				</tr>
			</table>
		</form>
	</body>
</HTML>
