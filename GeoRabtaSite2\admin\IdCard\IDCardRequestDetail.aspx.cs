using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Configuration;
using System.Data.SqlClient;
using System.IO;
namespace GeoRabtaSite.admin
{
	/// <summary>
	/// Summary description for _default.
	/// </summary>
	public class IDCardsRequestDetailAdmin : System.Web.UI.Page
	{
		protected System.Web.UI.WebControls.Label txtReqID;
		protected Anthem.Image Image1;
		protected System.Web.UI.WebControls.TextBox txtComments;
		protected System.Web.UI.WebControls.Button btnReply;
		protected System.Web.UI.WebControls.Label lblHTML;
		protected System.Web.UI.WebControls.Label txtPCode;
		protected System.Web.UI.WebControls.Label txtDept;
		protected System.Web.UI.WebControls.Label txtDesg;
		protected System.Web.UI.WebControls.Label txtStation;
		protected System.Web.UI.WebControls.Label txtCNIC;
		protected System.Web.UI.WebControls.Label txtLastAction;
		protected System.Web.UI.WebControls.Label txtLastActionDate;
		protected System.Web.UI.WebControls.Label txtName;
		protected System.Web.UI.WebControls.DropDownList ddActionCode;
		protected System.Web.UI.WebControls.Label lblSuccessMsg;
		SqlConnection con; 
		private void Page_Load(object sender, System.EventArgs e)
		{
			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			try
			{
				string userId=Session["user_id"].ToString();
				if(userId=="")
				{
					Response.Redirect("../../login.aspx");
				}
			}
			catch
			{
				Response.Redirect("../../login.aspx");
			}
			if(!IsPostBack)
			{
			 GetActionCode();
			 ServiceTrack();
			 btnReply.Attributes.Add("onclick","return SetParentWindowsHiddenFieldValue();");
			 this.txtReqID.Text=Request.QueryString[0];
			 this.txtPCode.Text=Request.QueryString[1];
			 this.txtName.Text=Request.QueryString[2];
			 this.txtDept.Text=Request.QueryString[3];
			 this.txtDesg.Text=Request.QueryString[4];
			 this.txtStation.Text=Request.QueryString[5];
			 this.txtCNIC.Text=Request.QueryString[6];
			 this.txtLastAction.Text=Request.QueryString[7];
				if(Request.QueryString[8].Length>0)
				{
					this.txtLastActionDate.Text=DateTime.Parse(Request.QueryString[8]).ToString("dd MMM,yyyy hh:mm:ss:tt");
				}
				else
				{
				 this.txtLastActionDate.Text="N/A";
				}
				if(File.Exists(Server.MapPath(@"..\..\EmpThumbnail\"+txtPCode.Text.Trim()+".jpg")))
				{
					this.Image1.ImageUrl=@"..\..\EmpThumbnail\"+txtPCode.Text.Trim()+".jpg";
				}
				else
				{
				 this.Image1.ImageUrl=@"..\..\EmpThumbnail\none.jpg";
				}
			}
			// Put user code to initialize the page here
		}
		public void GetActionCode()
		{
		 ddActionCode.Items.Clear();
		 ddActionCode.Items.Add(new ListItem("Select--Action Code","0"));
		 con=new SqlConnection(Connection.ConnectionString);
		 con.Open();
		 SqlCommand cmd=new SqlCommand("select id,actioncode from t_idcardactioncode where isactive=1" ,con);
	     SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
			 ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
			 this.ddActionCode.Items.Add(itm);
			}
		 rd.Close();
		 con.Close();
		}
		public void ServiceTrack()
		{
			string cQuery=" SELECT dbo.t_IDCardActionCode.ActionCode, dbo.t_IDCardLog.Comments, dbo.t_IDCardLog.ActionDate, dbo.t_IDCardLog.Name " +
            " FROM  dbo.t_IDCardLog INNER JOIN " +
            " dbo.t_IDCardActionCode ON dbo.t_IDCardLog.ActionCode = dbo.t_IDCardActionCode.ID " +
            " WHERE (dbo.t_IDCardLog.RequestID = "+Request.QueryString[0]+") " +
            " ORDER BY dbo.t_IDCardLog.ActionDate ";
			SqlDataAdapter dr=new SqlDataAdapter(cQuery,con);
			DataSet ds=new DataSet();
			dr.Fill(ds,"Records");
			ds.AcceptChanges();
			string html="<table cellpadding=1 cellspacing=0 border=1 class='display' id='example'> "+
				"<thead> "+
				"<tr> "+
				"<th>Action Code</th> "+
				"<th>Comment</th> "+
				"<th>Action Date</th> "+
				"<th>Action Performed By</th> "+
				"</tr> "+
				"</thead> "+
				"<tbody>";
			for(int i=0;i<ds.Tables[0].Rows.Count;i++)
			{
				string reqNo="";
				string code="";
				string name="";
				string depart="";
				reqNo=ds.Tables[0].Rows[i]["ActionCode"].ToString();
				code=ds.Tables[0].Rows[i]["Comments"].ToString(); 
				name=ds.Tables[0].Rows[i]["ActionDate"].ToString(); ;
				depart=ds.Tables[0].Rows[i]["Name"].ToString(); ;
				html+="<tr> "+
				"<td>"+reqNo+"</td> "+
				"<td>"+code+"</td> "+
				"<td>"+DateTime.Parse(name).ToString("dd MMM,yyyy hh:mm:ss tt")+"</td> "+
				"<td>"+depart+"</td> "+
				"</tr> ";
			}
			html+="</tbody>";
			html+="</table>";
			this.lblHTML.Text=html;
		}
		#region Web Form Designer generated code
					override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.ddActionCode.SelectedIndexChanged += new System.EventHandler(this.ddActionCode_SelectedIndexChanged);
			this.btnReply.Click += new System.EventHandler(this.btnReply_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion
        
		public string GetEmpName(string pcode)
		{
		  SqlConnection con=new SqlConnection(Connection.ConnectionString);
		  con.Open();
		  SqlCommand cmd=new SqlCommand("select name from t_employee where pcode='"+pcode+"'",con);
		  SqlDataReader rd=cmd.ExecuteReader();
		  rd.Read();
		  string Name=rd[0].ToString();
		  rd.Close();
		  con.Close();
		  return Name;
		}
		private void btnReply_Click(object sender, System.EventArgs e)
		{
			string message="";
			string reqStat="";
			if(this.ddActionCode.SelectedValue=="1")
			{
				message=IDCardsMessage.CardDelivered();
				reqStat="1";
			}
			else if(this.ddActionCode.SelectedValue=="2")
			{
				message=IDCardsMessage.CardDeliveredOnBehalfOf(this.txtComments.Text);
				reqStat="1";
			}
			else if(this.ddActionCode.SelectedValue=="3")
			{
				message=IDCardsMessage.CardHold();
				reqStat="0";
			}
			else if(this.ddActionCode.SelectedValue=="4")
			{
				message=IDCardsMessage.CardHoldDueToHRProcess();
				reqStat="0";
			}
			else if(this.ddActionCode.SelectedValue=="5")
			{
				message=IDCardsMessage.CardDelivered();
				reqStat="1";
			}
			else if(this.ddActionCode.SelectedValue=="6")
			{
				message=IDCardsMessage.ExceptionErrorResolved();
				reqStat="0";
			}
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
            //==============================DB Query===============//
            string Query="UPDATE  t_IDCardRequest SET ActionCodeID = @ActionCodeID, ActionDate = @ActionDate, SpecialistPCode = @SpecialistPCode, Status = @Status WHERE (RequestID = @RequestID)";
			SqlParameter _aCodeID=new SqlParameter("@ActionCodeID",SqlDbType.TinyInt);
			SqlParameter _aCodeDate=new SqlParameter("@ActionDate",SqlDbType.DateTime);
			SqlParameter _SpPCode=new SqlParameter("@SpecialistPCode",SqlDbType.Char);
			SqlParameter _Status=new SqlParameter("@Status",SqlDbType.TinyInt);
			SqlParameter _ReqID=new SqlParameter("@RequestID",SqlDbType.Int);
			_aCodeID.Value=this.ddActionCode.SelectedValue;
			_aCodeDate.Value=DateTime.Now;
			_SpPCode.Value=Session["user_id"].ToString();
			_Status.Value=reqStat;
			_ReqID.Value=this.txtReqID.Text.Trim();
			SqlCommand cmd=new SqlCommand(Query,con);
			cmd.Parameters.Add(_aCodeID);
			cmd.Parameters.Add(_aCodeDate);
			cmd.Parameters.Add(_SpPCode);
			cmd.Parameters.Add(_Status);
			cmd.Parameters.Add(_ReqID);
			cmd.ExecuteNonQuery();

			Query="INSERT INTO t_IDCardLog (RequestID, ActionCode, Comments, ActionDate, Name) "+
            "VALUES (@RequestID,@ActionCode,@Comments,@ActionDate,@Name)";
			SqlParameter _reqID=new SqlParameter("@RequestID",this.txtReqID.Text.Trim());
			SqlParameter _aCode=new SqlParameter("@ActionCode",this.ddActionCode.SelectedValue);
			SqlParameter _Comments=new SqlParameter("@Comments",this.txtComments.Text);
			SqlParameter _aDate=new SqlParameter("@ActionDate",DateTime.Now);
            SqlParameter _Name=new SqlParameter("@Name",GetEmpName(Session["user_id"].ToString()));
			cmd=new SqlCommand(Query,con);
			cmd.Parameters.Add(_reqID);
			cmd.Parameters.Add(_aCode);
			cmd.Parameters.Add(_Comments);
			cmd.Parameters.Add(_aDate);
			cmd.Parameters.Add(_Name);
			cmd.ExecuteNonQuery();
			ServiceTrack();
			this.btnReply.Enabled=false;
			this.lblSuccessMsg.Visible=true;
			this.lblSuccessMsg.Text="Request has been successfully submitted";  
			//=====================================================//
			con.Close();
			geoaurjeenaydo.Service mailService=new GeoRabtaSite.geoaurjeenaydo.Service();
			string email = "";
			string Val=GetEmail(this.txtPCode.Text.Trim());
			//Response.Write(Val);
			if(Val!="")
			{
				email = Val;
			}
			else
			{
			 //=============PCode missing in AD / Email Address not Found ,Send this Exception to E.R=========//
			 mailService.Post("<EMAIL>","ID Card Email Failure","E-mail not delivered due to PCode missing in Active Directory / Email Account does not exist <br><br><b>Employee Code: "+this.txtPCode.Text.Trim()+"</b><br><b>Employee Name: "+this.txtName.Text+"</b>",0);
			}
			if (email.Length > 0)
			{
				//email = email.Remove(email.Length - 1, 1);
				string Value=mailService.Post(email,"GEO I.D Card for 2021-22",message,0);
				//Response.Write(Value); 
			}
		}
		public string GetEmail(string pcode)
		{
		 SqlConnection conn=new SqlConnection(Connection.ConnectionString);
		 conn.Open();
		 SqlCommand cmd=new SqlCommand("select email from t_idcardrequest where pcode=@pcode",conn);
		 cmd.Parameters.Add("@pcode",pcode);
		 SqlDataReader rd=cmd.ExecuteReader();
		 rd.Read();
		 string temp="";
			if(rd.HasRows)
			{
			 temp=rd[0].ToString();
			}
		 rd.Close();
		 conn.Close();
		 return temp;
		}

		private void ddActionCode_SelectedIndexChanged(object sender, System.EventArgs e)
		{
		
		}

	
	}
}
