<%@ Page CodeBehind="ManageSpecialist.aspx.cs" Language="c#" AutoEventWireup="false" Inherits="GeoRabtaSite.ManageSpecialist" smartNavigation="False"%>
<%@ Register TagPrefix="uc1" TagName="myMenus" Src="myMenus.ascx" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Rabta :: Manage Queue List Specialist </title>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="RaabtaAdmin.css" type="text/css" rel="stylesheet">
		<script language="javascript" id="clientEventHandlersJS">
<!--

//-->
		</script>
	</HEAD>
	<body language="javascript" dir="ltr" bottomMargin="0" bgProperties="fixed" leftMargin="0"
		topMargin="0" onLoad="return window_onload()" rightMargin="0" onscroll="return window_onscroll()">
		<form id="myForm" name="MyForm" runat="server">
			<table height="100%" cellSpacing="0" cellPadding="0" width="750" align="center" bgColor="#ffffff"
				border="0">
				<TR>
					<TD vAlign="top" align="left" height="69">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="69" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="1826">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top1.swf">
							<PARAM NAME="Src" VALUE="flash/Top1.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top1.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="69"> </embed>
						</OBJECT>
					</TD>
				</TR>
				<tr>
					<td height="10">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="10" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="265">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top2.swf">
							<PARAM NAME="Src" VALUE="flash/Top2.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top2.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="10"> </embed>
						</OBJECT>
					</td>
				</tr>
				<TR>
					<TD height="20" class="PageTitle">Geo Raabta Admin :: Station Setup</TD>
				</TR>
				<tr>
					<td height="20" background="../images/menu-off-bg.gif"><uc1:mymenus id="MyMenus1" runat="server"></uc1:mymenus></td>
				</tr>
				<tr>
					<td vAlign="top" align="left" class="MainBG"><STRONG><FONT size="4"><BR>
								<TABLE class="MainFormColor" id="tdFrom2" cellSpacing="0" cellPadding="3" width="90%" align="center"
									border="0" runat="server">
									<TR>
										<TD class="OrangeFormTitle" colSpan="3">Queue List Specialist&nbsp;</TD>
									</TR>
									<TR>
										<TD width="320" style="HEIGHT: 133px">Select Department:<BR>
											<asp:panel id="Panel3" runat="server" Height="112px" Width="99%" CssClass="ov">
												<asp:Label id="Label18" runat="server" CssClass="Label" Font-Bold="True">Department: </asp:Label>
												<asp:linkbutton id="btnDepartAll" runat="server" CssClass="Label" CausesValidation="False">Select All</asp:linkbutton>
												<asp:checkboxlist id="chkDepartment" runat="server" CssClass="TextBox" Width="300px" Height="2px"
													AutoPostBack="True"></asp:checkboxlist>
											</asp:panel>
										</TD>
										<TD style="HEIGHT: 133px"></TD>
										<TD width="320" style="HEIGHT: 133px">
											<P>Select Designation:<BR>
												<asp:panel id="Panel4" runat="server" Height="112px" Width="99%" CssClass="ov">
													<asp:Label id="Label19" runat="server" CssClass="Label" Font-Bold="True">Designation:</asp:Label>
													<asp:linkbutton id="btnDesignationAll" runat="server" CssClass="Label" CausesValidation="False">Select All</asp:linkbutton>
													<BR>
													<asp:checkboxlist id="chkDesignation" runat="server" CssClass="TextBox" Width="300px" AutoPostBack="True"></asp:checkboxlist>
												</asp:panel></P>
										</TD>
									</TR>
									<TR>
										<TD width="320">Add to Specialist Owner (Functional Designation):<BR>
											<asp:panel id="Panel8" runat="server" Height="112px" Width="99%" CssClass="ov">
												<asp:Label id="Label26" runat="server" CssClass="Label" Font-Bold="True">Functional Designation:</asp:Label>
												<asp:linkbutton id="btnFuncAll" runat="server" CssClass="Label" CausesValidation="False">Select All</asp:linkbutton>
												<asp:checkboxlist id="chkFunctionalDesignation" runat="server" CssClass="TextBox" Width="300px"></asp:checkboxlist>
											</asp:panel>
										</TD>
										<TD align="center">
											<asp:LinkButton id="lnkAddFD" runat="server" ToolTip="Add to Specialist Owner list">>></asp:LinkButton><BR>
											<asp:LinkButton id="lnkRemFD" runat="server" ToolTip="Remove from Specialist Owner list"><<</asp:LinkButton></TD>
										<TD width="320">Specialist Owner:<BR>
											<asp:panel id="Panel1" runat="server" Height="112px" Width="99%" CssClass="ov">
												<asp:Label id="Label1" runat="server" CssClass="Label" Font-Bold="True">Specialist Owner</asp:Label>
												<asp:checkboxlist id="chkOwnerList" runat="server" CssClass="TextBox" Width="300px"></asp:checkboxlist>
											</asp:panel>
										</TD>
									</TR>
									<TR>
										<TD width="320">Specialist&nbsp;No:
											<asp:Label id="lblSpecialistNo" runat="server" Font-Bold="True"></asp:Label><BR>
										</TD>
										<TD></TD>
										<TD width="320">Select Specialist Department:<BR>
											<asp:DropDownList id="ddDept" runat="server" Width="100%" CssClass="textbox">
												<asp:ListItem Value="0">Select--Department</asp:ListItem>
											</asp:DropDownList>
										</TD>
									</TR>
									<TR>
										<TD width="320">
											<P>Manage Requests Timeslot:<BR>
												<asp:DropDownList id="ddTimeslot" runat="server" Width="100%" CssClass="textbox">
													<asp:ListItem Value="0">Select--Timeslot</asp:ListItem>
													<asp:ListItem Value="1">8 a.m - 4 p.m</asp:ListItem>
													<asp:ListItem Value="2">4 p.m - 12 p.m</asp:ListItem>
													<asp:ListItem Value="3">12 p.m - 8 a.m</asp:ListItem>
													<asp:ListItem Value="4">Public Holidays</asp:ListItem>
													<asp:ListItem Value="5">Weekly Holidays</asp:ListItem>
													<asp:ListItem Value="6">Any Time</asp:ListItem>
												</asp:DropDownList><BR>
												<BR>
												<BR>
												<BR>
												<BR>
												<BR>
											</P>
											<P>&nbsp;</P>
										</TD>
										<TD></TD>
										<TD width="320">Requested&nbsp;Department(s):<BR>
											<asp:panel id="Panel2" runat="server" Height="112px" Width="99%" CssClass="ov">
												<asp:Label id="Label2" runat="server" CssClass="Label" Font-Bold="True">Department: </asp:Label>
												<asp:linkbutton id="Linkbutton2" runat="server" CssClass="Label" CausesValidation="False">Select All</asp:linkbutton>
												<asp:checkboxlist id="chkOwnerDept" runat="server" CssClass="TextBox" Width="300px" Height="2px"></asp:checkboxlist>
											</asp:panel>
										</TD>
									</TR>
									<TR>
										<TD width="320">
											<P>Requested&nbsp;Category(ies):<BR>
												<asp:panel id="Panel5" runat="server" Height="112px" Width="99%" CssClass="ov">
													<asp:Label id="Label3" runat="server" CssClass="Label" Font-Bold="True">Category: </asp:Label>
													<asp:linkbutton id="Linkbutton3" runat="server" CssClass="Label" CausesValidation="False">Select All</asp:linkbutton>
													<asp:checkboxlist id="chkOwnerCat" runat="server" CssClass="TextBox" Width="300px" Height="2px"></asp:checkboxlist>
												</asp:panel>
											</P>
										</TD>
										<TD></TD>
										<TD width="320">
											<P>Requested Stations:<BR>
												<asp:panel id="Panel6" runat="server" Height="112px" Width="99%" CssClass="ov">
													<asp:Label id="Label4" runat="server" CssClass="Label" Font-Bold="True">Station: </asp:Label>
													<asp:linkbutton id="Linkbutton4" runat="server" CssClass="Label" CausesValidation="False">Select All</asp:linkbutton>
													<asp:checkboxlist id="chkOwnerStation" runat="server" CssClass="TextBox" Width="300px" Height="2px"></asp:checkboxlist>
												</asp:panel>
											</P>
										</TD>
									</TR>
									<TR>
										<TD width="320"></TD>
										<TD></TD>
										<TD width="320"></TD>
									</TR>
									<TR>
										<TD colSpan="3">
											<asp:Button id="btnNew" runat="server" Width="64px" Text="New"></asp:Button>
											<asp:Button id="btnSave" runat="server" Width="64px" Text="Save"></asp:Button></TD>
									</TR>
								</TABLE>
							</FONT></STRONG>
						<BR>
						<TABLE id="Table2" cellSpacing="0" cellPadding="3" width="90%" align="center" border="0"
							runat="server">
							<TR>
								<TD width="320"></TD>
								<TD width="120"></TD>
								<TD width="320"></TD>
							</TR>
							<TR>
								<TD colSpan="3"></TD>
							</TR>
						</TABLE>
					</td>
				</tr>
				<tr>
					<td vAlign="middle" align="center" height="15">Copyright © 2005 Independent Media 
						Corporation <A href="http://www.geo.tv">www.geo.tv</A></td>
				</tr>
			</table>
		</form>
	</body>
</HTML>
