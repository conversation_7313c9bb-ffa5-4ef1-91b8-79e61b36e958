<%@ Page CodeBehind="UpdateEmployee.aspx.cs" Language="c#" AutoEventWireup="false" Inherits="GeoRabtaSite.UpdateEmployeInfo" smartNavigation="False" %>
<%@ Register TagPrefix="uc1" TagName="myMenus" Src="myMenus.ascx" %>
<%@ Register TagPrefix="ew" Namespace="eWorld.UI" Assembly="eWorld.UI" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Rabta ::</title>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="RaabtaAdmin.css" type="text/css" rel="stylesheet">
		<LINK href="../Styles4.css" type="text/css" rel="stylesheet">
		<script language="javascript">
		function checkValue()
         {
 var Val=document.getElementById('lblFuncFV');
 var cVal=document.getElementById('lblFdID');
 var cFVal=document.getElementById('lblFunc');
 //var sVal=cFVal.item(cFVal.selectedIndex).value;
 var sVal=cFVal;
 if(Val.innerHTML=='0' && sVal.innerHTML != cVal.innerHTML)
 {
  alert('You can not assign selected functional designation because there is no vacant position on it');
  //document.getElementById('ddFDesignation').focus();
  event.retrurnValue=false;
  return false;
 }
 else if (Val.innerHTML=='')
 {
  alert('Please select Functional Designation');
  //document.getElementById('ddFDesignation').focus();
  event.retrurnValue=false;
  return false;
 }
	// add attribute to command button and call this function like this
	// btnSave.attributes.add("onclick","return checkValue();");
	var d = document.getElementById("calendarDOB");
	var dateOfBirth= new Date(d.value);
	//var dateOfBirth = new Date("25/2/1955");
	var sDate=document.getElementById("txtServerDate");
	var serverDate = new Date(sDate.value);
	var dif=new Date(serverDate-dateOfBirth);
	var age = dif.getFullYear()-1970;
	//alert(age);
	//window.document.title="Age is " + age.toSring();
	var c;
	if (age<18)
		{
		//return confirm("Employee is under 18. Are you sure to continue with this age?");
		if(confirm("Employee is under 18. Are you sure to continue with this age?"))
			{
			event.returnValue=true;
			return true;
			}
		else
			{
			event.returnValue=false;
			return false;
			}
		}
	   else if(age >60)
		{
		  var status=document.getElementById("txtHidden");
		  if(status.value==1)
		  {
		   alert("Employee Age is above 60 therefore you cannot allocate him/her Permanent Status. Please re-assign employment type other than Permanent");
		   event.returnValue=false;
		   return false;
		  }
		}	
	else
		{
		event.returnValue=true;
		return true;
		}
		
	}
	function CheckVacancy()
    {
 var Val=document.getElementById('lblFV');
 var cVal=document.getElementById('lblFdID');
 var cFVal=document.getElementById('ddFDesignation');
 var sVal=cFVal.item(cFVal.selectedIndex).value;
 //alert('('+cVal.innerHTML+')');
 if(Val.innerHTML=='0' && sVal != cVal.innerHTML)
 {
  alert('You can not assign selected functional designation because there is no vacant position on it');
  document.getElementById('ddFDesignation').focus();
  event.retrurnValue=false;
  return false;
 }
 else if (Val.innerHTML=='')
 {
  alert('Please select Functional Designation');
  document.getElementById('ddFDesignation').focus();
  event.retrurnValue=false;
  return false;
 }
 else
 {
    event.retrurnValue=true;
    return true;
 }
}

function ReActivateFD()
    {
 var cVal=document.getElementById('lblFdID');
 if(cVal.innerHTML=='')
 {
  alert('There is no Functional Designation exist on this employee.Please assign Functional Designation & Press on update button to modify the record then you can Re-Active it')
  event.retrurnValue=false;
  return false;
 }
 else
 {
    event.retrurnValue=true;
    return true;
 }
}

function VerifyFD()
    {
 var cVal=document.getElementById('lblFdID');
 if(cVal.innerHTML=='')
 {
  alert('There is no Functional Designation exist on this employee.Please assign Functional Designation')
  event.retrurnValue=false;
  return false;
 }
 else
 {
    event.retrurnValue=true;
    return true;
 }
}
		</script>
		<script language="javascript" id="clientEventHandlersJS">
<!--

function window_onscroll() {
document.getElementById("txtX").value=document.body.scrollTop;
document.getElementById("txtY").value=document.body.scrollLeft;
}

function window_onload() {
document.body.scrollTop=document.getElementById("txtX").value;
document.body.scrollLeft=document.getElementById("txtY").value;
}
function DisableCtrlN()
{
	
	if (event.ctrlKey)
	{
		if (event.keyCode==78 || event.keyCode==73 || event.keyCode==65 || event.keyCode==67 || event.keyCode==68)
		{
		event.returnValue=false;
		return false;
		}
		
	}
}
function document_onselectstart() {
event.returnValue=false;
	return false;
}

function document_ondragstart() {
event.returnValue=false;
	return false;
}
function checkNum()
{
   var ddBox=document.getElementById("ddProbabtion");
   if(ddBox.value==-2)
   { 
   var str= document.getElementById("txtProbabtionOther");
   //if(isNaN(str.value)==true)
   if (event.keyCode <48 || event.keyCode >57)
   {
    alert("Please enter only numeric values");
    str.value="";
    str.focus();
    return false;  
   } 
 }
}
//-->
		</script>
		<script language="javascript" event="onselectstart" for="document">
<!--

return document_onselectstart()
//-->
		</script>
		<script language="javascript" event="ondragstart" for="document">
<!--

return document_ondragstart()
//-->
		</script>
	</HEAD>
	<body language="javascript" onkeypress="DisableCtrlN()" dir="ltr" onkeydown="DisableCtrlN()"
		onkeyup="DisableCtrlN()" onscroll="return window_onscroll()" bottomMargin="0" bgProperties="fixed"
		leftMargin="0" topMargin="0" onload="return window_onload()" rightMargin="0">
		<form id="myForm" name="MyForm" runat="server">
			<TABLE id="Table3" height="100%" cellSpacing="0" cellPadding="0" width="780" align="center"
				bgColor="#ffffff" border="0">
				<TR>
					<TD vAlign="middle" align="left" height="69">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" width="780" height="69">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="1826">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top1.swf">
							<PARAM NAME="Src" VALUE="flash/Top1.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<embed src="flash/Top1.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="69"> </embed>
						</OBJECT>
					</TD>
				</TR>
				<TR>
					<TD height="10">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" width="780" height="10">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="265">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top2.swf">
							<PARAM NAME="Src" VALUE="flash/Top2.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<embed src="flash/Top2.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="10"> </embed>
						</OBJECT>
					</TD>
				</TR>
				<TR>
					<TD class="pagetitle" height="20">Geo Raabta :: Update Employee</TD>
				</TR>
				<TR>
					<TD class="mainbg" width="780" height="20"><asp:imagebutton id="ImageButton1" runat="server" ImageUrl="..\images\employee.gif" CausesValidation="False"></asp:imagebutton><asp:imagebutton id="ImageButton2" runat="server" ImageUrl="..\images\personal.gif" CausesValidation="False"></asp:imagebutton><asp:imagebutton id="ImageButton3" runat="server" ImageUrl="..\images\family.gif" CausesValidation="False"></asp:imagebutton><asp:imagebutton id="ImageButton4" runat="server" ImageUrl="..\images\education.gif" CausesValidation="False"></asp:imagebutton><asp:imagebutton id="ImageButton5" runat="server" ImageUrl="..\buttons\bondpaper.gif" CausesValidation="False"></asp:imagebutton><asp:imagebutton id="ImageButton6" runat="server" ImageUrl="..\buttons\tabFunctionalDesignation.gif"
							CausesValidation="False"></asp:imagebutton><asp:imagebutton id="Imagebutton7" runat="server" ImageUrl="..\buttons\account.gif" CausesValidation="False"></asp:imagebutton><asp:imagebutton id="ImgCompensation" runat="server" ImageUrl="../buttons/CompensationBenefits.gif"
							CausesValidation="False"></asp:imagebutton>
						<asp:ImageButton id="ImgRelative" runat="server" ImageUrl="../images/relativeinfo.gif"></asp:ImageButton></TD>
				</TR>
				<TR>
					<TD background="..\images\tabstrip.jpg" height="14"></TD>
				</TR>
				<TR>
					<TD class="mainbg" vAlign="top" align="left">
						<TABLE id="Table5" cellSpacing="0" cellPadding="0" width="100%" align="center" bgColor="white"
							border="0">
							<TR>
								<TD class="mainbg" width="100%"><BR>
									&nbsp;
									<asp:image id="Image1" runat="server" Height="110px" Width="100px"></asp:image><asp:label id="lblEmployeeName" runat="server" Font-Size="Large" Font-Bold="True" Font-Names="Verdana"></asp:label><asp:panel id="pnlPersonal" runat="server" Height="304px" Width="100%" Visible="False">
										<asp:Panel id="pnlPersonalUPdate" runat="server" Width="100%">
											<TABLE id="Table9" class="MainFormColor" border="0" cellSpacing="0" cellPadding="3" width="100%"
												align="center">
												<TR>
													<TD class="OrangeFormTitle" colSpan="3">Personal</TD>
												</TR>
												<TR>
													<TD class="MENUBAR" vAlign="top" width="320"><STRONG><FONT size="2">* </FONT>Date Of 
															Birth:</STRONG>
														<asp:RequiredFieldValidator id="RequiredFieldValidator1" runat="server" ControlToValidate="calendarDOB" ErrorMessage="Please Select Date">*</asp:RequiredFieldValidator>
														<asp:CompareValidator id="CompareValidator2" runat="server" ControlToValidate="calendarDOB" ErrorMessage="Date of birth must be less  than date of  join"
															Operator="LessThan" ControlToCompare="CalenderDOJ" Type="Date" Display="Dynamic">*</asp:CompareValidator>
														<asp:TextBox style="VISIBILITY: hidden" id="txtHidden" runat="server" CssClass="textbox"></asp:TextBox></TD>
													<TD class="MENUBAR" vAlign="top" width="95"><STRONG></STRONG></TD>
													<TD class="MENUBAR" vAlign="top" width="320"><STRONG><STRONG><FONT size="2">* </FONT></STRONG>
															Father's Name:</STRONG>
														<asp:RequiredFieldValidator id="RequiredFieldValidator2" runat="server" ControlToValidate="txtFName" ErrorMessage="Please Enter Father's Name">*</asp:RequiredFieldValidator>
														<asp:RegularExpressionValidator id="RegularExpressionValidator6" runat="server" ControlToValidate="txtFName" ErrorMessage="Enter Only Alphabets"
															ValidationExpression="^[a-z A-Z.-]+[(a-z A-Z)]+[(()')]*$">*</asp:RegularExpressionValidator></TD>
												</TR>
												<TR>
													<TD vAlign="top" width="320">
														<ew:CalendarPopup id="calendarDOB" runat="server" Width="120px" EnableHideDropDown="True" Nullable="True">
															<TextboxLabelStyle CssClass="textbox"></TextboxLabelStyle>
															<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="White"></WeekdayStyle>
															<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Yellow"></MonthHeaderStyle>
															<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																BackColor="AntiqueWhite"></OffMonthStyle>
															<ButtonStyle CssClass="button"></ButtonStyle>
															<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="White"></GoToTodayStyle>
															<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="LightGoldenrodYellow"></TodayDayStyle>
															<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Orange"></DayHeaderStyle>
															<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="LightGray"></WeekendStyle>
															<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Yellow"></SelectedDateStyle>
															<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="White"></ClearDateStyle>
															<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="White"></HolidayStyle>
														</ew:CalendarPopup>
														<asp:Label id="Label1" runat="server"></asp:Label>
														<asp:Label style="VISIBILITY: hidden" id="lblFunc" runat="server"></asp:Label>
														<asp:Label style="VISIBILITY: hidden" id="lblFuncFV" runat="server"></asp:Label></TD>
													<TD vAlign="top" width="95"></TD>
													<TD vAlign="top" width="320">
														<asp:TextBox id="txtFName" runat="server" Width="100%" CssClass="TextBox" MaxLength="1000"></asp:TextBox></TD>
												</TR>
												<TR>
													<TD class="menubar" vAlign="top" width="320"><STRONG>Gender:</STRONG></TD>
													<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
													<TD class="menubar" vAlign="top" width="320"><STRONG>Blood Group:</STRONG></TD>
												</TR>
												<TR>
													<TD vAlign="top" width="320">
														<asp:RadioButtonList id="RadioButtonList1" runat="server" Font-Size="10px" RepeatDirection="Horizontal">
															<asp:ListItem Value="1">Male</asp:ListItem>
															<asp:ListItem Value="2">Female</asp:ListItem>
														</asp:RadioButtonList></TD>
													<TD vAlign="top" width="95"></TD>
													<TD vAlign="top" width="320">
														<asp:DropDownList id="ddBloodGrp" runat="server" Width="100%" Visible="False" CssClass="textbox">
															<asp:ListItem Value="0">Select Blood Group</asp:ListItem>
															<asp:ListItem Value="1">A+</asp:ListItem>
															<asp:ListItem Value="2">A-</asp:ListItem>
															<asp:ListItem Value="3">B+</asp:ListItem>
															<asp:ListItem Value="4">B-</asp:ListItem>
															<asp:ListItem Value="5">AB+</asp:ListItem>
															<asp:ListItem Value="6">AB-</asp:ListItem>
															<asp:ListItem Value="7">O+</asp:ListItem>
															<asp:ListItem Value="8">O-</asp:ListItem>
														</asp:DropDownList></TD>
												</TR>
												<TR>
													<TD class="menubar" vAlign="top" width="320"><STRONG>Religion:</STRONG></TD>
													<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
													<TD class="menubar" vAlign="top" width="320"><STRONG>Nick Name:</STRONG></TD>
												</TR>
												<TR>
													<TD vAlign="top" width="320">
														<asp:DropDownList id="ddReligion" runat="server" Width="100%" CssClass="textbox">
															<asp:ListItem Value="0">Select--Religion</asp:ListItem>
															<asp:ListItem Value="1">Islam</asp:ListItem>
															<asp:ListItem Value="2">Christianity</asp:ListItem>
															<asp:ListItem Value="3">Buddhism</asp:ListItem>
															<asp:ListItem Value="4">Zoroastrian</asp:ListItem>
															<asp:ListItem Value="5">Jewish</asp:ListItem>
															<asp:ListItem Value="6">Hinduism</asp:ListItem>
															<asp:ListItem Value="7">Others</asp:ListItem>
														</asp:DropDownList></TD>
													<TD vAlign="top" width="95"></TD>
													<TD vAlign="top" width="320">
														<asp:TextBox id="txtNick" runat="server" Width="100%" CssClass="textbox" MaxLength="100"></asp:TextBox></TD>
												</TR>
												<TR>
													<TD class="menubar" vAlign="top" width="320"><STRONG><STRONG><FONT size="2">* </FONT></STRONG>
															Address:</STRONG>
														<asp:RequiredFieldValidator id="RequiredFieldValidator3" runat="server" ControlToValidate="txtAddress" ErrorMessage="Please Enter Address">*</asp:RequiredFieldValidator></TD>
													<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
													<TD class="menubar" vAlign="top" width="320"><STRONG>Marital Status:</STRONG></TD>
												</TR>
												<TR>
													<TD vAlign="top" width="320">
														<asp:TextBox id="txtAddress" runat="server" Width="100%" CssClass="TextBox" MaxLength="2000"
															TextMode="MultiLine"></asp:TextBox></TD>
													<TD vAlign="top" width="95"></TD>
													<TD vAlign="top" width="320">
														<asp:DropDownList id="ddStatus" runat="server" Width="100%" CssClass="textbox">
															<asp:ListItem Value="0">Select--Status</asp:ListItem>
															<asp:ListItem Value="1">Single</asp:ListItem>
															<asp:ListItem Value="2">Married</asp:ListItem>
															<asp:ListItem Value="3">Divorced</asp:ListItem>
															<asp:ListItem Value="4">Widow</asp:ListItem>
															<asp:ListItem Value="5">Separated</asp:ListItem>
														</asp:DropDownList></TD>
												</TR>
												<TR>
													<TD class="menubar" vAlign="top" width="320"><STRONG>Email (Personal):
															<asp:RegularExpressionValidator id="RegularExpressionValidator9" runat="server" ControlToValidate="txtEmail" ErrorMessage="Enter email in correct format<br><EMAIL>"
																ValidationExpression="\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*">*</asp:RegularExpressionValidator></STRONG></TD>
													<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
													<TD class="menubar" vAlign="top" width="320"><STRONG>NTN No:</STRONG></TD>
												</TR>
												<TR>
													<TD vAlign="top" width="320">
														<asp:TextBox id="txtEmail" runat="server" Width="100%" CssClass="TextBox" MaxLength="1000"></asp:TextBox></TD>
													<TD vAlign="top" width="95"></TD>
													<TD vAlign="top" width="320">
														<asp:TextBox id="txtNTN" runat="server" Width="100%" CssClass="TextBox" MaxLength="50"></asp:TextBox></TD>
												</TR>
												<TR>
													<TD class="menubar" vAlign="top" width="320"><STRONG>Residential Tel #: </STRONG>
														<asp:RegularExpressionValidator id="RegularExpressionValidator7" runat="server" ControlToValidate="txtContactNo"
															ErrorMessage="Alphabets not allowed in residential telephone" ValidationExpression="^[ 0-9-,]*$">*</asp:RegularExpressionValidator></TD>
													<TD class="menubar" vAlign="top" width="95"></TD>
													<TD class="menubar" vAlign="top" width="320"><STRONG>Mobile #:</STRONG>
														<asp:RegularExpressionValidator id="RegularExpressionValidator8" runat="server" ControlToValidate="txtMobile" ErrorMessage="Alphabets not allowed"
															ValidationExpression="^[0-9 ,-]*$">*</asp:RegularExpressionValidator></TD>
												</TR>
												<TR>
													<TD vAlign="top" width="320">
														<asp:TextBox id="txtContactNo" runat="server" Width="100%" CssClass="TextBox" MaxLength="50"></asp:TextBox></TD>
													<TD vAlign="top" width="95"></TD>
													<TD vAlign="top" width="320">
														<asp:TextBox id="txtMobile" runat="server" Width="100%" CssClass="TextBox" MaxLength="50"></asp:TextBox></TD>
												</TR>
												<TR>
													<TD class="menubar" vAlign="top" width="320"><STRONG>Nationality 1:</STRONG>
														<asp:CustomValidator id="CustomValidator1" runat="server" ControlToValidate="ddNationality" ErrorMessage="Please select nationality">*</asp:CustomValidator></TD>
													<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
													<TD class="menubar" vAlign="top" width="320"><STRONG>Nationality 2:</STRONG></TD>
												</TR>
												<TR>
													<TD vAlign="top" width="320"><STRONG>
															<asp:DropDownList id="ddNationality" runat="server" Width="100%" CssClass="textbox" AutoPostBack="True">
																<asp:ListItem Value="0">Select--Nationality</asp:ListItem>
															</asp:DropDownList></STRONG></TD>
													<TD vAlign="top" width="95"><STRONG></STRONG></TD>
													<TD vAlign="top" width="320"><STRONG>
															<asp:DropDownList id="ddNationality2" runat="server" Width="100%" CssClass="textbox" AutoPostBack="True"
																Enabled="False"></asp:DropDownList></STRONG></TD>
												</TR>
												<TR>
													<TD class="menubar" vAlign="top" width="320"><STRONG>Passport #:</STRONG>
														<asp:RequiredFieldValidator id="RequiredFieldValidator6" runat="server" ControlToValidate="txtPassport" ErrorMessage="Please enter passportno"
															Enabled="False">*</asp:RequiredFieldValidator></TD>
													<TD class="menubar" vAlign="top" width="95"><STRONG><STRONG></STRONG></STRONG></TD>
													<TD class="menubar" vAlign="top" width="320"><STRONG>Next Of Kin:</STRONG></TD>
												</TR>
												<TR>
													<TD vAlign="top" width="320"><STRONG><STRONG>
																<asp:TextBox id="txtPassport" runat="server" Width="100%" CssClass="TextBox" MaxLength="100"></asp:TextBox></STRONG></STRONG></TD>
													<TD vAlign="top" width="95"><STRONG><STRONG></STRONG></STRONG></TD>
													<TD vAlign="top" width="320"><STRONG><STRONG>
																<asp:TextBox id="txtKin" runat="server" Width="100%" CssClass="TextBox" MaxLength="1000"></asp:TextBox></STRONG></STRONG></TD>
												</TR>
												<TR>
													<TD id="row1" class="menubar" vAlign="top" width="320" runat="server"><STRONG><STRONG><FONT size="2"><STRONG><FONT size="2">*</FONT></STRONG>&nbsp;</FONT></STRONG>NIC 
															#(New):</STRONG>
														<asp:RequiredFieldValidator id="RequiredFieldValidator5" runat="server" ControlToValidate="txtNewNic" ErrorMessage="Please enter your New NIC"
															Enabled="False">*</asp:RequiredFieldValidator>
														<asp:RegularExpressionValidator id="RegularExpressionValidator1" runat="server" ControlToValidate="txtNewNic" ErrorMessage="Please enter your  New NIC in correct format"
															ValidationExpression="^\d{5}[-]\d{7}[-]\d{1}$">*</asp:RegularExpressionValidator></TD>
													<TD class="menubar" vAlign="top" width="95"><STRONG><STRONG></STRONG></STRONG></TD>
													<TD class="menubar" vAlign="top" width="320">
														<DIV id="row3" runat="server"><STRONG>NIC #(Old):</STRONG>
															<asp:RegularExpressionValidator id="RegularExpressionValidator2" runat="server" ControlToValidate="txtOldNic" ErrorMessage="Please enter NIC in correct format<br>example:123-12-123456"
																ValidationExpression="^\d{3}[-]\d{2}[-]\d{6}$">*</asp:RegularExpressionValidator></DIV>
													</TD>
												</TR>
												<TR>
													<TD id="row2" vAlign="top" width="320" runat="server"><STRONG>
															<ew:MaskedTextBox id="txtNewNic" runat="server" Width="100%" CssClass="textbox" Mask="99999-9999999-9"></ew:MaskedTextBox></STRONG></TD>
													<TD vAlign="top" width="95"><STRONG></STRONG></TD>
													<TD id="row4" vAlign="top" width="320" runat="server"><STRONG>
															<asp:TextBox id="txtOldNic" runat="server" Width="100%" CssClass="TextBox"></asp:TextBox></STRONG></TD>
												</TR>
												<TR>
													<TD class="menubar" vAlign="top" width="320"><STRONG>Bank Account No:</STRONG></TD>
													<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
													<TD class="menubar" vAlign="top" width="320"><STRONG>Account Details:</STRONG></TD>
												</TR>
												<TR>
													<TD vAlign="top" width="320">
														<asp:TextBox id="txtBankAcctNo" runat="server" Width="100%" CssClass="TextBox" MaxLength="100"></asp:TextBox></TD>
													<TD vAlign="top" width="95"></TD>
													<TD vAlign="top" width="320">
														<asp:TextBox id="txtAccountDetails" runat="server" Width="100%" CssClass="TextBox" MaxLength="1000"
															TextMode="MultiLine"></asp:TextBox></TD>
												</TR>
												<TR>
													<TD class="menubar" vAlign="top" width="320"><STRONG>Picture Upload </STRONG>
													</TD>
													<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
													<TD class="menubar" vAlign="top" width="320"><STRONG>Compensatory Off :</STRONG></TD>
												</TR>
												<TR>
													<TD vAlign="top" width="320"><INPUT onbeforeeditfocus="return false" style="WIDTH: 240px; HEIGHT: 16px" id="FileToUpload"
															oncontextmenu="return false" class="TextBox" onkeypress="return false" type="file" name="FileToUpload" runat="server"></TD>
													<TD vAlign="top" width="95"></TD>
													<TD vAlign="top" width="320">
														<asp:DropDownList id="rdCompansatory" runat="server" Width="100%" CssClass="textbox" Enabled="False">
															<asp:ListItem Value="-1">Select--Compensatory</asp:ListItem>
															<asp:ListItem Value="1">Yes</asp:ListItem>
															<asp:ListItem Value="0">No</asp:ListItem>
														</asp:DropDownList></TD>
												</TR>
												<TR>
													<TD id="rowNIC" vAlign="top" colSpan="3" runat="server">
														<asp:DataGrid id="dgNIC" runat="server" Width="100%" Visible="False" BorderStyle="Solid" Border
															AutoGenerateColumns="False" GridLines="Horizontal" CellPadding="3" BorderWidth="1px" BorderColor="Navy">
															<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
															<AlternatingItemStyle CssClass="alternetitem"></AlternatingItemStyle>
															<ItemStyle CssClass="item"></ItemStyle>
															<HeaderStyle Font-Bold="True" ForeColor="Black" CssClass="header"></HeaderStyle>
															<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
															<Columns>
																<asp:BoundColumn DataField="pcode" HeaderText="PCode"></asp:BoundColumn>
																<asp:BoundColumn DataField="name" HeaderText="Name"></asp:BoundColumn>
																<asp:BoundColumn DataField="designation" HeaderText="Designation"></asp:BoundColumn>
																<asp:BoundColumn DataField="department" HeaderText="Department"></asp:BoundColumn>
																<asp:BoundColumn DataField="active" HeaderText="Status"></asp:BoundColumn>
																<asp:TemplateColumn>
																	<ItemTemplate>
																		<asp:Button id="btnNIC" onclick="Proceed" runat="server" CssClass="button" Text="Select"></asp:Button>
																	</ItemTemplate>
																</asp:TemplateColumn>
																<asp:BoundColumn Visible="False" DataField="del"></asp:BoundColumn>
															</Columns>
															<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
														</asp:DataGrid></TD>
												</TR>
												<TR>
													<TD vAlign="top" colSpan="3">
														<asp:Label id="lblEmp" runat="server" Font-Bold="True" ForeColor="DarkOrange"></asp:Label></TD>
												</TR>
												<TR>
													<TD vAlign="top" colSpan="3">
														<asp:Button id="btnUpdate" runat="server" Width="70px" Text="Update"></asp:Button>
														<asp:Button id="btnDelete" runat="server" CausesValidation="False" Text="Exit Employee"></asp:Button>
														<asp:Button id="btnHold" runat="server" Text="Hold Employee"></asp:Button>
														<asp:Button id="btnReactive" runat="server" Text="Re-Activate Employee"></asp:Button></TD>
												</TR>
												<TR>
													<TD vAlign="top" colSpan="3">
														<asp:label id="lblVacancy" runat="server" Font-Bold="True" Visible="False" ForeColor="DarkOrange">The Record Has Been Successfully Updated </asp:label>
														<asp:Label id="lblStop" runat="server" Font-Bold="True" ForeColor="DarkOrange"></asp:Label></TD>
												</TR>
											</TABLE>
											<BR>
											<BR>
											<asp:Panel id="pnlDel" runat="server" Width="100%" BorderStyle="Double">
												<EM><STRONG>Employee&nbsp;Status </STRONG>
													<BR>
												</EM>
												<asp:Label id="lbleStatus" runat="server"></asp:Label>
												<BR>
												<ew:CalendarPopup id="DateOfExit" runat="server" Nullable="True">
													<TextboxLabelStyle CssClass="textbox"></TextboxLabelStyle>
													<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="White"></WeekdayStyle>
													<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="Yellow"></MonthHeaderStyle>
													<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
														BackColor="AntiqueWhite"></OffMonthStyle>
													<ButtonStyle CssClass="button"></ButtonStyle>
													<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="White"></GoToTodayStyle>
													<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="LightGoldenrodYellow"></TodayDayStyle>
													<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="Orange"></DayHeaderStyle>
													<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="LightGray"></WeekendStyle>
													<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="Yellow"></SelectedDateStyle>
													<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="White"></ClearDateStyle>
													<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
														BackColor="White"></HolidayStyle>
												</ew:CalendarPopup>
												<asp:RequiredFieldValidator id="RequiredFieldValidator10" runat="server" ControlToValidate="DateOfExit" ErrorMessage="Please enter date of exit">*</asp:RequiredFieldValidator>
												<BR>
												<asp:Button id="btnDelEmp" runat="server" CausesValidation="False" Font-Bold="True" Text="Delete"></asp:Button>
												<asp:Button id="btnCancelEmp" runat="server" CausesValidation="False" Font-Bold="True" Text="Cancel"></asp:Button>
											</asp:Panel>
											<BR>
											<asp:TextBox style="VISIBILITY: hidden" id="txtServerDate" runat="server"></asp:TextBox>
										</asp:Panel>
									</asp:panel><asp:panel id="pnlEmployeeInfo" runat="server" Width="100%">
										<TABLE id="Table4" class="MainFormColor" border="0" cellSpacing="0" cellPadding="3" width="100%"
											align="center">
											<TR>
												<TD class="OrangeFormTitle" colSpan="3">Employment Information</TD>
											</TR>
											<TR>
												<TD style="WIDTH: 328px" class="menubar" vAlign="top" width="328"><STRONG>Employee 
														Code:</STRONG></TD>
												<TD class="menubar" vAlign="top" width="95"></TD>
												<TD class="menubar" vAlign="top" width="320"><STRONG><FONT size="2">*</FONT></STRONG>&nbsp;<STRONG>Name
														<asp:RequiredFieldValidator id="RequiredFieldValidator7" runat="server" ControlToValidate="txtEmpInfoName" ErrorMessage="Enter Name">*</asp:RequiredFieldValidator>
														<asp:RegularExpressionValidator id="RegularExpressionValidator3" runat="server" ControlToValidate="txtEmpInfoName"
															ErrorMessage="Enter Only Alphabet In Name" ValidationExpression="^[a-z A-Z.-]+[(a-z A-Z)]*$">*</asp:RegularExpressionValidator></STRONG></TD>
											</TR>
											<TR>
												<TD style="WIDTH: 328px" vAlign="top" width="328">
													<asp:TextBox id="txtEmpInfoCode" runat="server" Width="100%" CssClass="TextBox" ReadOnly="True"></asp:TextBox></TD>
												<TD vAlign="top" width="95"></TD>
												<TD vAlign="top" width="320">
													<asp:TextBox id="txtEmpInfoName" runat="server" Width="100%" CssClass="TextBox" MaxLength="1000"></asp:TextBox></TD>
											</TR>
											<TR>
												<TD style="WIDTH: 328px" class="menubar" vAlign="top" width="328" colSpan="3"><STRONG><FONT size="2">*</FONT>
														Network
														<asp:RequiredFieldValidator id="RequiredFieldValidator20" runat="server" ControlToValidate="txtEmpInfoName"
															ErrorMessage="Please Select Appropriate Network" InitialValue="0">*</asp:RequiredFieldValidator></STRONG></TD>
											</TR>
											<TR>
												<TD style="WIDTH: 328px; HEIGHT: 14px" vAlign="top" width="328" colSpan="3">
													<asp:DropDownList id="ddNetwork" runat="server" Width="325px" CssClass="textbox" AutoPostBack="True">
														<asp:ListItem Value="0">Select--Network</asp:ListItem>
													</asp:DropDownList></TD>
											</TR>
											<TR>
												<TD style="WIDTH: 328px" class="menubar" vAlign="top" width="328"><STRONG><FONT size="2">*</FONT></STRONG>&nbsp;<STRONG>Department:
														<asp:CustomValidator id="CustomValidator4" runat="server" ControlToValidate="ddDepartment" ErrorMessage="Please select department"
															OnServerValidate="TextValidate">*</asp:CustomValidator></STRONG></TD>
												<TD class="menubar" vAlign="top" width="95"></TD>
												<TD class="menubar" vAlign="top" width="320"><STRONG><FONT size="2">*</FONT></STRONG>&nbsp;<STRONG>Designation:
														<asp:CustomValidator id="CustomValidator2" runat="server" ControlToValidate="ddDesignation" ErrorMessage="Please Select Designation"
															Display="Dynamic">*</asp:CustomValidator>
														<asp:Label id="lblFunctionalDesignation" runat="server"></asp:Label></STRONG></TD>
											</TR>
											<TR>
												<TD style="WIDTH: 328px" vAlign="top" width="328">
													<asp:DropDownList id="ddDepartment" runat="server" Width="100%" CssClass="textbox" AutoPostBack="True">
														<asp:ListItem Value="0">Select--Department</asp:ListItem>
													</asp:DropDownList></TD>
												<TD vAlign="top" width="95"></TD>
												<TD vAlign="top" width="320">
													<asp:DropDownList id="ddDesignation" runat="server" Width="100%" CssClass="textbox" AutoPostBack="True">
														<asp:ListItem Value="0">Select--Designation</asp:ListItem>
													</asp:DropDownList></TD>
											</TR>
											<TR>
												<TD style="WIDTH: 328px" class="menubar" vAlign="top" width="328"><STRONG><STRONG><FONT size="2">*
															</FONT><FONT size="1">Functional Designation (Vacant&nbsp;Position(s)-&gt;
																<asp:Label id="lblFV" runat="server"></asp:Label>)&nbsp;</FONT></STRONG></STRONG></TD>
												<TD class="menubar" vAlign="top" width="95"></TD>
												<TD class="menubar" vAlign="top" width="320"><FONT size="2"><STRONG>*</STRONG></FONT>&nbsp;<STRONG>Station:
													</STRONG>
													<asp:CustomValidator id="CustomValidator3" runat="server" ControlToValidate="ddOpertingCity" ErrorMessage="Please select your city">*</asp:CustomValidator></TD>
											</TR>
											<TR>
												<TD style="WIDTH: 328px" vAlign="top" width="328">
													<asp:DropDownList id="ddFDesignation" runat="server" Width="100%" CssClass="textbox" AutoPostBack="True">
														<asp:ListItem Value="0">Select--Functional Designation</asp:ListItem>
													</asp:DropDownList></TD>
												<TD vAlign="top" width="95"></TD>
												<TD vAlign="top" width="320">
													<asp:DropDownList id="ddOpertingCity" runat="server" Width="100%" CssClass="textbox">
														<asp:ListItem Value="0">Select--Operating Station</asp:ListItem>
													</asp:DropDownList></TD>
											</TR>
											<TR>
												<TD style="WIDTH: 328px" class="menubar" vAlign="top" width="328"><STRONG>Employment 
														Status:(IF Employee is To Be Joined THEN Must Give His/Her Tentative Date of 
														Join)</STRONG></TD>
												<TD class="menubar" vAlign="top" width="95"></TD>
												<TD class="menubar" vAlign="top" width="320"><STRONG>Tentative Date of Join: </STRONG>
													<asp:RequiredFieldValidator id="RequiredFieldValidator17" runat="server" ControlToValidate="CTDOJ" ErrorMessage="Please Select Tentative Date of Joining"
														Display="Dynamic">*</asp:RequiredFieldValidator></TD>
											</TR>
											<TR>
												<TD style="WIDTH: 328px" vAlign="top" width="328">
													<asp:DropDownList id="ddeStatus" runat="server" Width="160px" CssClass="textbox" AutoPostBack="True">
														<asp:ListItem Value="1" Selected="True">Expected To Join</asp:ListItem>
														<asp:ListItem Value="0">Serving</asp:ListItem>
													</asp:DropDownList></TD>
												<TD vAlign="top" width="95"></TD>
												<TD vAlign="top" width="320">
													<ew:CalendarPopup id="CTDOJ" runat="server" Width="208px" EnableHideDropDown="True" Nullable="True"
														AutoPostBack="True">
														<TextboxLabelStyle CssClass="TextBox"></TextboxLabelStyle>
														<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></WeekdayStyle>
														<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Yellow"></MonthHeaderStyle>
														<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
															BackColor="AntiqueWhite"></OffMonthStyle>
														<ButtonStyle CssClass="Button"></ButtonStyle>
														<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></GoToTodayStyle>
														<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="LightGoldenrodYellow"></TodayDayStyle>
														<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Orange"></DayHeaderStyle>
														<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="LightGray"></WeekendStyle>
														<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Yellow"></SelectedDateStyle>
														<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></ClearDateStyle>
														<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></HolidayStyle>
													</ew:CalendarPopup></TD>
											</TR>
											<TR>
												<TD style="WIDTH: 328px" class="menubar" vAlign="top" width="328">
													<asp:Label id="Label7" runat="server" Font-Names="Courier New" Font-Bold="True" Font-Size="Small">*</asp:Label><STRONG>Date 
														Of Joining:
														<asp:RequiredFieldValidator id="RequiredFieldValidator8" runat="server" ControlToValidate="CalenderDOJ" ErrorMessage="Please Select Date oj Joining">*</asp:RequiredFieldValidator></STRONG></TD>
												<TD class="menubar" vAlign="top" width="95"></TD>
												<TD class="menubar" vAlign="top" width="320"><STRONG><FONT size="2">* </FONT>Probabtion/Confirmation 
														Period (in Months)</STRONG></TD>
											</TR>
											<TR>
												<TD style="WIDTH: 328px" vAlign="top" width="328">
													<ew:CalendarPopup id="CalenderDOJ" runat="server" Width="194px" EnableHideDropDown="True" Nullable="True"
														AutoPostBack="True" AllowArbitraryText="False">
														<TextboxLabelStyle CssClass="textbox"></TextboxLabelStyle>
														<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></WeekdayStyle>
														<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Yellow"></MonthHeaderStyle>
														<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
															BackColor="AntiqueWhite"></OffMonthStyle>
														<ButtonStyle CssClass="button"></ButtonStyle>
														<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></GoToTodayStyle>
														<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="LightGoldenrodYellow"></TodayDayStyle>
														<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Orange"></DayHeaderStyle>
														<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="LightGray"></WeekendStyle>
														<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Yellow"></SelectedDateStyle>
														<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></ClearDateStyle>
														<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></HolidayStyle>
													</ew:CalendarPopup></TD>
												<TD vAlign="top" width="95"></TD>
												<TD vAlign="top" width="320">
													<asp:DropDownList id="ddProbabtion" runat="server" Width="328px" CssClass="textbox" AutoPostBack="True">
														<asp:ListItem Value="-1">Select--Probabtion Period</asp:ListItem>
														<asp:ListItem Value="0">0</asp:ListItem>
														<asp:ListItem Value="3">3</asp:ListItem>
														<asp:ListItem Value="6">6</asp:ListItem>
														<asp:ListItem Value="12">12</asp:ListItem>
														<asp:ListItem Value="-2">Other</asp:ListItem>
													</asp:DropDownList><BR>
													<asp:TextBox id="txtProbabtionOther" runat="server" Width="96px" Visible="False" CssClass="TextBox"
														MaxLength="50" AutoPostBack="True"></asp:TextBox></TD>
											</TR>
											<TR>
												<TD style="WIDTH: 328px" class="menubar" vAlign="top" width="328"><STRONG>
														<asp:Label id="Label2" runat="server" Font-Names="Courier New" Font-Bold="True" Font-Size="Small">*</asp:Label>Confirmation 
														Due Date:</STRONG> <STRONG>
														<asp:RequiredFieldValidator id="RequiredFieldValidator9" runat="server" ControlToValidate="CalenderCOD" ErrorMessage="Please Select Due Date Of Confirmation">*</asp:RequiredFieldValidator></STRONG></TD>
												<TD class="menubar" vAlign="top" width="95"></TD>
												<TD class="menubar" vAlign="top" width="320"><STRONG>Confirmation Date:
														<asp:CompareValidator id="CompareValidator1" runat="server" ControlToValidate="calenderFinalConfirmation"
															ErrorMessage="Confirmation date must be greater than or equal to date of join" Operator="GreaterThanEqual"
															ControlToCompare="CalenderDOJ" Type="Date">*</asp:CompareValidator></STRONG></TD>
											</TR>
											<TR>
												<TD style="WIDTH: 328px" vAlign="top" width="328">
													<ew:CalendarPopup id="CalenderCOD" runat="server" Width="200px" EnableHideDropDown="True" Nullable="True"
														Enabled="False">
														<TextboxLabelStyle CssClass="textbox"></TextboxLabelStyle>
														<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></WeekdayStyle>
														<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Yellow"></MonthHeaderStyle>
														<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
															BackColor="AntiqueWhite"></OffMonthStyle>
														<ButtonStyle CssClass="button"></ButtonStyle>
														<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></GoToTodayStyle>
														<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="LightGoldenrodYellow"></TodayDayStyle>
														<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Orange"></DayHeaderStyle>
														<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="LightGray"></WeekendStyle>
														<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Yellow"></SelectedDateStyle>
														<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></ClearDateStyle>
														<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></HolidayStyle>
													</ew:CalendarPopup></TD>
												<TD vAlign="top" width="95"></TD>
												<TD vAlign="top" width="320">
													<ew:CalendarPopup id="calenderFinalConfirmation" runat="server" Width="200px" EnableHideDropDown="True"
														Nullable="True" CellPadding="50px" Text="... " CalendarLocation="Top" CellSpacing="10px" DisplayOffsetY="-70">
														<TextboxLabelStyle CssClass="textbox"></TextboxLabelStyle>
														<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></WeekdayStyle>
														<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Yellow"></MonthHeaderStyle>
														<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
															BackColor="AntiqueWhite"></OffMonthStyle>
														<ButtonStyle CssClass="button"></ButtonStyle>
														<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></GoToTodayStyle>
														<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="LightGoldenrodYellow"></TodayDayStyle>
														<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Orange"></DayHeaderStyle>
														<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="LightGray"></WeekendStyle>
														<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Yellow"></SelectedDateStyle>
														<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></ClearDateStyle>
														<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></HolidayStyle>
													</ew:CalendarPopup></TD>
											</TR>
											<TR>
												<TD style="WIDTH: 328px" class="menubar" vAlign="top" width="328"><STRONG><FONT size="2">*</FONT></STRONG>&nbsp;<STRONG>Employment 
														Type:
														<asp:CustomValidator id="CustomValidator5" runat="server" ControlToValidate="txtEmpInfoTOE" ErrorMessage="Please select Employee Type">*</asp:CustomValidator></STRONG></TD>
												<TD class="menubar" vAlign="top" width="95"></TD>
												<TD class="menubar" vAlign="top" width="320"><STRONG>Email Official:
														<asp:RegularExpressionValidator id="RegularExpressionValidator4" runat="server" ControlToValidate="txtEmpInfoEmail"
															ErrorMessage="Please enter email in correct format<br>example:<EMAIL>" ValidationExpression="\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*">*</asp:RegularExpressionValidator></STRONG></TD>
											</TR>
											<TR>
												<TD style="WIDTH: 328px" vAlign="top" width="328">
													<asp:DropDownList id="txtEmpInfoTOE" runat="server" Width="100%" CssClass="textbox">
														<asp:ListItem Value="0">Select--Employment</asp:ListItem>
														<asp:ListItem Value="1">Permanent</asp:ListItem>
														<asp:ListItem Value="2">Contractual</asp:ListItem>
														<asp:ListItem Value="3">Retainer</asp:ListItem>
														<asp:ListItem Value="4">Honorary</asp:ListItem>
														<asp:ListItem Value="5">Talent</asp:ListItem>
													</asp:DropDownList></TD>
												<TD vAlign="top" width="95"></TD>
												<TD vAlign="top" width="320">
													<asp:TextBox id="txtEmpInfoEmail" runat="server" Width="100%" CssClass="TextBox" MaxLength="1000"></asp:TextBox></TD>
											</TR>
											<TR>
												<TD style="WIDTH: 328px" class="menubar" vAlign="top" width="328"><STRONG>EOBI #: </STRONG>
												</TD>
												<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
												<TD class="menubar" vAlign="top" width="320"><STRONG>SESSI #:</STRONG></TD>
											</TR>
											<TR>
												<TD style="WIDTH: 328px" vAlign="top">
													<asp:TextBox id="txtEmpInfoEOBI" runat="server" Width="100%" CssClass="TextBox" MaxLength="50"></asp:TextBox></TD>
												<TD vAlign="top" width="95"></TD>
												<TD vAlign="top" width="320">
													<asp:TextBox id="txtEmpInfoSESSI" runat="server" Width="100%" CssClass="TextBox" MaxLength="50"></asp:TextBox></TD>
											</TR>
											<TR>
												<TD style="WIDTH: 328px" class="menubar" vAlign="top" width="328"><STRONG>Employee 
														Working&nbsp;Location:</STRONG></TD>
												<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
												<TD class="menubar" vAlign="top" width="320"><STRONG>Insurance Code:</STRONG></TD>
											</TR>
											<TR>
												<TD style="WIDTH: 328px" vAlign="top" width="328">
													<asp:TextBox id="txtEmpLocation" runat="server" Width="100%" CssClass="textbox" TextMode="MultiLine"></asp:TextBox></TD>
												<TD vAlign="top" width="95"></TD>
												<TD vAlign="top" width="320">
													<asp:TextBox id="txtEmpInfoInsurance" runat="server" Width="100%" CssClass="TextBox" MaxLength="50"></asp:TextBox></TD>
											</TR>
											<TR>
												<TD style="WIDTH: 328px" class="menubar" vAlign="top" width="335"><STRONG>New Joining:</STRONG></TD>
												<TD class="menubar" vAlign="top" width="95"></TD>
												<TD class="menubar" vAlign="top" width="320"><STRONG>Extension #: </STRONG>
													<asp:RegularExpressionValidator id="RegularExpressionValidator5" runat="server" ControlToValidate="txtEmpInfoExtension"
														ErrorMessage="Enter Only Numeric Values In Extension" ValidationExpression="^[0-9 ,]*$">*</asp:RegularExpressionValidator></TD>
											</TR>
											<TR>
												<TD style="WIDTH: 328px" vAlign="top" width="335">
													<asp:DropDownList id="ddNewJoiner" runat="server" Width="80px" CssClass="textbox">
														<asp:ListItem Value="0" Selected="True">No</asp:ListItem>
														<asp:ListItem Value="1">Yes</asp:ListItem>
													</asp:DropDownList></TD>
												<TD vAlign="top" width="95"></TD>
												<TD vAlign="top" width="320">
													<asp:TextBox id="txtEmpInfoExtension" runat="server" Width="100%" CssClass="TextBox" MaxLength="50"></asp:TextBox></TD>
											</TR>
											<TR>
												<TD style="WIDTH: 328px" vAlign="top" width="335">
													<ew:CalendarPopup id="InterimCommitment" runat="server" Visible="False" Nullable="True">
														<TextboxLabelStyle CssClass="textbox"></TextboxLabelStyle>
														<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></WeekdayStyle>
														<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Yellow"></MonthHeaderStyle>
														<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
															BackColor="AntiqueWhite"></OffMonthStyle>
														<ButtonStyle CssClass="button"></ButtonStyle>
														<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></GoToTodayStyle>
														<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="LightGoldenrodYellow"></TodayDayStyle>
														<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Orange"></DayHeaderStyle>
														<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="LightGray"></WeekendStyle>
														<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Yellow"></SelectedDateStyle>
														<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></ClearDateStyle>
														<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></HolidayStyle>
													</ew:CalendarPopup></TD>
												<TD vAlign="top" width="95"></TD>
												<TD vAlign="top" width="320">
													<asp:DropDownList id="ddNICcity" runat="server" Width="100%" Visible="False" CssClass="textbox">
														<asp:ListItem Value="0">Select--Reporting City</asp:ListItem>
													</asp:DropDownList></TD>
											</TR>
										</TABLE>
									</asp:panel><asp:panel id="pnlFamilyInfo" runat="server" Width="100%" Visible="False">
										<TABLE id="Table6" class="MainFormColor" border="0" cellSpacing="0" cellPadding="3" width="100%"
											align="center">
											<TR>
												<TD class="OrangeFormTitle" colSpan="3">Family Information</TD>
											</TR>
											<TR>
												<TD vAlign="top" colSpan="3">
													<asp:DataGrid id="DataGrid1" runat="server" Width="750px" Visible="False" BorderStyle="Solid"
														AutoGenerateColumns="False" GridLines="Horizontal" CellPadding="3" BorderWidth="1px" BorderColor="Navy">
														<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
														<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
														<AlternatingItemStyle CssClass="alternetitem"></AlternatingItemStyle>
														<ItemStyle CssClass="item"></ItemStyle>
														<HeaderStyle Font-Bold="True" ForeColor="Black" CssClass="header"></HeaderStyle>
														<Columns>
															<asp:BoundColumn DataField="name" HeaderText="Name"></asp:BoundColumn>
															<asp:BoundColumn DataField="relationship" HeaderText="RelationShip"></asp:BoundColumn>
															<asp:BoundColumn DataField="maritialstatus" HeaderText="MaritialStatus"></asp:BoundColumn>
															<asp:BoundColumn Visible="False" DataField="gender" HeaderText="Gender"></asp:BoundColumn>
															<asp:BoundColumn Visible="False" DataField="occupation" HeaderText="Occupation"></asp:BoundColumn>
															<asp:BoundColumn Visible="False" DataField="DOB" HeaderText="Date Of Birth" DataFormatString="{0:d}"></asp:BoundColumn>
															<asp:BoundColumn DataField="dependent" HeaderText="Dependent"></asp:BoundColumn>
															<asp:ButtonColumn Text="Edit" CommandName="Select"></asp:ButtonColumn>
															<asp:BoundColumn Visible="False" DataField="pcode"></asp:BoundColumn>
															<asp:BoundColumn Visible="False" DataField="fdid"></asp:BoundColumn>
															<asp:TemplateColumn>
																<ItemTemplate>
																	<asp:LinkButton id="delFamily" runat="server" CausesValidation="False" OnClick="dFamily">Delete</asp:LinkButton>
																</ItemTemplate>
															</asp:TemplateColumn>
														</Columns>
														<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
													</asp:DataGrid>
													<asp:Label id="lblRecord" runat="server" Width="170px" Font-Bold="True">No Record Found</asp:Label></TD>
											</TR>
											<TR>
												<TD vAlign="top" colSpan="3">
													<asp:Panel id="Panel2" runat="server" Width="100%">
														<TABLE id="Table10" class="MainFormColor" border="0" cellSpacing="0" cellPadding="3" width="100%"
															align="center">
															<TR>
																<TD class="OrangeFormTitle" colSpan="3">Add Family Information:</TD>
															</TR>
															<TR>
																<TD class="menubar" vAlign="top" width="320"><STRONG>Name Of Relative: </STRONG>
																	<asp:RequiredFieldValidator id="RequiredFieldValidator13" runat="server" ControlToValidate="txtFamilyName" ErrorMessage="Please enter name of relative"
																		Enabled="False">*</asp:RequiredFieldValidator></TD>
																<TD class="menubar" vAlign="top" width="95"></TD>
																<TD class="menubar" vAlign="top" width="320"><STRONG>Data Of Birth:</STRONG></TD>
															</TR>
															<TR>
																<TD vAlign="top" width="320">
																	<asp:TextBox id="txtFamilyName" runat="server" Width="100%" CssClass="TextBox" MaxLength="1000"></asp:TextBox></TD>
																<TD vAlign="top" width="95"></TD>
																<TD vAlign="top" width="320">
																	<ew:CalendarPopup id="CalendarPopup1" runat="server" Width="180px" EnableHideDropDown="True" SelectedDate="2007-04-24">
																		<TextboxLabelStyle CssClass="textbox"></TextboxLabelStyle>
																		<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="White"></WeekdayStyle>
																		<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="Yellow"></MonthHeaderStyle>
																		<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																			BackColor="AntiqueWhite"></OffMonthStyle>
																		<ButtonStyle CssClass="button"></ButtonStyle>
																		<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="White"></GoToTodayStyle>
																		<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="LightGoldenrodYellow"></TodayDayStyle>
																		<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="Orange"></DayHeaderStyle>
																		<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="LightGray"></WeekendStyle>
																		<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="Yellow"></SelectedDateStyle>
																		<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="White"></ClearDateStyle>
																		<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="White"></HolidayStyle>
																	</ew:CalendarPopup></TD>
															</TR>
															<TR>
																<TD class="menubar" vAlign="top" width="320"><STRONG>Relationship: </STRONG>
																	<asp:CustomValidator id="CustomValidator8" runat="server" ControlToValidate="rdRelation" ErrorMessage="Please select relationship"
																		Enabled="False">*</asp:CustomValidator></TD>
																<TD class="menubar" vAlign="top" width="95"></TD>
																<TD class="menubar" vAlign="top" width="320"><STRONG>Marital Status: </STRONG>
																	<asp:CustomValidator id="CustomValidator6" runat="server" ControlToValidate="rdStatus" ErrorMessage="Please select maritial status"
																		Enabled="False">*</asp:CustomValidator></TD>
															</TR>
															<TR>
																<TD vAlign="top" width="320">
																	<asp:DropDownList id="rdRelation" runat="server" Width="100%" CssClass="textbox">
																		<asp:ListItem Value="0">Select--Relation</asp:ListItem>
																		<asp:ListItem Value="2">Husband</asp:ListItem>
																		<asp:ListItem Value="3">Wife</asp:ListItem>
																		<asp:ListItem Value="4">Son</asp:ListItem>
																		<asp:ListItem Value="5">Daughter</asp:ListItem>
																	</asp:DropDownList></TD>
																<TD vAlign="top" width="95"></TD>
																<TD vAlign="top" width="320">
																	<asp:DropDownList id="rdStatus" runat="server" Width="100%" CssClass="textbox">
																		<asp:ListItem Value="0">Select--Marital Status</asp:ListItem>
																		<asp:ListItem Value="1">Single</asp:ListItem>
																		<asp:ListItem Value="2">Married</asp:ListItem>
																		<asp:ListItem Value="3">Divorced</asp:ListItem>
																		<asp:ListItem Value="4">Widow</asp:ListItem>
																		<asp:ListItem Value="5">Separated</asp:ListItem>
																	</asp:DropDownList></TD>
															</TR>
															<TR>
																<TD class="menubar" vAlign="top" width="320"><STRONG>Dependent:</STRONG></TD>
																<TD class="menubar" vAlign="top" width="95"></TD>
																<TD class="menubar" vAlign="top" width="320"><STRONG>Gender: </STRONG>
																	<asp:CustomValidator id="CustomValidator7" runat="server" ControlToValidate="rdFamilyGender" ErrorMessage="Please select gender"
																		Enabled="False">*</asp:CustomValidator></TD>
															</TR>
															<TR>
																<TD vAlign="top" width="320">
																	<asp:DropDownList id="ddDependent" runat="server" Width="100%" CssClass="textbox">
																		<asp:ListItem Value="0">Select--Option</asp:ListItem>
																		<asp:ListItem Value="1">Yes</asp:ListItem>
																		<asp:ListItem Value="2">No</asp:ListItem>
																	</asp:DropDownList></TD>
																<TD vAlign="top" width="95"></TD>
																<TD vAlign="top" width="320">
																	<asp:DropDownList id="rdFamilyGender" runat="server" Width="100%" CssClass="textbox">
																		<asp:ListItem Value="0">Select--Gender</asp:ListItem>
																		<asp:ListItem Value="1">Male</asp:ListItem>
																		<asp:ListItem Value="2">Female</asp:ListItem>
																	</asp:DropDownList></TD>
															</TR>
															<TR>
																<TD class="menubar" vAlign="top" width="320"><STRONG>Occupation Of Spouse:</STRONG></TD>
																<TD class="menubar" vAlign="top" width="95"></TD>
																<TD class="menubar" vAlign="top" width="320"></TD>
															</TR>
															<TR>
																<TD vAlign="top" width="320">
																	<asp:TextBox id="txtOccupation" runat="server" Width="100%" CssClass="textbox" MaxLength="1000"></asp:TextBox></TD>
																<TD vAlign="top" width="95"></TD>
																<TD vAlign="top" width="320"></TD>
															</TR>
															<TR>
																<TD vAlign="top" width="320">
																	<asp:Button id="btnNew" runat="server" Width="70px" Text="Add New"></asp:Button>
																	<asp:Button id="btnFupdate" runat="server" Width="70px" Enabled="False" Text="Update"></asp:Button></TD>
																<TD vAlign="top" width="95"></TD>
																<TD vAlign="top" width="320"></TD>
															</TR>
														</TABLE>
														<BR>
													</asp:Panel></TD>
											</TR>
										</TABLE>
										<BR>
									</asp:panel><asp:panel id="pnlEducation" runat="server" Width="100%" Visible="False">
										<TABLE id="Table2" class="MainFormColor" border="0" cellSpacing="0" cellPadding="3" width="100%"
											align="center">
											<TR>
												<TD class="OrangeFormTitle" colSpan="3">Educational Information</TD>
											</TR>
											<TR>
												<TD vAlign="top" colSpan="3">
													<asp:datagrid id="DataGrid3" runat="server" Width="750px" Visible="False" BorderStyle="Solid"
														AutoGenerateColumns="False" GridLines="Horizontal" CellPadding="3" BorderWidth="1px" BorderColor="Navy"
														AllowSorting="True">
														<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
														<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
														<AlternatingItemStyle CssClass="alternetitem"></AlternatingItemStyle>
														<ItemStyle CssClass="item"></ItemStyle>
														<HeaderStyle Font-Bold="True" ForeColor="Black" CssClass="header"></HeaderStyle>
														<Columns>
															<asp:BoundColumn DataField="degree" SortExpression="degree" HeaderText="Degree"></asp:BoundColumn>
															<asp:BoundColumn DataField="institute" SortExpression="institute" HeaderText="Institute"></asp:BoundColumn>
															<asp:BoundColumn Visible="False" DataField="durationfrom" SortExpression="durationfrom" HeaderText="Duration From"
																DataFormatString="{0:d}"></asp:BoundColumn>
															<asp:BoundColumn Visible="False" DataField="durationto" SortExpression="durationto" HeaderText="Duration To"
																DataFormatString="{0:d}"></asp:BoundColumn>
															<asp:BoundColumn DataField="result" SortExpression="result" HeaderText="Result"></asp:BoundColumn>
															<asp:BoundColumn DataField="majors" SortExpression="majors" HeaderText="Majors"></asp:BoundColumn>
															<asp:BoundColumn Visible="False" DataField="achievements" SortExpression="achievements" HeaderText="Distinction"></asp:BoundColumn>
															<asp:ButtonColumn Text="Edit" CommandName="Select"></asp:ButtonColumn>
															<asp:BoundColumn Visible="False" DataField="eduinfoid"></asp:BoundColumn>
															<asp:BoundColumn Visible="False" DataField="type"></asp:BoundColumn>
															<asp:TemplateColumn>
																<ItemTemplate>
																	<asp:LinkButton id="delEducation" onclick="dEducation" runat="server" CausesValidation="False">Delete</asp:LinkButton>
																</ItemTemplate>
															</asp:TemplateColumn>
														</Columns>
														<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
													</asp:datagrid>
													<asp:label id="lblEdBoRecord" runat="server" Font-Bold="True">No Record Found</asp:label></TD>
											</TR>
										</TABLE>
										<BR>
										<TABLE id="Table1" class="MainFormColor" border="0" cellSpacing="0" cellPadding="3" width="100%"
											align="center">
											<TR>
												<TD class="OrangeFormTitle" colSpan="3">Add Educational Information:
												</TD>
											</TR>
											<TR>
												<TD class="menubar" vAlign="top" width="320"><STRONG>Degree:</STRONG>
													<asp:CustomValidator id="CustomValidator9" runat="server" ControlToValidate="txtDegree" ErrorMessage="Please select degree"
														Enabled="False">*</asp:CustomValidator>
													<asp:RequiredFieldValidator id="RequiredFieldValidator14" runat="server" ControlToValidate="txtOtherDegree"
														ErrorMessage="Please enter degree" Enabled="False">*</asp:RequiredFieldValidator></TD>
												<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
												<TD class="menubar" vAlign="top" width="320"><STRONG>Institute:</STRONG>
													<asp:CustomValidator id="CustomValidator10" runat="server" ControlToValidate="txtInstitute" ErrorMessage="Please select institute"
														Enabled="False">*</asp:CustomValidator>
													<asp:RequiredFieldValidator id="RequiredFieldValidator15" runat="server" ControlToValidate="txtOtherInstitute"
														ErrorMessage="Please enter institute" Enabled="False">*</asp:RequiredFieldValidator></TD>
											</TR>
											<TR>
												<TD vAlign="top" width="320">
													<asp:dropdownlist id="txtDegree" runat="server" Width="100%" CssClass="textbox" AutoPostBack="True">
														<asp:ListItem Value="0">Select--Degree</asp:ListItem>
													</asp:dropdownlist></TD>
												<TD vAlign="top" width="95"></TD>
												<TD vAlign="top" width="320">
													<asp:dropdownlist id="txtInstitute" runat="server" Width="100%" CssClass="textbox" AutoPostBack="True">
														<asp:ListItem Value="0">Select--Institute</asp:ListItem>
													</asp:dropdownlist></TD>
											</TR>
											<TR>
												<TD vAlign="top" width="320">
													<asp:textbox id="txtOtherDegree" runat="server" Width="100%" Visible="False" CssClass="textbox"></asp:textbox></TD>
												<TD vAlign="top" width="95"></TD>
												<TD vAlign="top" width="320">
													<asp:textbox id="txtOtherInstitute" runat="server" Width="100%" Visible="False" CssClass="textbox"></asp:textbox></TD>
											</TR>
											<TR>
												<TD class="menubar" vAlign="top" width="320"><STRONG>Result:</STRONG>
													<asp:CustomValidator id="CustomValidator11" runat="server" ControlToValidate="txtResult" ErrorMessage="Please select result"
														Enabled="False">*</asp:CustomValidator></TD>
												<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
												<TD class="menubar" vAlign="top" width="320"><STRONG>Majors:</STRONG></TD>
											</TR>
											<TR>
												<TD vAlign="top" width="320">
													<asp:dropdownlist id="txtResult" runat="server" Width="100%" CssClass="textbox">
														<asp:ListItem Value="0">Select--Results</asp:ListItem>
														<asp:ListItem Value="A+ Grade">A+ Grade</asp:ListItem>
														<asp:ListItem Value="A Grade">A Grade</asp:ListItem>
														<asp:ListItem Value="B Grade">B Grade</asp:ListItem>
														<asp:ListItem Value="C Grade">C Grade</asp:ListItem>
														<asp:ListItem Value="D Grade">D Grade</asp:ListItem>
														<asp:ListItem Value="F Grade">F Grade</asp:ListItem>
														<asp:ListItem Value="2.1">2.1</asp:ListItem>
														<asp:ListItem Value="2.2">2.2</asp:ListItem>
														<asp:ListItem Value="2.3">2.3</asp:ListItem>
														<asp:ListItem Value="2.4">2.4</asp:ListItem>
														<asp:ListItem Value="2.5">2.5</asp:ListItem>
														<asp:ListItem Value="2.6">2.6</asp:ListItem>
														<asp:ListItem Value="2.7">2.7</asp:ListItem>
														<asp:ListItem Value="2.8">2.8</asp:ListItem>
														<asp:ListItem Value="2.9">2.9</asp:ListItem>
														<asp:ListItem Value="3.0">3.0</asp:ListItem>
														<asp:ListItem Value="3.1">3.1</asp:ListItem>
														<asp:ListItem Value="3.2">3.2</asp:ListItem>
														<asp:ListItem Value="3.3">3.3</asp:ListItem>
														<asp:ListItem Value="3.4">3.4</asp:ListItem>
														<asp:ListItem Value="3.5">3.5</asp:ListItem>
														<asp:ListItem Value="3.6">3.6</asp:ListItem>
														<asp:ListItem Value="3.7">3.7</asp:ListItem>
														<asp:ListItem Value="3.8">3.8</asp:ListItem>
														<asp:ListItem Value="3.9">3.9</asp:ListItem>
														<asp:ListItem Value="4.0">4.0</asp:ListItem>
														<asp:ListItem Value="Passed">Passed</asp:ListItem>
														<asp:ListItem Value="Fail">Fail</asp:ListItem>
														<asp:ListItem Value="1st Division">1st Division</asp:ListItem>
														<asp:ListItem Value="2nd Division">2nd Division</asp:ListItem>
														<asp:ListItem Value="3rd Division">3rd Division</asp:ListItem>
														<asp:ListItem Value="In Progress">In Progress</asp:ListItem>
													</asp:dropdownlist></TD>
												<TD vAlign="top" width="95"></TD>
												<TD vAlign="top" width="320">
													<asp:textbox id="txtOtherMajors" runat="server" Width="100%" Visible="False" CssClass="textbox"></asp:textbox></TD>
											</TR>
											<TR>
												<TD class="menubar" vAlign="top" width="320"><STRONG>Duration From:
														<asp:CustomValidator id="CustomValidator13" runat="server" ControlToValidate="DurationFrm" ErrorMessage="Please select duration from"
															Display="Dynamic" Enabled="False">*</asp:CustomValidator></STRONG></TD>
												<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
												<TD class="menubar" vAlign="top" width="320"><STRONG>Duration To:
														<asp:CustomValidator id="CustomValidator14" runat="server" ControlToValidate="DurationTo" ErrorMessage="Please select duration to"
															Display="Dynamic" Enabled="False">*</asp:CustomValidator></STRONG></TD>
											</TR>
											<TR>
												<TD vAlign="top" width="320">
													<asp:DropDownList id="DurationFrm" runat="server" Width="100%" CssClass="textbox">
														<asp:ListItem Value="0">Select--Start Date</asp:ListItem>
													</asp:DropDownList></TD>
												<TD vAlign="top" width="95"></TD>
												<TD vAlign="top" width="320">
													<asp:DropDownList id="DurationTo" runat="server" Width="100%" CssClass="textbox">
														<asp:ListItem Value="0">Select--End Date</asp:ListItem>
													</asp:DropDownList></TD>
											</TR>
											<TR>
												<TD class="menubar" vAlign="top" colSpan="3"><STRONG>Distinction: </STRONG>
												</TD>
											</TR>
											<TR>
												<TD vAlign="top" colSpan="3">
													<asp:textbox id="txtAchievements" runat="server" Width="100%" CssClass="TextBox" TextMode="MultiLine"
														Rows="3"></asp:textbox></TD>
											</TR>
											<TR>
												<TD vAlign="top" colSpan="3">
													<asp:button id="btnEdNew" runat="server" Width="70px" Text="Add New"></asp:button>
													<asp:button id="btnEdUpdate" runat="server" Width="70px" Enabled="False" Text="Update"></asp:button>
													<asp:button id="btnECancel" runat="server" CausesValidation="False" Width="70px" Text="Cancel"></asp:button></TD>
											</TR>
										</TABLE>
									</asp:panel><asp:panel id="pnlBondPaper" dir="ltr" runat="server" Visible="False">
										<TABLE id="Table7" class="MainFormColor" border="0" cellSpacing="0" cellPadding="3" width="100%"
											align="center">
											<TR>
												<TD class="OrangeFormTitle" colSpan="3">Employee's Bond Paper Information</TD>
											</TR>
											<TR>
												<TD class="menubar" vAlign="top" width="320"><STRONG>Current Bond: </STRONG>
												</TD>
												<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
												<TD class="menubar" vAlign="top" width="320"><STRONG></STRONG></TD>
											</TR>
											<TR>
												<TD vAlign="top" width="320">
													<asp:Label id="lblCurrentBond" runat="server"></asp:Label></TD>
												<TD vAlign="top" width="95"></TD>
												<TD vAlign="top" width="320"></TD>
											</TR>
											<TR>
												<TD class="menubar" vAlign="top" width="320"><STRONG>Signing Date:</STRONG></TD>
												<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
												<TD class="menubar" vAlign="top" width="320"><STRONG>End Date:</STRONG></TD>
											</TR>
											<TR>
												<TD vAlign="top" width="320">
													<ew:CalendarPopup id="SignDate" runat="server">
														<TextboxLabelStyle CssClass="textbox"></TextboxLabelStyle>
														<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></WeekdayStyle>
														<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Yellow"></MonthHeaderStyle>
														<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
															BackColor="AntiqueWhite"></OffMonthStyle>
														<ButtonStyle CssClass="button"></ButtonStyle>
														<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></GoToTodayStyle>
														<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="LightGoldenrodYellow"></TodayDayStyle>
														<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Orange"></DayHeaderStyle>
														<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="LightGray"></WeekendStyle>
														<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Yellow"></SelectedDateStyle>
														<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></ClearDateStyle>
														<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></HolidayStyle>
													</ew:CalendarPopup></TD>
												<TD vAlign="top" width="95"></TD>
												<TD vAlign="top" width="320">
													<ew:CalendarPopup id="EndDate" runat="server">
														<TextboxLabelStyle CssClass="textbox"></TextboxLabelStyle>
														<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></WeekdayStyle>
														<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Yellow"></MonthHeaderStyle>
														<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
															BackColor="AntiqueWhite"></OffMonthStyle>
														<ButtonStyle CssClass="button"></ButtonStyle>
														<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></GoToTodayStyle>
														<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="LightGoldenrodYellow"></TodayDayStyle>
														<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Orange"></DayHeaderStyle>
														<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="LightGray"></WeekendStyle>
														<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Yellow"></SelectedDateStyle>
														<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></ClearDateStyle>
														<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></HolidayStyle>
													</ew:CalendarPopup>
													<asp:CompareValidator id="CompareValidator3" runat="server" ControlToValidate="EndDate" ErrorMessage="End Date Must be greater than signing date"
														Operator="GreaterThan" ControlToCompare="EffectiveDate" Type="Date" Display="Dynamic">End Date Must be greater than signing date</asp:CompareValidator></TD>
											</TR>
											<TR>
												<TD vAlign="top" colSpan="3">
													<asp:Button id="btnInsertBond" runat="server" Width="70px" Text="Insert"></asp:Button>
													<asp:Button id="btnBondUpdate" runat="server" Width="70px" Enabled="False" Text="Update"></asp:Button>
													<asp:Button id="btnBondCancel" runat="server" Width="70px" Text="Cancel"></asp:Button></TD>
											</TR>
											<TR>
												<TD vAlign="top" colSpan="3">
													<asp:Label id="lblBond" runat="server" Visible="False" ForeColor="DarkOrange"></asp:Label></TD>
											</TR>
											<TR>
												<TD class="OrangeFormTitle" vAlign="top" colSpan="3">Bond Detail</TD>
											</TR>
											<TR>
												<TD vAlign="top" colSpan="3">
													<asp:DataGrid id="dgBondPaper" runat="server" Width="100%" Visible="False" BorderStyle="None"
														AutoGenerateColumns="False" GridLines="Horizontal" CellPadding="3" BorderWidth="1px" BorderColor="Navy">
														<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
														<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
														<AlternatingItemStyle CssClass="alternetitem"></AlternatingItemStyle>
														<ItemStyle CssClass="item"></ItemStyle>
														<HeaderStyle Font-Bold="True" ForeColor="Black" CssClass="header"></HeaderStyle>
														<Columns>
															<asp:BoundColumn Visible="False" DataField="bondno"></asp:BoundColumn>
															<asp:BoundColumn DataField="signdate" HeaderText="Signing Date" DataFormatString="{0:d}"></asp:BoundColumn>
															<asp:BoundColumn DataField="enddate" HeaderText="End Date" DataFormatString="{0:d}"></asp:BoundColumn>
															<asp:BoundColumn DataField="active" HeaderText="Status"></asp:BoundColumn>
															<asp:TemplateColumn>
																<ItemTemplate>
																	<asp:LinkButton id="lnkEdit" onclick="EditBond" runat="server" CausesValidation="False">Edit</asp:LinkButton>
																</ItemTemplate>
															</asp:TemplateColumn>
															<asp:TemplateColumn>
																<ItemTemplate>
																	<asp:LinkButton id="lnkDelete" runat="server" OnClick="DeleteBond" CausesValidation="False">Delete</asp:LinkButton>
																</ItemTemplate>
															</asp:TemplateColumn>
														</Columns>
														<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
													</asp:DataGrid></TD>
											</TR>
										</TABLE>
									</asp:panel><BR>
									<asp:panel id="pnlFunctionalDesignation" runat="server" Visible="False" DESIGNTIMEDRAGDROP="43">
										<TABLE id="Table8" class="MainFormColor" border="0" cellSpacing="0" cellPadding="3" width="100%"
											align="center">
											<TR>
												<TD class="OrangeFormTitle" colSpan="3">Functional Designation</TD>
											</TR>
											<TR>
												<TD class="menubar" vAlign="top" width="320"><STRONG>Designation:</STRONG></TD>
												<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
												<TD class="menubar" vAlign="top" width="320"><STRONG>Employee Pcode:</STRONG></TD>
											</TR>
											<TR>
												<TD vAlign="top" width="320">
													<asp:Label id="lblEmpDesignation" runat="server" Font-Bold="True"></asp:Label></TD>
												<TD vAlign="top" width="95"></TD>
												<TD vAlign="top" width="320">
													<asp:Label id="lblFuncPcode" runat="server" Font-Bold="True"></asp:Label></TD>
											</TR>
											<TR>
												<TD class="menubar" vAlign="top" width="320"><STRONG>Functional Designation:</STRONG>
													<asp:CustomValidator id="CustomValidator12" runat="server" ControlToValidate="ddFunctionalDesignation"
														ErrorMessage="Please select any Functional Designation" Enabled="False">*</asp:CustomValidator></TD>
												<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
												<TD class="menubar" vAlign="top" width="320"><STRONG>Total Vacancies:</STRONG></TD>
											</TR>
											<TR>
												<TD vAlign="top" width="320">
													<asp:DropDownList id="ddFunctionalDesignation" runat="server" Width="100%" CssClass="textbox" AutoPostBack="True"></asp:DropDownList></TD>
												<TD vAlign="top" width="95"></TD>
												<TD vAlign="top" width="320">
													<asp:Label id="lblFunctionalVacancy" runat="server" Font-Bold="True"></asp:Label></TD>
											</TR>
											<TR>
												<TD class="menubar" vAlign="top" width="320"><STRONG>Effective Date:</STRONG>
													<asp:RequiredFieldValidator id="RequiredFieldValidator11" runat="server" ControlToValidate="EffectiveDate" ErrorMessage="Please enter effective date">*</asp:RequiredFieldValidator></TD>
												<TD class="menubar" vAlign="top" width="95"><STRONG></STRONG></TD>
												<TD class="menubar" vAlign="top" width="320"><STRONG></STRONG></TD>
											</TR>
											<TR>
												<TD vAlign="top" width="320">
													<ew:CalendarPopup id="EffectiveDate" runat="server" Width="208px">
														<TextboxLabelStyle CssClass="textbox"></TextboxLabelStyle>
														<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></WeekdayStyle>
														<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Yellow"></MonthHeaderStyle>
														<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
															BackColor="AntiqueWhite"></OffMonthStyle>
														<ButtonStyle CssClass="button"></ButtonStyle>
														<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></GoToTodayStyle>
														<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="LightGoldenrodYellow"></TodayDayStyle>
														<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Orange"></DayHeaderStyle>
														<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="LightGray"></WeekendStyle>
														<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="Yellow"></SelectedDateStyle>
														<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></ClearDateStyle>
														<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
															BackColor="White"></HolidayStyle>
													</ew:CalendarPopup></TD>
												<TD vAlign="top" width="95"></TD>
												<TD vAlign="top" width="320"></TD>
											</TR>
											<TR>
												<TD vAlign="top" colSpan="3">
													<asp:Button id="btnAddFunctional" runat="server" CausesValidation="False" Width="56px" Text="Add"></asp:Button>
													<asp:Button id="btnFunctionalCancel" runat="server" CausesValidation="False" Text="Cancel"></asp:Button></TD>
											</TR>
											<TR>
												<TD vAlign="top" colSpan="3">
													<asp:label id="lblFunMsg" runat="server" Font-Bold="True" Visible="False" ForeColor="DarkOrange"></asp:label></TD>
											</TR>
											<TR>
												<TD vAlign="top" colSpan="3" align="center">
													<asp:Panel id="pnlAddFunc" runat="server" Width="90%" Visible="False" BorderStyle="Double">
														<TABLE id="Table13" class="MainFormColor" border="0" cellSpacing="0" cellPadding="3" width="100%"
															align="center">
															<TR>
																<TD class="OrangeFormTitle" colSpan="3">End date for current functional 
																	designation:</TD>
															</TR>
															<TR>
																<TD colSpan="3">
																	<asp:Label id="lblFuncError" runat="server" Font-Bold="True" ForeColor="Red"></asp:Label></TD>
															</TR>
															<TR>
																<TD class="menubar" vAlign="top" width="320">End Date:
																	<asp:RequiredFieldValidator id="RequiredFieldValidator12" runat="server" ControlToValidate="FuncEndDate" ErrorMessage="Please enter end date"></asp:RequiredFieldValidator></TD>
																<TD class="menubar" vAlign="top" width="95"></TD>
																<TD class="menubar" vAlign="top" width="320"></TD>
															</TR>
															<TR>
																<TD vAlign="top" width="320">
																	<ew:CalendarPopup id="FuncEndDate" runat="server" Width="160px" Nullable="True">
																		<TextboxLabelStyle CssClass="textbox"></TextboxLabelStyle>
																		<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="White"></WeekdayStyle>
																		<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="Yellow"></MonthHeaderStyle>
																		<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																			BackColor="AntiqueWhite"></OffMonthStyle>
																		<ButtonStyle CssClass="button"></ButtonStyle>
																		<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="White"></GoToTodayStyle>
																		<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="LightGoldenrodYellow"></TodayDayStyle>
																		<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="Orange"></DayHeaderStyle>
																		<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="LightGray"></WeekendStyle>
																		<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="Yellow"></SelectedDateStyle>
																		<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="White"></ClearDateStyle>
																		<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="White"></HolidayStyle>
																	</ew:CalendarPopup></TD>
																<TD vAlign="top" width="95"></TD>
																<TD vAlign="top" width="320"></TD>
															</TR>
															<TR>
																<TD vAlign="top" width="320">
																	<asp:Button id="Ok" runat="server" CausesValidation="False" Width="40px" Text="OK"></asp:Button>
																	<asp:Button id="btnTerminate" runat="server" CausesValidation="False" Text="Cancel"></asp:Button></TD>
																<TD vAlign="top" width="95"></TD>
																<TD vAlign="top" width="320"></TD>
															</TR>
														</TABLE>
													</asp:Panel></TD>
											</TR>
											<TR>
												<TD vAlign="top" colSpan="3" align="center">
													<asp:DataGrid id="dgFunctionalDesignation" runat="server" Width="750px" Visible="False" BorderStyle="Solid"
														AutoGenerateColumns="False" GridLines="Horizontal" CellPadding="3" BorderWidth="1px" BorderColor="Navy"
														BackColor="White">
														<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
														<AlternatingItemStyle CssClass="AlternetItem"></AlternatingItemStyle>
														<ItemStyle ForeColor="#4A3C8C" CssClass="Item"></ItemStyle>
														<HeaderStyle Font-Bold="True" ForeColor="#F7F7F7" CssClass="header"></HeaderStyle>
														<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
														<Columns>
															<asp:BoundColumn Visible="False" DataField="fdesigid"></asp:BoundColumn>
															<asp:BoundColumn DataField="designation" HeaderText="Designation"></asp:BoundColumn>
															<asp:BoundColumn DataField="title" HeaderText="Functional Designation"></asp:BoundColumn>
															<asp:BoundColumn DataField="_from" HeaderText="From" DataFormatString="{0:d}"></asp:BoundColumn>
															<asp:BoundColumn DataField="_to" HeaderText="To" DataFormatString="{0:d}"></asp:BoundColumn>
															<asp:BoundColumn DataField="status" HeaderText="Status"></asp:BoundColumn>
															<asp:TemplateColumn>
																<ItemTemplate>
																	<asp:LinkButton id="lnkDisable" runat="server" OnClick="DelFunctionalDesignation">Disable</asp:LinkButton>
																</ItemTemplate>
															</asp:TemplateColumn>
														</Columns>
														<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
													</asp:DataGrid></TD>
											</TR>
										</TABLE>
										<BR>
									</asp:panel></TD>
							</TR>
						</TABLE>
						<asp:validationsummary id="ValidationSummary1" runat="server" Height="28px" Width="152px" ShowMessageBox="True"></asp:validationsummary><asp:panel id="pnlAccount" runat="server" Width="100%">
							<TABLE id="Table11" class="MainFormColor" border="0" cellSpacing="0" cellPadding="3" width="100%"
								align="center">
								<TR>
									<TD class="OrangeFormTitle" colSpan="3">Employee's System Account:</TD>
								</TR>
								<TR>
									<TD class="menubar" vAlign="top" width="320"><STRONG>Employee Code:</STRONG></TD>
									<TD class="menubar" vAlign="top" width="95"></TD>
									<TD class="menubar" vAlign="top" width="320"><STRONG>Employee Name:</STRONG></TD>
								</TR>
								<TR>
									<TD vAlign="top" width="320">
										<asp:label id="lblPCode_2" runat="server"></asp:label></TD>
									<TD vAlign="top" width="95"></TD>
									<TD vAlign="top" width="320">
										<asp:label id="lblName_2" runat="server"></asp:label></TD>
								</TR>
								<TR>
									<TD class="menubar" vAlign="top" width="320"><STRONG>Designation:</STRONG></TD>
									<TD class="menubar" vAlign="top" width="95"></TD>
									<TD class="menubar" vAlign="top" width="320"><STRONG>Department:</STRONG></TD>
								</TR>
								<TR>
									<TD vAlign="top" width="320">
										<asp:label id="lblDesg_2" runat="server"></asp:label></TD>
									<TD vAlign="top" width="95"></TD>
									<TD vAlign="top" width="320">
										<asp:label id="lblDept_2" runat="server"></asp:label></TD>
								</TR>
								<TR>
									<TD class="menubar" vAlign="top" width="320"><STRONG>Official Email:</STRONG></TD>
									<TD class="menubar" vAlign="top" width="95"></TD>
									<TD class="menubar" vAlign="top" width="320"></TD>
								</TR>
								<TR>
									<TD vAlign="top" width="320">
										<asp:label id="lblEmailOfficial_2" runat="server"></asp:label></TD>
									<TD vAlign="top" width="95"></TD>
									<TD vAlign="top" width="320"></TD>
								</TR>
								<TR>
									<TD style="HEIGHT: 18px" class="menubar" vAlign="top" width="320"><STRONG>User&nbsp;ID:
											<asp:requiredfieldvalidator id="Requiredfieldvalidator4" runat="server" ControlToValidate="txtUserID" ErrorMessage="* Required"
												Display="Dynamic"></asp:requiredfieldvalidator>
											<asp:linkbutton id="lbCheckAvailability" runat="server" CausesValidation="False">Check Availability</asp:linkbutton>
											<asp:Label id="lblAvailable" runat="server"></asp:Label></STRONG></TD>
									<TD style="HEIGHT: 18px" class="menubar" vAlign="top" width="95"></TD>
									<TD style="HEIGHT: 18px" class="menubar" vAlign="top" width="320"><STRONG>
											<asp:label id="lblADExist_2" runat="server" Visible="False"></asp:label></STRONG></TD>
								</TR>
								<TR>
									<TD vAlign="top" width="320">
										<asp:textbox id="txtUserID" runat="server" Width="100%" CssClass="TextBox"></asp:textbox></TD>
									<TD vAlign="top" width="95"></TD>
									<TD vAlign="top" width="320"></TD>
								</TR>
								<TR>
									<TD vAlign="top" width="320">
										<asp:CheckBox id="chkActiveAccount" runat="server" Text="Active Account" Checked="True"></asp:CheckBox></TD>
									<TD vAlign="top" width="95">
										<asp:TextBox style="VISIBILITY: hidden" id="txtPassword" runat="server" Width="100%"></asp:TextBox></TD>
									<TD vAlign="top" width="320">
										<asp:textbox style="VISIBILITY: hidden" id="txtEmpInfoEmail2" runat="server"></asp:textbox></TD>
								</TR>
								<TR>
									<TD vAlign="top" colSpan="3">
										<asp:label id="lblError" runat="server" Font-Bold="True"></asp:label></TD>
								</TR>
								<TR>
									<TD vAlign="top" colSpan="3">
										<asp:Button id="cmdCreate" runat="server" Text="Create Account"></asp:Button></TD>
								</TR>
								<TR>
									<TD vAlign="top" colSpan="3" align="center">
										<asp:label id="lblAccountMessage_2" runat="server" Font-Bold="True" ForeColor="DarkOrange"></asp:label></TD>
								</TR>
							</TABLE>
							<BR>
							<TABLE id="Table12" class="MainFormColor" border="0" cellSpacing="0" cellPadding="3" width="100%"
								align="center">
								<TR>
									<TD class="OrangeFormTitle" colSpan="3">Roles</TD>
								</TR>
								<TR>
									<TD vAlign="top" colSpan="3">
										<asp:datagrid id="dgRoles" runat="server" Width="100%" BorderStyle="Solid" AutoGenerateColumns="False"
											GridLines="Horizontal" CellPadding="3" BorderWidth="1px" BorderColor="Navy" AllowSorting="True">
											<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
											<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
											<AlternatingItemStyle CssClass="alternetitem"></AlternatingItemStyle>
											<ItemStyle CssClass="item"></ItemStyle>
											<HeaderStyle Font-Bold="True" ForeColor="Black" CssClass="header"></HeaderStyle>
											<Columns>
												<asp:BoundColumn Visible="False" DataField="RoleID" SortExpression="RoleID" HeaderText="RoleID"></asp:BoundColumn>
												<asp:BoundColumn DataField="roleName" SortExpression="roleName" HeaderText="Role Name">
													<ItemStyle HorizontalAlign="Left" VerticalAlign="Bottom"></ItemStyle>
												</asp:BoundColumn>
												<asp:TemplateColumn>
													<ItemStyle HorizontalAlign="Center" Width="40px" VerticalAlign="Middle"></ItemStyle>
													<ItemTemplate>
														<asp:CheckBox id="chkSelectRole" runat="server"></asp:CheckBox>
													</ItemTemplate>
												</asp:TemplateColumn>
											</Columns>
											<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
										</asp:datagrid></TD>
								</TR>
								<TR>
									<TD vAlign="top" colSpan="3">
										<asp:Button id="cmdUpdate" runat="server" Text="Update Roles"></asp:Button></TD>
								</TR>
								<TR>
									<TD vAlign="top" colSpan="3" align="center">
										<asp:label id="lblRoleMessage_2" runat="server" Font-Bold="True" ForeColor="DarkOrange"></asp:label></TD>
								</TR>
							</TABLE>
							<BR>
						</asp:panel><asp:panel id="pnlCompensation" runat="server" Width="101.06%" Visible="False">
      <TABLE id="Table14" class="MainFormColor" border="0" cellSpacing="0" cellPadding="3" width="100%"
								align="left">
								<TR>
									<TD class="OrangeFormTitle" colSpan="3">Employee Compensation</TD>
								</TR>
								<TR>
									<TD style="WIDTH: 356px; HEIGHT: 18px" class="menubar"><STRONG>Mobile Entitlement:
											<asp:RegularExpressionValidator id="RegularExpressionValidator10" runat="server" ControlToValidate="txtMobileEntitlement"
												ErrorMessage="Numeric Values Only" Display="Dynamic" ValidationExpression="^[0-9]*$">Numeric Values Only</asp:RegularExpressionValidator>
											<asp:RequiredFieldValidator id="RequiredFieldValidator18" runat="server" ControlToValidate="txtMobileEntitlement"
												ErrorMessage="Please enter mobile entitlement" Display="Dynamic"></asp:RequiredFieldValidator></STRONG></TD>
									<TD style="WIDTH: 94px; HEIGHT: 18px" class="menubar"></TD>
									<TD style="HEIGHT: 18px" class="menubar"><STRONG>Petrol:
											<asp:RegularExpressionValidator id="RegularExpressionValidator11" runat="server" ControlToValidate="txtPetrolEntitlement"
												ErrorMessage="Numeric Values Only" Display="Dynamic" ValidationExpression="^[0-9]*$">Numeric Values Only</asp:RegularExpressionValidator>
											<asp:RequiredFieldValidator id="RequiredFieldValidator19" runat="server" ControlToValidate="txtPetrolEntitlement"
												ErrorMessage="Please enter petrol entitlement" Display="Dynamic"></asp:RequiredFieldValidator></STRONG></TD>
								</TR>
								<TR>
									<TD style="WIDTH: 356px; HEIGHT: 13px">
										<asp:TextBox id="txtMobileEntitlement" runat="server" Width="100%" CssClass="TextBox" MaxLength="1000">0</asp:TextBox></TD>
									<TD style="WIDTH: 94px; HEIGHT: 13px"></TD>
									<TD style="HEIGHT: 13px">
										<asp:TextBox id="txtPetrolEntitlement" runat="server" Width="100%" CssClass="TextBox" MaxLength="1000">0</asp:TextBox></TD>
								</TR>
								<TR>
									<TD style="WIDTH: 356px" class="menubar"><STRONG>Car/Conveyance:</STRONG></TD>
									<TD style="WIDTH: 94px" class="menubar"></TD>
									<TD class="menubar"><STRONG>Car/Conveyance Description:</STRONG></TD>
								</TR>
								<TR>
									<TD style="WIDTH: 356px">
										<asp:DropDownList id="ddCarConveyance" runat="server" Width="100%" CssClass="textbox" AutoPostBack="True">
											<asp:ListItem Value="0">Select--Car/Conveyance</asp:ListItem>
											<asp:ListItem Value="1">Company Maintained Car</asp:ListItem>
											<asp:ListItem Value="2">Conveyance Allowance</asp:ListItem>
											<asp:ListItem Value="3">Leased Car</asp:ListItem>
											<asp:ListItem Value="4">Pick &amp; Drop</asp:ListItem>
										</asp:DropDownList></TD>
									<TD style="WIDTH: 94px"></TD>
									<TD>
										<asp:TextBox id="txtCarDescription" runat="server" Width="100%" CssClass="TextBox" MaxLength="1000"
											TextMode="MultiLine"></asp:TextBox></TD>
								</TR>
								<TR>
									<TD style="WIDTH: 356px" class="menubar"><STRONG>Gross Salary:
											<asp:RegularExpressionValidator id="RegularExpressionValidator12" runat="server" ControlToValidate="txtGrossSal"
												ErrorMessage="Numeric Values Only" Display="Dynamic" ValidationExpression="^[0-9]*$">Numeric Values Only</asp:RegularExpressionValidator>
											<asp:RequiredFieldValidator id="RequiredFieldValidator16" runat="server" ControlToValidate="txtGrossSal" ErrorMessage="Please Enter Employee Salary"
												Display="Dynamic">Please Enter Employee Salary</asp:RequiredFieldValidator></STRONG></TD>
									<TD style="WIDTH: 94px" class="menubar"></TD>
									<TD class="menubar"><STRONG>Basic Salary:
											<asp:RegularExpressionValidator id="RegularExpressionValidator14" runat="server" ControlToValidate="txtBasicSal"
												ErrorMessage="Numeric Values Only" ValidationExpression="^[0-9]*$">Numeric Values Only</asp:RegularExpressionValidator></STRONG></TD>
								</TR>
								<TR>
									<TD style="WIDTH: 356px">
										<asp:TextBox id="txtGrossSal" runat="server" Width="100%" CssClass="TextBox" MaxLength="1000"
											AutoPostBack="True">0</asp:TextBox></TD>
									<TD style="WIDTH: 94px"></TD>
									<TD>
										<asp:TextBox id="txtBasicSal" runat="server" Width="100%" CssClass="TextBox" MaxLength="1000"
											Enabled="False">0</asp:TextBox></TD>
								</TR>
								<TR>
									<TD style="WIDTH: 356px" class="menubar"><STRONG>House Rent:
											<asp:RegularExpressionValidator id="RegularExpressionValidator13" runat="server" ControlToValidate="txtHouseRent"
												ErrorMessage="Numeric Values Only" ValidationExpression="^[0-9]*$">Numeric Values Only</asp:RegularExpressionValidator></STRONG></TD>
									<TD style="WIDTH: 94px" class="menubar"></TD>
									<TD class="menubar"><STRONG>Utility:
											<asp:RegularExpressionValidator id="RegularExpressionValidator15" runat="server" ControlToValidate="txtUtility"
												ErrorMessage="Numeric Values Only" ValidationExpression="^[0-9]*$">Numeric Values Only</asp:RegularExpressionValidator></STRONG></TD>
								</TR>
								<TR>
									<TD style="WIDTH: 356px">
										<asp:TextBox id="txtHouseRent" runat="server" Width="100%" CssClass="TextBox" MaxLength="1000"
											Enabled="False">0</asp:TextBox></TD>
									<TD></TD>
									<TD>
										<asp:TextBox id="txtUtility" runat="server" Width="100%" CssClass="TextBox" MaxLength="1000"
											Enabled="False">0</asp:TextBox></TD>
								</TR>
								<TR>
									<TD style="WIDTH: 368px" class="menubar"><STRONG>Others:</STRONG></TD>
									<TD style="WIDTH: 94px" class="menubar"></TD>
									<TD class="menubar"></TD>
								</TR>
								<TR>
									<TD style="WIDTH: 368px">
										<asp:TextBox id="txtEmpInfoOthers" runat="server" Width="100%" CssClass="TextBox" MaxLength="1000"
											TextMode="MultiLine"></asp:TextBox></TD>
									<TD style="WIDTH: 94px"></TD>
									<TD></TD>
								</TR>
								<TR>
									<TD colSpan="3">
										<asp:Button id="btnAddCompensation" runat="server" Width="56px" Text="Update"></asp:Button>
										<asp:Button id="btnCancelCompensation" runat="server" CausesValidation="False" Text="Cancel"></asp:Button></TD>
								</TR>
								<TR>
									<TD colSpan="3" align="center">
										<asp:label id="lblComMsg" runat="server" Font-Bold="True" Visible="False" ForeColor="DarkOrange"></asp:label></TD>
								</TR>
							</TABLE>&nbsp;</asp:panel>
				<TR>
					<TD vAlign="middle" align="center" height="20">Copyright © 2005 Independent Media 
						Corporation <A href="http://www.geo.tv">www.geo.tv</A></TD>
				</TR>
			</TABLE>
			<asp:textbox id="txtX" style="VISIBILITY: hidden" runat="server" Width="40px"></asp:textbox><asp:textbox id="txtY" style="VISIBILITY: hidden" runat="server" Width="32px"></asp:textbox><asp:label id="lblFdID" style="VISIBILITY: hidden" runat="server"></asp:label></form>
	</body>
</HTML>
