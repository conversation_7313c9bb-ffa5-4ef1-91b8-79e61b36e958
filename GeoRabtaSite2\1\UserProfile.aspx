﻿<%@ Register TagPrefix="uc1" TagName="MenuControl" Src="MenuControl.ascx" %>
<%@ Register TagPrefix="ajax" Namespace="MagicAjax.UI.Controls" Assembly="MagicAjax" %>
<%@ Page CodeBehind="UserProfile.aspx.cs" Language="c#" AutoEventWireup="false" Inherits="GeoRabtaSite.EmployeInfo" validateRequest="false" smartNavigation="False"%>
<%@ Register TagPrefix="uc1" TagName="Organimeter" Src="Organimeter.ascx" %>
<%@ Register TagPrefix="ew" Namespace="eWorld.UI" Assembly="eWorld.UI" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
  <HEAD>
		<title>Geo Rabta ::</title>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="StyleSheet1.css" type="text/css" rel="stylesheet">
		<style type="text/css">.style2 { FONT-WEIGHT: bold; FONT-SIZE: 16pt; COLOR: #ffffff }
	</style>
		<style type="text/css">A:link { COLOR: white }
	</style>
		<script src="jquery-1.2.6.js" type="text/javascript"></script>
		<script language="javascript" src="jquery.MetaData.js" type="text/javascript"></script>
		<script src="documentation.js" type="text/javascript"></script>
		<script language="javascript" src="jquery.MultiFile.js" type="text/javascript"></script>
		<script language="javascript" src="jquery.blockUI.js" type="text/javascript"></script>
		<script language="javascript">
		function checkValue()
         {
	// add attribute to command button and call this function like this
	// btnSave.attributes.add("onclick","return checkValue();");
	var d = document.getElementById("calendarDOB");
	var dateOfBirth= new Date(d.value);
	//var dateOfBirth = new Date("25/2/1955");
	var sDate=document.getElementById("txtServerDate");
	var serverDate = new Date(sDate.value);
	var dif=new Date(serverDate-dateOfBirth);
	var age = dif.getFullYear()-1970;
	//alert(age);
	//window.document.title="Age is " + age.toSring();
	var c;
	if (age<18)
		{
		//return confirm("Employee is under 18. Are you sure to continue with this age?");
		if(confirm("Employee is under 18. Are you sure to continue with this age?"))
			{
			event.returnValue=true;
			return true;
			}
		else
			{
			event.returnValue=false;
			return false;
			}
		}	
	else
		{
		event.returnValue=true;
		return true;
		}
	}
	function PopUpSlip()
	{
	 var w = 600, h = 350;
     if (document.all) {
     w = document.body.clientWidth;
     h = document.body.clientHeight;
    }
   else if (document.layers) {
   w = window.innerWidth;
   h = window.innerHeight;
    }
   var popW = 800, popH = 375;
   var leftPos = (w-popW)/2, topPos = (h-popH)/2;
   window.open('Report.aspx','MyPaySlip','width=' + popW + ',height='+popH+',top='+topPos+ ',left='+leftPos)
   //window.open('Report.aspx','MyPaySlip','status:no,toolbar:no,scrollbars:no,width=800');
	 return false; 
	}
	
function OpenURL(file,docw,doch)
{
var w=screen.width;
var h=screen.height;
var l=(w-docw)/2;
var t=(h-doch)/2;
var viewimageWin=window.open(file,"codewindow","toolbar=0,scrollbars=1,location=0,statusbar=0,menubar=0,resizable=0,width="+docw+",height="+doch+",left=" + l + ",top="+t);
return false;
}
		</script>
		<script language="javascript" id="clientEventHandlersJS">
<!--

function window_onload() {
d=document.getElementById('info');
d.style.visibility='hidden';
document.body.scrollTop =document.getElementById("txtX").value;
document.body.scrollLeft=document.getElementById("txtY").value;
//alert(document.getElementById("txtY").value);
}
function displayDiv()
{
d=document.getElementById('info');
d.style.visibility='visible';
d.style.left=window.event.x+document.body.scrollLeft+10;
d.style.top=window.event.y+document.body.scrollTop+15;
var val=document.getElementById('TextBox1');
d.innerHTML=val.value;
}
function disableDiv()
{
d=document.getElementById('info');
d.style.visibility='hidden';
}

function window_onscroll() {
document.getElementById("txtX").value=document.body.scrollTop;
document.getElementById("txtY").value=document.body.scrollLeft;
}

//-->
		</script>
</HEAD>
	<body language="javascript" oncontextmenu="return false" onselectstart="return false"
		ondrag="return false" onscroll="return window_onscroll()" bottomMargin="0" bgProperties="fixed"
		leftMargin="0" background="images\bg.jpg" topMargin="0" onload="return window_onload()"
		rightMargin="0">
		<script src="wz_tooltip.js" type="text/javascript"></script>
		<form id="myForm" name="MyForm" runat="server">
			<TABLE id="Table1" height="100%" cellSpacing="0" cellPadding="0" width="1004" border="0">
				<TR>
					<TD height="200">
      <OBJECT id=Shockwaveflash1 
      codeBase=http://fpdownload.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,0,0 
      height=200 width=1004 align=middle 
      classid=clsid:d27cdb6e-ae6d-11cf-96b8-444553540000>
	<PARAM NAME="_cx" VALUE="26564">
	<PARAM NAME="_cy" VALUE="5292">
	<PARAM NAME="FlashVars" VALUE="">
	<PARAM NAME="Movie" VALUE="top.swf">
	<PARAM NAME="Src" VALUE="top.swf">
	<PARAM NAME="WMode" VALUE="Transparent">
	<PARAM NAME="Play" VALUE="-1">
	<PARAM NAME="Loop" VALUE="-1">
	<PARAM NAME="Quality" VALUE="High">
	<PARAM NAME="SAlign" VALUE="">
	<PARAM NAME="Menu" VALUE="-1">
	<PARAM NAME="Base" VALUE="">
	<PARAM NAME="AllowScriptAccess" VALUE="sameDomain">
	<PARAM NAME="Scale" VALUE="ExactFit">
	<PARAM NAME="DeviceFont" VALUE="0">
	<PARAM NAME="EmbedMovie" VALUE="0">
	<PARAM NAME="BGColor" VALUE="0066FF">
	<PARAM NAME="SWRemote" VALUE="">
	<PARAM NAME="MovieData" VALUE="">
	<PARAM NAME="SeamlessTabbing" VALUE="1">
	<PARAM NAME="Profile" VALUE="0">
	<PARAM NAME="ProfileAddress" VALUE="">
	<PARAM NAME="ProfilePort" VALUE="0">
	<PARAM NAME="AllowNetworking" VALUE="all">
	<PARAM NAME="AllowFullScreen" VALUE="false">
	<embed src="top.swf" 
      width="1004" height="200" align="middle" quality="high" 
      wmode="transparent"								bgcolor="#0066ff" scale="exactfit" 
      allowScriptAccess="sameDomain" type="application/x-shockwave-flash"
      								pluginspage="http://www.macromedia.com/go/getflashplayer" />
	</OBJECT>
					</TD>
				</TR>
				<TR>
					<TD vAlign="top" align="center">
						<TABLE id="Table2" cellSpacing="0" cellPadding="0" width="100%" border="0">
							<TR>
								<TD vAlign="top" align="center" width="175"><uc1:menucontrol id="MenuControl1" runat="server"></uc1:menucontrol></TD>
								<TD vAlign="top" align="left" width="650" style="WIDTH: 650px; BACKGROUND-COLOR: #ffffff">
									<table cellSpacing="0" cellPadding="2" width="100%" align="center" bgColor="white" border="0">
										<tr>
											<td background="images/PanelTop.jpg" bgColor="#004477" height="65">
												<table cellSpacing="0" cellPadding="4" width="650" border="0">
													<tr>
														<td width="40"></td>
														<td><span class="style2">User Profile</span></td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td><asp:imagebutton id="ImageButton1" runat="server" CausesValidation="False" ImageUrl="images\employee.gif"></asp:imagebutton><asp:imagebutton id="ImageButton2" runat="server" CausesValidation="False" ImageUrl="images\personal.gif"></asp:imagebutton><asp:imagebutton id="ImageButton3" runat="server" CausesValidation="False" ImageUrl="images\family.gif"></asp:imagebutton><asp:imagebutton id="ImageButton4" runat="server" CausesValidation="False" ImageUrl="images\education.gif"></asp:imagebutton>
												<asp:imagebutton id="imgTraining" runat="server" ImageUrl="images\training.gif"></asp:imagebutton>
												<asp:HyperLink id="hlMyExp" runat="server" ImageUrl="images/MyExperience.gif" NavigateUrl="EmpExperience.aspx"
													BorderStyle="None" BorderWidth="0px">HyperLink</asp:HyperLink><asp:imagebutton id="ImageButton5" runat="server" CausesValidation="False" ImageUrl="buttons/myorganogram.gif"></asp:imagebutton>
												<asp:imagebutton id="ibSalary" runat="server" CausesValidation="False" ImageUrl="images/payslip.gif"></asp:imagebutton><asp:imagebutton id="ibMySelf" runat="server" CausesValidation="False" ImageUrl="Images\MeMyself.gif"
													Visible="False"></asp:imagebutton><asp:imagebutton id="imgMyLeave" runat="server" CausesValidation="False" ImageUrl="images\MyLeaveBalance.gif"></asp:imagebutton><asp:imagebutton id="ImgMyAttendance" runat="server" CausesValidation="False" ImageUrl="images\MyAttendance.gif"></asp:imagebutton><asp:imagebutton id="imgMyGrievance" runat="server" ImageUrl="images\mygrievance.gif"></asp:imagebutton>
												<asp:HyperLink id="hlMyRequests" runat="server" ImageUrl="images/myrequests.gif" BorderWidth="0px"
													BorderStyle="None" NavigateUrl="MyRequest.aspx">My Requests</asp:HyperLink><BR>
												<asp:panel id="Panel1" runat="server" BackImageUrl="images\tabstrip.jpg" Height="10px" Width="100%"></asp:panel><BR>
												<TABLE id="Table9" cellSpacing="0" cellPadding="0" width="400" align="center" bgColor="#666666"
													border="0">
													<TR>
														<TD vAlign="middle" align="center">
															<TABLE id="Table10" cellSpacing="1" cellPadding="2" width="100%" align="center" border="0">
																<TR>
																	<TD class="FormSubTitle" vAlign="middle" align="left" background="images/orangestrip.jpg">User 
																		Profile
																	</TD>
																</TR>
																<TR>
																	<TD bgColor="#ffffff">Welcome
																		<asp:label id="lblDisName" runat="server" Font-Bold="True" Font-Size="15px">Label</asp:label><BR>
																		You have visited us
																		<asp:label id="lblCount" runat="server" Font-Bold="True"></asp:label>&nbsp;Times
																	</TD>
																</TR>
															</TABLE>
														</TD>
													</TR>
												</TABLE>
												&nbsp;&nbsp;<asp:label id="Label2" runat="server" Visible="False"></asp:label><asp:label id="lblPCode" runat="server" Font-Bold="True" Font-Size="Medium" Visible="False"></asp:label><asp:label id="lblEmpInfo" runat="server" Font-Bold="True" Font-Size="Medium" Visible="False"></asp:label><asp:label id="Label1" runat="server" Visible="False"></asp:label><BR>
												<uc1:organimeter id="Organimeter1" runat="server"></uc1:organimeter><BR>
												<asp:panel id="pnlPersonal" runat="server" Visible="False" Width="100%">
                  <TABLE id=Table7 cellSpacing=0 cellPadding=3 width="100%" 
                  border=0>
                    <TR>
                      <TD class=PanelTitle>Personal 
                  Informatiom</TD></TR></TABLE>
                  <TABLE id=Table3 cellSpacing=0 cellPadding=4 width="100%" 
                  align=center border=0>
                    <TR vAlign=middle align=left>
                      <TD class=FirstColumncolor vAlign=top noWrap align=right 
                      width=118><STRONG>Date Of Birth</STRONG></TD>
                      <TD class=SecondColumnColor vAlign=top width=195>
<asp:Label id=lblName runat="server"></asp:Label></TD>
                      <TD class=ThirdColumnColor vAlign=top noWrap align=right 
                      width=118><STRONG>Father Name:</STRONG></TD>
                      <TD class=FourthColumnColor vAlign=top width=195>
<asp:Label id=lblFName runat="server"></asp:Label></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD vAlign=top noWrap align=right width=118 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD>
                      <TD vAlign=top noWrap align=right width=118 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD class=FirstColumncolor vAlign=top noWrap align=right 
                      width=118><STRONG>Gender:</STRONG></TD>
                      <TD class=SecondColumnColor vAlign=top width=195>
<asp:Label id=lblGender runat="server"></asp:Label></TD>
                      <TD class=ThirdColumnColor vAlign=top noWrap align=right 
                      width=118><STRONG>Blood Group:</STRONG></TD>
                      <TD class=FourthColumnColor vAlign=top width=195>
<asp:Label id=lblBloodGrp runat="server"></asp:Label></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD vAlign=top noWrap align=right width=118 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD>
                      <TD vAlign=top noWrap align=right width=118 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD class=FirstColumncolor vAlign=top noWrap align=right 
                      width=118><STRONG>Religion:</STRONG></TD>
                      <TD class=SecondColumnColor vAlign=top width=195>
<asp:Label id=lblReligion runat="server"></asp:Label></TD>
                      <TD class=ThirdColumnColor vAlign=top noWrap align=right 
                      width=118><STRONG>Passport No:</STRONG></TD>
                      <TD class=FourthColumnColor vAlign=top width=195>
<asp:Label id=lblPassport runat="server"></asp:Label></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD vAlign=top noWrap align=right width=118 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD>
                      <TD vAlign=top noWrap align=right width=118 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD class=FirstColumncolor vAlign=top noWrap align=right 
                      width=118><STRONG>Address:</STRONG></TD>
                      <TD class=SecondColumnColor vAlign=top width=195>
<asp:Label id=lblAddress runat="server"></asp:Label></TD>
                      <TD class=ThirdColumnColor vAlign=top noWrap align=right 
                      width=118><STRONG>Maritial Status: </STRONG></TD>
                      <TD class=FourthColumnColor vAlign=top width=195>
<asp:Label id=lblMaritialStat runat="server"></asp:Label></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD vAlign=top noWrap align=right width=118 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD>
                      <TD vAlign=top noWrap align=right width=118 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD class=FirstColumncolor vAlign=top noWrap align=right 
                      width=118><STRONG>Email (Personal):</STRONG></TD>
                      <TD class=SecondColumnColor vAlign=top width=195>
<asp:Label id=lblEmail runat="server"></asp:Label></TD>
                      <TD class=ThirdColumnColor vAlign=top noWrap align=right 
                      width=118><STRONG>NTN No</STRONG></TD>
                      <TD class=FourthColumnColor vAlign=top width=195>
<asp:Label id=lblNTN runat="server"></asp:Label></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD vAlign=top noWrap align=right width=118 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD>
                      <TD vAlign=top noWrap align=right width=118 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD class=FirstColumncolor vAlign=top noWrap align=right 
                      width=118><STRONG>Contact #:</STRONG></TD>
                      <TD class=SecondColumnColor vAlign=top width=195>
<asp:Label id=lblcontactNo runat="server"></asp:Label></TD>
                      <TD class=ThirdColumnColor vAlign=top noWrap align=right 
                      width=118><STRONG>Mobile #:</STRONG></TD>
                      <TD class=FourthColumnColor vAlign=top width=195>
<asp:Label id=lblMobileNo runat="server"></asp:Label></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD vAlign=top noWrap align=right width=118 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD>
                      <TD vAlign=top noWrap align=right width=118 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD class=FirstColumncolor vAlign=top noWrap align=right 
                      width=118><STRONG>NIC #(New):</STRONG></TD>
                      <TD class=SecondColumnColor vAlign=top width=195>
<asp:Label id=lblNICNew runat="server"></asp:Label></TD>
                      <TD class=ThirdColumnColor vAlign=top noWrap align=right 
                      width=118><STRONG>NIC (Old): </STRONG></TD>
                      <TD class=FourthColumnColor vAlign=top width=195>
<asp:Label id=lblNICOld runat="server"></asp:Label></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD vAlign=top noWrap align=right width=118 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD>
                      <TD vAlign=top noWrap align=right width=118 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD class=FirstColumncolor vAlign=top noWrap align=right 
                      width=118><STRONG>Kin:</STRONG></TD>
                      <TD class=SecondColumnColor vAlign=top width=195>
<asp:Label id=lblKin runat="server"></asp:Label></TD>
                      <TD class=ThirdColumnColor vAlign=top noWrap align=right 
                      width=118><STRONG>Bank Account No:</STRONG></TD>
                      <TD class=FourthColumnColor vAlign=top width=195>
<asp:Label id=lblBankAcctNo runat="server"></asp:Label></TD></TR>
                    <TR>
                      <TD vAlign=top noWrap align=right 
                        width=118><STRONG></STRONG></TD>
                      <TD vAlign=top width=195></TD>
                      <TD vAlign=top noWrap align=right 
                        width=118><STRONG></STRONG></TD>
                      <TD vAlign=top width=195></TD></TR>
                    <TR>
                      <TD class=FirstColumncolor vAlign=top noWrap align=right 
                      width=118><STRONG>Account Details:</STRONG></TD>
                      <TD class=SecondColumnColor vAlign=top width=195>
<asp:Label id=lblAccountDetails runat="server"></asp:Label></TD>
                      <TD class=ThirdColumnColor vAlign=top noWrap align=right 
                      width=118><STRONG>Nick:</STRONG></TD>
                      <TD class=FourthColumnColor vAlign=top width=195>
<asp:Label id=lblNick runat="server"></asp:Label></TD></TR>
                    <TR>
                      <TD vAlign=top noWrap align=right 
                        width=118><STRONG></STRONG></TD>
                      <TD vAlign=top width=195></TD>
                      <TD vAlign=top noWrap align=right 
                        width=118><STRONG></STRONG></TD>
                      <TD vAlign=top width=195></TD></TR>
                    <TR>
                      <TD class=FirstColumncolor vAlign=top noWrap align=right 
                      width=118><STRONG>Nationality 1:</STRONG></TD>
                      <TD class=SecondColumnColor vAlign=top width=195>
<asp:Label id=lblNationality runat="server"></asp:Label></TD>
                      <TD class=ThirdColumnColor vAlign=top noWrap align=right 
                      width=118><STRONG>Nationality 2:</STRONG></TD>
                      <TD class=FourthColumnColor vAlign=top width=195>
<asp:Label id=lblNationality2 runat="server"></asp:Label></TD></TR></TABLE><BR>
<asp:Panel id=pnlPersonalUPdate runat="server" Width="100%">
                  <TABLE id=Table22 cellSpacing=0 cellPadding=2 width="100%" 
                  border=0>
                    <TR>
                      <TD class=PanelTitle>Update Personal 
                    Information</TD></TR></TABLE>
                  <TABLE cellSpacing=0 cellPadding=2 width="100%" border=0>
                    <TR>
                      <TD class=FormColor1 style="WIDTH: 196px" vAlign=top 
                      align=right><STRONG>* Date Of Birth: </STRONG></TD>
                      <TD class=FormColor1 vAlign=top>
<ew:CalendarPopup id=calendarDOB runat="server" Width="248px" EnableHideDropDown="True" Nullable="True">
																		<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="White"></WeekdayStyle>
																		<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="Yellow"></MonthHeaderStyle>
																		<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																			BackColor="AntiqueWhite"></OffMonthStyle>
																		<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="White"></GoToTodayStyle>
																		<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="LightGoldenrodYellow"></TodayDayStyle>
																		<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="Orange"></DayHeaderStyle>
																		<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="LightGray"></WeekendStyle>
																		<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="Yellow"></SelectedDateStyle>
																		<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="White"></ClearDateStyle>
																		<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="White"></HolidayStyle>
																	</ew:CalendarPopup>&nbsp; 
<asp:Label id=lbltDateofBirth style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label>
<asp:RequiredFieldValidator id=RequiredFieldValidator3 runat="server" ErrorMessage="Please select date of birth" ControlToValidate="calendarDOB">*</asp:RequiredFieldValidator></TD></TR>
                    <TR>
                      <TD class=FormColor1 style="WIDTH: 196px" vAlign=top 
                      align=right></TD>
                      <TD class=FormColor1 vAlign=top><INPUT class=multi 
                        id=fpDateofBirth 
                        style="BORDER-RIGHT: silver 1px solid; BORDER-TOP: silver 1px solid; FONT-SIZE: 10px; BORDER-LEFT: silver 1px solid; WIDTH: 288px; BORDER-BOTTOM: silver 1px solid; HEIGHT: 17px" 
                        type=file maxLength=1 size=28 name=fpDateofBirth 
                        runat="server"><BR>
<asp:LinkButton id=lnDateofBirth runat="server" Visible="False" Font-Bold="True">[remove]</asp:LinkButton>
<asp:Label id=lblfpDateofBirth runat="server" Visible="False"></asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor2 style="WIDTH: 196px" vAlign=top 
                      align=right><STRONG>&nbsp;* Father Name:</STRONG></TD>
                      <TD class=FormColor2 vAlign=top>
<asp:TextBox id=txtFName runat="server" Width="280px" CssClass="TextBox" MaxLength="1000"></asp:TextBox>&nbsp; 
<asp:Label id=lbltFatherName style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label>
<asp:RegularExpressionValidator id=RegularExpressionValidator6 runat="server" ErrorMessage="Enter Only Alphabets" ControlToValidate="txtFName" ValidationExpression="^[a-z A-Z.-]+[(a-z A-Z)]*$" Display="Dynamic">*</asp:RegularExpressionValidator>
<asp:RequiredFieldValidator id=RequiredFieldValidator4 runat="server" ErrorMessage="Please Enter Father Name" ControlToValidate="txtFName" Display="Dynamic">*</asp:RequiredFieldValidator></TD></TR>
                    <TR>
                      <TD class=FormColor2 style="WIDTH: 196px" vAlign=top 
                      align=right></TD>
                      <TD class=FormColor2 vAlign=top><INPUT class=multi 
                        id=fpFatherName 
                        style="BORDER-RIGHT: silver 1px solid; BORDER-TOP: silver 1px solid; FONT-SIZE: 10px; BORDER-LEFT: silver 1px solid; WIDTH: 288px; BORDER-BOTTOM: silver 1px solid; HEIGHT: 17px" 
                        type=file maxLength=1 size=28 name=fpFatherName 
                        runat="server"><BR>
<asp:LinkButton id=lnFatherName runat="server" Visible="False" Font-Bold="True">[remove]</asp:LinkButton>
<asp:Label id=lblfpFatherName runat="server" Visible="False"></asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor2 style="WIDTH: 196px" vAlign=top 
                      align=right><STRONG>Gender #:</STRONG></TD>
                      <TD class=FormColor2 vAlign=top>
<asp:RadioButtonList id=RadioButtonList1 runat="server" Font-Size="10px" RepeatLayout="Flow" RepeatDirection="Horizontal">
																		<asp:ListItem Value="1">Male</asp:ListItem>
																		<asp:ListItem Value="2">Female</asp:ListItem>
																	</asp:RadioButtonList>&nbsp; 
<asp:Label id=lbltGender style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor1 style="WIDTH: 196px" vAlign=top 
                      align=right><STRONG>Blood Group:</STRONG></TD>
                      <TD class=FormColor1 vAlign=top>
<asp:DropDownList id=ddBloodGrp runat="server" Visible="False" Width="280px" CssClass="textbox">
																		<asp:ListItem Value="0">Select Blood Group</asp:ListItem>
																		<asp:ListItem Value="1">A+</asp:ListItem>
																		<asp:ListItem Value="2">A-</asp:ListItem>
																		<asp:ListItem Value="3">B+</asp:ListItem>
																		<asp:ListItem Value="4">B-</asp:ListItem>
																		<asp:ListItem Value="5">AB+</asp:ListItem>
																		<asp:ListItem Value="6">AB-</asp:ListItem>
																		<asp:ListItem Value="7">O+</asp:ListItem>
																		<asp:ListItem Value="8">O-</asp:ListItem>
																	</asp:DropDownList>&nbsp; 
<asp:Label id=lbltBloodGroup style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor2 style="WIDTH: 196px" vAlign=top 
                      align=right><STRONG>Religion:</STRONG></TD>
                      <TD class=FormColor2 vAlign=top>
<asp:DropDownList id=ddReligion runat="server" Width="280px" CssClass="textbox">
																		<asp:ListItem Value="0">Select--Religion</asp:ListItem>
																		<asp:ListItem Value="1">Islam</asp:ListItem>
																		<asp:ListItem Value="2">Christianity</asp:ListItem>
																		<asp:ListItem Value="3">Buddhism</asp:ListItem>
																		<asp:ListItem Value="4">Zoroastrian</asp:ListItem>
																		<asp:ListItem Value="5">Jewish</asp:ListItem>
																		<asp:ListItem Value="6">Hiduism</asp:ListItem>
																		<asp:ListItem Value="7">Others</asp:ListItem>
																	</asp:DropDownList>&nbsp; 
<asp:Label id=lbltReligion style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor1 style="WIDTH: 196px" vAlign=top 
                      align=right><STRONG>Nick:</STRONG></TD>
                      <TD class=FormColor1 vAlign=top>
<asp:TextBox id=txtNick runat="server" Width="280px" CssClass="textbox" MaxLength="50"></asp:TextBox>&nbsp; 
<asp:Label id=lbltNick style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor2 style="WIDTH: 196px" vAlign=top 
                      align=right><STRONG>* Address:</STRONG></TD>
                      <TD class=FormColor2 vAlign=top>
<asp:TextBox id=txtAddress runat="server" Width="280px" CssClass="TextBox" MaxLength="2000" TextMode="MultiLine"></asp:TextBox>&nbsp; 
<asp:Label id=lbltAddress style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label>
<asp:RequiredFieldValidator id=RequiredFieldValidator5 runat="server" ErrorMessage="Please enter your residential address" ControlToValidate="txtAddress">*</asp:RequiredFieldValidator></TD></TR>
                    <TR>
                      <TD class=FormColor2 style="WIDTH: 196px" vAlign=top 
                      align=right><STRONG>Marital Status:</STRONG></TD>
                      <TD class=FormColor2 vAlign=top>
<asp:DropDownList id=ddStatus runat="server" Width="280px" CssClass="textbox">
																		<asp:ListItem Value="0">Select--Status</asp:ListItem>
																		<asp:ListItem Value="1">Single</asp:ListItem>
																		<asp:ListItem Value="2">Married</asp:ListItem>
																		<asp:ListItem Value="3">Divorced</asp:ListItem>
																		<asp:ListItem Value="4">Widow</asp:ListItem>
																		<asp:ListItem Value="5">Separated</asp:ListItem>
																	</asp:DropDownList>&nbsp; 
<asp:Label id=lbltMaritalStatus style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor2 style="WIDTH: 196px" vAlign=top 
                      align=right></TD>
                      <TD class=FormColor2 vAlign=top><INPUT class=multi 
                        id=fpMartialStatus 
                        style="BORDER-RIGHT: silver 1px solid; BORDER-TOP: silver 1px solid; FONT-SIZE: 10px; BORDER-LEFT: silver 1px solid; WIDTH: 288px; BORDER-BOTTOM: silver 1px solid; HEIGHT: 17px" 
                        type=file maxLength=1 size=28 name=fpName 
                        runat="server"><BR>
<asp:LinkButton id=lnMartialStatus runat="server" Visible="False" Font-Bold="True">[remove]</asp:LinkButton>
<asp:Label id=lblfpMaritalStatus runat="server" Visible="False"></asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor1 style="WIDTH: 196px" vAlign=top 
                      align=right><STRONG>Email (Personal):</STRONG></TD>
                      <TD class=FormColor1 vAlign=top>
<asp:TextBox id=txtEmail runat="server" Width="280px" CssClass="TextBox" MaxLength="1000"></asp:TextBox>&nbsp; 
<asp:Label id=lbltEmailPersonal style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label>
<asp:RegularExpressionValidator id=RegularExpressionValidator9 runat="server" ErrorMessage="Enter email in correct format<br><EMAIL>" ControlToValidate="txtEmail" ValidationExpression="\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*" Display="Dynamic">*</asp:RegularExpressionValidator></TD></TR>
                    <TR>
                      <TD class=FormColor2 style="WIDTH: 196px" vAlign=top 
                      align=right><STRONG>NTN #:</STRONG></TD>
                      <TD class=FormColor2 vAlign=top>
<asp:TextBox id=txtNTN runat="server" Width="280px" CssClass="TextBox" MaxLength="50"></asp:TextBox>&nbsp; 
<asp:Label id=lbltNTN style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor1 style="WIDTH: 196px" vAlign=top 
                      align=right><STRONG>Residential#:</STRONG></TD>
                      <TD class=FormColor1 vAlign=top>
<asp:TextBox id=txtContactNo runat="server" Width="280px" CssClass="TextBox" MaxLength="50"></asp:TextBox>&nbsp; 
<asp:Label id=lbltTelephoneNo style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label>
<asp:RegularExpressionValidator id=RegularExpressionValidator7 runat="server" ErrorMessage="Alphabets not allowed in residential telephone" ControlToValidate="txtContactNo" ValidationExpression="^[ 0-9-,]*$" Display="Dynamic">*</asp:RegularExpressionValidator></TD></TR>
                    <TR>
                      <TD class=FormColor2 style="WIDTH: 196px" vAlign=top 
                      align=right><STRONG>Mobile #:</STRONG></TD>
                      <TD class=FormColor2 vAlign=top>
<asp:TextBox id=txtMobile runat="server" Width="280px" CssClass="TextBox" MaxLength="50"></asp:TextBox>&nbsp; 
<asp:Label id=lbltMobileNo style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label>
<asp:RegularExpressionValidator id=RegularExpressionValidator8 runat="server" ErrorMessage="Alphabets not allowed" ControlToValidate="txtMobile" ValidationExpression="^[0-9 ,-]*$" Display="Dynamic">*</asp:RegularExpressionValidator></TD></TR>
                    <TR>
                      <TD class=FormColor1 style="WIDTH: 196px" vAlign=top 
                      align=right><STRONG>Nationality1 </STRONG></TD>
                      <TD class=FormColor1 vAlign=top>
<asp:DropDownList id=ddNationality runat="server" Width="280px" CssClass="textbox" AutoPostBack="True">
																		<asp:ListItem Value="0">Select--Nationality</asp:ListItem>
																	</asp:DropDownList>&nbsp; 
<asp:Label id=lbltNationality style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label>
<asp:CustomValidator id=CustomValidator1 runat="server" ErrorMessage="Please select nationality" ControlToValidate="ddNationality">*</asp:CustomValidator></TD></TR>
                    <TR>
                      <TD class=FormColor1 style="WIDTH: 196px" vAlign=top 
                      align=right></TD>
                      <TD class=FormColor1 vAlign=top><INPUT class=multi 
                        id=fpNationality 
                        style="BORDER-RIGHT: silver 1px solid; BORDER-TOP: silver 1px solid; FONT-SIZE: 10px; BORDER-LEFT: silver 1px solid; WIDTH: 288px; BORDER-BOTTOM: silver 1px solid; HEIGHT: 17px" 
                        type=file maxLength=1 size=28 name=fpNationality 
                        runat="server"><BR>
<asp:LinkButton id=lnNationality runat="server" Visible="False" Font-Bold="True">[remove]</asp:LinkButton>
<asp:Label id=lblfpNationality runat="server" Visible="False"></asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor2 style="WIDTH: 196px" vAlign=top 
                      align=right><STRONG>Nationality 2</STRONG></TD>
                      <TD class=FormColor2 vAlign=top>
<asp:DropDownList id=ddNationality2 runat="server" Width="280px" CssClass="textbox" AutoPostBack="True" Enabled="False">
																		<asp:ListItem Value="0">Select--Nationality</asp:ListItem>
																	</asp:DropDownList>&nbsp; 
<asp:Label id=lbltNationality2 style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor2 style="WIDTH: 196px" vAlign=top 
                      align=right></TD>
                      <TD class=FormColor2 vAlign=top><INPUT class=multi 
                        id=fpNationality2 
                        style="BORDER-RIGHT: silver 1px solid; BORDER-TOP: silver 1px solid; FONT-SIZE: 10px; BORDER-LEFT: silver 1px solid; WIDTH: 288px; BORDER-BOTTOM: silver 1px solid; HEIGHT: 17px" 
                        type=file maxLength=1 size=28 name=fpNationality 
                        runat="server"><BR>
<asp:LinkButton id=lnNationality2 runat="server" Visible="False" Font-Bold="True">[remove]</asp:LinkButton>
<asp:Label id=lblfpNationality2 runat="server" Visible="False"></asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor1 style="WIDTH: 196px" vAlign=top 
                      align=right><STRONG>Passport #:</STRONG></TD>
                      <TD class=FormColor1 vAlign=top>
<asp:TextBox id=txtPassport runat="server" Width="280px" CssClass="TextBox" MaxLength="100"></asp:TextBox>&nbsp; 
<asp:Label id=lbltPassport style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label>
<asp:RequiredFieldValidator id=RequiredFieldValidator7 runat="server" ErrorMessage="Please enter passportno" ControlToValidate="txtPassport" Enabled="False">*</asp:RequiredFieldValidator></TD></TR>
                    <TR>
                      <TD class=FormColor1 style="WIDTH: 196px" vAlign=top 
                      align=right></TD>
                      <TD class=FormColor1 vAlign=top><INPUT class=multi 
                        id=fpPassport 
                        style="BORDER-RIGHT: silver 1px solid; BORDER-TOP: silver 1px solid; FONT-SIZE: 10px; BORDER-LEFT: silver 1px solid; WIDTH: 288px; BORDER-BOTTOM: silver 1px solid; HEIGHT: 17px" 
                        type=file maxLength=1 size=28 name=fpName 
                        runat="server"><BR>
<asp:LinkButton id=lnPassport runat="server" Visible="False" Font-Bold="True">[remove]</asp:LinkButton>
<asp:Label id=lblfpPassprot runat="server" Visible="False"></asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor2 style="WIDTH: 196px" vAlign=top 
                      align=right><STRONG>Next Of Kin:</STRONG></TD>
                      <TD class=FormColor2 vAlign=top>
<asp:TextBox id=txtKin runat="server" Width="280px" CssClass="TextBox" MaxLength="1000"></asp:TextBox>&nbsp; 
<asp:Label id=lbltNextofKin style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor2 style="WIDTH: 196px" vAlign=top 
                      align=right></TD>
                      <TD class=FormColor2 vAlign=top><INPUT class=multi 
                        id=fpNextofKin 
                        style="BORDER-RIGHT: silver 1px solid; BORDER-TOP: silver 1px solid; FONT-SIZE: 10px; BORDER-LEFT: silver 1px solid; WIDTH: 288px; BORDER-BOTTOM: silver 1px solid; HEIGHT: 17px" 
                        type=file maxLength=1 size=28 name=fpName 
                        runat="server"><BR>
<asp:LinkButton id=lnNextofKin runat="server" Visible="False" Font-Bold="True">[remove]</asp:LinkButton>
<asp:Label id=lblfpNextofKin runat="server" Visible="False"></asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor1 style="WIDTH: 196px" vAlign=top 
                      align=right><STRONG>* NIC (New) </STRONG></TD>
                      <TD class=FormColor1 vAlign=top>
<asp:TextBox id=txtNewNic runat="server" Width="280px" CssClass="TextBox"></asp:TextBox>&nbsp; 
<asp:Label id=lbltNICNew style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label>
<asp:RegularExpressionValidator id=RegularExpressionValidator1 runat="server" ErrorMessage="Please enter your  New NIC in correct format" ControlToValidate="txtNewNic" ValidationExpression="^\d{5}[-]\d{7}[-]\d{1}$">*</asp:RegularExpressionValidator>
<asp:RequiredFieldValidator id=RequiredFieldValidator6 runat="server" ErrorMessage="Please enter your New NIC" ControlToValidate="txtNewNic" Display="Dynamic" Enabled="False">*</asp:RequiredFieldValidator></TD></TR>
                    <TR>
                      <TD class=FormColor1 style="WIDTH: 196px" vAlign=top 
                      align=right></TD>
                      <TD class=FormColor1 vAlign=top><INPUT class=multi 
                        id=fpNICNew 
                        style="BORDER-RIGHT: silver 1px solid; BORDER-TOP: silver 1px solid; FONT-SIZE: 10px; BORDER-LEFT: silver 1px solid; WIDTH: 288px; BORDER-BOTTOM: silver 1px solid; HEIGHT: 17px" 
                        type=file maxLength=1 size=28 name=fpNICNew 
                        runat="server"><BR>
<asp:LinkButton id=lnNICNew runat="server" Visible="False" Font-Bold="True">[remove]</asp:LinkButton>
<asp:Label id=lblfpNICNew runat="server" Visible="False"></asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor2 style="WIDTH: 196px" vAlign=top 
                      align=right><STRONG>NIC (Old):</STRONG></TD>
                      <TD class=FormColor2 vAlign=top>
<asp:TextBox id=txtOldNic runat="server" Width="280px" CssClass="TextBox"></asp:TextBox>&nbsp; 
<asp:Label id=lbltNICOld style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label>
<asp:RegularExpressionValidator id=RegularExpressionValidator2 runat="server" ErrorMessage="Please enter your Old NIC in correct format" ControlToValidate="txtOldNic" ValidationExpression="^\d{3}[-]\d{2}[-]\d{6}$">*</asp:RegularExpressionValidator></TD></TR>
                    <TR>
                      <TD class=FormColor2 style="WIDTH: 196px" vAlign=top 
                      align=right></TD>
                      <TD class=FormColor2 vAlign=top><INPUT class=multi 
                        id=fpNICOld 
                        style="BORDER-RIGHT: silver 1px solid; BORDER-TOP: silver 1px solid; FONT-SIZE: 10px; BORDER-LEFT: silver 1px solid; WIDTH: 288px; BORDER-BOTTOM: silver 1px solid; HEIGHT: 17px" 
                        type=file maxLength=1 size=28 name=fpNICOld 
                        runat="server"><BR>
<asp:LinkButton id=lnNICOld runat="server" Visible="False" Font-Bold="True">[remove]</asp:LinkButton>
<asp:Label id=lblfpNICOld runat="server" Visible="False"></asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor1 style="WIDTH: 196px" vAlign=top 
                      align=right><STRONG>Bank AccountNo:</STRONG></TD>
                      <TD class=FormColor1 vAlign=top>
<asp:TextBox id=txtBankAcctNo runat="server" Width="280px" CssClass="TextBox" MaxLength="100"></asp:TextBox>&nbsp; 
<asp:Label id=lbltBankNo style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor1 style="WIDTH: 196px" vAlign=top 
                      align=right></TD>
                      <TD class=FormColor1 vAlign=top><INPUT class=multi 
                        id=fpBankNo 
                        style="BORDER-RIGHT: silver 1px solid; BORDER-TOP: silver 1px solid; FONT-SIZE: 10px; BORDER-LEFT: silver 1px solid; WIDTH: 288px; BORDER-BOTTOM: silver 1px solid; HEIGHT: 17px" 
                        type=file maxLength=1 size=28 name=fpBankNo 
                        runat="server"><BR>
<asp:LinkButton id=lnBankNo runat="server" Visible="False" Font-Bold="True">[remove]</asp:LinkButton>
<asp:Label id=lblfpBankNo runat="server" Visible="False"></asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor2 style="WIDTH: 196px" vAlign=top 
                      align=right><STRONG>Account Details:</STRONG></TD>
                      <TD class=FormColor2 vAlign=top>
<asp:TextBox id=txtAccountDetails runat="server" Width="280px" CssClass="TextBox" MaxLength="1000" TextMode="MultiLine"></asp:TextBox>&nbsp; 
<asp:Label id=lbltBankDetail style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor2 style="WIDTH: 196px" vAlign=top 
                      align=right></TD>
                      <TD class=FormColor2 vAlign=top><INPUT class=multi 
                        id=fpBankDetail 
                        style="BORDER-RIGHT: silver 1px solid; BORDER-TOP: silver 1px solid; FONT-SIZE: 10px; BORDER-LEFT: silver 1px solid; WIDTH: 288px; BORDER-BOTTOM: silver 1px solid; HEIGHT: 17px" 
                        type=file maxLength=1 size=28 name=fpBankDetail 
                        runat="server"><BR>
<asp:LinkButton id=lnBankDetail runat="server" Visible="False" Font-Bold="True">[remove]</asp:LinkButton>
<asp:Label id=lblfpBankDetail runat="server" Visible="False"></asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor1 style="WIDTH: 196px" vAlign=top 
                      align=right><STRONG>Picture Upload</STRONG></TD>
                      <TD class=FormColor1 vAlign=top><INPUT class=textbox 
                        id=FileToUpload style="WIDTH: 280px; HEIGHT: 17px" 
                        type=file size=27 name=FileToUpload 
                        runat="server">&nbsp; <BR><INPUT class=multi 
                        id=fpPicture 
                        style="BORDER-RIGHT: silver 1px solid; BORDER-TOP: silver 1px solid; FONT-SIZE: 10px; BORDER-LEFT: silver 1px solid; WIDTH: 288px; BORDER-BOTTOM: silver 1px solid; HEIGHT: 17px" 
                        type=file maxLength=1 size=28 name=fpPicture 
                        runat="server"> 
<asp:Label id=lbltPicture style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label><BR>
<asp:LinkButton id=lnPicture runat="server" Visible="False" Font-Bold="True">[remove]</asp:LinkButton>
<asp:Label id=lblfpPicture runat="server" Visible="False"></asp:Label></TD></TR>
                    <TR>
                      <TD style="WIDTH: 196px" vAlign=top align=right></TD>
                      <TD vAlign=top>
<asp:Label id=lblEmp runat="server" Font-Bold="True" ForeColor="DarkOrange"></asp:Label></TD></TR></TABLE></asp:Panel>
												</asp:panel><asp:panel id="pnlEmployeeInfo" runat="server">
                  <TABLE id=Table12 cellSpacing=0 cellPadding=3 width="100%" 
                  border=0>
                    <TR>
                      <TD class=PanelTitle>Employee 
                  Information</TD></TR></TABLE>
                  <TABLE id=Table5 cellSpacing=0 cellPadding=4 width="100%" 
                  align=center border=0>
                    <TR vAlign=middle align=left>
                      <TD class=FirstColumncolor vAlign=top align=right 
                      width=115><STRONG>Name: </STRONG></TD>
                      <TD class=SecondColumnColor vAlign=top width=195>
<asp:Label id=lblEmpInfoName runat="server"></asp:Label></TD>
                      <TD class=ThirdColumnColor vAlign=top align=right 
                      width=115><STRONG>Department:</STRONG></TD>
                      <TD class=FourthColumnColor vAlign=top width=195>
<asp:Label id=lnlEmpInfoDept runat="server"></asp:Label></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD vAlign=top align=right width=115 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD>
                      <TD vAlign=top align=right width=115 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD class=FirstColumncolor vAlign=top align=right 
                      width=115><STRONG>Designation:</STRONG></TD>
                      <TD class=SecondColumnColor vAlign=top width=195>
<asp:Label id=lblEmpInfoDesignation runat="server"></asp:Label></TD>
                      <TD class=ThirdColumnColor vAlign=top align=right 
                      width=115><STRONG>Functional Designation:</STRONG></TD>
                      <TD class=FourthColumnColor vAlign=top width=195>
<asp:Label id=lblFunctionalDesignation runat="server"></asp:Label><IMG 
                        id=imgInfo height=20 src="images/small_information.gif" 
                        runat="server"></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD vAlign=top align=right width=115 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD>
                      <TD vAlign=top align=right width=115 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD class=FirstColumncolor vAlign=top align=right 
                      width=115><STRONG>Category: </STRONG></TD>
                      <TD class=SecondColumnColor vAlign=top width=195>
<asp:Label id=lblEmpInfoCategory runat="server"></asp:Label></TD>
                      <TD class=ThirdColumnColor vAlign=top align=right 
                      width=115><STRONG>Date Of Joining:</STRONG></TD>
                      <TD class=FourthColumnColor vAlign=top width=195>
<asp:Label id=lblEmpInfoDOJ runat="server"></asp:Label></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD vAlign=top align=right width=115 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD>
                      <TD vAlign=top align=right width=115 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD class=FirstColumncolor vAlign=top align=right 
                      width=115><STRONG>Email Official:</STRONG></TD>
                      <TD class=SecondColumnColor vAlign=top width=195>
<asp:Label id=lblEmpInfoEmail runat="server"></asp:Label></TD>
                      <TD class=ThirdColumnColor vAlign=top align=right 
                      width=115><STRONG>Ext #:</STRONG></TD>
                      <TD class=FourthColumnColor vAlign=top width=195>
<asp:Label id=lblEmpInfoExtension runat="server"></asp:Label></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD vAlign=top align=right width=115 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD>
                      <TD vAlign=top align=right width=115 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD class=FirstColumncolor vAlign=top align=right 
                      width=115><STRONG>SESSI #:</STRONG></TD>
                      <TD class=SecondColumnColor vAlign=top width=195>
<asp:Label id=lblEmpInfoSessi runat="server"></asp:Label></TD>
                      <TD class=ThirdColumnColor vAlign=top align=right 
                      width=115><STRONG>EOBI #:</STRONG></TD>
                      <TD class=FourthColumnColor vAlign=top width=195>
<asp:Label id=lblEmpInfoEObi runat="server"></asp:Label></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD vAlign=top align=right width=115 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD>
                      <TD vAlign=top align=right width=115 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD class=FirstColumncolor vAlign=top align=right 
                      width=115><STRONG>Station:</STRONG></TD>
                      <TD class=SecondColumnColor vAlign=top width=195>
<asp:Label id=lblEmpInfoCity runat="server"></asp:Label></TD>
                      <TD class=ThirdColumnColor vAlign=top align=right 
                      width=115><STRONG>Date Of Confirmation:</STRONG></TD>
                      <TD class=FourthColumnColor vAlign=top width=195>
<asp:Label id=lblEmpInfoDOC runat="server"></asp:Label></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD vAlign=top align=right width=115 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD>
                      <TD vAlign=top align=right width=115 
                        height=3><STRONG></STRONG></TD>
                      <TD vAlign=top width=195 height=3></TD></TR>
                    <TR vAlign=middle align=left>
                      <TD class=FirstColumncolor vAlign=top align=right 
                      width=115><STRONG>Type Of Employment:</STRONG></TD>
                      <TD class=SecondColumnColor vAlign=top width=195>
<asp:Label id=lblEmpInfoTOE runat="server"></asp:Label></TD>
                      <TD class=ThirdColumnColor vAlign=top align=right 
                      width=115><STRONG>Compensatory Off:</STRONG></TD>
                      <TD class=FourthColumnColor vAlign=top width=195>
<asp:Label id=lblEmpInfoComp runat="server"></asp:Label></TD></TR>
                    <TR>
                      <TD vAlign=top align=right 
width=115><STRONG></STRONG></TD>
                      <TD vAlign=top width=195>
<asp:Label id=lblEmpInfoOthers runat="server" Font-Bold="True"></asp:Label></TD>
                      <TD vAlign=top align=right 
width=115><STRONG></STRONG></TD>
                      <TD vAlign=top width=195></TD></TR>
                    <TR>
                      <TD class=FirstColumncolor vAlign=top align=right 
                      width=115><STRONG>Workstation Address:</STRONG></TD>
                      <TD class=SecondColumnColor vAlign=top width=195>
<asp:Label id=lblEmpLocation runat="server" ForeColor="Black" BackColor="Transparent"></asp:Label></TD>
                      <TD class=FirstColumnColor vAlign=top align=right 
                      width=115></TD>
                      <TD class=SecondColumnColor vAlign=top 
                    width=195></TD></TR></TABLE><BR>
                  <TABLE id=Table13 cellSpacing=0 cellPadding=3 width="100%" 
                  border=0>
                    <TR>
                      <TD class=PanelTitle>Update Employment Information 
                    </TD></TR></TABLE>
<asp:Panel id=pnlUpdateEmp Width="100%" Runat="server">
                  <TABLE id=Table21 cellSpacing=0 cellPadding=2 width="100%" 
                  border=0>
                    <TR>
                      <TD class=FormColor1 vAlign=top align=right 
                        width=195><STRONG>* Name</STRONG><FONT 
                        size=2><STRONG>:</STRONG></FONT></TD>
                      <TD class=FormColor1 vAlign=top align=left>
<asp:TextBox id=txtEmpInfoName runat="server" Width="288px" CssClass="TextBox" MaxLength="50"></asp:TextBox>&nbsp; 
<asp:Label id=lbltName style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label>
<asp:RequiredFieldValidator id=RequiredFieldValidator1 runat="server" ErrorMessage="Please enter your name" ControlToValidate="txtEmpInfoName" Display="Dynamic">*</asp:RequiredFieldValidator>
<asp:RegularExpressionValidator id=RegularExpressionValidator3 runat="server" ErrorMessage="Enter Only Alphabet In Name" ControlToValidate="txtEmpInfoName" ValidationExpression="^[a-z A-Z.-]+[(a-z A-Z)]*$" Display="Dynamic">*</asp:RegularExpressionValidator></TD></TR>
                    <TR>
                      <TD class=FormColor1 vAlign=top align=right 
width=195></TD>
                      <TD class=FormColor1 vAlign=top align=left><INPUT 
                        class=multi id=fpName 
                        style="BORDER-RIGHT: silver 1px solid; BORDER-TOP: silver 1px solid; FONT-SIZE: 10px; BORDER-LEFT: silver 1px solid; WIDTH: 288px; BORDER-BOTTOM: silver 1px solid; HEIGHT: 17px" 
                        type=file maxLength=1 size=28 name=fpName 
                        runat="server"><BR>
<asp:LinkButton id=lnName runat="server" Visible="False" Font-Bold="True">[remove]</asp:LinkButton>
<asp:Label id=lblfpName runat="server" Visible="False"></asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor2 vAlign=top align=right 
                        width=195><STRONG>* Department:</STRONG></TD>
                      <TD class=FormColor2 vAlign=top align=left>
<asp:DropDownList id=ddDepartment runat="server" Width="288px" CssClass="textbox" AutoPostBack="True">
																		<asp:ListItem Value="0">Select--Department</asp:ListItem>
																	</asp:DropDownList>&nbsp; 
<asp:Label id=lbltDepartment style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label>
<asp:CustomValidator id=CustomValidator2 runat="server" ErrorMessage="Please select your department" ControlToValidate="ddDepartment" Display="Dynamic">*</asp:CustomValidator></TD></TR>
                    <TR>
                      <TD class=FormColor1 vAlign=top align=right 
                        width=195><STRONG>* Designation</STRONG><FONT 
                        size=2><STRONG>:</STRONG></FONT></TD>
                      <TD class=FormColor1 vAlign=top align=left>
<asp:DropDownList id=ddDesignation runat="server" Width="288px" CssClass="textbox" AutoPostBack="True">
																		<asp:ListItem Value="0">Select--Designation</asp:ListItem>
																	</asp:DropDownList>&nbsp; 
<asp:Label id=lbltDesignation style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label>
<asp:CustomValidator id=CustomValidator3 runat="server" ErrorMessage="Please Select Designation" ControlToValidate="ddDesignation" Display="Dynamic">*</asp:CustomValidator></TD></TR>
                    <TR>
                      <TD class=FormColor2 vAlign=top align=right 
                        width=195><STRONG>Functional Designation:</STRONG></TD>
                      <TD class=FormColor2 vAlign=top align=left>
<asp:DropDownList id=ddFunctionalDesignation runat="server" Width="288px" CssClass="TextBox" Enabled="False">
																		<asp:ListItem Value="0">Select--Functional Designation</asp:ListItem>
																	</asp:DropDownList></TD></TR>
                    <TR>
                      <TD class=FormColor1 vAlign=top align=right 
                        width=195><STRONG>Station:</STRONG></TD>
                      <TD class=FormColor1 vAlign=top align=left>
<asp:DropDownList id=ddStation runat="server" Width="288px" CssClass="textbox">
																		<asp:ListItem Value="0">Select--Station</asp:ListItem>
																	</asp:DropDownList>&nbsp; 
<asp:Label id=lbltStation style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label>
<asp:CustomValidator id=CustomValidator4 runat="server" ErrorMessage="Please select your city" ControlToValidate="ddStation" Display="Dynamic">*</asp:CustomValidator></TD></TR>
                    <TR>
                      <TD class=FormColor2 style="HEIGHT: 29px" vAlign=top 
                      align=right width=195><STRONG>* Date Of 
                      Joining:</STRONG></TD>
                      <TD class=FormColor2 style="HEIGHT: 29px" vAlign=top 
                      align=left>
<ew:CalendarPopup id=CalenderDOJ runat="server" EnableHideDropDown="True" Nullable="True" CellPadding="1px" CellSpacing="0px">
																		<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="White"></WeekdayStyle>
																		<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="Yellow"></MonthHeaderStyle>
																		<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																			BackColor="AntiqueWhite"></OffMonthStyle>
																		<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="White"></GoToTodayStyle>
																		<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="LightGoldenrodYellow"></TodayDayStyle>
																		<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="Orange"></DayHeaderStyle>
																		<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="LightGray"></WeekendStyle>
																		<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="Yellow"></SelectedDateStyle>
																		<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="White"></ClearDateStyle>
																		<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																			BackColor="White"></HolidayStyle>
																	</ew:CalendarPopup>&nbsp; 
<asp:Label id=lbltDateofJoin style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label><STRONG></STRONG>
<asp:RequiredFieldValidator id=RequiredFieldValidator2 runat="server" ErrorMessage="Please select your date of join" ControlToValidate="CalenderDOJ" Display="Dynamic">*</asp:RequiredFieldValidator></TD></TR>
                    <TR>
                      <TD class=FormColor1 vAlign=top align=right 
                        width=195><STRONG>Extension #:</STRONG></TD>
                      <TD class=FormColor1 vAlign=top align=left>
<asp:TextBox id=txtEmpInfoExtension runat="server" Width="288px" CssClass="TextBox" MaxLength="50"></asp:TextBox>&nbsp;<STRONG></STRONG> 
<asp:Label id=lbltExtension style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor2 vAlign=top align=right 
                        width=195><STRONG>Email Official:</STRONG></TD>
                      <TD class=FormColor2 vAlign=top 
                        align=left><STRONG><STRONG>
<asp:TextBox id=txtEmpInfoEmail runat="server" Width="288px" CssClass="TextBox" MaxLength="50"></asp:TextBox>&nbsp; 
<asp:Label id=lbltEmailofficial style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label></STRONG></STRONG></TD></TR>
                    <TR>
                      <TD class=FormColor1 vAlign=top align=right 
                        width=195><STRONG>SESSI #</STRONG></TD>
                      <TD class=FormColor1 vAlign=top 
                        align=left><STRONG><STRONG>
<asp:TextBox id=txtEmpInfoSESSI runat="server" Width="288px" CssClass="TextBox" MaxLength="50"></asp:TextBox>&nbsp; 
<asp:Label id=lbltSessi style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label></STRONG></STRONG></TD></TR>
                    <TR>
                      <TD class=FormColor2 vAlign=top align=right 
                        width=195><STRONG>EOBI #:</STRONG></TD>
                      <TD class=FormColor2 vAlign=top 
                        align=left><STRONG><STRONG>
<asp:TextBox id=txtEmpInfoEOBI runat="server" Width="288px" CssClass="TextBox"></asp:TextBox>&nbsp; 
<asp:Label id=lbltEOBI style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label></STRONG></STRONG></TD></TR>
                    <TR>
                      <TD class=FormColor2 vAlign=top align=right 
                        width=195><STRONG></STRONG></TD>
                      <TD class=FormColor2 vAlign=top 
                        align=left><STRONG><STRONG><INPUT class=multi id=fpEOBI 
                        style="BORDER-RIGHT: silver 1px solid; BORDER-TOP: silver 1px solid; FONT-SIZE: 10px; BORDER-LEFT: silver 1px solid; WIDTH: 288px; BORDER-BOTTOM: silver 1px solid; HEIGHT: 17px" 
                        type=file maxLength=1 size=28 name=fpEOBI 
                        runat="server"><BR>
<asp:LinkButton id=lnEOBI runat="server" Visible="False" Font-Bold="True">[remove]</asp:LinkButton>
<asp:Label id=lblfpEOBI runat="server" Visible="False"></asp:Label></STRONG></STRONG></TD></TR>
                    <TR>
                      <TD class=FormColor1 vAlign=top align=right 
                        width=195><STRONG>Workstation Address:</STRONG></TD>
                      <TD class=FormColor1 vAlign=top 
                        align=left><STRONG><STRONG>
<asp:TextBox id=txtEmpLocation runat="server" Width="288px" CssClass="textbox"></asp:TextBox>&nbsp; 
<asp:Label id=lbltWorkstation style="CURSOR: hand" runat="server" Font-Size="Small" Font-Bold="True" Font-Underline="True" ForeColor="Yellow">?</asp:Label></STRONG></STRONG></TD></TR></TABLE></asp:Panel>
												</asp:panel><asp:panel id="pnlPaySlip" runat="server">
                  <TABLE id=Table20 cellSpacing=0 cellPadding=3 width="100%" 
                  border=0>
                    <TR>
                      <TD class=PanelTitle vAlign=top colSpan=3>Employee 
                        Information:</TD></TR>
                    <TR>
                      <TD class=FormColor1 width=280><STRONG>Employee 
                        Code:</STRONG></TD>
                      <TD class=FormColor1 width=75><STRONG></STRONG></TD>
                      <TD class=FormColor1 width=280><STRONG></STRONG></TD></TR>
                    <TR>
                      <TD class=FormColor2 width=280>
<asp:Label id=lblPCodeP runat="server"></asp:Label></TD>
                      <TD class=FormColor2 width=75></TD>
                      <TD class=FormColor2 width=280></TD></TR>
                    <TR>
                      <TD class=FormColor1 width=280><STRONG>Employee 
                        Name:</STRONG></TD>
                      <TD class=FormColor1 width=75><STRONG></STRONG></TD>
                      <TD class=FormColor1 
                        width=280><STRONG>Designation:</STRONG></TD></TR>
                    <TR>
                      <TD class=FormColor2 width=280>
<asp:Label id=lblNameP runat="server"></asp:Label></TD>
                      <TD class=FormColor2 width=75></TD>
                      <TD class=FormColor2 width=280>
<asp:Label id=lblDesgP runat="server"></asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor1 
                        width=280><STRONG>Department:</STRONG></TD>
                      <TD class=FormColor1 width=75><STRONG></STRONG></TD>
                      <TD class=FormColor1 width=280><STRONG></STRONG></TD></TR>
                    <TR>
                      <TD class=FormColor2 width=280>
<asp:Label id=lblDeptP runat="server"></asp:Label></TD>
                      <TD class=FormColor2 width=75></TD>
                      <TD class=FormColor2 width=280></TD></TR>
                    <TR>
                      <TD class=PanelTitle colSpan=3><FONT 
                        size=3><STRONG>Salary 
                        Information</STRONG><STRONG></STRONG><STRONG></STRONG></FONT></TD></TR>
                    <TR>
                      <TD class=FormColor1 width=280><STRONG>Year:</STRONG></TD>
                      <TD class=FormColor1 width=75><STRONG></STRONG></TD>
                      <TD class=FormColor1 
                    width=280><STRONG>Month:</STRONG></TD></TR>
                    <TR>
                      <TD class=FormColor2 width=280>
<asp:DropDownList id=ddlYear runat="server" Width="100%" CssClass="textbox" AutoPostBack="True">
																	<asp:ListItem Value="0">Select -- Year</asp:ListItem>
																	<asp:ListItem Value="2003">2003</asp:ListItem>
																	<asp:ListItem Value="2004">2004</asp:ListItem>
																	<asp:ListItem Value="2005">2005</asp:ListItem>
																	<asp:ListItem Value="2006">2006</asp:ListItem>
																</asp:DropDownList></TD>
                      <TD class=FormColor2 width=75></TD>
                      <TD class=FormColor2 width=280>
<asp:DropDownList id=ddlMonths runat="server" Width="100%" CssClass="textbox" AutoPostBack="True" Enabled="False">
																	<asp:ListItem Value="0">Select -- Month</asp:ListItem>
																	<asp:ListItem Value="1">Jan</asp:ListItem>
																	<asp:ListItem Value="2">Feb</asp:ListItem>
																	<asp:ListItem Value="3">Mar</asp:ListItem>
																	<asp:ListItem Value="4">Apr</asp:ListItem>
																	<asp:ListItem Value="5">May</asp:ListItem>
																	<asp:ListItem Value="6">Jun</asp:ListItem>
																	<asp:ListItem Value="7">Jul</asp:ListItem>
																	<asp:ListItem Value="8">Aug</asp:ListItem>
																	<asp:ListItem Value="9">Sep</asp:ListItem>
																	<asp:ListItem Value="10">Oct</asp:ListItem>
																	<asp:ListItem Value="11">Nov</asp:ListItem>
																	<asp:ListItem Value="12">Dec</asp:ListItem>
																</asp:DropDownList></TD></TR></TABLE>
                  <TABLE id=tabPaySlip cellSpacing=0 cellPadding=3 width="100%" 
                  border=0 runat="server">
                    <TR>
                      <TD class=FormColor1 width=280><STRONG>Salary 
                        Date:</STRONG></TD>
                      <TD class=FormColor1 width=75></TD>
                      <TD class=FormColor1 width=280></TD></TR>
                    <TR>
                      <TD class=formcolor2 width=280>
<asp:Label id=lblSalaryDateP runat="server"></asp:Label></TD>
                      <TD class=formcolor2 width=75></TD>
                      <TD class=formcolor2 width=280></TD></TR>
                    <TR>
                      <TD class=FormColor1 width=280><STRONG>Bank</STRONG></TD>
                      <TD class=FormColor1 width=75></TD>
                      <TD class=FormColor1 
                      width=280><STRONG>Branch:</STRONG></TD></TR>
                    <TR>
                      <TD class=formcolor2 width=280>
<asp:Label id=lblBankP runat="server"></asp:Label></TD>
                      <TD class=formcolor2 width=75></TD>
                      <TD class=formcolor2 width=280>
<asp:Label id=lblBranchP runat="server"></asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor1 width=280><STRONG>Gross 
                        Salary:</STRONG></TD>
                      <TD class=FormColor1 width=75></TD>
                      <TD class=FormColor1 width=280><STRONG>Incom 
                      Tax</STRONG></TD></TR>
                    <TR>
                      <TD class=formcolor2 width=280>
<asp:Label id=lblGrossSalaryP runat="server"></asp:Label></TD>
                      <TD class=formcolor2 width=75></TD>
                      <TD class=formcolor2 width=280>
<asp:Label id=lblIncomTaxP runat="server"></asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor1 
                      width=280><STRONG>Arrears</STRONG></TD>
                      <TD class=FormColor1 width=75></TD>
                      <TD class=FormColor1 width=280><STRONG>Net 
                        Payroll</STRONG></TD></TR>
                    <TR>
                      <TD class=formcolor2 width=280>
<asp:Label id=lblArearsP runat="server"></asp:Label></TD>
                      <TD class=formcolor2 width=75></TD>
                      <TD class=formcolor2 width=280>
<asp:Label id=lblNetPayRollP runat="server"></asp:Label></TD></TR>
                    <TR>
                      <TD class=FormColor1 width=280><STRONG>Total Working 
                        Days:</STRONG></TD>
                      <TD class=FormColor1 width=75></TD>
                      <TD class=FormColor1 width=280></TD></TR>
                    <TR>
                      <TD class=formcolor2 width=280>
<asp:Label id=lblTotalWorkingDaysP runat="server"></asp:Label></TD>
                      <TD class=formcolor2 width=75></TD>
                      <TD class=formcolor2 width=280>
<asp:Button id=btnPrint runat="server" Enabled="False" Text="Print PaySlip"></asp:Button></TD></TR></TABLE>
<asp:Label id=lblMessage runat="server" Visible="False" CssClass="ErrorLabel">Label</asp:Label>
												</asp:panel><asp:button id="btnUpdate" runat="server" Text="Submit"></asp:button><BR>
												&nbsp;
												<asp:label id="lblCombine" runat="server" Visible="False" Font-Bold="True"></asp:label><BR>
												<asp:button id="btnIgnore" runat="server" Visible="False" Width="200px" Text="Ignore Dublicate Request &amp; Send"></asp:button><asp:button id="btnResend" runat="server" Visible="False" Width="176px" Text="Re-Send Complete Request"></asp:button><BR>
												<asp:label id="lblCompare" runat="server" Visible="False" Font-Size="XX-Small" ForeColor="Red">.Date of join must be less then date of confirmation.Your  date of join exceeds your confirmation date </asp:label><BR>
												<asp:validationsummary id="ValidationSummary1" runat="server" Height="100%"></asp:validationsummary><asp:textbox id="TextBox1" style="VISIBILITY: hidden" runat="server"></asp:textbox><asp:textbox id="txtX" style="VISIBILITY: hidden" runat="server" Width="24px">0</asp:textbox><asp:textbox id="txtY" style="VISIBILITY: hidden" runat="server" Width="24px">0</asp:textbox></td>
										</tr>
									</table>
								</TD>
								<TD vAlign="top" align="center" style="WIDTH: 180px">
								</TD>
							</TR>
						</TABLE>
					</TD>
				</TR>
				<TR>
					<TD class="Fotter" align="center" height="20">Copyright © 2005 Independent Media 
						Corporation www.geo.tv<BR>
					</TD>
				</TR>
			</TABLE>
			<DIV id="info" style="BORDER-RIGHT: gray 1px solid; BORDER-TOP: gray 1px solid; LEFT: 50px; BORDER-LEFT: gray 1px solid; WIDTH: 336px; COLOR: black; BORDER-BOTTOM: gray 1px solid; FONT-FAMILY: Verdana; POSITION: absolute; TOP: 50px; HEIGHT: 80px; BACKGROUND-COLOR: lavender"
				runat="server"></DIV>
		</form>
	</body>
</HTML>
