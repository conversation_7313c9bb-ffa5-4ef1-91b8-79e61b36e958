using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;
namespace GeoRabtaSite.admin
{
	/// <summary>
	/// Summary description for Project.
	/// </summary>
	public class ProjectEdit : System.Web.UI.Page
	{
		protected System.Web.UI.WebControls.Label lblwDays;
		protected System.Web.UI.WebControls.TextBox txtDescription;
		protected System.Web.UI.WebControls.Panel pnlStep1;
		protected System.Web.UI.WebControls.DropDownList ddProjectType;
		protected System.Web.UI.WebControls.Panel pnlStep2;
		protected System.Web.UI.WebControls.ImageButton imgNext1;
		protected System.Web.UI.WebControls.Label lblTeamLead;
		protected System.Web.UI.WebControls.DropDownList ddTeamType;
		protected System.Web.UI.WebControls.DataGrid dgTeam;
		protected System.Web.UI.WebControls.Button btnAddToProject;
		protected eWorld.UI.CalendarPopup calFrom;
		protected eWorld.UI.CalendarPopup CalTo;
		protected System.Web.UI.WebControls.TextBox txtTecDevlop;
		protected System.Web.UI.WebControls.TextBox txtQC;
		protected System.Web.UI.WebControls.TextBox txtImp;
		protected System.Web.UI.WebControls.ImageButton ImageButton1;
		protected System.Web.UI.HtmlControls.HtmlTable Table1;
		protected System.Web.UI.WebControls.TextBox txtCost;
		protected System.Web.UI.WebControls.TextBox txtX;
		protected System.Web.UI.WebControls.TextBox txtY;
		protected System.Web.UI.HtmlControls.HtmlTableCell r1;
		protected System.Web.UI.WebControls.DataGrid dgTeamFinal;
		protected System.Web.UI.WebControls.Panel pnlStep3;
		protected System.Web.UI.WebControls.ImageButton imgNext2;
		protected eWorld.UI.CalendarPopup CaltFrom;
		protected eWorld.UI.CalendarPopup CalTto;
		protected System.Web.UI.WebControls.Label lblwtDays;
		protected System.Web.UI.WebControls.CheckBoxList chkBoxTeam;
		protected System.Web.UI.WebControls.TextBox txtWeightage;
		protected System.Web.UI.WebControls.Button btnAddToTask;
		protected System.Web.UI.WebControls.ImageButton imgBack;
		protected System.Web.UI.HtmlControls.HtmlTableCell r2;
		protected System.Web.UI.WebControls.DropDownList ddYesNo;
		protected System.Web.UI.WebControls.Button btnCreate;
		protected System.Web.UI.WebControls.DataGrid dgTask;
		protected System.Web.UI.WebControls.TextBox txtTitle;
		protected eWorld.UI.CollapsablePanel CollapsablePanel1;
		protected System.Web.UI.WebControls.TextBox txtScope;
		protected System.Web.UI.HtmlControls.HtmlTableCell TD1;
		protected System.Web.UI.WebControls.CheckBoxList chkLine;
		protected System.Web.UI.WebControls.TextBox txtObjectives;
		protected System.Web.UI.WebControls.TextBox txtDeliverables;
		protected System.Web.UI.WebControls.TextBox txtMainTitle;
		protected System.Web.UI.HtmlControls.HtmlTableCell cRow;
		protected System.Web.UI.WebControls.Panel Panel1;
		protected System.Web.UI.WebControls.TextBox txtpCode;
		protected System.Web.UI.WebControls.TextBox txtName;
		protected System.Web.UI.WebControls.DropDownList ddDepartment;
		protected System.Web.UI.WebControls.DropDownList ddDesignation;
		protected System.Web.UI.WebControls.DropDownList ddStation;
		protected System.Web.UI.WebControls.Button btnSearch;
		protected System.Web.UI.WebControls.DataGrid dgTeamTask;
		protected System.Web.UI.WebControls.TextBox txtHidden;
		protected System.Web.UI.WebControls.TextBox txtaHidden;
	    SqlConnection con=new SqlConnection(Connection.ConnectionString);
		
		private void Page_Load(object sender, System.EventArgs e)
		{
			// Put user code to initialize the page here
			if(!IsPostBack)
			{
			 r1.Visible=false;
			 r2.Visible=false;
			 TD1.Visible=false;
			 cRow.Visible=false;
			 getDepartments();
			 getStation();
			 GetProjectDetail("PR-1");
			 FillProjectasks("PR-1");
				if(IsProjectShowntoLine("PR-1"))
				{
					this.ddYesNo.Enabled=false;
				}
				else
				{
				 this.ddYesNo.Enabled=true;
				}
			 }
			this.CalTo.LowerBoundDate=this.calFrom.SelectedDate.Date;
		}
		public void GetProjectDetail(string pId)
		{
		 con.Open();
		 SqlCommand cmd=new SqlCommand("select * from t_geoprojects where pid='"+pId+"'",con);
		 SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
			 this.txtMainTitle.Text=rd["title"].ToString();
			 this.txtDescription.Text=rd["description"].ToString();
			 this.txtScope.Text=rd["scope"].ToString();
			 this.txtObjectives.Text=rd["objectives"].ToString();
			 this.txtDeliverables.Text=rd["deliverables"].ToString();
			 this.ddProjectType.SelectedValue=rd["ptype"].ToString();
             this.calFrom.SelectedDate=DateTime.Parse(rd["durationfrom"].ToString());
			 this.CalTo.SelectedDate=DateTime.Parse(rd["durationto"].ToString());
			 this.lblwDays.Text=rd["totalworkingdays"].ToString();
			 this.txtCost.Text=rd["cost"].ToString();
			 this.txtTecDevlop.Text=rd["tdev"].ToString();
			 this.txtQC.Text=rd["qcircle"].ToString();
			 this.txtImp.Text=rd["imp"].ToString();
			}
		 rd.Close();
		 con.Close();
		}
		public bool IsProjectShowntoLine(string id)
		{
		 con.Open();
		 SqlCommand cmd=new SqlCommand("select * from t_geoprojectssupervisor where pid='"+id+"'",con);
		 SqlDataReader rd=cmd.ExecuteReader();
		 rd.Read();
			if(rd.HasRows)
			{
				rd.Close();
				con.Close();
				return true;
			}
			else
			{
				rd.Close();
				con.Close();
				return false;
			}
		}
		public void GetProjectTeam(string pId)
		{
		  con=new SqlConnection(Connection.ConnectionString);
		  con.Open();
		  string Query="SELECT 'delete',dbo.t_GeoProjectsTeam.team AS pcode,'<img src=../empthumbnail/' + RTRIM(dbo.t_GeoProjectsTeam.team) + '.jpg border=0 width=60>' AS pic,dbo.t_Employee.name,dbo.t_GeoProjectsTeam.role, "+
          "dbo.t_GeoProjectsTeam.teamid "+
          "FROM dbo.t_GeoProjects INNER JOIN "+
          "dbo.t_GeoProjectsTeam ON dbo.t_GeoProjects.pid = dbo.t_GeoProjectsTeam.pid INNER JOIN "+
          "dbo.t_Employee ON dbo.t_GeoProjectsTeam.team = dbo.t_Employee.pcode "+
          "WHERE (dbo.t_GeoProjectsTeam.isactive = 1) AND (dbo.t_GeoProjects.pid = '"+pId+"')";
		  SqlDataAdapter dr=new SqlDataAdapter(Query,con);
		  DataSet ds=new DataSet();
		  dr.Fill(ds,"Records");
		  this.dgTeamFinal.DataSource=ds;
		  this.dgTeamFinal.DataBind();
		  Session["dgTeamFinal"]=ds;
		  con.Close();
		}
		public void FillProjectasks(string pid)
		{
			con.Open();
			string Query="SELECT dbo.t_GeoProjectsTask.title, dbo.t_GeoProjectsTask.tdes,dbo.t_GeoProjectsTask.tid, dbo.t_GeoProjectsTask.durationfrom, "+ 
				"dbo.t_GeoProjectsTask.durationto,dbo.t_GeoProjectsTask.totalworkingdays,dbo.t_GeoProjectsTask.weightage "+
				"FROM dbo.t_GeoProjects INNER JOIN "+
				"dbo.t_GeoProjectsTask ON dbo.t_GeoProjects.pid = dbo.t_GeoProjectsTask.pid "+
				"WHERE (dbo.t_GeoProjectsTask.pid = '"+pid+"')";
			SqlDataAdapter dr=new SqlDataAdapter(Query,con);
			DataSet ds=new DataSet();
			dr.Fill(ds,"Records");
			DataColumn tc=new DataColumn("Responsible");
			ds.Tables[0].Columns.Add(tc);
			tc=new DataColumn("Pcode");
			ds.Tables[0].Columns.Add(tc);
			this.dgTeamTask.DataSource=ds;
			this.dgTeamTask.DataBind();
			this.dgTeamTask.Visible=true;
			Session["dgTeamTask"]=ds;
			
			for(int i=0;i<this.dgTeamTask.Items.Count;i++)
			{
				string tid=this.dgTeamTask.Items[i].Cells[2].Text;
				Query="SELECT dbo.t_GeoProjectsTask.tid, dbo.t_GeoProjectsTask.pid, dbo.t_GeoProjectsTeamTasks.team, "+ 
					"CASE iscomplete WHEN 0 THEN 'Pending' WHEN 1 THEN 'Completed' ELSE ' ' END AS Status, dbo.t_GeoProjectsTeamTasks.cdate, "+
					"dbo.t_Employee.name "+
					"FROM dbo.t_GeoProjectsTask INNER JOIN "+
					"dbo.t_GeoProjectsTeamTasks ON dbo.t_GeoProjectsTask.tid = dbo.t_GeoProjectsTeamTasks.tid INNER JOIN "+
					"dbo.t_Employee ON dbo.t_GeoProjectsTeamTasks.team = dbo.t_Employee.pcode "+
					"WHERE (dbo.t_GeoProjectsTask.tid = "+tid+") and dbo.t_GeoProjectsTeamTasks.isactive=1";
				SqlCommand cmd=new SqlCommand(Query,con);
                SqlDataReader rd=cmd.ExecuteReader();
				string rcode="";
				while(rd.Read())
				{
					string pcode=rd["team"].ToString();
					rcode+=rd["team"].ToString().Trim()+",";
					this.dgTeamTask.Items[i].Cells[7].Text+="<a><img border=0 src=../empthumbnail/"+rd["team"].ToString().Trim()+".jpg width=50>"+rd["name"].ToString().Trim()+"</a><br/>";
				    this.dgTeamTask.Items[i].Cells[8].Text=rcode;
					ds.Tables[0].Rows[i][7]=this.dgTeamTask.Items[i].Cells[7].Text;
					ds.Tables[0].Rows[i][8]=this.dgTeamTask.Items[i].Cells[8].Text;
				}
				rd.Close();
				ds.Tables[0].AcceptChanges();
				Session["dgTeamTask"]=ds;
			}
			con.Close(); 
		}
		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.calFrom.DateChanged += new eWorld.UI.DateChangedEventHandler(this.calFrom_DateChanged);
			this.CalTo.DateChanged += new eWorld.UI.DateChangedEventHandler(this.CalTo_DateChanged);
			this.imgNext1.Click += new System.Web.UI.ImageClickEventHandler(this.imgNext1_Click);
			this.ddTeamType.SelectedIndexChanged += new System.EventHandler(this.ddTeamType_SelectedIndexChanged);
			this.ddDepartment.SelectedIndexChanged += new System.EventHandler(this.ddDepartment_SelectedIndexChanged);
			this.btnSearch.Click += new System.EventHandler(this.btnSearch_Click);
			this.btnAddToProject.Click += new System.EventHandler(this.btnAddToProject_Click);
			this.dgTeamFinal.CancelCommand += new System.Web.UI.WebControls.DataGridCommandEventHandler(this.dgTeamFinal_CancelCommand);
			this.dgTeamFinal.EditCommand += new System.Web.UI.WebControls.DataGridCommandEventHandler(this.dgTeamFinal_EditCommand);
			this.dgTeamFinal.UpdateCommand += new System.Web.UI.WebControls.DataGridCommandEventHandler(this.dgTeamFinal_UpdateCommand);
			this.dgTeamFinal.DeleteCommand += new System.Web.UI.WebControls.DataGridCommandEventHandler(this.dgTeamFinal_DeleteCommand);
			this.dgTeamFinal.SelectedIndexChanged += new System.EventHandler(this.dgTeamFinal_SelectedIndexChanged);
			this.ImageButton1.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton1_Click);
			this.imgNext2.Click += new System.Web.UI.ImageClickEventHandler(this.imgNext2_Click);
			this.dgTeamTask.SelectedIndexChanged += new System.EventHandler(this.dgTeamTask_SelectedIndexChanged);
			this.CaltFrom.DateChanged += new eWorld.UI.DateChangedEventHandler(this.CaltFrom_DateChanged);
			this.CalTto.DateChanged += new eWorld.UI.DateChangedEventHandler(this.CalTto_DateChanged);
			this.btnAddToTask.Click += new System.EventHandler(this.btnAddToTask_Click);
			this.dgTask.DeleteCommand += new System.Web.UI.WebControls.DataGridCommandEventHandler(this.dgTask_DeleteCommand);
			this.dgTask.SelectedIndexChanged += new System.EventHandler(this.dgTask_SelectedIndexChanged);
			this.ddYesNo.SelectedIndexChanged += new System.EventHandler(this.ddYesNo_SelectedIndexChanged);
			this.btnCreate.Click += new System.EventHandler(this.btnCreate_Click);
			this.imgBack.Click += new System.Web.UI.ImageClickEventHandler(this.imgBack_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		private void imgNext1_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			this.pnlStep1.Visible=false;
			this.pnlStep2.Visible=true;
			this.pnlStep3.Visible=false;
            dgTeam.Visible=true;
			if(this.lblTeamLead.Text.Length==0)
			{
			 this.lblTeamLead.Text=GetMyName(Session["user_id"].ToString());
			}
			if(dgTeam.Items.Count<=0)
			{
			 FillTeam(1);
			}
			GetProjectTeam("PR-1");
            if(this.dgTeamFinal.Items.Count>0)
			{
				r1.Visible=true;
				this.dgTeamFinal.Visible=true;
				DisabledTeamMember();
			}
		}

		private void calFrom_DateChanged(object sender, System.EventArgs e)
		{
			this.CalTo.LowerBoundDate=this.calFrom.SelectedDate.Date;
			lblwDays.Text=WorkingDays(calFrom.SelectedDate,CalTo.SelectedDate).ToString();
		}

		private void ImageButton1_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			this.pnlStep1.Visible=true;
			this.pnlStep2.Visible=false;
			this.pnlStep3.Visible=false;
			dgTeam.Visible=false; 
		}
		public string GetMyName(string PCode)
		{
		 con.Open();
		 SqlCommand cmd=new SqlCommand("select name from t_employee where pcode='"+PCode.Trim()+"'",con);
		 SqlDataReader rd=cmd.ExecuteReader();
		 rd.Read();
		 string name=rd[0].ToString();
		 con.Close();
		 con.Dispose();
		 return name;
		}
		public void FillTeam(int state)
		{
			if(state==1)
			{
				MyTeam t=new MyTeam();
				ArrayList list=t.getDirectTeamWithTeamLead(Session["user_id"].ToString());
				DataTable td=new DataTable("MyTable");
				td.Columns.Add("Select");
				td.Columns.Add("Pcode");
				td.Columns.Add("Pic");
				td.Columns.Add("name");
				td.Columns.Add("Role");
				DataRow r=null;
				ArrayList tempList=new ArrayList();
				for(int j=0;j<list.Count;j++)
				{
					string member=(string)list[j];
					string []memberStr=member.Split('^');
					tempList.Add(memberStr[2]);
				}
				tempList.Sort();
				ArrayList _list=new ArrayList();
				for(int k=0;k<tempList.Count;k++)
				{
					string members=(string)tempList[k];
					for(int z=0;z<list.Count;z++)
					{
						string member=(string)list[z];
						string []memberStr=member.Split('^');
						if(memberStr[2].Equals(members))
						{
							_list.Add(list[z]);
						} 
					}			  
				}
				for(int i=0;i<_list.Count;i++)
				{
					string str=(string)_list[i];
					string []strQ=str.Split('^');
					r=td.NewRow();
					r[1]=strQ[0];
					r[2]=@"<img src=../empthumbnail/"+strQ[0].Trim()+".jpg border=0 width=60>";
					r[3]=strQ[2];
					td.Rows.Add(r);
				}
				DataSet ds=new DataSet("myDataSet");
				ds.Tables.Add(td);
				dgTeam.DataSource=ds;
				dgTeam.DataBind();
			}
			if(state==2)
			{
				MyTeam t=new MyTeam();
				ArrayList list=t.getBoss(Session["user_id"].ToString(),1);
				DataTable td=new DataTable("MyTable");
				td.Columns.Add("Select");
				td.Columns.Add("Pcode");
				td.Columns.Add("Pic");
				td.Columns.Add("name");
				td.Columns.Add("Role");
				DataRow r=null;
				ArrayList tempList=new ArrayList();
				for(int j=0;j<list.Count;j++)
				{
					string member=(string)list[j];
					string []memberStr=member.Split('^');
					tempList.Add(memberStr[2]);
				}
				tempList.Sort();
				ArrayList _list=new ArrayList();
				for(int k=0;k<tempList.Count;k++)
				{
					string members=(string)tempList[k];
					for(int z=0;z<list.Count;z++)
					{
						string member=(string)list[z];
						string []memberStr=member.Split('^');
						if(memberStr[2].Equals(members))
						{
							_list.Add(list[z]);
						} 
					}			  
				}
				for(int i=0;i<_list.Count;i++)
				{
					string str=(string)_list[i];
					string []strQ=str.Split('^');
					r=td.NewRow();
					r[1]=strQ[0];
					r[2]=@"<img src=../empthumbnail/"+strQ[0].Trim()+".jpg border=0 width=60>";
					r[3]=strQ[2];
					td.Rows.Add(r);
				}
				DataSet ds=new DataSet("myDataSet");
				ds.Tables.Add(td);
				dgTeam.DataSource=ds;
				dgTeam.DataBind();
			}
		}

		private void ddTeamType_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			if(this.ddTeamType.SelectedValue=="1" || this.ddTeamType.SelectedValue=="2")
			{
				cRow.Visible=false;
				DataSet ds=null;
				dgTeam.DataSource=ds;
				dgTeam.DataBind();
				FillTeam(Int32.Parse(this.ddTeamType.SelectedItem.Value));
			}
			if(this.ddTeamType.SelectedValue=="3")
			{
				cRow.Visible=true;
				DataSet ds=null;
				dgTeam.DataSource=ds;
				dgTeam.DataBind();
			}
			DisabledTeamMember();
		}
		public void DisabledTeamMember()
		{
			for(int i=0;i<this.dgTeamFinal.Items.Count;i++)
			{
				string fTeam=this.dgTeamFinal.Items[i].Cells[2].Text.Trim();
				for(int j=0;j<this.dgTeam.Items.Count;j++)
				{
					if(this.dgTeam.Items[j].Cells[1].Text.Trim().Equals(fTeam))
					{
						this.dgTeam.Items[j].Enabled=false;
					}
				}
			}
		}
		public void EnabledTeamMember(string pcode)
		{
			for(int j=0;j<this.dgTeam.Items.Count;j++)
			{
				if(this.dgTeam.Items[j].Cells[1].Text.Trim().Equals(pcode.Trim()))
				{
					this.dgTeam.Items[j].Enabled=true;
				}
			}
		}
		public DataSet SearchEmployee(string pcode, string name, string dept, string desg, string station)
		{
			try
			{
				con.Open();
				string sql="SELECT '<img src=../EmpThumbnail/'+emp.pic+ ' width=60>'as pic,emp.pcode, emp.name " +
					" FROM dbo.t_Employee emp INNER JOIN " +
					" dbo.t_Designation desg ON emp.desigid = desg.desigid INNER JOIN " +
					" dbo.t_Department dept ON desg.deptid = dept.deptid " +
					" WHERE (emp.del = 1) AND (emp.name LIKE '%" + name + "%')  " ;
			
				if (pcode!="")
					sql+="AND (emp.pcode LIKE '" + pcode + "') ";

				if (dept!="0")
					sql+=" AND (dept.deptid = " + dept + ")";
			
				if (desg!="0")
					sql+=" AND (desg.desigid = " + desg + ") ";

				if (station!="0")
					sql+=" AND (emp.station = " + station + ") " ;

				sql+=" ORDER BY emp.name ";

				SqlDataAdapter da = new SqlDataAdapter(sql,con);
				DataSet ds = new DataSet();
				da.Fill(ds);
				con.Close();
				return ds;

			}
			catch(Exception ex)
			{
			 Response.Write("Exception Occured "+ex.Message);
			 con.Close();
			 return null;
			}
		}

		private void CalTo_DateChanged(object sender, System.EventArgs e)
		{
			lblwDays.Text=WorkingDays(calFrom.SelectedDate,CalTo.SelectedDate).ToString();
	    }
		public int WorkingDays(DateTime from,DateTime to)
		{
		 TimeSpan s=to-from;
		 int tdays=s.Days;
			for(int i=0;i<=s.TotalDays;i++)
			{
				DateTime d=from.AddDays(i);
				if(DayOfWeek.Sunday.Equals(d.DayOfWeek))
				{
                 tdays-=1;  
				}
			}
		 return tdays;
		}

		private void btnAddToProject_Click(object sender, System.EventArgs e)
		{
			r1.Visible=true;
			if(this.btnAddToProject.Text=="Add To Project")
			{
				if(this.dgTeamFinal.Items.Count==0)
				{
					DataTable td=new DataTable("MyTable");
					td.Columns.Add("Delete");
					td.Columns.Add("Pcode");
					td.Columns.Add("Pic");
					td.Columns.Add("name");
					td.Columns.Add("Role");
					td.Columns.Add("teamid");
					DataRow r=null;
					DataSet ds=new DataSet();
					for(int i=0;i<dgTeam.Items.Count;i++)
					{
						CheckBox chk=(CheckBox)dgTeam.Items[i].FindControl("chkSelect");
						if(chk.Checked)
						{
							r=td.NewRow();
							r[1]=dgTeam.Items[i].Cells[1].Text;
							r[2]=dgTeam.Items[i].Cells[2].Text;
							r[3]=dgTeam.Items[i].Cells[3].Text;
							TextBox t=(TextBox)dgTeam.Items[i].FindControl("txtRole");
							r[4]=t.Text;
							r["teamid"]=0;
							td.Rows.Add(r);
						}
					}
					ds.Tables.Add(td);
					dgTeamFinal.DataSource=ds;
					dgTeamFinal.DataBind();
					dgTeamFinal.Visible=true;
					Session["dgTeamFinal"]=ds;
				}
				else
				{
					DataSet ds=(DataSet)Session["dgTeamFinal"];
					DataRow r=null;
					DataTable td=ds.Tables[0];
					for(int i=0;i<dgTeam.Items.Count;i++)
					{
						CheckBox chk=(CheckBox)dgTeam.Items[i].FindControl("chkSelect");
						if(chk.Checked)
						{
							r=td.NewRow();
							r[1]=dgTeam.Items[i].Cells[1].Text;
							r[2]=dgTeam.Items[i].Cells[2].Text;
							r[3]=dgTeam.Items[i].Cells[3].Text;
							TextBox t=(TextBox)dgTeam.Items[i].FindControl("txtRole");
							r[4]=t.Text;
							r["teamid"]=0;
							td.Rows.Add(r);
						}
					}
					ds.Tables.Clear();
					ds.Tables.Add(td);
					dgTeamFinal.DataSource=ds;
					dgTeamFinal.DataBind();
					dgTeamFinal.Visible=true;
					Session["dgTeamFinal"]=ds;
				}
			}
			else
			{
				DataSet ds=(DataSet)Session["dgTeamFinal"];
				DataRow r=null;
				DataTable td=ds.Tables[0];
				for(int i=0;i<dgTeam.Items.Count;i++)
				{
					CheckBox chk=(CheckBox)dgTeam.Items[i].FindControl("chkSelect");
					if(chk.Checked)
					{
						r=td.NewRow();
						r[1]=dgTeam.Items[i].Cells[1].Text;
						r[2]=dgTeam.Items[i].Cells[2].Text;
						r[3]=dgTeam.Items[i].Cells[3].Text;
						TextBox t=(TextBox)dgTeam.Items[i].FindControl("txtRole");
						r[4]=t.Text;
						r["teamid"]=0;
						td.Rows.Add(r);
					}
				}
				ds.Tables.Clear();
				ds.Tables.Add(td);
				dgTeamFinal.DataSource=ds;
				dgTeamFinal.DataBind();
				dgTeamFinal.Visible=true;
				Session["dgTeamFinal"]=ds;
			}
			DisabledTeamMember();
		}

		private void dgTeamFinal_DeleteCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{
			for(int i=0;i<this.dgTask.Items.Count;i++)
			{
			 string []team=this.dgTask.Items[i].Cells[8].Text.Split(',');
				for(int j=0;j<team.Length;j++)
				{
					if(team[j].Trim().ToString().Equals(this.dgTeamFinal.Items[e.Item.ItemIndex].Cells[2].Text.Trim()))
					{
					 Response.Write("<script>window.alert('Selected TeamMember cannot be deleted because task assigned on TeamMember. Please remove his/her task then delete it')</script>");
					 return;
					}
				}
			}
			for(int i=0;i<this.dgTeamTask.Items.Count;i++)
			{
				string []team=this.dgTeamTask.Items[i].Cells[8].Text.Split(',');
				for(int j=0;j<team.Length;j++)
				{
					if(team[j].Trim().ToString().Equals(this.dgTeamFinal.Items[e.Item.ItemIndex].Cells[2].Text.Trim()))
					{
						Response.Write("<script>window.alert('Selected TeamMember cannot be deleted because task assigned on TeamMember. Please remove his/her task then delete it')</script>");
						return;
					}
				}
			}
			 
			DataSet ds=(DataSet)Session["dgTeamFinal"];
			ds.Tables[0].Rows.RemoveAt(e.Item.ItemIndex);
			dgTeamFinal.DataSource=ds;
			dgTeamFinal.DataBind();
			Session["dgTeamFinal"]=ds;
			EnabledTeamMember(e.Item.Cells[2].Text);
		}

		private void imgNext2_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			this.CalTto.LowerBoundDate=this.CaltFrom.SelectedDate.Date;
			this.pnlStep1.Visible=false;
			this.pnlStep2.Visible=false;
			this.pnlStep3.Visible=true;
			if(this.dgTask.Items.Count>0)
			{
			 r2.Visible=true;
			 this.dgTask.Visible=true;
			}
			DataSet ds=(DataSet)Session["dgTeamFinal"];
			DataTable dt=ds.Tables[0];
			this.chkBoxTeam.Items.Clear();
			for(int i=0;i<dt.Rows.Count;i++)
			{
				DataRow r=dt.Rows[i];
				string name=@"<img src=../empthumbnail/"+r[1].ToString().Trim()+ ".jpg border=0 width=60>"+" "+r[3].ToString();
				ListItem itm=new ListItem(name,r[1].ToString().Trim());
				this.chkBoxTeam.Items.Add(itm);
			}
		}

		private void CaltFrom_DateChanged(object sender, System.EventArgs e)
		{
			this.CalTto.LowerBoundDate=this.CaltFrom.SelectedDate.Date;
			lblwDays.Text=WorkingDays(CaltFrom.SelectedDate,CalTto.SelectedDate).ToString();
		}

		private void CalTto_DateChanged(object sender, System.EventArgs e)
		{
			lblwtDays.Text=WorkingDays(CaltFrom.SelectedDate,CalTto.SelectedDate).ToString();
		}

		private void imgBack_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			this.pnlStep1.Visible=false;
			this.pnlStep2.Visible=true;
			this.pnlStep3.Visible=false;
			dgTeam.Visible=true;
			r2.Visible=false;
		}

		private void btnAddToTask_Click(object sender, System.EventArgs e)
		{
			r2.Visible=true;
			if(this.btnAddToTask.Text=="Add to Team Issue & Task Sheet")
			{
				if(this.dgTask.Items.Count==0)
				{
					DataTable td=new DataTable("MyTable");
					td.Columns.Add("Edit");
					td.Columns.Add("Delete");
					td.Columns.Add("Title");
					td.Columns.Add("Des");
					td.Columns.Add("Duration");
					td.Columns.Add("days");
					td.Columns.Add("weight");
					td.Columns.Add("team");
					td.Columns.Add("Pcode");
					DataRow r=null;
					DataSet ds=new DataSet();
					r=td.NewRow();
					r[2]=this.txtTitle.Text;
					r[3]="asdfsdfsadfs";
					r[4]=this.CaltFrom.SelectedDate.Date.ToString("d MMM,yyyy")+"-"+this.CalTto.SelectedDate.Date.ToString("d MMM,yyyy");
					r[5]=this.lblwtDays.Text;
					r[6]=this.txtWeightage.Text;
					string name="";
					string code="";
					for(int i=0;i<this.chkBoxTeam.Items.Count;i++)
					{
						if(this.chkBoxTeam.Items[i].Selected)
						{
							name+=this.chkBoxTeam.Items[i].Text+"<br>";
							code+=this.chkBoxTeam.Items[i].Value+",";
						}
					}
					if(code.Length>0)
					{
						code=code.Remove(code.Length-1,1);
					}
					r[7]=name;
					r[8]=code;
					td.Rows.Add(r);
					ds.Tables.Add(td);
					this.dgTask.DataSource=ds;
					this.dgTask.DataBind();
					this.dgTask.Visible=true;
					Session["dgTask"]=ds;
				}
				else
				{
					if(txtaHidden.Text.Length>0)
					{
						DataSet ds=(DataSet)Session["dgTask"];
						ds.Tables[0].Rows.RemoveAt(Int32.Parse(txtaHidden.Text));
						dgTask.DataSource=ds;
						dgTask.DataBind();
						Session["dgTask"]=ds;
						txtaHidden.Text="";
						//====================Add New Row=======================//
						ds=(DataSet)Session["dgTask"];
						DataRow r=null;
						DataTable td=ds.Tables[0];
						r=td.NewRow();
						r[2]=this.txtTitle.Text;
						r[3]="asdfsdfsadfs";
						r[4]=this.CaltFrom.SelectedDate.Date.ToString("d MMM,yyyy")+"-"+this.CalTto.SelectedDate.Date.ToString("d MMM,yyyy");
						r[5]=this.lblwtDays.Text;
						r[6]=this.txtWeightage.Text;
						string name="";
						string code="";
						for(int i=0;i<this.chkBoxTeam.Items.Count;i++)
						{
							if(this.chkBoxTeam.Items[i].Selected)
							{
								name+=this.chkBoxTeam.Items[i].Text+"<br>";
								code+=this.chkBoxTeam.Items[i].Value+",";
							}
						}
						code=code.Remove(code.Length-1,1);
						r[7]=name;
						r[8]=code;
						td.Rows.Add(r);
						ds.Tables.Clear();
						ds.Tables.Add(td);
						this.dgTask.DataSource=ds;
						this.dgTask.DataBind();
						this.dgTask.Visible=true;
						Session["dgTask"]=ds;
						//=====================================================//
					}
					else
					{
						DataSet ds=(DataSet)Session["dgTask"];
						DataRow r=null;
						DataTable td=ds.Tables[0];
						r=td.NewRow();
						r[2]=this.txtTitle.Text;
						r[3]="asdfsdfsadfs";
						r[4]=this.CaltFrom.SelectedDate.Date.ToString("d MMM,yyyy")+"-"+this.CalTto.SelectedDate.Date.ToString("d MMM,yyyy");
						r[5]=this.lblwtDays.Text;
						r[6]=this.txtWeightage.Text;
						string name="";
						string code="";
						for(int i=0;i<this.chkBoxTeam.Items.Count;i++)
						{
							if(this.chkBoxTeam.Items[i].Selected)
							{
								name+=this.chkBoxTeam.Items[i].Text+"<br>";
								code+=this.chkBoxTeam.Items[i].Value+",";
							}
						}
						if(code.Length>0)
						{
							code=code.Remove(code.Length-1,1);
						}
						r[7]=name;
						r[8]=code;
						td.Rows.Add(r);
						ds.Tables.Clear();
						ds.Tables.Add(td);
						this.dgTask.DataSource=ds;
						this.dgTask.DataBind();
						this.dgTask.Visible=true;
						Session["dgTask"]=ds;
					}
				}
			}
			else
			{
				int rIndx=-1;
				DataSet ds=(DataSet)Session["dgTeamTask"];
				DataRow r=null;
				DataTable td=ds.Tables[0];
				for(int i=0;i<td.Rows.Count;i++)
				{
					string hid=txtHidden.Text;
					string gid=td.Rows[i][2].ToString();
					string text=td.Rows[i][7].ToString();
					string cp=td.Rows[i][8].ToString();
					if(txtHidden.Text.Equals(td.Rows[i][2].ToString()))
					{
					  rIndx=i;
					  r=td.Rows[i];
					}
				}
				r[0]=this.txtTitle.Text;
				r[1]="abjbjbjsbd";
				r[3]=this.CaltFrom.SelectedDate.Date.ToString("d MMM,yyyy");
				r[4]=this.CalTto.SelectedDate.Date.ToString("d MMM,yyyy");
				r[5]=this.lblwtDays.Text;
				r[6]=this.txtWeightage.Text;
				string name="";
				string code="";
				for(int i=0;i<this.chkBoxTeam.Items.Count;i++)
				{
					if(this.chkBoxTeam.Items[i].Selected)
					{
						name+="<a>"+this.chkBoxTeam.Items[i].Text+"</a><br>";
						code+=this.chkBoxTeam.Items[i].Value.Trim()+",";
					}
				}
				if(code.Length>0)
				{
					code=code.Remove(code.Length-1,1);
				}
				r[7]=name;
				r[8]=code;
				//td.Rows[rIndx]=r;
				txtHidden.Text="";
				td.AcceptChanges();
				//ds.Tables.Clear();
				//ds.Tables.Add(td);
				this.dgTeamTask.DataSource=ds;
				this.dgTeamTask.DataBind();
				for(int i=0;i<ds.Tables[0].Rows.Count;i++)
				{
					DataRow rm=ds.Tables[0].Rows[i];
					this.dgTeamTask.Items[i].Cells[7].Text=rm[7].ToString();
					this.dgTeamTask.Items[i].Cells[8].Text=rm[8].ToString();
				}
				Session["dgTeamTask"]=ds;
				this.btnAddToTask.Text="Add to Team Issue & Task Sheet";
			}
			this.txtTitle.Text="";
			this.CaltFrom.Reset();
			this.CalTto.Reset();
			this.lblwtDays.Text="";
			this.txtWeightage.Text="";
			for(int i=0;i<this.chkBoxTeam.Items.Count;i++)
			{
				this.chkBoxTeam.Items[i].Selected=false;
			}
		}

		private void ddYesNo_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			if(this.ddYesNo.SelectedValue=="1")
			{
			 TD1.Visible=true;
			 this.chkLine.Items.Clear();
			 MyTeam t=new MyTeam();
			 ArrayList list=t.getBoss(Session["user_id"].ToString(),1);
				for(int i=0;i<list.Count;i++)
				{
					string members=(string)list[i];
					string []StrMem=members.Split('^');
					chkLine.Items.Add(new ListItem(StrMem[2],StrMem[0])); 
				}
			}
			else
			{
			 TD1.Visible=false;
			}
		}

		private void btnCreate_Click(object sender, System.EventArgs e)
		{
			try
			{
				string pId="PR-1";
				con.Open();
				SqlCommand cmd=new SqlCommand("update t_geoprojects set title=@title,description=@description,scope=@scope,objectives=@objectives,deliverables=@deliverables,ptype=@ptype,durationfrom=@durationfrom,durationto=@durationto,totalworkingdays=@totalworkingdays,cost=@cost,tdev=@tdev,qcircle=@qcircle,imp=@imp where pid=@pid",con);
				SqlParameter _pid=new SqlParameter("@pid",SqlDbType.VarChar);
				SqlParameter _title=new SqlParameter("@title",SqlDbType.VarChar);
				SqlParameter _description=new SqlParameter("@description",SqlDbType.Text);
				SqlParameter _scope=new SqlParameter("@scope",SqlDbType.Text);
				SqlParameter _objectives=new SqlParameter("@objectives",SqlDbType.Text);
				SqlParameter _deliverables=new SqlParameter("@deliverables",SqlDbType.Text);
				SqlParameter _ptype=new SqlParameter("@ptype",SqlDbType.Int);
				SqlParameter _durationfrom=new SqlParameter("@durationfrom",SqlDbType.DateTime);
				SqlParameter _durationto=new SqlParameter("@durationto",SqlDbType.DateTime);
				SqlParameter _wdays=new SqlParameter("@totalworkingdays",SqlDbType.Int);
				SqlParameter _cost=new SqlParameter("@cost",SqlDbType.Money);
				SqlParameter _tdev=new SqlParameter("@tdev",SqlDbType.Text);
				SqlParameter _qcircle=new SqlParameter("@qcircle",SqlDbType.Text);
				SqlParameter _imp=new SqlParameter("@imp",SqlDbType.Text);
				SqlParameter _status=new SqlParameter("@status",SqlDbType.Int);
				_pid.Value=pId;
				_title.Value=this.txtMainTitle.Text;
				_description.Value=this.txtDescription.Text;
				_scope.Value=this.txtScope.Text;
				_objectives.Value=this.txtObjectives.Text;
				_deliverables.Value=this.txtDeliverables.Text;
				_ptype.Value=this.ddProjectType.SelectedValue.ToString();
				_durationfrom.Value=this.calFrom.SelectedDate.Date.ToString("d MMM,yyyy");
				_durationto.Value=this.CalTo.SelectedDate.Date.ToString("d MMM,yyyy");
				_wdays.Value=this.lblwDays.Text;
				_cost.Value=this.txtCost.Text;
				_tdev.Value=this.txtTecDevlop.Text;
				_qcircle.Value=this.txtQC.Text;
				_imp.Value=this.txtImp.Text;
				cmd.Parameters.Add(_pid);
				cmd.Parameters.Add(_title);
				cmd.Parameters.Add(_description);
				cmd.Parameters.Add(_scope);
				cmd.Parameters.Add(_objectives);
				cmd.Parameters.Add(_deliverables);
				cmd.Parameters.Add(_ptype);
				cmd.Parameters.Add(_durationfrom);
				cmd.Parameters.Add(_durationto);
				cmd.Parameters.Add(_wdays);
				cmd.Parameters.Add(_cost);
				cmd.Parameters.Add(_tdev);
				cmd.Parameters.Add(_qcircle);
				cmd.Parameters.Add(_imp);
				cmd.ExecuteNonQuery();
				
				cmd=new SqlCommand("delete from t_geoprojectsteam where pid='"+pId+"'",con);
				cmd.ExecuteNonQuery();
				for(int i=0;i<this.dgTeamFinal.Items.Count;i++)
				{
					cmd=new SqlCommand("insert into t_geoprojectsteam(pid,team,role,isactive) values(@pid,@team,@role,@isactive)",con);
				    SqlParameter _pteamid=new SqlParameter("@pid",SqlDbType.VarChar);
					SqlParameter _team=new SqlParameter("@team",SqlDbType.Char);
					SqlParameter _role=new SqlParameter("@role",SqlDbType.Text);
					SqlParameter _isactive=new SqlParameter("@isactive",SqlDbType.Int);
					_pteamid.Value=pId;
					_team.Value=this.dgTeamFinal.Items[i].Cells[2].Text.Trim();
					_role.Value=this.dgTeamFinal.Items[i].Cells[5].Text.Trim();
					_isactive.Value=1;
					cmd.Parameters.Add(_pteamid);
					cmd.Parameters.Add(_team);
					cmd.Parameters.Add(_role);
					cmd.Parameters.Add(_isactive);
					cmd.ExecuteNonQuery();
				}
				
				for(int j=0;j<this.dgTeamTask.Items.Count;j++)
				{
					ArrayList list=new ArrayList();
					string tid=this.dgTeamTask.Items[j].Cells[2].Text;
					//======================================DeActive Past TeamMember==========================//
				    cmd=new SqlCommand("select team from t_geoprojectsteamtasks where pid='"+pId+"' and tid="+tid+" and isactive=1",con);
					SqlDataReader rd=cmd.ExecuteReader();
					while(rd.Read())
					{
					 list.Add(rd[0].ToString().Trim());
					}
					rd.Close();
					if(list.Count>0)
					{
						for(int k=0;k<list.Count;k++)
						{
						 string []teamm=this.dgTeamTask.Items[j].Cells[8].Text.Split(',');
						 int count =0;
							for(int z=0;z<teamm.Length;z++)
							{
							 string oldVal=(string)list[k];
							 string Val=teamm[z];
								if(oldVal!=Val)
								{
								 count++;
								}
							}
							if(count==teamm.Length)
							{
							 cmd=new SqlCommand("update t_geoprojectsteamtasks set isactive=0 where tid='"+tid+"' and team='"+list[k]+"'",con);
							 cmd.ExecuteNonQuery();
							}
						}
					}
				    //=========================================================================================//
					cmd=new SqlCommand("update t_geoprojectstask set title=@title,tdes=@tdes,durationfrom=@durationfrom,durationto=@durationto,totalworkingdays=@totalworkingdays,weightage=@weightage where pid=@pid and tid=@tid",con);
					SqlParameter _tid=new SqlParameter("@tid",SqlDbType.Int);
					SqlParameter _tpid=new SqlParameter("@pid",SqlDbType.VarChar);
					SqlParameter _ttitle=new SqlParameter("@title",SqlDbType.VarChar);
					SqlParameter _tdes=new SqlParameter("@tdes",SqlDbType.Text);
					SqlParameter _tdfrm=new SqlParameter("@durationfrom",SqlDbType.DateTime);
					SqlParameter _tdto=new SqlParameter("@durationto",SqlDbType.DateTime);
					SqlParameter _twdays=new SqlParameter("@totalworkingdays",SqlDbType.Int);
					SqlParameter _wtage=new SqlParameter("@weightage",SqlDbType.Int);
					_tid.Value=tid;
					_tpid.Value=pId;
					_ttitle.Value=this.dgTeamTask.Items[j].Cells[0].Text;
					_tdes.Value=this.dgTeamTask.Items[j].Cells[1].Text;
					_tdfrm.Value=this.dgTeamTask.Items[j].Cells[3].Text;
					_tdto.Value=this.dgTeamTask.Items[j].Cells[4].Text;
					_twdays.Value=this.dgTeamTask.Items[j].Cells[5].Text;
					_wtage.Value=this.dgTeamTask.Items[j].Cells[6].Text;
					cmd.Parameters.Add(_tid);
					cmd.Parameters.Add(_tpid);
					cmd.Parameters.Add(_ttitle);
					cmd.Parameters.Add(_tdes);
					cmd.Parameters.Add(_tdfrm);
					cmd.Parameters.Add(_tdto);
					cmd.Parameters.Add(_twdays);
					cmd.Parameters.Add(_wtage);
					cmd.ExecuteNonQuery();
                       
					string []team=this.dgTeamTask.Items[j].Cells[8].Text.Split(',');
					for(int i=0;i<team.Length;i++)
					{
						string tVal=team[i];
						if(tVal.Length>0)
						{
							cmd=new SqlCommand("select * from t_geoprojectsteamtasks where pid='"+pId+"' and tid='"+tid+"' and team='"+team[i].Trim()+"' and isactive=1",con);
							rd=cmd.ExecuteReader();
							if(rd.HasRows)
							{
								rd.Close();
							}
							else
							{
								rd.Close();
								cmd=new SqlCommand("insert into t_geoprojectsteamtasks (pid,tid,team) values(@pid,@tid,@team)",con);
								SqlParameter _gpid=new SqlParameter("@pid",SqlDbType.VarChar);
								SqlParameter _gtid=new SqlParameter("@tid",SqlDbType.Int);
								SqlParameter _gteamid=new SqlParameter("@team",SqlDbType.Char);
								_gpid.Value=pId;
								_gtid.Value=tid;
								_gteamid.Value=team[i].Trim();
								cmd.Parameters.Add(_gpid);
								cmd.Parameters.Add(_gtid);
								cmd.Parameters.Add(_gteamid);
								cmd.ExecuteNonQuery();
							}
						}
					}
				}
				for(int j=0;j<this.dgTask.Items.Count;j++)
				{
				 string taskid=GenerateTaskID();
				 cmd=new SqlCommand("insert into t_geoprojectstask (tid,pid,title,tdes,durationfrom,durationto,totalworkingdays,weightage,status) values(@tid,@pid,@title,@tdes,@durationfrom,@durationto,@totalworkingdays,@weightage,@status)",con);
				 SqlParameter _tid=new SqlParameter("@tid",SqlDbType.Int);
				 SqlParameter _tpid=new SqlParameter("@pid",SqlDbType.VarChar);
				 SqlParameter _ttitle=new SqlParameter("@title",SqlDbType.VarChar);
 				 SqlParameter _tdes=new SqlParameter("@tdes",SqlDbType.Text);
				 SqlParameter _tdfrm=new SqlParameter("@durationfrom",SqlDbType.DateTime);
				 SqlParameter _tdto=new SqlParameter("@durationto",SqlDbType.DateTime);
				 SqlParameter _twdays=new SqlParameter("@totalworkingdays",SqlDbType.Int);
				 SqlParameter _wtage=new SqlParameter("@weightage",SqlDbType.Int);
				 SqlParameter _tstatus=new SqlParameter("@status",SqlDbType.Int);
				 _tid.Value=taskid;
				 _tpid.Value=pId;
				 _ttitle.Value=this.dgTask.Items[j].Cells[2].Text;
				 _tdes.Value=this.dgTask.Items[j].Cells[3].Text;
				 string []Date=this.dgTask.Items[j].Cells[4].Text.Split('-');
				 _tdfrm.Value=Date[0];
				 _tdto.Value=Date[1];
				 _twdays.Value=this.dgTask.Items[j].Cells[5].Text;
				 _wtage.Value=this.dgTask.Items[j].Cells[6].Text;
				 _tstatus.Value=1;
				 cmd.Parameters.Add(_tid);
				 cmd.Parameters.Add(_tpid);
				 cmd.Parameters.Add(_ttitle);
				 cmd.Parameters.Add(_tdes);
				 cmd.Parameters.Add(_tdfrm);
				 cmd.Parameters.Add(_tdto);
				 cmd.Parameters.Add(_twdays);
				 cmd.Parameters.Add(_wtage);
				 cmd.Parameters.Add(_tstatus);
				 cmd.ExecuteNonQuery();
    			    string []team=this.dgTask.Items[j].Cells[8].Text.Split(',');
					for(int i=0;i<team.Length;i++)
					{
					 cmd=new SqlCommand("insert into t_geoprojectsteamtasks (pid,tid,team) values(@pid,@tid,@team)",con);
					 SqlParameter _gpid=new SqlParameter("@pid",SqlDbType.VarChar);
					 SqlParameter _gtid=new SqlParameter("@tid",SqlDbType.Int);
					 SqlParameter _gteamid=new SqlParameter("@team",SqlDbType.Char);
					 _gpid.Value=pId;
					 _gtid.Value=taskid;
					 _gteamid.Value=team[i].Trim();
					 cmd.Parameters.Add(_gpid);
					 cmd.Parameters.Add(_gtid);
					 cmd.Parameters.Add(_gteamid);
					 cmd.ExecuteNonQuery();
					}
				}
				if(this.ddYesNo.SelectedValue=="1" && this.ddYesNo.Enabled)
				{
					for(int i=0;i<this.chkLine.Items.Count;i++)
					{
						if(this.chkLine.Items[i].Selected)
						{
						 cmd=new SqlCommand("insert into t_geoprojectssupervisor (pid,supervisor) values(@pid,@supervisor)",con);
						 SqlParameter _gpsid=new SqlParameter("@pid",SqlDbType.Char);
						 SqlParameter _sid=new SqlParameter("@supervisor",SqlDbType.Char);
						 _gpsid.Value=pId;
						 _sid.Value=this.chkLine.Items[i].Value.ToString().Trim();
						 cmd.Parameters.Add(_gpsid);
						 cmd.Parameters.Add(_sid);
						 cmd.ExecuteNonQuery();
						}
					}
				}
				con.Close();
				con.Dispose();
			}
			catch(Exception eX)
			{
			 Response.Write("Exception Occured: "+eX.Message);
			 con.Close();
			 con.Dispose();
			}
		}
		public string GenerateTeamID()
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			SqlCommand cmd=new SqlCommand("SELECT MAX(teamid) + 1 AS TeamID FROM dbo.t_GeoProjectsTeam",conn);
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			if(rd.HasRows && rd[0].ToString()!="")
			{
				string ID=rd[0].ToString();
				rd.Close();
				conn.Close();
				return ID;
			}
			else
			{
				rd.Close();
				conn.Close();
				return "1";
			}
		}
		public string GenerateTaskID()
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			SqlCommand cmd=new SqlCommand("SELECT MAX(tid) + 1 AS TaskID FROM dbo.t_GeoProjectsTask",conn);
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			if(rd.HasRows && rd[0].ToString()!="")
			{
				string ID=rd[0].ToString();
				rd.Close();
				conn.Close();
				return ID;
			}
			else
			{
				rd.Close();
				conn.Close();
				return "1";
			}
		}
		public void getDepartments()
		{
			con.Open();
			SqlCommand cmd=new SqlCommand("select deptid,deptname from t_department where status=1 order by deptname",con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
				this.ddDepartment.Items.Add(itm);
			}
			rd.Close();
			con.Close();
		}
		public void getStation()
		{
			con.Open();
			SqlCommand cmd=new SqlCommand("select cityid,cityname from t_city where status=1 order by cityname",con); 
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
				this.ddStation.Items.Add(itm);
			}
			rd.Close();
			con.Close();
		}

		private void ddDepartment_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			this.ddDesignation.Items.Clear();
			ListItem item=new ListItem("Select--Designation","0");
			this.ddDesignation.Items.Add(item);
			con.Open();
			SqlCommand cmd=new SqlCommand("select d.desigid,d.designation from t_designation d,t_department dept where dept.status=1 And d.status=1 And d.deptid=dept.deptid And dept.deptid="+ this.ddDepartment.SelectedValue.ToString() +" order by d.designation",con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
				this.ddDesignation.Items.Add(itm); 
			}
			rd.Close();
			con.Close();
		}

		private void btnSearch_Click(object sender, System.EventArgs e)
		{
			DataSet ds=SearchEmployee(this.txtpCode.Text.Trim(),this.txtName.Text.Trim(),this.ddDepartment.SelectedItem.Value,this.ddDesignation.SelectedValue,ddStation.SelectedValue);
			dgTeam.DataSource=ds;
			dgTeam.DataBind();
			DisabledTeamMember();
		}

		private void dgTeamFinal_SelectedIndexChanged(object sender, System.EventArgs e)
		{
//			this.btnAddToProject.Text="Edit TeamMember";
//			for(int i=0;i<this.dgTeam.Items.Count;i++)
//			{
//			 string pcode=this.dgTeamFinal.Items[this.dgTeamFinal.SelectedIndex].Cells[2].Text;
//			 string role=this.dgTeamFinal.Items[this.dgTeamFinal.SelectedIndex].Cells[5].Text;
//			 txthidden.Text=this.dgTeamFinal.SelectedIndex.ToString();
//				if(this.dgTeam.Items[i].Cells[1].Text.Trim().Equals(pcode.Trim()))
//				{
//					this.dgTeam.Items[i].Enabled=true;
//					CheckBox chk=(CheckBox)this.dgTeam.Items[i].FindControl("chkSelect");
//					chk.Checked=true;
//					TextBox t=(TextBox)this.dgTeam.Items[i].FindControl("txtRole");
//					t.Text=role;
//				}
//				else
//				{
//					this.dgTeam.Items[i].Enabled=false;
//					CheckBox chk=(CheckBox)this.dgTeam.Items[i].FindControl("chkSelect");
//					chk.Checked=false;
//				}
//			}
		}

		private void dgTeamFinal_EditCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{
			this.dgTeamFinal.EditItemIndex = e.Item.ItemIndex;
			DataSet ds=(DataSet)Session["dgTeamFinal"];
			DataTable td=ds.Tables[0];
			dgTeamFinal.DataSource=ds;
			dgTeamFinal.DataBind();
			dgTeamFinal.Visible=true;
			Session["dgTeamFinal"]=ds;
     	}

		private void dgTeamFinal_CancelCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{
			this.dgTeamFinal.EditItemIndex = -1;
			DataSet ds=(DataSet)Session["dgTeamFinal"];
			DataTable td=ds.Tables[0];
			dgTeamFinal.DataSource=ds;
			dgTeamFinal.DataBind();
			dgTeamFinal.Visible=true;
			Session["dgTeamFinal"]=ds;
		}

		private void dgTeamFinal_UpdateCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{
//			System.Web.UI.WebControls.TextBox cName = new System.Web.UI.WebControls.TextBox();
//			cName = (System.Web.UI.WebControls.TextBox) e.Item.Cells[1].Controls[0];
			TextBox cName=(TextBox)this.dgTeamFinal.Items[e.Item.ItemIndex].FindControl("TextBox1");
			DataSet ds=(DataSet)Session["dgTeamFinal"];
			DataTable td=ds.Tables[0];
			DataRow r=td.Rows[e.Item.ItemIndex];
			//string Val=r[4].ToString();
			r[4]=cName.Text;
			td.Rows[e.Item.ItemIndex][4]=cName.Text;
			td.AcceptChanges();
			ds.Tables.Clear();
			ds.Tables.Add(td);
			this.dgTeamFinal.EditItemIndex = -1;
			dgTeamFinal.DataSource=ds;
			dgTeamFinal.DataBind();
			dgTeamFinal.Visible=true;
			Session["dgTeamFinal"]=ds;

		}

		private void dgTeamTask_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			for(int i=0;i<this.chkBoxTeam.Items.Count;i++)
			{
			 this.chkBoxTeam.Items[i].Selected=false;
			}
			this.txtTitle.Text=this.dgTeamTask.Items[this.dgTeamTask.SelectedIndex].Cells[0].Text;
			string tid=this.dgTeamTask.Items[this.dgTeamTask.SelectedIndex].Cells[2].Text;
			CaltFrom.SelectedDate=DateTime.Parse(this.dgTeamTask.Items[this.dgTeamTask.SelectedIndex].Cells[3].Text);
			CalTto.SelectedDate=DateTime.Parse(this.dgTeamTask.Items[this.dgTeamTask.SelectedIndex].Cells[4].Text);
			this.lblwtDays.Text=this.dgTeamTask.Items[this.dgTeamTask.SelectedIndex].Cells[5].Text;
			this.txtWeightage.Text=this.dgTeamTask.Items[this.dgTeamTask.SelectedIndex].Cells[6].Text;
//			con.Open();
//			SqlCommand cmd=new SqlCommand("SELECT dbo.t_GeoProjectsTeamTasks.tid, dbo.t_GeoProjectsTeamTasks.team, dbo.t_Employee.name "+
//            "FROM dbo.t_GeoProjectsTeamTasks INNER JOIN "+
//            "dbo.t_Employee ON dbo.t_GeoProjectsTeamTasks.team = dbo.t_Employee.pcode "+
//            "WHERE (dbo.t_GeoProjectsTeamTasks.tid ="+tid+")",con);
//			SqlDataReader rd=cmd.ExecuteReader();
//			while(rd.Read())
//			{
//			 string name=@"<img src=../empthumbnail/"+rd["team"].ToString().Trim()+ ".jpg border=0 width=60>"+" "+rd["name"].ToString();
//			 ListItem itm=new ListItem(name,rd["team"].ToString());
//				if(this.chkBoxTeam.Items.Contains(itm))
//				{
//				 int idx= this.chkBoxTeam.Items.IndexOf(itm);
//				 this.chkBoxTeam.Items[idx].Selected=true;
//				}
//
//			}
//			rd.Close();
//			con.Close();
			DataSet ds=(DataSet)Session["dgTeamTask"];
			for(int i=0;i<ds.Tables[0].Rows.Count;i++)
			{
				if(ds.Tables[0].Rows[i][2].ToString().Equals(tid))
				{
                string []pcode=ds.Tables[0].Rows[i][8].ToString().Split(',');
					for(int j=0;j<pcode.Length;j++)
					{
					 //string name=@"<img src=../empthumbnail/"+pcode[j].ToString().Trim()+ ".jpg border=0 width=60>"+" "+rd["name"].ToString();
					    string Val=pcode[j];
						ListItem itm=this.chkBoxTeam.Items.FindByValue(pcode[j]);
                        if(this.chkBoxTeam.Items.Contains(itm))
						{
							itm.Selected=true;
						 //this.chkBoxTeam.Items[j].Selected=true;
						}
					}
				}
			}
			this.btnAddToTask.Text="Update Task";
			txtHidden.Text=tid;
		}

		private void dgTask_DeleteCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{
		 
			DataSet ds=(DataSet)Session["dgTask"];
			ds.Tables[0].Rows.RemoveAt(e.Item.ItemIndex);
			dgTask.DataSource=ds;
			dgTask.DataBind();
			Session["dgTask"]=ds;
		}

		private void dgTask_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			this.txtTitle.Text=this.dgTask.Items[this.dgTask.SelectedIndex].Cells[2].Text;
			//this.tdes.Text=this.dgTask.Items[this.dgTask.SelectedIndex].Cells[3].Text;
			string []DateVal=this.dgTask.Items[this.dgTask.SelectedIndex].Cells[4].Text.Split('-');
			this.CaltFrom.SelectedDate=DateTime.Parse(DateVal[0]);
			this.CalTto.SelectedDate=DateTime.Parse(DateVal[1]);
			lblwtDays.Text=this.dgTask.Items[this.dgTask.SelectedIndex].Cells[5].Text;
			this.txtWeightage.Text=this.dgTask.Items[this.dgTask.SelectedIndex].Cells[6].Text;
			string []Code=this.dgTask.Items[this.dgTask.SelectedIndex].Cells[8].Text.Split(',');
			for(int i=0;i<Code.Length;i++)
			{
				string Val=Code[i];
				ListItem itm=this.chkBoxTeam.Items.FindByValue(Code[i].Trim());
				if(this.chkBoxTeam.Items.Contains(itm))
				{
					itm.Selected=true;
					//this.chkBoxTeam.Items[j].Selected=true;
				}
			}
			this.txtaHidden.Text=this.dgTask.SelectedIndex.ToString();
		}

	}
}
