<%@ Register TagPrefix="ew" Namespace="eWorld.UI" Assembly="eWorld.UI" %>
<%@ Page language="c#" Codebehind="DynamicReport.aspx.cs" AutoEventWireup="false" Inherits="GeoRabtaSite.DynamicReport" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<HTML>
	<HEAD>
		<title>Advance Report</title>
		<meta name="GENERATOR" content="Microsoft Visual Studio .NET 7.1">
		<meta name="CODE_LANGUAGE" content="C#">
		<meta name="vs_defaultClientScript" content="JavaScript">
		<meta name="vs_targetSchema" content="http://schemas.microsoft.com/intellisense/ie5">
		<LINK rel="stylesheet" type="text/css" href="main.css">
		<script language="javascript">

        // sort function - ascending (case-insensitive)
        function sortFuncAsc(record1, record2) {
            var value1 = record1.optText.toLowerCase();
            var value2 = record2.optText.toLowerCase();
            if (value1 > value2) return(1);
            if (value1 < value2) return(-1);
            return(0);
        }

        // sort function - descending (case-insensitive)
        function sortFuncDesc(record1, record2) {
            var value1 = record1.optText.toLowerCase();
            var value2 = record2.optText.toLowerCase();
            if (value1 > value2) return(-1);
            if (value1 < value2) return(1);
            return(0);
        }

        function sortSelect(ddlName, ascendingOrder) {
			selectToSort=document.getElementById(ddlName);
            if (arguments.length == 1) ascendingOrder = true;    // default to ascending sort

            // copy options into an array
            var myOptions = [];
            for (var loop=0; loop<selectToSort.options.length; loop++) {
                myOptions[loop] = { optText:selectToSort.options[loop].text, optValue:selectToSort.options[loop].value };
            }

            // sort array
            if (ascendingOrder) {
                myOptions.sort(sortFuncAsc);
            } else {
                myOptions.sort(sortFuncDesc);
            }

            // copy sorted options from array back to select box
            selectToSort.options.length = 0;
            for (var loop=0; loop<myOptions.length; loop++) {
                var optObj = document.createElement('option');
                optObj.text = myOptions[loop].optText;
                optObj.value = myOptions[loop].optValue;
                selectToSort.options.add(optObj);
            }
        }


		</script>
	</HEAD>
	<body onload="sortSelect('ddSearchAdvance',true)">
		<form id="Form1" method="post" runat="server">
			<TABLE id="Table4" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Station</TD>
					<TD><asp:dropdownlist id="ddStation" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
						</asp:dropdownlist></TD>
					<td width="95"><asp:dropdownlist id="ddStationOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<TD width="50"><asp:linkbutton id="Lnk4" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table2" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Designation</TD>
					<TD><asp:dropdownlist id="ddDesignation" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
						</asp:dropdownlist></TD>
					<td width="95"><asp:dropdownlist id="ddDesignationOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<TD width="50"><asp:linkbutton id="Lnk2" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table1" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Employee Code</TD>
					<TD><asp:dropdownlist id="ddCode" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtCodeLike" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator1" runat="server" ValidationExpression="^[a-z A-Z 0-9]*$"
							ControlToValidate="txtCodeLike" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<td width="95"><asp:dropdownlist id="ddCodeOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<TD width="50"><asp:linkbutton id="lnk1" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table3" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Name</TD>
					<TD><asp:dropdownlist id="ddName" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtNameLike" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator2" runat="server" ValidationExpression="^[a-z A-Z 0-9()]*$"
							ControlToValidate="txtNameLike" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<td width="95"><asp:dropdownlist id="ddNameOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<TD width="50"><asp:linkbutton id="Lnk3" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table5" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Date of Join (mm/dd/yyyy)</TD>
					<TD><asp:dropdownlist id="ddDoj" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is less than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is greater than or equal to</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><ew:calendarpopup id="txtDojLike" runat="server" Width="110px" EnableHideDropDown="True">
							<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></WeekdayStyle>
							<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Yellow"></MonthHeaderStyle>
							<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
								BackColor="AntiqueWhite"></OffMonthStyle>
							<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></GoToTodayStyle>
							<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="LightGoldenrodYellow"></TodayDayStyle>
							<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Orange"></DayHeaderStyle>
							<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="LightGray"></WeekendStyle>
							<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Yellow"></SelectedDateStyle>
							<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></ClearDateStyle>
							<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></HolidayStyle>
						</ew:calendarpopup></TD>
					<td width="95"><asp:dropdownlist id="ddDojOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<TD width="50"><asp:linkbutton id="Lnk5" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table6" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Confirmation Due Date</TD>
					<TD><asp:dropdownlist id="ddConfirmation" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is less than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is greater than or equal to</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><ew:calendarpopup id="txtConfirmation" runat="server" Width="110px" EnableHideDropDown="True">
							<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></WeekdayStyle>
							<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Yellow"></MonthHeaderStyle>
							<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
								BackColor="AntiqueWhite"></OffMonthStyle>
							<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></GoToTodayStyle>
							<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="LightGoldenrodYellow"></TodayDayStyle>
							<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Orange"></DayHeaderStyle>
							<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="LightGray"></WeekendStyle>
							<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Yellow"></SelectedDateStyle>
							<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></ClearDateStyle>
							<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></HolidayStyle>
						</ew:calendarpopup></TD>
					<td width="95"><asp:dropdownlist id="ddConfOrderby" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<TD width="50"><asp:linkbutton id="Lnk6" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table7" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Date of Exit</TD>
					<TD><asp:dropdownlist id="ddDoE" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is less than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is greater than or equal to</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><ew:calendarpopup id="txtDOE" runat="server" Width="110px" EnableHideDropDown="True">
							<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></WeekdayStyle>
							<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Yellow"></MonthHeaderStyle>
							<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
								BackColor="AntiqueWhite"></OffMonthStyle>
							<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></GoToTodayStyle>
							<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="LightGoldenrodYellow"></TodayDayStyle>
							<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Orange"></DayHeaderStyle>
							<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="LightGray"></WeekendStyle>
							<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Yellow"></SelectedDateStyle>
							<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></ClearDateStyle>
							<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></HolidayStyle>
						</ew:calendarpopup></TD>
					<td width="95"><asp:dropdownlist id="ddDoEOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<TD width="50"><asp:linkbutton id="Lnk7" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table8" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Category</TD>
					<TD><asp:dropdownlist id="ddCategoryChk" runat="server" Width="328px">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is less than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is greater than or equal to</asp:ListItem>
						</asp:dropdownlist><asp:dropdownlist id="ddCategory" runat="server" Width="152px">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
						</asp:dropdownlist></TD>
					<td width="95"><asp:dropdownlist id="ddCategoryOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<TD width="50"><asp:linkbutton id="Lnk8" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table9" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Date of Birth</TD>
					<TD><asp:dropdownlist id="ddDoB" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is less than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is greater than or equal to</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><ew:calendarpopup id="txtDoB" runat="server" Width="110px" EnableHideDropDown="True">
							<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></WeekdayStyle>
							<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Yellow"></MonthHeaderStyle>
							<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
								BackColor="AntiqueWhite"></OffMonthStyle>
							<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></GoToTodayStyle>
							<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="LightGoldenrodYellow"></TodayDayStyle>
							<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Orange"></DayHeaderStyle>
							<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="LightGray"></WeekendStyle>
							<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Yellow"></SelectedDateStyle>
							<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></ClearDateStyle>
							<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></HolidayStyle>
						</ew:calendarpopup></TD>
					<td width="95"><asp:dropdownlist id="ddDoBOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<TD width="50"><asp:linkbutton id="Lnk9" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table10" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Gender</TD>
					<TD><asp:dropdownlist id="ddGender" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="1">Male</asp:ListItem>
							<asp:ListItem Value="2">Female</asp:ListItem>
						</asp:dropdownlist></TD>
					<td width="95"><asp:dropdownlist id="ddGenderOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk10" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table11" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Blood Group</TD>
					<TD><asp:dropdownlist id="ddBloodGrp" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="1">A+</asp:ListItem>
							<asp:ListItem Value="2">A-</asp:ListItem>
							<asp:ListItem Value="3">B+</asp:ListItem>
							<asp:ListItem Value="4">B-</asp:ListItem>
							<asp:ListItem Value="5">AB+</asp:ListItem>
							<asp:ListItem Value="6">AB-</asp:ListItem>
							<asp:ListItem Value="7">O+</asp:ListItem>
							<asp:ListItem Value="8">O-</asp:ListItem>
						</asp:dropdownlist></TD>
					<td width="95"><asp:dropdownlist id="ddBloodGrpOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk11" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table12" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Passport #</TD>
					<TD><asp:dropdownlist id="ddPassport" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtPassport" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator3" runat="server" ValidationExpression="^[a-z A-Z 0-9 -]*$"
							ControlToValidate="txtPassport" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<td width="95"><asp:dropdownlist id="ddPassPortOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<TD width="50"><asp:linkbutton id="Lnk12" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table13" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">NIC (New)</TD>
					<TD><asp:dropdownlist id="ddNICNew" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtNICNew" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator4" runat="server" ValidationExpression="^[a-z A-Z 0-9 -]*$"
							ControlToValidate="txtNICNew" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<td width="95"><asp:dropdownlist id="ddNICNewOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk13" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table14" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">NIC (Old)</TD>
					<td><asp:dropdownlist id="ddNICOld" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:dropdownlist></td>
					<TD width="150"><asp:textbox id="txtNICOld" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator5" runat="server" ValidationExpression="^[a-z A-Z 0-9 -]*$"
							ControlToValidate="txtNICOld" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<td width="95"><asp:dropdownlist id="ddNICOldOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk14" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table15" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Address</TD>
					<TD><asp:dropdownlist id="ddAddress" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtAddress" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator6" runat="server" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'
							ControlToValidate="txtAddress" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<td width="95"><asp:dropdownlist id="ddAddressOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk15" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table16" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Extension</TD>
					<TD><asp:dropdownlist id="ddExtension" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtExtension" runat="server" Width="120px"></asp:textbox></TD>
					<td width="95"><asp:dropdownlist id="ddExtensionOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk16" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table17" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Employment Type</TD>
					<TD><asp:dropdownlist id="ddEmploymentType" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="1">Permanent</asp:ListItem>
							<asp:ListItem Value="2">Contractual</asp:ListItem>
							<asp:ListItem Value="3">Retainer</asp:ListItem>
							<asp:ListItem Value="4">Honorary</asp:ListItem>
							<asp:ListItem Value="5">Talent</asp:ListItem>
						</asp:dropdownlist></TD>
					<td width="95"><asp:dropdownlist id="ddEmploymentTypeOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk17" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table18" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Father's Name</TD>
					<TD><asp:dropdownlist id="ddFatherName" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtFatherName" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator7" runat="server" ValidationExpression="^[a-z A-Z 0-9()]*$"
							ControlToValidate="txtFatherName" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<td width="95"><asp:dropdownlist id="ddFatherNameOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk18" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table19" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Marital Status</TD>
					<TD><asp:dropdownlist id="ddMaritalStatus" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="1">Single</asp:ListItem>
							<asp:ListItem Value="2">Married</asp:ListItem>
							<asp:ListItem Value="3">Divorced</asp:ListItem>
							<asp:ListItem Value="4">Widow</asp:ListItem>
							<asp:ListItem Value="5">Separated</asp:ListItem>
						</asp:dropdownlist></TD>
					<td width="95"><asp:dropdownlist id="ddMaritalStatusOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk19" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table20" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Religion</TD>
					<TD><asp:dropdownlist id="ddReligion" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="1">Islam</asp:ListItem>
							<asp:ListItem Value="2">Christianity</asp:ListItem>
							<asp:ListItem Value="3">Buddhism</asp:ListItem>
							<asp:ListItem Value="4">Zoroastrian</asp:ListItem>
							<asp:ListItem Value="5">Jewish</asp:ListItem>
							<asp:ListItem Value="6">Hinduism</asp:ListItem>
							<asp:ListItem Value="7">Others</asp:ListItem>
						</asp:dropdownlist></TD>
					<td width="95"><asp:dropdownlist id="ddReligionOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk20" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table21" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Telephone #</TD>
					<TD><asp:dropdownlist id="ddTelephone" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtTelephone" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator8" runat="server" ValidationExpression="^[a-z A-Z 0-9 -]*$"
							ControlToValidate="txtTelephone" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<td width="95"><asp:dropdownlist id="ddTelephoneOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk21" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table22" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Mobile&nbsp;#</TD>
					<TD><asp:dropdownlist id="ddMobileNo" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtMobileNo" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator9" runat="server" ValidationExpression="^[a-z A-Z 0-9 -]*$"
							ControlToValidate="txtMobileNo" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<td width="95"><asp:dropdownlist id="ddMobileNoOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk22" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table23" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">E-mail(Personal)</TD>
					<TD><asp:dropdownlist id="ddEmailPersonal" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtEmailPersonal" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator10" runat="server" ValidationExpression="\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*"
							ControlToValidate="txtEmailPersonal" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<td width="95"><asp:dropdownlist id="ddEmailPersonalOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk23" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table24" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Next of Kin</TD>
					<TD><asp:dropdownlist id="ddNextofKin" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtNextofKin" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator11" runat="server" ValidationExpression="^[a-z A-Z 0-9()]*$"
							ControlToValidate="txtNextofKin" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<td width="95"><asp:dropdownlist id="ddNextofKinOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk24" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table25" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Bond</TD>
					<TD><asp:dropdownlist id="ddBond" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="1">Yes</asp:ListItem>
							<asp:ListItem Value="2">No</asp:ListItem>
						</asp:dropdownlist></TD>
					<td width="95"><asp:dropdownlist id="ddBondOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk25" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table26" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">E-mail (Official)</TD>
					<TD><asp:dropdownlist id="ddEmailOfficial" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtEmailOfficial" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator12" runat="server" ValidationExpression="\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*"
							ControlToValidate="txtEmailOfficial" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<td width="95"><asp:dropdownlist id="ddEmailOfficialOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk26" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table27" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Bank Account #</TD>
					<TD><asp:dropdownlist id="ddBankAccountNo" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtBankAccountNo" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator13" runat="server" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'
							ControlToValidate="txtBankAccountNo" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<td width="95"><asp:dropdownlist id="ddBankAccountNoOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk27" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table28" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Bank&nbsp;Detail</TD>
					<TD><asp:dropdownlist id="ddBankDetail" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtBankDetail" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator14" runat="server" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'
							ControlToValidate="txtBankDetail" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<td width="95"><asp:dropdownlist id="ddBankDetailOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk28" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table29" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">NTN #</TD>
					<TD><asp:dropdownlist id="ddNTN" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtNTN" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator15" runat="server" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'
							ControlToValidate="txtNTN" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<td width="95"><asp:dropdownlist id="ddNTNOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk29" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table30" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">EOBI&nbsp;#</TD>
					<TD><asp:dropdownlist id="ddEOBI" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtEOBI" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator16" runat="server" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'
							ControlToValidate="txtEOBI" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<td width="95"><asp:dropdownlist id="ddEOBIOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk30" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table31" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">SESSI&nbsp;#</TD>
					<TD><asp:dropdownlist id="ddSESSI" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtSESSI" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator17" runat="server" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'
							ControlToValidate="txtSESSI" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<td width="95"><asp:dropdownlist id="ddSESSIOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk31" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table32" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Insurance Code</TD>
					<TD><asp:dropdownlist id="ddInsuranceNo" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtInsuranceNo" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator18" runat="server" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'
							ControlToValidate="txtInsuranceNo" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<td width="95"><asp:dropdownlist id="ddInsuranceNoOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk32" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table33" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Compensatory off</TD>
					<TD><asp:dropdownlist id="ddCompensatory" runat="server" Width="100%">
							<asp:ListItem Value="-1" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="1">Yes</asp:ListItem>
							<asp:ListItem Value="0">No</asp:ListItem>
						</asp:dropdownlist></TD>
					<td width="95"><asp:dropdownlist id="ddCompensatoryOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk33" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table34" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Nationality</TD>
					<TD><asp:dropdownlist id="ddNationality" runat="server" Width="100%">
							<asp:ListItem Value="-1" Selected="True">-Select Where-</asp:ListItem>
						</asp:dropdownlist></TD>
					<td width="95"><asp:dropdownlist id="ddNationalityOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk34" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table35" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Nationality(Secondary)&nbsp;</TD>
					<TD><asp:dropdownlist id="ddNationality2" runat="server" Width="100%">
							<asp:ListItem Value="-1" Selected="True">-Select Where-</asp:ListItem>
						</asp:dropdownlist></TD>
					<td width="95"><asp:dropdownlist id="ddNationality2Order" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk35" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table36" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Mobile Entitlement</TD>
					<TD><asp:dropdownlist id="ddMobile" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is less than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is greater than or equal to</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtMobile" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator19" runat="server" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'
							ControlToValidate="txtMobile" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<td width="95"><asp:dropdownlist id="ddMobileOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk36" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table37" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Petrol Entitlement</TD>
					<TD><asp:dropdownlist id="ddPetrol" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is less than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is greater than or equal to</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtPetrol" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator20" runat="server" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'
							ControlToValidate="txtPetrol" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<td width="95"><asp:dropdownlist id="ddPetrolOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk37" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table38" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Car &amp; Conveyance</TD>
					<TD><asp:dropdownlist id="ddCar" runat="server" Width="100%">
							<asp:ListItem Value="-1" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="1">Company Maintained Car</asp:ListItem>
							<asp:ListItem Value="2">Conveyance Allowance</asp:ListItem>
							<asp:ListItem Value="3">Leased Car</asp:ListItem>
							<asp:ListItem Value="4">Pick &amp; Drop</asp:ListItem>
						</asp:dropdownlist></TD>
					<td width="95"><asp:dropdownlist id="ddCarOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk38" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table39" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Car &amp; Conveyance Description</TD>
					<TD><asp:dropdownlist id="ddCarDes" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtCarDes" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator21" runat="server" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'
							ControlToValidate="txtCarDes" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<td width="95"><asp:dropdownlist id="ddCarDesOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk39" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table40" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Probation Period</TD>
					<TD><asp:dropdownlist id="ddProbation" runat="server" Width="100%">
							<asp:ListItem Value="-1" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="0">0</asp:ListItem>
							<asp:ListItem Value="3">3</asp:ListItem>
							<asp:ListItem Value="6">6</asp:ListItem>
							<asp:ListItem Value="12">12</asp:ListItem>
							<asp:ListItem Value="-2">Other</asp:ListItem>
						</asp:dropdownlist></TD>
					<td width="95"><asp:dropdownlist id="ddProbationOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk40" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table41" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Confirmation Date</TD>
					<TD><asp:dropdownlist id="ddDoC" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is less than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is greater than or equal to</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><ew:calendarpopup id="txtDoC" runat="server" Width="110px" EnableHideDropDown="True">
							<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></WeekdayStyle>
							<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Yellow"></MonthHeaderStyle>
							<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
								BackColor="AntiqueWhite"></OffMonthStyle>
							<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></GoToTodayStyle>
							<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="LightGoldenrodYellow"></TodayDayStyle>
							<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Orange"></DayHeaderStyle>
							<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="LightGray"></WeekendStyle>
							<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Yellow"></SelectedDateStyle>
							<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></ClearDateStyle>
							<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></HolidayStyle>
						</ew:calendarpopup></TD>
					<td width="95"><asp:dropdownlist id="ddDoCOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk41" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table42" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">New Joining&nbsp;</TD>
					<TD><asp:dropdownlist id="ddNew" runat="server" Width="100%">
							<asp:ListItem Value="-1" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="1">Yes</asp:ListItem>
							<asp:ListItem Value="0">No</asp:ListItem>
						</asp:dropdownlist></TD>
					<td width="95"><asp:dropdownlist id="ddNewOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk42" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table43" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Other</TD>
					<TD><asp:dropdownlist id="ddOther" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="like">is like</asp:ListItem>
							<asp:ListItem Value="not like">is not like</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtOther" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator22" runat="server" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'
							ControlToValidate="txtOther" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<td width="95"><asp:dropdownlist id="ddOtherOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></td>
					<td width="50"><asp:linkbutton id="Lnk43" runat="server">Remove</asp:linkbutton></td>
				</TR>
			</TABLE>
			<TABLE id="Table44" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Department</TD>
					<TD><asp:dropdownlist id="ddDept" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="95"><asp:dropdownlist id="ddDeptOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="50"><asp:linkbutton id="Lnk44" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table45" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Employment Status</TD>
					<TD><asp:dropdownlist id="ddEStatus" runat="server" Width="100%">
							<asp:ListItem Value="-1" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="1">Active</asp:ListItem>
							<asp:ListItem Value="2">Hold/InActive</asp:ListItem>
							<asp:ListItem Value="0">Exited</asp:ListItem>
							<asp:ListItem Value="4">Expected To Join</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="95"><asp:dropdownlist id="ddEStatusOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="50"><asp:linkbutton id="Lnk45" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table46" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Basic Salary</TD>
					<TD><asp:dropdownlist id="ddBasicSal" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is less than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is greater than or equal to</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtBasicSal" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator23" runat="server" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'
							ControlToValidate="txtBasicSal" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<TD width="95"><asp:dropdownlist id="ddBasicSalOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="50"><asp:linkbutton id="Lnk46" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table47" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Gross&nbsp;Salary</TD>
					<TD><asp:dropdownlist id="ddGrossSal" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is less than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is greater than or equal to</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtGrossSal" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator24" runat="server" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'
							ControlToValidate="txtGrossSal" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<TD width="95"><asp:dropdownlist id="ddGrossSalOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="50"><asp:linkbutton id="Lnk47" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table48" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">House Rent</TD>
					<TD><asp:dropdownlist id="ddRent" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is less than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is greater than or equal to</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtRent" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator25" runat="server" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'
							ControlToValidate="txtRent" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<TD width="95"><asp:dropdownlist id="ddRentOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="50"><asp:linkbutton id="Lnk48" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table49" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Utilities</TD>
					<TD><asp:dropdownlist id="ddUtilities" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is less than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is greater than or equal to</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150"><asp:textbox id="txtUtilities" runat="server" Width="120px"></asp:textbox><asp:regularexpressionvalidator id="RegularExpressionValidator26" runat="server" ValidationExpression='^[a-z A-Z 0-9-,()"#./]*$'
							ControlToValidate="txtUtilities" ErrorMessage="Entered Character Not Allowed">*</asp:regularexpressionvalidator></TD>
					<TD width="95"><asp:dropdownlist id="ddUtilitiesOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="50"><asp:linkbutton id="Lnk49" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table50" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Functional Designation</TD>
					<TD><asp:dropdownlist id="ddFunc" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="95"><asp:dropdownlist id="ddFuncOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="50"><asp:linkbutton id="Lnk50" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table51" border="1" cellSpacing="0" cellPadding="1" width="850" runat="server">
				<TR>
					<TD width="200">Business Unit</TD>
					<TD colSpan="3"><asp:dropdownlist id="ddBusinessUnit" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="95"><asp:dropdownlist id="ddBusinessUnitOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="50"><asp:linkbutton id="lnk51" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<TABLE id="Table52" border="1" cellSpacing="1" cellPadding="1" width="850" runat="server">
				<TR>
					<TD style="WIDTH: 198px" width="198">Contract Expiry</TD>
					<TD colSpan="3">
						<asp:dropdownlist style="Z-INDEX: 0" id="ddCt" runat="server" Width="100%">
							<asp:ListItem Value="0" Selected="True">-Select Where-</asp:ListItem>
							<asp:ListItem Value="=">is equal to</asp:ListItem>
							<asp:ListItem Value="!=">is not equal to</asp:ListItem>
							<asp:ListItem Value="&lt;">is less than</asp:ListItem>
							<asp:ListItem Value="&lt;=">is less than or equal to</asp:ListItem>
							<asp:ListItem Value="&gt;">is greater than</asp:ListItem>
							<asp:ListItem Value="&gt;=">is greater than or equal to</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="150">
						<ew:calendarpopup style="Z-INDEX: 0" id="txtCtDate" runat="server" Width="110px" EnableHideDropDown="True">
							<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></WeekdayStyle>
							<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Yellow"></MonthHeaderStyle>
							<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
								BackColor="AntiqueWhite"></OffMonthStyle>
							<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></GoToTodayStyle>
							<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="LightGoldenrodYellow"></TodayDayStyle>
							<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Orange"></DayHeaderStyle>
							<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="LightGray"></WeekendStyle>
							<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="Yellow"></SelectedDateStyle>
							<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></ClearDateStyle>
							<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
								BackColor="White"></HolidayStyle>
						</ew:calendarpopup></TD>
					<TD width="95">
						<asp:dropdownlist style="Z-INDEX: 0" id="ddCtOrder" runat="server">
							<asp:ListItem Value="0" Selected="True">-Order by-</asp:ListItem>
							<asp:ListItem Value="1">Ascending</asp:ListItem>
							<asp:ListItem Value="2">Descending</asp:ListItem>
						</asp:dropdownlist></TD>
					<TD width="50">
						<asp:linkbutton style="Z-INDEX: 0" id="lnk52" runat="server">Remove</asp:linkbutton></TD>
				</TR>
			</TABLE>
			<BR>
			<BR>
			<P style="FONT-WEIGHT: bold" align="center">BU HR Managers can only view 
				Compensation &amp; Benefits details of their own department(s)</P>
			<DIV>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<asp:dropdownlist id="ddSearchAdvance_" runat="server" Visible="False">
					<asp:ListItem Value="1">Employee Code</asp:ListItem>
					<asp:ListItem Value="2">Designation</asp:ListItem>
					<asp:ListItem Value="3">Name</asp:ListItem>
				</asp:dropdownlist></DIV>
			<DIV align="center">
				<TABLE style="WIDTH: 482px; HEIGHT: 88px" id="Table52" border="0" cellSpacing="1" cellPadding="1"
					width="482" align="center">
					<TR>
						<TD>
							<P align="center"><asp:dropdownlist id="ddNetwork" runat="server" Width="200px" AutoPostBack="True"></asp:dropdownlist></P>
						</TD>
					</TR>
					<TR>
						<TD>
							<P align="center"><asp:dropdownlist id="ddSearchAdvance" runat="server">
									<asp:ListItem Value="1">Employee Code</asp:ListItem>
									<asp:ListItem Value="2">Designation</asp:ListItem>
									<asp:ListItem Value="3">Name</asp:ListItem>
									<asp:ListItem Value="4">Station</asp:ListItem>
									<asp:ListItem Value="5">Date of Join</asp:ListItem>
									<asp:ListItem Value="6">Confirmation Due Date</asp:ListItem>
									<asp:ListItem Value="7">Date of Exit</asp:ListItem>
									<asp:ListItem Value="8">Category</asp:ListItem>
									<asp:ListItem Value="9">Date of Birth</asp:ListItem>
									<asp:ListItem Value="10">Gender</asp:ListItem>
									<asp:ListItem Value="11">Blood Group</asp:ListItem>
									<asp:ListItem Value="12">Passport No</asp:ListItem>
									<asp:ListItem Value="13">NIC (new)</asp:ListItem>
									<asp:ListItem Value="14">NIC (old)</asp:ListItem>
									<asp:ListItem Value="15">Address</asp:ListItem>
									<asp:ListItem Value="16">Extension</asp:ListItem>
									<asp:ListItem Value="17">Employment Type</asp:ListItem>
									<asp:ListItem Value="18">Father Name</asp:ListItem>
									<asp:ListItem Value="19">Marital Status</asp:ListItem>
									<asp:ListItem Value="20">Religion</asp:ListItem>
									<asp:ListItem Value="21">Telephone (Home)</asp:ListItem>
									<asp:ListItem Value="22">Mobile</asp:ListItem>
									<asp:ListItem Value="23">Email (Personal)</asp:ListItem>
									<asp:ListItem Value="24">Next of Kin</asp:ListItem>
									<asp:ListItem Value="25">Bond Paper</asp:ListItem>
									<asp:ListItem Value="26">Email (Official)</asp:ListItem>
									<asp:ListItem Value="27">Bank Account#</asp:ListItem>
									<asp:ListItem Value="28">Bank Account Detail</asp:ListItem>
									<asp:ListItem Value="29">NTN#</asp:ListItem>
									<asp:ListItem Value="30">EOBI#</asp:ListItem>
									<asp:ListItem Value="31">SESSI#</asp:ListItem>
									<asp:ListItem Value="32">Insurance Code</asp:ListItem>
									<asp:ListItem Value="33">Compensatory Off</asp:ListItem>
									<asp:ListItem Value="34">Nationality</asp:ListItem>
									<asp:ListItem Value="35">Nationality (Secondary)</asp:ListItem>
									<asp:ListItem Value="36">Mobile Entitlement</asp:ListItem>
									<asp:ListItem Value="37">Petrol Entitlement</asp:ListItem>
									<asp:ListItem Value="38">Car Conveyance</asp:ListItem>
									<asp:ListItem Value="39">Car Description</asp:ListItem>
									<asp:ListItem Value="40">Probabtion Period</asp:ListItem>
									<asp:ListItem Value="41">Confirmation Date</asp:ListItem>
									<asp:ListItem Value="42">New Joiner</asp:ListItem>
									<asp:ListItem Value="43">Other</asp:ListItem>
									<asp:ListItem Value="44">Department</asp:ListItem>
									<asp:ListItem Value="45">Employment Status</asp:ListItem>
									<asp:ListItem Value="46">Basic Salary</asp:ListItem>
									<asp:ListItem Value="47">Gross Salary</asp:ListItem>
									<asp:ListItem Value="48">House Rent</asp:ListItem>
									<asp:ListItem Value="49">Utilities</asp:ListItem>
									<asp:ListItem Value="50">Functional Designation</asp:ListItem>
									<asp:ListItem Value="51">Business Unit</asp:ListItem>
									<asp:ListItem Value="52">Contract Expiry</asp:ListItem>
								</asp:dropdownlist><asp:button id="btnAdd" runat="server" Text="Add"></asp:button></P>
						</TD>
					</TR>
					<TR>
						<TD>
							<P align="center"><asp:linkbutton id="ViewComp" runat="server" Font-Names="Verdana" Font-Size="XX-Small">Add Compensation & Benefits Info</asp:linkbutton></P>
						</TD>
					</TR>
				</TABLE>
				&nbsp;
			</DIV>
			<P align="center"><asp:hyperlink id="HyperLink1" runat="server" NavigateUrl="default.aspx">Back</asp:hyperlink></P>
			<asp:panel id="Panel1" runat="server" Height="168px">
				<P align="center">
					<asp:PlaceHolder id="PlaceHolder1" runat="server"></asp:PlaceHolder><BR>
					<asp:CheckBox id="chkEmpPic" runat="server" Visible="False" Text="Employee Picture"></asp:CheckBox>
					<asp:CheckBox id="chkFamilyDetail" runat="server" Visible="False" Text="Family Detail"></asp:CheckBox>
					<asp:CheckBox id="chkRelative" runat="server" Visible="False" Text="Relative Detail"></asp:CheckBox>&nbsp;<BR>
					<asp:CheckBox id="chkEducation" runat="server" Visible="False" Text="Education Detail"></asp:CheckBox>&nbsp;
					<asp:CheckBox id="chkExperience" runat="server" Visible="False" Text="Experience Detail"></asp:CheckBox>&nbsp;
					<asp:CheckBox id="chkTraining" runat="server" Visible="False" Text="Training Detail"></asp:CheckBox><BR>
					<asp:CheckBox id="chkDirectReport" runat="server" Visible="False" Text="Direct  Reporting"></asp:CheckBox>&nbsp;
					<asp:CheckBox id="chkDottedReport" runat="server" Visible="False" Text="Dotted Reporting"></asp:CheckBox><BR>
					<asp:Button id="btnSubmit" runat="server" Visible="False" Text="Submit"></asp:Button><BR>
				</P>
			</asp:panel><asp:datagrid id="DataGrid1" runat="server" BorderColor="#000040" BorderStyle="None" BorderWidth="1px"
				BackColor="White" CellPadding="3">
				<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
				<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
				<AlternatingItemStyle BackColor="#F7F7F7"></AlternatingItemStyle>
				<ItemStyle ForeColor="#4A3C8C" BackColor="#E7E7FF"></ItemStyle>
				<HeaderStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#4A3C8C"></HeaderStyle>
				<Columns>
					<asp:HyperLinkColumn Text="Select" Target="_blank" DataNavigateUrlField="Employee Code" DataNavigateUrlFormatString="updateemployee.aspx?id={0}"
						DataTextField="Employee Code" DataTextFormatString="Select"></asp:HyperLinkColumn>
					<asp:HyperLinkColumn Text="View" Target="_blank" DataNavigateUrlField="Employee Code" DataNavigateUrlFormatString="employeereport.aspx?id={0}"
						DataTextField="Employee Code" DataTextFormatString="View"></asp:HyperLinkColumn>
				</Columns>
				<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
			</asp:datagrid>
			<asp:ValidationSummary id="ValidationSummary1" runat="server" ShowMessageBox="True"></asp:ValidationSummary></form>
	</body>
</HTML>
