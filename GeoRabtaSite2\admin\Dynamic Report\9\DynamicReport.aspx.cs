using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;
namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for WebForm2.
	/// </summary>
	public class DynamicReport : System.Web.UI.Page
	{
		protected System.Web.UI.WebControls.Button btnAdd;
		protected System.Web.UI.WebControls.Panel Panel1;
		protected System.Web.UI.HtmlControls.HtmlTable Table1;
		protected System.Web.UI.WebControls.DropDownList ddCode;
		protected System.Web.UI.WebControls.DropDownList ddCodeOrder;
		protected System.Web.UI.WebControls.DropDownList ddDesignationOrder;
		protected System.Web.UI.WebControls.DropDownList ddDesignation;
		protected System.Web.UI.HtmlControls.HtmlTable Table2;
		protected System.Web.UI.WebControls.TextBox txtCodeLike;
		protected System.Web.UI.WebControls.DropDownList ddStationOrder;
	    ArrayList _Main=new ArrayList();
		ArrayList _req=new ArrayList();
		protected System.Web.UI.WebControls.LinkButton Lnk2;
		protected System.Web.UI.WebControls.LinkButton lnk1;
		protected System.Web.UI.WebControls.PlaceHolder PlaceHolder1;
		protected System.Web.UI.WebControls.DropDownList ddSearchAdvance_;
		protected System.Web.UI.WebControls.DropDownList ddSearchAdvance;
		protected System.Web.UI.WebControls.DropDownList ddStation;
		protected System.Web.UI.WebControls.LinkButton Lnk4;
		protected System.Web.UI.HtmlControls.HtmlTable Table4;
		protected System.Web.UI.WebControls.LinkButton Lnk3;
		protected System.Web.UI.WebControls.DropDownList ddNameOrder;
		protected System.Web.UI.WebControls.TextBox txtNameLike;
		protected System.Web.UI.WebControls.DropDownList ddName;
		protected System.Web.UI.HtmlControls.HtmlTable Table3;
		protected System.Web.UI.WebControls.LinkButton Lnk5;
		protected System.Web.UI.WebControls.DropDownList ddDojOrder;
		protected System.Web.UI.WebControls.DropDownList ddDoj;
		protected System.Web.UI.HtmlControls.HtmlTable Table5;
		protected System.Web.UI.WebControls.LinkButton Lnk6;
		protected System.Web.UI.HtmlControls.HtmlTable Table6;
		protected System.Web.UI.WebControls.DropDownList ddConfOrderby;
		protected System.Web.UI.WebControls.DropDownList ddConfirmation;
		protected System.Web.UI.WebControls.LinkButton Lnk7;
		protected System.Web.UI.WebControls.DropDownList ddDoEOrder;
		protected System.Web.UI.WebControls.DropDownList ddDoE;
		protected System.Web.UI.HtmlControls.HtmlTable Table7;
		protected System.Web.UI.WebControls.LinkButton Lnk8;
		protected System.Web.UI.WebControls.DropDownList ddCategoryOrder;
		protected System.Web.UI.WebControls.DropDownList ddCategory;
		protected System.Web.UI.HtmlControls.HtmlTable Table8;
		protected System.Web.UI.WebControls.DropDownList ddGenderOrder;
		protected System.Web.UI.WebControls.DropDownList ddGender;
		protected System.Web.UI.WebControls.LinkButton Lnk10;
		protected System.Web.UI.HtmlControls.HtmlTable Table10;
		protected System.Web.UI.WebControls.LinkButton Lnk9;
		protected System.Web.UI.WebControls.DropDownList ddDoBOrder;
		protected System.Web.UI.WebControls.DropDownList ddDoB;
		protected System.Web.UI.HtmlControls.HtmlTable Table9;
		protected System.Web.UI.WebControls.LinkButton Lnk11;
		protected System.Web.UI.WebControls.DropDownList ddBloodGrpOrder;
		protected System.Web.UI.WebControls.DropDownList ddBloodGrp;
		protected System.Web.UI.HtmlControls.HtmlTable Table11;
		protected System.Web.UI.WebControls.LinkButton Lnk12;
		protected System.Web.UI.WebControls.DropDownList ddPassPortOrder;
		protected System.Web.UI.WebControls.TextBox txtPassport;
		protected System.Web.UI.WebControls.DropDownList ddPassport;
		protected System.Web.UI.HtmlControls.HtmlTable Table12;
		protected System.Web.UI.WebControls.LinkButton Lnk13;
		protected System.Web.UI.WebControls.DropDownList ddNICNewOrder;
		protected System.Web.UI.WebControls.TextBox txtNICNew;
		protected System.Web.UI.WebControls.DropDownList ddNICNew;
		protected System.Web.UI.HtmlControls.HtmlTable Table13;
		protected System.Web.UI.WebControls.LinkButton Lnk14;
		protected System.Web.UI.WebControls.DropDownList ddNICOldOrder;
		protected System.Web.UI.WebControls.TextBox txtNICOld;
		protected System.Web.UI.WebControls.DropDownList ddNICOld;
		protected System.Web.UI.HtmlControls.HtmlTable Table14;
		protected System.Web.UI.WebControls.LinkButton Lnk15;
		protected System.Web.UI.WebControls.DropDownList ddAddressOrder;
		protected System.Web.UI.WebControls.TextBox txtAddress;
		protected System.Web.UI.WebControls.DropDownList ddAddress;
		protected System.Web.UI.HtmlControls.HtmlTable Table15;
		protected System.Web.UI.WebControls.LinkButton Lnk16;
		protected System.Web.UI.WebControls.DropDownList ddExtensionOrder;
		protected System.Web.UI.WebControls.TextBox txtExtension;
		protected System.Web.UI.WebControls.DropDownList ddExtension;
		protected System.Web.UI.HtmlControls.HtmlTable Table16;
		protected System.Web.UI.WebControls.LinkButton Lnk17;
		protected System.Web.UI.WebControls.DropDownList ddEmploymentTypeOrder;
		protected System.Web.UI.WebControls.DropDownList ddEmploymentType;
		protected System.Web.UI.HtmlControls.HtmlTable Table17;
		protected System.Web.UI.WebControls.LinkButton Lnk18;
		protected System.Web.UI.WebControls.DropDownList ddFatherNameOrder;
		protected System.Web.UI.WebControls.TextBox txtFatherName;
		protected System.Web.UI.WebControls.DropDownList ddFatherName;
		protected System.Web.UI.HtmlControls.HtmlTable Table18;
		protected System.Web.UI.WebControls.LinkButton Lnk19;
		protected System.Web.UI.WebControls.DropDownList ddMaritalStatusOrder;
		protected System.Web.UI.WebControls.DropDownList ddMaritalStatus;
		protected System.Web.UI.HtmlControls.HtmlTable Table19;
		protected System.Web.UI.WebControls.LinkButton Lnk20;
		protected System.Web.UI.WebControls.DropDownList ddReligionOrder;
		protected System.Web.UI.WebControls.DropDownList ddReligion;
		protected System.Web.UI.HtmlControls.HtmlTable Table20;
		protected System.Web.UI.WebControls.LinkButton Lnk21;
		protected System.Web.UI.WebControls.DropDownList ddTelephoneOrder;
		protected System.Web.UI.HtmlControls.HtmlTable Table21;
		protected System.Web.UI.WebControls.DropDownList ddTelephone;
		protected System.Web.UI.WebControls.TextBox txtTelephone;
		protected System.Web.UI.WebControls.LinkButton Lnk22;
		protected System.Web.UI.WebControls.DropDownList ddMobileNoOrder;
		protected System.Web.UI.WebControls.TextBox txtMobileNo;
		protected System.Web.UI.WebControls.DropDownList ddMobileNo;
		protected System.Web.UI.HtmlControls.HtmlTable Table22;
		protected System.Web.UI.WebControls.LinkButton Lnk23;
		protected System.Web.UI.WebControls.DropDownList ddEmailPersonalOrder;
		protected System.Web.UI.WebControls.TextBox txtEmailPersonal;
		protected System.Web.UI.WebControls.DropDownList ddEmailPersonal;
		protected System.Web.UI.HtmlControls.HtmlTable Table23;
		protected System.Web.UI.WebControls.LinkButton Lnk24;
		protected System.Web.UI.WebControls.DropDownList ddNextofKinOrder;
		protected System.Web.UI.WebControls.TextBox txtNextofKin;
		protected System.Web.UI.WebControls.DropDownList ddNextofKin;
		protected System.Web.UI.HtmlControls.HtmlTable Table24;
		protected System.Web.UI.WebControls.LinkButton Lnk25;
		protected System.Web.UI.WebControls.DropDownList ddBondOrder;
		protected System.Web.UI.WebControls.DropDownList ddBond;
		protected System.Web.UI.HtmlControls.HtmlTable Table25;
		protected System.Web.UI.WebControls.LinkButton Lnk26;
		protected System.Web.UI.WebControls.DropDownList ddEmailOfficialOrder;
		protected System.Web.UI.WebControls.TextBox txtEmailOfficial;
		protected System.Web.UI.WebControls.DropDownList ddEmailOfficial;
		protected System.Web.UI.HtmlControls.HtmlTable Table26;
		protected System.Web.UI.WebControls.LinkButton Lnk27;
		protected System.Web.UI.WebControls.DropDownList ddBankAccountNoOrder;
		protected System.Web.UI.WebControls.TextBox txtBankAccountNo;
		protected System.Web.UI.WebControls.DropDownList ddBankAccountNo;
		protected System.Web.UI.HtmlControls.HtmlTable Table27;
		protected System.Web.UI.WebControls.LinkButton Lnk28;
		protected System.Web.UI.WebControls.DropDownList ddBankDetailOrder;
		protected System.Web.UI.WebControls.TextBox txtBankDetail;
		protected System.Web.UI.WebControls.DropDownList ddBankDetail;
		protected System.Web.UI.HtmlControls.HtmlTable Table28;
		protected System.Web.UI.WebControls.LinkButton Lnk29;
		protected System.Web.UI.WebControls.DropDownList ddNTNOrder;
		protected System.Web.UI.WebControls.TextBox txtNTN;
		protected System.Web.UI.WebControls.DropDownList ddNTN;
		protected System.Web.UI.HtmlControls.HtmlTable Table29;
		protected System.Web.UI.WebControls.LinkButton Lnk30;
		protected System.Web.UI.WebControls.DropDownList ddEOBIOrder;
		protected System.Web.UI.WebControls.TextBox txtEOBI;
		protected System.Web.UI.WebControls.DropDownList ddEOBI;
		protected System.Web.UI.HtmlControls.HtmlTable Table30;
		protected System.Web.UI.WebControls.LinkButton Lnk31;
		protected System.Web.UI.WebControls.DropDownList ddSESSIOrder;
		protected System.Web.UI.WebControls.TextBox txtSESSI;
		protected System.Web.UI.WebControls.DropDownList ddSESSI;
		protected System.Web.UI.HtmlControls.HtmlTable Table31;
		protected System.Web.UI.WebControls.LinkButton Lnk32;
		protected System.Web.UI.WebControls.DropDownList ddInsuranceNoOrder;
		protected System.Web.UI.WebControls.TextBox txtInsuranceNo;
		protected System.Web.UI.WebControls.DropDownList ddInsuranceNo;
		protected System.Web.UI.HtmlControls.HtmlTable Table32;
		protected System.Web.UI.WebControls.LinkButton Lnk33;
		protected System.Web.UI.WebControls.DropDownList ddCompensatoryOrder;
		protected System.Web.UI.WebControls.DropDownList ddCompensatory;
		protected System.Web.UI.HtmlControls.HtmlTable Table33;
		protected System.Web.UI.WebControls.LinkButton Lnk34;
		protected System.Web.UI.WebControls.DropDownList ddNationalityOrder;
		protected System.Web.UI.WebControls.DropDownList ddNationality;
		protected System.Web.UI.HtmlControls.HtmlTable Table34;
		protected System.Web.UI.WebControls.LinkButton Lnk35;
		protected System.Web.UI.WebControls.DropDownList ddNationality2Order;
		protected System.Web.UI.WebControls.DropDownList ddNationality2;
		protected System.Web.UI.HtmlControls.HtmlTable Table35;
		protected System.Web.UI.WebControls.LinkButton Lnk36;
		protected System.Web.UI.WebControls.DropDownList ddMobileOrder;
		protected System.Web.UI.WebControls.TextBox txtMobile;
		protected System.Web.UI.WebControls.DropDownList ddMobile;
		protected System.Web.UI.HtmlControls.HtmlTable Table36;
		protected System.Web.UI.WebControls.LinkButton Lnk37;
		protected System.Web.UI.WebControls.DropDownList ddPetrolOrder;
		protected System.Web.UI.WebControls.TextBox txtPetrol;
		protected System.Web.UI.WebControls.DropDownList ddPetrol;
		protected System.Web.UI.HtmlControls.HtmlTable Table37;
		protected System.Web.UI.WebControls.LinkButton Lnk38;
		protected System.Web.UI.WebControls.DropDownList ddCarOrder;
		protected System.Web.UI.WebControls.DropDownList ddCar;
		protected System.Web.UI.HtmlControls.HtmlTable Table38;
		protected System.Web.UI.WebControls.LinkButton Lnk39;
		protected System.Web.UI.WebControls.DropDownList ddCarDesOrder;
		protected System.Web.UI.WebControls.TextBox txtCarDes;
		protected System.Web.UI.WebControls.DropDownList ddCarDes;
		protected System.Web.UI.HtmlControls.HtmlTable Table39;
		protected System.Web.UI.WebControls.LinkButton Lnk40;
		protected System.Web.UI.WebControls.DropDownList ddProbationOrder;
		protected System.Web.UI.HtmlControls.HtmlTable Table40;
		protected System.Web.UI.WebControls.LinkButton Lnk41;
		protected System.Web.UI.WebControls.DropDownList ddDoCOrder;
		protected System.Web.UI.WebControls.DropDownList ddDoC;
		protected System.Web.UI.HtmlControls.HtmlTable Table41;
		protected System.Web.UI.WebControls.LinkButton Lnk42;
		protected System.Web.UI.WebControls.DropDownList ddNewOrder;
		protected System.Web.UI.WebControls.DropDownList ddNew;
		protected System.Web.UI.HtmlControls.HtmlTable Table42;
		protected System.Web.UI.WebControls.LinkButton Lnk43;
		protected System.Web.UI.WebControls.DropDownList ddOtherOrder;
		protected System.Web.UI.WebControls.TextBox txtOther;
		protected System.Web.UI.WebControls.DropDownList ddOther;
		protected System.Web.UI.HtmlControls.HtmlTable Table43;
		protected System.Web.UI.WebControls.Button btnSubmit;
		protected System.Web.UI.WebControls.DataGrid DataGrid1;
		protected System.Web.UI.WebControls.DropDownList ddProbation;
		protected eWorld.UI.CalendarPopup txtDojLike;
		protected eWorld.UI.CalendarPopup txtConfirmation;
		protected eWorld.UI.CalendarPopup txtDOE;
		protected eWorld.UI.CalendarPopup txtDoB;
		protected eWorld.UI.CalendarPopup txtDoC;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator1;
		protected System.Web.UI.WebControls.ValidationSummary ValidationSummary1;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator2;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator3;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator4;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator5;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator6;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator7;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator8;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator9;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator10;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator11;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator12;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator13;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator14;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator15;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator16;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator17;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator18;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator19;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator20;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator21;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator22;
		protected System.Web.UI.WebControls.LinkButton Lnk44;
		protected System.Web.UI.WebControls.DropDownList ddDeptOrder;
		protected System.Web.UI.WebControls.DropDownList ddDept;
		protected System.Web.UI.HtmlControls.HtmlTable Table44;
		protected System.Web.UI.WebControls.HyperLink HyperLink1;
		protected System.Web.UI.WebControls.DropDownList ddEStatus;
		protected System.Web.UI.HtmlControls.HtmlTable Table45;
		protected System.Web.UI.WebControls.LinkButton Lnk45;
		protected System.Web.UI.WebControls.DropDownList ddEStatusOrder;
		protected System.Web.UI.WebControls.LinkButton Lnk46;
		protected System.Web.UI.WebControls.DropDownList ddBasicSalOrder;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator23;
		protected System.Web.UI.WebControls.DropDownList ddBasicSal;
		protected System.Web.UI.HtmlControls.HtmlTable Table46;
		protected System.Web.UI.WebControls.LinkButton Lnk47;
		protected System.Web.UI.WebControls.DropDownList ddGrossSalOrder;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator24;
		protected System.Web.UI.WebControls.TextBox txtGrossSal;
		protected System.Web.UI.WebControls.DropDownList ddGrossSal;
		protected System.Web.UI.HtmlControls.HtmlTable Table47;
		protected System.Web.UI.WebControls.LinkButton Lnk48;
		protected System.Web.UI.WebControls.DropDownList ddRentOrder;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator25;
		protected System.Web.UI.WebControls.TextBox txtRent;
		protected System.Web.UI.WebControls.DropDownList ddRent;
		protected System.Web.UI.HtmlControls.HtmlTable Table48;
		protected System.Web.UI.WebControls.LinkButton Lnk49;
		protected System.Web.UI.WebControls.DropDownList ddUtilitiesOrder;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator26;
		protected System.Web.UI.WebControls.TextBox txtUtilities;
		protected System.Web.UI.WebControls.DropDownList ddUtilities;
		protected System.Web.UI.HtmlControls.HtmlTable Table49;
		protected System.Web.UI.WebControls.TextBox txtBasicSal;
		SqlConnection con;
		protected System.Web.UI.WebControls.CheckBox chkFamilyDetail;
		protected System.Web.UI.WebControls.LinkButton ViewComp;
		protected System.Web.UI.WebControls.DropDownList ddFuncOrder;
		protected System.Web.UI.WebControls.DropDownList ddFunc;
		protected System.Web.UI.HtmlControls.HtmlTable Table50;
		protected System.Web.UI.WebControls.LinkButton Lnk50;
		string userId="";
		private bool IsPageAccessAllowed()
		{
			
			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(2,48,userId,"View")==true)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("../notavailable.htm");
				return false;
			}
		}
		private void Page_Load(object sender, System.EventArgs e)
		{
			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception)
			{
				Response.Redirect("../notavailable.htm");
			}

			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			if(IsPageAccessAllowed())
			{
				
			}
			else
			{
			 Response.Redirect("../notavailable.htm");
			}
			if(Session["chkState"].ToString()=="0")
			{
			 this.ViewComp.Visible=false;
			}
			con=new SqlConnection(Connection.ConnectionString);
			if(!IsPostBack)
			{
//				if(Session["chkState"].ToString()=="0")
//				{
//				 Session["chkState"]="1";
//				}
				_Main.Clear();
				_req.Clear();
				for(int i=0;i<this.ddSearchAdvance.Items.Count;i++)
				{
					ListItem itm=new ListItem(this.ddSearchAdvance.Items[i].Text,this.ddSearchAdvance.Items[i].Value);
					_Main.Insert(i,itm);
				}
				Session["mainArr"]=_Main;
				Session["reqArr"]=_req;
                ViewComp.Attributes.Add("onclick", "return (window.showModalDialog('popup.aspx','yyy','dialogHeight:200px;dialogWidth:300px;center:yes;status:no;')==true)");   
				if(GeoSecurity.isControlVisible(2,48,userId,"Compensation & Benefits Detail")==false)
				{
					this.ViewComp.Visible=false;
				}
				else
				{
				    this.ViewComp.Visible=true;
				}
					ListItem BasicSalary=this.ddSearchAdvance.Items.FindByText("Basic Salary");
					ListItem GrossSalary=this.ddSearchAdvance.Items.FindByText("Gross Salary");
					ListItem HouseRent=this.ddSearchAdvance.Items.FindByText("House Rent");
					ListItem Utilities=this.ddSearchAdvance.Items.FindByText("Utilities");
					ListItem Other=this.ddSearchAdvance.Items.FindByText("Other");
					ListItem MEntitlement=this.ddSearchAdvance.Items.FindByText("Mobile Entitlement");
					ListItem PEntitlement=this.ddSearchAdvance.Items.FindByText("Petrol Entitlement");
					ListItem Car=this.ddSearchAdvance.Items.FindByText("Car Conveyance");
					ListItem CarDes=this.ddSearchAdvance.Items.FindByText("Car Description");
					this.ddSearchAdvance.Items.Remove(BasicSalary);
					this.ddSearchAdvance.Items.Remove(GrossSalary);
					this.ddSearchAdvance.Items.Remove(HouseRent);
					this.ddSearchAdvance.Items.Remove(Utilities);
					this.ddSearchAdvance.Items.Remove(Other);
					this.ddSearchAdvance.Items.Remove(MEntitlement);
					this.ddSearchAdvance.Items.Remove(PEntitlement);
					this.ddSearchAdvance.Items.Remove(Car);
					this.ddSearchAdvance.Items.Remove(CarDes);
			}
			Table1.Visible=false;
			Table2.Visible=false;
			Table3.Visible=false;
			Table4.Visible=false;
			Table5.Visible=false;
			Table6.Visible=false;
			Table7.Visible=false;
			Table8.Visible=false;
			Table9.Visible=false;
			Table10.Visible=false;
			Table11.Visible=false;
            Table12.Visible=false; 
			Table13.Visible=false;
			Table14.Visible=false;
			Table15.Visible=false;
			Table16.Visible=false;
			Table17.Visible=false;
			Table18.Visible=false;
			Table19.Visible=false;
			Table20.Visible=false;
			Table21.Visible=false;
			Table22.Visible=false;
			Table23.Visible=false;
			Table24.Visible=false;
			Table25.Visible=false;
			Table26.Visible=false;
			Table27.Visible=false;
			Table28.Visible=false;
			Table29.Visible=false;
			Table30.Visible=false;
			Table31.Visible=false;
			Table32.Visible=false;
			Table33.Visible=false;
			Table34.Visible=false;
			Table35.Visible=false;
			Table36.Visible=false;
			Table37.Visible=false;
			Table38.Visible=false;
			Table39.Visible=false;
			Table40.Visible=false;
			Table41.Visible=false;
			Table42.Visible=false;
			Table43.Visible=false;
			Table44.Visible=false;
			Table45.Visible=false;
            Table46.Visible=false;
			Table47.Visible=false;
			Table48.Visible=false;
			Table49.Visible=false;
			Table50.Visible=false;
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.Lnk4.Click += new System.EventHandler(this.ClickMe);
			this.Lnk2.Click += new System.EventHandler(this.ClickMe);
			this.lnk1.Click += new System.EventHandler(this.ClickMe);
			this.Lnk3.Click += new System.EventHandler(this.ClickMe);
			this.Lnk5.Click += new System.EventHandler(this.ClickMe);
			this.Lnk6.Click += new System.EventHandler(this.ClickMe);
			this.Lnk7.Click += new System.EventHandler(this.ClickMe);
			this.Lnk8.Click += new System.EventHandler(this.ClickMe);
			this.Lnk9.Click += new System.EventHandler(this.ClickMe);
			this.Lnk10.Click += new System.EventHandler(this.ClickMe);
			this.Lnk11.Click += new System.EventHandler(this.ClickMe);
			this.Lnk12.Click += new System.EventHandler(this.ClickMe);
			this.Lnk13.Click += new System.EventHandler(this.ClickMe);
			this.Lnk14.Click += new System.EventHandler(this.ClickMe);
			this.Lnk15.Click += new System.EventHandler(this.ClickMe);
			this.Lnk16.Click += new System.EventHandler(this.ClickMe);
			this.Lnk17.Click += new System.EventHandler(this.ClickMe);
			this.Lnk18.Click += new System.EventHandler(this.ClickMe);
			this.Lnk19.Click += new System.EventHandler(this.ClickMe);
			this.Lnk20.Click += new System.EventHandler(this.ClickMe);
			this.Lnk21.Click += new System.EventHandler(this.ClickMe);
			this.Lnk22.Click += new System.EventHandler(this.ClickMe);
			this.Lnk23.Click += new System.EventHandler(this.ClickMe);
			this.Lnk24.Click += new System.EventHandler(this.ClickMe);
			this.Lnk25.Click += new System.EventHandler(this.ClickMe);
			this.Lnk26.Click += new System.EventHandler(this.ClickMe);
			this.Lnk27.Click += new System.EventHandler(this.ClickMe);
			this.Lnk28.Click += new System.EventHandler(this.ClickMe);
			this.Lnk29.Click += new System.EventHandler(this.ClickMe);
			this.Lnk30.Click += new System.EventHandler(this.ClickMe);
			this.Lnk31.Click += new System.EventHandler(this.ClickMe);
			this.Lnk32.Click += new System.EventHandler(this.ClickMe);
			this.Lnk33.Click += new System.EventHandler(this.ClickMe);
			this.Lnk34.Click += new System.EventHandler(this.ClickMe);
			this.Lnk35.Click += new System.EventHandler(this.ClickMe);
			this.Lnk36.Click += new System.EventHandler(this.ClickMe);
			this.Lnk37.Click += new System.EventHandler(this.ClickMe);
			this.Lnk38.Click += new System.EventHandler(this.ClickMe);
			this.Lnk39.Click += new System.EventHandler(this.ClickMe);
			this.Lnk40.Click += new System.EventHandler(this.ClickMe);
			this.Lnk41.Click += new System.EventHandler(this.ClickMe);
			this.Lnk42.Click += new System.EventHandler(this.ClickMe);
			this.Lnk43.Click += new System.EventHandler(this.ClickMe);
			this.Lnk44.Click += new System.EventHandler(this.ClickMe);
			this.Lnk45.Click += new System.EventHandler(this.ClickMe);
			this.Lnk46.Click += new System.EventHandler(this.ClickMe);
			this.Lnk47.Click += new System.EventHandler(this.ClickMe);
			this.Lnk48.Click += new System.EventHandler(this.ClickMe);
			this.Lnk49.Click += new System.EventHandler(this.ClickMe);
			this.btnAdd.Click += new System.EventHandler(this.btnAdd_Click);
			this.ViewComp.Click += new System.EventHandler(this.ViewComp_Click);
			this.btnSubmit.Click += new System.EventHandler(this.btnSubmit_Click);
			this.Lnk50.Click += new System.EventHandler(this.ClickMe);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		public bool VisibleTable(string row,string ItemText,string ItemValue)
		{
			ArrayList _list=(ArrayList)Session["reqArr"];
			ListItem itm=null;
			bool flag=false;
			switch(row)
			{
				case "1":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
				    break; 
				case "2":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "3":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					//Table3.Visible=true;
					//this.PlaceHolder1.Controls.Add(Table3);
					//this.PlaceHolder1.Controls.Add(new LiteralControl("<br>"));
				     flag= true;
					break;
				case "4":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "5":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "6":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "7":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "8":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "9":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "10":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "11":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "12":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "13":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "14":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "15":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "16":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "17":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "18":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "19":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "20":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "21":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "22":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "23":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "24":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "25":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "26":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "27":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "28":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "29":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "30":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "31":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "32":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "33":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "34":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "35":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "36":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "37":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "38":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "39":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "40":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "41":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "42":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "43":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "44":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "45":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "46":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "47":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "48":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "49":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				case "50":
					itm=new ListItem(ItemText,ItemValue);
					_list.Add(itm);
					AddInPlaceHolder(_list,ItemValue);
					flag= true;
					break;
				default:
					flag= false;
					break;
			}
			return flag;
		}

		private void btnAdd_Click(object sender, System.EventArgs e)
		{
			ArrayList _list=(ArrayList)Session["reqArr"];
			ListItem itm=new ListItem(this.ddSearchAdvance.SelectedItem.Text,this.ddSearchAdvance.SelectedValue.ToString());
			if(!_list.Contains(itm))
			{
				bool flag=VisibleTable(this.ddSearchAdvance.SelectedValue.ToString(),this.ddSearchAdvance.SelectedItem.Text,this.ddSearchAdvance.SelectedItem.Value);
			}
			else
			{
			 AddInPlaceHolder(_list,"1");
			}
			if(_list.Count>0)
			{
				this.btnSubmit.Visible=true;
				this.chkFamilyDetail.Visible=true;
			}
			else
			{
				this.btnSubmit.Visible=false;
				this.chkFamilyDetail.Visible=false;
			}
			DataSet ds=null;
			this.DataGrid1.DataSource=ds;
			this.DataGrid1.DataBind();
		}
		public void AddInPlaceHolder(ArrayList list,string Current)
		{
			try
			{
				this.PlaceHolder1.Controls.Clear();
				for(int i=0;i<list.Count;i++)
				{
					ListItem itm=(ListItem)list[i];
					string ItemVal=itm.Value;
					HtmlTable table=getTable(ItemVal);
					table.Visible=true;
				    this.PlaceHolder1.Controls.Add(table);
				}
			}
			catch(Exception ex)
			{
			 Response.Write(ex.Message+ex.StackTrace);
			}
		}
		public void AddInPlaceHolder(ArrayList list,int Current)
		{
			try
			{
				this.PlaceHolder1.Controls.Clear();
				for(int i=0;i<list.Count;i++)
				{
					ListItem itm=(ListItem)list[i];
					string ItemVal=itm.Value;
					//HtmlTable table=getTable(ItemVal,true);
					HtmlTable table=getTable(ItemVal);
					table.Visible=true;
					//table.EnableViewState=false;
					this.PlaceHolder1.Controls.Add(table);
					//this.PlaceHolder1.Controls.AddAt(Int32.Parse(ItemVal),table);
				}
			}
			catch(Exception ex)
			{
			 Response.Write(ex.Message+ex.StackTrace);
			}
		}
		public HtmlTable getTable(string Val)
		{
			HtmlTable tb=null;
			SqlCommand cmd=null;
			SqlDataReader rd=null; 
			switch(Val)
			{
				case "1":
					tb= Table1;
					break;
				case "2":
					tb= Table2;
					//ddDesignation.Items.Clear();
					if(this.ddDesignation.Items.Count==1)
					{
						con.Open();
						cmd=new SqlCommand("select distinct designation from t_designation where status=1 order by designation",con);
						rd=cmd.ExecuteReader();
						while(rd.Read())
						{
							ListItem itm=new ListItem(rd[0].ToString(),rd[0].ToString());
							ddDesignation.Items.Add(itm);
						}
						rd.Close();
						con.Close();
					}
					break;
				case "3":
					tb= Table3;
					break;
				case "4":
					if(ddStation.Items.Count==1)
					{
						con.Open();
						cmd=new SqlCommand("select cityname,cityid from t_city where status=1",con);
						rd=cmd.ExecuteReader();
						while(rd.Read())
						{
							ListItem itm=new ListItem(rd[0].ToString(),rd[1].ToString());
							ddStation.Items.Add(itm); 
						}
						rd.Close();
						con.Close();
					}
					tb= Table4;
					break;
				case "5":
					tb= Table5;
					break;
				case "6":
					tb= Table6;
					break;
				case "7":
					tb= Table7;
					break;
				case "8":
					tb= Table8;
					if(this.ddCategory.Items.Count==1)
					{
						con.Open();
						cmd=new SqlCommand("select cat_name from t_categorization where isactive=1 order by Clevel",con);
						rd=cmd.ExecuteReader();
						while(rd.Read())
						{
							ListItem itm=new ListItem(rd[0].ToString(),rd[0].ToString());
							ddCategory.Items.Add(itm);
						}
						rd.Close();
						con.Close();
					}
					break;
				case "9":
					tb= Table9;
					break;
				case "10":
					tb= Table10;
					break;
				case "11":
					tb= Table11;
					break;
				case "12":
					tb= Table12;
					break;
				case "13":
					tb= Table13;
					break;
				case "14":
					tb= Table14;
					break;
				case "15":
					tb= Table15;
					break;
				case "16":
					tb= Table16;
					break;
				case "17":
					tb= Table17;
					break;
				case "18":
					tb= Table18;
					break;
				case "19":
					tb= Table19;
					break;
				case "20":
					tb= Table20;
					break;
				case "21":
					tb= Table21;
					break;
				case "22":
					tb= Table22;
					break;
				case "23":
					tb= Table23;
					break;
				case "24":
					tb= Table24;
					break;
				case "25":
					tb= Table25;
					break;
				case "26":
					tb= Table26;
					break;
				case "27":
					tb= Table27;
					break;
				case "28":
					tb= Table28;
					break;
				case "29":
					tb= Table29;
					break;
				case "30":
					tb= Table30;
					break;
				case "31":
					tb= Table31;
					break;
				case "32":
					tb= Table32;
					break;
				case "33":
					tb= Table33;
					break;
				case "34":
					tb= Table34;
					if(this.ddNationality.Items.Count==1)
					{
						con.Open();
						cmd=new SqlCommand("select countryid,cname from t_country",con);
						rd=cmd.ExecuteReader();
						while(rd.Read())
						{
							ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
							this.ddNationality.Items.Add(itm);
						}
						rd.Close();
						con.Close();
					}
					break;
				case "35":
					tb= Table35;
					if(this.ddNationality2.Items.Count==1)
					{
						con.Open();
						cmd=new SqlCommand("select countryid,cname from t_country",con);
						rd=cmd.ExecuteReader();
						while(rd.Read())
						{
							ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
							this.ddNationality2.Items.Add(itm);
						}
						rd.Close();
						con.Close();
					}
					break;
				case "36":
					tb= Table36;
					break;
				case "37":
					tb= Table37;
					break;
				case "38":
					tb= Table38;
					break;
				case "39":
					tb= Table39;
					break;
				case "40":
					tb= Table40;
					break;
				case "41":
					tb= Table41;
					break;
				case "42":
					tb= Table42;
					break;
				case "43":
					tb= Table43;
					break;
				case "44":
					tb= Table44;
					//ddDesignation.Items.Clear();
					if(this.ddDept.Items.Count==1)
					{
						con.Open();
						cmd=new SqlCommand("select distinct deptname from t_department where status=1 order by deptname",con);
						rd=cmd.ExecuteReader();
						while(rd.Read())
						{
							ListItem itm=new ListItem(rd[0].ToString(),rd[0].ToString());
							ddDept.Items.Add(itm);
						}
						rd.Close();
						con.Close();
					}
					break;
				case "45":
					tb= Table45;
					break;
				case "46":
					tb= Table46;
					break;
				case "47":
					tb= Table47;
					break;
				case "48":
					tb= Table48;
					break;
				case "49":
					tb= Table49;
					break;
				case "50":
					tb=Table50;
					if(this.ddFunc.Items.Count==1)
					{
					 con.Open();
					 cmd=new SqlCommand("select distinct functionaltitle from t_functionaldesignation where isactive=1 order by functionaltitle",con);
					 rd=cmd.ExecuteReader();
						while(rd.Read())
						{
							ListItem itm=new ListItem(rd[0].ToString(),rd[0].ToString());
							ddFunc.Items.Add(itm);
						}
					 rd.Close();
					 con.Close();
					}
					break;
			}
			return tb;
		}
		public HtmlTable getTable(string Val,bool flo)
		{
			HtmlTable tb=null;
			switch(Val)
			{
				case "1":
					tb= Table1;
					break;
				case "2":
					tb= Table2;
					break;
				case "3":
					tb= Table3;
					break;
				case "4":
					tb= Table4;
					break;
				case "5":
					tb= Table5;
					break;
				case "6":
					tb= Table6;
					break;
				case "7":
					tb= Table7;
					break;
				case "8":
					tb= Table8;
					break;
				case "9":
					tb= Table9;
					break;
				case "10":
					tb= Table10;
					break;
				case "11":
					tb= Table11;
					break;
				case "12":
					tb= Table12;
					break;
				case "13":
					tb= Table13;
					break;
				case "14":
					tb= Table14;
					break;
				case "15":
					tb= Table15;
					break;
				case "16":
					tb= Table16;
					break;
				case "17":
					tb= Table17;
					break;
				case "18":
					tb= Table18;
					break;
				case "19":
					tb= Table19;
					break;
				case "20":
					tb= Table20;
					break;
				case "21":
					tb= Table21;
					break;
				case "22":
					tb= Table22;
					break;
				case "23":
					tb= Table23;
					break;
				case "24":
					tb= Table24;
					break;
				case "25":
					tb= Table25;
					break;
				case "26":
					tb= Table26;
					break;
				case "27":
					tb= Table27;
					break;
				case "28":
					tb= Table28;
					break;
				case "29":
					tb= Table29;
					break;
				case "30":
					tb= Table30;
					break;
				case "31":
					tb= Table31;
					break;
				case "32":
					tb= Table32;
					break;
				case "33":
					tb= Table33;
					break;
				case "34":
					tb= Table34;
					break;
				case "35":
					tb= Table35;
					break;
				case "36":
					tb= Table36;
					break;
				case "37":
					tb= Table37;
					break;
				case "38":
					tb= Table38;
					break;
				case "39":
					tb= Table39;
					break;
				case "40":
					tb= Table40;
					break;
				case "41":
					tb= Table41;
					break;
				case "42":
					tb= Table42;
					break;
				case "43":
					tb= Table43;
					break;
				case "44":
					tb= Table44;
					break;
				case "45":
					tb= Table45;
					break;
				case "46":
					tb= Table46;
					break;
				case "47":
					tb= Table47;
					break;
				case "48":
					tb= Table48;
					break;
				case "49":
					tb= Table49;
					break;
				case "50":
					tb= Table50;
					break;
			}
			return tb;
		}
        
		private void ClickMe(object sender,EventArgs e)
		{
			LinkButton rBtn=(LinkButton)sender;
			int len=rBtn.ID.Length;
			string iRow="";
			if(len==4)
			{
				iRow=rBtn.ID.Substring(3,1);
			}
			if(len==5)
			{
			 iRow=rBtn.ID.Substring(3,2);
			}
			HtmlTable t=getTable(iRow,true);
			ArrayList _list=(ArrayList)Session["reqArr"];
			for(int i=0;i<_list.Count;i++)
			{
				ListItem itms=(ListItem)_list[i];
				HtmlTable tl=getTable(itms.Value,true);
				if(this.PlaceHolder1.Controls.Contains(tl))
				this.PlaceHolder1.Controls.Remove(tl);
			}
			AddInPlaceHolder((ArrayList)Session["reqArr"],-1);
			//ArrayList _list=(ArrayList)Session["reqArr"];
			ListItem itm=(ListItem)(this.ddSearchAdvance.Items.FindByValue(iRow));	
			_list.Remove(itm);
			Session["reqArr"]=_list;
			t.Visible=false;
			this.PlaceHolder1.Controls.Remove(t);
			if(_list.Count>=1)
			{
				this.btnSubmit.Visible=true;
				this.chkFamilyDetail.Visible=true;
				this.DataGrid1.Visible=true;
				string Query="Select Emp.Pcode as [Employee Code],";
				string temp="";
				string Orderby="";
				
				string[] LogField;
				string[]LogCondition;
				string LogFile="";
				string LogCon="";
				for(int i=0;i<_list.Count;i++)
				{
					ListItem itms=(ListItem)_list[i];
					//Query+=FiledSelector(itms.Text);
					//temp+=ConditionQuery(itms.Text);
					//Orderby+=OrderByCondition(itms.Text);
					
					LogField=FiledSelector(itms.Text).Split('^');
					Query+=LogField[0];
					LogFile+=LogField[1];
					LogCondition=ConditionQuery(itms.Text).Split('^');
					temp+=LogCondition[0];
					LogCon+=LogCondition[1];
					Orderby+=OrderByCondition(itm.Text);  
				}
				//Query+="emp.pcode as code,";
				if(this.chkFamilyDetail.Checked)
				{
					Query+="dbo.FamilyInfo(emp.pcode) as [Family Detail],";
				}
				con.Open();
				Query=Query.Remove(Query.Length-1,1);
				//Query+=" FROM dbo.t_Employee AS emp INNER JOIN  dbo.t_Designation AS desg ON emp.desigid = desg.desigid INNER JOIN "+
				//"dbo.t_Categorization AS cat ON desg.category = cat.cat_id";
				Query+=" FROM dbo.t_Employee emp INNER JOIN dbo.t_Designation desg ON emp.desigid = desg.desigid INNER JOIN dbo.t_Categorization cat ON desg.category = cat.cat_id INNER JOIN "+
					"dbo.t_Department dept ON desg.deptid = dept.deptid ";
				if(temp.Length>0)
				{
					temp=temp.Remove(temp.Length-3,3);
					Query+=" where "+temp;
				}
				else
				{
					Query+=temp;
				}
				if(Orderby.Length>0)
				{
					Orderby=Orderby.Remove(Orderby.Length-1,1);
					Query+=" order by "+Orderby;
				}
				//Response.Write(Query);
				//Response.End();
				if(temp.Length>0)
				{
					SqlDataAdapter sda=new SqlDataAdapter(Query,con);
					DataSet ds=new DataSet();
					sda.Fill(ds,"Records");
					//ds.Tables["Records"].Columns["code"].ColumnMapping=MappingType.Hidden;
					ds.AcceptChanges();
					GeoRabtaSite.admin.Convertor c=new GeoRabtaSite.admin.Convertor();
					ds=c.SetAdvanceGrid(ds);
					this.DataGrid1.DataSource=ds;
					this.DataGrid1.DataBind();
					if(GeoSecurity.isControlVisible(2,48,userId,"Edit Profile")==true)
					{
				      this.DataGrid1.Columns[0].Visible=true; 
					}
					else
					{
				      this.DataGrid1.Columns[0].Visible=false;
					}	
				}
				else
				{
					DataSet ds=null;
					this.DataGrid1.DataSource=ds;
					this.DataGrid1.DataBind();
				}
				con.Close();
			}
			else
			{
				this.btnSubmit.Visible=false;
				this.chkFamilyDetail.Visible=false;
			}
			
		}
		protected override void LoadViewState(object savedState)
		{
		    ArrayList _list=(ArrayList)Session["reqArr"];
			for(int i=0;i<_list.Count;i++)
			{
				ListItem itm=(ListItem)_list[i];
				HtmlTable t=getTable(itm.Value,true);
				if(this.PlaceHolder1.Controls.Contains(t))
					this.PlaceHolder1.Controls.Remove(t);
			}
			try
			{
				this.PlaceHolder1.Controls.Clear();
				for(int i=0;i<_list.Count;i++)
				{
					ListItem itm=(ListItem)_list[i];
					string ItemVal=itm.Value;
					HtmlTable table=getTable(ItemVal,true);
					if(!this.PlaceHolder1.Controls.Contains(table))
					{
						this.PlaceHolder1.Controls.Add(table);
					}
				}
				if(_list.Count>=1)
				{
					this.btnSubmit.Visible=true;
					this.chkFamilyDetail.Visible=true;
				}
			}
			catch(Exception ex)
			{
				Response.Write(ex.Message+ex.StackTrace);
			}
		}

		private void btnSubmit_Click(object sender, System.EventArgs e)
		{
			ArrayList _list =(ArrayList)Session["reqArr"];
			AddInPlaceHolder(_list,1);
			string Query="Select emp.pcode as [Employee Code],";
			string temp="";
			string Orderby="";
			string[] LogField;
			string[]LogCondition;
			string LogFile="";
			string LogCon="";
			for(int i=0;i<_list.Count;i++)
			{
				ListItem itm=(ListItem)_list[i];
				//Query+=FiledSelector(itm.Text);
				//temp+=ConditionQuery(itm.Text);
				//Orderby+=OrderByCondition(itm.Text);
				LogField=FiledSelector(itm.Text).Split('^');
				Query+=LogField[0];
				LogFile+=LogField[1];
				LogCondition=ConditionQuery(itm.Text).Split('^');
				temp+=LogCondition[0];
				LogCon+=LogCondition[1];
				Orderby+=OrderByCondition(itm.Text);
			}
			//Query+="emp.pcode as code,";
			if(this.chkFamilyDetail.Checked)
			{
			 Query+="dbo.FamilyInfo(emp.pcode) as [Family Detail],";
			}
			Query=Query.Remove(Query.Length-1,1);
            //Query+=" FROM dbo.t_Employee AS emp INNER JOIN  dbo.t_Designation AS desg ON emp.desigid = desg.desigid INNER JOIN "+
            //"dbo.t_Categorization AS cat ON desg.category = cat.cat_id";
			Query+=" FROM dbo.t_Employee emp INNER JOIN dbo.t_Designation desg ON emp.desigid = desg.desigid INNER JOIN dbo.t_Categorization cat ON desg.category = cat.cat_id INNER JOIN "+
            "dbo.t_Department dept ON desg.deptid = dept.deptid ";
			if(temp.Length>0)
			{
				temp=temp.Remove(temp.Length-3,3);
				Query+=" where "+temp;
			}
			else
			{
				Query+=temp;
			}
			if(Orderby.Length>0)
			{
			 Orderby=Orderby.Remove(Orderby.Length-1,1);
			 Query+=" order by "+Orderby;
			}
			//Response.Write(Query);
			//Response.End();
			if(temp.Length>0)
			{
				SqlDataAdapter sda=new SqlDataAdapter(Query,con);
				DataSet ds=new DataSet();
				sda.Fill(ds,"Records");
                //ds.Tables["Records"].Columns["code"].ColumnMapping=MappingType.Hidden;
				ds.AcceptChanges();
				GeoRabtaSite.admin.Convertor c=new GeoRabtaSite.admin.Convertor();
				ds=c.SetAdvanceGrid(ds);
				ds=setGrid(ds);
				this.DataGrid1.DataSource=ds;
				this.DataGrid1.DataBind();
				if(GeoSecurity.isControlVisible(2,48,userId,"Edit Profile")==true)
				{
					this.DataGrid1.Columns[0].Visible=true; 
				}
				else
				{
					this.DataGrid1.Columns[0].Visible=false;
				}
				con.Open();
				SqlCommand cmd=new SqlCommand("insert into t_userlog(userid,action,description,accessdate) values(@userid,@action,@description,@accessdate)",con);
				SqlParameter _userid=new SqlParameter("@userid",SqlDbType.Char);
				SqlParameter _action=new SqlParameter("@action",SqlDbType.VarChar);
				SqlParameter _description=new SqlParameter("@description",SqlDbType.VarChar);
				SqlParameter _accessdate=new SqlParameter("@accessdate",SqlDbType.DateTime);
				_userid.Value=Session["user_id"].ToString().Trim();
				_action.Value="View Advanced Report";
				LogFile=LogFile.Remove(LogFile.Length-1,1);
				LogCon=LogCon.Remove(LogCon.Length-1,1);
				_description.Value="Advance Report accessed having selection fields are: "+LogFile+" and searched criteria was on "+LogCon;
				_accessdate.Value=DateTime.Now;
				cmd.Parameters.Add(_userid);
				cmd.Parameters.Add(_action);
				cmd.Parameters.Add(_description);
				cmd.Parameters.Add(_accessdate);
				cmd.ExecuteNonQuery();
				con.Close();
				con.Dispose();
			}
			if(_list.Count>=1)
			{
				this.PlaceHolder1.Controls.Clear();
				for(int i=0;i<_list.Count;i++)
				{
					ListItem itm=(ListItem)_list[i];
					string ItemVal=itm.Value;
					HtmlTable table=getTable(ItemVal);
					if(!this.PlaceHolder1.Controls.Contains(table))
					{
						this.PlaceHolder1.Controls.Add(table);
					}
				}
			 this.btnSubmit.Visible=true;
			 this.chkFamilyDetail.Visible=true;
			}
		}

		public DataSet setGrid(DataSet ds)
		{
			if(Session["chkState"].ToString()=="0")
			{
				//this.ViewComp.Visible=false;
				con.Close();
				if(GeoSecurity.isControlVisible(2,33,Session["user_id"].ToString(),"View Compensation & Benefits Info")==true && ValidateAccount(Session["user_id"].ToString().Trim()) && GeoSecurity.isControlVisible(2,32,Session["user_id"].ToString(),"Compensation")==true)
				{
					//this.ViewComp.Visible=true;
					return ds;
				}
				else
				{
					con.Open();
					SqlCommand cmd =new SqlCommand("select b.deptid from t_bumanagers b where pcode='"+Session["user_id"].ToString().Trim()+"' and b.isactive=1",con);
					SqlDataReader de=cmd.ExecuteReader();
					de.Read();
					if(de.HasRows)
					{
						//this.ViewComp.Visible=true;
						de.Close();
						for(int i=0;i<ds.Tables[0].Rows.Count;i++)
						{
							string cPcode=ds.Tables[0].Rows[i]["Employee Code"].ToString().Trim();
							cmd=new SqlCommand("select b.deptid from t_bumanagers b where pcode='"+Session["user_id"].ToString()+"' and b.isactive=1 and b.deptid in (select dept.deptid from t_employee e,t_designation d,t_department dept where e.desigid=d.desigid and dept.deptid=d.deptid and e.pcode='"+cPcode+"')",con);
							de=cmd.ExecuteReader();
							de.Read();
							if(!de.HasRows)
							{
								if(ds.Tables[0].Columns.Contains("Basic Salary"))
									ds.Tables[0].Rows[i]["Basic Salary"]="";

								if(ds.Tables[0].Columns.Contains("Gross Salary"))
									ds.Tables[0].Rows[i]["Gross Salary"]="";
  
								if(ds.Tables[0].Columns.Contains("House Rent"))
									ds.Tables[0].Rows[i]["House Rent"]="";

								if(ds.Tables[0].Columns.Contains("Utilities"))
									ds.Tables[0].Rows[i]["Utilities"]="";

								if(ds.Tables[0].Columns.Contains("Mobile Entitlement"))
									ds.Tables[0].Rows[i]["Mobile Entitlement"]="";

								if(ds.Tables[0].Columns.Contains("Petrol Entitlement"))
									ds.Tables[0].Rows[i]["Petrol Entitlement"]="";

								if(ds.Tables[0].Columns.Contains("Car Conveyance"))
									ds.Tables[0].Rows[i]["Car Conveyance"]="";

								if(ds.Tables[0].Columns.Contains("Car Description"))
									ds.Tables[0].Rows[i]["Car Description"]="";
								de.Close();
							}
							else
							{
								de.Close();
							}
						}
						de.Close();
						con.Close();
						return ds;
					}
					else
					{
						ds=null;
						return ds;
						//this.ViewComp.Visible=false;
					}
				}
			}
			else
			{
			 return ds;
			}
		}
		public bool ValidateAccount(string ID)
		{
			if (con.State==ConnectionState.Closed)
				con.Open();
			string selectQry="select * from t_password where pcode='"+Session["user_id"].ToString()+"' and (state= 1 or state=0)";
			SqlCommand cmd=new SqlCommand(selectQry,con);
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			if(rd.HasRows)
			{
				rd.Close();
				con.Close();
				return true;
			}
			else
			{
				rd.Close();
				con.Close();
				return false;
			}
		
		}
		public string ConditionQuery(string iField)
		{
			string query="";
			string userLog="";
			switch (iField)
			{
				case "Employee Code":
					if(this.ddCode.SelectedValue.ToString()!="0")
					{
						query+=" emp.pcode "+this.ddCode.SelectedValue.ToString()+"'%"+this.txtCodeLike.Text+"%' and";
						userLog+="Employee Code like '"+this.txtCodeLike.Text+"',";
					}
					break;
				case "Designation":
					if(this.ddDesignation.SelectedValue.ToString()!="0")
					{
						query+=" desg.designation= '"+this.ddDesignation.SelectedItem.Text+"' and";
						userLog+="Designation '"+this.ddDesignation.SelectedItem.Text+"',"; 
					}
                    
					break;
				case "Name":
					if(this.ddName.SelectedValue.ToString()!="0")
					{
						query+=" emp.name "+this.ddName.SelectedValue.ToString()+"'%"+this.txtNameLike.Text+"%' and";
						userLog+="Name like '"+this.txtNameLike.Text+"',"; 
					}
					break;
				case "Date of Join":
					if(this.ddDoj.SelectedValue.ToString()!="0")
					{
						query+=" emp.dateofjoin "+this.ddDoj.SelectedValue.ToString()+"'"+this.txtDojLike.SelectedDate.ToShortDateString()+"' and";
						userLog+="Date of Join '"+this.txtDojLike.SelectedDate.ToShortDateString()+"',"; 
					}
                    
					break;
				case "Confirmation Due Date":
					if(this.ddConfirmation.SelectedValue.ToString()!="0")
					{
						query+=" emp.dateofconfirmation "+this.ddConfirmation.SelectedValue.ToString()+"'"+this.txtConfirmation.SelectedDate.ToShortDateString()+"' and";
						userLog+="Confiration Due Date '"+this.txtConfirmation.SelectedDate.ToShortDateString()+"',";
					}
					break;
				case "Date of Exit":
					if(this.ddDoE.SelectedValue.ToString()!="0")
					{
						query+=" emp.dateofexit "+this.ddDoE.SelectedValue.ToString()+"'"+this.txtDOE.SelectedDate.ToShortDateString()+"' and";
						userLog+="Date of Exit '"+this.txtDOE.SelectedDate.ToShortDateString()+"',";
					}
					break;
				case "Category":
					if(this.ddCategory.SelectedValue.ToString()!="0")
					{
						query+=" cat.cat_name='"+this.ddCategory.SelectedValue.ToString()+"' and"; 			
						userLog+="Category '"+this.ddCategory.SelectedItem.Text+"',"; 
					}
					break;
				case "Date of Birth":
					if(this.ddDoB.SelectedValue.ToString()!="0")
					{
						query+=" emp.dateofbirth "+this.ddDoB.SelectedValue.ToString()+"'"+this.txtDoB.SelectedDate.ToShortDateString()+"' and";
						userLog+="Date of Birth '"+this.txtDoB.SelectedDate.ToShortDateString()+"',";
					}
					break;
				case "Gender":
					if(this.ddGender.SelectedValue.ToString()!="0")
					{
						query+=" emp.gender ="+this.ddGender.SelectedValue.ToString()+" and";
						userLog+="Gender '"+this.ddGender.SelectedItem.Text+"',";
					}
					break;
				case "Blood Group":
					if(this.ddBloodGrp.SelectedValue.ToString()!="0")
					{
						query+=" emp.bloodgroup ="+this.ddBloodGrp.SelectedValue.ToString()+" and";
						userLog+="Blood Group '"+this.ddBloodGrp.SelectedItem.Text+"',";
					}
					break;
				case "Passport No":
					if(this.ddPassport.SelectedValue.ToString()!="0")
					{
						query+=" emp.passportno "+this.ddPassport.SelectedValue.ToString()+"'%"+this.txtPassport.Text+"%' and";
						userLog+="Passport No '"+this.txtPassport.Text+"',";
					}
					break;
				case "NIC (new)":
					if(this.ddNICNew.SelectedValue.ToString()!="0")
					{
						query+=" emp.nic_new "+this.ddNICNew.SelectedValue.ToString()+"'%"+this.txtNICNew.Text+"%' and";
						userLog+="NIC (new) '"+this.txtNICNew.Text+"',"; 
					}
					break;
				case "NIC (old)":
					if(this.ddNICOld.SelectedValue.ToString()!="0")
					{
						query+=" emp.nic_old "+this.ddNICOld.SelectedValue.ToString()+"'%"+this.txtNICOld.Text+"%' and";
						userLog+="NIC (old) '"+this.txtNICOld.Text+"',";
					}
					break;
				case "Address":
					if(this.ddAddress.SelectedValue.ToString()!="0")
					{
						query+=" emp.address "+this.ddAddress.SelectedValue.ToString()+"'%"+this.txtAddress.Text+"%' and";
						userLog+="Address '"+this.txtAddress.Text+"',";
					}
					break;
				case "Extension":
					if(this.ddExtension.SelectedValue.ToString()!="0")
					{
						query+=" emp.extension "+this.ddExtension.SelectedValue.ToString()+"'%"+this.txtExtension.Text+"%' and";
						userLog+="Extension '"+this.txtExtension.Text+"',";
					}
					break;
				case "Employment Type":
					if(this.ddEmploymentType.SelectedValue.ToString()!="0")
					{
						query+=" emp.typeofemployment ="+this.ddEmploymentType.SelectedValue.ToString()+" and";
						userLog+="Employment Type '"+this.ddEmploymentType.SelectedItem.Text+"',";
					}
					break;
				case "Father Name":
					if(this.ddFatherName.SelectedValue.ToString()!="0")
					{
						query+=" emp.fathername "+this.ddFatherName.SelectedValue.ToString()+"'%"+this.txtFatherName.Text+"%' and";
						userLog+="Father Name '"+this.txtFatherName.Text+"',";
					}
					break;
				case "Marital Status":
					if(this.ddMaritalStatus.SelectedValue.ToString()!="0")
					{
						query+=" emp.maritialstatus ="+this.ddMaritalStatus.SelectedValue.ToString()+" and";
						userLog+="Marital Status '"+this.ddMaritalStatus.SelectedItem.Text+"',";
					}
					break;
				case "Religion":
					if(this.ddReligion.SelectedValue.ToString()!="0")
					{
						query+=" emp.religion ="+this.ddReligion.SelectedValue.ToString()+" and";
						userLog+="Religion '"+this.ddReligion.SelectedItem.Text+"',";
					}
					break;
				case "Telephone (Home)":
					if(this.ddTelephone.SelectedValue.ToString()!="0")
					{
						query+=" emp.telephone "+this.ddTelephone.SelectedValue.ToString()+"'%"+this.txtTelephone.Text+"%' and";
						userLog+="Telephone (Home) '"+this.txtTelephone.Text+"',";
					}
					break;
				case "Mobile":
					if(this.ddMobileNo.SelectedValue.ToString()!="0")
					{
						query+=" emp.mobile "+this.ddMobileNo.SelectedValue.ToString()+"'%"+this.txtMobileNo.Text+"%' and";
						userLog+="Mobile '"+this.txtMobileNo.Text+"',";
					}
					break;
				case "Email (Personal)":
					if(this.ddEmailPersonal.SelectedValue.ToString()!="0")
					{
						query+=" emp.email_personal "+this.ddEmailPersonal.SelectedValue.ToString()+"'%"+this.txtEmailPersonal.Text+"%' and";
						userLog+="Email (Personal) '"+this.txtEmailPersonal.Text+"',";
					}
					break;
				case "Next of Kin":
					if(this.ddNextofKin.SelectedValue.ToString()!="0")
					{
						query+=" emp.kin "+this.ddNextofKin.SelectedValue.ToString()+"'%"+this.txtNextofKin.Text+"%' and";
						userLog+="Next of Kin '"+this.txtNextofKin.Text+"',";
					}
					break;
				case "Bond Paper":
					if(this.ddBond.SelectedValue.ToString()!="0")
					{
						query+=" emp.bondpaper ="+this.ddBond.SelectedValue.ToString()+" and";
						userLog+="Bond Paper '"+this.ddBond.SelectedItem.Text+"',";
					}
					break;
				case "Email (Official)":
					if(this.ddEmailOfficial.SelectedValue.ToString()!="0")
					{
						query+=" emp.email_official "+this.ddEmailOfficial.SelectedValue.ToString()+"'%"+this.txtEmailOfficial.Text+"%' and";
						userLog+="Email (Official) '"+this.txtEmailOfficial.Text+"',";
					}
					break;
				case "Bank Account#":
					if(this.ddBankAccountNo.SelectedValue.ToString()!="0")
					{
						query+=" emp.bankaccountno "+this.ddBankAccountNo.SelectedValue.ToString()+"'%"+this.txtBankAccountNo.Text+"%' and";
						userLog+="Bank Account# '"+this.txtBankAccountNo.Text+"',";
					}
					break;
				case "Bank Account Detail":
					if(this.ddBankDetail.SelectedValue.ToString()!="0")
					{
						query+=" emp.bankaccountdetails "+this.ddBankDetail.SelectedValue.ToString()+"'%"+this.txtBankDetail.Text+"%' and";
						userLog+="Bank Account Detail '"+this.txtBankDetail.Text+"',";
					}
					break;
				case "NTN#":
					if(this.ddNTN.SelectedValue.ToString()!="0")
					{
						query+=" emp.ntnno "+this.ddNTN.SelectedValue.ToString()+"'%"+this.txtNTN.Text+"%' and";
						userLog+="NTN # '"+this.txtNTN.Text+"',";
					}
					break;
				case "EOBI#":
					if(this.ddEOBI.SelectedValue.ToString()!="0")
					{
						query+=" emp.eobino "+this.ddEOBI.SelectedValue.ToString()+"'%"+this.txtEOBI.Text+"%' and";
						userLog+="EOBI # '"+this.txtEOBI.Text+"',";
					}
					break;
				case "SESSI#":
					if(this.ddSESSI.SelectedValue.ToString()!="0")
					{
						query+=" emp.sessino "+this.ddSESSI.SelectedValue.ToString()+"'%"+this.txtSESSI.Text+"%' and";
						userLog+="SESSI # '"+this.txtSESSI.Text+"',";
					}
					break;
				case "Insurance Code":
					if(this.ddInsuranceNo.SelectedValue.ToString()!="0")
					{
						query+=" emp.insurancecode "+this.ddInsuranceNo.SelectedValue.ToString()+"'%"+this.txtInsuranceNo.Text+"%' and";
						userLog+="Insurance Code '"+this.txtInsuranceNo.Text+"',";
					}
					break;
				case "Compensatory Off":
					if(this.ddCompensatory.SelectedValue.ToString()!="-1")
					{
						query+=" emp.compansatoryoff ="+this.ddCompensatory.SelectedValue.ToString()+" and";
						userLog+="Compensatory Off '"+this.ddCompensatory.SelectedItem.Text+"',";
					}
					break;
				case "Nationality":
					if(ddNationality.SelectedValue.ToString()!="-1")
					{
						query+=" emp.nationality ="+this.ddNationality.SelectedValue.ToString()+" and";
						userLog+="Nationality '"+this.ddNationality.SelectedItem.Text+"',";
					}
					break;
				case "Nationality (Secondary)":
					if(ddNationality2.SelectedValue.ToString()!="-1")
					{
						query+=" emp.nationality2 ="+this.ddNationality2.SelectedValue.ToString()+" and";
						userLog+="Nationality(Secondary) '"+this.ddNationality2.SelectedItem.Text+"',";
					}
					break;
				case "Mobile Entitlement":
					if(this.ddMobile.SelectedValue.ToString()!="0")
					{
						query+=" emp.mobileentitle "+this.ddMobile.SelectedValue.ToString()+this.txtMobile.Text+" and";
						userLog+="Mobile Entitlement '"+this.txtMobile.Text+"',";
					}
					break;
				case "Petrol Entitlement":
					if(this.ddPetrol.SelectedValue.ToString()!="0")
					{
						query+=" emp.petrolentitle "+this.ddPetrol.SelectedValue.ToString()+this.txtPetrol.Text+" and";
						userLog+="Petrol Entitlement '"+this.txtPetrol.Text+"',";
					}
					break;
				case "Car Conveyance":
					if(this.ddCar.SelectedValue.ToString()!="-1")
					{
						query+=" emp.carconveyance ="+this.ddCar.SelectedValue.ToString()+" and";
						userLog+="Car Conveyance '"+this.ddCar.SelectedItem.Text+"',";
					}
					break;
				case "Car Description":
					if(this.ddCarDes.SelectedValue.ToString()!="0")
					{
						query+=" emp.cardescription "+this.ddCarDes.SelectedValue.ToString()+"'%"+this.txtCarDes.Text+"%' and";
						userLog+="Car Description '"+this.txtCarDes.Text+"',";
					}
					break;
				case "Probabtion Period":
					if(this.ddProbation.SelectedValue.ToString()!="-1")
					{
						query+=" emp.probabtionPeriod ="+this.ddProbation.SelectedValue.ToString()+" and";
						userLog+="Probabtion Period '"+this.ddProbation.SelectedItem.Text+"',";
					}
					break;
				case "Confirmation Date":
					if(this.ddDoC.SelectedValue.ToString()!="0")
					{
						query+=" emp.finalconfirmationdate "+this.ddDoC.SelectedValue.ToString()+"'"+this.txtDoC.Text+"' and";
						userLog+="Confirmation Date '"+this.txtDoC.Text+"',";
					}
					break;
				case "New Joiner":
					if(this.ddNew.SelectedValue.ToString()!="-1")
					{
						query+=" emp.isnew="+this.ddNew.SelectedValue.ToString()+" and";
						userLog+="New Joiner '"+this.ddNew.SelectedItem.Text+"',";
					}
					break;
				case "Other":
					if(this.ddOther.SelectedValue.ToString()!="0")
					{
						query+=" emp.others "+this.ddOther.SelectedValue.ToString()+"'%"+this.txtOther.Text+"%' and";
						userLog+="Other Benefits '"+this.txtOther.Text+"',";
					}
					break;
				case "Station":
					if(this.ddStation.SelectedValue.ToString()!="0")
					{
						query+=" emp.station ="+this.ddStation.SelectedValue.ToString()+" and";
						userLog+="Station '"+this.ddStation.SelectedItem.Text+"',";
					}
					break;
				case "Department":
					if(this.ddDept.SelectedValue.ToString()!="0")
					{
						query+=" dept.deptname='"+this.ddDept.SelectedValue.ToString()+"' and"; 			
						userLog+="Department '"+this.ddDept.SelectedItem.Text+"',";
					}
					break;
				case "Employment Status":
					if(this.ddEStatus.SelectedValue.ToString()!="-1")
					{
						//query+=" emp.del="+this.ddEStatus.SelectedValue.ToString()+" and";
						if(this.ddEStatus.SelectedItem.Text=="Expected To Join")
						{
							query+=" emp.istobejoined=1 and";
							
						}
						else
						{
							query+=" emp.del="+this.ddEStatus.SelectedValue.ToString()+" and";
						}
						userLog+="Employment Status '"+this.ddEStatus.SelectedItem.Text;
						
					}
					break;
				case "Basic Salary":
					if(this.ddBasicSal.SelectedValue.ToString()!="0")
					{
						query+=" emp.basicsalary "+this.ddBasicSal.SelectedValue.ToString()+this.txtBasicSal.Text+" and";
						userLog+="Basic Salary '"+this.txtBasicSal.Text+"',";
					}
					break;
				case "Gross Salary":
					if(this.ddGrossSal.SelectedValue.ToString()!="0")
					{
						query+=" emp.grosssalary "+this.ddGrossSal.SelectedValue.ToString()+this.txtGrossSal.Text+" and";
						userLog+="Gross Salary '"+this.txtGrossSal.Text+"',";
					}
					break;
				case "House Rent":
					if(this.ddRent.SelectedValue.ToString()!="0")
					{
						query+=" emp.houserent "+this.ddRent.SelectedValue.ToString()+this.txtRent.Text+" and";
						userLog+="House Rent '"+this.txtRent.Text+"',";
					}
					break;
				case "Utilities":
					if(this.ddUtilities.SelectedValue.ToString()!="0")
					{
						query+=" emp.utilities "+this.ddUtilities.SelectedValue.ToString()+this.txtUtilities.Text+" and";
						userLog+="Utilities '"+this.txtUtilities.Text+"',";
					}
					break;
				case "Functional Designation":
					if(this.ddFunc.SelectedValue.ToString()!="0")
					{
						//query+=" f.functionaltitle='"+this.ddFunc.SelectedValue.ToString()+"' and"; 			
						query+=" (case emp.pcode when emp.pcode then (select distinct f.functionaltitle from t_functionaldesignation f,t_designationhistory h where f.functionaldesignation=desg.desigid and f.isactive=1 and h.fdesigid=f.fdesigid and h.pcode=emp.pcode and emp.desigid=f.functionaldesignation and h.isactive=1) end)='"+this.ddFunc.SelectedItem.Text.Trim()+"' and"; 
						userLog+="Functional Designation '"+this.ddFunc.SelectedItem.Text+"',";
					}
					break;
				default:
					break;
			}
			return query+"^"+userLog;
		}
		public string FiledSelector(string iField)
		{
			string query="";
			string userLog="";
			switch (iField)
			{
					//case "Employee Code":
					//query+="emp.pcode as [Employee Code],";
					//break;
				case "Designation":
					query+="desg.designation as [Designation],";
					userLog+="Designation,";
					break;
				case "Name":
					query+="emp.name as [Employee Name],";
					userLog+="Name,";
					break;
				case "Date of Join":
					query+="convert(varchar,emp.dateofjoin,106) as [Date of Join],";
					userLog+="Date of Join,";
					break;
				case "Confirmation Due Date":
					query="convert(varchar,emp.dateofconfirmation,106) as [Confirmation Due Date],";
					userLog+="Confiration Due Date,";
					break;
				case "Date of Exit":
					query="convert(varchar,emp.dateofexit,106) as [Date of Exit],";
					userLog+="Date of Exit,";
					break;
				case "Category":
					query+="cat.cat_name as [Category],";
					userLog+="Category,";
					break;
				case "Date of Birth":
					query+="convert(varchar,emp.dateofbirth,106) as [Date of Birth],";
					userLog+="Date of Birth,";
					break;
				case "Gender":
					query+="Gender=case emp.gender when 1 then 'Male' when 2 then 'Female' end,";
					userLog+="Gender,";
					break;
				case "Blood Group":
					query+="[Blood Group]=case emp.bloodgroup when 1 then 'A+' when 2 then 'A-' when 3 then 'B+' when 4 then 'B-' when 5 then 'AB+' when 6 then 'AB-' when 7 then 'O+' when 8 then 'O-' end,";
					userLog+="Blood Group,";
					break;
				case "Passport No":
					query+="emp.passportno as [Passport No],";
					userLog+="Passport No,";
					break;
				case "NIC (new)":
					query+="emp.nic_new as [NIC (New)],";
					userLog+="NIC (new),";
					break;
				case "NIC (old)":
					query+="emp.nic_old as [NIC (Old)],";
					userLog+="NIC (old),";
					break;
				case "Address":
					query+="emp.address as [Residential Address],";
					userLog+="Address,";
					break;
				case "Extension":
					query+="emp.extension as [Extension],";
					userLog+="Extension,";
					break;
				case "Employment Type":
					query+="[Type of Employment]=case emp.typeofemployment when 1 then 'Permanent' when 2 then 'Contractual' when 3 then 'Retainer' when 4 then 'Honorary' end,";
					userLog+="Employment Type,";
					break;
				case "Father Name":
					query+="emp.fathername as [Father Name],";
					userLog+="Father Name,";
					break;
				case "Marital Status":
					query+="[Marital Status]=case emp.maritialstatus when 1 then 'Single' when 2 then 'Married' when 3 then 'Divorced' when 4 then 'Widow' when 5 then 'Separated' end,";
					userLog+="Marital Status,";
					break;
				case "Religion":
					query+="Religion=case emp.religion when 1 then 'Islam' when 2 then 'Christianity' when 3 then 'Buddhism' when 4 then 'Zoroastrian' when 5 then 'Jewish' when 6 then 'Hinduism' when 7 then 'Other' end,";
					userLog+="Religion,";
					break;
				case "Telephone (Home)":
					query+="emp.telephone as [Telephone (Home)],";
					userLog+="Telephone (Home),";
					break;
				case "Mobile":
					query+="emp.mobile as [Mobile No],";
					userLog+="Mobile,";
					break;
				case "Email (Personal)":
					query+="emp.email_personal as [Emial (Personal)],";
					userLog+="Email (Personal),";
					break;
				case "Next of Kin":
					query+="emp.kin as [Next of Kin],";
					userLog+="Next of Kin,";
					break;
				case "Bond Paper":
					query+="Bond=case emp.bondpaper when 1 then 'Yes' when 2 then 'No' end,";
					userLog+="Bond Paper,";
					break;
				case "Email (Official)":
					query+="emp.email_official as [Email (Official)],";
					userLog+="Email (Official),";
					break;
				case "Bank Account#":
					query+="emp.bankaccountno as [Bank Account#],";
					userLog+="Bank Account#,";
					break;
				case "Bank Account Detail":
					query+="emp.bankaccountdetails as [Bank Account Detail],";
					userLog+="Bank Account Detail,";
					break;
				case "NTN#":
					query+="emp.ntnno as [NTN#],";
					userLog+="NTN #,";
					break;
				case "EOBI#":
					query+="emp.eobino as [EOBI#],";
					userLog+="EOBI #,";
					break;
				case "SESSI#":
					query+="emp.sessino as [SESSI#],";
					userLog+="SESSI #,";
					break;
				case "Insurance Code":
					query+="emp.insurancecode as [Insurance Code],";
					userLog+="Insurance Code,";
					break;
				case "Compensatory Off":
					query+="[Conpensatory Off]=case emp.compansatoryoff when 0 then 'No' when 1 then 'Yes' end,";
					userLog+="Compensatory Off,";
					break;
				case "Nationality":
					query+="Nationality=case cast(emp.nationality as varchar) when nationality then (select c.cname from t_country c where c.countryid=emp.nationality)end,";
					userLog+="Nationality,";
					break;
				case "Nationality (Secondary)":
					query+="[Nationality 2]=case cast(emp.nationality2 as varchar) when nationality2 then (select c.cname from t_country c where c.countryid=emp.nationality2)end,";
					userLog+="Nationality(Secondary),";
					break;
				case "Mobile Entitlement":
					query+="emp.mobileentitle as [Mobile Entitlement],";
					userLog+="Mobile Entitlement,";
					break;
				case "Petrol Entitlement":
					query+="emp.petrolentitle as [Petrol Entitlement],";
					userLog+="Petrol Entitlement,";
					break;
				case "Car Conveyance":
					query+="[Car Conveyance]=case emp.carconveyance when 1 then 'Maintained Car' when 2 then 'Conveyance Allowance' when 3 then 'Leased Car' when 4 then 'Pick & Drop' end,";
					userLog+="Car Conveyance,";
					break;
				case "Car Description":
					query+="emp.cardescription as [Car Description],";
					userLog+="Car Description,";
					break;
				case "Probabtion Period":
					query+="[Probabtion Period]=case emp.probabtionPeriod when 0 then '0' when 3 then '3' when 6 then '6' when 12 then '12' when -2 then probabtionperiodother end,";
					//query+="emp.probabtionPeriod as [Probabtion Period],";
					userLog+="Probabtion Period,";
					break;
				case "Confirmation Date":
					query+="convert(varchar,emp.finalconfirmationdate,106) as [Confirmation Date],";
					userLog+="Confirmation Date,";
					break;
				case "New Joiner":
					query+="[New Joining]=case emp.isnew when 0 then 'No' when 1 then 'Yes' end,";
					userLog+="New Joiner,";
					break;
				case "Other":
					query+="emp.others as [Other],";
					userLog+="Other,";
					break;
				case "Station":
					query+="Station=case cast(emp.station as varchar) when station then (select c.cityname from t_city c where emp.station=c.cityid)end,";
					userLog+="Station,";
					break;
				case "Department":
					query+="dept.deptname as [Department],";
					userLog+="Department,";
					break;
				case "Employment Status":
					//query+="[Employment Status]=case emp.del when 1 then 'Active' when 2 then 'InActive/Hold' when 0 then 'Exited' end,";
					if(this.ddEStatus.SelectedItem.Text=="Expected To Join")
					{
						query+="[Employment Status]=case istobejoined when '1' then 'Expected to Join' end,";
					}
					else if (this.ddEStatus.SelectedItem.Text=="Active" || this.ddEStatus.SelectedItem.Text=="Exited" || this.ddEStatus.SelectedItem.Text=="Hold/InActive") 
					{
						query+="[Employment Status]=case del when '1' then 'Active' when 0 then 'Exited' when 2 then 'InActive / Hold' end,";
					}
					else
					{
						query+="[Employment Status]=case istobejoined when '1' then 'Expected to Join' else (case del when 1 then 'Active' when 2 then 'InActive / Hold' when 0 then 'Exited' end) end,";
					}
					userLog+="Employment Status,";
					break;
				case "Basic Salary":
					query+="emp.basicsalary as [Basic Salary],";
					userLog+="Basic Salary,";
					break;
				case "Gross Salary":
					query+="emp.grosssalary as [Gross Salary],";
					userLog+="Gross Salary,";
					break;
				case "House Rent":
					query+="emp.houserent as [House Rent],";
					userLog+="House Rent,";
					break;
				case "Utilities":
					query+="emp.utilities as [Utilities],";
					userLog+="Utilities,";
					break;
				case "Functional Designation":
					query+="[Functional Designation]=case emp.pcode when emp.pcode then (select f.functionaltitle from t_functionaldesignation f,t_designationhistory h where f.functionaldesignation=desg.desigid and f.isactive=1 and h.fdesigid=f.fdesigid and h.pcode=emp.pcode and h.isactive=1)end,";
					userLog+="Functional Designation,";
					break;
				default:
					break;
			}
			return query+"^"+userLog;
		}

		public string OrderByCondition(string Item)
		{
		 string query="";
			switch(Item)
			{
				case "Employee Code":
					if(this.ddCodeOrder.SelectedIndex==1)
					{
						//query+=" left(pcode,1), cast (substring(pcode,2,10) as int),";
						query+=" left(replace(pcode,'gt',''),1),cast(substring(replace(pcode,'gt',''),2,10) as int),";
					}
					else if(this.ddCodeOrder.SelectedIndex==2)
					{
					 //query+=" left(pcode,1), cast (substring(pcode,2,10) as int) desc,";
					  query+=" left(replace(pcode,'gt',''),1),cast(substring(replace(pcode,'gt',''),2,10) as int) desc,";
					}
					break;
				case "Designation":
					if(this.ddDesignationOrder.SelectedIndex==1)
					{
						//query+=" desg.designation= '"+this.ddDesignation.SelectedItem.Text+"' and";
						query+="desg.designation,";
					}
					else if(this.ddDesignationOrder.SelectedIndex==2)
					{
					 query+=" desg.designation desc,";
					}
					break;
				case "Name":
					if(this.ddNameOrder.SelectedIndex==1)
					{
						//query+=" emp.name "+this.ddName.SelectedValue.ToString()+"'%"+this.txtNameLike.Text+"%' and";
					   query+=" emp.name,";
					}
					else if(this.ddNameOrder.SelectedIndex==2)
					{
					 query+=" emp.name desc,";
					}
					break;
				case "Date of Join":
					if(this.ddDojOrder.SelectedIndex==1)
					{
						//query+=" emp.dateofjoin "+this.ddDoj.SelectedValue.ToString()+"'"+this.txtDojLike.Text+"' and";
					    query+=" emp.dateofjoin,";
					}
					else if(this.ddDojOrder.SelectedIndex==2)
					{
					 query+=" emp.dateofjoin desc,";
					}
					break;
				case "Confirmation Due Date":
					if(this.ddConfOrderby.SelectedIndex==1)
					{
						//query+=" emp.dateofconfirmation "+this.ddConfirmation.SelectedValue.ToString()+"'"+this.txtConfirmation.Text+"' and";
						query+=" emp.dateofconfirmation,"; 
					}
					else if(this.ddConfOrderby.SelectedIndex==2)
					{
					 query+=" emp.dateofconfirmation desc,";
					}
					break;
				case "Date of Exit":
					if(this.ddDoEOrder.SelectedIndex==1)
					{
						//query+=" emp.dateofexit "+this.ddDoE.SelectedValue.ToString()+"'"+this.txtDOE.Text+"' and";
					      query+=" emp.dateofexit,";
					}
					else if(this.ddDoEOrder.SelectedIndex==2)
					{
					 query+=" emp.dateofexit desc,";
					}
					break;
				case "Category":
					if(this.ddCategoryOrder.SelectedIndex==1)
					{
						//query+=" cat.cat_name='"+this.ddCategory.SelectedValue.ToString()+"' and";
 					    query+="cat.cat_name,";
					}
					else if(this.ddCategoryOrder.SelectedIndex==2)
					{
					 query+="cat.cat_name desc,";
					}
					break;
				case "Date of Birth":
					if(this.ddDoBOrder.SelectedIndex==1)
					{
						//query+=" emp.dateofbirth "+this.ddDoB.SelectedValue.ToString()+"'"+this.txtDoB.Text+"' and";
					      query+=" emp.dateofbirth,";
					}
					else if(this.ddDoBOrder.SelectedIndex==2)
					{
					 query+=" emp.dateofbirth desc,";
					}
					break;
				case "Gender":
					if(this.ddGenderOrder.SelectedIndex==1)
					{
						//query+=" emp.gender ="+this.ddGender.SelectedValue.ToString()+" and";
					     query+=" emp.gender,";
					}
					else if(this.ddGenderOrder.SelectedIndex==2)
					{
					 query+=" emp.gender desc,";
					}
					break;
				case "Blood Group":
					if(this.ddBloodGrpOrder.SelectedIndex==1)
					{
						//query+=" emp.bloodgroup ="+this.ddBloodGrp.SelectedValue.ToString()+" and";
					    query+=" emp.bloodgroup,";
					}
					else if(this.ddBloodGrpOrder.SelectedIndex==2)
					{
					 query+=" emp.bloodgroup desc,";
					}
					break;
				case "Passport No":
					if(this.ddPassPortOrder.SelectedIndex==1)
					{
						//query+=" emp.passportno "+this.ddPassport.SelectedValue.ToString()+"'%"+this.txtPassport.Text+"%' and";
					    query+=" emp.passportno,";
					}
					else if(this.ddPassPortOrder.SelectedIndex==2)
					{
					 query+=" emp.passportno desc,";
					}
					break;
				case "NIC (new)":
					if(this.ddNICNewOrder.SelectedIndex==1)
					{
						//query+=" emp.nic_new "+this.ddNICNew.SelectedValue.ToString()+"'%"+this.txtNICNew.Text+"%' and";
					      query+=" emp.nic_new,";
					}
					else if(this.ddNICNewOrder.SelectedIndex==2)
					{
					 query+=" emp.nic_new desc,";
					}
					break;
				case "NIC (old)":
					if(this.ddNICOldOrder.SelectedIndex==1)
					{
						//query+=" emp.nic_old "+this.ddNICOld.SelectedValue.ToString()+"'%"+this.txtNICOld.Text+"%' and";
					      query+=" emp.nic_old,";
					}
					else if(this.ddNICOldOrder.SelectedIndex==2)
					{
					 query+=" emp.nic_old desc,";
					}
					break;
				case "Address":
					if(this.ddAddressOrder.SelectedIndex==1)
					{
						//query+=" emp.address "+this.ddAddress.SelectedValue.ToString()+"'%"+this.txtAddress.Text+"%' and";
					      query+=" emp.address,";
					}
					else if(this.ddAddressOrder.SelectedIndex==2)
					{
					 query+=" emp.address desc,";
					}
					break;
				case "Extension":
					if(this.ddExtensionOrder.SelectedIndex==1)
					{
						//query+=" emp.extension "+this.ddExtension.SelectedValue.ToString()+"'%"+this.txtExtension.Text+"%' and";
					     query+=" emp.extension,"; 
					}
					else if(this.ddExtensionOrder.SelectedIndex==2)
					{
					 query+=" emp.extension desc,";
					}
					break;
				case "Employment Type":
					if(this.ddEmploymentTypeOrder.SelectedIndex==1)
					{
						//query+=" emp.typeofemployment ="+this.ddEmploymentType.SelectedValue.ToString()+" and";
					      query+=" emp.typeofemployment,";
					}
					else if(this.ddEmploymentTypeOrder.SelectedIndex==2)
					{
					 query+=" emp.typeofemployment desc,";
					}
					break;
				case "Father Name":
					if(this.ddFatherNameOrder.SelectedIndex==1)
					{
						//query+=" emp.fathername "+this.ddFatherName.SelectedValue.ToString()+"'%"+this.txtFatherName.Text+"%' and";
					      query+=" emp.fathername,";
					}
					else if(this.ddFatherNameOrder.SelectedIndex==2)
					{
					 query+=" emp.fathername desc,";
					}
					break;
				case "Marital Status":
					if(this.ddMaritalStatusOrder.SelectedIndex==1)
					{
						//query+=" emp.maritialstatus ="+this.ddMaritalStatus.SelectedValue.ToString()+" and";
					      query+=" emp.maritialstatus,";
					}
					else if(this.ddMaritalStatusOrder.SelectedIndex==2)
					{
					 query+=" emp.maritialstatus desc,";
					}
					break;
				case "Religion":
					if(this.ddReligionOrder.SelectedIndex==1)
					{
						//query+=" emp.religion ="+this.ddReligion.SelectedValue.ToString()+" and";
						query+=" emp.religion,";
					}
					else if(this.ddReligionOrder.SelectedIndex==2)
					{
					 query+=" emp.religion desc,";
					}
					break;
				case "Telephone (Home)":
					if(this.ddTelephoneOrder.SelectedIndex==1)
					{
						//query+=" emp.telephone "+this.ddTelephone.SelectedValue.ToString()+"'%"+this.txtTelephone.Text+"%' and";
					      query+=" emp.telephone,";
					}
					else if(this.ddTelephoneOrder.SelectedIndex==2)
					{
					 query+=" emp.telephone desc,";
					}
					break;
				case "Mobile":
					if(this.ddMobileNoOrder.SelectedIndex==1)
					{
						//query+=" emp.mobile "+this.ddMobileNo.SelectedValue.ToString()+"'%"+this.txtMobileNo.Text+"%' and";
					    query+=" emp.mobile,"; 
					}
					else if(this.ddMobileNoOrder.SelectedIndex==2)
					{
					 query+=" emp.mobile desc,";
					}
					break;
				case "Email (Personal)":
					if(this.ddEmailPersonalOrder.SelectedIndex==1)
					{
						//query+=" emp.email_personal "+this.ddEmailPersonal.SelectedValue.ToString()+"'%"+this.txtEmailPersonal.Text+"%' and";
					      query+=" emp.email_personal,";  
					}
					else if(this.ddEmailPersonalOrder.SelectedIndex==2)
					{
					 query+=" emp.email_personal desc,";
					}
					break;
				case "Next of Kin":
					if(this.ddNextofKinOrder.SelectedIndex==1)
					{
						//query+=" emp.kin "+this.ddNextofKin.SelectedValue.ToString()+"'%"+this.txtNextofKin.Text+"%' and";
					     query+=" emp.kin,";  
					}
					else if(this.ddNextofKinOrder.SelectedIndex==2)
					{
					 query+=" emp.kin desc,";
					}
					break;
				case "Bond Paper":
					if(this.ddBondOrder.SelectedIndex==1)
					{
						//query+=" emp.bondpaper ="+this.ddBond.SelectedValue.ToString()+" and";
						query+=" emp.bondpaper,";
					}
					else if(this.ddBondOrder.SelectedIndex==2)
					{
					 query+=" emp.bondpaper desc,";
					}
					break;
				case "Email (Official)":
					if(this.ddEmailOfficialOrder.SelectedIndex==1)
					{
						//query+=" emp.email_official "+this.ddEmailOfficial.SelectedValue.ToString()+"'%"+this.txtEmailOfficial.Text+"%' and";
					      query+=" emp.email_official,";
					}
					else if(this.ddEmailOfficialOrder.SelectedIndex==1)
					{
					  query+=" emp.email_official desc,";
					}
					break;
				case "Bank Account#":
					if(this.ddBankAccountNoOrder.SelectedIndex==1)
					{
						//query+=" emp.bankaccountno "+this.ddBankAccountNo.SelectedValue.ToString()+"'%"+this.txtBankAccountNo.Text+"%' and";
					      query+=" emp.bankaccountno,";
					}
					else if(this.ddBankAccountNoOrder.SelectedIndex==2)
					{
					 query+=" emp.bankaccountno desc,";
					}
					break;
				case "Bank Account Detail":
					if(this.ddBankDetailOrder.SelectedIndex==1)
					{
						//query+=" emp.bankaccountdetails "+this.ddBankDetail.SelectedValue.ToString()+"'%"+this.txtBankDetail.Text+"%' and";
					      query+=" emp.bankaccountdetails,";
					}
					else if(this.ddBankDetailOrder.SelectedIndex==2)
					{
					 query+=" emp.bankaccountdetails desc,";
					}
					break;
				case "NTN#":
					if(this.ddNTNOrder.SelectedIndex==1)
					{
						//query+=" emp.ntnno "+this.ddNTN.SelectedValue.ToString()+"'%"+this.txtNTN.Text+"%' and";
					      query+=" emp.ntnno,";
					}
					else if(this.ddNTNOrder.SelectedIndex==2)
					{
					 query+=" emp.ntnno desc,";
					}
					break;
				case "EOBI#":
					if(this.ddEOBIOrder.SelectedIndex==1)
					{
						//query+=" emp.eobino "+this.ddEOBI.SelectedValue.ToString()+"'%"+this.txtEOBI.Text+"%' and";
					      query+=" emp.eobino,"; 
					}
					else if(this.ddEOBIOrder.SelectedIndex==2)
					{
					 query+=" emp.eobino desc,";
					}
					break;
				case "SESSI#":
					if(this.ddSESSIOrder.SelectedIndex==1)
					{
						//query+=" emp.sessino "+this.ddSESSI.SelectedValue.ToString()+"'%"+this.txtSESSI.Text+"%' and";
					      query+=" emp.sessino,"; 
					}
					else if(this.ddSESSIOrder.SelectedIndex==2)
					{
					 query+=" emp.sessino desc,";  
					}
					break;
				case "Insurance Code":
					if(this.ddInsuranceNoOrder.SelectedIndex==1)
					{
						//query+=" emp.insurancecode "+this.ddInsuranceNo.SelectedValue.ToString()+"'%"+this.txtInsuranceNo.Text+"%' and";
					      query+=" emp.insurancecode,";
					}
					else if(this.ddInsuranceNoOrder.SelectedIndex==2)
					{
					 query+=" emp.insurancecode desc,";
					}
					break;
				case "Compensatory Off":
					if(this.ddCompensatoryOrder.SelectedIndex==1)
					{
						//query+=" emp.compansatoryoff ="+this.ddCompensatory.SelectedValue.ToString()+" and";
					      query+=" emp.compansatoryoff,";  
					}
					else if(this.ddCompensatoryOrder.SelectedIndex==2)
					{
					 query+=" emp.compansatoryoff desc,"; 
					}
					break;
				case "Nationality":
					if(ddNationalityOrder.SelectedIndex==1)
					{
						//query+=" emp.nationality ="+this.ddNationality.SelectedValue.ToString()+" and";
						query+=" emp.nationality,";
					}
					else if(ddNationalityOrder.SelectedIndex==2)
					{
					 query+=" emp.nationality desc,";
					}
					break;
				case "Nationality (Secondary)":
					if(ddNationality2Order.SelectedIndex==1)
					{
						//query+=" emp.nationality2 ="+this.ddNationality2.SelectedValue.ToString()+" and";
						query+=" emp.nationality2,";
					}
					if(ddNationality2Order.SelectedIndex==2)
					{
					 query+=" emp.nationality2 desc,";
					}
					break;
				case "Mobile Entitlement":
					if(this.ddMobileOrder.SelectedIndex==1)
					{
						//query+=" emp.mobileentitle "+this.ddMobile.SelectedValue.ToString()+this.txtMobile.Text+" and";
					      query+=" emp.mobileentitle,";
					}
					else if(this.ddMobileOrder.SelectedIndex==2)
					{
					 query+=" emp.mobileentitle desc,";
					}
					break;
				case "Petrol Entitlement":
					if(this.ddPetrolOrder.SelectedIndex==1)
					{
						//query+=" emp.petrolentitle "+this.ddPetrol.SelectedValue.ToString()+this.txtPetrol.Text+" and";
					      query+=" emp.petrolentitle,";   
					}
					else if(this.ddPetrolOrder.SelectedIndex==2)
					{
					 query+=" emp.petrolentitle desc,"; 
					}
					break;
				case "Car Conveyance":
					if(this.ddCarOrder.SelectedIndex==1)
					{
						//query+=" emp.carconveyance ="+this.ddCar.SelectedValue.ToString()+" and";
						query+=" emp.carconveyance,";
					}
					else if(this.ddCarOrder.SelectedIndex==2)
					{
					 query+=" emp.carconveyance desc,";
					}
					break;
				case "Car Description":
					if(this.ddCarDesOrder.SelectedIndex==1)
					{
						//query+=" emp.cardescription "+this.ddCarDes.SelectedValue.ToString()+"'%"+this.txtCarDes.Text+"%' and";
					      query+=" emp.cardescription,";
					}
					else if(this.ddCarDesOrder.SelectedIndex==2)
					{
					 query+=" emp.cardescription desc,";
					}
					break;
				case "Probabtion Period":
					if(this.ddProbationOrder.SelectedIndex==1)
					{
						//query+=" emp.probabtionPeriod ="+this.ddProbation.SelectedValue.ToString()+" and";
						query+=" emp.probabtionPeriod,";
					}
					else if(this.ddProbationOrder.SelectedIndex==2)
					{
					 query+=" emp.probabtionPeriod desc,";
					}
					break;
				case "Confirmation Date":
					if(this.ddDoCOrder.SelectedIndex==1)
					{
						//query+=" emp.finalconfirmationdate "+this.ddDoC.SelectedValue.ToString()+"'"+this.txtDoC.Text+"' and";
					      query+=" emp.finalconfirmationdate,";  
					}
					else if(this.ddDoCOrder.SelectedIndex==2)
					{
					 query+=" emp.finalconfirmationdate desc,";
					}
					break;
				case "New Joiner":
					if(this.ddNewOrder.SelectedIndex==1)
					{
						//query+=" emp.isnew="+this.ddNew.SelectedValue.ToString()+" and";
						query+=" emp.isnew,";
					}
					else if(this.ddNewOrder.SelectedIndex==2)
					{
					 query+=" emp.isnew desc,";
					}
					break;
				case "Other":
					if(this.ddOtherOrder.SelectedIndex==1)
					{
						//query+=" emp.others "+this.ddOther.SelectedValue.ToString()+"'%"+this.txtOther.Text+"%' and";
					      query+=" emp.others,"; 
					}
					else if(this.ddOtherOrder.SelectedIndex==2)
					{
					 query+=" emp.others desc,";
					}
					break;
				case "Station":
					if(this.ddStationOrder.SelectedIndex==1)
					{
						//query+=" emp.station ="+this.ddStation.SelectedValue.ToString()+" and";
					      query+=" emp.station,";  
					}
					else if(this.ddStationOrder.SelectedIndex==2)
					{
					 query+=" emp.station desc,";
					}
					break;
				case "Department":
					if(this.ddDeptOrder.SelectedIndex==1)
					{
						//query+=" cat.cat_name='"+this.ddCategory.SelectedValue.ToString()+"' and";
						query+="dept.deptname,";
					}
					else if(this.ddDeptOrder.SelectedIndex==2)
					{
						query+="dept.deptname desc,";
					}
					break;
				case "Employment Status":
					if(this.ddEStatusOrder.SelectedIndex==1)
					{
						//query+="e.del,";
						query+="[Employment Status],";
					}
					else if(this.ddEStatusOrder.SelectedIndex==2)
					{
						//query+="e.del desc,";
						query+="[Employment Status] desc,";
					}
					break;
				case "Basic Salary":
					if(this.ddBasicSalOrder.SelectedIndex==1)
					{
						query+=" emp.basicsalary,";
					}
					else if(this.ddBasicSalOrder.SelectedIndex==2)
					{
						query+=" emp.basicsalary desc,";
					}
					break;
				case "Gross Salary":
					if(this.ddGrossSalOrder.SelectedIndex==1)
					{
						query+=" emp.grosssalary,";
					}
					else if(this.ddGrossSalOrder.SelectedIndex==2)
					{
						query+=" emp.grosssalary desc,";
					}
					break;
				case "House Rent":
					if(this.ddRentOrder.SelectedIndex==1)
					{
						query+=" emp.houserent,";
					}
					else if(this.ddRentOrder.SelectedIndex==2)
					{
						query+=" emp.houserent desc,";
					}
					break;
				case "Utilities":
					if(this.ddUtilitiesOrder.SelectedIndex==1)
					{
						query+=" emp.utilities,";
					}
					else if(this.ddUtilitiesOrder.SelectedIndex==2)
					{
						query+=" emp.utilities desc,";
					}
					break;
				case "Functional Designation":
					if(this.ddFuncOrder.SelectedIndex==1)
					{
						//query+=" cat.cat_name='"+this.ddCategory.SelectedValue.ToString()+"' and";
						query+="[Functional Designation],";
					}
					else if(this.ddFuncOrder.SelectedIndex==2)
					{
						query+="[Functional Designation] desc,";
					}
					break;
				default:
					break;
			}
			return query;
		}

		private void ViewComp_Click(object sender, System.EventArgs e)
		{
			ListItem BasicSalary= new ListItem("Basic Salary","46");
			ListItem GrossSalary= new ListItem("Gross Salary","47");
			ListItem HouseRent= new ListItem("House Rent","48");
			ListItem Utilities= new ListItem("Utilities","49");
			ListItem Other=new ListItem("Other","43");
			ListItem MEntitlement= new ListItem("Mobile Entitlement","36");
			ListItem PEntitlement= new ListItem("Petrol Entitlement","37");
			ListItem Car= new ListItem("Car Conveyance","38");
			ListItem CarDes=new ListItem("Car Description","39");
			this.ddSearchAdvance.Items.Add(BasicSalary);
			this.ddSearchAdvance.Items.Add(GrossSalary);
			this.ddSearchAdvance.Items.Add(HouseRent);
			this.ddSearchAdvance.Items.Add(Utilities);
			this.ddSearchAdvance.Items.Add(Other);
			this.ddSearchAdvance.Items.Add(MEntitlement);
			this.ddSearchAdvance.Items.Add(PEntitlement);
			this.ddSearchAdvance.Items.Add(Car);
			this.ddSearchAdvance.Items.Add(CarDes);
			this.ViewComp.Visible=false;
			Session["chkState"]="0";
            con.Open(); 
			SqlCommand cmd=new SqlCommand("insert into t_userlog(userid,action,description,accessdate) values(@userid,@action,@description,@accessdate)",con);
			SqlParameter _userid=new SqlParameter("@userid",SqlDbType.Char);
			SqlParameter _action=new SqlParameter("@action",SqlDbType.VarChar);
			SqlParameter _description=new SqlParameter("@description",SqlDbType.VarChar);
			SqlParameter _accessdate=new SqlParameter("@accessdate",SqlDbType.DateTime);
			_userid.Value=Session["user_id"].ToString().Trim();
			_action.Value="View Compensation";
			_description.Value="Compensation Details Add in Advance Report";
			_accessdate.Value=DateTime.Now;
			cmd.Parameters.Add(_userid);
			cmd.Parameters.Add(_action);
			cmd.Parameters.Add(_description);
			cmd.Parameters.Add(_accessdate);
			cmd.ExecuteNonQuery();
			con.Close();
			con.Dispose();
		}


	}
}
