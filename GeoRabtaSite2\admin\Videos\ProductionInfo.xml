﻿<?xml version="1.0" encoding="UTF-8" ?><Production_Data Version="5.10"><HTMLTemplateFile>Template.$[AnyExtension]</HTMLTemplateFile>
<HTMLTemplateBodyFile>expressShow\SSWFTemplate-body.htm</HTMLTemplateBodyFile>
<HTMLTemplateHeaderFile>expressShow\SSWFTemplate-header.htm</HTMLTemplateHeaderFile>
<VideoFilenameWithPath>C:\Documents and Settings\Administrator\My Documents\Camtasia Studio\dd\dd.swf</VideoFilenameWithPath>
<AddIPodFileSize>0 bytes</AddIPodFileSize>
<AddMP3FileSize>0 bytes</AddMP3FileSize>
<AddPPTFileSize>0 bytes</AddPPTFileSize>
<ProjectName>dd</ProjectName>
<MediaDir></MediaDir>
<ShouldCreateMediaDir>0</ShouldCreateMediaDir>
<MaxTimelineWidth>640</MaxTimelineWidth>
<MaxTimelineHeight>480</MaxTimelineHeight>
<MaxTimelineFPS>9.00</MaxTimelineFPS>
<MaxVideoWidth>800</MaxVideoWidth>
<MaxVideoHeight>612</MaxVideoHeight>
<MaxVideoFPS>9.00</MaxVideoFPS>
<MaxPIPWidth>0</MaxPIPWidth>
<MaxPIPHeight>0</MaxPIPHeight>
<MaxPIPFPS>0.00</MaxPIPFPS>
<VideoDuration>35.90</VideoDuration>
<PIPVideoDuration>35.90</PIPVideoDuration>
<VideoFPS>30.00</VideoFPS>
<PIPVideoFPS>30.00</PIPVideoFPS>
<VideoWidth>640</VideoWidth>
<VideoHeight>538</VideoHeight>
<ActualVideoWidth>640</ActualVideoWidth>
<ActualVideoHeight>520</ActualVideoHeight>
<PIPVideoWidth>0</PIPVideoWidth>
<PIPVideoHeight>0</PIPVideoHeight>
<TOCWidth>-1</TOCWidth>
<MinTOCWidth>160</MinTOCWidth>
<TOCDividerHeight>18</TOCDividerHeight>
<TimelineHasAudio>1</TimelineHasAudio>
<TimelineDuration>359000000</TimelineDuration>
<SWFControllerTotalHeight>18</SWFControllerTotalHeight>
<SWFMinWidth>320</SWFMinWidth>
<SWFTOCXPos>0</SWFTOCXPos>
<SWFTOCYPos>0</SWFTOCYPos>
<SWFTOCWidth>-1</SWFTOCWidth>
<SWFTOCHeight>0</SWFTOCHeight>
<SWFHasPIPOverlay>0</SWFHasPIPOverlay>
<SWFControllerColor></SWFControllerColor>
<SWFTimeDispFontColor>0x000000</SWFTimeDispFontColor>
<SWFPlaybackConfigFileName>dd_config.xml</SWFPlaybackConfigFileName>
<SWFPlaybackControllerFileName>dd_controller.swf</SWFPlaybackControllerFileName>
<SWFPreloaderFileName></SWFPreloaderFileName>
<StripWhitespace>0</StripWhitespace>
<WritingImages>0</WritingImages>
<WritePreviewXML>0</WritePreviewXML>
<DisableTOC>0</DisableTOC>
<AutoPlay>1</AutoPlay>
<CancelledProduction>0</CancelledProduction>
<CancelledProdLength>357666667</CancelledProdLength>
<Hotspot_Array>
<Hotspot_Array_Object>
               <array>
               <hotspot>
               </hotspot>
               </array>
</Hotspot_Array_Object>
</Hotspot_Array>
<AdjGlobalRenderRangeStart>-1</AdjGlobalRenderRangeStart>
<AdjGlobalRenderRangeStop>-1</AdjGlobalRenderRangeStop>
<AdjRenderRange_Array>
</AdjRenderRange_Array>
<AdjMarkerDuration_Array>
</AdjMarkerDuration_Array>
<AdjMarkerTimes_Array>
</AdjMarkerTimes_Array>
<HasQuizQuestions>0</HasQuizQuestions>
<m_strQuizQuestionOfString>Question @ of @</m_strQuizQuestionOfString>
<m_strQuizPrevString>Prev</m_strQuizPrevString>
<m_strQuizSubmitString>Submit</m_strQuizSubmitString>
<m_strQuizNextString>Next</m_strQuizNextString>
<m_strQuizDoneString>Done</m_strQuizDoneString>
<m_strQuizPreviewString>Preview</m_strQuizPreviewString>
<m_strQuizSendString>Send</m_strQuizSendString>
<m_strQuizMainMessageString>You have reached the end of the questions and this video. Your answers are ready to be sent.</m_strQuizMainMessageString>
<m_strQuizEmailContentsString>E-mail Contents</m_strQuizEmailContentsString>
<m_strQuizEmailToString>E-mail to </m_strQuizEmailToString>
<m_strQuizAnswersSentString>Your answers have been sent.</m_strQuizAnswersSentString>
<m_strQuizSummaryString>You have completed @ of @ questions.</m_strQuizSummaryString>
<m_strQuizScoreString>Score = @/@</m_strQuizScoreString>
<m_strQuizSummaryTitleString>Answers Summary</m_strQuizSummaryTitleString>
<Quiz_Array>
</Quiz_Array>
<Markers_Array>
</Markers_Array>
<CaptionPlayerXML_Array>
</CaptionPlayerXML_Array>
<CaptionFontHeight>11</CaptionFontHeight>
<MaxCaptionsCharsPerLine>82</MaxCaptionsCharsPerLine>
<TimelineCaptionCount>5</TimelineCaptionCount>
<CaptionHeight>40</CaptionHeight>
<CloseCaptions_Array>
</CloseCaptions_Array>
<NumMetaDataTypes>13</NumMetaDataTypes>
<MetaData_Array>
<MetaData_Object>
<MetaDataType>Title</MetaDataType>
<MetaDataValue>dd</MetaDataValue>
<MetaDataEmbedName>DC.title</MetaDataEmbedName>
</MetaData_Object>
</MetaData_Array>
<NumFileTypesUsedByProduction>4</NumFileTypesUsedByProduction>
<FileTypesUsed_Array>
<FileTypeUsed_Object>
<FileTypeName>Video</FileTypeName>
</FileTypeUsed_Object>
<FileTypeUsed_Object>
<FileTypeName>HTMLFile</FileTypeName>
</FileTypeUsed_Object>
<FileTypeUsed_Object>
<FileTypeName>EmbededPlayerJS</FileTypeName>
</FileTypeUsed_Object>
<FileTypeUsed_Object>
<FileTypeName>ProductionInfoXML</FileTypeName>
</FileTypeUsed_Object>
</FileTypesUsed_Array>
<NumFilesTotal>4</NumFilesTotal>
<ProducedFiles_Array>
<ProducedFile_Object>
<FileName>C:\Documents and Settings\Administrator\My Documents\Camtasia Studio\dd\dd.swf</FileName>
<FileSize>4646294</FileSize>
<FileType>Video</FileType>
<WhichFilesOfCurType>0</WhichFilesOfCurType>
<TotalFilesOfCurType>1</TotalFilesOfCurType>
</ProducedFile_Object>
<ProducedFile_Object>
<FileName>C:\Documents and Settings\Administrator\My Documents\Camtasia Studio\dd\dd.htm</FileName>
<FileSize>0</FileSize>
<FileType>HTMLFile</FileType>
<WhichFilesOfCurType>0</WhichFilesOfCurType>
<TotalFilesOfCurType>1</TotalFilesOfCurType>
</ProducedFile_Object>
<ProducedFile_Object>
<FileName>C:\Documents and Settings\Administrator\My Documents\Camtasia Studio\dd\swfobject.js</FileName>
<FileSize>0</FileSize>
<FileType>EmbededPlayerJS</FileType>
<WhichFilesOfCurType>0</WhichFilesOfCurType>
<TotalFilesOfCurType>1</TotalFilesOfCurType>
</ProducedFile_Object>
<ProducedFile_Object>
<FileName>C:\Documents and Settings\Administrator\My Documents\Camtasia Studio\dd\ProductionInfo.xml</FileName>
<FileSize>0</FileSize>
<FileType>ProductionInfoXML</FileType>
<WhichFilesOfCurType>0</WhichFilesOfCurType>
<TotalFilesOfCurType>1</TotalFilesOfCurType>
</ProducedFile_Object>
</ProducedFiles_Array>
<OutputBasePath>C:\Documents and Settings\Administrator\My Documents\Camtasia Studio\dd\</OutputBasePath>
<ProductionFormat>SWF</ProductionFormat>
<NumVideoSegments>1</NumVideoSegments>
<NumHTMLFiles>1</NumHTMLFiles>
<TotalProductionSize>4646294</TotalProductionSize>
<m_cf_rtRenderRangeStart>-1</m_cf_rtRenderRangeStart>
<m_cf_rtRenderRangeStop>-1</m_cf_rtRenderRangeStop>
<m_cf_strTempDir>C:\DOCUME~1\ADMINI~1\LOCALS~1\Temp\</m_cf_strTempDir>
<m_cf_bWritePIPFile>0</m_cf_bWritePIPFile>
<m_cf_bDisablePIPVideo>1</m_cf_bDisablePIPVideo>
<m_cf_bWriteIndex>0</m_cf_bWriteIndex>
<m_cf_bWriteMultipleFiles>0</m_cf_bWriteMultipleFiles>
<m_cf_strHTMLTemplateName>Template.$[AnyExtension]</m_cf_strHTMLTemplateName>
<m_cf_HTMLAlignment>1</m_cf_HTMLAlignment>
<m_cf_bSCORMOutput>0</m_cf_bSCORMOutput>
<m_cf_bController>1</m_cf_bController>
<m_cf_bAutoPlay>1</m_cf_bAutoPlay>
<m_cf_bNumberIndexMarkers>0</m_cf_bNumberIndexMarkers>
<m_cf_bShortenIndexNames>1</m_cf_bShortenIndexNames>
<m_cf_nShortenedIndexLength>17</m_cf_nShortenedIndexLength>
<m_cf_bQuizShowFeedback>1</m_cf_bQuizShowFeedback>
<m_cf_bEMailQuizResults>0</m_cf_bEMailQuizResults>
<m_cf_bEMailIncludeScore>0</m_cf_bEMailIncludeScore>
<m_cf_bJavaScriptQuizResults>0</m_cf_bJavaScriptQuizResults>
<m_cf_bHTMLOutput>1</m_cf_bHTMLOutput>
<m_cf_SCORMVersion>0</m_cf_SCORMVersion>
<m_cf_strManifestID>ID-D0020F40-B77B-4528-AD72-7550058AF711</m_cf_strManifestID>
<m_cf_strManifestTitle></m_cf_strManifestTitle>
<m_cf_strDuration>0:00:05.03</m_cf_strDuration>
<m_cf_strSubject></m_cf_strSubject>
<m_cf_strManifestDescription></m_cf_strManifestDescription>
<m_cf_strLanguage>en</m_cf_strLanguage>
<m_cf_SCORMPackage>2</m_cf_SCORMPackage>
<m_cf_strSCOTitle></m_cf_strSCOTitle>
<m_cf_ProductionFormat>8</m_cf_ProductionFormat>
<m_cf_strTitle>dd</m_cf_strTitle>
<m_cf_strAuthor></m_cf_strAuthor>
<m_cf_strCopyright></m_cf_strCopyright>
<m_cf_strDescription></m_cf_strDescription>
<m_cf_MultiFileOutputStyle>0</m_cf_MultiFileOutputStyle>
<m_cf_strVideoCompressor>Techsmith Screen Capture Codec</m_cf_strVideoCompressor>
<m_cf_bAddCaption>1</m_cf_bAddCaption>
<m_cf_bWriteCCInfo>0</m_cf_bWriteCCInfo>
<m_cf_bOverlayCaption>0</m_cf_bOverlayCaption>
<m_cf_strCaptionFontFamily>Microsoft Sans Serif</m_cf_strCaptionFontFamily>
<m_cf_bAddIPODFileToProduction>0</m_cf_bAddIPODFileToProduction>
<m_cf_bAddMP3FileToProduction>0</m_cf_bAddMP3FileToProduction>
<m_cf_bAddPPTFileToProduction>0</m_cf_bAddPPTFileToProduction>
<m_cf_strPPTFileToAddToProduction></m_cf_strPPTFileToAddToProduction>
<m_cf_bAviEncodeAudio>1</m_cf_bAviEncodeAudio>
<m_cf_AVIFrameRate>0</m_cf_AVIFrameRate>
<m_cf_nKeyFrameRate>75</m_cf_nKeyFrameRate>
<m_cf_PIP_nKeyFrameRate>30</m_cf_PIP_nKeyFrameRate>
<m_cf_PIP_AVIFrameRate>0</m_cf_PIP_AVIFrameRate>
<m_cf_SwfPlaybackControls>0</m_cf_SwfPlaybackControls>
<m_cf_strControllerSkinName>onyx</m_cf_strControllerSkinName>
<m_cf_nSwfTargetVersion>7</m_cf_nSwfTargetVersion>
<m_cf_bSwfMatchPreloadMovieSize>0</m_cf_bSwfMatchPreloadMovieSize>
<m_cf_bSwfPauseAtStart>1</m_cf_bSwfPauseAtStart>
<m_cf_bSwfPreloadAdvanced>1</m_cf_bSwfPreloadAdvanced>
<m_cf_bSwfSingleSWF>1</m_cf_bSwfSingleSWF>
<m_cf_nSwfMainMovieLoadPercent>10</m_cf_nSwfMainMovieLoadPercent>
<m_cf_nSwfPreloadMinDisplayTime>3</m_cf_nSwfPreloadMinDisplayTime>
<m_cf_clrSwfControlColor>0x00C0C0C0</m_cf_clrSwfControlColor>
<m_cf_bSwfShowAboutBtn>1</m_cf_bSwfShowAboutBtn>
<m_cf_strSwfAboutBoxTxt></m_cf_strSwfAboutBoxTxt>
<m_cf_strSwfAboutBoxInfoTxt></m_cf_strSwfAboutBoxInfoTxt>
<m_cf_strSwfAboutTitle></m_cf_strSwfAboutTitle>
<m_cf_strSwfStartTxt></m_cf_strSwfStartTxt>
<m_cf_SwfAboutBoxWidth>336</m_cf_SwfAboutBoxWidth>
<m_cf_SwfAboutBoxHeight>270</m_cf_SwfAboutBoxHeight>
<m_cf_bSwfAboutBoxTabSelected>1</m_cf_bSwfAboutBoxTabSelected>
<m_cf_strSwfTimeDisplayFormat>MM:SS</m_cf_strSwfTimeDisplayFormat>
<m_cf_bSwfCCInitiallyVisible>1</m_cf_bSwfCCInitiallyVisible>
<m_cf_bSwfFullScreen>1</m_cf_bSwfFullScreen>
<m_cf_bSwfAllowResizing>1</m_cf_bSwfAllowResizing>
<m_cf_nSwfVolueme>100</m_cf_nSwfVolueme>
<m_cf_bSwfBrandContextMenu>0</m_cf_bSwfBrandContextMenu>
<m_cf_SwfTOCType>1</m_cf_SwfTOCType>
<m_cf_SwfTOCTitle></m_cf_SwfTOCTitle>
<m_cf_bSwfShowMovieDuration>1</m_cf_bSwfShowMovieDuration>
<m_cf_bSwfShowTimeElapsed>1</m_cf_bSwfShowTimeElapsed>
<m_cf_strSwfTimeFont>Arial</m_cf_strSwfTimeFont>
<m_cf_clrSwfTimeFontColor>0x00000000</m_cf_clrSwfTimeFontColor>
<m_cf_SwfEndAction>0</m_cf_SwfEndAction>
<m_cf_strSwfEndActionUrl>http://</m_cf_strSwfEndActionUrl>
<m_cf_nSwfEndActionTarget>0</m_cf_nSwfEndActionTarget>
<m_cf_bPauseAfterJump>1</m_cf_bPauseAfterJump>
<m_cf_rtJumpPoint>0</m_cf_rtJumpPoint>
<m_cf_strSwfStartLogo></m_cf_strSwfStartLogo>
<m_cf_strSwfEndLogo></m_cf_strSwfEndLogo>
<m_cf_strSwfAboutLogo></m_cf_strSwfAboutLogo>
<m_cf_strSwfAboutTitleLogo></m_cf_strSwfAboutTitleLogo>
<m_cf_strSwfTocLogo></m_cf_strSwfTocLogo>
</Production_Data>
