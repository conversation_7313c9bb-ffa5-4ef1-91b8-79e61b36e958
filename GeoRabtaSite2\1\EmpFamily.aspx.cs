using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for EmpFamily.
	/// </summary>
	public class EmpFamily : System.Web.UI.Page
	{
		protected System.Web.UI.WebControls.Label Label2;
		protected System.Web.UI.WebControls.Label lblPCode;
		protected System.Web.UI.WebControls.Label lblEmpInfo;
		protected System.Web.UI.WebControls.Label Label1;
		protected System.Web.UI.WebControls.DataGrid dgFamily;
		protected System.Web.UI.WebControls.LinkButton LinkButton2;
		protected System.Web.UI.WebControls.Label lblFDID;
		protected System.Web.UI.WebControls.DataGrid dgFamilyReq;
		protected System.Web.UI.WebControls.Panel pnlFamilyInfo;
		protected System.Web.UI.WebControls.TextBox TextBox1;
		protected System.Web.UI.WebControls.TextBox txtX;
		protected System.Web.UI.WebControls.TextBox txtY;
		protected System.Web.UI.WebControls.Label lblFGender;
		protected System.Web.UI.WebControls.Button cmdFDelete;
		protected System.Web.UI.WebControls.Button btnFupdate;
		protected System.Web.UI.WebControls.Label lblFamilyFile;
		protected System.Web.UI.WebControls.TextBox txtOccupation;
		protected System.Web.UI.WebControls.DropDownList ddDependent;
		protected System.Web.UI.WebControls.DropDownList rdStatus;
		protected System.Web.UI.WebControls.DropDownList rdRelation;
		protected System.Web.UI.WebControls.Label lblInvisible;
		protected eWorld.UI.CalendarPopup CalendarPopup1;
		protected System.Web.UI.WebControls.TextBox txtFamilyName;
		protected System.Web.UI.WebControls.Panel Panel2;
		protected System.Web.UI.HtmlControls.HtmlInputFile fileFamily;
		protected System.Web.UI.WebControls.Label lblMsg;
		protected System.Web.UI.WebControls.Button btnNew;
		protected System.Web.UI.WebControls.Label lblSpouseTooltip;
		protected System.Web.UI.WebControls.Label lblChildrenToolTip;
		protected System.Web.UI.WebControls.Panel Panel1;
		protected System.Web.UI.WebControls.HyperLink hlMyRequests;
		protected System.Web.UI.WebControls.ImageButton imgMyGrievance;
		protected System.Web.UI.WebControls.ImageButton ImgMyAttendance;
		protected System.Web.UI.WebControls.ImageButton imgMyLeave;
		protected System.Web.UI.WebControls.ImageButton ibMySelf;
		protected System.Web.UI.WebControls.ImageButton ibSalary;
		protected System.Web.UI.WebControls.ImageButton ImageButton5;
		protected System.Web.UI.WebControls.HyperLink hlMyExp;
		protected System.Web.UI.WebControls.ImageButton imgTraining;
		protected System.Web.UI.WebControls.ImageButton ImageButton4;
		protected System.Web.UI.WebControls.ImageButton ImageButton3;
		protected System.Web.UI.WebControls.ImageButton ImageButton2;
		protected System.Web.UI.WebControls.ImageButton ImageButton1;
		private SqlConnection conn;

		private void GetFamilyInfo()
		{
			string sql="SELECT fdid, pcode, name, " +
				" CASE relationship WHEN 2 THEN 'Husband' WHEN 3 THEN 'Wife' WHEN 4 THEN 'Son' WHEN 5 THEN 'Daughter' WHEN 6 THEN 'Father' WHEN 7 THEN " +
				" 'Mother' END AS relationship, occupation, dob, isactive, CASE dependent WHEN 1 THEN 'Yes' WHEN 2 THEN 'No' ELSE '' END AS Dependent, " +
				" CASE maritialstatus WHEN 1 THEN 'Single' WHEN 2 THEN 'Married' WHEN 3 THEN 'Divorced' WHEN 4 THEN 'Widow' WHEN 5 THEN 'Separated' END AS " +
				" MaritalStatus " +
				" FROM dbo.t_FamilyDetails " +
				" WHERE (isactive = 1) AND (pcode = '"+Session["user_id"].ToString()+"') AND (relationship IN (2, 3, 4, 5)) ";
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.Fill(ds,"familyinfo");
			dgFamily.DataSource=ds.Tables[0].DefaultView;
			dgFamily.DataBind();

			sql="SELECT reqid, fdid, pcode, name, " +
				" CASE relationship WHEN 2 THEN 'Husband' WHEN 3 THEN 'Wife' WHEN 4 THEN 'Son' WHEN 5 THEN 'Daughter' WHEN 6 THEN 'Father' WHEN 7 THEN " +
				" 'Mother' END AS Relationship, " +
				" CASE maritialstatus WHEN 1 THEN 'Single' WHEN 2 THEN 'Married' WHEN 3 THEN 'Divorced' WHEN 4 THEN 'Widow' WHEN 5 THEN 'Separated' END AS " +
				" maritalstatus, occupation, dob, CASE Dependent WHEN 1 THEN 'Yes' WHEN 2 THEN 'No' END AS Dependent, " +
				" CASE addflag WHEN 1 THEN 'New family memebr' WHEN 2 THEN 'Changes' WHEN 3 THEN 'Request for Remove' END AS AddFlag, wfid, " +
				" c_at AS RequestedOn "+
				" FROM dbo.t_FamilyDetailsRequest AS fdr " +
				" WHERE (AppFlag = 2) AND (pcode = '"+Session["user_id"].ToString()+"') ";
			da=new SqlDataAdapter(sql,conn);
			da.Fill(ds,"requestedfamilyinfo");
			dgFamilyReq.DataSource=ds.Tables[1].DefaultView;
			dgFamilyReq.DataBind();
			for(int j=0;j<dgFamilyReq.Items.Count;j++)
			{
				LinkButton lb=(LinkButton)dgFamilyReq.Items[j].FindControl("lbFCancelRequest");
				lb.Attributes.Add("onclick","return confirm('Are you sure to delete this request');");
			}

		}

		public string GetTooltip(int ID)
		{
			SqlConnection con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select tooltip from t_fieldsmgt where f_id="+ID+"",con);
			string message="";
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				message=rd[0].ToString();	
			}
			message+="<br>";
			message=HttpUtility.HtmlEncode(message);
			message=message.Replace("\r\n","<br/>");
			rd.Close();
			cmd=new SqlCommand("select d.documentname from t_documentsrequired d,t_fieldmgtdocEmp f where f.documentbyemployee=d.d_id and f.f_id="+ID+"",con);
			rd=cmd.ExecuteReader();
			message+="<ul>";
			while(rd.Read())
			{
				//message+="<STRONG>/STRONG> "+rd[0].ToString()+"<br>";
				message+="<li>"+HttpUtility.HtmlEncode(rd[0].ToString()).Replace("\r\n","<br/>")+"</li>";
			}
			message+="</ul>";
			rd.Close();
			con.Close();
			//Response.Write(message);
			return message;
		}


		private void Page_Load(object sender, System.EventArgs e)
		{
			conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			lblMsg.Visible=false;
			if(!IsPostBack)
			{
				ibMySelf.Attributes.Add("onclick","window.open('myinfo.aspx','MyProfile','toolbar=no,statusbar=no,addressbar=no,scrollbars=yes,resizable=no,width=700,height=650');return false;");

				lblSpouseTooltip.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Family</font></b><br/><br/>"+GetTooltip(40)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				lblSpouseTooltip.Attributes.Add("onmouseout","UnTip()");

				lblChildrenToolTip.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Family</font></b><br/><br/>"+GetTooltip(41)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				lblChildrenToolTip.Attributes.Add("onmouseout","UnTip()");

				GetFamilyInfo();
				ImageButton5.Attributes.Add("onclick","window.open('admin/organogram.htm','_blank','resizable=yes,menubar=no,scrollbars=yes,top=0,status=no');return false;");
				this.imgMyLeave.Attributes.Add("onclick","return OpenURL('MyLeaveBalance.aspx',650,300);");
				this.ImgMyAttendance.Attributes.Add("onclick","return OpenURL('MyAttendance.aspx',700,400);");
				//this.Image1.ImageUrl=@"employee\"+Session["user_id"].ToString()+".jpg";
				if(Request.QueryString.Count>0)
				{
					this.lblPCode.Text=Request.QueryString[0].ToString();
					this.lblEmpInfo.Text=Request.QueryString[1].ToString();
				}
			}

		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.ImageButton1.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton1_Click);
			this.ImageButton2.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton2_Click);
			this.ImageButton3.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton3_Click);
			this.ImageButton4.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton4_Click);
			this.imgTraining.Click += new System.Web.UI.ImageClickEventHandler(this.imgTraining_Click);
			this.ImageButton5.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton5_Click);
			this.imgMyGrievance.Click += new System.Web.UI.ImageClickEventHandler(this.imgMyGrievance_Click);
			this.dgFamily.SelectedIndexChanged += new System.EventHandler(this.dgFamily_SelectedIndexChanged);
			this.LinkButton2.Click += new System.EventHandler(this.LinkButton2_Click);
			this.btnFupdate.Click += new System.EventHandler(this.btnFupdate_Click);
			this.cmdFDelete.Click += new System.EventHandler(this.cmdFDelete_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		private void LinkButton2_Click(object sender, System.EventArgs e)
		{
			ResetFamilyForm();			
			Panel2.Visible=true;
			lblFDID.Text="";
			LinkButton2.Visible=false;
			dgFamily.SelectedIndex=-1;
			dgFamilyReq.SelectedIndex=-1;
			lblFamilyFile.Visible=true;
			fileFamily.Visible=true;
			pnlFamilyInfo.Visible=false;
		}

		private void ResetFamilyForm()
		{
			cmdFDelete.Visible=false;
			txtFamilyName.Text="";
			rdRelation.SelectedIndex=0;
			rdStatus.SelectedIndex=0;
			ddDependent.SelectedIndex=0;
			txtOccupation.Text="";
			dgFamily.SelectedIndex=-1;
			dgFamilyReq.SelectedIndex=-1;
			pnlFamilyInfo.Visible=true;
			Panel2.Visible=false;
			//dgFamilyReq.Visible=true;
			dgFamily.Visible=true;
			

		}
		private void dgFamily_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			cmdFDelete.Visible=true;
			pnlFamilyInfo.Visible=false;
			//lblFFlag.Text="1";
			LinkButton2.Visible=false;
			Panel2.Visible=true;
			//string fdid=dgFamily.SelectedItem.Cells[0].Text;
			string fdid=dgFamily.Items[dgFamily.SelectedIndex].Cells[0].Text;
			lblFDID.Text=fdid;
			string sql="SELECT fdid, pcode, name, relationship, maritialstatus, gender, occupation, dob, isactive, Dependent "+
				" FROM dbo.t_FamilyDetails " +
				" WHERE (fdid = "+fdid+") AND (isactive=1)";
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			
			DataSet ds=new DataSet();
			da.Fill(ds);
			txtFamilyName.Text=ds.Tables[0].Rows[0]["name"].ToString();
			txtOccupation.Text=ds.Tables[0].Rows[0]["occupation"].ToString();
			SetDDL(rdRelation,ds.Tables[0].Rows[0]["relationship"].ToString());
			SetDDL(rdStatus,ds.Tables[0].Rows[0]["maritialstatus"].ToString());
			SetDDL(ddDependent,ds.Tables[0].Rows[0]["Dependent"].ToString());
			try
			{
				CalendarPopup1.SelectedDate=DateTime.Parse(ds.Tables[0].Rows[0]["dob"].ToString());
			}
			catch
			{

			}
			btnNew.Attributes.Clear();
			cmdFDelete.Attributes.Clear();
			if(isFamilyInfoRequested(fdid))
			{
				btnNew.Attributes.Add("onclick", "return confirm('A pending request already exist for this family member do you want to resend it?'); ");
				cmdFDelete.Attributes.Add("onclick", "return confirm('A pending request already exist for this family member do you want to resend it for removal?'); ");
			}
			else
			{
				cmdFDelete.Attributes.Add("onclick", "return confirm('Are you sure to remove this family memeber?'); ");
			}
			lblFamilyFile.Visible=false;
			fileFamily.Visible=false;

		}
		private bool isFamilyInfoRequested(string fdid)
		{
			if (fdid!="")
			{
				string sql="SELECT reqid FROM dbo.t_FamilyDetailsRequest WHERE (AppFlag = 2) AND (fdid = "+fdid+")";
				SqlConnection conn=new SqlConnection(Connection.ConnectionString);
				SqlDataAdapter da=new SqlDataAdapter(sql,conn);
				DataSet ds=new DataSet();
				da.Fill(ds);
				if(ds.Tables[0].Rows.Count>0)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				return false;
			}
		}

		private string GetTimeline(string wfid)
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			string sql=" SELECT timeline " +
				" FROM dbo.t_fieldsmgt " +
				" WHERE (f_id = "+wfid+") ";
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			return ds.Tables[0].Rows[0][0].ToString();
		}

		private string GetReplyMsg(string type, string wfid)
		{
			string sql="SELECT "+type+" FROM dbo.t_fieldsmgt WHERE (f_id = "+wfid+")";
			SqlConnection conn2=new SqlConnection(Connection.ConnectionString);
			conn2.Open();
			SqlDataAdapter da=new SqlDataAdapter(sql,conn2);
			DataSet ds=new DataSet();
			da.Fill(ds);
			
			conn2.Close();
			conn2.Dispose();
			return ds.Tables[0].Rows[0][type].ToString();
		}

		private void btnNew_Click(object sender, System.EventArgs e)
		{
			string replyatrequest="";//GetReplyMsg("replyatrequest","38");
			//string replyonreject="";
			SqlConnection con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlTransaction tran = con.BeginTransaction();

			
			string sql="";
			
			string gid=Guid.NewGuid().ToString();
			string fid=gid+".jpg";
			gid=fid;

			if(lblFamilyFile.Visible==true)
			{
				fileFamily.PostedFile.SaveAs(Server.MapPath("tempEmployee/"+"/"+fid));
			}

			//SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			//conn.Open();
			bool flag=false;
			if(isFamilyInfoRequested(lblFDID.Text))
			{
				string ssql="update t_familydetailsrequest set AppFlag=4 where fdid=" + lblFDID.Text + " and AppFlag=2";
				SqlCommand cmd2=new SqlCommand(ssql,con);
				cmd2.Transaction=tran;
				cmd2.ExecuteNonQuery();
				flag=true;
			}

			sql="insert into t_FamilyDetailsRequest(fdid,pcode, name, relationship, maritialstatus, gender, occupation, dob, Dependent, Addflag,wfid, AppFlag,c_at,filename,requesttimeline) " +
				" values (@fdid, @pcode, @name, @relationship, @maritialstatus, @gender, @occupation, @dob, @Dependent, @Addflag,@wfid, @AppFlag,getdate(),@filename,@requesttimeline) ";
				
			SqlCommand cmd=new SqlCommand(sql,con);
			cmd.Transaction=tran;	
			SqlParameter _fdid=new SqlParameter("@fdid",SqlDbType.Int);
			if(lblFDID.Text=="")
				_fdid.Value=DBNull.Value;
			else
				_fdid.Value=lblFDID.Text;

			SqlParameter _pcode=new SqlParameter("@pcode",Session["user_id"].ToString());
			SqlParameter _name=new SqlParameter("@name",txtFamilyName.Text.Trim());
			SqlParameter _relationship=new SqlParameter("@relationship",rdRelation.SelectedValue);
			SqlParameter _maritialstatus=new SqlParameter("@maritialstatus",rdStatus.SelectedValue);
			int gen=0;
			if(rdRelation.SelectedValue=="2" || rdRelation.SelectedValue=="4")
				gen=1;
			else if (rdRelation.SelectedValue=="3" || rdRelation.SelectedValue=="5")
				gen=2;
			SqlParameter _gender=new SqlParameter("@gender",gen);
			SqlParameter _occupation=new SqlParameter("@occupation",txtOccupation.Text.Trim());
			SqlParameter _dependent=new SqlParameter("@dependent",ddDependent.SelectedValue);
			SqlParameter _requesttimeline=new SqlParameter("@requesttimeline",SqlDbType.Int);

				
			SqlParameter _addflag=new SqlParameter("@addflag",SqlDbType.Int);	// Request Add Flag 1 for New Family Member 2 For Edit Family Member 3 For Delete Family Member
			if(lblFDID.Text=="")
				_addflag.Value=1;
			else
				_addflag.Value=2;
			SqlParameter _AppFlag=new SqlParameter("@AppFlag",2);	// 1=Approved, 2=Pendding, 0=Rejected
			SqlParameter _wfid=new SqlParameter("@wfid",SqlDbType.Int);
			if(rdRelation.SelectedValue=="2" || rdRelation.SelectedValue=="3")
			{
				_requesttimeline.Value=GetTimeline("40");
				replyatrequest=GetReplyMsg("replyatrequest","40");
				
				_wfid.Value=40;
			}
			else if(rdRelation.SelectedValue=="4" || rdRelation.SelectedValue=="5")
			{
				_requesttimeline.Value=GetTimeline("41");
				replyatrequest=GetReplyMsg("replyatrequest","40");
				
				_wfid.Value=41;
			}
			SqlParameter _filename=new SqlParameter("@filename",SqlDbType.VarChar,500);
			if(lblFamilyFile.Visible==true)
			{
				_filename.Value=fid;
			}
			else
			{
				_filename.Value=DBNull.Value;
			}
				
			SqlParameter _dob=new SqlParameter("@dob",CalendarPopup1.SelectedDate.ToString("d MMM, yyyy"));
			cmd.Parameters.Add(_fdid);
			cmd.Parameters.Add(_pcode);
			cmd.Parameters.Add(_name);
			cmd.Parameters.Add(_relationship);
			cmd.Parameters.Add(_maritialstatus);
			cmd.Parameters.Add(_gender);
			cmd.Parameters.Add(_occupation);
			cmd.Parameters.Add(_dependent);
			cmd.Parameters.Add(_addflag);
			cmd.Parameters.Add(_AppFlag);
			cmd.Parameters.Add(_wfid);
			cmd.Parameters.Add(_dob);
			cmd.Parameters.Add(_filename);
			cmd.Parameters.Add(_requesttimeline);

			try
			{
				if(flag)
					Bulletin.PostReqMessage(con,tran,"Request Canceled By User","GEO Raabta",Session["user_id"].ToString(),2,"Family","t_FamilyDetailsRequest");
				cmd.ExecuteNonQuery();
				Panel2.Visible=false;
				lblFDID.Text="";
				Bulletin.PostReqMessage(con,tran,replyatrequest,"GEO Raabta",Session["user_id"].ToString(),1,"Family","t_FamilyDetailsRequest");
				tran.Commit();
				con.Close();
				GetFamilyInfo();
				LinkButton2.Visible=true;
				Panel2.Visible=false;
				ResetFamilyForm();
				lblMsg.Visible=true;
			}
			catch(Exception)
			{
				tran.Rollback();
			}
		}

		private void btnFupdate_Click(object sender, System.EventArgs e)
		{
			Panel2.Visible=false;
			lblFDID.Text="";
			btnNew.Attributes.Clear();
			LinkButton2.Visible=true;
			pnlFamilyInfo.Visible=true;
		}

		public void RemoveFmailyRequest(object sender, EventArgs e)
		{
			LinkButton rLink=(LinkButton)sender;
			for(int i=0;i<this.dgFamilyReq.Items.Count;i++)
			{
				LinkButton lnk=(LinkButton)this.dgFamilyReq.Items[i].FindControl("lbFCancelRequest");
				if(lnk.Equals(rLink))
				{
					string reqid=dgFamilyReq.Items[i].Cells[0].Text;
					string sql="Update t_FamilyDetailsRequest set appflag=4, AppRejDate=getdate() where reqid="+reqid;
					SqlConnection conn=new SqlConnection(Connection.ConnectionString);
					conn.Open();
					SqlCommand cmd=new SqlCommand(sql,conn);
					cmd.ExecuteNonQuery();
					conn.Close();
					GetFamilyInfo();
					break;
				}
			}
			ResetFamilyForm();
		}

		private void SetDDL(DropDownList ddl, string val)
		{
			ddl.SelectedIndex=0;
			for(int j=0;j<ddl.Items.Count;j++)
			{
				if(ddl.Items[j].Value==val)
				{
					ddl.SelectedIndex=j;
					break;
				}
			}
		}


		private void cmdFDelete_Click(object sender, System.EventArgs e)
		{
			string replyatrequest="";//GetReplyMsg("replyatrequest","38");
			string sql="";
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			SqlTransaction tran=conn.BeginTransaction();

			bool flag=false;
			if(isFamilyInfoRequested(lblFDID.Text))
			{
				string ssql="update t_familydetailsrequest set AppFlag=4 where fdid=" + lblFDID.Text + " and AppFlag=2";
				SqlCommand cmd2=new SqlCommand(ssql,conn);
				cmd2.Transaction=tran;
				cmd2.ExecuteNonQuery();
				flag=true;
			}

			sql="insert into t_FamilyDetailsRequest(fdid,pcode, name, relationship, maritialstatus, gender, occupation, dob, Dependent, Addflag,wfid, AppFlag,c_at) " +
				" values (@fdid, @pcode, @name, @relationship, @maritialstatus, @gender, @occupation, @dob, @Dependent, 3,@wfid, 2,getdate()) ";
				
			SqlCommand cmd=new SqlCommand(sql,conn);
			cmd.Transaction=tran;

			SqlDataAdapter da=new SqlDataAdapter("select * from t_familydetails where fdid="+lblFDID.Text,conn);
			DataSet	ds=new DataSet();
			da.SelectCommand.Transaction=tran;

			da.Fill(ds);
			SetParameterValue(cmd,ds,"fdid");
			SetParameterValue(cmd,ds,"pcode");
			SetParameterValue(cmd,ds,"name");
			SetParameterValue(cmd,ds,"relationship");
			SetParameterValue(cmd,ds,"maritialstatus");
			SetParameterValue(cmd,ds,"gender");
			SetParameterValue(cmd,ds,"occupation");
			SetParameterValue(cmd,ds,"dob");
			SetParameterValue(cmd,ds,"Dependent");
				
			SqlParameter _wfid=new SqlParameter("@wfid",SqlDbType.Int);
			if(rdRelation.SelectedValue=="2" || rdRelation.SelectedValue=="3")
			{
				_wfid.Value=40;
				replyatrequest=GetReplyMsg("replyatrequest","40");
			}
			else
			{
				_wfid.Value=41;
				replyatrequest=GetReplyMsg("replyatrequest","41");
			}

			cmd.Parameters.Add(_wfid);	
	
			if(flag)
				Bulletin.PostReqMessage(conn,tran,"Request Canceled By User","GEO Raabta",Session["user_id"].ToString(),2,"Family","t_FamilyDetailsRequest");

			cmd.ExecuteNonQuery();
			Bulletin.PostReqMessage(conn,tran,replyatrequest,"GEO Raabta",Session["user_id"].ToString(),1,"Family","t_FamilyDetailsRequest");
			tran.Commit();

			Panel2.Visible=false;
			lblFDID.Text="";
			conn.Close();
			GetFamilyInfo();
			LinkButton2.Visible=true;
			dgFamily.SelectedIndex=-1;
			lblMsg.Visible=true;
			pnlFamilyInfo.Visible=true;
			Panel2.Visible=false;
			

		}
		private void SetParameterValue(SqlCommand cmd, DataSet ds, string field)
		{
			cmd.Parameters.Add("@"+field,ds.Tables[0].Rows[0][field].ToString());
		}

		private void imgEmp_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("UserProfile.aspx?id=1");
		}

		private void imgMyGrievance_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("MyGrievance.aspx?pcode="+lblPCode.Text+"&name="+lblEmpInfo.Text);
		}

		private void imgPersonal_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("UserProfile.aspx?id=2");
		}

		private void imgFamily_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("EmpFamily.aspx?pcode="+this.lblPCode.Text+"&name="+this.lblEmpInfo.Text);
		}

		private void imgEducation_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("EmpEducation.aspx?pcode="+this.lblPCode.Text+"&name="+this.lblEmpInfo.Text);
		}

		private void imgTraining_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("EmpTraining.aspx?pcode="+this.lblPCode.Text+"&name="+this.lblEmpInfo.Text);
		}

		private void ImageButton1_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("UserProfile.aspx?id=1");
		}

		private void ImageButton2_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("UserProfile.aspx?id=2");
		}
		private void ImageButton5_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			//pnlPaySlip.Visible=false;
			//Response.Write("<script language = Javascript >var win=window.showModalDialog('admin/textonimage1.aspx','','dialogHeight:900px;dialogWidth:800px;center:yes')</script"); 
			Response.Write("<script language = Javascript >win=window.open('admin/orgamogram.htm','_blank','resizable=yes,menubar=no,scrollbars=yes,top=0,status=0',true).focus();</script>");

		}

		private void ImageButton4_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("empeducation.aspx");
		}

		private void ImageButton3_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("empfamily.aspx");
		}








	}
}
