<%@ Page CodeBehind="MyGrievance.aspx.cs" Language="c#" AutoEventWireup="false" Inherits="GeoRabtaSite.MyGrievance" %>
<%@ Register TagPrefix="uc1" TagName="MenuControl" Src="MenuControl.ascx" %>
<%@ Register TagPrefix="uc1" TagName="Organimeter" Src="Organimeter.ascx" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Rabta :: User Profile</title>
		<meta content="text/html; charset=utf-8" http-equiv="Content-Type">
		<meta name="GENERATOR" content="Microsoft Visual Studio .NET 7.1">
		<meta name="vs_targetSchema" content="http://schemas.microsoft.com/intellisense/ie5">
		<LINK rel="stylesheet" type="text/css" href="StyleSheet1.css">
		<style type="text/css">.style2 { FONT-WEIGHT: bold; FONT-SIZE: 16pt; COLOR: #ffffff }
	A:link { COLOR: white }
		</style>
		<script type="text/javascript" src="jquery-1.2.6.js"></script>
		<script language="javascript" type="text/javascript" src="jquery.MetaData.js"></script>
		<script type="text/javascript" src="documentation.js"></script>
		<script language="javascript" type="text/javascript" src="jquery.MultiFile.js"></script>
		<script language="javascript" type="text/javascript" src="jquery.blockUI.js"></script>
		<script language="javascript">
     String.prototype.trim = function() {
	return this.replace(/^\s+|\s+$/g,"");
}

function OpenURL(file,docw,doch)
{
var w=screen.width;
var h=screen.height;
var l=(w-docw)/2;
var t=(h-doch)/2;
var viewimageWin=window.open(file,"codewindow","toolbar=0,scrollbars=1,location=0,statusbar=0,menubar=0,resizable=0,width="+docw+",height="+doch+",left=" + l + ",top="+t);
return false;
}
function validateEdu()
{
	var txtDegree=document.getElementById("txtDegree");
	var txtOtherDegree=document.getElementById("txtOtherDegree");
	var txtInstitute=document.getElementById("txtInstitute");
	var txtOtherInstitute=document.getElementById("txtOtherInstitute");
	var txtResult=document.getElementById("txtResult");
	var txtOtherMajors=document.getElementById("txtOtherMajors");
	var DurationFrom=document.getElementById("DurationFrom");
	var DurationTo=document.getElementById("DurationTo");
	if(txtDegree.selectedIndex==0)
	{
		alert("Degree is required");
		txtDegree.focus();
		return false;
	}
	else if (txtDegree[txtDegree.selectedIndex].value=="-1")
	{
		if(txtOtherDegree.value.trim()=="")
		{
			alert("Degree is required");
			txtOtherDegree.focus();
			return false;
		}
	}

	if(txtInstitute.selectedIndex==0)
	{
		alert("Institute is required");
		txtInstitute.focus();
		return false;
	}
	else if (txtInstitute[txtInstitute.selectedIndex].value=="-1")
	{
		if(txtOtherInstitute.value=="")
		{
			alert("Institute is required");
			txtOtherInstitute.focus();
			return false;
		}
	}
	
	if(txtResult.selectedIndex==0)
	{
		alert("Result is required");
		txtResult.focus();
		return false;
	}
	
	if(txtOtherMajors.value=="")
	{
		alert("Please enter Education Major");
		txtOtherMajors.focus();
		return false;
	}
			
	if(DurationFrom.selectedIndex==0)
	{
		alert("'Duration From' is required");
		DurationFrom.focus();
		return false;
	}
			
	if(DurationTo.selectedIndex==0)
	{
		alert("'Duration To' is required");
		DurationTo.focus();
		return false;
	}
	var fileEdu=document.getElementById("fileEdu");
	if(fileEdu.value=="")
	{
		alert("Document is required, please select file for upload");
		return false;
	}
	return true;

}

	
function OpenURL(file,docw,doch)
{
	var w=screen.width;
	var h=screen.height;
	var l=(w-docw)/2;
	var t=(h-doch)/2;
	var viewimageWin=window.open(file,"codewindow","toolbar=0,scrollbars=1,location=0,statusbar=0,menubar=0,resizable=0,width="+docw+",height="+doch+",left=" + l + ",top="+t);
	return false;
}

function window_onload() 
{
//	d=document.getElementById('info');
//	d.style.visibility='hidden';
	document.body.scrollTop =document.getElementById("txtX").value;
	document.body.scrollLeft=document.getElementById("txtY").value;
}
function displayDiv()
{
	d=document.getElementById('info');
	d.style.visibility='visible';
	d.style.left=window.event.x+document.body.scrollLeft+10;
	d.style.top=window.event.y+document.body.scrollTop+15;
	var val=document.getElementById('TextBox1');
	d.innerHTML=val.value;
}


function window_onscroll() 
{
	document.getElementById("txtX").value=document.body.scrollTop;
	document.getElementById("txtY").value=document.body.scrollLeft;
}


		</script>
	</HEAD>
	<body onscroll="return window_onscroll()" oncontextmenu="return false" language="javascript"
		onselectstart="return false" ondrag="return false" onload="return window_onload()"
		bottomMargin="0" background="images\bg.jpg" leftMargin="0" rightMargin="0" bgProperties="fixed"
		topMargin="0">
		<script type="text/javascript" src="wz_tooltip.js"></script>
		<form id="myForm" runat="server">
			<table id="Table1" border="0" cellSpacing="0" cellPadding="0" width="1004" height="100%">
				<tr>
					<td style="HEIGHT: 200px">
						<OBJECT id="Shockwaveflash1" codeBase="http://fpdownload.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,0,0"
							height="200" width="1004" align="middle" classid="clsid:d27cdb6e-ae6d-11cf-96b8-444553540000"
							VIEWASTEXT>
							<PARAM NAME="_cx" VALUE="26564">
							<PARAM NAME="_cy" VALUE="5292">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="top.swf">
							<PARAM NAME="Src" VALUE="top.swf">
							<PARAM NAME="WMode" VALUE="Transparent">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="sameDomain">
							<PARAM NAME="Scale" VALUE="ExactFit">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="0066FF">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="top.swf" width="1004" height="200" align="middle" quality="high" wmode="transparent"
								bgcolor="#0066ff" scale="exactfit" allowscriptaccess="sameDomain" type="application/x-shockwave-flash"
								pluginspage="http://www.macromedia.com/go/getflashplayer" />
						</OBJECT>
					</td>
				</tr>
				<tr>
					<td vAlign="top" align="center">
						<table id="Table2" border="0" cellSpacing="0" cellPadding="0" width="100%">
							<tr>
								<td vAlign="top" align="center" width="176"><uc1:menucontrol id="MenuControl1" runat="server"></uc1:menucontrol></td>
								<td style="WIDTH: 650px" vAlign="top" align="left" borderColor="white">
									<table style="BACKGROUND-COLOR: white" border="0" cellSpacing="0" cellPadding="2" width="100%"
										align="center">
										<tr>
											<td bgColor="#004477" height="65" background="images/PanelTop.jpg">
												<table border="0" cellSpacing="0" cellPadding="4" width="650">
													<tr>
														<td style="WIDTH: 40px"></td>
														<td><SPAN class="style2">My Grievance</SPAN>&nbsp;
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td width="650"><asp:imagebutton id="imgEmp" runat="server" CausesValidation="False" ImageUrl="images\employee.gif"></asp:imagebutton><asp:imagebutton id="imgPersonal" runat="server" CausesValidation="False" ImageUrl="images\personal.gif"></asp:imagebutton><asp:imagebutton id="imgFamily" runat="server" CausesValidation="False" ImageUrl="images\family.gif"></asp:imagebutton><asp:imagebutton id="imgEducation" runat="server" CausesValidation="False" ImageUrl="images\education.gif"></asp:imagebutton>
												<asp:imagebutton id="imgTraining" runat="server" ImageUrl="images\training.gif"></asp:imagebutton>
												<asp:HyperLink id="hlMyExp" runat="server" ImageUrl="images/MyExperience.gif" BorderWidth="0px"
													NavigateUrl="EmpExperience.aspx" BorderStyle="None">HyperLink</asp:HyperLink><asp:imagebutton id="imgOrganogram" runat="server" CausesValidation="False" ImageUrl="buttons/myorganogram.gif"></asp:imagebutton>
												<asp:imagebutton id="ibSalary" runat="server" CausesValidation="False" ImageUrl="images/payslip.gif"></asp:imagebutton><asp:imagebutton id="ibMySelf" runat="server" CausesValidation="False" ImageUrl="Images\MeMyself.gif"
													Visible="False"></asp:imagebutton><asp:imagebutton id="imgMyLeave" runat="server" CausesValidation="False" ImageUrl="images\MyLeaveBalance.gif"></asp:imagebutton><asp:imagebutton id="ImgMyAttendance" runat="server" CausesValidation="False" ImageUrl="images\MyAttendance.gif"></asp:imagebutton>
												<asp:imagebutton id="imgMyGrievance" runat="server" ImageUrl="images\mygrievance.gif"></asp:imagebutton>
												<asp:HyperLink id="hlMyRequests" runat="server" ImageUrl="images/myrequests.gif" BorderStyle="None"
													NavigateUrl="MyRequest.aspx" BorderWidth="0px">My Requests</asp:HyperLink><br>
												<asp:panel id="Panel1" runat="server" BackImageUrl="images\tabstrip.jpg" Height="10px" Width="100%"></asp:panel>
												<uc1:organimeter id="Organimeter1" runat="server"></uc1:organimeter>
												<asp:label id="lblEmpInfo" runat="server" Visible="False" Font-Bold="True" Font-Size="Medium"></asp:label>
												<asp:label id="lblPCode" runat="server" Visible="False" Font-Bold="True" Font-Size="Medium"></asp:label><BR>
												<asp:panel id="pnlMyGrievance" runat="server" Visible="False" Width="100%">
													<asp:DataGrid id="dgMyGrievance" runat="server" BorderWidth="1px" Visible="False" Width="100%"
														BorderColor="Tan" BackColor="LightGoldenrodYellow" CellPadding="2" ForeColor="Black" AutoGenerateColumns="False">
														<FooterStyle BackColor="Tan"></FooterStyle>
														<SelectedItemStyle ForeColor="GhostWhite" BackColor="DarkSlateBlue"></SelectedItemStyle>
														<AlternatingItemStyle BackColor="PaleGoldenrod"></AlternatingItemStyle>
														<HeaderStyle Font-Bold="True" BackColor="Tan"></HeaderStyle>
														<Columns>
															<asp:TemplateColumn>
																<ItemTemplate>
																	<asp:LinkButton id="lnkView" onclick="SetView" runat="server" ForeColor="Blue">View</asp:LinkButton>
																</ItemTemplate>
															</asp:TemplateColumn>
															<asp:ButtonColumn Text="View" CommandName="Select"></asp:ButtonColumn>
															<asp:BoundColumn DataField="grievancetitle" HeaderText="Title"></asp:BoundColumn>
															<asp:BoundColumn Visible="False" DataField="grievancedetail" HeaderText="Detail"></asp:BoundColumn>
															<asp:BoundColumn DataField="requestdate" HeaderText="Request on"></asp:BoundColumn>
															<asp:BoundColumn DataField="requesttimeline" HeaderText="Request Timeline"></asp:BoundColumn>
															<asp:BoundColumn DataField="requeststatus" HeaderText="Status"></asp:BoundColumn>
															<asp:BoundColumn DataField="rid" HeaderText="Request ID">
																<ItemStyle HorizontalAlign="Center"></ItemStyle>
															</asp:BoundColumn>
															<asp:BoundColumn Visible="False" DataField="sgid"></asp:BoundColumn>
															<asp:BoundColumn DataField="status"></asp:BoundColumn>
															<asp:BoundColumn Visible="False" DataField="sgrievance"></asp:BoundColumn>
															<asp:BoundColumn Visible="False" DataField="grievance"></asp:BoundColumn>
															<asp:TemplateColumn>
																<ItemTemplate>
																	<asp:LinkButton id="lnkCancel" onclick="SetCancel" runat="server" ForeColor="Blue">Withdrawl</asp:LinkButton>
																</ItemTemplate>
															</asp:TemplateColumn>
														</Columns>
														<PagerStyle HorizontalAlign="Center" ForeColor="DarkSlateBlue" BackColor="PaleGoldenrod"></PagerStyle>
													</asp:DataGrid>
													<asp:Panel id="pnlWithdrawl" runat="server">
														<STRONG>
															<asp:Label id="lblClosingCode" runat="server">Select Closing Code</asp:Label><BR>
															<asp:DropDownList id="ddClosingCode" runat="server" CssClass="textbox"></asp:DropDownList><BR>
															<asp:Button id="btnSave" runat="server" CssClass="button" Text="Submit"></asp:Button></STRONG></asp:Panel>
												</asp:panel>
												<asp:panel id="pnlgView" runat="server" Visible="False" Height="222px">
													<STRONG>
														<TABLE id="Table3" cellSpacing="1" cellPadding="1" width="100%" border="0">
															<TR>
																<TD class="FormColor1" style="WIDTH: 149px" vAlign="top" noWrap align="left"><STRONG>Request 
																		ID:</STRONG></TD>
																<TD class="FormColor1" vAlign="top" align="left">
																	<asp:Label id="lblRID" runat="server"></asp:Label></TD>
															</TR>
															<TR>
																<TD class="FormColor2" style="WIDTH: 149px" vAlign="top" noWrap align="left"><STRONG>Grievance 
																		Subject:&nbsp;</STRONG></TD>
																<TD class="FormColor2" vAlign="top" align="left">
																	<asp:Label id="lblTitle" runat="server"></asp:Label></TD>
															</TR>
															<TR>
																<TD class="FormColor1" style="WIDTH: 149px" vAlign="top" noWrap align="left"><STRONG>Grievance 
																		Type:&nbsp;&nbsp;</STRONG></TD>
																<TD class="FormColor1" vAlign="top" align="left">
																	<asp:Label id="lblGrievanceType" runat="server"></asp:Label></TD>
															</TR>
															<TR>
																<TD class="FormColor2" style="WIDTH: 149px" vAlign="top" noWrap align="left"><STRONG>Grievance 
																		Sub Type:</STRONG></TD>
																<TD class="FormColor2" vAlign="top" align="left">
																	<asp:Label id="lblGrievanceSubType" runat="server"></asp:Label></TD>
															</TR>
															<TR>
																<TD class="FormColor1" style="WIDTH: 149px" vAlign="top" noWrap align="left"><STRONG>Grievance 
																		Detail:&nbsp;</STRONG></TD>
																<TD class="FormColor1" vAlign="top" align="left">
																	<asp:Label id="txtpDetail" runat="server"></asp:Label></TD>
															</TR>
														</TABLE>
														Grievance Blog:<BR>
														<asp:DataGrid id="dgBlog" runat="server" BorderWidth="1px" Visible="False" Width="100%" BorderColor="Tan"
															BackColor="LightGoldenrodYellow" CellPadding="2" ForeColor="Black" AutoGenerateColumns="False">
															<FooterStyle BackColor="Tan"></FooterStyle>
															<SelectedItemStyle ForeColor="GhostWhite" BackColor="DarkSlateBlue"></SelectedItemStyle>
															<AlternatingItemStyle BackColor="PaleGoldenrod"></AlternatingItemStyle>
															<HeaderStyle Font-Bold="True" BackColor="Tan"></HeaderStyle>
															<Columns>
																<asp:BoundColumn DataField="comment" HeaderText="Comments"></asp:BoundColumn>
																<asp:BoundColumn DataField="sendon" HeaderText="Post on"></asp:BoundColumn>
																<asp:BoundColumn DataField="pcode" HeaderText="Pcode"></asp:BoundColumn>
																<asp:BoundColumn DataField="sendby" HeaderText="Posted by"></asp:BoundColumn>
																<asp:BoundColumn DataField="senderdesignation" HeaderText="Designation"></asp:BoundColumn>
																<asp:BoundColumn DataField="senderdepartment" HeaderText="Department"></asp:BoundColumn>
															</Columns>
															<PagerStyle HorizontalAlign="Center" ForeColor="DarkSlateBlue" BackColor="PaleGoldenrod"></PagerStyle>
														</asp:DataGrid><BR>
													</STRONG>
													<P><STRONG>Comments:</STRONG><BR>
														<asp:TextBox id="txtComments" runat="server" Width="100%" Height="74px" CssClass="textbox" TextMode="MultiLine"></asp:TextBox><BR>
														<BR>
														<asp:Button id="btnGrievanceSubmit" runat="server" CssClass="button" Text="Submit"></asp:Button>
														<asp:Button id="btnCancel" runat="server" CssClass="button" Text="Cancel"></asp:Button></P>
												</asp:panel>
												<asp:Label id="lblmessage" runat="server"></asp:Label><br>
												<br>
												<asp:textbox style="VISIBILITY: hidden" id="TextBox1" runat="server"></asp:textbox><asp:textbox style="VISIBILITY: hidden" id="txtX" runat="server" Width="24px"></asp:textbox><asp:textbox style="VISIBILITY: hidden" id="txtY" runat="server" Width="24px"></asp:textbox></td>
										</tr>
									</table>
								</td>
								<td vAlign="top" align="center" width="200">
								</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td class="Fotter" height="20" align="center">Copyright © 2005 Independent Media 
						Corporation www.geo.tv<br>
					</td>
				</tr>
			</table>
		</form>
	</body>
</HTML>
