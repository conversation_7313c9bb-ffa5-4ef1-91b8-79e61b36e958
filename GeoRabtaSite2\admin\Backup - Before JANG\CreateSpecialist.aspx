<%@ Page CodeBehind="CreateSpecialist.aspx.cs" Language="c#" AutoEventWireup="false" Inherits="GeoRabtaSite.CreateSpecialist" smartNavigation="False" %>
<%@ Register TagPrefix="uc1" TagName="myMenus" Src="myMenus.ascx" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Rabta :: Manage Queue List Specialist </title>
		<META content="text/html; charset=utf-8" http-equiv="Content-Type">
		<meta name="GENERATOR" content="Microsoft Visual Studio .NET 7.1">
		<meta name="vs_targetSchema" content="http://schemas.microsoft.com/intellisense/ie5">
		<LINK rel="stylesheet" type="text/css" href="RaabtaAdmin.css">
		<script id="clientEventHandlersJS" language="javascript">
<!--
function window_onload() {
document.body.scrollTop =document.getElementById("txtX").value;
document.body.scrollLeft=document.getElementById("txtY").value;
return true;
}

function window_onscroll() {
document.getElementById("txtX").value=document.body.scrollTop;
document.getElementById("txtY").value=document.body.scrollLeft;
return true;

}

function CheckAll(field, state){
var chkBoxList = document.getElementById(field);
var chkBoxCount= chkBoxList.getElementsByTagName("input");
for(var i=0;i<chkBoxCount.length;i++){
chkBoxCount[i].checked = state;}
return true; }

function Func()
{
 var title=document.getElementById("txtTitle");
 if(title.value=='')
 {
  alert('Please provide title of queuelist');
  title.focus();
  return false;
 }
 var chkList1= document.getElementById ("chkOwnerList"); 
 var arrayOfCheckBoxes= chkList1.getElementsByTagName("input"); 
 if(arrayOfCheckBoxes.length==0)
 {
  alert('Please select queuelist owner');
  return false;
 }
 var dept=document.getElementById("ddDept");
 if(dept.selectedIndex==0)
 {
  alert('Please select queuelist department');
  dept.focus();
  return false;
 }
 var slot=document.getElementById("ddTimeslot");
 if(slot.selectedIndex==0)
 {
  alert('Please select queuelist timeslot');
  slot.focus();
  return false;
 }
 var type=document.getElementById("chkSpType");
 if(type.selectedIndex==0)
 {
  alert('Please select queuelist specialist type');
  type.focus();
  return false;
 }
}
//-->
		</script>
	</HEAD>
	<body dir="ltr" onscroll="return window_onscroll()" language="javascript" onload="return window_onload()"
		bottomMargin="0" leftMargin="0" rightMargin="0" bgProperties="fixed" topMargin="0">
		<form id="myForm" name="MyForm" runat="server">
			<table border="0" cellSpacing="0" cellPadding="0" width="750" bgColor="#ffffff" align="center"
				height="100%">
				<TR>
					<TD height="69" vAlign="top" align="left">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" width="780" height="69">
							<PARAM NAME="_cx" VALUE="20637">
							<PARAM NAME="_cy" VALUE="1825">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top1.swf">
							<PARAM NAME="Src" VALUE="flash/Top1.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top1.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="69"> </embed>
						</OBJECT>
					</TD>
				</TR>
				<tr>
					<td height="10">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" width="780" height="10">
							<PARAM NAME="_cx" VALUE="20637">
							<PARAM NAME="_cy" VALUE="264">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top2.swf">
							<PARAM NAME="Src" VALUE="flash/Top2.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top2.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="10"> </embed>
						</OBJECT>
					</td>
				</tr>
				<TR>
					<TD class="PageTitle" height="20">Geo Raabta Admin :: Create Specialist</TD>
				</TR>
				<tr>
					<td height="20" background="../images/menu-off-bg.gif"><uc1:mymenus id="MyMenus1" runat="server"></uc1:mymenus></td>
				</tr>
				<tr>
					<td class="MainBG" vAlign="top" align="left"><STRONG><FONT size="4"><BR>
								<TABLE id="tdFrom2" class="MainFormColor" border="1" cellSpacing="0" cellPadding="3" width="90%"
									align="center" runat="server">
									<TR>
										<TD class="OrangeFormTitle" colSpan="3">Queue List Configuration</TD>
									</TR>
									<TR>
										<TD colSpan="3"><asp:panel style="Z-INDEX: 0" id="pnlMain" runat="server">
												<TABLE style="Z-INDEX: 0" id="Table1" border="0" cellSpacing="1" cellPadding="1" width="100%">
													<TR>
														<TD style="Z-INDEX: 0" class="OrangeFormTitle" colSpan="2">Specialist Information</TD>
													</TR>
													<TR>
														<TD style="Z-INDEX: 0" colSpan="2"><STRONG>Title:<BR>
																<asp:textbox style="Z-INDEX: 0" id="txtTitle" runat="server" Width="488px" CssClass="TextBox"
																	MaxLength="500"></asp:textbox></STRONG></TD>
													</TR>
													<TR>
														<TD style="Z-INDEX: 0; WIDTH: 344px"><INPUT id="cbDept2" onclick="return CheckAll('chkDepartment',cbDept2.checked)" type="checkbox"
																name="cbDept2"><STRONG> &nbsp;Department:</STRONG>&nbsp;
															<DIV style="OVERFLOW: auto" id="frm">
																<asp:panel style="Z-INDEX: 0" id="Panel3" runat="server" Width="100%" CssClass="ov" Height="112px">
																	<asp:checkboxlist id="chkDepartment" runat="server" Width="100%" CssClass="TextBox" Height="2px" RepeatLayout="Flow"></asp:checkboxlist>
																</asp:panel></DIV>
														</TD>
														<TD style="Z-INDEX: 0"><INPUT id="cbDesg2" onclick="return CheckAll('chkDesignation',cbDesg2.checked)" type="checkbox"
																name="cbDesg2"><STRONG> &nbsp;Designation:</STRONG>
															<BR>
															<DIV style="OVERFLOW: auto" id="frm">
																<asp:panel style="Z-INDEX: 0" id="Panel4" runat="server" Width="100%" CssClass="ov" Height="112px">
																	<asp:checkboxlist id="chkDesignation" runat="server" Width="100%" CssClass="TextBox" RepeatLayout="Flow"></asp:checkboxlist>
																</asp:panel></DIV>
														</TD>
													</TR>
													<TR>
														<TD style="WIDTH: 344px">
															<asp:button style="Z-INDEX: 0" id="cmdUpdateDesg" runat="server" Width="176px" CssClass="Button"
																Text=">> Designations"></asp:button></TD>
														<TD>
															<asp:button style="Z-INDEX: 0" id="Button2" runat="server" Width="176px" CssClass="Button" Text=">> Functional Designations"></asp:button></TD>
													</TR>
													<TR>
														<TD style="Z-INDEX: 0; WIDTH: 344px"><STRONG><INPUT id="cbFD2" onclick="return CheckAll('chkFunctionalDesignation',cbFD2.checked)" type="checkbox"
																	name="cbFD2"> &nbsp;Functional Designation:</STRONG>&nbsp;
															<DIV style="OVERFLOW: auto" id="frm">
																<asp:panel style="Z-INDEX: 0" id="Panel8" runat="server" Width="100%" CssClass="ov" Height="112px">
																	<asp:checkboxlist id="chkFunctionalDesignation" runat="server" Width="100%" CssClass="TextBox" RepeatLayout="Flow"></asp:checkboxlist>
																</asp:panel></DIV>
														</TD>
														<TD style="Z-INDEX: 0"><INPUT id="cbFD" onclick="return CheckAll('chkOwnerList',cbFD.checked)" type="checkbox"
																name="chFD"><STRONG>&nbsp;Specialist Owner:</STRONG>&nbsp;
															<DIV style="OVERFLOW: auto" id="frm">
																<asp:panel style="Z-INDEX: 0" id="Panel1" runat="server" Width="100%" CssClass="ov" Height="112px">
																	<asp:checkboxlist id="chkOwnerList" runat="server" Width="100%" CssClass="TextBox" RepeatLayout="Flow"></asp:checkboxlist>
																</asp:panel></DIV>
														</TD>
													</TR>
													<TR>
														<TD style="WIDTH: 344px">
															<asp:button style="Z-INDEX: 0" id="Button3" runat="server" Width="176px" CssClass="Button" Text=">> Add as Specialist Owner"></asp:button></TD>
														<TD>
															<asp:button style="Z-INDEX: 0" id="Button4" runat="server" Width="176px" CssClass="Button" Text="<< Remove Specialist Owner"></asp:button></TD>
													</TR>
													<TR>
														<TD style="Z-INDEX: 0; WIDTH: 344px"><STRONG>Specialist Department:</STRONG><BR>
															<asp:dropdownlist id="ddDept" runat="server" Width="100%" CssClass="textbox">
																<asp:ListItem Value="0">Select--Department</asp:ListItem>
															</asp:dropdownlist></TD>
														<TD style="Z-INDEX: 0"><STRONG>Manage Requests Timeslot:<BR>
																<asp:dropdownlist style="Z-INDEX: 0" id="ddTimeslot" runat="server" Width="100%" CssClass="textbox">
																	<asp:ListItem Value="0">Select--Timeslot</asp:ListItem>
																	<asp:ListItem Value="1">8 a.m - 4 p.m</asp:ListItem>
																	<asp:ListItem Value="2">4 p.m - 12 p.m</asp:ListItem>
																	<asp:ListItem Value="3">12 p.m - 8 a.m</asp:ListItem>
																	<asp:ListItem Value="4">Public Holidays</asp:ListItem>
																	<asp:ListItem Value="5">Weekly Holidays</asp:ListItem>
																	<asp:ListItem Value="6">Any Time</asp:ListItem>
																</asp:dropdownlist></STRONG></TD>
													</TR>
													<TR>
														<TD style="Z-INDEX: 0; WIDTH: 344px"><STRONG>Select Specialist Type:<BR>
																<asp:dropdownlist style="Z-INDEX: 0" id="chkSpType" runat="server" Width="100%" CssClass="textbox">
																	<asp:ListItem Value="0">Select--Specialist Type</asp:ListItem>
																</asp:dropdownlist></STRONG></TD>
														<TD style="Z-INDEX: 0"></TD>
													</TR>
													<TR>
														<TD colSpan="2"><FONT size="3">Specialist&nbsp;No: </FONT>
															<asp:label id="lblSpecialistNo" runat="server" Font-Size="X-Small" Font-Bold="True" ForeColor="Navy"
																Font-Names="Verdana"></asp:label></TD>
													</TR>
													<TR>
														<TD colSpan="2">
															<asp:label style="Z-INDEX: 0" id="lblMsg" runat="server" Font-Size="X-Small" Font-Bold="True"
																ForeColor="Navy" Visible="False"></asp:label></TD>
													</TR>
													<TR>
														<TD style="WIDTH: 344px" colSpan="2">
															<asp:button style="Z-INDEX: 0" id="cmdCancel" runat="server" Width="64px" CssClass="fbtab" Text="New"></asp:button>
															<asp:button style="Z-INDEX: 0" id="btnSave" runat="server" Width="64px" CssClass="fbtab" Text="Save"></asp:button>
															<asp:LinkButton id="lnkDynamics" runat="server" Width="192px" Font-Size="X-Small" Font-Bold="True"
																Enabled="False">Add Specialist Dimensions</asp:LinkButton></TD>
													</TR>
												</TABLE>
											</asp:panel></TD>
									</TR>
									<TR>
										<TD colSpan="3">
											<asp:Panel id="pnlSub" runat="server">
												<TABLE id="Table2" border="0" cellSpacing="1" cellPadding="1" width="100%">
													<TR>
														<TD style="WIDTH: 333px" class="OrangeFormTitle" colSpan="2">Specialist Dimensions</TD>
													</TR>
													<TR>
														<TD style="WIDTH: 333px"><STRONG>Requisition Module:</STRONG></TD>
														<TD style="Z-INDEX: 0"><INPUT id="cbDept" onclick="return CheckAll('chkOwnerDept',cbDept.checked)" type="checkbox"
																name="cbDept">&nbsp;<STRONG>Requested&nbsp;Department(s):</STRONG></TD>
													</TR>
													<TR>
														<TD style="WIDTH: 333px">
															<asp:dropdownlist style="Z-INDEX: 0" id="chkModule" runat="server" Width="320px" CssClass="textbox">
																<asp:ListItem Value="0">Select--Module</asp:ListItem>
															</asp:dropdownlist></TD>
														<TD>
															<DIV style="OVERFLOW: auto" id="frm">
																<asp:panel style="Z-INDEX: 0" id="Panel2" runat="server" Width="100%" CssClass="ov" Height="112px">
																	<asp:checkboxlist id="chkOwnerDept" runat="server" Width="100%" CssClass="TextBox" Height="2px" RepeatLayout="Flow"></asp:checkboxlist>
																</asp:panel></DIV>
														</TD>
													</TR>
													<TR>
														<TD style="Z-INDEX: 0; WIDTH: 333px"><INPUT id="cbCat" onclick="return CheckAll('chkOwnerCat',cbCat.checked)" type="checkbox"
																name="cbCat">&nbsp;<STRONG>Requested&nbsp;Category(ies):</STRONG></TD>
														<TD style="Z-INDEX: 0"><INPUT id="cbStation" onclick="return CheckAll('chkOwnerStation',cbStation.checked)" type="checkbox"
																name="cbStation">&nbsp;<STRONG>Requested Stations:</STRONG></TD>
													</TR>
													<TR>
														<TD style="Z-INDEX: 0; WIDTH: 333px">
															<DIV style="OVERFLOW: auto" id="frm">
																<asp:panel style="Z-INDEX: 0" id="Panel5" runat="server" Width="100%" CssClass="ov" Height="112px">
																	<asp:checkboxlist id="chkOwnerCat" runat="server" Width="100%" CssClass="TextBox" Height="2px" RepeatLayout="Flow"></asp:checkboxlist>
																</asp:panel></DIV>
														</TD>
														<TD style="Z-INDEX: 0">
															<DIV style="OVERFLOW: auto" id="frm">
																<asp:panel style="Z-INDEX: 0" id="Panel6" runat="server" Width="100%" CssClass="ov" Height="112px">
																	<asp:checkboxlist id="chkOwnerStation" runat="server" Width="100%" CssClass="TextBox" Height="2px"
																		RepeatLayout="Flow"></asp:checkboxlist>
																</asp:panel></DIV>
														</TD>
													</TR>
													<TR>
														<TD style="Z-INDEX: 0; WIDTH: 333px" colSpan="2">
															<asp:button style="Z-INDEX: 0" id="btnCancelDynamics" runat="server" Width="64px" CssClass="fbtab"
																Text="New"></asp:button>
															<asp:button style="Z-INDEX: 0" id="btnSaveDynamics" runat="server" Width="64px" CssClass="fbtab"
																Text="Save"></asp:button></TD>
													</TR>
													<TR>
														<TD colSpan="2">
															<asp:label style="Z-INDEX: 0" id="Label1" runat="server" Font-Size="X-Small" Font-Bold="True"
																ForeColor="Navy" Visible="False"></asp:label></TD>
													</TR>
												</TABLE>
											</asp:Panel></TD>
									</TR>
									<TR>
										<TD colSpan="3"></TD>
									</TR>
								</TABLE>
							</FONT></STRONG>
						<BR>
					</td>
				</tr>
				<tr>
					<td height="15" vAlign="middle" align="center">Copyright © 2005 Independent Media 
						Corporation <A href="http://www.geo.tv">www.geo.tv</A></td>
				</tr>
			</table>
			<asp:textbox style="VISIBILITY: hidden" id="txtY" runat="server" Width="24px"></asp:textbox><asp:textbox style="VISIBILITY: hidden" id="txtX" runat="server" Width="24px"></asp:textbox></form>
	</body>
</HTML>
