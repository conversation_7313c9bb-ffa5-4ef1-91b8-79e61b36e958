/* Reset */
.highcontrastSkin table, .highcontrastSkin tbody, .highcontrastSkin a, .highcontrastSkin img, .highcontrastSkin tr, .highcontrastSkin div, .highcontrastSkin td, .highcontrastSkin iframe, .highcontrastSkin span, .highcontrastSkin *, .highcontrastSkin .mceText {border:0; margin:0; padding:0; vertical-align:baseline; border-collapse:separate;}
.highcontrastSkin a:hover, .highcontrastSkin a:link, .highcontrastSkin a:visited, .highcontrastSkin a:active {text-decoration:none; font-weight:normal; cursor:default;}
.highcontrastSkin table td {vertical-align:middle}

.highcontrastSkin .mceIconOnly {display: block !important;}

/* External */
.highcontrastSkin .mceExternalToolbar {position:absolute; border:1px solid; border-bottom:0; display:none; background-color: white;}
.highcontrastSkin .mceExternalToolbar td.mceToolbar {padding-right:13px;}
.highcontrastSkin .mceExternalClose {position:absolute; top:3px; right:3px; width:7px; height:7px;}

/* Layout */
.highcontrastSkin table.mceLayout {border: 1px solid;}
.highcontrastSkin .mceIframeContainer {border-top:1px solid; border-bottom:1px solid}
.highcontrastSkin .mceStatusbar a:hover {text-decoration:underline}
.highcontrastSkin .mceStatusbar {display:block; line-height:1.5em; overflow:visible;}
.highcontrastSkin .mceStatusbar div {float:left}
.highcontrastSkin .mceStatusbar a.mceResize {display:block; float:right; width:20px; height:20px; cursor:se-resize; outline:0}

.highcontrastSkin .mceToolbar td { display: inline-block; float: left;}
.highcontrastSkin .mceToolbar tr { display: block;}
.highcontrastSkin .mceToolbar table { display: block; }

/* Button */

.highcontrastSkin .mceButton { display:block; margin: 2px; padding: 5px 10px;border: 1px solid; border-radius: 3px; -moz-border-radius: 3px; -webkit-border-radius: 3px; -ms-border-radius: 3px; height: 2em;}
.highcontrastSkin .mceButton .mceVoiceLabel { height: 100%; vertical-align: center; line-height: 2em}
.highcontrastSkin .mceButtonDisabled .mceVoiceLabel { opacity:0.6; -ms-filter:'alpha(opacity=60)'; filter:alpha(opacity=60);}
.highcontrastSkin .mceButtonActive, .highcontrastSkin .mceButton:focus, .highcontrastSkin .mceButton:active { border: 5px solid; padding: 1px 6px;-webkit-focus-ring-color:none;outline:none;}

/* Separator */
.highcontrastSkin .mceSeparator {display:block; width:16px; height:26px;}

/* ListBox */
.highcontrastSkin .mceListBox { display: block; margin:2px;-webkit-focus-ring-color:none;outline:none;}
.highcontrastSkin .mceListBox .mceText {padding: 5px 6px;  line-height: 2em; width: 15ex; overflow: hidden;}
.highcontrastSkin .mceListBoxDisabled .mceText { opacity:0.6; -ms-filter:'alpha(opacity=60)'; filter:alpha(opacity=60);}
.highcontrastSkin .mceListBox a.mceText { padding: 5px 10px; display: block; height: 2em; line-height: 2em; border: 1px solid; border-right: 0; border-radius: 3px 0px 0px 3px; -moz-border-radius: 3px 0px 0px 3px; -webkit-border-radius: 3px 0px 0px 3px; -ms-border-radius: 3px 0px 0px 3px;}
.highcontrastSkin .mceListBox a.mceOpen { padding: 5px 4px; display: block; height: 2em; line-height: 2em; border: 1px solid; border-left: 0; border-radius: 0px 3px 3px 0px; -moz-border-radius: 0px 3px 3px 0px; -webkit-border-radius: 0px 3px 3px 0px; -ms-border-radius: 0px 3px 3px 0px;}
.highcontrastSkin .mceListBox:focus a.mceText, .highcontrastSkin .mceListBox:active a.mceText { border-width: 5px; padding: 1px 10px 1px 6px;}
.highcontrastSkin .mceListBox:focus a.mceOpen, .highcontrastSkin .mceListBox:active a.mceOpen { border-width: 5px; padding: 1px 0px 1px 4px;}

.highcontrastSkin .mceListBoxMenu {overflow-y:auto}

/* SplitButton */
.highcontrastSkin .mceSplitButtonDisabled .mceAction {opacity:0.3; -ms-filter:'alpha(opacity=30)'; filter:alpha(opacity=30)}

.highcontrastSkin .mceSplitButton { border-collapse: collapse; margin: 2px; height: 2em; line-height: 2em;-webkit-focus-ring-color:none;outline:none;}
.highcontrastSkin .mceSplitButton td { display: table-cell; float: none; margin: 0; padding: 0; height: 2em;}
.highcontrastSkin .mceSplitButton tr { display: table-row; }
.highcontrastSkin table.mceSplitButton  { display: table; }
.highcontrastSkin .mceSplitButton a.mceAction { padding: 5px 10px; display: block; height: 2em; line-height: 2em; overflow: hidden; border: 1px solid; border-right: 0; border-radius: 3px 0px 0px 3px; -moz-border-radius: 3px 0px 0px 3px; -webkit-border-radius: 3px 0px 0px 3px; -ms-border-radius: 3px 0px 0px 3px;}
.highcontrastSkin .mceSplitButton a.mceOpen { padding: 5px 4px;  display: block; height: 2em; line-height: 2em; border: 1px solid; border-radius: 0px 3px 3px 0px; -moz-border-radius: 0px 3px 3px 0px; -webkit-border-radius: 0px 3px 3px 0px; -ms-border-radius: 0px 3px 3px 0px;}
.highcontrastSkin .mceSplitButton .mceVoiceLabel { height: 2em; vertical-align: center; line-height: 2em; } 
.highcontrastSkin .mceSplitButton:focus a.mceAction, .highcontrastSkin .mceSplitButton:active a.mceAction { border-width: 5px; border-right-width: 1px; padding: 1px 10px 1px 6px;-webkit-focus-ring-color:none;outline:none;}
.highcontrastSkin .mceSplitButton:focus a.mceOpen, .highcontrastSkin .mceSplitButton:active a.mceOpen { border-width: 5px; border-left-width: 1px; padding: 1px 0px 1px 4px;-webkit-focus-ring-color:none;outline:none;}

/* Menu */
.highcontrastSkin .mceNoIcons span.mceIcon {width:0;}
.highcontrastSkin .mceMenu {position:absolute; left:0; top:0; z-index:1000; border:1px solid; }
.highcontrastSkin .mceMenu table {background:white; color: black}
.highcontrastSkin .mceNoIcons a .mceText {padding-left:10px}
.highcontrastSkin .mceMenu a, .highcontrastSkin .mceMenu span, .highcontrastSkin .mceMenu {display:block;background:white; color: black}
.highcontrastSkin .mceMenu td {height:2em}
.highcontrastSkin .mceMenu a {position:relative;padding:3px 0 4px 0; display: block;}
.highcontrastSkin .mceMenu .mceText {position:relative; display:block; cursor:default; margin:0; padding:0 25px 0 25px;}
.highcontrastSkin .mceMenu pre.mceText {font-family:Monospace}
.highcontrastSkin .mceMenu .mceIcon {position:absolute; top:0; left:0; width:26px;}
.highcontrastSkin td.mceMenuItemSeparator {border-top:1px solid; height:1px}
.highcontrastSkin .mceMenuItemTitle a {border:0; border-bottom:1px solid}
.highcontrastSkin .mceMenuItemTitle span.mceText {font-weight:bold; padding-left:4px}
.highcontrastSkin .mceNoIcons .mceMenuItemSelected span.mceText:before {content: "\2713\A0";}
.highcontrastSkin .mceMenu span.mceMenuLine {display:none}
.highcontrastSkin .mceMenuItemSub a .mceText:after {content: "\A0\25B8"}

/* ColorSplitButton */
.highcontrastSkin div.mceColorSplitMenu table {background:#FFF; border:1px solid; color: #000}
.highcontrastSkin .mceColorSplitMenu td {padding:2px}
.highcontrastSkin .mceColorSplitMenu a {display:block; width:16px; height:16px; overflow:hidden; color:#000; margin: 0; padding: 0;}
.highcontrastSkin .mceColorSplitMenu td.mceMoreColors {padding:1px 3px 1px 1px}
.highcontrastSkin .mceColorSplitMenu a.mceMoreColors {width:100%; height:auto; text-align:center; font-family:Tahoma,Verdana,Arial,Helvetica; font-size:11px; line-height:20px; border:1px solid #FFF}
.highcontrastSkin .mceColorSplitMenu a.mceMoreColors:hover {border:1px solid; background-color:#B6BDD2}
.highcontrastSkin a.mceMoreColors:hover {border:1px solid #0A246A; color: #000;}
.highcontrastSkin .mceColorPreview {display:none;}
.highcontrastSkin .mce_forecolor span.mceAction, .highcontrastSkin .mce_backcolor span.mceAction {height:17px;overflow:hidden}

/* Progress,Resize */
.highcontrastSkin .mceBlocker {position:absolute; left:0; top:0; z-index:1000; opacity:0.5; -ms-filter:'alpha(opacity=30)'; filter:alpha(opacity=50); background:#FFF}
.highcontrastSkin .mceProgress {position:absolute; left:0; top:0; z-index:1001; background:url(../default/img/progress.gif) no-repeat; width:32px; height:32px; margin:-16px 0 0 -16px}

/* Formats */
.highcontrastSkin .mce_p span.mceText {}
.highcontrastSkin .mce_address span.mceText {font-style:italic}
.highcontrastSkin .mce_pre span.mceText {font-family:monospace}
.highcontrastSkin .mce_h1 span.mceText {font-weight:bolder; font-size: 2em}
.highcontrastSkin .mce_h2 span.mceText {font-weight:bolder; font-size: 1.5em}
.highcontrastSkin .mce_h3 span.mceText {font-weight:bolder; font-size: 1.17em}
.highcontrastSkin .mce_h4 span.mceText {font-weight:bolder; font-size: 1em}
.highcontrastSkin .mce_h5 span.mceText {font-weight:bolder; font-size: .83em}
.highcontrastSkin .mce_h6 span.mceText {font-weight:bolder; font-size: .75em}
