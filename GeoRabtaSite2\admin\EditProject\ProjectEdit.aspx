<%@ Register TagPrefix="uc1" TagName="LoginUser" Src="../LoginUser.ascx" %>
<%@ Register TagPrefix="uc1" TagName="AdminUserControl" Src="../AdminUserControl.ascx" %>
<%@ Register TagPrefix="uc1" TagName="myMenus" Src="myMenus.ascx" %>
<%@ Register TagPrefix="ew" Namespace="eWorld.UI" Assembly="eWorld.UI" %>
<%@ Page language="c#" Codebehind="ProjectEdit.aspx.cs" AutoEventWireup="false" Inherits="GeoRabtaSite.admin.ProjectEdit" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Rabta ::</title>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="RaabtaAdmin.css" type="text/css" rel="stylesheet">
		<LINK href="../Styles4.css" type="text/css" rel="stylesheet">
		<style type="text/css">.hid { DISPLAY: none }
		</style>
		<script language="javascript">
		
			
	function divEdit_onpaste() {
	UpdateText('divEdit','txtDescription');
}

function divEdit_onkeyup() {
	UpdateText('divEdit','txtDescription');
}

function divEdit_onkeypress() {
	UpdateText('divEdit','txtDescription');
}

function UpdateText(html, txt)
{
	var t=document.getElementById(txt);
	var h=document.getElementById(html);
	var s=document.getElementById("txtSadaText");
	if(t!=null && h!=null)
	{
		t.value=h.innerHTML;
		s.value=h.innerText;
	}
}
function UpdateHTML(txt, html)
{
	var t=document.getElementById(txt);
	var h=document.getElementById(html)
	if(t!=null && h!=null)
	{
		h.innerHTML=t.value
	}
}

function EditCommand(html, txt, cmd)
{
	var t=document.getElementById(txt);
	var h=document.getElementById(html)
	var s=document.getElementById("txtSadaText");
	//alert (html+" " +txt+" " + cmd);
	//alert(h);
	if(t!=null && h!=null)
	{
		//alert('ok');
		h.document.execCommand(cmd,false,null);
		t.value=h.innerHTML;
		s.value=h.innerText;
		h.focus()
	}
}

function ChangeFontSize(html, txt, size)
{
	var t=document.getElementById(txt);
	var h=document.getElementById(html)
	var s=document.getElementById("txtSadaText");
	
	if(t!=null && h!=null)
	{
		h.document.execCommand('FontSize', false, size);
		t.value=h.innerHTML;
		s.value=h.innerText;
		html.focus();
	}
}
function ddlFontSize_onchange() {
	var ddl =document.getElementById('ddlFontSize');
	var fs=ddl[ddl.selectedIndex].value;
	//alert(fs);
	ChangeFontSize('divEdit','txtDescription',fs);
}
		</script>
		<script language="javascript" id="clientEventHandlersJS">
<!--

function window_onload() {
UpdateHTML('txtDescription','divEdit');
document.body.scrollTop=document.getElementById("txtX").value;
document.body.scrollLeft=document.getElementById("txtY").value;
}

function window_onscroll() {
document.getElementById("txtX").value=document.body.scrollTop;
document.getElementById("txtY").value=document.body.scrollLeft;
}

//-->
		</script>
	</HEAD>
	<body language="javascript" dir="ltr" onscroll="return window_onscroll()" bottomMargin="0"
		leftMargin="0" topMargin="0" onload="return window_onload()" rightMargin="0">
		<form id="myForm" name="MyForm" runat="server">
			<table height="100%" cellSpacing="0" cellPadding="0" width="750" align="center" bgColor="#ffffff"
				border="0">
				<tr>
					<td vAlign="middle" align="left" height="69">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="69" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="1826">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top1.swf">
							<PARAM NAME="Src" VALUE="flash/Top1.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top1.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="69"> </embed>
						</OBJECT>
					</td>
				</tr>
				<tr>
					<td height="10">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="10" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="265">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top2.swf">
							<PARAM NAME="Src" VALUE="flash/Top2.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top2.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="10"> </embed>
						</OBJECT>
					</td>
				</tr>
				<TR>
					<TD class="PageTitle" height="20">Geo Raabta :: Project Management</TD>
				</TR>
				<tr>
					<td background="../images/menu-off-bg.gif" height="20"><uc1:mymenus id="MyMenus1" runat="server"></uc1:mymenus></td>
				</tr>
				<tr>
					<td class="MainBG" vAlign="top" align="left">
						<P>
							<TABLE class="MainFormColor" id="Table1" cellSpacing="1" cellPadding="1" width="100%" align="center"
								border="0" runat="server">
								<TR>
									<TD class="OrangeFormTitle">Project Management Tool</TD>
								</TR>
								<TR>
									<TD><asp:textbox id="txtX" runat="server" CssClass="hid" Width="32px"></asp:textbox><asp:textbox id="txtY" runat="server" CssClass="hid" Width="32px"></asp:textbox></TD>
								</TR>
								<TR>
									<TD></TD>
								</TR>
								<TR>
									<TD><asp:panel id="pnlStep1" runat="server">
											<TABLE id="Table2" cellSpacing="1" cellPadding="1" width="100%" border="1">
												<TR>
													<TD class="OrangeFormTitle">
														<P><FONT size="2">Step 1: Project Details</FONT></P>
													</TD>
												</TR>
												<TR>
													<TD class="MenuBar"><STRONG>*Title:</STRONG></TD>
												</TR>
												<TR>
													<TD>
														<asp:TextBox id="txtMainTitle" runat="server" CssClass="textbox" Width="696px" MaxLength="100"></asp:TextBox></TD>
												</TR>
												<TR>
													<TD class="MenuBar"><STRONG>Description:
															<asp:TextBox id="txtDescription" runat="server" CssClass="hid"></asp:TextBox></STRONG></TD>
												</TR>
												<TR>
													<TD><IMG language="javascript" class="Hand" id="btnBold" onclick="EditCommand('divEdit', 'txtDescription','Bold');"
															height="24" alt="Bold Selected Text" src="../scripts/bold.gif" width="25"><IMG class="Hand" id="btnItalic" onclick="EditCommand('divEdit', 'txtDescription','Italic');"
															height="24" alt="Italic Selected Text" src="../scripts/italic.gif" width="25"><IMG class="Hand" id="btnUnderline" onclick="EditCommand('divEdit', 'txtDescription','Underline');"
															height="24" alt="Underline Selected Text" src="../scripts/underline.gif" width="25"><IMG language="javascript" class="Hand" id="btnLeft" onclick="EditCommand('divEdit', 'txtDescription','justifyleft');"
															height="24" alt="Justify Left Selected Text" src="../scripts/left_just.gif" width="25"><IMG class="Hand" id="btnCenter" onclick="EditCommand('divEdit', 'txtDescription','justifycenter');"
															height="24" alt="Center Justify Selected Text" src="../scripts/centre.gif" width="25"><IMG language="javascript" class="Hand" id="btnRight" onclick="EditCommand('divEdit', 'txtDescription','justifyright');"
															height="24" alt="Justify Right Selected Text" src="../scripts/right_just.gif" width="25"><IMG language="javascript" class="Hand" id="btnUnorderdList" onclick="EditCommand('divEdit', 'txtDescription','insertunorderedlist');"
															height="24" alt="Bulites Selected Text" src="../scripts/unoderlist.gif" width="25"><IMG language="javascript" class="Hand" id="btnOrderdList" onclick="EditCommand('divEdit', 'txtDescription','insertorderedlist');"
															height="24" alt="Orderd List " src="../scripts/orderdlist.gif" width="25"><FONT size="2"><STRONG>Font 
																Size:</STRONG></FONT>
														<SELECT language="javascript" id="ddlFontSize" style="HEIGHT: 24px" onchange="return ddlFontSize_onchange()"
															name="ddlFontSize">
															<OPTION value="1" selected>1</OPTION>
															<OPTION value="2">2</OPTION>
															<OPTION value="3">3</OPTION>
															<OPTION value="4">4</OPTION>
															<OPTION value="5">5</OPTION>
															<OPTION value="6">6</OPTION>
															<OPTION value="7">7</OPTION>
														</SELECT><BR>
														<DIV language="javascript" oncontextmenu="return false" onkeypress="return divEdit_onkeypress()"
															onpaste="return divEdit_onpaste()" id="divEdit" dir="ltr" onkeyup="return divEdit_onkeyup()"
															contentEditable="true" style="BORDER-RIGHT: black 1px solid; BORDER-TOP: black 1px solid; FONT-SIZE: 12px; VERTICAL-ALIGN: baseline; OVERFLOW: auto; BORDER-LEFT: black 1px solid; WIDTH: 99.48%; DIRECTION: ltr; BORDER-BOTTOM: black 1px solid; FONT-FAMILY: Arial; HEIGHT: 182px; BACKGROUND-COLOR: white; TEXT-ALIGN: left"></DIV>
													</TD>
												</TR>
												<TR>
													<TD class="MenuBar"><STRONG>Scope: </STRONG>
													</TD>
												</TR>
												<TR>
													<TD>
														<asp:TextBox id="txtScope" runat="server" CssClass="textbox" Width="100%" Height="108px" TextMode="MultiLine"></asp:TextBox><BR>
													</TD>
												</TR>
												<TR>
													<TD class="MenuBar"><STRONG>Objectives:</STRONG></TD>
												</TR>
												<TR>
													<TD>
														<asp:TextBox id="txtObjectives" runat="server" CssClass="textbox" Width="100%" Height="108px"
															TextMode="MultiLine"></asp:TextBox><BR>
													</TD>
												</TR>
												<TR>
													<TD class="MenuBar"><STRONG>Deliverables:</STRONG></TD>
												</TR>
												<TR>
													<TD>
														<asp:TextBox id="txtDeliverables" runat="server" CssClass="textbox" Width="100%" Height="108px"
															TextMode="MultiLine"></asp:TextBox><BR>
													</TD>
												</TR>
												<TR>
													<TD class="MenuBar"><STRONG>Project Type:</STRONG></TD>
												</TR>
												<TR>
													<TD>
														<asp:DropDownList id="ddProjectType" runat="server" CssClass="textbox" Width="280px">
															<asp:ListItem Value="0">Select--Project Type</asp:ListItem>
															<asp:ListItem Value="1">KPI &amp; Project</asp:ListItem>
															<asp:ListItem Value="2">Special Projects</asp:ListItem>
														</asp:DropDownList></TD>
												</TR>
												<TR>
													<TD class="MenuBar" style="HEIGHT: 15px"><STRONG>Duration</STRONG><STRONG>:</STRONG></TD>
												</TR>
												<TR>
													<TD>From(date)&nbsp;
														<ew:CalendarPopup id="calFrom" runat="server" AutoPostBack="True">
															<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="White"></WeekdayStyle>
															<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Yellow"></MonthHeaderStyle>
															<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																BackColor="AntiqueWhite"></OffMonthStyle>
															<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="White"></GoToTodayStyle>
															<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="LightGoldenrodYellow"></TodayDayStyle>
															<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Orange"></DayHeaderStyle>
															<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="LightGray"></WeekendStyle>
															<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Yellow"></SelectedDateStyle>
															<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="White"></ClearDateStyle>
															<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Red"></HolidayStyle>
														</ew:CalendarPopup>To(date)
														<ew:CalendarPopup id="CalTo" runat="server" AutoPostBack="True">
															<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="White"></WeekdayStyle>
															<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Yellow"></MonthHeaderStyle>
															<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																BackColor="AntiqueWhite"></OffMonthStyle>
															<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="White"></GoToTodayStyle>
															<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="LightGoldenrodYellow"></TodayDayStyle>
															<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Orange"></DayHeaderStyle>
															<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="LightGray"></WeekendStyle>
															<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Yellow"></SelectedDateStyle>
															<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="White"></ClearDateStyle>
															<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Red"></HolidayStyle>
														</ew:CalendarPopup></TD>
												</TR>
												<TR>
													<TD><STRONG>Total # of Working Day(s):</STRONG>
														<asp:Label id="lblwDays" runat="server"></asp:Label></TD>
												</TR>
												<TR>
													<TD class="MenuBar"><STRONG>Project Cost:</STRONG></TD>
												</TR>
												<TR>
													<TD>
														<asp:TextBox id="txtCost" runat="server" CssClass="textbox" Width="256px"></asp:TextBox></TD>
												</TR>
												<TR>
													<TD class="menuBar"><STRONG>Technology Development:</STRONG></TD>
												</TR>
												<TR>
													<TD>
														<asp:TextBox id="txtTecDevlop" runat="server" CssClass="textbox" Width="560px" Height="66px"
															TextMode="MultiLine"></asp:TextBox></TD>
												</TR>
												<TR>
													<TD class="MenuBar"><STRONG>Quality Circle:</STRONG></TD>
												</TR>
												<TR>
													<TD>
														<asp:TextBox id="txtQC" runat="server" CssClass="textbox" Width="560px" Height="66px" TextMode="MultiLine"></asp:TextBox></TD>
												</TR>
												<TR>
													<TD class="MenuBar"><STRONG>Implementation:</STRONG></TD>
												</TR>
												<TR>
													<TD>
														<asp:TextBox id="txtImp" runat="server" CssClass="textbox" Width="560px" Height="66px" TextMode="MultiLine"></asp:TextBox></TD>
												</TR>
												<TR>
													<TD align="right">
														<asp:ImageButton id="imgNext1" runat="server" Width="64px" ImageUrl="..\images\next.jpg"></asp:ImageButton></TD>
												</TR>
											</TABLE>
										</asp:panel><asp:panel id="pnlStep2" runat="server" Visible="False">
											<TABLE id="Table3" cellSpacing="1" cellPadding="1" width="100%" border="1">
												<TR>
													<TD class="OrangeFormTitle"><FONT size="2">Step 2: Allocation of Human Capital</FONT></TD>
												</TR>
												<TR>
													<TD><STRONG>Team Lead: </STRONG>
														<asp:Label id="lblTeamLead" runat="server"></asp:Label></TD>
												</TR>
												<TR>
													<TD class="MenuBar"><STRONG>Select Team Type:</STRONG></TD>
												</TR>
												<TR>
													<TD>
														<asp:DropDownList id="ddTeamType" runat="server" CssClass="textbox" Width="280px" AutoPostBack="True">
															<asp:ListItem Value="0">Select--Team Type</asp:ListItem>
															<asp:ListItem Value="1" Selected="True">My Team</asp:ListItem>
															<asp:ListItem Value="2">My Line Manager(s)</asp:ListItem>
															<asp:ListItem Value="3">Cross Functional Team</asp:ListItem>
														</asp:DropDownList></TD>
												</TR>
												<TR>
													<TD id="cRow" runat="server">
														<asp:Panel id="Panel1" runat="server">
															<TABLE id="Table5" cellSpacing="1" cellPadding="1" width="100%" border="1">
																<TR>
																	<TD>Employee Code:</TD>
																	<TD>
																		<asp:TextBox id="txtpCode" runat="server" CssClass="textbox" Width="285px"></asp:TextBox></TD>
																</TR>
																<TR>
																	<TD>Department:</TD>
																	<TD>
																		<asp:DropDownList id="ddDepartment" runat="server" CssClass="textbox" Width="403px" AutoPostBack="True">
																			<asp:ListItem Value="0" Selected="True">Select--Department</asp:ListItem>
																		</asp:DropDownList></TD>
																</TR>
																<TR>
																	<TD>Designation:</TD>
																	<TD>
																		<asp:DropDownList id="ddDesignation" runat="server" CssClass="textbox" Width="403px">
																			<asp:ListItem Value="0" Selected="True">Select--Designation</asp:ListItem>
																		</asp:DropDownList></TD>
																</TR>
																<TR>
																	<TD>Station:</TD>
																	<TD>
																		<asp:DropDownList id="ddStation" runat="server" CssClass="textbox" Width="403px">
																			<asp:ListItem Value="0" Selected="True">Select--Station</asp:ListItem>
																		</asp:DropDownList></TD>
																</TR>
																<TR>
																	<TD>Name:</TD>
																	<TD>
																		<asp:TextBox id="txtName" runat="server" CssClass="textbox" Width="280px"></asp:TextBox></TD>
																</TR>
																<TR>
																	<TD colSpan="2">
																		<asp:Button id="btnSearch" runat="server" CssClass="button" Text="Search"></asp:Button></TD>
																</TR>
															</TABLE>
														</asp:Panel></TD>
												</TR>
												<TR>
													<TD class="MenuBar"><STRONG>Team Member(s):</STRONG></TD>
												</TR>
												<TR>
													<TD>
														<asp:DataGrid id="dgTeam" runat="server" Width="100%" Visible="False" AutoGenerateColumns="False"
															BorderColor="#FFC0C0">
															<Columns>
																<asp:TemplateColumn HeaderText="Select">
																	<HeaderStyle Width="1px"></HeaderStyle>
																	<ItemTemplate>
																		<asp:CheckBox id="chkSelect" runat="server"></asp:CheckBox>
																	</ItemTemplate>
																</asp:TemplateColumn>
																<asp:BoundColumn Visible="False" DataField="pcode"></asp:BoundColumn>
																<asp:BoundColumn DataField="pic">
																	<ItemStyle Width="55px"></ItemStyle>
																</asp:BoundColumn>
																<asp:BoundColumn DataField="name" HeaderText="Team Member"></asp:BoundColumn>
																<asp:TemplateColumn HeaderText="Role / Responsibility">
																	<ItemStyle Width="300px"></ItemStyle>
																	<ItemTemplate>
																		<asp:TextBox id="txtRole" runat="server" Width="100%" CssClass="textbox" Height="51px" TextMode="MultiLine"></asp:TextBox>
																	</ItemTemplate>
																</asp:TemplateColumn>
															</Columns>
														</asp:DataGrid>
														<asp:Button id="btnAddToProject" runat="server" CssClass="button" Text="Add To Project"></asp:Button></TD>
												</TR>
												<TR>
													<TD id="r1" runat="server"><STRONG><FONT size="2">Project Team:<BR>
																<asp:DataGrid id="dgTeamFinal" runat="server" Width="100%" Visible="False" AutoGenerateColumns="False"
																	BorderColor="#FFC0C0" DataKeyField="pcode">
																	<Columns>
																		<asp:ButtonColumn Text="Delete" CommandName="Delete"></asp:ButtonColumn>
																		<asp:ButtonColumn Visible="False" Text="Edit" CommandName="Select"></asp:ButtonColumn>
																		<asp:BoundColumn Visible="False" DataField="pcode"></asp:BoundColumn>
																		<asp:BoundColumn DataField="pic" ReadOnly="True">
																			<ItemStyle Width="55px"></ItemStyle>
																		</asp:BoundColumn>
																		<asp:BoundColumn DataField="name" ReadOnly="True" HeaderText="Team Member"></asp:BoundColumn>
																		<asp:BoundColumn DataField="role" ReadOnly="True" HeaderText="Role / Responsibility"></asp:BoundColumn>
																		<asp:BoundColumn Visible="False" DataField="teamid"></asp:BoundColumn>
																		<asp:TemplateColumn>
																			<EditItemTemplate>
																				<asp:TextBox id=TextBox1 runat="server" Width="250px" Height="64px" TextMode="MultiLine" Text='<%# DataBinder.Eval(Container.DataItem,"role") %>'>
																				</asp:TextBox>
																			</EditItemTemplate>
																		</asp:TemplateColumn>
																		<asp:EditCommandColumn ButtonType="LinkButton" UpdateText="Update" CancelText="Cancel" EditText="Edit"></asp:EditCommandColumn>
																	</Columns>
																</asp:DataGrid></FONT></STRONG></TD>
												</TR>
												<TR>
													<TD>
														<asp:ImageButton id="ImageButton1" runat="server" Width="64px" ImageUrl="..\images\backnext.jpg"></asp:ImageButton>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
														<asp:ImageButton id="imgNext2" runat="server" Width="64px" ImageUrl="..\images\next.jpg"></asp:ImageButton></TD>
												</TR>
											</TABLE>
										</asp:panel><asp:panel id="pnlStep3" runat="server" Visible="False">
											<TABLE id="Table4" cellSpacing="1" cellPadding="1" width="100%" border="1">
												<TR>
													<TD class="OrangeFormTitle"><FONT size="2">Team wise Task(s)</FONT></TD>
												</TR>
												<TR>
													<TD>
														<asp:datagrid id="dgTeamTask" runat="server" Width="100%" Visible="False" AutoGenerateColumns="False"
															BorderColor="#FFC0C0">
															<Columns>
																<asp:BoundColumn DataField="title" HeaderText="Task Title"></asp:BoundColumn>
																<asp:BoundColumn DataField="tdes" HeaderText="Description"></asp:BoundColumn>
																<asp:BoundColumn Visible="False" DataField="tid"></asp:BoundColumn>
																<asp:BoundColumn DataField="durationfrom" HeaderText="Duration (from)" DataFormatString="{0:d MMM,yyyy}"></asp:BoundColumn>
																<asp:BoundColumn DataField="durationto" HeaderText="Duration (to)" DataFormatString="{0:d MMM,yyyy}"></asp:BoundColumn>
																<asp:BoundColumn DataField="totalworkingdays" HeaderText="Total Working Day(s)"></asp:BoundColumn>
																<asp:BoundColumn DataField="weightage" HeaderText="Weightage"></asp:BoundColumn>
																<asp:TemplateColumn HeaderText="Responsible"></asp:TemplateColumn>
																<asp:BoundColumn Visible="False"></asp:BoundColumn>
																<asp:ButtonColumn Text="Edit" CommandName="Select"></asp:ButtonColumn>
															</Columns>
														</asp:datagrid></TD>
												</TR>
												<TR>
													<TD class="OrangeFormTitle">Step 3:&nbsp;Create Project&nbsp;Tasks &amp; Assign 
														Team</TD>
												</TR>
												<TR>
													<TD class="MenuBar"><STRONG>Task Title:
															<asp:TextBox id="txtHidden" style="VISIBILITY: hidden" runat="server" CssClass="textbox" Width="24px"></asp:TextBox>
															<asp:TextBox id="txtaHidden" style="VISIBILITY: hidden" runat="server" CssClass="textbox" Width="24px"></asp:TextBox></STRONG></TD>
												</TR>
												<TR>
													<TD>
														<asp:TextBox id="txtTitle" runat="server" CssClass="textbox" Width="656px"></asp:TextBox></TD>
												</TR>
												<TR>
													<TD class="MenuBar"><STRONG>Description:</STRONG></TD>
												</TR>
												<TR>
													<TD><IMG language="javascript" class="Hand" id="Img25" onclick="EditCommand('divEdit', 'txtDescription','Bold');"
															height="24" alt="Bold Selected Text" src="../scripts/bold.gif" width="25"><IMG class="Hand" id="Img26" onclick="EditCommand('divEdit', 'txtDescription','Italic');"
															height="24" alt="Italic Selected Text" src="../scripts/italic.gif" width="25"><IMG class="Hand" id="Img27" onclick="EditCommand('divEdit', 'txtDescription','Underline');"
															height="24" alt="Underline Selected Text" src="../scripts/underline.gif" width="25"><IMG language="javascript" class="Hand" id="Img28" onclick="EditCommand('divEdit', 'txtDescription','justifyleft');"
															height="24" alt="Justify Left Selected Text" src="../scripts/left_just.gif" width="25"><IMG class="Hand" id="Img29" onclick="EditCommand('divEdit', 'txtDescription','justifycenter');"
															height="24" alt="Center Justify Selected Text" src="../scripts/centre.gif" width="25"><IMG language="javascript" class="Hand" id="Img30" onclick="EditCommand('divEdit', 'txtDescription','justifyright');"
															height="24" alt="Justify Right Selected Text" src="../scripts/right_just.gif" width="25"><IMG language="javascript" class="Hand" id="Img31" onclick="EditCommand('divEdit', 'txtDescription','insertunorderedlist');"
															height="24" alt="Bulites Selected Text" src="../scripts/unoderlist.gif" width="25"><IMG language="javascript" class="Hand" id="Img32" onclick="EditCommand('divEdit', 'txtDescription','insertorderedlist');"
															height="24" alt="Orderd List " src="../scripts/orderdlist.gif" width="25"><FONT size="2"><STRONG>Font 
																Size:</STRONG></FONT>
														<SELECT language="javascript" id="Select4" style="HEIGHT: 24px" onchange="return ddlFontSize_onchange()"
															name="ddlFontSize">
															<OPTION value="1" selected>1</OPTION>
															<OPTION value="2">2</OPTION>
															<OPTION value="3">3</OPTION>
															<OPTION value="4">4</OPTION>
															<OPTION value="5">5</OPTION>
															<OPTION value="6">6</OPTION>
															<OPTION value="7">7</OPTION>
														</SELECT><BR>
														<DIV language="javascript" oncontextmenu="return false" onkeypress="return divEdit_onkeypress()"
															onpaste="return divEdit_onpaste()" id="Div4" dir="ltr" onkeyup="return divEdit_onkeyup()"
															contentEditable="true" style="BORDER-RIGHT: black 1px solid; BORDER-TOP: black 1px solid; FONT-SIZE: 12px; VERTICAL-ALIGN: baseline; OVERFLOW: auto; BORDER-LEFT: black 1px solid; WIDTH: 99.48%; DIRECTION: ltr; BORDER-BOTTOM: black 1px solid; FONT-FAMILY: Arial; HEIGHT: 182px; BACKGROUND-COLOR: white; TEXT-ALIGN: left"></DIV>
													</TD>
												</TR>
												<TR>
													<TD class="MenuBar">
														<P><STRONG>Duration:</STRONG></P>
													</TD>
												</TR>
												<TR>
													<TD>From(date)&nbsp;
														<ew:CalendarPopup id="CaltFrom" runat="server" AutoPostBack="True">
															<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="White"></WeekdayStyle>
															<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Yellow"></MonthHeaderStyle>
															<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																BackColor="AntiqueWhite"></OffMonthStyle>
															<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="White"></GoToTodayStyle>
															<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="LightGoldenrodYellow"></TodayDayStyle>
															<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Orange"></DayHeaderStyle>
															<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="LightGray"></WeekendStyle>
															<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Yellow"></SelectedDateStyle>
															<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="White"></ClearDateStyle>
															<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Red"></HolidayStyle>
														</ew:CalendarPopup>To(date)
														<ew:CalendarPopup id="CalTto" runat="server" AutoPostBack="True">
															<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="White"></WeekdayStyle>
															<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Yellow"></MonthHeaderStyle>
															<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																BackColor="AntiqueWhite"></OffMonthStyle>
															<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="White"></GoToTodayStyle>
															<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="LightGoldenrodYellow"></TodayDayStyle>
															<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Orange"></DayHeaderStyle>
															<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="LightGray"></WeekendStyle>
															<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Yellow"></SelectedDateStyle>
															<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="White"></ClearDateStyle>
															<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Red"></HolidayStyle>
														</ew:CalendarPopup></TD>
												</TR>
												<TR>
													<TD class="MenuBar"><STRONG>Total # of Working Days:</STRONG>
														<asp:Label id="lblwtDays" runat="server"></asp:Label></TD>
												</TR>
												<TR>
													<TD class="MenuBar"><STRONG>Responsible TeamMember:</STRONG></TD>
												</TR>
												<TR>
													<TD>
														<ew:CollapsablePanel id="CollapsablePanel1" runat="server" AllowSliding="True" ExpandText="Show Team"
															CollapseText="Hide Team">
															<asp:CheckBoxList id="chkBoxTeam" runat="server"></asp:CheckBoxList>
														</ew:CollapsablePanel></TD>
												</TR>
												<TR>
													<TD class="MenuBar"><STRONG>Weightage of Task to Overall Project (out of 100%):</STRONG></TD>
												</TR>
												<TR>
													<TD>
														<asp:TextBox id="txtWeightage" runat="server" CssClass="textbox" Width="184px"></asp:TextBox></TD>
												</TR>
												<TR>
													<TD>
														<asp:Button id="btnAddToTask" runat="server" CssClass="button" Text="Add to Team Issue &amp; Task Sheet"></asp:Button></TD>
												</TR>
												<TR>
													<TD id="r2" runat="server">
														<asp:DataGrid id="dgTask" runat="server" Width="100%" Visible="False" AutoGenerateColumns="False"
															BorderColor="#FFC0C0">
															<Columns>
																<asp:ButtonColumn Text="Edit" CommandName="Select"></asp:ButtonColumn>
																<asp:ButtonColumn Text="Delete" CommandName="Delete"></asp:ButtonColumn>
																<asp:BoundColumn DataField="title" HeaderText="Title"></asp:BoundColumn>
																<asp:BoundColumn DataField="des" HeaderText="Description"></asp:BoundColumn>
																<asp:BoundColumn DataField="duration" HeaderText="Duration"></asp:BoundColumn>
																<asp:BoundColumn DataField="days" HeaderText="Total Working Days"></asp:BoundColumn>
																<asp:BoundColumn DataField="weight" HeaderText="Weightage (%)"></asp:BoundColumn>
																<asp:BoundColumn DataField="team" HeaderText="Responsible"></asp:BoundColumn>
																<asp:BoundColumn Visible="False" DataField="Pcode"></asp:BoundColumn>
															</Columns>
														</asp:DataGrid></TD>
												</TR>
												<TR>
													<TD></TD>
												</TR>
												<TR>
													<TD style="HEIGHT: 16px"><STRONG>Is Project Show to Line Manager(s) ?:</STRONG></TD>
												</TR>
												<TR>
													<TD style="HEIGHT: 29px">
														<asp:DropDownList id="ddYesNo" runat="server" CssClass="textbox" Width="280px" AutoPostBack="True">
															<asp:ListItem Value="1">Yes</asp:ListItem>
															<asp:ListItem Value="2" Selected="True">No</asp:ListItem>
														</asp:DropDownList></TD>
												</TR>
												<TR>
													<TD id="TD1" runat="server"><STRONG>Line Manager(s):<BR>
															<asp:CheckBoxList id="chkLine" runat="server"></asp:CheckBoxList></STRONG></TD>
												</TR>
												<TR>
													<TD>
														<asp:Button id="btnCreate" runat="server" CssClass="button" Text="Update Project"></asp:Button></TD>
												</TR>
												<TR>
													<TD>
														<asp:ImageButton id="imgBack" runat="server" Width="64px" ImageUrl="..\images\backnext.jpg"></asp:ImageButton></TD>
												</TR>
											</TABLE>
										</asp:panel></TD>
								</TR>
							</TABLE>
						</P>
					</td>
				</tr>
				<tr>
					<td vAlign="middle" align="center" height="20">Copyright © 2009 Independent Media 
						Corporation <A href="http://www.geo.tv">www.geo.tv</A></td>
				</tr>
			</table>
		</form>
	</body>
</HTML>
