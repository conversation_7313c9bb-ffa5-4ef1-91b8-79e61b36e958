using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for GeoIdeas.
	/// </summary>
	public class ManageEmployeeRequest : System.Web.UI.Page
	{
		SqlConnection con;
		protected System.Web.UI.HtmlControls.HtmlTable Table2;
		protected System.Web.UI.HtmlControls.HtmlTable tdFrom2;	// 
		string userId="";
		private bool _refreshState;
		protected System.Web.UI.WebControls.DropDownList ddFeilds;
		protected System.Web.UI.WebControls.DropDownList ddIsEditable;
		protected System.Web.UI.WebControls.DropDownList ddIsVerificationRequired;
		protected System.Web.UI.WebControls.CheckBoxList chkvDcouments;
		protected System.Web.UI.WebControls.CheckBoxList chkeDocuments;
		protected System.Web.UI.WebControls.DropDownList ddTimeline;
		protected System.Web.UI.WebControls.TextBox txtReplyatRequest;
		protected System.Web.UI.WebControls.TextBox txtReplyAccept;
		protected System.Web.UI.WebControls.TextBox txteplyReject;
		protected System.Web.UI.WebControls.CheckBoxList chkSpecialist;
		protected System.Web.UI.WebControls.Button btnReset;
		protected System.Web.UI.WebControls.Button btnSave;
		protected System.Web.UI.WebControls.TextBox txtVerifyCriteria;
		protected System.Web.UI.WebControls.Label lblmessage;
		protected System.Web.UI.WebControls.TextBox txtTooltipMsg;
		private bool _isRefresh;

		public bool IsRefresh
		{
			get
			{
				return _isRefresh;
			}
		}

		protected override void LoadViewState(object savedState)
		{
			object[] allStates = (object[]) savedState;
			base.LoadViewState(allStates[0]);
			_refreshState = (bool) allStates[1];
			try
			{
				_isRefresh = _refreshState == (bool) Session["__ISREFRESH"];
			}
			catch(Exception)
			{
				Response.Redirect("../Login.aspx");
			}

		}

		protected override object SaveViewState()
		{
			Session["__ISREFRESH"] = _refreshState;
			object[] allStates = new object[2];
			allStates[0] = base.SaveViewState();
			allStates[1] = !_refreshState;
			return allStates;
		}



		/*private bool IsPageAccessAllowed()
		{


			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception ex)
			{
				string s=ex.Message;
				Response.Redirect("../Login.aspx");
			}
		
			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"View")==true)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("../Login.aspx");
				return false;
			}
		}*/

		public void GetDocumentslist()
		{
		 con=new SqlConnection(Connection.ConnectionString);
		 con.Open();
		 SqlCommand cmd=new SqlCommand("select d_id,documentname from t_documentsrequired",con);
		 SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
			 ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
			 this.chkeDocuments.Items.Add(itm);
			 this.chkvDcouments.Items.Add(itm);
			}
		 rd.Close();
		 con.Close();
		 con.Dispose();
		}

		public void GetSpecialist()
		{
		 
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select distinct(s.specialistno) from t_functionaldesignation f,t_designationhistory h,t_employee e,t_specialistowner sp,t_specialist s where f.fdesigid=h.fdesigid and h.isactive=1 and e.desigid=f.functionaldesignation and e.del=1 and sp.s_fdesigid=f.fdesigid and s.specialistno=sp.specialistno",con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem(rd[0].ToString(),rd[0].ToString());
				this.chkSpecialist.Items.Add(itm);
			}
			rd.Close();
			con.Close();
			con.Dispose();
		}																																																																		 
		public void ResetChanges()
		{
		 this.ddFeilds.SelectedIndex=0;
		 this.txtVerifyCriteria.Text="";
		 this.txteplyReject.Text="";
		 this.txtReplyAccept.Text="";
		 this.txtReplyatRequest.Text="";
		 this.txtTooltipMsg.Text="";
		 this.ddIsEditable.SelectedIndex=0;
		 this.ddIsVerificationRequired.SelectedIndex=0;
		 this.ddTimeline.SelectedIndex=0;
			for(int i=0;i<this.chkvDcouments.Items.Count;i++)
			{
			 this.chkvDcouments.Items[i].Selected=false;
			 this.chkeDocuments.Items[i].Selected=false;
			}
			for(int i=0;i<this.chkSpecialist.Items.Count;i++)
			{
				this.chkSpecialist.Items[i].Selected=false;
			}
			this.btnSave.Enabled=true;
			this.lblmessage.Visible=false;
			this.lblmessage.Text="";
			this.btnSave.Text="Save";
		}
		public void ResetChanges(bool Val)
		{
			this.txtVerifyCriteria.Text="";
			this.txteplyReject.Text="";
			this.txtReplyAccept.Text="";
			this.txtReplyatRequest.Text="";
			this.txtTooltipMsg.Text="";
			this.ddIsEditable.SelectedIndex=0;
			this.ddIsVerificationRequired.SelectedIndex=0;
			this.ddTimeline.SelectedIndex=0;
			for(int i=0;i<this.chkvDcouments.Items.Count;i++)
			{
				this.chkvDcouments.Items[i].Selected=false;
				this.chkeDocuments.Items[i].Selected=false;
			}
			for(int i=0;i<this.chkSpecialist.Items.Count;i++)
			{
				this.chkSpecialist.Items[i].Selected=false;
			}
			this.btnSave.Enabled=true;
			this.lblmessage.Visible=false;
			this.lblmessage.Text="";
		}

		public bool checkInfo(string fieldname)
		{
		 con=new SqlConnection(Connection.ConnectionString);
		 con.Open();
		 SqlCommand cmd=new SqlCommand("select * from t_fieldsmgt where isactive=1 and fieldname=@fieldname",con);
		 SqlParameter _fieldname=new SqlParameter("@fieldname",SqlDbType.VarChar);
		 _fieldname.Value=fieldname;
		 cmd.Parameters.Add(_fieldname);
		 SqlDataReader rd=cmd.ExecuteReader();
		 rd.Read();
			if(rd.HasRows)
			{
				rd.Close();
				con.Close();
				con.Dispose();
				return true;
			}
			else
			{
				rd.Close();
				con.Close();
				con.Dispose();
				return false;
			}
		}

		public void GetInfo(string fieldname)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select * from t_fieldsmgt where isactive=1 and fieldname=@fieldname",con);
			SqlParameter _fieldname=new SqlParameter("@fieldname",SqlDbType.VarChar);
			_fieldname.Value=fieldname;
			cmd.Parameters.Add(_fieldname);
			SqlDataReader rd=cmd.ExecuteReader();
			string f_id="";
			while(rd.Read())
			{
			 f_id=rd["f_id"].ToString();
			 this.ddIsEditable.SelectedValue=rd["editable"].ToString();
			 this.ddIsVerificationRequired.SelectedValue=rd["vrequired"].ToString();
			 this.txtVerifyCriteria.Text=rd["vcriteria"].ToString();
			 this.ddTimeline.SelectedValue=rd["timeline"].ToString();
			 this.txtReplyatRequest.Text=rd["replyatrequest"].ToString();
			 this.txtReplyAccept.Text=rd["replyonaccept"].ToString();
			 this.txteplyReject.Text=rd["replyonreject"].ToString();
			 this.txtTooltipMsg.Text=rd["tooltip"].ToString();
			}
			rd.Close();
			cmd=new SqlCommand("select * from t_fieldmgtspecialist where f_id="+f_id+"",con);
			rd=cmd.ExecuteReader();
			int i=0;
			while(rd.Read())
			{
				string ID=rd[1].ToString();
                ListItem item=new ListItem(rd[1].ToString(),rd[1].ToString());
				if(this.chkSpecialist.Items.Contains(item))
				{
                  i=this.chkSpecialist.Items.IndexOf(item);
				  this.chkSpecialist.Items[i].Selected=true;
				}
			
			}
			rd.Close();
			cmd=new SqlCommand("select * from t_fieldmgtdocHR where f_id="+f_id+"",con);
			rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				string val=rd[1].ToString();
				for(int j=0;j<this.chkvDcouments.Items.Count;j++)
				{
					if(this.chkvDcouments.Items[j].Value.Equals(val))
					{
					 this.chkvDcouments.Items[j].Selected=true;
					}
				}
			}
			rd.Close();

			cmd=new SqlCommand("select * from t_fieldmgtdocemp where f_id="+f_id+"",con);
			rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				string val=rd[1].ToString();
				for(int j=0;j<this.chkeDocuments.Items.Count;j++)
				{
					if(this.chkeDocuments.Items[j].Value.Equals(val))
					{
						this.chkeDocuments.Items[j].Selected=true;
					}
				}
			}
			rd.Close();
		}
		
		private void Page_Load(object sender, System.EventArgs e)
		{

			/*Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			if(IsPageAccessAllowed())
			{
			}*/
			if(!IsPostBack)
			{
				GetDocumentslist();
				GetSpecialist();
			}
			
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.ddFeilds.SelectedIndexChanged += new System.EventHandler(this.ddFeilds_SelectedIndexChanged);
			this.btnReset.Click += new System.EventHandler(this.btnReset_Click);
			this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		private void btnReset_Click(object sender, System.EventArgs e)
		{
			ResetChanges();
		}

		private void btnSave_Click(object sender, System.EventArgs e)
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlTransaction trans=con.BeginTransaction();
			if(btnSave.Text=="Save")
			{
				try
				{
					SqlCommand cmd=new SqlCommand("insert into t_fieldsmgt(fieldname,editable,vrequired,vcriteria,timeline,replyatrequest,replyonaccept,replyonreject,tooltip,isactive) values(@fieldname,@editable,@vrequired,@vcriteria,@timeline,@replyatrequest,@replyonaccept,@replyonreject,@tooltip,@isactive)",con);
					SqlParameter _fieldname=new SqlParameter("@fieldname",SqlDbType.VarChar);
					SqlParameter _editable=new SqlParameter("@editable",SqlDbType.Int);
					SqlParameter _vrequired=new SqlParameter("@vrequired",SqlDbType.Int);
					SqlParameter _vcriteria=new SqlParameter("@vcriteria",SqlDbType.VarChar);
					SqlParameter _timeline=new SqlParameter("@timeline",SqlDbType.Int);
					SqlParameter _replyatrequest=new SqlParameter("@replyatrequest",SqlDbType.VarChar);
					SqlParameter _replyonaccept=new SqlParameter("@replyonaccept",SqlDbType.VarChar);
					SqlParameter _replyonreject=new SqlParameter("@replyonreject",SqlDbType.VarChar);
					SqlParameter _tooltip=new SqlParameter("@tooltip",SqlDbType.VarChar);
					SqlParameter _isactive=new SqlParameter("@isactive",SqlDbType.Int);
					_fieldname.Value=this.ddFeilds.SelectedItem.Text;
					_editable.Value=this.ddIsEditable.SelectedItem.Value;
					_vrequired.Value=this.ddIsVerificationRequired.SelectedItem.Value;
					_vcriteria.Value=this.txtVerifyCriteria.Text;
					_timeline.Value=this.ddTimeline.SelectedItem.Value;
					_replyatrequest.Value=this.txtReplyatRequest.Text;
					_replyonaccept.Value=this.txtReplyAccept.Text;
					_replyonreject.Value=this.txteplyReject.Text;
					_tooltip.Value=this.txtTooltipMsg.Text;
					_isactive.Value=1;
					cmd.Parameters.Add(_fieldname);
					cmd.Parameters.Add(_editable);
					cmd.Parameters.Add(_vrequired);
					cmd.Parameters.Add(_vcriteria);
					cmd.Parameters.Add(_timeline);
					cmd.Parameters.Add(_replyatrequest);
					cmd.Parameters.Add(_replyonaccept);
					cmd.Parameters.Add(_replyonreject);
					cmd.Parameters.Add(_tooltip);
					cmd.Parameters.Add(_isactive);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
					cmd=new SqlCommand("select max(f_id) from t_fieldsmgt",con);
					cmd.Transaction=trans;
					SqlDataReader rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						int ID=Int32.Parse(rd[0].ToString());
						rd.Close();
						//===================Insert Specialist Detail=====================//
						for(int i=0;i<this.chkSpecialist.Items.Count;i++)
						{
							if(this.chkSpecialist.Items[i].Selected)
							{
								cmd=new SqlCommand("insert into t_fieldmgtspecialist (f_id,sp_id) values(@f_id,@sp_id)",con);
								SqlParameter _fid=new SqlParameter("@f_id",SqlDbType.Int);
								SqlParameter _spid=new SqlParameter("@sp_id",SqlDbType.VarChar);
								_fid.Value=ID;
								_spid.Value=this.chkSpecialist.Items[i].Value;
								cmd.Parameters.Add(_fid);
								cmd.Parameters.Add(_spid);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
							}
						}
						//================================================================//
						//===================Insert Document HR Detail=====================//
						for(int i=0;i<this.chkvDcouments.Items.Count;i++)
						{
							if(this.chkvDcouments.Items[i].Selected)
							{
								cmd=new SqlCommand("insert into t_fieldmgtdocHR (f_id,documentbyhr) values(@f_id,@documentbyhr)",con);
								SqlParameter _fid=new SqlParameter("@f_id",SqlDbType.Int);
								SqlParameter _documentbyhr=new SqlParameter("@documentbyhr",SqlDbType.VarChar);
								_fid.Value=ID;
								_documentbyhr.Value=this.chkvDcouments.Items[i].Value;
								cmd.Parameters.Add(_fid);
								cmd.Parameters.Add(_documentbyhr);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
							}
						}
						//=================================================================//
						//==========Insert Document Required from TeamGeo Member Detail========//
						for(int i=0;i<this.chkeDocuments.Items.Count;i++)
						{
							if(this.chkeDocuments.Items[i].Selected)
							{
								cmd=new SqlCommand("insert into t_fieldmgtdocemp (f_id,documentbyemployee) values(@f_id,@documentbyemployee)",con);
								SqlParameter _fid=new SqlParameter("@f_id",SqlDbType.Int);
								SqlParameter _documentbyemployee=new SqlParameter("@documentbyemployee",SqlDbType.VarChar);
								_fid.Value=ID;
								_documentbyemployee.Value=this.chkeDocuments.Items[i].Value;
								cmd.Parameters.Add(_fid);
								cmd.Parameters.Add(_documentbyemployee);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
							}
						}
						//=================================================================//
					}
					trans.Commit();
					con.Close();
					con.Dispose();
					this.lblmessage.Visible=true;
					this.lblmessage.Text="Field Information has been saved successfully";
					this.btnSave.Enabled=false;
				}
				catch(Exception ex)
				{
					trans.Rollback();
					Response.Write(ex.Message);
					this.lblmessage.Visible=true;
					this.lblmessage.Text=ex.Message;
				}
				finally
				{
					con.Close();
					con.Dispose();
				}
			}
			else if(btnSave.Text=="Update")
			{
				try
				{
					SqlCommand cmd=new SqlCommand("update t_fieldsmgt set fieldname=@fieldname,editable=@editable,vrequired=@vrequired,vcriteria=@vcriteria,timeline=@timeline,replyatrequest=@replyatrequest,replyonaccept=@replyonaccept,replyonreject=@replyonreject,tooltip=@tooltip where fieldname=@fieldname",con);
					SqlParameter _fieldname=new SqlParameter("@fieldname",SqlDbType.VarChar);
					SqlParameter _editable=new SqlParameter("@editable",SqlDbType.Int);
					SqlParameter _vrequired=new SqlParameter("@vrequired",SqlDbType.Int);
					SqlParameter _vcriteria=new SqlParameter("@vcriteria",SqlDbType.VarChar);
					SqlParameter _timeline=new SqlParameter("@timeline",SqlDbType.Int);
					SqlParameter _replyatrequest=new SqlParameter("@replyatrequest",SqlDbType.VarChar);
					SqlParameter _replyonaccept=new SqlParameter("@replyonaccept",SqlDbType.VarChar);
					SqlParameter _replyonreject=new SqlParameter("@replyonreject",SqlDbType.VarChar);
					SqlParameter _tooltip=new SqlParameter("@tooltip",SqlDbType.VarChar);
					SqlParameter _isactive=new SqlParameter("@isactive",SqlDbType.Int);
					_fieldname.Value=this.ddFeilds.SelectedItem.Text;
					_editable.Value=this.ddIsEditable.SelectedItem.Value;
					_vrequired.Value=this.ddIsVerificationRequired.SelectedItem.Value;
					_vcriteria.Value=this.txtVerifyCriteria.Text;
					_timeline.Value=this.ddTimeline.SelectedItem.Value;
					_replyatrequest.Value=this.txtReplyatRequest.Text;
					_replyonaccept.Value=this.txtReplyAccept.Text;
					_replyonreject.Value=this.txteplyReject.Text;
					_tooltip.Value=this.txtTooltipMsg.Text;
					cmd.Parameters.Add(_fieldname);
					cmd.Parameters.Add(_editable);
					cmd.Parameters.Add(_vrequired);
					cmd.Parameters.Add(_vcriteria);
					cmd.Parameters.Add(_timeline);
					cmd.Parameters.Add(_replyatrequest);
					cmd.Parameters.Add(_replyonaccept);
					cmd.Parameters.Add(_replyonreject);
					cmd.Parameters.Add(_tooltip);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();

					cmd=new SqlCommand("select f_id from t_fieldsmgt where fieldname=@fieldname",con);
					SqlParameter _fname=new SqlParameter("@fieldname",SqlDbType.VarChar);
					_fname.Value=this.ddFeilds.SelectedItem.Text;
					cmd.Parameters.Add(_fname);
					cmd.Transaction=trans;
					SqlDataReader rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						int ID=Int32.Parse(rd[0].ToString());
						rd.Close();
						//===================Insert Specialist Detail=====================//
						cmd=new SqlCommand("delete from t_fieldmgtspecialist where f_id="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						for(int i=0;i<this.chkSpecialist.Items.Count;i++)
						{
							if(this.chkSpecialist.Items[i].Selected)
							{
								cmd=new SqlCommand("insert into t_fieldmgtspecialist (f_id,sp_id) values(@f_id,@sp_id)",con);
								SqlParameter _fid=new SqlParameter("@f_id",SqlDbType.Int);
								SqlParameter _spid=new SqlParameter("@sp_id",SqlDbType.VarChar);
								_fid.Value=ID;
								_spid.Value=this.chkSpecialist.Items[i].Value;
								cmd.Parameters.Add(_fid);
								cmd.Parameters.Add(_spid);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
							}
						}
						//================================================================//
						
						//===================Insert Document HR Detail=====================//
						cmd=new SqlCommand("delete from t_fieldmgtdocHR where f_id="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						for(int i=0;i<this.chkvDcouments.Items.Count;i++)
						{
							if(this.chkvDcouments.Items[i].Selected)
							{
								cmd=new SqlCommand("insert into t_fieldmgtdocHR (f_id,documentbyhr) values(@f_id,@documentbyhr)",con);
								SqlParameter _fid=new SqlParameter("@f_id",SqlDbType.Int);
								SqlParameter _documentbyhr=new SqlParameter("@documentbyhr",SqlDbType.VarChar);
								_fid.Value=ID;
								_documentbyhr.Value=this.chkvDcouments.Items[i].Value;
								cmd.Parameters.Add(_fid);
								cmd.Parameters.Add(_documentbyhr);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
							}
						}
						//=================================================================//
						//==========Insert Document Required from TeamGeo Member Detail========//
						cmd=new SqlCommand("delete from t_fieldmgtdocemp where f_id="+ID+"",con);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
						for(int i=0;i<this.chkeDocuments.Items.Count;i++)
						{
							if(this.chkeDocuments.Items[i].Selected)
							{
								cmd=new SqlCommand("insert into t_fieldmgtdocemp (f_id,documentbyemployee) values(@f_id,@documentbyemployee)",con);
								SqlParameter _fid=new SqlParameter("@f_id",SqlDbType.Int);
								SqlParameter _documentbyemployee=new SqlParameter("@documentbyemployee",SqlDbType.VarChar);
								_fid.Value=ID;
								_documentbyemployee.Value=this.chkeDocuments.Items[i].Value;
								cmd.Parameters.Add(_fid);
								cmd.Parameters.Add(_documentbyemployee);
								cmd.Transaction=trans;
								cmd.ExecuteNonQuery();
							}
						}
						//=================================================================//
					}
					trans.Commit();
					con.Close();
					con.Dispose();
					this.lblmessage.Visible=true;
					this.lblmessage.Text="Field Information has been modified successfully";
					this.btnSave.Enabled=false;
					ResetChanges();
				}
				catch(Exception ex)
				{
					trans.Rollback();
					Response.Write(ex.Message);
					this.lblmessage.Visible=true;
					this.lblmessage.Text=ex.Message;
				}
				finally
				{
					con.Close();
					con.Dispose();
				}
				
			}
			else
			{}
		}

		private void ddFeilds_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			string s=this.ddFeilds.SelectedItem.Text;
			if(checkInfo(this.ddFeilds.SelectedItem.Text))
			{
			 ResetChanges(true);
			 this.btnSave.Text="Update"; 
			 GetInfo(this.ddFeilds.SelectedItem.Text);
			}
			else
			{
			 ResetChanges(true);
			 this.btnSave.Text="Save";
			 //this.ddFeilds.SelectedItem.Text=s;
			}
		}
		
	

	}
}
