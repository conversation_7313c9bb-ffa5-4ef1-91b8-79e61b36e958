using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Configuration;
using System.Data.SqlClient;
namespace GeoRabtaSite.admin
{
	/// <summary>
	/// Summary description for _default.
	/// </summary>
	public class Dictionary : System.Web.UI.Page
	{
		protected System.Web.UI.WebControls.Button btnAdd;
		protected System.Web.UI.WebControls.Button btnDelete;
		protected System.Web.UI.WebControls.Button btnCancel;
		protected System.Web.UI.WebControls.DataGrid dgItems;
		protected System.Web.UI.WebControls.Label Label1;
		SqlConnection con;
		DataSet ds;
		string userId="";
		protected System.Web.UI.WebControls.Label lblmsg;
		protected System.Web.UI.WebControls.Label lnkdgmsg;
		protected System.Web.UI.WebControls.Label lnkduplicate;
		public static string sort;
		
		private bool _refreshState;
		protected System.Web.UI.WebControls.LinkButton hNew;
		protected System.Web.UI.WebControls.TextBox txtName;
		protected System.Web.UI.WebControls.DropDownList ddnCat;
		protected System.Web.UI.WebControls.CheckBox chkVerify;
		protected System.Web.UI.HtmlControls.HtmlTableCell Criteria;
		protected System.Web.UI.WebControls.DropDownList ddcCat;
		protected System.Web.UI.HtmlControls.HtmlTableCell TD1;
		private bool _isRefresh;

		public bool IsRefresh
		{
			get
			{
				return _isRefresh;
			}
		}

		protected override void LoadViewState(object savedState)
		{
			object[] allStates = (object[]) savedState;
			base.LoadViewState(allStates[0]);
			_refreshState = (bool) allStates[1];
			try
			{
				_isRefresh = _refreshState == (bool) Session["__ISREFRESH"];
			}
			catch(Exception)
			{
				Response.Redirect("../Login.aspx");
			}

		}

		protected override object SaveViewState()
		{
			Session["__ISREFRESH"] = _refreshState;
			object[] allStates = new object[2];
			allStates[0] = base.SaveViewState();
			allStates[1] = !_refreshState;
			return allStates;
		}

		public string RaabtaURL()
		{
			string strRaabtaURL=ConfigurationSettings.AppSettings["RaabtaURL"].ToString();
			return strRaabtaURL;
		}

		private void Page_Load(object sender, System.EventArgs e)
		{
			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			try
			{
				string userId=Session["user_id"].ToString();
			}
			catch
			{
				Response.Redirect("../login.aspx");
			}
			if(IsPageAccessAllowed())
			{
				if(GeoSecurity.isControlVisible(2,47,userId,"Edit")==true)
				{
					this.dgItems.Columns[0].Visible=true;
				}
				else
				{
					this.dgItems.Columns[0].Visible=false;
				}
				if(GeoSecurity.isControlVisible(2,47,userId,"Delete")==true)
				{
					btnDelete.Enabled=true;
				}
				else
				{
					btnDelete.Enabled=false;
				}
				//con=new SqlConnection(ConfigurationSettings.AppSettings["ConnectionString"].ToString());
				con=new SqlConnection(Connection.ConnectionString);
				if(!IsPostBack)
				{
					BindGrid();
					PageLoadSetting();
					txtName.Attributes.Add("onkeyup","return GetDesignations(event);");
					txtName.Attributes.Add("onkeypress","return disableEnterKey(event);");
					getCategory();
					this.Criteria.Visible=false;
					this.btnAdd.Attributes.Add("onclick","return Verify(myForm);");
					
				}
			}
			else
			{
				Response.Redirect("ErrorPage.aspx");
			}
			

		}
		private bool IsPageAccessAllowed()
		{
			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception ex)
			{
				string s=ex.Message;
				Response.Redirect("../Login.aspx");
			}
			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(2,47,userId,"View")==true)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("../Login.aspx");
				return false;
			}
		} 
		public void BindGrid()
		{
			con.Open();
			//SqlDataAdapter dr=new SqlDataAdapter("select ID,designation from t_dictionary where isactive=1",con);
			SqlDataAdapter dr=new SqlDataAdapter("select d.id ID,d.designation,category=case designation when designation then(select c.cat_name from t_categorization c where c.cat_id=d.cat_id)end,IsDepartmentalHead=case isdh when 0 then 'No' when 1 then 'Yes' end,CategoryAfterDHCriteria=case designation when designation then(select c.cat_name from t_categorization c where c.cat_id=d.catafterDH)end,cat_id,catafterdh from t_dictionary d where d.isactive=1",con);
			ds=new DataSet();
			dr.Fill(ds,"Dictionary");
			DataView dv=new DataView(ds.Tables["Dictionary"]);
			dv.Sort=sort;
			dgItems.DataSource=dv;
			dgItems.DataBind();
			con.Close();
			if(dgItems.Items.Count<=0)
			{
				this.lnkdgmsg.Text="No Items Found";
				this.lnkdgmsg.Visible=true;
				dgItems.AllowPaging=false;
			}
			else
			{
				this.lnkdgmsg.Visible=false;
				//dgItems.AllowPaging=true;
				dgItems.DataBind(); 
			}
		}
		public void getCategory()
		{
		 con.Open();
		 SqlCommand cmd=new SqlCommand("Select cat_id,cat_name,clevel from t_categorization where isactive=1 order by clevel",con);
		 SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
			 ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString()+"^"+rd[2].ToString());
			 this.ddnCat.Items.Add(itm);
			 //itm=new ListItem(rd[1].ToString(),rd[0].ToString());
			 //this.ddcCat.Items.Add(itm);
			}
		 rd.Close();
		 con.Close();
		}
		public void getCategory(int clevel)
		{
			this.ddcCat.Items.Clear();
			ListItem its=new ListItem("Select--Category","0");
			this.ddcCat.Items.Add(its);
			this.ddcCat.SelectedIndex=0;
			con.Open();
			SqlCommand cmd=new SqlCommand("Select cat_id,cat_name,clevel from t_categorization where isactive=1 and clevel <"+clevel+"order by clevel",con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
				this.ddcCat.Items.Add(itm);
			}
			rd.Close();
			con.Close();
		}
		public void PageLoadSetting()
		{
			this.btnAdd.CssClass="BlueButton";
			this.btnDelete.CssClass="BlueButton";
			this.btnCancel.CssClass="BlueButton";
			this.btnDelete.Enabled=false;
			this.btnCancel.Enabled=false;
		}
		public void SelectionSetting()
		{
			this.btnAdd.CssClass="BlueButton";
			this.btnDelete.CssClass="BlueButton";
			this.btnCancel.CssClass="BlueButton";
			if(GeoSecurity.isControlVisible(2,47,userId,"Delete")==true)
			{
				this.btnDelete.Enabled=true;
			}
			else
			{
				this.btnDelete.Enabled=false;
			}
			this.btnCancel.Enabled=true;
			this.btnAdd.Text="Update";
		}
		public bool CheckDuplicate(string designation,int pId,string Id)
		{
			string Query="";
			if(pId==1)
			{
				Query="select designation from t_dictionary where designation=@designation";
			}
			else
			{
				Query="select designation from t_dictionary where designation=@designation and ID !="+Id+"";
			}
			SqlCommand cmd=new SqlCommand(Query,con);
			SqlParameter _designation =new SqlParameter("@designation",SqlDbType.VarChar,200);
			_designation.Value=designation;
			cmd.Parameters.Add(_designation);
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			if(rd.HasRows)
			{
				rd.Close();
				return true;
			}
			else
			{
				rd.Close();
				return false;
			}
		}
		public int Insertion(string designation,SqlConnection con)
		{
			SqlTransaction trans=con.BeginTransaction();
			try
			{
				SqlCommand cmd=new SqlCommand("insert into t_dictionary(designation,isactive,cat_id,isdh,catafterdh,createby,createdate) values(@designation,1,@cat_id,@isdh,@catafterdh,@createby,@createdate)",con);
				cmd.Transaction=trans;
				SqlParameter _designation=new SqlParameter("@designation",SqlDbType.VarChar,200);
				_designation.Value=designation;
				SqlParameter _cat=new SqlParameter("@cat_id",SqlDbType.Int);
				SqlParameter _catDH=new SqlParameter("@catafterdh",SqlDbType.Int);
				SqlParameter _isdh=new SqlParameter("@isdh",SqlDbType.TinyInt);
				SqlParameter _createby=new SqlParameter("@createby",SqlDbType.Char);
				SqlParameter _createdate=new SqlParameter("@createdate",SqlDbType.SmallDateTime);
				string []tempArr=this.ddnCat.SelectedItem.Value.Split('^');
				_cat.Value=tempArr[0];
				if(this.ddcCat.Visible)
				{
					_catDH.Value=this.ddcCat.SelectedItem.Value;
				}
				else
				{
					_catDH.Value=DBNull.Value;
				}
				if(this.chkVerify.Checked)
				{
					_isdh.Value=1;
				}
				else
				{
					_isdh.Value=0;
				}
				_createby.Value=Session["user_id"].ToString();
				_createdate.Value=DateTime.Now.Date.ToShortDateString();
				cmd.Parameters.Add(_designation);
				cmd.Parameters.Add(_cat);
				cmd.Parameters.Add(_isdh);
				cmd.Parameters.Add(_catDH);
				cmd.Parameters.Add(_createby);
				cmd.Parameters.Add(_createdate);
				int i=cmd.ExecuteNonQuery();
				if(i>0)
				{
					int RaabtaID=0;
					cmd=new SqlCommand("SELECT MAX(ID) AS RaabtaID FROM t_dictionary",con);
					cmd.Transaction=trans;
					SqlDataReader rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
						RaabtaID=Int32.Parse(rd[0].ToString());
					}
					rd.Close();
					trans.Commit();
					return RaabtaID;
				}
				else
				{
                    trans.Rollback();
					return 0;
				}
			}
			catch(Exception ex)
			{
			 trans.Rollback();
			 return 0;
			}
		}
		public bool Update(string designation,string ID)
		{
			SqlCommand cmd=new SqlCommand("update t_dictionary set designation=@designation,cat_id=@cat_id,isdh=@isdh,catafterdh=@catafterdh,modifyby=@modifyby,modifydate=@modifydate where id="+ID+"",con);
			SqlParameter _designation=new SqlParameter("@designation",SqlDbType.VarChar,200);
			_designation.Value=designation;
			SqlParameter _cat=new SqlParameter("@cat_id",SqlDbType.Int);
			SqlParameter _catDH=new SqlParameter("@catafterdh",SqlDbType.Int);
			SqlParameter _isdh=new SqlParameter("@isdh",SqlDbType.TinyInt);
			SqlParameter _modifyby=new SqlParameter("@modifyby",SqlDbType.Char);
			SqlParameter _modifydate=new SqlParameter("@modifydate",SqlDbType.SmallDateTime);
			string []tempArr=this.ddnCat.SelectedItem.Value.Split('^');
			_cat.Value=tempArr[0];
			if(this.ddcCat.Visible)
			{
				_catDH.Value=this.ddcCat.SelectedItem.Value;
			}
			else
			{
				_catDH.Value=DBNull.Value;
			}
			if(this.chkVerify.Checked)
			{
				_isdh.Value=1;
			}
			else
			{
				_isdh.Value=0;
			}
			_modifyby.Value=Session["user_id"].ToString();
			_modifydate.Value=DateTime.Now.Date.ToString();
			cmd.Parameters.Add(_cat);
			cmd.Parameters.Add(_isdh);
			cmd.Parameters.Add(_catDH);
			cmd.Parameters.Add(_designation);
			cmd.Parameters.Add(_modifyby);
			cmd.Parameters.Add(_modifydate);
			int i=cmd.ExecuteNonQuery();
			string previousTitle="";
			for(int j=0;j<this.dgItems.Items.Count;j++)
			{
				if(this.dgItems.Items[j].Cells[1].Text==ID)
				{
					previousTitle=this.dgItems.Items[j].Cells[2].Text.Trim();
					break;
				}
			}
			cmd=new SqlCommand("update t_designation set designation=@designation where designation=@previousTitle",con);
			SqlParameter _desg=new SqlParameter("@designation",SqlDbType.VarChar);
			_desg.Value=designation;
			SqlParameter _previousTitle=new SqlParameter("@previousTitle",SqlDbType.VarChar);
			_previousTitle.Value=previousTitle;
			cmd.Parameters.Add(_desg);
			cmd.Parameters.Add(_previousTitle);
			cmd.ExecuteNonQuery();
			if(i>0)
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		public void GenericSetting()
		{
			this.txtName.Text="";
			this.txtName.Enabled=true;
			this.btnAdd.Text="Add To Dictionary";
			this.btnDelete.Enabled=false;
			this.btnCancel.Enabled=false;
			lnkduplicate.Visible=false;
			this.lblmsg.Visible=false;
			this.hNew.Visible=false;
			this.lnkduplicate.Visible=false;
			this.ddnCat.SelectedIndex=0;
			this.chkVerify.Enabled=false;
			this.chkVerify.Checked=false;
			this.Criteria.Visible=false;
			this.ddnCat.SelectedIndex=0;
		}
		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.ddnCat.SelectedIndexChanged += new System.EventHandler(this.ddnCat_SelectedIndexChanged);
			this.chkVerify.CheckedChanged += new System.EventHandler(this.chkVerify_CheckedChanged);
			this.btnAdd.Click += new System.EventHandler(this.btnAdd_Click);
			this.btnDelete.Click += new System.EventHandler(this.btnDelete_Click);
			this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
			this.hNew.Click += new System.EventHandler(this.hNew_Click);
			this.dgItems.SortCommand += new System.Web.UI.WebControls.DataGridSortCommandEventHandler(this.dgItems_SortCommand);
			this.dgItems.SelectedIndexChanged += new System.EventHandler(this.dgItems_SelectedIndexChanged);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		private void dgItems_SortCommand(object source, System.Web.UI.WebControls.DataGridSortCommandEventArgs e)
		{
			sort=e.SortExpression.ToString();
			BindGrid();
		}

		private void dgItems_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			SelectionSetting();
			this.txtName.ToolTip="";
			this.txtName.Text=dgItems.Items[this.dgItems.SelectedIndex].Cells[2].Text;
			this.txtName.ToolTip=dgItems.Items[this.dgItems.SelectedIndex].Cells[1].Text;
			this.txtName.Enabled=true;
			//this.ddnCat.SelectedItem.Text=dgItems.Items[this.dgItems.SelectedIndex].Cells[3].Text;
			for(int i=0;i<this.ddnCat.Items.Count;i++)
			{
				if(this.ddnCat.Items[i].Text.Trim()==dgItems.Items[this.dgItems.SelectedIndex].Cells[3].Text.Trim())
				{
					this.ddnCat.SelectedIndex=i;
					break;
				}
				else
				{
					this.ddnCat.SelectedIndex=0;
				}
			}
			
			
				string []Arr=this.ddnCat.SelectedItem.Value.Split('^');
				getCategory(Int32.Parse(Arr[1]));
				for(int j=0;j<this.ddcCat.Items.Count;j++)
				{
					if(this.ddcCat.Items[j].Text.Trim()==dgItems.Items[this.dgItems.SelectedIndex].Cells[5].Text.Trim())
					{
						
						this.ddcCat.SelectedIndex=j;
						break;
					}
					else
					{
						this.ddcCat.SelectedIndex=0;
						this.chkVerify.Checked=false;
						this.Criteria.Visible=false;
					}
				}
			if(Int32.Parse(Arr[1]) >7)	
			{
				this.ddcCat.SelectedIndex=0;
				this.chkVerify.Checked=false;
				this.Criteria.Visible=false;
				this.chkVerify.Enabled=false;
			}
			else
			{
				if(this.dgItems.Items[this.dgItems.SelectedIndex].Cells[4].Text=="Yes")
				{
					this.chkVerify.Checked=true;
					this.Criteria.Visible=true;
					this.chkVerify.Enabled=true;
				}
				else
				{
					this.chkVerify.Enabled=true;
					this.chkVerify.Checked=false;
					this.Criteria.Visible=false;
				}
				
			}
		    this.lblmsg.Visible=false;
			this.lnkduplicate.Visible=false;
			this.hNew.Visible=false;
		}

		private void btnCancel_Click(object sender, System.EventArgs e)
		{
			GenericSetting();
		}

		private void btnAdd_Click(object sender, System.EventArgs e)
		{
			if(IsRefresh)
				return;
			con.Open();
			if(this.btnAdd.Text=="Update")
			{
				if(CheckDuplicate(this.txtName.Text.Trim(),2,this.txtName.ToolTip))
				{
					this.lnkduplicate.Text="Designation Title already exist. Duplicate designations are not allowed";
					this.lnkduplicate.Visible=true;
				}
				else
				{
					if(Update(this.txtName.Text,this.txtName.ToolTip))
					{
						UpdateToTIS(this.txtName.Text.Trim(),this.txtName.ToolTip);
						this.lblmsg.Text="Designation Has Been Successfully Modified In Dictionary";
						this.lblmsg.Visible=true;
						this.txtName.Text="";
						this.lnkduplicate.Visible=false;
						this.btnDelete.Enabled=false;
						this.btnCancel.Enabled=false;
						this.btnAdd.Text="Add To Dictionary";
						this.txtName.ToolTip="";
						this.ddnCat.SelectedIndex=0;
						this.ddcCat.SelectedIndex=0;
						this.chkVerify.Enabled=false;
						this.chkVerify.Checked=false;
						this.Criteria.Visible=false;
						con.Close();
						BindGrid();
					}
				}
			}
			else
			{
				if(CheckDuplicate(this.txtName.Text.Trim(),1,""))
				{
					this.lnkduplicate.Text="Designation Title already exist. Duplicate designations are not allowed";
					this.lnkduplicate.Visible=true;
				}
				else
				{
					int RaabtaID=Insertion(this.txtName.Text.Trim(),con);
					if(RaabtaID>0)
					{
						this.lblmsg.Text="New Designation Has Successfully Entered In Dictionary";
						this.lblmsg.Visible=true;
						this.hNew.Visible=true;
						this.txtName.Enabled=false;
						this.lnkduplicate.Visible=false;
						this.ddnCat.SelectedIndex=0;
						this.ddcCat.SelectedIndex=0;
						this.chkVerify.Enabled=false;
						this.chkVerify.Checked=false;
						this.Criteria.Visible=false;
						con.Close();
						InsertToTIS(this.txtName.Text.Trim(),RaabtaID);
						BindGrid();
					}
				}
			}
			con.Close();
		}
        
		public void InsertToTIS(string name,int RaabtaID)
		{
		 SqlConnection conn=new SqlConnection(Connection.ConnectionStringTIS);
		 conn.Open();
		 SqlTransaction trans=conn.BeginTransaction();
		 SqlCommand cmd=new SqlCommand("insert into hrtcompanylogicdetail (typecode,description,raabtaid,status) values(@typecode,@description,@raabtaid,@status)",conn);
		 cmd.Transaction=trans;
		 SqlParameter _typecode=new SqlParameter("@typecode",SqlDbType.NVarChar);
		 SqlParameter _description=new SqlParameter("@description",SqlDbType.NVarChar);
		 SqlParameter _raabtaid=new SqlParameter("@raabtaid",SqlDbType.Int);
		 SqlParameter _status=new SqlParameter("@status",SqlDbType.Int);
		 _typecode.Value="Des1";
		 _description.Value=name;
		 _raabtaid.Value=RaabtaID;
		 _status.Value=0;
		 cmd.Parameters.Add(_typecode);
		 cmd.Parameters.Add(_description);
		 cmd.Parameters.Add(_raabtaid);
		 cmd.Parameters.Add(_status);
		 int z=cmd.ExecuteNonQuery();
			if(z>0)
			{
				trans.Commit();
			}
			else
			{
			 trans.Rollback();
			}
		 conn.Close();
		 conn.Dispose();
		}
		public void UpdateToTIS(string designation,string ID)
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionStringTIS);
			conn.Open();
			SqlTransaction trans=conn.BeginTransaction();
			string sql="select companylogicid from hrtcompanylogicdetail where raabtaid="+ID+" and typecode='Des1' and companylogicid is not null";
			SqlCommand cmd=new SqlCommand(sql,conn);
			cmd.Transaction=trans;
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			bool status=false;
			if(rd.HasRows)
			{
			 status=true;
			}
			else
			{
			 status=false;
			}
			rd.Close();
            cmd=new SqlCommand("update hrtcompanylogicdetail set description=@description,status=@status where raabtaid="+ID+" and typecode='Des1'",conn);
			cmd.Transaction=trans;
			SqlParameter _description=new SqlParameter("@description",SqlDbType.NVarChar);
			SqlParameter _status=new SqlParameter("@status",SqlDbType.Int);
			_description.Value=designation;
			if(status)
			{
				_status.Value=2;
			}
			else
			{
			  _status.Value=0;
			}
            cmd.Parameters.Add(_description);
			cmd.Parameters.Add(_status);
			int z=cmd.ExecuteNonQuery();
			if(z>0)
			{
				trans.Commit();
			}
			else
			{
			 trans.Rollback();
			}
			conn.Close();
			conn.Dispose();
		}
		private void hNew_Click(object sender, System.EventArgs e)
		{
			GenericSetting();
		}

		private void btnDelete_Click(object sender, System.EventArgs e)
		{
			con.Open();
			if(CheckExist(this.txtName.Text))
			{
				this.lnkduplicate.Text="You cannot delete this designation title from dictionary because employee designation has been created on that title";
				this.lnkduplicate.Visible=true;
			}
			else
			{
				if(Deletion(this.txtName.ToolTip))
				{
					this.lblmsg.Text="Designation Has Been Successfully Deleted From Dictionary";
					this.lblmsg.Visible=true;
					this.txtName.Text="";
					this.lnkduplicate.Visible=false;
					this.btnDelete.Enabled=false;
					this.btnCancel.Enabled=false;
					this.btnAdd.Text="Add To Dictionary";
					this.txtName.ToolTip="";
					this.ddnCat.SelectedIndex=0;
					this.ddcCat.SelectedIndex=0;
					this.chkVerify.Enabled=false;
					this.chkVerify.Checked=false;
					this.Criteria.Visible=false;
					con.Close();
					BindGrid();
				}
			}
			con.Close();
		}
		public bool CheckExist(string designation)
		{
			SqlCommand cmd=new SqlCommand("select designation,desigid from t_designation where status=1 and designation=@designation",con);
			SqlParameter _designation=new SqlParameter("@designation",SqlDbType.VarChar,200);
			_designation.Value=designation;
			cmd.Parameters.Add(_designation);
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			if(rd.HasRows)
			{
				rd.Close();
				return true;
			}
			else
			{
				rd.Close();
				return false;
			}
		}
		public bool Deletion(string ID)
		{
			//SqlCommand cmd=new SqlCommand("delete from t_dictionary where Id="+ID+"",con);
			SqlCommand cmd=new SqlCommand("update t_dictionary set isactive=0,deleteby=@del,deletedate=@deletedate where Id=@id",con);
			SqlParameter _id=new SqlParameter("@id",SqlDbType.Int);
			SqlParameter _del=new SqlParameter("@del",SqlDbType.Char);
			SqlParameter _deletedate=new SqlParameter("@deletedate",SqlDbType.DateTime);
			_id.Value=ID;
			_del.Value=Session["user_id"].ToString();
			_deletedate.Value=DateTime.Now.Date.ToShortTimeString();
			cmd.Parameters.Add(_id);
			cmd.Parameters.Add(_del);
			cmd.Parameters.Add(_deletedate);
            int i=cmd.ExecuteNonQuery();
			return true;
		}

		private void chkVerify_CheckedChanged(object sender, System.EventArgs e)
		{
			if(this.chkVerify.Checked)
			{
				this.Criteria.Visible=true;
			}
			else
			{
			 this.Criteria.Visible=false;
			 this.ddcCat.SelectedIndex=0;
			}
		}

		private void ddnCat_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			if(this.ddnCat.SelectedIndex !=0)
			{
				string []Arr=this.ddnCat.SelectedItem.Value.Split('^');
				if(Int32.Parse(Arr[1]) <=7)
				{
					this.chkVerify.Enabled=true;
					getCategory(Int32.Parse(Arr[1]));
				}
				else
				{
					this.chkVerify.Checked=false;
					this.chkVerify.Enabled=false;
					this.Criteria.Visible=false;
				}
			}
			else
			{
				this.chkVerify.Checked=false;
				this.chkVerify.Enabled=false;
				this.Criteria.Visible=false;
			}
		}
	}
}
