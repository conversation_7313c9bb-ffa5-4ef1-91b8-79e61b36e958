<%@ Page language="c#" Codebehind="IDCardRequestDetail.aspx.cs" AutoEventWireup="false" Inherits="GeoRabtaSite.admin.IDCardsRequestDetailAdmin" %>
<%@ Register TagPrefix="anthem" Namespace="Anthem" Assembly="Anthem" %>
<%@ Register TagPrefix="uc1" TagName="myMenus" Src="../myMenus.ascx" %>
<%@ Register TagPrefix="uc1" TagName="AdminUserControl" Src="../../AdminUserControl.ascx" %>
<%@ Register TagPrefix="uc1" TagName="LoginUser" Src="../../LoginUser.ascx" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Rabta ::</title>
		<META content="text/html; charset=utf-8" http-equiv="Content-Type">
		<meta name="GENERATOR" content="Microsoft Visual Studio .NET 7.1">
		<meta name="vs_targetSchema" content="http://schemas.microsoft.com/intellisense/ie5">
		<LINK rel="stylesheet" type="text/css" href="../RaabtaAdmin.css">
		<LINK rel="stylesheet" type="text/css" href="../Styles4.css">
		<LINK rel="stylesheet" type="text/css" href="../css/start/jquery-ui-1.8.4.custom.css">
		<script type="text/javascript" src="../js/jquery-1.4.2.min.js"></script>
		<script type="text/javascript" src="../js/jquery-ui-1.8.2.custom.min.js"></script>
		<style type="text/css">.style1 { FONT-SIZE: 8px; FONT-WEIGHT: bold }
	.hid { DISPLAY: none }
	TH { FONT-SIZE: 12px }
		</style>
		<style title="currentStyle" type="text/css">@import url( ../../media/css/demos.css ); 
		</style>
		</STYLE> 
		<!--<script language="javascript" src="../media/js/jquery.js" type="text/javascript"></script>-->
		<style type="text/css">#lnkClientToggle { POSITION: relative; PADDING-BOTTOM: 0.4em; PADDING-LEFT: 20px; PADDING-RIGHT: 1em; TEXT-DECORATION: none; PADDING-TOP: 0.4em }
	#lnkClientToggle SPAN.ui-icon { POSITION: absolute; MARGIN: -12px 5px 0px 0px; TOP: 50%; LEFT: 0.2em }
	UL#icons { PADDING-BOTTOM: 0px; MARGIN: 0px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; PADDING-TOP: 0px }
	UL#icons LI { POSITION: relative; PADDING-BOTTOM: 4px; LIST-STYLE-TYPE: none; MARGIN: 2px; PADDING-LEFT: 0px; PADDING-RIGHT: 0px; FLOAT: left; CURSOR: pointer; PADDING-TOP: 4px }
	UL#icons SPAN.ui-icon { MARGIN: 0px 4px; FLOAT: left }
	#lnkServiceToggle { POSITION: relative; PADDING-BOTTOM: 0.4em; PADDING-LEFT: 20px; PADDING-RIGHT: 1em; TEXT-DECORATION: none; PADDING-TOP: 0.4em }
	#lnkServiceToggle SPAN.ui-icon { POSITION: absolute; MARGIN: -12px 5px 0px 0px; TOP: 50%; LEFT: 0.2em }
		</style>
		<!--<script language="javascript" type="text/javascript" src="../media/js/jquery.js"></script>-->
		<script language="javascript" type="text/javascript" src="../../media/js/jquery.dataTables.js"></script>
		<script type="text/javascript" charset="utf-8">
			$(document).ready(function() 
			{
			    $('#example').dataTable({
                "bJQueryUI": true,
                "sPaginationType": "full_numbers"
                 });
                 $('#example2').dataTable({
                "bJQueryUI": true,
                "sPaginationType": "full_numbers"
                 });
                 $('#example3').dataTable({
                "bJQueryUI": true,
                "sPaginationType": "full_numbers"
                 });
                 $('#example4').dataTable({
                "bJQueryUI": true,
                "sPaginationType": "full_numbers"
                 });
				
				$('#tabs').tabs();
				$('#tabs2').tabs();
				$('#accordion').accordion();
			    
			    $("#ddActionComments").hide();
			    $("#ddActionCode").change(function(){
			    
			    if($("#ddActionCode").val()!='0' && $("#ddActionCode").val()!='1')
			    {
			     $("#ddActionComments").show('slow');
			     $("#txtComments").focus();
			     DisplayMesg();
			    }
			    else
			    {
			     $("#ddActionComments").hide('slow');
			     $("#txtComments").val('');
			     DisplayMesg();
			    }
			   });
			   //window.opener.reFresh();
			});
		</script>
		<script language="javascript">
		function SetParentWindowsHiddenFieldValue()
        {
        if(document.getElementById("ddActionCode").selectedIndex==0)
         {
          alert('Please select any action code');
          document.getElementById("ddActionCode").focus();
          return false;
         }
        } 
        function HideMesg()
        {
         var msg=new Array('Dear Member TeamGEO,<br/><br/>It is hereby acknowledged that your renewed GEO ID Card for 2013 has been delivered to you and your expired GEO ID Card/lost report (Roznamcha) has been received.<br/><br/><b>"A lost or damaged card (i.e tampered seal) should immediately be reported to GEO Admin for cancellation of the previous and issuance of the new card."<br/><br/>*Rupees 500/= will be charged for a replacement ID Card.</b><br/><br/>Sar Utha Kay GEO!',
                  'Dear Member TeamGEO,<br/><br/>It is hereby acknowledged that your renewed GEO ID Card for 2013 has been received by <b>(Receiver PCode (Receiver Name))</b> on your behalf. Also, your expired GEO ID Card/ lost report (Roznamcha) has been received.<br/><br/><b>"A lost or damaged card (i.e tampered seal) should immediately be reported to GEO Admin for cancellation of the previous and issuance of the new card."<br/><br/>*Rupees 500/= will be charged for a replacement ID Card.</b><br/><br/>Sar Utha Kay GEO!',
                  'Dear Member TeamGEO,<br/><br/>Thank you for contacting us for your new GEO ID Card for 2013. However, we regret to inform you that your renewed GEO ID Card is On-Hold as your expired GEO ID Card/ lost report (Roznamcha) was not submitted. Kindly return your expired GEO ID Card/ lost report (Roznamcha) in order to receive your renewed Card.<br/><br/>Sar Utha Kay GEO!',
                  'Dear Member TeamGEO,<br/><br/>Thank you for contacting us for your new GEO ID Card for 2013. However, we regret to inform you that your renewed GEO ID Card is On-Hold due to an exceptional Process Error.<br/><br/>HR representative will contact you as soon as we resolve the exceptions.<br/><br/>Sar Utha Kay GEO!',
                  'Dear Member TeamGEO,<br/><br/>We would like to inform you that the <b>Process Error</b> has been resolved and your renewed GEO ID Card for 2013 is ready to be collected. For collection process, please refer to the initial email sent to you.<br/><br/><b>"A lost or damaged card (i.e tampered seal) should immediately be reported to GEO Admin for cancellation of the previous and issuance of the new card."<br/><br/>*Rupees 500/= will be charged for a replacement ID Card.</b> <br/><br/>Sar Utha Kay GEO!');
         var idx=document.getElementById('ddActionCode').selectedIndex;
         var Heading="<b>Mail Message Text</b><br>"
         if(idx==0)
         {
          $('#aMesg').text('');
         }
         
         if(idx==1)
         {
         $('#divMesg').html(Heading+msg[0]);
         $('#divMesg').toggle();
         $('#aMesg').text('View Mail Message');
         }
         if(idx==2)
         {
         $('#divMesg').html(Heading+msg[1]);
         $('#divMesg').toggle();
         $('#aMesg').text('View Mail Message');
         }
         if(idx==3)
         {
         $('#divMesg').html(Heading+msg[2]);
         $('#divMesg').toggle();
         $('#aMesg').text('View Mail Message');
         }
         if(idx==4)
         {
         $('#divMesg').html(Heading+msg[3]);
         $('#divMesg').toggle();
         $('#aMesg').text('View Mail Message');
         }
         if(idx==5)
         {
         $('#divMesg').html(Heading+msg[0]);
         $('#divMesg').toggle();
         $('#aMesg').text('View Mail Message');
         }
         if(idx==6)
         {
         $('#divMesg').html(Heading+msg[4]);
         $('#divMesg').toggle();
         $('#aMesg').text('View Mail Message');
         }
        }
        function DisplayMesg()
        {
         var msg=new Array('Dear Member TeamGEO,<br/><br/>It is hereby acknowledged that your renewed GEO ID Card for 2013 has been delivered to you and your expired GEO ID Card/lost report (Roznamcha) has been received.<br/><br/><b>"A lost or damaged card (i.e tampered seal) should immediately be reported to GEO Admin for cancellation of the previous and issuance of the new card."<br/><br/>*Rupees 500/= will be charged for a replacement ID Card.</b><br/><br/>Sar Utha Kay GEO!',
                  'Dear Member TeamGEO,<br/><br/>It is hereby acknowledged that your renewed GEO ID Card for 2013 has been received by <b>(Receiver PCode (Receiver Name))</b> on your behalf. Also, your expired GEO ID Card/ lost report (Roznamcha) has been received.<br/><br/><b>"A lost or damaged card (i.e tampered seal) should immediately be reported to GEO Admin for cancellation of the previous and issuance of the new card."<br/><br/>*Rupees 500/= will be charged for a replacement ID Card.</b><br/><br/>Sar Utha Kay GEO!',
                  'Dear Member TeamGEO,<br/><br/>Thank you for contacting us for your new GEO ID Card for 2013. However, we regret to inform you that your renewed GEO ID Card is On-Hold as your expired GEO ID Card/ lost report (Roznamcha) was not submitted. Kindly return your expired GEO ID Card/ lost report (Roznamcha) in order to receive your renewed Card.<br/><br/>Sar Utha Kay GEO!',
                  'Dear Member TeamGEO,<br/><br/>Thank you for contacting us for your new GEO ID Card for 2013. However, we regret to inform you that your renewed GEO ID Card is On-Hold due to an exceptional Process Error.<br/><br/>HR representative will contact you as soon as we resolve the exceptions.<br/><br/>Sar Utha Kay GEO!',
                  'Dear Member TeamGEO,<br/><br/>We would like to inform you that the <b>Process Error</b> has been resolved and your renewed GEO ID Card for 2013 is ready to be collected. For collection process, please refer to the initial email sent to you. <br/><br/><b>"A lost or damaged card (i.e tampered seal) should immediately be reported to GEO Admin for cancellation of the previous and issuance of the new card."<br/><br/>*Rupees 500/= will be charged for a replacement ID Card.</b><br/><br/>Sar Utha Kay GEO!');
         var idx=document.getElementById('ddActionCode').selectedIndex;
         var Heading="<b>Mail Message Text</b><br>"
         if(idx==0)
         {
          $('#aMesg').text('');
          $('#divMesg').html('');
         }
         if(idx==1)
         {
         $('#divMesg').html(Heading+msg[0]);
         $('#divMesg').show();
         $('#aMesg').text('Hide Mail Message');
         }
         if(idx==2)
         {
         $('#divMesg').html(Heading+msg[1]);
         $('#divMesg').show();
         $('#aMesg').text('Hide Mail Message');
         }
         if(idx==3)
         {
         $('#divMesg').html(Heading+msg[2]);
         $('#divMesg').show();
         $('#aMesg').text('Hide Mail Message');
         }
         if(idx==4)
         {
         $('#divMesg').html(Heading+msg[3]);
         $('#divMesg').show();
         $('#aMesg').text('Hide Mail Message');
         }
         if(idx==5)
         {
         $('#divMesg').html(Heading+msg[0]);
         $('#divMesg').show();
         $('#aMesg').text('Hide Mail Message');
         }
         if(idx==6)
         {
         $('#divMesg').html(Heading+msg[4]);
         $('#divMesg').show();
         $('#aMesg').text('Hide Mail Message');
         }
        } 
		</script>
	</HEAD>
	<body dir="ltr" bottomMargin="0" leftMargin="0" rightMargin="0" topMargin="0">
		<form id="myForm" name="MyForm" runat="server">
			<table border="0" cellSpacing="0" cellPadding="0" width="750" bgColor="#ffffff" align="center"
				height="100%">
				<tr>
					<td vAlign="middle" align="left"></td>
				</tr>
				<tr>
					<td height="10"></td>
				</tr>
				<TR>
					<TD class="PageTitle" height="20">Geo Raabta Admin :: Request Detail</TD>
				</TR>
				<tr>
					<td height="20" background="../images/menu-off-bg.gif"></td>
				</tr>
				<tr>
					<td class="MainBG" vAlign="top" align="left">
						<P><BR>
							<BR>
						</P>
						<fieldset><legend>Request Detail</legend><label><b>
									<TABLE id="Table1" border="0" cellSpacing="1" cellPadding="1" width="100%">
										<TR>
											<TD class="info"><anthem:image id="Image1" runat="server"></anthem:image></TD>
										</TR>
										<TR>
											<TD class="info"><STRONG>Request #:</STRONG><BR>
												<asp:label id="txtReqID" Runat="server"></asp:label></TD>
										</TR>
										<TR>
											<TD class="info"><STRONG>Employee Code:<BR>
												</STRONG>
												<asp:label id="txtPCode" Runat="server"></asp:label></TD>
										</TR>
										<TR>
											<TD class="info"><STRONG>Employee Name:<BR>
												</STRONG>
												<asp:label id="txtName" Runat="server"></asp:label></TD>
										</TR>
										<TR>
											<TD class="info"><STRONG>Department:<BR>
												</STRONG>
												<asp:label id="txtDept" Runat="server"></asp:label></TD>
										</TR>
										<TR>
											<TD class="info"><STRONG>Designation:<BR>
												</STRONG>
												<asp:label id="txtDesg" Runat="server"></asp:label></TD>
										</TR>
										<TR>
											<TD class="info"><STRONG>Station:<BR>
												</STRONG>
												<asp:label id="txtStation" Runat="server"></asp:label></TD>
										</TR>
										<TR>
											<TD class="info"><STRONG>CNIC #:<BR>
												</STRONG>
												<asp:label id="txtCNIC" Runat="server"></asp:label></TD>
										</TR>
										<TR>
											<TD class="info"><STRONG>Last Action on Request:</STRONG><BR>
												<asp:label id="txtLastAction" Runat="server"></asp:label></TD>
										</TR>
										<TR>
											<TD class="info"><STRONG>Last Action Date:<BR>
												</STRONG>
												<asp:label id="txtLastActionDate" Runat="server"></asp:label></TD>
										</TR>
									</TABLE>
									<P></P>
								</b></label>
						</fieldset>
						<fieldset><legend>Action Code</legend><label><b>
									<TABLE id="Table2" border="0" cellSpacing="1" cellPadding="1" width="100%">
										<TR>
											<TD class="info"><STRONG>Select Action Code:</STRONG><BR>
												<DIV style="WIDTH: 705px; HEIGHT: 22px" id="ddAbondand" ms_positioning="FlowLayout"><asp:dropdownlist id="ddActionCode" runat="server" Width="512px" CssClass="textbox"></asp:dropdownlist><BR>
													<div id="ddActionComments"><STRONG>Comments:<br>
															<asp:textbox id="txtComments" Runat="server" Width="608px" CssClass="textbox" TextMode="MultiLine"
																Rows="6" Height="124px"></asp:textbox></STRONG></div>
												</DIV>
												<div style="DISPLAY: none" id="divMesg"><span id="spnMesg"></span></div>
												<A id="aMesg" onclick="HideMesg()" href="javascript:void(0)">Hide Mail Message</A>
											</TD>
										</TR>
									</TABLE>
								</b></label>
							<asp:button id="btnReply" runat="server" CssClass="fbtab" Text="Save Request"></asp:button><BR>
							<asp:label id="lblSuccessMsg" runat="server" Font-Bold="True" Font-Size="X-Small" ForeColor="MidnightBlue"
								Visible="False"></asp:label></fieldset>
						<BR>
						<FIELDSET><LEGEND>Service Track</LEGEND><LABEL><B>
									<TABLE id="Table3" border="0" cellSpacing="1" cellPadding="1" width="100%">
										<TR>
											<TD class="info"><asp:label id="lblHTML" runat="server"></asp:label></TD>
										</TR>
									</TABLE>
						</FIELDSET>
						</B></LABEL></td>
				</tr>
				<TR>
					<TD vAlign="middle" align="center" height="20">Copyright ©
						<% =DateTime.Now.Year%>
						Independent Media Corporation <A href="http://www.geo.tv">www.geo.tv</A></TD>
				</TR>
			</table>
		</form>
	</body>
</HTML>
