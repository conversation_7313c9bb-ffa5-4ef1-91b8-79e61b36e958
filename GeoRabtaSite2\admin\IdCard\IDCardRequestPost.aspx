<%@ Page language="c#" Codebehind="IDCardRequestPost.aspx.cs" AutoEventWireup="false" Inherits="GeoRabtaSite.admin.IDCardRequestPostAdmin" %>
<%@ Register TagPrefix="uc1" TagName="myMenus" Src="../myMenus.ascx" %>
<%@ Register TagPrefix="uc1" TagName="AdminUserControl" Src="../../AdminUserControl.ascx" %>
<%@ Register TagPrefix="uc1" TagName="LoginUser" Src="../../LoginUser.ascx" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Rabta ::</title>
		<META content="text/html; charset=utf-8" http-equiv="Content-Type">
		<meta name="GENERATOR" content="Microsoft Visual Studio .NET 7.1">
		<meta name="vs_targetSchema" content="http://schemas.microsoft.com/intellisense/ie5">
		<LINK rel="stylesheet" type="text/css" href="../RaabtaAdmin.css">
		<LINK rel="stylesheet" type="text/css" href="../Styles4.css">
		<script language="javascript">
		
		</script>
	</HEAD>
	<body dir="ltr" bottomMargin="0" leftMargin="0" rightMargin="0" topMargin="0">
		<form id="myForm" name="MyForm" runat="server">
			<table border="0" cellSpacing="0" cellPadding="0" width="750" bgColor="#ffffff" align="center"
				height="100%">
				<tr>
					<td vAlign="middle" align="left">
					</td>
				</tr>
				<tr>
					<!--<td height="10">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" data="data:application/x-oleobject;base64,btt80m2uzxGWuERFU1QAAGdVZlUACQAAnVAAAAgBAAAIAAIAAAAAAAgAAAAAAAgAAAAAAAgADgAAAFcAaQBuAGQAbwB3AAAACAAGAAAALQAxAAAACAAGAAAALQAxAAAACAAKAAAASABpAGcAaAAAAAgAAgAAAAAACAAGAAAALQAxAAAACAAAAAAACAACAAAAAAAIABAAAABTAGgAbwB3AEEAbABsAAAACAAEAAAAMAAAAAgABAAAADAAAAAIAAIAAAAAAAgAAAAAAAgAAgAAAAAADQAAAAAAAAAAAAAAAAAAAAAACAAEAAAAMQAAAAgABAAAADAAAAAIAAAAAAAIAAQAAAAwAAAACAAIAAAAYQBsAGwAAAAIAAwAAABmAGEAbABzAGUAAAA="
							width="780" height="10" VIEWASTEXT>
							<embed src="flash/Top2.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="10"> </embed>
						</OBJECT>
					</td>-->
				</tr>
				<TR>
					<TD class="PageTitle" height="20">Raabta Admin ::&nbsp;Post ID Card Request</TD>
				</TR>
				<tr>
					<td height="20" background="../images/menu-off-bg.gif">
						<asp:ImageButton style="Z-INDEX: 0" id="imgPost" runat="server" ImageUrl="..\..\images\PostReq.gif"></asp:ImageButton>
						<asp:ImageButton style="Z-INDEX: 0" id="imgProcess" runat="server" ImageUrl="..\..\images\procreq.gif"></asp:ImageButton>
						<asp:HyperLink id="HyperLink1" runat="server" NavigateUrl="https://georaabta">Back to Raabta</asp:HyperLink></td>
				</tr>
				<tr>
					<td class="MainBG" vAlign="top" align="left">
						<P>
							<TABLE id="Table1" border="1" cellSpacing="1" cellPadding="1" width="100%">
								<TR>
									<TD class="OrangeFormTitle">Post ID Card Request</TD>
								</TR>
								<TR>
									<TD>
										<asp:Label id="Label1" runat="server" Font-Bold="True">Search Employee by Pcode( e.g: P1443)</asp:Label><BR>
										<asp:TextBox style="Z-INDEX: 0" id="txtPCodeSearch" runat="server" CssClass="textbox" Width="224px"></asp:TextBox>
										<asp:LinkButton id="lnkSearch" runat="server">Search</asp:LinkButton><BR>
										<asp:Label style="Z-INDEX: 0" id="lblMessageError" runat="server" Visible="False"></asp:Label></TD>
								</TR>
								<TR>
									<TD>
										<TABLE id="Table2" border="1" cellSpacing="1" cellPadding="1" width="100%" runat="server">
											<TR>
												<TD style="WIDTH: 118px" colSpan="2">
													<asp:Image style="Z-INDEX: 0" id="imgPic" runat="server"></asp:Image></TD>
											</TR>
											<TR>
												<TD width="20%">Employee Pcode:</TD>
												<TD>
													<asp:Label style="Z-INDEX: 0" id="lblPCode" runat="server" Font-Bold="True"></asp:Label></TD>
											</TR>
											<TR>
												<TD width="20%">Name:<STRONG> </STRONG>
												</TD>
												<TD>
													<asp:Label style="Z-INDEX: 0" id="lblName" runat="server" Font-Bold="True"></asp:Label></TD>
											</TR>
											<TR>
												<TD width="20%">Designation:</TD>
												<TD>
													<asp:Label style="Z-INDEX: 0" id="lblDesignation" runat="server" Font-Bold="True"></asp:Label></TD>
											</TR>
											<TR>
												<TD width="20%">Employee Status:
												</TD>
												<TD>
													<asp:Label style="Z-INDEX: 0" id="lblStatus" runat="server" Font-Bold="True"></asp:Label></TD>
											</TR>
											<TR>
												<TD colSpan="2"><STRONG>Email Address:<BR>
													</STRONG>(You can&nbsp;give email address of Supervisor if particular 
													employee&nbsp;does not has email)&nbsp;:<BR>
													<asp:TextBox style="Z-INDEX: 0" id="txtEmail" runat="server" CssClass="textbox" Width="216px"></asp:TextBox>(e.g
													<A href="mailto:<EMAIL>"><EMAIL></A>)
													<asp:RegularExpressionValidator style="Z-INDEX: 0" id="RegularExpressionValidator1" runat="server" ErrorMessage="Incorrect email address"
														Display="Dynamic" ValidationExpression="\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*" ControlToValidate="txtEmail"></asp:RegularExpressionValidator></TD>
											</TR>
											<TR>
												<TD colSpan="2">
													<asp:Button style="Z-INDEX: 0" id="btnPost" runat="server" CssClass="button" Text="Post Request"></asp:Button><BR>
													<asp:Label style="Z-INDEX: 0" id="lblMessage" runat="server" Visible="False"></asp:Label></TD>
											</TR>
										</TABLE>
									</TD>
								</TR>
							</TABLE>
						</P>
					</td>
				</tr>
				<tr>
					<td vAlign="middle" align="center" height="20">Copyright ©
						<% =DateTime.Now.Year%>
						Independent Media Corporation <A href="http://www.geo.tv">www.geo.tv</A></td>
				</tr>
			</table>
		</form>
	</body>
</HTML>
