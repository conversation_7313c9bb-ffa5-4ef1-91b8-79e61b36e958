using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;
namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for GeoIdeas.
	/// </summary>
	public class GeoSBU : System.Web.UI.Page
	{
		private SqlConnection con;
		protected System.Web.UI.WebControls.Button btnAdd;
		protected System.Web.UI.WebControls.DataGrid DataGrid1;
		protected System.Web.UI.WebControls.Button cmdDelete;
		protected System.Web.UI.WebControls.Button cmdCancel;
		protected System.Web.UI.WebControls.Label lblSBUID;
		protected System.Web.UI.WebControls.TextBox txtName;
		protected System.Web.UI.WebControls.Button cmdSBUHead;
		protected System.Web.UI.WebControls.Button Button1;
		protected System.Web.UI.WebControls.Button cmdSave;
		protected System.Web.UI.WebControls.DropDownList ddlHOD;
		protected System.Web.UI.WebControls.Panel Panel1;
		protected System.Web.UI.WebControls.Button btnChange;
		protected System.Web.UI.WebControls.Label Label1;
		protected System.Web.UI.WebControls.DataGrid DataGrid2;
		protected System.Web.UI.WebControls.Panel Panel12;
		protected eWorld.UI.CalendarPopup dpTo;
		protected eWorld.UI.CalendarPopup dpFrom;
		protected System.Web.UI.WebControls.CheckBox CheckBox1;
		protected System.Web.UI.WebControls.Label lblSbuHeadID;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator1; 
		public static string sort;
		public static int WebFormID=1;
		public static int ProjectID=2;
		public static string userId="";
				private bool IsPageAccessAllowed()
				{
					
					try
					{
						userId=Session["user_id"].ToString();
					}
					catch(Exception ex)
					{
						Response.Redirect("Login.aspx");
					}
		
					if(userId!="")
					{
						if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"View")==true)
						{
							return true;
						}
						else
						{
							return false;
						}
					}
					else
					{
						Response.Redirect("Login.aspx");
						return false;
					}
				}
		private void Page_Load(object sender, System.EventArgs e)
		{
			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			if(IsPageAccessAllowed())
			{
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Add")==false)
				{
					this.btnAdd.Enabled=false;
				}
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Delete")==false)
				{
				 cmdDelete.Enabled=false;
				}
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Edit")==false)
				{
					this.DataGrid1.Columns[0].Visible=false;
				}
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"SBUHead")==false)
				{
					this.btnChange.Enabled=false;
					this.DataGrid2.Columns[4].Visible=false;
				}


			}
			else
			{
				Response.Redirect("ErrorPage.aspx");
			}
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			Page.SmartNavigation=true;
			Session["user_id"]="P991";
			if(!IsPostBack)
			{
				cmdSave.Attributes.Add("onclick","if(confirm('Are you sure to change SBU Head?')){return true}else{return false}");
				cmdDelete.Attributes.Add("onclick","if(confirm('Are you sure to delete this SBU?')){return true}else{return false}");
				BindGrid();
				//getHeads(); 
				getHOD();
				Panel1.Visible=true;
				Panel12.Visible=false;

			}

		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.DataGrid2.EditCommand += new System.Web.UI.WebControls.DataGridCommandEventHandler(this.DataGrid2_EditCommand);
			this.btnChange.Click += new System.EventHandler(this.btnChange_Click);
			this.CheckBox1.CheckedChanged += new System.EventHandler(this.CheckBox1_CheckedChanged);
			this.cmdSave.Click += new System.EventHandler(this.cmdSave_Click);
			this.Button1.Click += new System.EventHandler(this.Button1_Click);
			this.btnAdd.Click += new System.EventHandler(this.btnAdd_Click);
			this.cmdDelete.Click += new System.EventHandler(this.cmdDelete_Click);
			this.cmdCancel.Click += new System.EventHandler(this.cmdCancel_Click);
			this.cmdSBUHead.Click += new System.EventHandler(this.cmdSBUHead_Click);
			this.DataGrid1.EditCommand += new System.Web.UI.WebControls.DataGridCommandEventHandler(this.DataGrid1_EditCommand);
			this.DataGrid1.SortCommand += new System.Web.UI.WebControls.DataGridSortCommandEventHandler(this.DataGrid1_SortCommand);
			this.DataGrid1.DeleteCommand += new System.Web.UI.WebControls.DataGridCommandEventHandler(this.DataGrid1_DeleteCommand);
			this.DataGrid1.SelectedIndexChanged += new System.EventHandler(this.DataGrid1_SelectedIndexChanged);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion


		private void UpdateEditColoumn()
		{
			foreach( DataGridItem di in DataGrid2.Items )			
			{
				if(di.Cells[3].Text=="No")
				{
					di.Cells[4].Text="";
				}
			}

		}
		public void getHOD()
		{
			ddlHOD.Items.Clear();
			string hdept="select e.pcode,e.name from t_employee e,t_categorization c where e.levels=c.cat_id and c.supervisory_role=1 and c.isactive=1";
			SqlCommand cmd=new SqlCommand(hdept,con);
			SqlDataReader rd=cmd.ExecuteReader();
			ListItem i=new ListItem();
			i.Value="0";
			i.Text="Select--Head of Department";
			ddlHOD.Items.Insert(0,i);
			while(rd.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rd[0].ToString();
				itm.Text=rd[1].ToString();
				ddlHOD.Items.Add(itm); 
			}
			rd.Close();
			
		}

		private void BindGrid()
		{
			//string query="select s.sbuname Name,e.name SBUHead from t_sbu s,t_employee e where isactive='"+1+"' And e.pcode=s.sbuhead";
			string query="select sbuid,sbuname from t_sbu where isActive=1 and Del=1 order by sbuname";
			SqlDataAdapter rd=new SqlDataAdapter(query,con);
			DataSet ds=new DataSet();
			rd.Fill(ds,"SBU");
			DataView dv=new DataView(ds.Tables["SBU"]);
			dv.Sort=sort;
			this.DataGrid1.DataSource=dv;
			this.DataGrid1.DataBind(); 
		}

//		private void getHeads()
//		{
//			string query="select pcode,name from t_employee";
//			SqlCommand cmd=new SqlCommand(query,con);
//			SqlDataReader rd=cmd.ExecuteReader();
//			while(rd.Read())
//			{
//				ListItem itm=new ListItem();
//				itm.Value=rd["pcode"].ToString();
//				itm.Text=rd["name"].ToString();
//				//this.dropDownHeads.Items.Add(itm); 
//			}
//			rd.Close();
//		}
//
		private void btnAdd_Click(object sender, System.EventArgs e)
		{
			if (btnAdd.Text=="Add")
			{
				
				//string queryInsert="insert into t_sbu values('"+this.txtName.Text+"','"+this.dropDownHeads.SelectedValue+"','"+1+"','"+1+"')";
				string queryInsert="Insert into t_sbu(sbuname,isActive,del) values('" + txtName.Text + "',1,1)";
				SqlCommand cmd=new SqlCommand(queryInsert,con);
				cmd.ExecuteNonQuery();
				BindGrid();
				ResetForm();
				
			}
			else if (btnAdd.Text=="Update")
			{
				
				//string queryInsert="insert into t_sbu values('"+this.txtName.Text+"','"+this.dropDownHeads.SelectedValue+"','"+1+"','"+1+"')";
				string queryUpdate="update t_sbu set sbuname='" + txtName.Text + "' where sbuid="+lblSBUID.Text;
				SqlCommand cmd=new SqlCommand(queryUpdate,con);
				cmd.ExecuteNonQuery();
				BindGrid();
				ResetForm();
			}
		}

		private void DataGrid1_SortCommand(object source, System.Web.UI.WebControls.DataGridSortCommandEventArgs e)
		{
			sort=e.SortExpression.ToString();
			BindGrid(); 
		}

		private void DataGrid1_DeleteCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{
		
		}

		private void DataGrid1_EditCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{
			lblSBUID.Text=e.Item.Cells[1].Text;
			txtName.Text=e.Item.Cells[2].Text;
			btnAdd.Text="Update";
			if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Delete")==false)
			{
				cmdDelete.Enabled=false;
			}
			else
			{
				cmdDelete.Enabled=true;
			}
			cmdCancel.Enabled=true;
			cmdSBUHead.Enabled=true;
			DataGrid1.Visible=false;
			Panel12.Visible=true;
			Panel1.Visible=false;
			DataGrid2.Visible=true;
			UpdateGrid();
			UpdateEditColoumn();
		}

		private void ResetForm()
		{
			lblSBUID.Text="";
			txtName.Text="";
			cmdDelete.Enabled=false;
			cmdCancel.Enabled=false;
			btnAdd.Enabled=true;
			cmdSBUHead.Enabled=false;
			Panel12.Visible=false;
			btnAdd.Text="Add";
			DataGrid1.Visible=true;

		}
		private void cmdCancel_Click(object sender, System.EventArgs e)
		{
			ResetForm();
			BindGrid();
			
		}

		private void DataGrid1_SelectedIndexChanged(object sender, System.EventArgs e)
		{
		
		}

		private void cmdDelete_Click(object sender, System.EventArgs e)
		{
			try
			{
				SqlCommand cmd=new SqlCommand("delete from t_sbu where sbuid="+lblSBUID.Text,con);
				cmd.ExecuteNonQuery();
				ResetForm();
				BindGrid();
				
			}
			catch(SqlException ex)
			{
			}

		}

		private void cmdSBUHead_Click(object sender, System.EventArgs e)
		{
			Panel12.Visible=true;
			Panel1.Visible=false;
			cmdSBUHead.Enabled=false;
			UpdateGrid();
		}

		private void Button1_Click(object sender, System.EventArgs e)
		{
			Panel1.Visible=false;
			btnChange.Enabled=true;
			lblSbuHeadID.Text="";
			UpdateEditColoumn();

		}


		private void UpdateGrid()
		{
			
			SqlCommand cmd = new SqlCommand(" SELECT dbo.t_sbuheads.sbuheadid, dbo.t_Employee.name, dbo.t_sbuheads.DateFrom, dbo.t_sbuheads.DateTo,  " +
				" CASE WHEN dbo.t_sbuheads.isActive = 0 THEN 'No' ELSE 'Yes' END AS IsActive " +
				" FROM dbo.t_sbuheads INNER JOIN " +
				" dbo.t_Employee ON dbo.t_sbuheads.pcode = dbo.t_Employee.pcode " +
				" WHERE (dbo.t_sbuheads.sbuid = " + lblSBUID.Text + ")",con);
			SqlDataReader dr=cmd.ExecuteReader();
			DataGrid2.DataKeyField="sbuheadid";
			DataGrid2.DataSource=dr;
			DataGrid2.DataBind();
			dr.Close();
		}


		private void btnChange_Click(object sender, System.EventArgs e)
		{
			Panel1.Visible=true;
			btnChange.Enabled=false;
			UpdateEditColoumn();

		}

		private void LinkButton1_Click(object sender, System.EventArgs e)
		{
			Panel12.Visible=false;
			cmdSBUHead.Enabled=true;
		}

		private void cmdSave_Click(object sender, System.EventArgs e)
		{
			
			if (cmdSave.Text=="Save")
			{
				if (lblSBUID.Text!="" && ddlHOD.SelectedIndex>0)
				{
					string SBUID=lblSBUID.Text; //ddlSBU2.SelectedValue.ToString();
					string pcode=ddlHOD.SelectedValue.ToString();
					SqlCommand cmd=new SqlCommand("update t_sbuheads set isActive=0 where sbuid="+SBUID,con);
					cmd.ExecuteNonQuery();
					cmd.Dispose();
					cmd=new SqlCommand("update t_sbuheads set dateto=getdate() where sbuid="+SBUID +" and dateto IS NULL",con);
					cmd.ExecuteNonQuery();

					cmd=new SqlCommand("Insert into t_sbuheads(pcode, sbuid, datefrom, dateto) values(@p_pcode, @p_sbuid, @p_datefrom, @p_dateto)",con);
				
					SqlParameter p_pcode=new SqlParameter("@p_pcode",SqlDbType.Char,10); p_pcode.Value=pcode;
					SqlParameter p_sbuid=new SqlParameter("@p_sbuid",SqlDbType.Int); p_sbuid.Value=lblSBUID.Text;
					SqlParameter p_datefrom=new SqlParameter("@p_datefrom",SqlDbType.SmallDateTime); p_datefrom.Value=dpFrom.SelectedDate.ToShortDateString();

					SqlParameter p_dateto=new SqlParameter("@p_dateto",SqlDbType.SmallDateTime);

					if (CheckBox1.Checked)
					{
						p_dateto.Value=dpTo.SelectedDate.ToShortDateString();
					}
					else
					{
						p_dateto.Value=DBNull.Value;
					}

					cmd.Parameters.Add(p_datefrom);
					cmd.Parameters.Add(p_dateto);
					cmd.Parameters.Add(p_pcode);
					cmd.Parameters.Add(p_sbuid);
					cmd.ExecuteNonQuery();
				
					UpdateGrid();
					btnChange.Enabled=true;
					Panel1.Visible=false;
					UpdateEditColoumn();
					DataGrid2.Visible=true;
					Panel1.Visible=false;
				}
			}
			else if (cmdSave.Text=="Update")
			{
				string dateTo=" NULL ";
				if(CheckBox1.Checked==true)
				{
					dateTo="'"+dpTo.SelectedDate.ToShortDateString()+"'";
				}
				SqlCommand cmd = new SqlCommand("update t_sbuheads  set pcode='" + ddlHOD.SelectedValue.ToString() + "', datefrom='" + dpFrom.SelectedDate.ToShortDateString() + "', dateto=" + dateTo + " where sbuheadid="+lblSbuHeadID.Text,con);
				cmd.ExecuteNonQuery();
				cmdSave.Text="Save";
				UpdateGrid();
				UpdateEditColoumn();
				Panel12.Visible=true;
				Panel1.Visible=false;
				btnChange.Enabled=true;
				DataGrid2.Visible=true;
			}

		}

		private void CheckBox1_CheckedChanged(object sender, System.EventArgs e)
		{
			dpTo.Visible=CheckBox1.Checked;
			UpdateEditColoumn();
		}

		private void DataGrid2_EditCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{
			lblSbuHeadID.Text=DataGrid2.DataKeys[e.Item.ItemIndex].ToString();
			SqlCommand cmd = new SqlCommand("select * from t_sbuheads where sbuheadid="+lblSbuHeadID.Text,con);
			SqlDataReader dr= cmd.ExecuteReader();
			dr.Read();
			
			if (dr.HasRows)
			{
				cmdSave.Text="Update";
				dpFrom.SelectedDate=DateTime.Parse(dr["DateFrom"].ToString());
		
				ddlHOD.SelectedValue=dr["pcode"].ToString();
				
				if (dr["dateto"].ToString()=="")
				{
					CheckBox1.Checked=false;
					dpTo.Visible=false;
				}
				else
				{
					CheckBox1.Checked=true;
					dpTo.SelectedDate=DateTime.Parse(dr["dateTo"].ToString());
					dpTo.Visible=true;
				}
				dr.Close();
				Panel1.Visible=true;
				UpdateEditColoumn();
			}
			else
			{
				dr.Close();
			}

		}

	}
}
