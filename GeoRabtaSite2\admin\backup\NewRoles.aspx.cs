using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;
namespace GeoRabtaSite.admin
{
	/// <summary>
	/// Summary description for NewRoles.
	/// </summary>
	public class NewRoles : System.Web.UI.Page
	{
		protected System.Web.UI.WebControls.DropDownList ddlProjects;
		protected System.Web.UI.WebControls.DropDownList ddlWebForms;
		protected System.Web.UI.WebControls.Button cmdUpdate;
		protected System.Web.UI.WebControls.DataGrid DataGrid1;
		protected SqlConnection conn;
		protected System.Web.UI.WebControls.TextBox TextBox1;
		static ArrayList al=new ArrayList();
		private void Page_Load(object sender, System.EventArgs e)
		{
			// Put user code to initialize the page here

			conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();

			if (!IsPostBack)
			{
				cmdUpdate.Attributes.Add("onclick","CheckSelected(); return true;");
				FillProjectsDDL();
			}
			else
			{

			}
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.ddlProjects.SelectedIndexChanged += new System.EventHandler(this.ddlProjects_SelectedIndexChanged);
			this.ddlWebForms.SelectedIndexChanged += new System.EventHandler(this.ddlWebForms_SelectedIndexChanged);
			this.cmdUpdate.Click += new System.EventHandler(this.cmdUpdate_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		private void FillProjectsDDL()
		{
			SqlCommand cmd = new SqlCommand("SELECT     projectID, projectName FROM         dbo.t_Projects WHERE     (isActive = 1)",conn);
			SqlDataReader dr = cmd.ExecuteReader();
			ddlProjects.Items.Clear();
			ddlProjects.Items.Add("");
			while (dr.Read())
			{
				ddlProjects.Items.Add(new ListItem(dr[1].ToString(), dr[0].ToString()));
			}
			dr.Close();
		}


		private void ddlProjects_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			if (ddlProjects.SelectedIndex>0)
			{
				SqlCommand cmd = new SqlCommand("SELECT WebFormID, webFormName, displayName FROM dbo.t_webForms WHERE (isActive = 1) AND (projectID = " + ddlProjects.SelectedValue + ") order by webFormName",conn);
				SqlDataReader dr = cmd.ExecuteReader();
				ddlWebForms.Items.Clear();
				ddlWebForms.Items.Add("");
				while (dr.Read())
				{
					ddlWebForms.Items.Add(new ListItem(dr["displayName"].ToString(), dr[0].ToString()));
				}
				dr.Close();
			}
		}

		private void cmdUpdate_Click(object sender, System.EventArgs e)
		{
			
			
			
			string [] selected = TextBox1.Text.Split(',');
			int i=0;
			string item="";

			SqlCommand cmdInsertRoleWiseWebForms=null;
			SqlCommand cmdDeleteRoleWiseControls = null;

			SqlTransaction myTrans;
			myTrans = conn.BeginTransaction();

			for(i=0;i<al.Count;i++)
			{
				ListItem RoleList = (ListItem)al[i];
				try
				{
					cmdInsertRoleWiseWebForms = new SqlCommand("INSERT INTO dbo.t_RoleWiseWebForms (roleID, webFormID, ProjectID) VALUES (" + RoleList.Value + ", " + ddlWebForms.SelectedValue + ", " + ddlProjects.SelectedValue + ")",conn);
					cmdInsertRoleWiseWebForms.Transaction=myTrans;
					int j = cmdInsertRoleWiseWebForms.ExecuteNonQuery();
					int k =j;
				}
				catch (SqlException ex)
				{
					string s=ex.Message;
				}

				try
				{
					cmdDeleteRoleWiseControls = new SqlCommand("DELETE FROM dbo.t_RoleWiseControls " +
						" WHERE (roleID = "+ RoleList.Value +") AND (WebFormID = " + ddlWebForms.SelectedValue + ") AND (ProjectID = " + ddlProjects.SelectedValue + ") ",conn);
					cmdDeleteRoleWiseControls.Transaction=myTrans;
					int j = cmdDeleteRoleWiseControls.ExecuteNonQuery();
					int k = j;
				}
				catch (SqlException ex)
				{
					string s=ex.Message;
				}
			}

			SqlCommand cmdInsertRolWiseControls = null;
			for (i=0;i<(selected.Length-1);i++)
			{
				item=selected[i];
				string [] tag = item.Split(':');
				int row=int.Parse(tag[0])-2;
				string roleName=tag[1];
				//Response.Write("Function is "+DataGrid1.Items[row].Cells[1].Text+"<br>"+"Role Name is "+roleName+"<br>");
				string ControlName = DataGrid1.Items[row].Cells[0].Text;

				try
				{
					
					cmdInsertRolWiseControls = new SqlCommand("INSERT INTO dbo.t_RoleWiseControls " +
                        " (ControlName, roleID, WebFormID, ProjectID, isVisible, isEnable) " +
                        " VALUES ('" + ControlName + "', " + GetRoleID(roleName) + ", " + ddlWebForms.SelectedValue + ", " + ddlProjects.SelectedValue + ", 1,1) ",conn);
					cmdInsertRolWiseControls.Transaction=myTrans;
					int l = cmdInsertRolWiseControls.ExecuteNonQuery();
					int k = l;

				}
				catch (SqlException ex)
				{
					string s=ex.Message;
				}


			}
			myTrans.Commit();
			FillGrid();
			
		}

		string  GetRoleID(string roleName)
		{
			int i=0;
			string rValue="";
			bool found=false;
			for (i=0;i<al.Count;i++)
			{
				ListItem RoleList = (ListItem)al[i];
				if (roleName==RoleList.Text)
				{
					//return int.Parse(RoleList.Value);
					rValue=RoleList.Value.ToString();
					found=true;
					break;
				}
			}
			if (found)
			{
				return rValue;
			}
			else
			{
				return "";
			}
		}

		private void UpdateGrid()
		{
			int i=0;
			int j=0;
			string controlName="";
			string roleID="";
			string projectID=ddlProjects.SelectedValue;
			string webformID=ddlWebForms.SelectedValue;
			for(i=0;i<al.Count;i++)
			{
				foreach (DataGridItem di in DataGrid1.Items)
				{
					controlName = di.Cells[0].Text;
					ListItem li = (ListItem)al[i];
					roleID = li.Value.ToString();
					SqlCommand cmd = new SqlCommand("SELECT isVisible FROM dbo.t_RoleWiseControls " + 
						" WHERE (ControlName = '" + controlName  + "') AND (roleID = " + roleID + ") AND (WebFormID = " + webformID + ") AND (ProjectID = " + projectID + ")",conn);
					SqlDataReader dr = cmd.ExecuteReader();
					dr.Read();
					CheckBox cb = (CheckBox)di.FindControl(li.Text);
					if (dr.HasRows)
					{
						if (dr["isVisible"].ToString()=="1")
						{
							cb.Checked=true;     
						}
						else
						{
							cb.Checked=false;
						}
						
					}
					else
					{
						cb.Checked=false;
					}
					dr.Close();
				}
			}

		}


		private void FillGrid()
		{
			if (ddlWebForms.SelectedIndex>0)
			{
				string sql="SELECT controlID, controlName, DisplayName, isActive, projectID, webFormID " +
					" FROM dbo.t_controls " +
					" WHERE (isActive = 1) AND (projectID = " + ddlProjects.SelectedValue + ") AND (webFormID = " + ddlWebForms.SelectedValue + ") ";
				SqlDataAdapter da = new SqlDataAdapter(sql,conn);
				DataSet ds = new DataSet();
				da.Fill(ds,"t_controls");

				DataGrid1.DataSource=ds.Tables["t_controls"].DefaultView;

				TemplateColumn tempCol=null;
				SqlCommand cmd = new SqlCommand("SELECT roleID, roleName FROM t_Roles WHERE (isActive = 1) ORDER BY roleID",conn);
				SqlDataReader dr=cmd.ExecuteReader();
				al.Clear();
				while (dr.Read())
				{
					al.Add(new ListItem(dr[1].ToString(),dr[0].ToString()));
					tempCol = new TemplateColumn();
					tempCol.HeaderTemplate = new DataGridTemplate(ListItemType.Header, dr[1].ToString(), 1);
					tempCol.ItemTemplate = new DataGridTemplate(ListItemType.Item, dr[1].ToString(), 1);
					tempCol.EditItemTemplate = new DataGridTemplate(ListItemType.EditItem, dr[1].ToString(),1);
					tempCol.FooterTemplate = new DataGridTemplate(ListItemType.Footer, dr[1].ToString(), 1);
					tempCol.ItemStyle.Width=new Unit(80);
					DataGrid1.Columns.Add(tempCol);
				}

				dr.Close();

				//				TemplateColumn temcol1 = new TemplateColumn();
				//				TemplateColumn temcol2 = new TemplateColumn();
				//				
				//				
				//				temcol1.HeaderTemplate = new DataGridTemplate(ListItemType.Header, "Employee", 1);
				//				temcol1.ItemTemplate = new DataGridTemplate(ListItemType.Item, "Employee", 1);
				//				temcol1.EditItemTemplate = new DataGridTemplate(ListItemType.EditItem, "Employee",1);
				//				temcol1.FooterTemplate = new DataGridTemplate(ListItemType.Footer, "Employee", 1);
				//
				//				temcol2.HeaderTemplate = new DataGridTemplate(ListItemType.Header, "GM HR", 0);
				//				temcol2.ItemTemplate = new DataGridTemplate(ListItemType.Item, "GM HR", 0);
				//				temcol2.EditItemTemplate = new DataGridTemplate(ListItemType.EditItem, "GM HR",0);
				//				temcol2.FooterTemplate = new DataGridTemplate(ListItemType.Footer, "GM HR", 0);
				//				DataGrid1.Columns.Add(temcol1);
				//
				//				DataGrid1.Columns.Add(temcol2);

				DataGrid1.DataBind();


				UpdateGrid();

			}

		}

		private void ddlWebForms_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			FillGrid();
			UpdateGrid();
		}

		private void cmdCheck_Click(object sender, System.EventArgs e)
		{
//			string [] t = TextBox1.Text.Split(",");
//			for(int i=0;i<(t.Length-1);i++)
//			{
//				string item=t[i].Trim();
//				string ele=item.Split(":");
//				int col=int.Parse(ele[1]);
//				int row=int.Parse(ele[0])-2;
//			}

		}

		private void Button1_Click(object sender, System.EventArgs e)
		{
			TemplateColumn temcol1 = new TemplateColumn();
			temcol1.HeaderTemplate = new DataGridTemplate(ListItemType.Header, "Employee", 1);
			temcol1.ItemTemplate = new DataGridTemplate(ListItemType.Item, "Employee", 1);
			temcol1.EditItemTemplate = new DataGridTemplate(ListItemType.EditItem, "Employee",1);
			temcol1.FooterTemplate = new DataGridTemplate(ListItemType.Footer, "Employee", 1);
			DataGrid1.Columns.Add(temcol1);
		}
	}
}
