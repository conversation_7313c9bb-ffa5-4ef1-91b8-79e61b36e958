using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for GeoIdeas.
	/// </summary>
	public class ManageSpecialist : System.Web.UI.Page
	{
		SqlConnection con;
		protected System.Web.UI.HtmlControls.HtmlTable Table2;
		protected System.Web.UI.HtmlControls.HtmlTable tdFrom2;	// 
		string userId="";
		private bool _refreshState;
		protected System.Web.UI.WebControls.CheckBoxList chkDesignation;
		protected System.Web.UI.WebControls.LinkButton btnDesignationAll;
		protected System.Web.UI.WebControls.Label Label19;
		protected System.Web.UI.WebControls.Panel Panel4;
		protected System.Web.UI.WebControls.LinkButton lnkAddFD;
		protected System.Web.UI.WebControls.CheckBoxList chkFunctionalDesignation;
		protected System.Web.UI.WebControls.LinkButton btnFuncAll;
		protected System.Web.UI.WebControls.Label Label26;
		protected System.Web.UI.WebControls.Panel Panel8;
		protected System.Web.UI.WebControls.Label Label1;
		protected System.Web.UI.WebControls.Panel Panel1;
		protected System.Web.UI.WebControls.Label lblSpecialistNo;
		protected System.Web.UI.WebControls.DropDownList ddDept;
		protected System.Web.UI.WebControls.DropDownList ddTimeslot;
		protected System.Web.UI.WebControls.LinkButton Linkbutton2;
		protected System.Web.UI.WebControls.Label Label2;
		protected System.Web.UI.WebControls.Panel Panel2;
		protected System.Web.UI.WebControls.CheckBoxList chkOwnerList;
		protected System.Web.UI.WebControls.CheckBoxList chkOwnerDept;
		protected System.Web.UI.WebControls.CheckBoxList chkOwnerCat;
		protected System.Web.UI.WebControls.LinkButton Linkbutton3;
		protected System.Web.UI.WebControls.Label Label3;
		protected System.Web.UI.WebControls.Panel Panel5;
		protected System.Web.UI.WebControls.CheckBoxList chkOwnerStation;
		protected System.Web.UI.WebControls.LinkButton Linkbutton4;
		protected System.Web.UI.WebControls.Label Label4;
		protected System.Web.UI.WebControls.Panel Panel6;
		protected System.Web.UI.WebControls.Button btnSave;
		protected System.Web.UI.WebControls.Panel Panel3;
		protected System.Web.UI.WebControls.Label Label18;
		protected System.Web.UI.WebControls.LinkButton btnDepartAll;
		protected System.Web.UI.WebControls.CheckBoxList chkDepartment;
		protected System.Web.UI.WebControls.LinkButton lnkRemFD;
		protected System.Web.UI.WebControls.Button btnNew;
		private bool _isRefresh;

		public bool IsRefresh
		{
			get
			{
				return _isRefresh;
			}
		}

		protected override void LoadViewState(object savedState)
		{
			object[] allStates = (object[]) savedState;
			base.LoadViewState(allStates[0]);
			_refreshState = (bool) allStates[1];
			try
			{
				_isRefresh = _refreshState == (bool) Session["__ISREFRESH"];
			}
			catch(Exception)
			{
				Response.Redirect("../Login.aspx");
			}

		}

		protected override object SaveViewState()
		{
			Session["__ISREFRESH"] = _refreshState;
			object[] allStates = new object[2];
			allStates[0] = base.SaveViewState();
			allStates[1] = !_refreshState;
			return allStates;
		}



		/*private bool IsPageAccessAllowed()
		{


			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception ex)
			{
				string s=ex.Message;
				Response.Redirect("../Login.aspx");
			}
		
			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"View")==true)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("../Login.aspx");
				return false;
			}
		}*/

		public void GetDepartment()
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select deptid,deptname from t_department where status=1 order by deptname",con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
				this.chkDepartment.Items.Add(itm);
				this.ddDept.Items.Add(itm);
				this.chkOwnerDept.Items.Add(itm);
			}
			rd.Close();
			con.Close();
			con.Dispose();
		}

		public void GetDesignations()
		{
			this.chkDesignation.Items.Clear();
			string ID="";
			for(int i=0;i<this.chkDepartment.Items.Count;i++)
			{
				if(this.chkDepartment.Items[i].Selected)
				{
					ID+=this.chkDepartment.Items[i].Value+",";
				}
			}
			if(ID.Length>0)
			{
				ID=ID.Remove(ID.Length-1,1);
			
				con=new SqlConnection(Connection.ConnectionString);
				con.Open();
				SqlCommand cmd=new SqlCommand("select d.desigid,d.designation from t_designation d,t_department dept where dept.deptid=d.deptid and dept.deptid in("+ID+") and d.status=1 order by d.designation",con);
				SqlDataReader rd=cmd.ExecuteReader();
				while(rd.Read())
				{
					ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
					this.chkDesignation.Items.Add(itm);
				}
				rd.Close();
				con.Close();
				con.Dispose();
			}
		}
		public void GetFDesignations()
		{
			this.chkFunctionalDesignation.Items.Clear();
			string ID="";
			for(int i=0;i<this.chkDesignation.Items.Count;i++)
			{
				if(this.chkDesignation.Items[i].Selected)
				{
					ID+=this.chkDesignation.Items[i].Value+",";
				}
			}
			if(ID.Length>0)
			{
				ID=ID.Remove(ID.Length-1,1);
			
				con=new SqlConnection(Connection.ConnectionString);
				con.Open();
				SqlCommand cmd=new SqlCommand("select f.fdesigid,f.functionaltitle from t_functionaldesignation f,t_designation d where d.desigid=f.functionaldesignation and d.desigid in("+ID+") and f.isactive=1 order by f.functionaltitle",con);
				SqlDataReader rd=cmd.ExecuteReader();
				while(rd.Read())
				{
					ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
					this.chkFunctionalDesignation.Items.Add(itm);
				}
				rd.Close();
				con.Close();
				con.Dispose();
			}
			
		}
		public void CopyFD()
		{
			this.chkOwnerList.Items.Clear();
			for(int i=0;i<this.chkFunctionalDesignation.Items.Count;i++)
			{
				if(this.chkFunctionalDesignation.Items[i].Selected)
				{
					ListItem itm=this.chkFunctionalDesignation.Items[i];
					this.chkOwnerList.Items.Add(itm);
					itm.Selected=false;
				}
			}
		}
		public void RemoveFD()
		{
			//System.Collections.ArrayList _list=new ArrayList();
			for(int i=0;i<this.chkOwnerList.Items.Count;)
			{
				if(this.chkOwnerList.Items[i].Selected)
				{
					//_list.Add(i);
					this.chkOwnerList.Items.RemoveAt(i);
					i--;
				}
				i++;
			}
		}
		public void GetCategory()
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select cat_id,cat_name from t_categorization where isactive=1 order by clevel",con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
				this.chkOwnerCat.Items.Add(itm);
			}
			rd.Close();
			con.Close();
			con.Dispose();
		}
		
		public void GetStation()
		{
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select cityid,cityname from t_city where status=1 order by cityname",con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem(rd[1].ToString(),rd[0].ToString());
				this.chkOwnerStation.Items.Add(itm);
			}
			con.Close();
			con.Dispose();
		}

		public string GetSpecialistID(SqlTransaction trans)
		{
			string Val="";
			int newVal=0;
			SqlCommand cmd=new SqlCommand("select specialistno from t_specialist order by specialistno desc",con);
			cmd.Transaction=trans;
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			if(rd.HasRows)
			{
				newVal=Int32.Parse(rd[0].ToString());
				//Val+=newVal.ToString();
			}
			else
			{
				newVal=0;
				//Val+=newVal.ToString();
			}
			rd.Close();
			for(int i=0;i<4-newVal.ToString().Length;i++)
			{
				Val+="0";
			}
			Val+=newVal+1;
			return Val;
		}

		public void ResetSetting()
		{
		 this.chkDesignation.Items.Clear();
		 this.chkFunctionalDesignation.Items.Clear();
		 this.chkOwnerList.Items.Clear();
		 this.ddDept.SelectedIndex=0;
		 this.ddTimeslot.SelectedIndex=0;
		 this.lblSpecialistNo.Text="";
			for(int i=0;i<this.chkDepartment.Items.Count;i++)
			{
				this.chkDepartment.Items[i].Selected=false;
				this.chkOwnerDept.Items[i].Selected=false;
			}
			for(int i=0;i<this.chkOwnerCat.Items.Count;i++)
			{
			 this.chkOwnerCat.Items[i].Selected=false;
			}
			for(int i=0;i<this.chkOwnerStation.Items.Count;i++)
			{
				this.chkOwnerStation.Items[i].Selected=false;
			}
		}
		private void Page_Load(object sender, System.EventArgs e)
		{

			/*Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			if(IsPageAccessAllowed())
			{
			}*/
			if(!IsPostBack)
			{
				GetDepartment();
				GetCategory();
				GetStation();
			}
			
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.chkDepartment.SelectedIndexChanged += new System.EventHandler(this.chkDepartment_SelectedIndexChanged);
			this.chkDesignation.SelectedIndexChanged += new System.EventHandler(this.chkDesignation_SelectedIndexChanged);
			this.lnkAddFD.Click += new System.EventHandler(this.lnkAddFD_Click);
			this.lnkRemFD.Click += new System.EventHandler(this.lnkRemFD_Click);
			this.btnNew.Click += new System.EventHandler(this.btnNew_Click);
			this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		private void chkDepartment_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			GetDesignations();
			GetFDesignations();
		}

		private void chkDesignation_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			GetFDesignations();
		}

		private void lnkAddFD_Click(object sender, System.EventArgs e)
		{
			CopyFD();
		}

		private void lnkRemFD_Click(object sender, System.EventArgs e)
		{
			RemoveFD();
		}

		private void btnSave_Click(object sender, System.EventArgs e)
		{
			
			con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlTransaction trans=con.BeginTransaction();
			try
			{
				
				SqlCommand cmd=new SqlCommand("insert into t_specialist(specialistno,stimeslot,sdepartment,isactive) values(@specialistno,@stimeslot,@sdepartment,@isactive)",con);
				SqlParameter _sno=new SqlParameter("@specialistno",SqlDbType.VarChar);
				SqlParameter _stime=new SqlParameter("@stimeslot",SqlDbType.VarChar);
				SqlParameter _sdept=new SqlParameter("@sdepartment",SqlDbType.Int);
				SqlParameter _isactive=new SqlParameter("@isactive",SqlDbType.Int);
				string Value=GetSpecialistID(trans);
				this.lblSpecialistNo.Text=Value;
				_sno.Value=Value;
				_stime.Value=this.ddTimeslot.SelectedItem.Text.ToString();
				_sdept.Value=this.ddDept.SelectedValue.ToString();
				_isactive.Value=1;
				cmd.Parameters.Add(_sno);
				cmd.Parameters.Add(_stime);
				cmd.Parameters.Add(_sdept);
				cmd.Parameters.Add(_isactive);
				cmd.Transaction=trans;
				int z=cmd.ExecuteNonQuery();
				for(int i=0;i<this.chkOwnerList.Items.Count;i++)
				{
					//if(this.chkOwnerList.Items[i].Selected)
				{
					cmd=new SqlCommand("insert into t_specialistowner (specialistno,s_fdesigid) values(@specialistno,@s_fdesigid)",con);
					SqlParameter _spno=new SqlParameter("@specialistno",SqlDbType.VarChar);
					SqlParameter _fdesigid=new SqlParameter("@s_fdesigid",SqlDbType.Int);
					_spno.Value=Value;
					_fdesigid.Value=this.chkOwnerList.Items[i].Value;
					cmd.Parameters.Add(_spno);
					cmd.Parameters.Add(_fdesigid);
					cmd.Transaction=trans;
					cmd.ExecuteNonQuery();
				}
				}
				for(int i=0;i<this.chkOwnerDept.Items.Count;i++)
				{
					if(this.chkOwnerDept.Items[i].Selected)
					{
						cmd=new SqlCommand("insert into t_specialistdept (s_deptid,specialistno) values(@s_deptid,@specialistno)",con);
						SqlParameter _dept=new SqlParameter("@s_deptid",SqlDbType.Int);
						SqlParameter _spno2=new SqlParameter("@specialistno",SqlDbType.VarChar);
						_dept.Value=this.chkOwnerDept.Items[i].Value;
						_spno2.Value=Value;
						cmd.Parameters.Add(_dept);
						cmd.Parameters.Add(_spno2);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
					}
				}
				for(int i=0;i<this.chkOwnerCat.Items.Count;i++)
				{
					if(this.chkOwnerCat.Items[i].Selected)
					{
						cmd=new SqlCommand("insert into t_specialistcat(s_catid,specialistno) values(@s_catid,@specialistno)",con);
						SqlParameter _cat=new SqlParameter("@s_catid",SqlDbType.Int);
						SqlParameter _spno3=new SqlParameter("@specialistno",SqlDbType.VarChar);
						_spno3.Value=Value;
						_cat.Value=this.chkOwnerCat.Items[i].Value;
						cmd.Parameters.Add(_spno3);
						cmd.Parameters.Add(_cat);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
					}
				}
				for(int i=0;i<this.chkOwnerStation.Items.Count;i++)
				{
					if(this.chkOwnerStation.Items[i].Selected)
					{
						cmd=new SqlCommand("insert into t_specialiststation(s_stationid,specialistno) values(@s_stationid,@specialistno)",con);
						SqlParameter _station=new SqlParameter("@s_stationid",SqlDbType.Int);
						SqlParameter _spno4=new SqlParameter("@specialistno",SqlDbType.VarChar);
						_spno4.Value=Value;
						_station.Value=this.chkOwnerStation.Items[i].Value;
						cmd.Parameters.Add(_spno4);
						cmd.Parameters.Add(_station);
						cmd.Transaction=trans;
						cmd.ExecuteNonQuery();
					}
				}
				trans.Commit();
				con.Close();
				con.Dispose();
			}
			catch(Exception ex)
			{
				trans.Rollback();
				Response.Write(ex.Message);
			}
			finally
			{
			 con.Close();
			 con.Dispose();
			}
			
		}

		private void btnNew_Click(object sender, System.EventArgs e)
		{
			ResetSetting();
		}
		
	

	}
}
