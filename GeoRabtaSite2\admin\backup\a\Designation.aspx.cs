using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for GeoIdeas.
	/// </summary>
	public class GeoDesignation : System.Web.UI.Page
	{
		private bool _refreshState;
		private bool _isRefresh;

		public bool IsRefresh
		{
			get
			{
				return _isRefresh;
			}
		}
		SqlConnection con; 
		private static DataSet ds;
		public static string sort="";
		protected System.Web.UI.WebControls.Label Label6;
		protected System.Web.UI.WebControls.DataGrid DataGrid1;
		protected System.Web.UI.WebControls.Button btnCancel;
		protected System.Web.UI.WebControls.Button btnFunctionalDesignation;
		protected System.Web.UI.WebControls.Button btnQueryButton;
		protected System.Web.UI.WebControls.Label Label4;
		protected System.Web.UI.WebControls.DataGrid DataGrid2;
		protected System.Web.UI.WebControls.TextBox txtVacancies;
		protected System.Web.UI.WebControls.Label Label7;
		protected System.Web.UI.WebControls.DropDownList ddCategory;
		protected System.Web.UI.WebControls.Label Label5;
		protected System.Web.UI.WebControls.DropDownList dropDownDept;
		protected System.Web.UI.WebControls.Label Label3;
		protected System.Web.UI.WebControls.TextBox txtName;
		protected System.Web.UI.WebControls.Label Label2;
		protected System.Web.UI.WebControls.Label Label1;
		protected System.Web.UI.HtmlControls.HtmlTableCell currentDesignation;
		protected System.Web.UI.WebControls.Label lblTitle;
		protected System.Web.UI.WebControls.TextBox txtTitle;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator1;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator2;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator3;
		protected System.Web.UI.WebControls.DropDownList ddStation;
		protected System.Web.UI.WebControls.TextBox txtJobDescription;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator4;
		protected System.Web.UI.WebControls.Label Label8;
		protected System.Web.UI.WebControls.DataGrid DataGrid3;
		protected System.Web.UI.WebControls.Label Label9;
		protected System.Web.UI.WebControls.Label lblVacancy;
		protected System.Web.UI.WebControls.Button btnAssign;
		protected System.Web.UI.WebControls.Button Button1;
		protected System.Web.UI.WebControls.Button btnSave;
		protected System.Web.UI.WebControls.TextBox txtSalaryRangeMin;
		protected System.Web.UI.WebControls.TextBox txtNoOfVacancy;
		public static string sortVar="";
		protected System.Web.UI.WebControls.DropDownList ddMdept;
		public Guid guid=new Guid();
		public int WebFormID=29;
		public int ProjectID=2;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator5;
		protected System.Web.UI.WebControls.RequiredFieldValidator RequiredFieldValidator6;
		protected System.Web.UI.WebControls.Label Label11;
		protected System.Web.UI.WebControls.Label lblMsg;
		protected System.Web.UI.WebControls.RangeValidator RangeValidator1;
		protected System.Web.UI.WebControls.TextBox txtSalaryRangeMax;
		protected System.Web.UI.WebControls.CompareValidator CompareValidator1;
		protected System.Web.UI.WebControls.CustomValidator CustomValidator1;
		protected System.Web.UI.WebControls.RangeValidator RangeValidator2;
		protected System.Web.UI.WebControls.Label lblTitleMsg;
		public string userId="";
		protected System.Web.UI.WebControls.Label lblMesg;
		protected System.Web.UI.WebControls.Panel Panel1;
		protected System.Web.UI.WebControls.TextBox txtDesId;
		protected System.Web.UI.HtmlControls.HtmlTable CurrentFuncDesignation;
		protected System.Web.UI.HtmlControls.HtmlTable myRow;
		protected System.Web.UI.HtmlControls.HtmlTable myCurrentDes;
		protected System.Web.UI.WebControls.Button btnRemove;
		protected System.Web.UI.WebControls.Label Label10;
		protected System.Web.UI.WebControls.DataGrid DataGrid4;
		protected System.Web.UI.WebControls.Button btnSclose;
		protected System.Web.UI.WebControls.Button btnsearchAdvance;
		protected System.Web.UI.WebControls.TextBox txtNIC;
		protected System.Web.UI.WebControls.TextBox txtAdName;
		protected System.Web.UI.WebControls.DropDownList ddLocation;
		protected System.Web.UI.WebControls.DropDownList ddEmployeeName;
		protected System.Web.UI.WebControls.Panel Panel2;
		protected System.Web.UI.HtmlControls.HtmlTable FunctionalDesignation;
		protected System.Web.UI.HtmlControls.HtmlTable myRowAssign;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator1;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator2;
		protected System.Web.UI.WebControls.Label lblError;
		protected System.Web.UI.WebControls.LinkButton lnkNewDesignation;
		protected System.Web.UI.WebControls.Label lblNewDes;
		protected System.Web.UI.WebControls.TextBox txttest;
		protected System.Web.UI.WebControls.TextBox txtDate;
		protected System.Web.UI.WebControls.TextBox txtPrev;
		protected System.Web.UI.WebControls.RangeValidator RangeValidator3;
		public static string fSort="";
		protected System.Web.UI.WebControls.CustomValidator CustomValidator2;
		protected System.Web.UI.WebControls.TextBox txtX;
		protected System.Web.UI.WebControls.TextBox txtY;
		protected System.Web.UI.WebControls.RegularExpressionValidator RegularExpressionValidator3;
		public static int count=0;
		private bool IsPageAccessAllowed()
		{
			
			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception ex)
			{
				string s=ex.Message;
				Response.Redirect("../Login.aspx");
			}

			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"View")==true)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("../Login.aspx");
				return false;
			}
		}

		private void Page_Load(object sender, System.EventArgs e)
		{
		
			this.Label6.Visible=false;
			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			if(IsPageAccessAllowed())
			{
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"View")==true)
				{
				 this.DataGrid1.Columns[1].Visible=false;
                 this.btnQueryButton.Enabled=false;
                 this.DataGrid2.Columns[4].Visible=false;
                 //btnSave.Enabled=false;
                 }
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Delete")==true)
				{
				 this.DataGrid1.Columns[1].Visible=true;
				}
			
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Add")==true)
				{
				   this.btnQueryButton.Enabled=true; 
				}
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Add FD")==true)
				{
				 this.DataGrid2.Columns[3].Visible=true;
				 this.DataGrid2.Columns[4].Visible=true;
				}
			}
			else
			{
				Response.Redirect("ErrorPage.aspx");
			}
			con=new SqlConnection(Connection.ConnectionString);
			
			//Page.SmartNavigation=true;
			this.DataGrid1.Columns[0].HeaderText="Modify";
			this.DataGrid1.Columns[1].HeaderText="Delete";
			if(!IsPostBack)
			{
				count=0;
				guid=Guid.Empty;
				Session["tempId"]="";
				Session["tempDept"]="";
				Session["pCode"]="";
				Session["fDesignation"]="";
				con.Open();	
				getDept();
				getCategory();
				getStation();
				con.Close();
				BindDesignation();
				this.myRow.Visible=false;
				this.FunctionalDesignation.Visible=false;
				this.myRowAssign.Visible=false;
				btnRemove.Attributes.Add("onclick","getPrevDate();");
				txtName.Attributes.Add("onkeyup","return GetDesignations(event);");
				txtName.Attributes.Add("onkeypress","return disableEnterKey(event);");
				//txtName.Attributes.Add("onblur","return CloseIt2();");
				txtTitle.Attributes.Add("onkeyup","return GetFD(event);");
				txtTitle.Attributes.Add("onkeypress","return disableEnterKey(event);");

				
				//this.CurrentFuncDesignation.Visible=false;
			}
			/*if(this.btnQueryButton.Text=="Save" || this.btnQueryButton.Text=="Edit")
			{
				this.btnQueryButton.CausesValidation=true;
				RequiredFieldValidator6.Validate();
				RequiredFieldValidator5.Validate();
				//Page.Validate();
			}
			else
			{
			this.btnQueryButton.CausesValidation=false;
			}*/
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.dropDownDept.SelectedIndexChanged += new System.EventHandler(this.dropDownDept_SelectedIndexChanged);
			this.CustomValidator2.ServerValidate += new System.Web.UI.WebControls.ServerValidateEventHandler(this.CustomValidator2_ServerValidate);
			this.btnQueryButton.Click += new System.EventHandler(this.btnQueryButton_Click);
			this.btnFunctionalDesignation.Click += new System.EventHandler(this.btnFunctionalDesignation_Click);
			this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
			this.lnkNewDesignation.Click += new System.EventHandler(this.lnkNewDesignation_Click);
			this.DataGrid2.SortCommand += new System.Web.UI.WebControls.DataGridSortCommandEventHandler(this.DataGrid2_SortCommand);
			this.DataGrid2.SelectedIndexChanged += new System.EventHandler(this.DataGrid2_SelectedIndexChanged);
			this.ddMdept.SelectedIndexChanged += new System.EventHandler(this.ddMdept_SelectedIndexChanged);
			this.DataGrid1.EditCommand += new System.Web.UI.WebControls.DataGridCommandEventHandler(this.DataGrid1_EditCommand);
			this.DataGrid1.SortCommand += new System.Web.UI.WebControls.DataGridSortCommandEventHandler(this.DataGrid1_SortCommand);
			this.txtTitle.TextChanged += new System.EventHandler(this.txtTitle_TextChanged);
			this.CustomValidator1.ServerValidate += new System.Web.UI.WebControls.ServerValidateEventHandler(this.CustomValidator1_ServerValidate_1);
			this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
			this.Button1.Click += new System.EventHandler(this.Button1_Click);
			this.btnAssign.Click += new System.EventHandler(this.btnAssign_Click);
			this.btnsearchAdvance.Click += new System.EventHandler(this.btnsearchAdvance_Click);
			this.btnSclose.Click += new System.EventHandler(this.btnSclose_Click);
			this.DataGrid4.SortCommand += new System.Web.UI.WebControls.DataGridSortCommandEventHandler(this.DataGrid4_SortCommand);
			this.btnRemove.Click += new System.EventHandler(this.btnRemove_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		public void BindGrid()
		{
			string str="select d.desigid as Id, d.designation as Designation,dept.deptname as Department,c.cat_name Category,dept.deptid from t_designation d,t_department dept,t_categorization c where d.status='"+1+"' And dept.status='"+1+"' And d.deptid=dept.deptid And c.cat_id=d.category And c.isactive='"+1+"' And dept.deptid='"+this.ddMdept.SelectedValue.ToString()+"'";
			SqlDataAdapter ord=new SqlDataAdapter(str,con);
			ds=new DataSet();
			ds.Clear();
			ord.Fill(ds,"designation");
			DataView dv=new DataView(ds.Tables["designation"]);
			this.DataGrid1.DataSource=dv;
			dv.Sort=sort;
			this.DataGrid1.DataBind();
            //dv.RowFilter="DesigName like'*Manager*'";
            //ds.Tables["designation"].DefaultView; 
			if(this.DataGrid1.Items.Count<=0)
			{
				this.Label6.Visible=true;
			}
			else
			{
			 this.Label6.Visible=false;
			}
			clickEvents();
			if(Session["isAdmin"].ToString()!="Yes")
			{
				if(Session["RightsOnDept"].ToString().Length >0)
				{
					string []Ids=Session["RightsOnDept"].ToString().Split(',');
					ArrayList list=new ArrayList();
					for(int i=0;i<Ids.Length;i++)
					{
				     list.Add(Ids[i].Trim()); 					 
					}
					if(list.Contains(this.ddMdept.SelectedValue.ToString().Trim()))
					{
						for(int i=0;i<this.DataGrid1.Items.Count;i++)
						{
							LinkButton lnk=(LinkButton)this.DataGrid1.Items[i].FindControl("lnkDelRec");
							lnk.Visible=true;
						}
					}
					else
					{
						for(int i=0;i<this.DataGrid1.Items.Count;i++)
						{
							LinkButton lnk=(LinkButton)this.DataGrid1.Items[i].FindControl("lnkDelRec");
							lnk.Visible=false;
						}
					}
				}
				else
				{
					for(int i=0;i<this.DataGrid1.Items.Count;i++)
					{
				      LinkButton lnk=(LinkButton)this.DataGrid1.Items[i].FindControl("lnkDelRec");
					  lnk.Visible=false;
					}
				}

			}
		}
		public void getDept()
		{
			string str="select deptid,deptname from t_department where status='"+1+"' order by deptname";
			SqlCommand cmd=new SqlCommand(str,con); 
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem lst=new ListItem();
				lst.Value=rd[0].ToString();
				lst.Text=rd[1].ToString();
				this.dropDownDept.Items.Add(lst);
				this.ddMdept.Items.Add(lst);
			}
			rd.Close();
			cmd=new SqlCommand("select roleid from t_usersroles where pcode='"+Session["user_id"].ToString()+"' and isactive=1 and roleid=15",con);
			rd=cmd.ExecuteReader();
			rd.Read();
			if(rd.HasRows)
			{
				rd.Close();
				Session["isAdmin"]="Yes";
			}
			else
			{
				rd.Close();
				string deptIds="";
				string getDeptRights="select deptid from t_bumanagers where pcode='"+Session["user_id"].ToString()+"' And isactive=1";
				cmd=new SqlCommand(getDeptRights,con);
				rd=cmd.ExecuteReader();
				while(rd.Read())
				{
					deptIds+=rd[0].ToString()+",";
				}
				rd.Close();
				if(deptIds.Length > 0)
				{
					deptIds=deptIds.Remove(deptIds.Length-1,1);
					Session["RightsOnDept"]=deptIds;
				}
			}
		}

		private void btnQueryButton_Click(object sender, System.EventArgs e)
		{
			string errorString="";
			    this.lblNewDes.Visible=false;
				this.lblNewDes.Text="";
				lblError.Visible=false;
				lblError.Text="";
			    Session["tempId"]="";
			    Session["tempDept"]="";
				con.Open();
				int tVacancy=-1;
				this.lblMesg.Visible=false;
				if(this.txtVacancies.Text.Length>0)
				{
					tVacancy=Int32.Parse(this.txtVacancies.Text);
				}
				if(this.btnQueryButton.Text=="New Designation")
				{
					this.btnQueryButton.Text="Save";
					this.btnCancel.Enabled=true;
					this.txtDesId.Text="";
					this.txtName.Text="";
					this.ddCategory.SelectedValue="0";
					this.dropDownDept.SelectedValue="0";
					this.txtVacancies.Text="";
					this.ddMdept.Enabled=false;
					this.Label11.Visible=false;
					this.lblMsg.Visible=false;
				}
				else
				{
					
					if(IsValid)
					{
						this.btnQueryButton.CausesValidation=true;
						if(this.btnQueryButton.Text=="Save" && !this.Label1.Visible && tVacancy>0)
						{
							//string verify="select designation from t_designation where status=1 And designation='"+ this.txtName.Text +"'And deptid='"+ this.dropDownDept.SelectedValue.ToString() +"'";
							string verify="select designation from t_designation where status=1 And designation=@designation And deptid=@deptid";
							SqlCommand cVerify=new SqlCommand(verify,con);
							SqlParameter _desig=new SqlParameter("@designation",SqlDbType.VarChar);
							SqlParameter _dept=new SqlParameter("@deptid",SqlDbType.Int);
							_desig.Value=this.txtName.Text;
							_dept.Value=this.dropDownDept.SelectedValue.ToString();
							cVerify.Parameters.Add(_desig);
							cVerify.Parameters.Add(_dept);
							SqlDataReader rVerify=null;
							try
							{
								 rVerify=cVerify.ExecuteReader();
							}
							catch(Exception em)
							{
							 Response.Write("<script>window.alert('"+ em.Message+"')</script>");
							}
							rVerify.Read();
							if(rVerify.HasRows)
							{
								this.lblMsg.Visible=true;
								this.lblMsg.Text="The name of inserting designation have already entered.Please choose other name";
								errorString+=lblMsg.Text;
							}
							else
							{
								rVerify.Close();
								this.lblMsg.Visible=false;
								//string str="insert into t_designation values('"+this.txtName.Text+"','"+Int32.Parse(this.dropDownDept.SelectedItem.Value)+"','"+1+"','"+Convert.ToInt32(this.ddCategory.SelectedValue.ToString())+"','"+this.txtVacancies.Text+"')"; 
								string str="insert into t_designation(designation,deptid,status,category,totalvacancies) values(@designation,@deptid,1,@category,@totalvacancies)";
                                SqlCommand c=new SqlCommand(str,con);
								SqlParameter _designation=new SqlParameter("@designation",SqlDbType.VarChar);
								SqlParameter _deptid=new SqlParameter("@deptid",SqlDbType.Int);
                                SqlParameter _category=new SqlParameter("@category",SqlDbType.Int);
								SqlParameter _totalvacancies=new SqlParameter("@totalvacancies",SqlDbType.Int);
                                _designation.Value=this.txtName.Text;
                                _deptid.Value=this.dropDownDept.SelectedItem.Value.ToString();
								_category.Value=this.ddCategory.SelectedValue.ToString();
                                _totalvacancies.Value=this.txtVacancies.Text;
								c.Parameters.Add(_designation);
								c.Parameters.Add(_deptid);
								c.Parameters.Add(_category);
								c.Parameters.Add(_totalvacancies);
								int i=c.ExecuteNonQuery();
								if(i>0)
								{
									this.ddMdept.Enabled=true;
									this.btnFunctionalDesignation.Enabled=false;
									this.btnQueryButton.Text="New Designation";
									this.lblNewDes.Visible=true;
									this.lblNewDes.Text="New Designation has been added successfully";
								}
								BindGrid();
								Session["tempDept"]="";
								Session["tempId"]="";
								Label11.Visible=false;
							}
						}
						if(tVacancy ==0)
						{
							Label11.Visible=true;
						}
						else
						{
							Label11.Visible=false;
						}
						if(this.btnQueryButton.Text=="Edit" && tVacancy>0)
						{
							string verify="select designation from t_designation where status=1 And designation=@designation And deptid=@deptid And desigid not in('"+ this.txtDesId.Text +"')";
							SqlCommand cVerify=new SqlCommand(verify,con);
							SqlParameter _desig=new SqlParameter("@designation",SqlDbType.VarChar);
							SqlParameter _dept=new SqlParameter("@deptid",SqlDbType.Int);
							_desig.Value=this.txtName.Text;
							_dept.Value=this.dropDownDept.SelectedValue.ToString();
							cVerify.Parameters.Add(_desig);
							cVerify.Parameters.Add(_dept);
							SqlDataReader rVerify=null;
							try
							{
								rVerify=cVerify.ExecuteReader();
							}
							catch(Exception z)
							{
							 Response.Write(z.Message);
							}
							rVerify.Read();
							if(rVerify.HasRows)
							{
								this.lblMsg.Visible=true;
								this.lblMsg.Text="The name of inserting designation have already entered.Please choose other name";
								errorString+=lblMsg.Text;
							}
							else
							{
								this.lblMsg.Visible=false;
								rVerify.Close(); 
								this.btnQueryButton.CausesValidation=true;

								int sVac=0;
								int cVac=Int32.Parse(this.txtVacancies.Text);
								string cal="select sum(noofvacancies) from t_functionaldesignation where functionaldesignation='"+ this.txtDesId.Text +"' And isactive=1";
								SqlCommand c =new SqlCommand(cal,con);
								SqlDataReader rc=c.ExecuteReader();
								rc.Read();
								if(rc.HasRows)
								{
									if(!rc.IsDBNull(0))
									{
										sVac=rc.GetInt32(0);
									}
								}
								if(sVac>0)
								{
									sVac=cVac-sVac;
								}
								rc.Close();
								if(sVac>=0)
								{
									string str="update t_designation set designation=@designation,deptid=@deptid,status=1,category=@category,totalvacancies=@totalvacancies where desigid='"+Int32.Parse(this.txtDesId.Text)+"'"; 
									c=new SqlCommand(str,con);
									SqlParameter _designation=new SqlParameter("@designation",SqlDbType.VarChar);
									SqlParameter _deptid=new SqlParameter("@deptid",SqlDbType.Int);
									SqlParameter _category=new SqlParameter("@category",SqlDbType.Int);
									SqlParameter _totalvacancies=new SqlParameter("@totalvacancies",SqlDbType.Int);
									_designation.Value=this.txtName.Text;
									_deptid.Value=this.dropDownDept.SelectedItem.Value.ToString();
									_category.Value=this.ddCategory.SelectedValue.ToString();
									_totalvacancies.Value=this.txtVacancies.Text;
									c.Parameters.Add(_designation);
									c.Parameters.Add(_deptid);
									c.Parameters.Add(_category);
									c.Parameters.Add(_totalvacancies);
									int i=c.ExecuteNonQuery();
									if(i>0)
									{
										this.lblNewDes.Visible=true;
										this.lblNewDes.Text="Designation "+this.txtName.Text+" has been edit successfully";
									}
									BindGrid();
									if(!this.btnQueryButton.CausesValidation)
									{
										this.btnQueryButton.Text="New Designation";
										this.btnCancel.Enabled=false;
										this.txtDesId.Enabled=true;
										this.Label1.Visible=false;
										this.txtDesId.Visible=false;
										this.txtDesId.Text="";
										this.txtName.Text="";
										this.txtVacancies.Text="";
										this.ddCategory.SelectedValue="0";
										this.dropDownDept.SelectedValue="0";
										this.DataGrid2.Visible=false;
										Label11.Visible=false;
										this.lblMsg.Visible=false;
									}
								}
								else
								{
									this.lblMsg.Visible=true;
									this.lblMsg.Text="Total Vacancies are not sufficient because no.of functional designation vacancies on this designation have exceed the limit";
									errorString+=lblMsg.Text; 
								}
							}
						}

					}
					else
					{
					 errorString+=this.CustomValidator2.ErrorMessage;
					}
				}
				con.Close();
//			else
//			{
//			 errorString+=this.CustomValidator2.ErrorMessage;
//			}
			if(errorString.Length>0)
			{
			 Response.Write("<script>window.alert('"+ errorString +"');</script>");
			}
		}

		private void btnCancel_Click(object sender, System.EventArgs e)
		{
			this.lblNewDes.Visible=false;
			this.lblNewDes.Text="";
            lblError.Visible=false;
			lblError.Text="";
			this.lblMesg.Visible=false;
			//this.btnQueryButton.CausesValidation=false;
			this.btnQueryButton.Text="New Designation";
			this.btnCancel.Enabled=false;
			this.txtDesId.Text="";
			this.txtName.Text="";
			this.Label1.Visible=false;
			this.txtDesId.Visible=false;
			this.ddCategory.SelectedValue="0";
			this.dropDownDept.SelectedValue="0";
			this.btnFunctionalDesignation.Enabled=false;
			this.txtVacancies.Text="";
			this.DataGrid2.Visible=false;
			this.ddMdept.Enabled=true;
			this.Label11.Visible=false;
			this.lblMsg.Visible=false;
			this.myRow.Visible=false;
			this.lnkNewDesignation.Visible=true;
		}

		private void DataGrid1_EditCommand(object source, System.Web.UI.WebControls.DataGridCommandEventArgs e)
		{
			this.lblNewDes.Visible=false;
			this.lblNewDes.Text="";
			lblError.Visible=false;
			lblError.Text="";
			con.Open();
			this.btnQueryButton.Text="Edit";
			this.btnCancel.Enabled=true;
			this.txtDesId.Enabled=false;
			this.Label1.Visible=true;
			this.txtDesId.Visible=true;
            this.btnFunctionalDesignation.Enabled=true; 
            this.CurrentFuncDesignation.Visible=true;
			this.ddMdept.Enabled=false;
			myRow.Visible=true;
			this.lnkNewDesignation.Visible=false;

			string str="select desigid as DesignationId,designation as DesigName,deptid as Department,category,totalvacancies from t_designation where desigid=@Id";
			SqlCommand cmd=new SqlCommand(str,con);
			cmd.Parameters.Add(new SqlParameter("@Id",SqlDbType.VarChar,20));
			cmd.Parameters["@Id"].Value=this.DataGrid1.DataKeys[e.Item.ItemIndex];
			Session["tempDept"]=this.DataGrid1.DataKeys[e.Item.ItemIndex].ToString();
			SqlDataReader rd=cmd.ExecuteReader();
			string desId="";
			string name="";
			string dept="";
			string vacancy="";
			while(rd.Read())
			{
				desId=rd[0].ToString(); 
				name=rd[1].ToString();
				dept=rd[2].ToString();
				this.ddCategory.SelectedValue=rd[3].ToString();
                vacancy=rd[4].ToString(); 
			}
			this.txtDesId.Text=desId;
			this.txtName.Text=name;
			this.txtVacancies.Text=vacancy;
			for(int i=0;i<this.dropDownDept.Items.Count;i++)
			{
				if(dept==this.dropDownDept.Items[i].Value)
				{
					this.dropDownDept.SelectedValue=dept;
				}
			}
			con.Close();
			//tempId=this.txtDesId.Text;
			getFunctionalDes(this.DataGrid1.DataKeys[e.Item.ItemIndex].ToString());
			/*if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Edit")==true && this.btnQueryButton.Text=="Save")
			{
				this.btnQueryButton.Enabled=true;
			}*/
			if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Edit")==true && this.btnQueryButton.Text=="Edit")
			{
				this.btnQueryButton.Enabled=true;
			}
			if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Add FD")==false)
			{
			 	this.btnFunctionalDesignation.Enabled=false;		
			}
			clickEvent(true);
			LinkButton lnk=(LinkButton)this.DataGrid1.Items[0].FindControl("lnkDelRec");
			if(!lnk.Visible)
			{
				for(int i=0;i<this.DataGrid2.Items.Count;i++)
				{
				 LinkButton lnr=(LinkButton)this.DataGrid2.Items[i].FindControl("lnkDelete");
				 lnr.Visible=false;
				}
				this.btnQueryButton.Enabled=false;
				this.btnFunctionalDesignation.Enabled=false;
			}
		
		}
          
		public void getCategory()
		{
		 string cat="select cat_id,cat_name from t_categorization where isactive='"+1+"' order by clevel";
         SqlCommand cmd=new SqlCommand(cat,con);
         SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
			 ListItem itm=new ListItem();
             itm.Value=rd[0].ToString();
             itm.Text=rd[1].ToString();
             this.ddCategory.Items.Add(itm);
			}
			rd.Close();
		}

		private void DataGrid1_SortCommand(object source, System.Web.UI.WebControls.DataGridSortCommandEventArgs e)
		{
			sort=e.SortExpression.ToString();
			BindGrid();
		}

		private void btnFunctionalDesignation_Click(object sender, System.EventArgs e)
		{ 
			this.lblNewDes.Visible=false;
			this.lblNewDes.Text="";
			lblError.Visible=false;
			lblError.Text="";
			Session["tempId"]="";
			this.lblMesg.Visible=false;
			this.myRow.Visible=false;
			this.myCurrentDes.Visible=false;
			this.FunctionalDesignation.Visible=true;
			this.lblTitle.Text=this.txtName.Text;
			
			this.txtTitle.Text="";
			this.txtJobDescription.Text="";
			this.txtSalaryRangeMax.Text="";
			this.txtSalaryRangeMin.Text="";
			this.txtNoOfVacancy.Text="";
			this.ddStation.SelectedIndex=0;
			this.btnSave.Text="Save";
			this.btnSave.Enabled=true;
			this.lblVacancy.Visible=false;
			this.Label8.Visible=false;
			this.btnAssign.Enabled=false;
			this.DataGrid3.Visible=false;
		}

		public void getFunctionalDes(string Id)
		{
         count=0;
		 con.Open();
		 string getDes="Select f.fdesigid Id,f.functionaltitle Title,d.designation Designation from t_designation d,t_functionaldesignation f where f.isactive=1 And f.functionaldesignation=d.desigid And f.functionaldesignation='"+Id+"'";
         SqlDataAdapter rd=new SqlDataAdapter(getDes,con);
         DataSet ds=new DataSet();
         ds.Clear();
         rd.Fill(ds,"Records");
         DataView dv=new DataView(ds.Tables["Records"]);
         this.DataGrid2.DataSource=dv;
         dv.Sort=fSort; 
         this.DataGrid2.DataBind();
         con.Close();
			if(this.DataGrid2.Items.Count<=0)
			{
				this.Label4.Visible=true;
				this.DataGrid2.Visible=false;
			}
			else
			{
			 this.Label4.Visible=false;
             this.DataGrid2.Visible=true; 
			}
		 }

//		public void ReLoad()
//		{
//			if(Session["tempDept"].ToString().Length>0 && !flag)
//			{
//				string str="select desigid as DesignationId,designation as DesigName,deptid as Department,category,totalvacancies from t_designation where desigid='"+Session["tempDept"].ToString()+"'";
//				SqlCommand cmd=new SqlCommand(str,con);
//				SqlDataReader rd=cmd.ExecuteReader();
//				string desId="";
//				string name="";
//				string dept="";
//				string vacancy="";
//				while(rd.Read())
//				{
//					desId=rd[0].ToString(); 
//					name=rd[1].ToString();
//					dept=rd[2].ToString();
//					this.ddCategory.SelectedValue=rd[3].ToString();
//					vacancy=rd[4].ToString();
//				}
//				this.txtDesId.Text=desId;
//				this.txtName.Text=name;
//				this.txtVacancies.Text=vacancy;
//				for(int i=0;i<this.dropDownDept.Items.Count;i++)
//				{
//					if(dept==this.dropDownDept.Items[i].Value)
//					{
//						this.dropDownDept.SelectedValue=dept;
//					}
//				}
//				con.Close();
//			}
//		}
		public override void Dispose()
		{
			base.Dispose ();
			
		}

		private void dropDownDept_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			
			
		}

		private void DataGrid2_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			this.lblNewDes.Visible=false;
			this.lblNewDes.Text="";
			lblError.Visible=false;
			lblError.Text="";
			Session["tempId"]=this.DataGrid2.Items[this.DataGrid2.SelectedIndex].Cells[1].Text;
            this.myCurrentDes.Visible=false;
            this.FunctionalDesignation.Visible=true;
			this.myRow.Visible=false;
			this.btnSave.Text="Edit";
			this.btnSave.Enabled=true;
			
			this.myRowAssign.Visible=false;
			this.lblVacancy.Visible=false;
			this.Label8.Visible=false;
			if(this.DataGrid2.Items.Count<=0)
			{
				this.Label4.Visible=true;
			}
			else
			{
				this.Label4.Visible=false;
			}

           	BindDesignation();
            clickEvent();
		
			con.Open();
			string getRec="select * from t_functionaldesignation where fdesigid='"+ Session["tempId"].ToString() +"' And isactive=1";
			SqlCommand cmd=new SqlCommand(getRec,con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
			 this.lblTitle.Text=rd[1].ToString();
			 this.txtTitle.Text=rd[1].ToString(); 
             this.txtJobDescription.Text=rd[3].ToString();
             this.txtNoOfVacancy.Text=rd[4].ToString();
             this.txtSalaryRangeMin.Text=rd[5].ToString();
             this.txtSalaryRangeMax.Text=rd[6].ToString();
				try
				{
					this.ddStation.SelectedValue=rd[7].ToString();
				}
				catch(Exception em){string s=em.Message;}
             }
			rd.Close();
			con.Close();
			if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"View")==true)
			{
			  btnSave.Enabled=false;
              this.DataGrid3.Columns[3].Visible=false; 
			}
			if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Edit")==true)
			{
				btnSave.Enabled=false;
				this.DataGrid3.Columns[3].Visible=false; 
			}
			if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Assign FD")==true)
			{
				this.btnAssign.Enabled=true;
				this.DataGrid3.Columns[3].Visible=true;
			}
			if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Add FD")==true)
			{
				this.DataGrid2.Columns[3].Visible=true;
			    this.btnSave.Enabled=true;
				this.DataGrid2.Columns[4].Visible=true;
			}
            LinkButton lnk=(LinkButton)this.DataGrid2.Items[0].FindControl("lnkDelete");
			if(!lnk.Visible)
			{
			 this.btnSave.Enabled=false;
			 this.btnAssign.Enabled=false;
				for(int i=0;i<this.DataGrid3.Items.Count;i++)
				{
				 LinkButton lnr=(LinkButton)this.DataGrid3.Items[i].FindControl("lnkDel");
				 lnr.Visible=false;
				}
			}

		}

		private void btnSave_Click(object sender, System.EventArgs e)
		{
			if(!_isRefresh)
			{
				string errorString="";
				if(Page.IsValid)
				{
					if(this.txtNoOfVacancy.Text.Length>0)
					{
						int textVal=Convert.ToInt32(this.txtNoOfVacancy.Text);
						if(textVal<=0)
						{
							this.Label8.Visible=true;
						}
						else
						{
							this.Label8.Visible=false;
						}
					}
					if(!this.Label8.Visible)
					{
						this.Label8.Visible=false;
						if(this.btnSave.Text=="Save")
						{
							con.Open();
							string getVacancy="select sum(f.noofvacancies),d.designation from t_functionaldesignation f,t_designation d where f.functionaldesignation=d.desigid And f.isactive=1 And d.desigid='"+ this.txtDesId.Text +"' group by d.designation"; 
							SqlCommand cVacancy=new SqlCommand(getVacancy,con);
							SqlDataReader rVacancy=cVacancy.ExecuteReader();
							int allotedVac=0;
							int totalVac=0;
							int givenVac=0;
							if(this.txtVacancies.Text.Length>0)
							{
								givenVac=Convert.ToInt32(this.txtNoOfVacancy.Text);
							}
							while(rVacancy.Read())
							{
								if(rVacancy[0].ToString().Length>0)
								{
									allotedVac=Convert.ToInt32(rVacancy[0].ToString());
								}
							}
							rVacancy.Close();
                 
							string gettVacancy="select totalvacancies from t_designation where desigid='"+ this.txtDesId.Text +"'";
							SqlCommand ctVacancy=new SqlCommand(gettVacancy,con);
							SqlDataReader rtVacancy=ctVacancy.ExecuteReader();
							while(rtVacancy.Read())
							{
								if(rtVacancy[0].ToString().Length>0)
								{
									totalVac=Convert.ToInt32(rtVacancy[0].ToString());
								}
							}
							rtVacancy.Close();
							con.Close();
							totalVac=totalVac-(allotedVac+givenVac);
							if(totalVac >=0)
							{
								con.Open();
								guid=Guid.NewGuid();
								//string InsertQuery="insert into t_functionaldesignation values('"+this.txtTitle.Text+"','"+this.txtDesId.Text+"','"+this.txtJobDescription.Text+"','"+this.txtNoOfVacancy.Text+"','"+this.txtSalaryRangeMin.Text+"','"+this.txtSalaryRangeMax.Text+"','"+this.ddStation.SelectedValue.ToString()+"','"+1+"','{"+ guid.ToString() +"}')";
								string InsertQuery="insert into t_functionaldesignation(functionaltitle,functionaldesignation,jobdescription,noofvacancies,salaryrange,salaryrange1,station,isactive,guid) values(@functionaltitle,@functionaldesignation,@jobdescription,@noofvacancies,@salaryrange,@salaryrange1,@station,1,@guid)";
								SqlCommand cmd=new SqlCommand(InsertQuery,con);
								SqlParameter _functionaltitle=new SqlParameter("@functionaltitle",SqlDbType.VarChar);
								SqlParameter _functionaldesignation=new SqlParameter("@functionaldesignation",SqlDbType.Int);
								SqlParameter _jd=new SqlParameter("@jobdescription",SqlDbType.VarChar);
								SqlParameter _vacancies=new SqlParameter("@noofvacancies",SqlDbType.Int);
								SqlParameter _salaryrange=new SqlParameter("@salaryrange",SqlDbType.Money);
								SqlParameter _salaryrange1=new SqlParameter("@salaryrange1",SqlDbType.Money);
								SqlParameter _station=new SqlParameter("@station",SqlDbType.Int);
								SqlParameter _guid=new SqlParameter("@guid",SqlDbType.UniqueIdentifier);
                                _functionaltitle.Value=this.txtTitle.Text;
								_functionaldesignation.Value=this.txtDesId.Text;
								_jd.Value=this.txtJobDescription.Text;
								_vacancies.Value=this.txtNoOfVacancy.Text;
								_salaryrange.Value=this.txtSalaryRangeMin.Text;
								_salaryrange1.Value=this.txtSalaryRangeMax.Text;
								_station.Value=this.ddStation.SelectedValue.ToString();
								_guid.Value=guid;
								cmd.Parameters.Add(_functionaltitle);
								cmd.Parameters.Add(_functionaldesignation);
								cmd.Parameters.Add(_jd);
								cmd.Parameters.Add(_vacancies);
								cmd.Parameters.Add(_salaryrange);
								cmd.Parameters.Add(_salaryrange1);
								cmd.Parameters.Add(_station);
								cmd.Parameters.Add(_guid);
								int i=0;
								try
								{
									i=cmd.ExecuteNonQuery();
								}
								catch(Exception exc)
								{
								 Response.Write(exc.Message);
								}
								con.Close();
								if(i>0)
								{
									this.btnSave.Enabled=false;
									con.Open();
									cmd=new SqlCommand("select fdesigid from t_functionaldesignation where guid='{"+guid.ToString()+"}'",con);
									SqlDataReader rd=cmd.ExecuteReader();
									rd.Read();
									Session["tempId"]=rd[0].ToString().Trim();
									con.Close();
								}
								this.lblTitleMsg.Visible=false;
								getFunctionalDes(this.txtDesId.Text);
								clickEvent(true);
								//this.btnCancel.Text="Close";
								if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Assign FD")==true)
								{
									this.btnAssign.Enabled=true;
								}
								this.lblVacancy.Visible=false;
								con.Close();
							}
							else
							{
								this.btnSave.Enabled=true;
								this.btnAssign.Enabled=false;
								this.DataGrid3.Visible=false;
								this.lblVacancy.Visible=true;
							}
						}
						else
						{
							//===========Edit=====================//
							con.Open();
							string getVacancy="select sum(f.noofvacancies),d.designation from t_functionaldesignation f,t_designation d where f.functionaldesignation=d.desigid And f.isactive=1 And f.fdesigid not in('"+ Session["tempId"].ToString() +"') And d.desigid in(select functionaldesignation from t_functionaldesignation where fdesigid='"+ Session["tempId"].ToString() +"')group by d.designation"; 
							SqlCommand cVacancy=new SqlCommand(getVacancy,con);
							SqlDataReader rVacancy=cVacancy.ExecuteReader();
							int allotedVac=0;
							int totalVac=0;
							int givenVac=0;
							if(this.txtVacancies.Text.Length>0)
							{
								givenVac=Convert.ToInt32(this.txtNoOfVacancy.Text);
							}
							while(rVacancy.Read())
							{
								if(rVacancy[0].ToString().Length>0)
								{
									allotedVac=Convert.ToInt32(rVacancy[0].ToString());
								}
							}
							rVacancy.Close();
                 
							string gettVacancy="select totalvacancies from t_designation where desigid in(select functionaldesignation from t_functionaldesignation where fdesigid='"+Session["tempId"].ToString()+"')";
							SqlCommand ctVacancy=new SqlCommand(gettVacancy,con);
							SqlDataReader rtVacancy=ctVacancy.ExecuteReader();
							while(rtVacancy.Read())
							{
								if(rtVacancy[0].ToString().Length>0)
								{
									totalVac=Convert.ToInt32(rtVacancy[0].ToString());
								}
							}
							rtVacancy.Close();
							con.Close();
							totalVac=totalVac-(allotedVac+givenVac);
					
							int hRecords=0;
							con.Open();
							string getRec="select Isnull(sum(isactive),0) from t_designationhistory where fdesigid='"+Session["tempId"].ToString() +"' and isactive=1";
							SqlCommand cRec=new SqlCommand(getRec,con);
							SqlDataReader rRec=cRec.ExecuteReader();
							rRec.Read();
							if(rRec.HasRows)
							{
								hRecords=rRec.GetInt32(0);
							}
							con.Close();
							hRecords=givenVac-hRecords;

							if(totalVac >=0 && hRecords >=0)
							{
								con.Open();
								//string updateQuery="update t_functionaldesignation set functionaltitle='"+this.txtTitle.Text+"',jobdescription='"+this.txtJobDescription.Text+"',noofvacancies='"+this.txtNoOfVacancy.Text+"',salaryrange='"+this.txtSalaryRangeMin.Text+"',salaryrange1='"+this.txtSalaryRangeMax.Text+"',station='"+this.ddStation.SelectedValue.ToString()+"' where fdesigid='"+ Session["tempId"].ToString() +"'";
								string updateQuery="update t_functionaldesignation set functionaltitle=@functionaltitle,jobdescription=@jobdescription,noofvacancies=@noofvacancies,salaryrange=@salaryrange,salaryrange1=@salaryrange1,station=@station where fdesigid='"+ Session["tempId"].ToString() +"'";
								SqlCommand cmd=new SqlCommand(updateQuery,con);
								SqlParameter _functionaltitle=new SqlParameter("@functionaltitle",SqlDbType.VarChar);
								SqlParameter _jd=new SqlParameter("@jobdescription",SqlDbType.VarChar);
								SqlParameter _vacancies=new SqlParameter("@noofvacancies",SqlDbType.Int);
								SqlParameter _salaryrange=new SqlParameter("@salaryrange",SqlDbType.Money);
								SqlParameter _salaryrange1=new SqlParameter("@salaryrange1",SqlDbType.Money);
								SqlParameter _station=new SqlParameter("@station",SqlDbType.Int);
								_functionaltitle.Value=this.txtTitle.Text;
								_jd.Value=this.txtJobDescription.Text;
								_vacancies.Value=this.txtNoOfVacancy.Text;
								_salaryrange.Value=this.txtSalaryRangeMin.Text;
								_salaryrange1.Value=this.txtSalaryRangeMax.Text;
								_station.Value=this.ddStation.SelectedValue.ToString();
								cmd.Parameters.Add(_functionaltitle);
								cmd.Parameters.Add(_jd);
								cmd.Parameters.Add(_vacancies);
								cmd.Parameters.Add(_salaryrange);
								cmd.Parameters.Add(_salaryrange1);
								cmd.Parameters.Add(_station);
								int j=cmd.ExecuteNonQuery();
								con.Close();
								this.lblVacancy.Visible=false;
								this.lblTitleMsg.Visible=false;
								getFunctionalDes(this.txtDesId.Text);
								//							}
								con.Close();
							}
							else
							{
								this.lblVacancy.Visible=true;
								errorString+=lblVacancy.Text;
							}
							//==============================================//
						}
					}
					else
					{
						this.Label8.Visible=true;
						errorString+=Label8.Text;
					}	
				}
				else
				{
					errorString+=CustomValidator1.ErrorMessage;
				}
				if(errorString.Length>0)
				{
					Response.Write("<script>window.alert('"+ errorString +"');</script>");
				}
			}
		}

		private void btnAssign_Click(object sender, System.EventArgs e)
		{
			this.lblTitleMsg.Visible=false;
			this.Label10.Visible=false;
			this.btnRemove.Visible=false;
			this.btnRemove.ToolTip="";
			this.txtAdName.Text="";
			this.txtNIC.Text="";
			ddEmployeeName.SelectedIndex=0;
			ddLocation.SelectedIndex=0;
			StopProceed();
		}

		public void getStation()
		{
			string Query="select cityid,cityname from t_city where status=1 order by cityname";
			SqlCommand cmd=new SqlCommand(Query,con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rd[0].ToString();
				itm.Text=rd[1].ToString();
				this.ddStation.Items.Add(itm);
			}
			rd.Close();
		}

		public void getAll()
		{
			this.ddEmployeeName.Items.Clear();
			ListItem item=new ListItem("Select: Employee","0");
			this.ddEmployeeName.Items.Add(item);
			this.Panel2.Visible=true;
			//con.Open();
			string query="select e.pcode, e.name from t_employee e,t_designation d,t_department dept where e.del=1 And d.status=1 And dept.status=1 And d.deptid=dept.deptid And e.desigid=d.desigid And dept.deptid='"+ ddMdept.SelectedValue.ToString() +"' order by e.name ";
			SqlCommand cmd=new SqlCommand(query,con);
			SqlDataReader rd=cmd.ExecuteReader();
			int i=1;
			while(rd.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rd[0].ToString();
				itm.Text=rd[0].ToString()+":"+rd[1].ToString();
				this.ddEmployeeName.Items.Add(itm);
				i++;
			}
			rd.Close();

			//string query3="select distinct(cityid),cityname from t_city where cityid=any(select e.station from t_employee e,t_department d,t_designation des where d.deptid=des.deptid And e.desigid=des.desigid And des.deptid =any(select deptid from t_employee e2,t_designation d2 where e2.pcode='"+Session["user_id"].ToString()+"' And e2.desigid=d2.desigid))";
			string query3="select distinct(cityid),cityname from t_city where status=1 order by cityname";
			SqlCommand cmd3=new SqlCommand(query3,con);
			SqlDataReader rd3=cmd3.ExecuteReader();
			int k=1;
			while(rd3.Read())
			{
				ListItem itm3=new ListItem();
				itm3.Value=rd3[0].ToString();
				itm3.Text=rd3[1].ToString();
				this.ddLocation.Items.Insert(k,itm3);
				k++;
			}
			rd3.Close();
			//con.Close();
		}

		public void Selected(object sender,System.EventArgs e)
		{
			this.Label10.Visible=false;
			this.btnRemove.Visible=false;
			LinkButton lnk=(LinkButton)sender;
			LinkButton lnkS=new LinkButton();
			int z=0;
			for(int i=0;i<this.DataGrid4.Items.Count;i++)
			{
				lnkS=(LinkButton)this.DataGrid4.Items[i].FindControl("btnSelect");
				if(lnk.Equals(lnkS))
				{
					con.Open();
					if(Session["tempId"].ToString()=="" && !guid.Equals(Guid.Empty))
					{
						string verify="select h.pcode,f.functionaltitle from t_designationhistory h,t_functionaldesignation f where h.pcode='"+this.DataGrid4.Items[i].Cells[2].Text+"' And h.isactive=1 And f.fdesigid=h.fdesigid";
						SqlCommand cm=new SqlCommand(verify,con);
						SqlDataReader r=cm.ExecuteReader();
						r.Read();
						if(r.HasRows)
						{
						 this.Label10.Text="The Selected Employee have already assigned a designation,Please remove its previous designation "+r[1].ToString();						
						 this.Label10.Visible=true;
                         Response.Write("<script>window.alert('"+ Label10.Text +"');</script>"); 
                         this.Label10.ForeColor=Color.Red;
						 this.btnRemove.Visible=true;
                         this.btnRemove.ToolTip=this.DataGrid4.Items[i].Cells[2].Text;
                         r.Close();
						 
						 Session["pCode"]=this.DataGrid4.Items[i].Cells[2].Text;
						 string getIdd="select fdesigid from t_functionaldesignation where guid='{"+guid.ToString()+"}' ";
						 SqlCommand cmdd=new SqlCommand(getIdd,con);
						 SqlDataReader rdd=cmdd.ExecuteReader();
						 rdd.Read();
						 Session["fDesignation"]=rdd[0].ToString();
                         rdd.Close(); 
						
						}
						else
						{
							r.Close();
							this.Label10.Visible=false;
							this.btnRemove.Visible=false;
                            this.btnRemove.ToolTip="";
							string getId="select fdesigid,functionaldesignation from t_functionaldesignation where guid='{"+guid.ToString()+"}' ";
							SqlCommand cmd=new SqlCommand(getId,con);
							SqlDataReader rd=cmd.ExecuteReader();
							rd.Read();
							string Id=rd[0].ToString();
							string desigid=rd[1].ToString();
							rd.Close();

							
							//string InsertRec="insert into t_designationhistory values('"+this.DataGrid4.Items[i].Cells[2].Text+"','"+Id+"','"+DateTime.Parse(this.txtDate.Text)+"',"+ var +",'"+1+"')";
							string InsertRec="insert into t_designationhistory(pcode,fdesigid,_from,isactive) values(@pcode,@fdesigid,@_from,1)";
							SqlParameter _pcode=new SqlParameter("@pcode",SqlDbType.Char);
							SqlParameter _fdesigid=new SqlParameter("@fdesigid",SqlDbType.Int);
							SqlParameter _from=new SqlParameter("@_from",SqlDbType.SmallDateTime);
							_pcode.Value=this.DataGrid4.Items[i].Cells[2].Text;
							_fdesigid.Value=Id;
							_from.Value=this.txtDate.Text;
							cmd=new SqlCommand(InsertRec,con);
							cmd.Parameters.Add(_pcode);
							cmd.Parameters.Add(_fdesigid);
							cmd.Parameters.Add(_from);
							z=cmd.ExecuteNonQuery();

							string updateDes="update t_employee set desigid='"+ desigid +"' where pcode='"+ this.DataGrid4.Items[i].Cells[2].Text +"' And del=1";
						    cmd=new SqlCommand(updateDes,con);
							cmd.ExecuteNonQuery();

							Session["pCode"]="";
							Session["fDesignation"]="";
							this.txtDate.Text="";
						}
					}
					else
					{
					 //===============Edit==================//
						string verify="select h.pcode,f.functionaltitle from t_designationhistory h,t_functionaldesignation f where h.pcode='"+this.DataGrid4.Items[i].Cells[2].Text+"' And h.isactive=1 And f.fdesigid=h.fdesigid";
						SqlCommand cm=new SqlCommand(verify,con);
						SqlDataReader r=cm.ExecuteReader();
						r.Read();
						if(r.HasRows)
						{
							this.Label10.Text="The Selected Employee have already assigned a designation,Please remove its previous designation "+r[1].ToString();
						    this.Label10.Visible=true;
							 Response.Write("<script>window.alert('"+ Label10.Text +"');</script>");
						    this.Label10.ForeColor=Color.Red;
							this.btnRemove.Visible=true;
							this.btnRemove.ToolTip=this.DataGrid4.Items[i].Cells[2].Text;
							r.Close();
							Session["pCode"]=this.DataGrid4.Items[i].Cells[2].Text;
							Session["fDesignation"]=Session["tempId"].ToString();
						}
						else
						{
							r.Close();
							this.Label10.Visible=false;
							this.btnRemove.Visible=false;
                            this.btnRemove.ToolTip="";

							//string InsertRec="insert into t_designationhistory values('"+this.DataGrid4.Items[i].Cells[2].Text+"','"+ Session["tempId"].ToString() +"','"+DateTime.Parse(this.txtDate.Text)+"',"+ var +",'"+1+"')";
							string InsertRec="insert into t_designationhistory(pcode,fdesigid,_from,isactive) values(@pcode,@fdesigid,@_from,1)";
							SqlParameter _pcode=new SqlParameter("@pcode",SqlDbType.Char);
							SqlParameter _fdesigid=new SqlParameter("@fdesigid",SqlDbType.Int);
							SqlParameter _from=new SqlParameter("@_from",SqlDbType.SmallDateTime);
							_pcode.Value=this.DataGrid4.Items[i].Cells[2].Text;
							_fdesigid.Value=Session["tempId"].ToString();
							_from.Value=this.txtDate.Text;
							SqlCommand cmd=new SqlCommand(InsertRec,con);
							cmd.Parameters.Add(_pcode);
							cmd.Parameters.Add(_fdesigid);
							cmd.Parameters.Add(_from);
							z=cmd.ExecuteNonQuery();

							string updateDes="update t_employee set desigid='"+ this.txtDesId.Text +"' where pcode='"+ this.DataGrid4.Items[i].Cells[2].Text +"' And del=1";
							cmd=new SqlCommand(updateDes,con);
							cmd.ExecuteNonQuery();
							Session["pCode"]="";
							Session["fDesignation"]="";
							this.txtDate.Text="";
						}
                     //=====================================//
					}
					if(!this.Label10.Visible)
					{
						this.myRowAssign.Visible=false;
						this.DataGrid4.Visible=false;
					}
					con.Close();
					BindDesignation();
					clickEvent();
					break;
				}
				
			}
		}

		public void BindDesignation()
		{
			con.Open();
			if(Session["tempId"].ToString()=="" && !guid.Equals(Guid.Empty))
			{
				string getId="select fdesigid from t_functionaldesignation where guid='{"+guid.ToString()+"}' ";
				SqlCommand cmd=new SqlCommand(getId,con);
				SqlDataReader rd=cmd.ExecuteReader();
				rd.Read();
				string Id=rd[0].ToString();
				rd.Close();

				string query="select '<img src=../employee/' + e.pic + ' width=100>' AS Photo,e.pcode PCode,e.name Name from t_designationhistory d,t_employee e where e.pcode=d.pcode And fdesigid='"+Id+"' And isactive=1";
				SqlDataAdapter rmd=new SqlDataAdapter(query,con);
				DataSet ds=new DataSet();
				ds.Clear();
				rmd.Fill(ds,"Records");
				DataView dv=new DataView(ds.Tables["Records"]);
				this.DataGrid3.DataSource=dv;
				this.DataGrid3.DataBind();
				this.DataGrid3.Visible=true;
				if(this.DataGrid3.Items.Count<=0)
				{
					this.DataGrid3.Visible=false;
					this.Label4.Visible=true;
				}
				else
				{
					this.DataGrid3.Visible=true; 
					this.Label4.Visible=false;
				}
			}
			else
			{
			 //==========Edit===============//
				string query="select '<img src=../employee/' + e.pic + ' width=100>' AS Photo,e.pcode PCode,e.name Name from t_designationhistory d,t_employee e where e.pcode=d.pcode And fdesigid='"+ Session["tempId"].ToString() +"' And isactive=1";
				SqlDataAdapter rmd=new SqlDataAdapter(query,con);
				DataSet ds=new DataSet();
				rmd.Fill(ds,"Records");
				DataView dv=new DataView(ds.Tables["Records"]);
				this.DataGrid3.DataSource=dv;
				this.DataGrid3.DataBind();
				this.DataGrid3.Visible=true;
				if(this.DataGrid3.Items.Count<=0)
				{
					this.DataGrid3.Visible=false;
					//this.Label9.Visible=true;
				}
				else
				{
					this.DataGrid3.Visible=true; 
					//this.Label9.Visible=false;
				}
             //=============================//
			}
			con.Close();
		}

		private void btnsearchAdvance_Click(object sender, System.EventArgs e)
		{
			this.DataGrid4.Visible=true;
			Session["sql"]="select '<img src=../employee/' + e.pic + ' width=100>' AS Photo ,e.pcode PCode,e.name Name,e.email_official Email,e.extension Extension from t_employee e,t_designation d,t_department dept where And e.del=1 And d.status=1 And dept.status=1 And dept.deptid=d.deptid And d.desigid=e.desigid And dept.deptid="+ this.ddMdept.SelectedValue.ToString() +"";
			string tempSql="";
			if(this.txtAdName.Text.Length>0)
			{
				//tempSql+=" And e.name like'%"+this.txtAdName.Text+"%'";
			      tempSql+=" And e.name like'%'+@name+'%'";
			}
			if(this.txtNIC.Text.Length>0)
			{
				//tempSql+=" And e.nic_new='"+this.txtNIC.Text+"'";
			    tempSql+=" And e.nic_new=@nic";
			}
			if(this.ddLocation.SelectedValue !="0")
			{
				tempSql+=" And e.station='"+Convert.ToInt32(this.ddLocation.SelectedValue.ToString())+"'";
			}
			if(this.ddEmployeeName.SelectedValue !="0")
			{
				tempSql+=" And e.pcode='"+this.ddEmployeeName.SelectedValue.ToString()+"'";
			}
			if(tempSql.Length>0)
			{
				Session["sql"]+=tempSql;
				int idx=Session["sql"].ToString().IndexOf("where");
				Session["sql"]=Session["sql"].ToString().Remove(idx+6,4);
				BindSearch(Session["sql"].ToString());
			}
		}

		public void BindSearch(string QueryString)
		{
			int i=QueryString.IndexOf("@name");
			int k=QueryString.IndexOf("@nic");
			con.Open();
			SqlDataAdapter rd=new SqlDataAdapter(QueryString,con);
			if(i>0)
			{
			 SqlParameter _name=new SqlParameter("@name",SqlDbType.VarChar);
			 _name.Value=this.txtAdName.Text;
			 rd.SelectCommand.Parameters.Add(_name);
			}
			if(k>0)
			{
				SqlParameter _nic=new SqlParameter("@nic",SqlDbType.VarChar);
				_nic.Value=this.txtNIC.Text;
				rd.SelectCommand.Parameters.Add(_nic);
			}
			DataSet ds2=new DataSet();
			ds2.Clear();
			rd.Fill(ds2,"Records");
			DataView dv=new DataView(ds2.Tables["Records"]);
			dv.Sort=sortVar;
			this.DataGrid4.DataSource=dv;
			this.DataGrid4.DataBind();
			con.Close();
			setEvent();
		}

		private void DataGrid4_SortCommand(object source, System.Web.UI.WebControls.DataGridSortCommandEventArgs e)
		{
			sortVar=e.SortExpression.ToString();
			BindSearch(Session["sql"].ToString());  
		}
      
		public void StopProceed()
		{
			con.Open();
			if(Session["tempId"].ToString()=="" && !guid.Equals(Guid.Empty))
			{
				string getId="select fdesigid from t_functionaldesignation where guid='{"+guid.ToString()+"}' ";
				SqlCommand cmd=new SqlCommand(getId,con);
				SqlDataReader rd=cmd.ExecuteReader();
				rd.Read();
				string Id=rd[0].ToString();
				rd.Close();

				string getVacancy="select sum(isactive) from t_designationhistory where fdesigid='"+Id+"' and isactive=1";
				SqlCommand cVacancy=new SqlCommand(getVacancy,con);
				SqlDataReader rVacancy=cVacancy.ExecuteReader();
				int allotedVac=0;
				int totalVac=0;
						
				while(rVacancy.Read())
				{
					if(rVacancy[0].ToString().Length>0)
					{
						allotedVac=Convert.ToInt32(rVacancy[0].ToString());
					}
				}
				rVacancy.Close();
                 
				//string gettVacancy="select totalvacancies from t_designation where desigid in(select functionaldesignation from t_functionaldesignation where fdesigid='"+getId+"')";
				string gettVacancy="select noofvacancies from t_functionaldesignation where fdesigid='"+Id+"'";
				SqlCommand ctVacancy=new SqlCommand(gettVacancy,con);
				SqlDataReader rtVacancy=ctVacancy.ExecuteReader();
				while(rtVacancy.Read())
				{
					if(rtVacancy[0].ToString().Length>0)
					{
						totalVac=Convert.ToInt32(rtVacancy[0].ToString());
					}
				}
				rtVacancy.Close();
				totalVac=totalVac-(allotedVac);
				if(totalVac<=0)
				{
					this.lblVacancy.Visible=true;
				}
				else
				{
					this.lblVacancy.Visible=false;
					this.myRowAssign.Visible=true;
//					this.titleRow.Visible=false;
//					this.JobRow.Visible=false;
//					this.SalaryRow.Visible=false;
//					this.EmptyRow.Visible=false;
//					ManipulationRow.Visible=false;
//					this.VacancyRow.Visible=false;
//					this.StationRow.Visible=false;
					this.DataGrid4.Visible=false;
					getAll();
				} 
			}
			else
			{
				string getVacancy="select sum(isactive) from t_designationhistory where fdesigid='"+Session["tempId"].ToString()+"' and isactive=1";
				SqlCommand cVacancy=new SqlCommand(getVacancy,con);
				SqlDataReader rVacancy=cVacancy.ExecuteReader();
				int allotedVac=0;
				int totalVac=0;
						
				while(rVacancy.Read())
				{
					if(rVacancy[0].ToString().Length>0)
					{
						allotedVac=Convert.ToInt32(rVacancy[0].ToString());
					}
				}
				rVacancy.Close();
                 
				//string gettVacancy="select totalvacancies from t_designation where desigid in(select functionaldesignation from t_functionaldesignation where fdesigid='"+getId+"')";
				string gettVacancy="select noofvacancies from t_functionaldesignation where fdesigid='"+ Session["tempId"].ToString()+"'";
				SqlCommand ctVacancy=new SqlCommand(gettVacancy,con);
				SqlDataReader rtVacancy=ctVacancy.ExecuteReader();
				while(rtVacancy.Read())
				{
					if(rtVacancy[0].ToString().Length>0)
					{
						totalVac=Convert.ToInt32(rtVacancy[0].ToString());
					}
				}
				rtVacancy.Close();
				totalVac=totalVac-(allotedVac);
				if(totalVac<=0)
				{
					this.lblVacancy.Visible=true;
				}
				else
				{
//					this.lblVacancy.Visible=false;
					this.myRowAssign.Visible=true;
//					this.titleRow.Visible=false;
//					this.JobRow.Visible=false;
//					this.SalaryRow.Visible=false;
//					this.EmptyRow.Visible=false;
//					ManipulationRow.Visible=false;
//					this.VacancyRow.Visible=false;
//					this.StationRow.Visible=false;
					this.DataGrid4.Visible=false;
					getAll();
				}
			}
			con.Close();
		}

		
		private void Button1_Click(object sender, System.EventArgs e)
		{
			this.myCurrentDes.Visible=true;
			this.FunctionalDesignation.Visible=false;
			this.myRow.Visible=true;
			this.myRowAssign.Visible=false;
			this.lblTitleMsg.Visible=false;
			if(this.DataGrid2.Items.Count<=0)
			{
				this.Label4.Visible=true;
			}
			else
			{
			 this.Label4.Visible=false;
			}
			Session["tempId"]="";
		}

		private void btnSclose_Click(object sender, System.EventArgs e)
		{
			this.myRowAssign.Visible=false;
//			this.titleRow.Visible=true;
//			this.JobRow.Visible=true;
//			this.SalaryRow.Visible=true;
//			this.EmptyRow.Visible=true;
//			ManipulationRow.Visible=true;
//			this.VacancyRow.Visible=true;
//			this.StationRow.Visible=true;
			this.DataGrid4.Visible=false;
			Session["pCode"]="";
			Session["fDesignation"]="";
		}

		private void ddMdept_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			BindGrid();
			this.FunctionalDesignation.Visible=false;
			this.myCurrentDes.Visible=true;
			this.txtName.Text="";
			this.ddCategory.SelectedIndex=0;
			this.txtVacancies.Text="";
			this.txtDesId.Visible=false;
			Label1.Visible=false;
			this.DataGrid2.Visible=false;
			lblError.Visible=false;
			lblError.Text="";
		}

		private void btnRemove_Click(object sender, System.EventArgs e)
		{
			if(!_isRefresh)
			{
				con.Open();
				string updateRec="update t_designationhistory set isactive=0,_to='"+ DateTime.Parse(this.txtPrev.Text) +"' where pcode='"+this.btnRemove.ToolTip.ToString()+"'";
				SqlCommand cmd=new SqlCommand(updateRec,con);
				int i= cmd.ExecuteNonQuery();
				if(i>0)
				{
					this.btnRemove.ToolTip="";
					this.btnRemove.Visible=false;
					this.Label10.Text="Designation has removed successfully";
					this.Label10.ForeColor=Color.Black;
				}
				//string var="NULL";
				//string InsertRec="insert into t_designationhistory values('"+Session["pCode"].ToString()+"','"+Session["fDesignation"].ToString() +"','"+DateTime.Parse(this.txtDate.Text)+"',"+ var +",'"+1+"')";
				string InsertRec="insert into t_designationhistory(pcode,fdesigid,_from,isactive) values(@pcode,@fdesigid,@_from,1)";
				SqlParameter _pcode=new SqlParameter("@pcode",SqlDbType.Char);
				SqlParameter _fdesigid=new SqlParameter("@fdesigid",SqlDbType.Int);
				SqlParameter _from=new SqlParameter("@_from",SqlDbType.SmallDateTime);
				_pcode.Value=Session["pCode"].ToString();
				_fdesigid.Value=Session["fDesignation"].ToString();
				_from.Value=this.txtDate.Text;
				cmd=new SqlCommand(InsertRec,con);
				cmd.Parameters.Add(_pcode);
				cmd.Parameters.Add(_fdesigid);
				cmd.Parameters.Add(_from);
				cmd.ExecuteNonQuery();

				string updateDes="update t_employee set desigid='"+ this.txtDesId.Text +"' where pcode='"+ Session["pCode"].ToString() +"' And del=1";
				cmd=new SqlCommand(updateDes,con);
				cmd.ExecuteNonQuery();
				Session["pCode"]="";
				Session["fDesignation"]="";
				this.txtPrev.Text="";
				this.txtDate.Text="";
				con.Close();
				this.myRowAssign.Visible=false;
				this.DataGrid4.Visible=false;
				BindDesignation();
				clickEvent();
			}
		}

		private void Button2_Click(object sender, System.EventArgs e)
		{
			
		}

		private void CustomValidator1_ServerValidate_1(object source, System.Web.UI.WebControls.ServerValidateEventArgs args)
		{
		args.IsValid = (args.Value !="0");
		} 

		public void clickEvent()
		{
			//this.btnRoute.Attributes.Add("onclick","return confirm('Are You Sure To Delete');");
			LinkButton btn=new LinkButton();
			for(int i=0;i<this.DataGrid3.Items.Count;i++)
			{
				btn=(LinkButton)this.DataGrid3.Items[i].FindControl("lnkDel");
				//btn.Attributes.Add("onclick","return confirm('If you delete selected employee on this functional designation then the employee will have same current designation otherwise you have to assign new functional designation to that employee,Are You Sure To Delete');");
			    btn.Attributes.Add("onclick","return checkVal();");
			}
		}

		public void clickEvent(bool val)
		{
			//this.btnRoute.Attributes.Add("onclick","return confirm('Are You Sure To Delete');");
			LinkButton btn=new LinkButton();
			for(int i=0;i<this.DataGrid2.Items.Count;i++)
			{
				btn=(LinkButton)this.DataGrid2.Items[i].FindControl("lnkDelete");
				btn.Attributes.Add("onclick","return confirm('Are You Sure To Delete');");
			}
		}

		public void Del(object sender ,EventArgs e)
		{
			lblError.Visible=false;
			lblError.Text="";
			con.Open();
			LinkButton link=new LinkButton();
			LinkButton rLink=(LinkButton)sender;
			for(int i=0;i<this.DataGrid3.Items.Count;i++)
			{
				link=(LinkButton)this.DataGrid3.Items[i].FindControl("lnkDel");
				if(link.Equals(rLink))
				{
					if(Session["tempId"].ToString()=="" && !guid.Equals(Guid.Empty))
					{
						string getId="select fdesigid from t_functionaldesignation where guid='{"+guid.ToString()+"}' ";
						SqlCommand cmd=new SqlCommand(getId,con);
						SqlDataReader rd=cmd.ExecuteReader();
						rd.Read();
						string Id=rd[0].ToString();
						rd.Close();

						string Query="update t_designationhistory set isactive=0,_to='"+ DateTime.Parse(this.txttest.Text) +"' where pcode='"+this.DataGrid3.Items[i].Cells[1].Text+"' And fdesigid='"+Id+"' And isactive=1";
						cmd=new SqlCommand(Query,con);
						cmd.ExecuteNonQuery();
						this.txttest.Text="";
						con.Close();
						BindDesignation();
						clickEvent();
						this.lblVacancy.Visible=false;
					}
					else
					{
						//=========Edit==================//
						string Query="update t_designationhistory set isactive=0,_to='"+ DateTime.Parse(this.txttest.Text) +"' where pcode='"+this.DataGrid3.Items[i].Cells[1].Text+"' And fdesigid='"+Session["tempId"].ToString()+"' And isactive=1";
						SqlCommand cmd=new SqlCommand(Query,con);
						cmd.ExecuteNonQuery();
						this.txttest.Text="";
						con.Close();
						BindDesignation();
						clickEvent();
						this.lblVacancy.Visible=false;
						//===============================// 
					}
				}
			}
			con.Close();
		}

		public void DelFunctionalDesignation(object sender,EventArgs e)
		{
			this.lblNewDes.Visible=false;
			this.lblNewDes.Text="";
			lblError.Visible=false;
			lblError.Text="";
			this.lblMesg.Text="";
			this.lblMesg.Visible=false;
			bool emp=false;
			bool dirTo=false;
			bool dotTo=false;
			bool dirFrm=false;
			bool dotFrm=false;
			string tDirMsg="Selected Functional Designation Direct Report To: ";
			string tDotMsg="Selected Functional Designation Dotted Report To: ";
			string tDirFrm="Functional Designation that Direct Reports to selected functional designation: ";
			string tDotFrm="Functional Designation that Dotted Reports to selected functional designation:  ";
			LinkButton lnk=(LinkButton)sender;
			for(int i=0;i<this.DataGrid2.Items.Count;i++)
			{
				LinkButton dgLink=(LinkButton)this.DataGrid2.Items[i].FindControl("lnkDelete");
				if(dgLink.Equals(lnk))
				{
					con.Open();
					string verifyEmployee="select fdesigid from t_designationhistory where fdesigid='"+ this.DataGrid2.Items[i].Cells[1].Text +"' And isactive=1";
					SqlCommand cmd=new SqlCommand(verifyEmployee,con);
					SqlDataReader rd=cmd.ExecuteReader();
					rd.Read();
					if(rd.HasRows)
					{
					   emp=true;                   
					}
					rd.Close();
                    string verifyDirReportTo="select f1.functionaltitle DirectReportingTo from t_functionaldesignation f1 where f1.isactive=1 and f1.fdesigid=Any(select d.sfdesigid from t_functionaldesignation f,t_directreporting d where f.isactive=1 And d.isactive=1 And f.fdesigid=d.fdesigid And f.fdesigid='"+ this.DataGrid2.Items[i].Cells[1].Text +"') ";
                    string verifyDottedReportTo="select f1.functionaltitle DottedReportingTo from t_functionaldesignation f1 where f1.isactive=1 and f1.fdesigid=Any(select d.sfdesigid from t_functionaldesignation f,t_dottedlinereporting d where f.isactive=1 And d.isactive=1 And f.fdesigid=d.fdesigid And f.fdesigid='"+ this.DataGrid2.Items[i].Cells[1].Text +"')";
					string verifyDirReportFrom="select f1.functionaltitle DirectReportingFrom from t_functionaldesignation f1 where f1.isactive=1 and f1.fdesigid=Any(select d.fdesigid from t_functionaldesignation f,t_directreporting d where f.isactive=1 And d.isactive=1 And f.fdesigid=d.sfdesigid And f.fdesigid='"+ this.DataGrid2.Items[i].Cells[1].Text +"')";
					string verifyDottedReportFrom="select f1.functionaltitle DottedReportingFrom from t_functionaldesignation f1 where f1.isactive=1 and f1.fdesigid=Any(select d.fdesigid from t_functionaldesignation f,t_dottedlinereporting d where f.isactive=1 And d.isactive=1 And f.fdesigid=d.sfdesigid And f.fdesigid='"+ this.DataGrid2.Items[i].Cells[1].Text +"')";
					
				    cmd=new SqlCommand(verifyDirReportTo,con);
					rd=cmd.ExecuteReader();
					while(rd.Read())
					{
					    dirTo=true; 
						tDirMsg+=rd[0].ToString()+",";
					}
					tDirMsg=tDirMsg.Remove(tDirMsg.Length-1,1);
                    rd.Close();
					cmd=new SqlCommand(verifyDottedReportTo,con);
					rd=cmd.ExecuteReader();
					while(rd.Read())
					{
						dotTo=true;
						tDotMsg+=rd[0].ToString()+",";
					}
					tDotMsg=tDotMsg.Remove(tDotMsg.Length-1,1);
					rd.Close();

					cmd=new SqlCommand(verifyDirReportFrom,con);
					rd=cmd.ExecuteReader();
					while(rd.Read())
					{
					    dirFrm=true;
						tDirFrm+=rd[0].ToString()+","; 
					}
					tDirFrm=tDirFrm.Remove(tDirFrm.Length-1,1);
					rd.Close();

					cmd=new SqlCommand(verifyDottedReportFrom,con);
					rd=cmd.ExecuteReader();
					while(rd.Read())
					{
					    dotFrm=true;
						tDotFrm+=rd[0].ToString()+",";
					}
					tDotFrm=tDotFrm.Remove(tDotFrm.Length-1,1);
					rd.Close();
					if(dirTo)
					{
					 this.lblMesg.Text+=tDirMsg+"<br>";
					}
					if(dotTo)
					{
					 this.lblMesg.Text+=tDotMsg+"<br>";
					}
					if(dirFrm)
					{
					 this.lblMesg.Text+=tDirFrm+"<br>";
					}
					if(dotFrm)
					{
					 this.lblMesg.Text+=tDotFrm+"<br>";
					}
					if(emp)
					{
					 this.lblMesg.Text+="Employee exist on selected designation.Please delete employee";
					}
					if(this.lblMesg.Text.Length>0)
					{
                     string errorString="System cannot delete this functional designation because: ";
                     errorString+=lblMesg.Text.Replace("<br>" ,",");
					 this.lblMesg.Text=this.lblMesg.Text.Insert(0,"System cannot delete this functional designation because: <br>");
					 this.lblMesg.Visible=true;
                     Response.Write("<script>window.alert('"+ errorString +"');</script>");  
					}
					if(this.lblMesg.Text.Length==0)
					{
						string del="update t_functionaldesignation set isactive=0 where fdesigid='"+ this.DataGrid2.Items[i].Cells[1].Text +"'";
						cmd=new SqlCommand(del,con);
						cmd.ExecuteNonQuery();
					}
					con.Close();
					getFunctionalDes(this.txtDesId.Text);
					clickEvent(true);
					break;
				}
			}
         
		}

		private void lnkNewDesignation_Click(object sender, System.EventArgs e)
		{
			lblError.Visible=false;
			lblError.Text="";
			this.lblNewDes.Visible=false;
			this.lblNewDes.Text="";
			this.myRow.Visible=true;
			this.lnkNewDesignation.Visible=false;
			this.btnQueryButton.Text="Save";
			this.btnCancel.Enabled=true;
		}
      
		public void setEvent()
		{
			for(int i=0;i<this.DataGrid4.Items.Count;i++)
			{
			 LinkButton lnk=(LinkButton)this.DataGrid4.Items[i].FindControl("btnSelect");
             lnk.Attributes.Add("onclick","getDate();"); 
			}
		}

		private void txtTitle_TextChanged(object sender, System.EventArgs e)
		{
			string tempName="";
			if(this.txtTitle.Text.Length>0)
			{
				con.Open();
				//SqlCommand cmd=new SqlCommand("select distinct(f.functionaltitle) from t_functionaldesignation f,t_designation d,t_department dept where f.isactive=1 And d.status=1 And dept.status=1 And f.functionaldesignation=d.desigid And d.deptid=dept.deptid And dept.deptid='"+ this.dropDownDept.SelectedValue.ToString() +"' And f.functionaltitle like '%"+ this.txtTitle.Text +"%'",con);
				string Query="select distinct(f.functionaltitle) from t_functionaldesignation f,t_designation d,t_department dept where f.isactive=1 And d.status=1 And dept.status=1 And f.functionaldesignation=d.desigid And d.deptid=dept.deptid And dept.deptid='"+ this.dropDownDept.SelectedValue.ToString() +"' And f.functionaltitle like '%'+@functionaltitle+'%'";
				SqlCommand cmd= new SqlCommand(Query,con);
				SqlParameter _f=new SqlParameter("@functionaltitle",SqlDbType.VarChar);
				_f.Value=this.txtTitle.Text;
				cmd.Parameters.Add(_f);
				SqlDataReader r=null;
				try
				{
					r=cmd.ExecuteReader();
				}
				catch(Exception ex)
				{
				 Response.Write(ex.Message);
				}
				while(r.Read())
				{
					tempName+=r[0].ToString()+",";
				}
				r.Close();
				con.Close();
				if(tempName.Length>0)
				{
								
					Response.Write("<script>strReturn=window.showModelessDialog('SimilarDesignation.aspx?id="+ this.dropDownDept.SelectedValue.ToString() +"&name="+ this.txtTitle.Text +" ',null,'dialogWidth:380px;dialogHeight:350px');</script>");
				}
				con.Close();
			}
		}
		public void DelRec(object sender ,EventArgs e)
		{
			int idx=0;
			LinkButton request=(LinkButton)sender;
			for(int i=0;i<this.DataGrid1.Items.Count;i++)
			{
			 LinkButton lnk=(LinkButton)this.DataGrid1.Items[i].FindControl("lnkDelRec");
				if(lnk.Equals(request))
				{
				 idx=i;
                 break; 
				}
			}
			
			this.lblNewDes.Visible=false;
			this.lblNewDes.Text="";
			con.Open();
			SqlCommand cmd=new SqlCommand("select fdesigid from t_functionaldesignation where isactive=1 And functionaldesignation='"+ this.DataGrid1.Items[idx].Cells[2].Text +"'",con);
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			if(!rd.HasRows)
			{
				rd.Close();
				string verify="select pcode from t_employee where desigid='"+ this.DataGrid1.Items[idx].Cells[2].Text +"'And del=1";
				cmd=new SqlCommand(verify,con);
				rd=cmd.ExecuteReader();
				rd.Read();
				if(!rd.HasRows)
				{
					rd.Close();
					string str="update t_designation set status='"+0+"' where desigid=@Id";
					SqlCommand cm=new SqlCommand(str,con);
					cm.Parameters.Add(new SqlParameter("@Id",SqlDbType.VarChar,20));
					cm.Parameters["@Id"].Value=this.DataGrid1.Items[idx].Cells[2].Text;
					cm.ExecuteNonQuery();
					this.DataGrid1.EditItemIndex=-1;
					BindGrid();
					con.Close();
					this.lblError.Visible=false;
					this.lblError.Text="";
				}
				else
				{
					this.lblError.Visible=true;
					this.lblError.Text="Employee exist on selected designation therefore you can not delete this designation ";  
				    Response.Write("<script>window.alert('"+ lblError.Text +"');</script>");
				}
			}
			else
			{
				this.lblError.Visible=true;
				this.lblError.Text="Functional designation exist on selected designation therefore you can not delete this designation ";  
			    Response.Write("<script>window.alert('"+ lblError.Text +"');</script>");
			}
		}
		public void clickEvents()
		{
			for(int i=0;i<this.DataGrid1.Items.Count;i++)
				{
					LinkButton lnk=(LinkButton)this.DataGrid1.Items[i].FindControl("lnkDelRec");
					lnk.Attributes.Add("onclick","return confirm('Are you sure to delete designation ? ');"); 
				}
		}

		private void DataGrid2_SortCommand(object source, System.Web.UI.WebControls.DataGridSortCommandEventArgs e)
		{
			fSort=e.SortExpression.ToString();
			getFunctionalDes(Session["tempDept"].ToString());
		}

		public int Count()
		{
         count++; 
		 return count;
		}

		private void CustomValidator2_ServerValidate(object source, System.Web.UI.WebControls.ServerValidateEventArgs args)
		{
			args.IsValid = (args.Value !="0");
		}
		protected override void LoadViewState(object savedState)
		{
			object[] allStates = (object[]) savedState;
			base.LoadViewState(allStates[0]);
			_refreshState = (bool) allStates[1];
			try
			{
				_isRefresh = _refreshState == (bool) Session["__ISREFRESH"];
			}
			catch(Exception)
			{
				Response.Redirect("../Login.aspx");
			}

		}

		protected override object SaveViewState()
		{
			Session["__ISREFRESH"] = _refreshState;
			object[] allStates = new object[2];
			allStates[0] = base.SaveViewState();
			allStates[1] = !_refreshState;
			return allStates;
		}

	}
}
