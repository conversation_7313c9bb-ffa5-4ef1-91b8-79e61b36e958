using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;
using System.IO;
namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for EmployeInfo.
	/// </summary>
	public class RequestEmployeInfo : System.Web.UI.Page
	{
		private bool _refreshState;
		private bool _isRefresh;

		public bool IsRefresh
		{
			get
			{
				return _isRefresh;
			}
		}
		protected System.Web.UI.WebControls.ImageButton ImageButton1;
		protected System.Web.UI.WebControls.ImageButton ImageButton2;
		protected System.Web.UI.WebControls.ImageButton ImageButton3;
		protected System.Web.UI.WebControls.Panel pnlPersonal;
		protected System.Web.UI.WebControls.Label lblName;
		protected System.Web.UI.WebControls.Label lblExtension;
		protected System.Web.UI.WebControls.Label lblDepartment;
		protected System.Web.UI.WebControls.Label lblDesignation;
		protected System.Web.UI.WebControls.Label lblDob;
		protected System.Web.UI.WebControls.Label lblDoJ;
		protected System.Web.UI.WebControls.Label lblEmail;
		protected System.Web.UI.WebControls.Label lblCity;
		protected System.Web.UI.WebControls.Label lblcontactNo;
		protected System.Web.UI.WebControls.Label lblMobileNo;
		protected System.Web.UI.WebControls.Label lblNICNew;
		protected System.Web.UI.WebControls.Label lblNICOld;
		protected System.Web.UI.WebControls.Label lblEmailPersonal;
		protected System.Web.UI.WebControls.TextBox txtExtension;
		protected System.Web.UI.WebControls.TextBox txtDepartment;
		protected System.Web.UI.WebControls.TextBox txtDesignation;
		protected System.Web.UI.WebControls.TextBox txtDOB;
		protected System.Web.UI.WebControls.TextBox txtDOJ;
		protected System.Web.UI.WebControls.TextBox txtEmailComp;
		protected System.Web.UI.WebControls.TextBox txtCity;
		protected System.Web.UI.WebControls.TextBox txtEmailPersonal;
		protected System.Web.UI.WebControls.Panel pnlEmployeeInfo;
		protected System.Web.UI.WebControls.Panel pnlFamilyInfo;
		protected System.Web.UI.WebControls.TextBox TextBox25;
		protected System.Web.UI.WebControls.TextBox TextBox26;
		protected System.Web.UI.WebControls.TextBox TextBox27;
		protected System.Web.UI.WebControls.TextBox TextBox28;
		protected System.Web.UI.WebControls.TextBox TextBox29;
		protected System.Web.UI.WebControls.TextBox TextBox30;
		protected System.Web.UI.WebControls.Label lblEmpInfoCode;
		protected System.Web.UI.WebControls.Label lblEmpInfoTOE;
		protected System.Web.UI.WebControls.Label lblEmpInfoDOJ;
		protected System.Web.UI.WebControls.Label lblEmpInfoName;
		protected System.Web.UI.WebControls.Label lblEmpInfoSessi;
		protected System.Web.UI.WebControls.Label lblGender;
		protected System.Web.UI.WebControls.Label lblBloodGrp;
		protected System.Web.UI.WebControls.Label lblPassport;
		protected System.Web.UI.WebControls.Label lblAddress;
		protected System.Web.UI.WebControls.Label lblMaritialStat;
		protected System.Web.UI.WebControls.Label lblReligion;
		protected System.Web.UI.WebControls.Label lblKin;
		protected System.Web.UI.WebControls.Label lblBankAcctNo;
		protected System.Web.UI.WebControls.Label lblNTN;
		protected System.Web.UI.WebControls.Label lblAccountDetails;
		protected System.Web.UI.WebControls.Label lblRecord;
		protected System.Web.UI.WebControls.ImageButton ImageButton4;
		protected System.Web.UI.WebControls.Panel pnlEducation;
		protected System.Web.UI.WebControls.Label lblEdBoRecord;
		protected System.Web.UI.WebControls.Button btnNew;
		protected System.Web.UI.WebControls.Button btnFupdate;
		protected System.Web.UI.WebControls.Button btnEdUpdate;
		protected System.Web.UI.WebControls.Button btnEdNew;
		protected System.Web.UI.WebControls.DataGrid DataGrid2;
		protected System.Web.UI.WebControls.DataGrid DataGrid4;
		protected System.Web.UI.WebControls.DataGrid DataGrid1;
		protected System.Web.UI.WebControls.DataGrid DataGrid3;
		protected System.Web.UI.WebControls.Label Label1;
		protected System.Web.UI.WebControls.Label lblFName;
		protected System.Web.UI.WebControls.Label Label2;
		protected System.Web.UI.WebControls.Label Label3;
		protected System.Web.UI.WebControls.Label Label4;
		protected System.Web.UI.WebControls.Label Label5;
		protected System.Web.UI.WebControls.Label Label6;
		protected System.Web.UI.WebControls.Label Label7;
		protected System.Web.UI.WebControls.Label Label8;
		protected System.Web.UI.WebControls.Label Label9;
		protected System.Web.UI.WebControls.Label Label10;
		protected System.Web.UI.WebControls.Label Label11;
		protected System.Web.UI.WebControls.Label Label12;
		protected System.Web.UI.WebControls.Label Label13;
		protected System.Web.UI.WebControls.Label Label14;
		protected System.Web.UI.WebControls.Label Label15;
		protected System.Web.UI.WebControls.Label Label16;
		protected System.Web.UI.WebControls.Label Label17;
		protected System.Web.UI.WebControls.RadioButtonList rdDOB;
		protected System.Web.UI.WebControls.RadioButtonList rdFname;
		protected System.Web.UI.WebControls.RadioButtonList rdGender;
		protected System.Web.UI.WebControls.RadioButtonList rdBloodGrp;
		protected System.Web.UI.WebControls.RadioButtonList rdReligion;
		protected System.Web.UI.WebControls.RadioButtonList rdPassport;
		protected System.Web.UI.WebControls.RadioButtonList rdAddress;
		protected System.Web.UI.WebControls.RadioButtonList rdMaritialStat;
		protected System.Web.UI.WebControls.RadioButtonList rdEmail;
		protected System.Web.UI.WebControls.RadioButtonList rdNTN;
		protected System.Web.UI.WebControls.RadioButtonList rdContact;
		protected System.Web.UI.WebControls.RadioButtonList rdMobile;
		protected System.Web.UI.WebControls.RadioButtonList rdNICNew;
		protected System.Web.UI.WebControls.RadioButtonList rdNICOld;
		protected System.Web.UI.WebControls.RadioButtonList rdKin;
		protected System.Web.UI.WebControls.RadioButtonList rdBnkNo;
		protected System.Web.UI.WebControls.RadioButtonList rdAcctDetails;
		protected System.Web.UI.WebControls.Label Label18;
		protected System.Web.UI.WebControls.Label Label19;
		protected System.Web.UI.WebControls.Label Label24;
		protected System.Web.UI.WebControls.Label Label30;
		protected System.Web.UI.WebControls.Label Label34;
		protected System.Web.UI.WebControls.Label Label23;
		protected System.Web.UI.WebControls.Label lblEmpInfoDesignation;
		protected System.Web.UI.WebControls.Label Label32;
		protected System.Web.UI.WebControls.Label lblEmpInfoEmail;
		protected System.Web.UI.WebControls.Label Label33;
		protected System.Web.UI.WebControls.Label lblEmpInfoEObi;
		protected System.Web.UI.WebControls.Label Label25;
		protected System.Web.UI.WebControls.Label lblEmpInfoCity;
		protected System.Web.UI.WebControls.RadioButtonList rdoEmpCode;
		protected System.Web.UI.WebControls.RadioButtonList rdoEmpName;
		protected System.Web.UI.WebControls.RadioButtonList rdoDesignation;
		protected System.Web.UI.WebControls.RadioButtonList rdoDOJ;
		protected System.Web.UI.WebControls.RadioButtonList rdoStation;
		protected System.Web.UI.WebControls.RadioButtonList rdoEmailOffical;
		protected System.Web.UI.WebControls.RadioButtonList rdoTOE;
		protected System.Web.UI.WebControls.RadioButtonList rdoEOBINO;
		protected System.Web.UI.WebControls.RadioButtonList rdoSessi;
		SqlConnection con;
		protected System.Web.UI.WebControls.Button btnReject;
		protected System.Web.UI.WebControls.Button btnUpdate;
		protected System.Web.UI.WebControls.Image Image1;
		protected System.Web.UI.WebControls.RadioButtonList rdoRequestPhoto;
		protected System.Web.UI.WebControls.Button btnOriginalImg;
		protected System.Web.UI.WebControls.Button btnRequestImg;
		protected System.Web.UI.WebControls.DropDownList txtDegree;
		protected System.Web.UI.WebControls.DropDownList txtInstitute;
		protected System.Web.UI.WebControls.TextBox txtOtherDegree;
		protected System.Web.UI.WebControls.TextBox txtOtherInstitute;
		protected System.Web.UI.WebControls.TextBox txtOtherMajors;
		protected System.Web.UI.WebControls.Button btnAdd;
		protected System.Web.UI.WebControls.Label lblDegree;
		protected System.Web.UI.WebControls.Button btnInstituteAdd;
		protected System.Web.UI.WebControls.Label lblInstitute;
		protected System.Web.UI.WebControls.TextBox txtAchievements;
		protected System.Web.UI.WebControls.RadioButtonList rdoFunctionalDesignation;
		protected System.Web.UI.WebControls.RadioButtonList rdoDepartment;
		protected System.Web.UI.WebControls.RadioButtonList rdoExtension;
		protected System.Web.UI.WebControls.Label Label20;
		protected System.Web.UI.WebControls.Label lblEmpInfoExtension;
		protected System.Web.UI.WebControls.Label lblEmpInfoDept;
		protected System.Web.UI.WebControls.Label Label21;
		protected System.Web.UI.WebControls.Label lblEmpInfoFunctionalDesignation;
		protected System.Web.UI.WebControls.Label Label22;
		protected System.Web.UI.HtmlControls.HtmlInputFile file;
 
		public static string sort4;
		public static string sort;
		public static string sort2;
		protected System.Web.UI.WebControls.Label lblMsg;
		protected System.Web.UI.WebControls.RadioButtonList rdoNick;
		protected System.Web.UI.WebControls.Label Label26;
		protected System.Web.UI.WebControls.Label lblNick;
		protected System.Web.UI.WebControls.RadioButtonList rdoNationality;
		protected System.Web.UI.WebControls.Label Label28;
		protected System.Web.UI.WebControls.Label lblNationality;
		protected System.Web.UI.WebControls.TextBox txtFamilyName;
		protected System.Web.UI.WebControls.DropDownList ddDependent;
		protected System.Web.UI.WebControls.TextBox txtOccupation;
		protected System.Web.UI.WebControls.RadioButtonList rdoNationality2;
		protected System.Web.UI.WebControls.Label Label29;
		protected System.Web.UI.WebControls.Label lblNationality2;
		protected System.Web.UI.WebControls.DropDownList rdStatus;
		protected System.Web.UI.WebControls.DropDownList rdRelation;
		protected eWorld.UI.CalendarPopup CalendarPopup1;
		protected System.Web.UI.WebControls.DropDownList rdFamilyGender;
		protected System.Web.UI.WebControls.DropDownList DurationFrom;
		protected System.Web.UI.WebControls.DropDownList DurationTo;
		protected System.Web.UI.WebControls.Panel Panel2;
		protected System.Web.UI.WebControls.Label lblEmployeeNameTitle;
		protected System.Web.UI.WebControls.LinkButton lnkClose;
		protected System.Web.UI.WebControls.LinkButton lnkEclose;
		protected System.Web.UI.WebControls.RadioButtonList rdoLocation;
		protected System.Web.UI.WebControls.Label lblEmpLocation;
		protected System.Web.UI.WebControls.Label lblRempLocation;
		protected System.Web.UI.WebControls.DropDownList txtResult;
		public static string sort3;

		private bool IsPageAccessAllowed()
		{
			int WebFormID=82;
			int ProjectID=3;
			string userId="";
			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception ex)
			{
				string s=ex.Message;
				Response.Redirect("../Login.aspx");
			}

			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(ProjectID,WebFormID,userId,"Page")==true)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("../Login.aspx");
				return false;
			}
		}
		private void Page_Load(object sender, System.EventArgs e)
		{
			this.Image1.Visible=false;
			/*Response.Cache.SetCacheability(HttpCacheability.NoCache);
			Response.Cache.SetAllowResponseInBrowserHistory(false);
			if(IsPageAccessAllowed())
			{
 
			}
			else
			{
			Response.Redirect("ErrorPage.aspx");
			}*/
			con=new SqlConnection(Connection.ConnectionString);
			//Page.SmartNavigation=true;
			if(!IsPostBack)
			{
				con.Open();
				string getName="select name,pic from t_employee where pcode='"+ Request.QueryString[0].ToString() +"'";
				SqlCommand cName=new SqlCommand(getName,con);
				SqlDataReader rName=cName.ExecuteReader();
				rName.Read();
				lblEmployeeNameTitle.Text=rName[0].ToString();
				this.Image1.ImageUrl=@"..\employee\"+rName[1].ToString();
				if(rName[0].ToString().Length>0)
				{
					this.Image1.Visible=true;
				}
				rName.Close();
				con.Close();

				sort4="";
				Session["realImg"]="";
				Session["requestImg"]="";
				Session["famId"]="0";
				Session["eduId"]="0";
				Session["updId"]=0;
				Session["up"]="0";
				Session["getString"]="";
				Session["getDesicion"]="";
				Session["empRequest"]="";
				Session["perRequest"]="";
				Session["empCode"]="";
				sort="";
				sort2="";
				sort3="";
				// btnUpdate.Attributes.Add("onclick","window.opener.location=window.opener.location; window.close();");
				// btnReject.Attributes.Add("onclick","window.opener.location=window.opener.location; window.close();");
				getYears();
				Session["getString"]=Request.QueryString[0];
				Session["empCode"]=Session["getString"].ToString();
				try
				{
					Session["getDesicion"]=Request.QueryString[1];
					if(Session["getDesicion"].ToString()=="EmployeeInfo")
					{
						EmployeeInfo();
						InterfaceEmpInfo();
						this.btnUpdate.Visible=true;
						this.pnlEmployeeInfo.Visible=true; 
						this.pnlPersonal.Visible=false;
						this.pnlFamilyInfo.Visible=false;
						this.pnlEducation.Visible=false;
 
						this.ImageButton1.Visible=true;
						this.ImageButton2.Visible=false;
						this.ImageButton3.Visible=false;
						this.ImageButton4.Visible=false;
					}
					if(Session["getDesicion"].ToString()=="PersonalInfo")
					{
						PersonalInfo();
						this.pnlEmployeeInfo.Visible=false; 
						this.pnlPersonal.Visible=true;
						this.pnlFamilyInfo.Visible=false;
						this.pnlEducation.Visible=false;
 
						this.ImageButton1.Visible=false;
						this.ImageButton2.Visible=true;
						this.ImageButton3.Visible=false;
						this.ImageButton4.Visible=false;
					}
					if(Session["getDesicion"].ToString()=="FamilyInfo")
					{
						FamilyInfo();
						FamilyRequest();
						this.pnlEmployeeInfo.Visible=false; 
						this.pnlPersonal.Visible=false;
						this.pnlFamilyInfo.Visible=true;
						this.pnlEducation.Visible=false;
						this.btnUpdate.Visible=false;
						this.btnReject.Visible=false;
 
						this.btnFupdate.Visible=true;
						this.btnNew.Visible=false;

						this.ImageButton1.Visible=false;
						this.ImageButton2.Visible=false;
						this.ImageButton3.Visible=true;
						this.ImageButton4.Visible=false;
					}
					if(Session["getDesicion"].ToString()=="EducationInfo")
					{
						EducationalInfo();
						EducationRequest();
						this.pnlEmployeeInfo.Visible=false; 
						this.pnlPersonal.Visible=false;
						this.pnlFamilyInfo.Visible=false;
						this.pnlEducation.Visible=true;
						this.btnUpdate.Visible=false;
						this.btnReject.Visible=false;
						this.btnEdNew.Visible=false;
						this.btnEdUpdate.Visible=true;
						txtOtherMajors.Visible=true;
						this.lblDegree.Visible=false;
						this.lblInstitute.Visible=false;
						this.btnAdd.Visible=false;
						this.btnInstituteAdd.Visible=false;

						this.ImageButton1.Visible=false;
						this.ImageButton2.Visible=false;
						this.ImageButton3.Visible=false;
						this.ImageButton4.Visible=true;
					}
 
				}
				catch(Exception ex)
				{
					string s=ex.Message;
 
					Session["getString"]=Request.QueryString[0];
					EmployeeInfo();
					PersonalInfo();
					FamilyInfo();
					FamilyRequest();
					EducationalInfo();
					EducationRequest();
				}

			}
 
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.ImageButton1.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton1_Click);
			this.ImageButton2.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton2_Click);
			this.ImageButton3.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton3_Click);
			this.ImageButton4.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton4_Click);
			this.btnOriginalImg.Click += new System.EventHandler(this.btnOriginalImg_Click);
			this.btnRequestImg.Click += new System.EventHandler(this.btnRequestImg_Click);
			this.DataGrid1.SortCommand += new System.Web.UI.WebControls.DataGridSortCommandEventHandler(this.DataGrid1_SortCommand);
			this.DataGrid1.SelectedIndexChanged += new System.EventHandler(this.DataGrid1_SelectedIndexChanged_1);
			this.DataGrid2.SortCommand += new System.Web.UI.WebControls.DataGridSortCommandEventHandler(this.DataGrid2_SortCommand);
			this.DataGrid2.SelectedIndexChanged += new System.EventHandler(this.DataGrid2_SelectedIndexChanged);
			this.btnNew.Click += new System.EventHandler(this.btnNew_Click);
			this.btnFupdate.Click += new System.EventHandler(this.btnFupdate_Click);
			this.lnkClose.Click += new System.EventHandler(this.lnkClose_Click);
			this.DataGrid3.SortCommand += new System.Web.UI.WebControls.DataGridSortCommandEventHandler(this.DataGrid3_SortCommand);
			this.DataGrid3.SelectedIndexChanged += new System.EventHandler(this.DataGrid3_SelectedIndexChanged_1);
			this.DataGrid4.SortCommand += new System.Web.UI.WebControls.DataGridSortCommandEventHandler(this.DataGrid4_SortCommand);
			this.DataGrid4.SelectedIndexChanged += new System.EventHandler(this.DataGrid4_SelectedIndexChanged);
			this.txtDegree.SelectedIndexChanged += new System.EventHandler(this.txtDegree_SelectedIndexChanged);
			this.btnAdd.Click += new System.EventHandler(this.btnAdd_Click);
			this.btnInstituteAdd.Click += new System.EventHandler(this.btnInstituteAdd_Click);
			this.btnEdNew.Click += new System.EventHandler(this.btnEdNew_Click);
			this.btnEdUpdate.Click += new System.EventHandler(this.btnEdUpdate_Click);
			this.lnkEclose.Click += new System.EventHandler(this.lnkEclose_Click);
			this.btnUpdate.Click += new System.EventHandler(this.btnUpdate_Click);
			this.btnReject.Click += new System.EventHandler(this.btnReject_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		private void ImageButton2_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			InterfacePersonal();
			InterfaceEmpInfo();
			this.btnUpdate.Visible=true;
			this.btnReject.Visible=true;
			this.pnlPersonal.Visible=true;
			this.pnlEmployeeInfo.Visible=false;
			this.pnlFamilyInfo.Visible=false;
			this.pnlEducation.Visible=false;
			this.btnUpdate.Visible=true;
			//PersonalInfo();
		}

		private void ImageButton1_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			InterfacePersonal();
			InterfaceEmpInfo();
			this.pnlPersonal.Visible=false;
			this.pnlEmployeeInfo.Visible=true;
			this.pnlFamilyInfo.Visible=false;
			this.pnlEducation.Visible=false;
			this.btnUpdate.Visible=true;
			//EmployeeInfo();
		}

		private void ImageButton3_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			this.pnlFamilyInfo.Visible=true;
			this.DataGrid1.Visible=true;
			this.pnlPersonal.Visible=false;
			this.pnlEmployeeInfo.Visible=false;
			this.pnlEducation.Visible=false;
			this.btnUpdate.Visible=false;
			this.btnFupdate.Visible=true; 
			//FamilyInfo();
 
		}

		public void PersonalInfo()
		{
			if(con.State==ConnectionState.Open)
			{
				con.Close();
			}
			Session["perRequest"]="";
			con.Open();
			//this.ddBloodGrp.Visible=true;
			string Id=Request.QueryString[0].ToString();
			//getString;
			//Session["user_id"].ToString();
			string getOriginal="SELECT dateofbirth, fathername, gender, bloodgroup, religion, passportno, address, maritialstatus, email_personal, ntnno, telephone, mobile, nic_new, nic_old, "+
				"kin, bankaccountno, bankaccountdetails,pic,nick,nationalityid=case nationality when nationality then(select c.countryid from t_country c where c.countryid=dbo.t_Employee.nationality)end,countryname=case pcode when pcode then(select c.nationality from t_country c where c.countryid=dbo.t_Employee.nationality)end,nationalityid2=case nationality2 when nationality2 then(select c.countryid from t_country c where c.countryid=dbo.t_Employee.nationality2)end,countryname2=case pcode when pcode then(select c.nationality from t_country c where c.countryid=dbo.t_Employee.nationality2)end FROM dbo.t_Employee where dbo.t_employee.pcode='"+ Id +"'";
			SqlCommand cmd=new SqlCommand(getOriginal,con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				if(rd[0].ToString().Length>0)
				{
					string []tempArr=rd[0].ToString().Split(' ');
					this.lblName.Text=tempArr[0];
				}
 
				this.lblFName.Text=rd[1].ToString();
				if(rd[2].ToString()=="1")
				{
					this.lblGender.Text="Male";
				}
				if(rd[2].ToString()=="2")
				{
					this.lblGender.Text="Female";
				}
 
				if(rd[3].ToString()=="1")
				{
					this.lblBloodGrp.Text="A+";
				}
				if(rd[3].ToString()=="2")
				{
					this.lblBloodGrp.Text="A-";
				}
				if(rd[3].ToString()=="3")
				{
					this.lblBloodGrp.Text="B+";
				}
				if(rd[3].ToString()=="4")
				{
					this.lblBloodGrp.Text="B-";
				}
				if(rd[3].ToString()=="5")
				{
					this.lblBloodGrp.Text="AB+";
				}
				if(rd[3].ToString()=="6")
				{
					this.lblBloodGrp.Text="AB-";
				}
				if(rd[3].ToString()=="7")
				{
					this.lblBloodGrp.Text="O+";
				}
				if(rd[3].ToString()=="8")
				{
					this.lblBloodGrp.Text="O-";
				} 
 
				//lblReligion.Text=rd[4].ToString();
				if(rd[4].ToString()=="1")
				{
					this.lblReligion.Text="Islam";
				}
				if(rd[4].ToString()=="2")
				{
					this.lblReligion.Text="Christianity";
				}
				if(rd[4].ToString()=="3")
				{
					this.lblReligion.Text="Buddhism";
				}
				if(rd[4].ToString()=="4")
				{
					this.lblReligion.Text="Zoroastrian";
				}
				if(rd[4].ToString()=="5")
				{
					this.lblReligion.Text="Jewish";
				}
				if(rd[4].ToString()=="6")
				{
					this.lblReligion.Text="Hinduism";
				}
				if(rd[4].ToString()=="7")
				{
					this.lblReligion.Text="Others";
				}
 
				lblPassport.Text=rd[5].ToString();
				lblAddress.Text=rd[6].ToString();
 
				if(rd[7].ToString()=="1")
				{
					this.lblMaritialStat.Text="Single";
				}
				if(rd[7].ToString()=="2")
				{
					this.lblMaritialStat.Text="Married";
				}
				if(rd[7].ToString()=="3")
				{
					this.lblMaritialStat.Text="Divorced";
				}
 
				lblEmail.Text=rd[8].ToString();
				lblNTN.Text=rd[9].ToString();
				lblcontactNo.Text=rd[10].ToString();
				lblMobileNo.Text=rd[11].ToString();
				lblNICNew.Text=rd[12].ToString();
				lblNICOld.Text=rd[13].ToString();
				lblKin.Text=rd[14].ToString();
				lblBankAcctNo.Text=rd[15].ToString();
				this.lblAccountDetails.Text=rd[16].ToString();
				Session["realImg"]=rd[17].ToString();
				lblNick.Text=rd[18].ToString();
				lblNationality.ToolTip=rd[19].ToString();
				lblNationality.Text=rd[20].ToString();
				lblNationality2.Text=rd[21].ToString();
				lblNationality2.Text=rd[22].ToString();
 
			}
			rd.Close();
			string getRequest="SELECT dateofbirth, fathername, gender, bloodgroup, religion, passportno, address, maritialstatus, email_personal, ntnno, telephone, mobile, nic_new, nic_old, "+
				"kin, bankaccountno, bankaccountdetails,requestdate,requestphoto,nick,nationalityid=case nationality when nationality then(select c.countryid from t_country c where c.countryid=dbo.t_personalinfo.nationality)end,countryname=case pcode when pcode then(select c.nationality from t_country c where c.countryid=dbo.t_personalinfo.nationality)end,nationalityid2=case nationality2 when nationality2 then(select c.countryid from t_country c where c.countryid=dbo.t_personalinfo.nationality2)end,countryname2=case pcode when pcode then(select c.nationality from t_country c where c.countryid=dbo.t_personalinfo.nationality2)end FROM dbo.t_personalinfo where dbo.t_personalinfo.pcode='"+ Id +"' And isactive='1'";
			SqlCommand cm=new SqlCommand(getRequest,con);
			SqlDataReader r=cm.ExecuteReader();
			while(r.Read())
			{
 
				if(r[0].ToString().Length>0)
				{
					string []Arr=r[0].ToString().Split(' ');
					this.Label1.Text=Arr[0];
				}
				this.Label2.Text=r[1].ToString();
 
				if(r[2].ToString()=="1")
				{
 
					Label3.Text="Male";
					Label3.ToolTip=r[2].ToString();
				}
				if(r[2].ToString()=="2")
				{
					Label3.Text="Female";
					Label3.ToolTip=r[2].ToString();
				}
 
				if(r[3].ToString()=="1")
				{
					this.Label4.Text="A+";
					Label4.ToolTip=r[3].ToString();
				}
				if(r[3].ToString()=="2")
				{
					this.Label4.Text="A-";
					Label4.ToolTip=r[3].ToString();
				}
				if(r[3].ToString()=="3")
				{
					this.Label4.Text="B+";
					Label4.ToolTip=r[3].ToString();
				}
				if(r[3].ToString()=="4")
				{
					this.Label4.Text="B-";
					Label4.ToolTip=r[3].ToString();
				}
				if(r[3].ToString()=="5")
				{
					this.Label4.Text="AB+";
					Label4.ToolTip=r[3].ToString();
				}
				if(r[3].ToString()=="6")
				{
					this.Label4.Text="AB-";
					Label4.ToolTip=r[3].ToString();
				}
				if(r[3].ToString()=="7")
				{
					this.Label4.Text="O+";
					Label4.ToolTip=r[3].ToString();
				}
				if(r[3].ToString()=="8")
				{
					this.Label4.Text="O-";
					Label4.ToolTip=r[3].ToString();
				}
 
				if(r[4].ToString()=="1")
				{
					this.Label5.Text="Islam";
					Label5.ToolTip=r[4].ToString();
				}
				if(r[4].ToString()=="2")
				{
					this.Label5.Text="Christianity";
					Label5.ToolTip=r[4].ToString();
				}
				if(r[4].ToString()=="3")
				{
					this.Label5.Text="Buddhism";
					Label5.ToolTip=r[4].ToString();
				}
				if(r[4].ToString()=="4")
				{
					this.Label5.Text="Zoroastrian";
					Label5.ToolTip=r[4].ToString();
				}
				if(r[4].ToString()=="5")
				{
					this.Label5.Text="Jewish";
					Label5.ToolTip=r[4].ToString();
				}
				if(r[4].ToString()=="6")
				{
					this.Label5.Text="Hinduism";
					Label5.ToolTip=r[4].ToString();
				}
				if(r[4].ToString()=="7")
				{
					this.Label5.Text="Others";
					Label5.ToolTip=r[4].ToString();
				}
				Label6.Text=r[5].ToString();
				Label7.Text=r[6].ToString();
 
				if(r[7].ToString()=="1")
				{
					this.Label8.Text="Single";
					Label8.ToolTip=r[7].ToString();
				}
				if(r[7].ToString()=="2")
				{
					this.Label8.Text="Married";
					Label8.ToolTip=r[7].ToString();
				}
				if(r[7].ToString()=="3")
				{
					this.Label8.Text="Divorced";
					Label8.ToolTip=r[7].ToString();
				}
 
				Label9.Text=r[8].ToString();
				Label10.Text=r[9].ToString();
				Label11.Text=r[10].ToString();
				Label12.Text=r[11].ToString();
				Label13.Text=r[12].ToString();
				Label14.Text=r[13].ToString();
				Label15.Text=r[14].ToString();
				Label16.Text=r[15].ToString();
				Label17.Text=r[16].ToString();
				Session["perRequest"]=r[17].ToString();
				Session["requestImg"]=r[18].ToString();

				this.Label26.Text=r[19].ToString();
				this.Label28.ToolTip=r[20].ToString();
				this.Label28.Text=r[21].ToString();
				this.Label29.ToolTip=r[22].ToString();
				this.Label29.Text=r[23].ToString();

			}
			r.Close(); 
			InterfacePersonal();
			con.Close(); 
		}

		public void EmployeeInfo()
		{
			con.Close(); 
			Session["empRequest"]="";
			con.Open();
			string getOriginal="SELECT dbo.t_Employee.pcode, dbo.t_Employee.name, dbo.t_Employee.extension, dbo.t_Employee.compansatoryoff, dbo.t_Designation.designation, "+
				"dbo.t_Employee.dateofjoin, dbo.t_Employee.insurancecode, dbo.t_Employee.dateofconfirmation, dbo.t_Employee.typeofemployment, "+
				"dbo.t_Employee.bondpaper, dbo.t_Employee.email_official, dbo.t_Employee.eobino, dbo.t_Employee.sessino, dbo.t_Employee.others ,dbo.t_Employee.operatingcity "+
				"FROM dbo.t_Employee INNER JOIN "+
				"dbo.t_Designation ON dbo.t_Employee.desigid = dbo.t_Designation.desigid WHERE (dbo.t_Employee.pcode = '"+Request.QueryString[0].ToString()+"')";
			SqlCommand cmd=new SqlCommand(getOriginal,con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				lblEmpInfoCode.Text=rd[0].ToString();
				lblEmpInfoName.Text=rd[1].ToString();
				lblEmpInfoExtension.Text=rd[2].ToString();
				if(rd[5].ToString().Length>0)
				{
					string []tempArr=rd[5].ToString().Split(' ');
					lblEmpInfoDOJ.Text=tempArr[0];
				}
				if(rd[8].ToString().Length>0)
				{
					if(rd[8].ToString()=="1")
					{
						lblEmpInfoTOE.Text="Permanent";
					}
					if(rd[8].ToString()=="2")
					{
						lblEmpInfoTOE.Text="Contractual";
					}
					if(rd[8].ToString()=="3")
					{
						lblEmpInfoTOE.Text="Retainer";
					}
 
				}
				//lblEmpInfoBondPaper.Text=rd[9].ToString();
				lblEmpInfoEmail.Text=rd[10].ToString();
				lblEmpInfoEObi.Text=rd[11].ToString();
				lblEmpInfoSessi.Text=rd[12].ToString();
				//TextBox1.Text=rd[13].ToString();
				lblEmpLocation.Text=rd[14].ToString();
			}
			rd.Close();
 

			string getStation="select c.cityid,c.cityname from t_employee e,t_city c where e.station=c.cityid And e.pcode='"+Request.QueryString[0].ToString()+"'";
			SqlCommand cCity=new SqlCommand(getStation,con);
			SqlDataReader rCity=cCity.ExecuteReader();
			rCity.Read();
			if(rCity.HasRows)
			{
				this.lblEmpInfoCity.ToolTip=rCity[0].ToString();
				this.lblEmpInfoCity.Text=rCity[1].ToString(); 
			}
			rCity.Close();
 
			string OriginalDes="select dept.deptname,d.designation,f.functionaltitle,f.fdesigid from t_department dept,t_designation d,t_functionaldesignation f where dept.status=1 and d.status=1 And f.isactive=1 And d.deptid=dept.deptid And f.functionaldesignation=d.desigid And f.fdesigid=(select fdesigid from t_designationhistory where pcode='"+ Request.QueryString[0].ToString() +"' And isactive=1)";
			SqlCommand cDes=new SqlCommand(OriginalDes,con);
			SqlDataReader rDes=cDes.ExecuteReader();
			while(rDes.Read())
			{
				this.lblEmpInfoDept.Text=rDes[0].ToString();
				this.lblEmpInfoDesignation.Text=rDes[1].ToString();
				this.lblEmpInfoFunctionalDesignation.Text=rDes[2].ToString();
				this.lblEmpInfoFunctionalDesignation.ToolTip=rDes[3].ToString();
			}
			rDes.Close();

			//string getRequest="SELECT pcode, name, extension, dateofjoin, insurancecode, emailoffcial, typeofemployment, eobino, sessinfo, other,requestdate,station=case pcode when pcode then (select c.cityname from t_city c,t_empinfo e where e.station=c.cityid And e.isrequested=1 And e.isactive=1 And c.status=1 And e.pcode='"+ Request.QueryString[0].ToString() +"')end,stationid=case pcode when pcode then (select c.cityname from t_city c,t_empinfo e where e.station=c.cityid And e.isrequested=1 And e.isactive=1 And c.status=1 And e.pcode='"+ Request.QueryString[0].ToString() +"')end,location FROM dbo.t_empinfo WHERE (pcode ='"+ Request.QueryString[0].ToString() +"' AND isrequested = '1' AND isactive = '1')";
			string getRequest="SELECT pcode, name, extension, dateofjoin, insurancecode, emailoffcial, typeofemployment, eobino, sessinfo, other, requestdate, CASE station WHEN NULL " +
				" THEN '' ELSE CAST(station AS varchar) END AS stationid, CASE station WHEN NULL THEN '' ELSE " +
				" (SELECT cityname  FROM t_city WHERE cityid = t_empinfo.station) END AS Station, location " +
				" FROM dbo.t_empinfo" +
				" WHERE (pcode = '"+ Request.QueryString[0].ToString() +"') AND (isrequested = '1') AND (isactive = '1') ";
				
			SqlCommand cRequest=new SqlCommand(getRequest,con);
			SqlDataReader rRequest=cRequest.ExecuteReader();
			while(rRequest.Read())
			{
				this.Label18.Text=rRequest[0].ToString();
				this.Label19.Text=rRequest[1].ToString();
				this.Label20.Text=rRequest[2].ToString();
				if(rRequest[3].ToString().Length>0)
				{
					string []Arr=rRequest[3].ToString().Split(' ');
					this.Label24.Text=Arr[0];
				}
				//this.Label28.Text=rRequest[4].ToString();
				//this.Label27.Text=rRequest[4].ToString();
				this.Label32.Text=rRequest[5].ToString();
 
				if(rRequest[6].ToString().Length>0)
				{
					if(rRequest[6].ToString()=="1")
					{
						this.Label30.Text="Permanent";
						this.Label30.ToolTip="1";
					}
					if(rRequest[6].ToString()=="2")
					{
						this.Label30.Text="Contractual";
						this.Label30.ToolTip="2";
					}
					if(rRequest[6].ToString()=="3")
					{
						this.Label30.Text="Retainer";
						this.Label30.ToolTip="3";
					}
				}
				this.Label33.Text=rRequest[7].ToString();
				this.Label34.Text=rRequest[8].ToString();
				//this.Label35.Text=rRequest[9].ToString();
				Session["empRequest"]=rRequest[10].ToString();
				this.Label25.Text=rRequest[11].ToString();
				this.Label25.Text=rRequest[12].ToString(); 
				lblRempLocation.Text=rRequest[13].ToString();
			}
			rRequest.Close();
			string requestDes="select dept.deptid,dept.deptname,d.desigid,d.designation,f.fdesigid,f.functionaltitle from t_department dept,t_designation d,t_functionaldesignation f,t_empinfo emp where dept.status=1 and d.status=1 And f.isactive=1 And d.deptid=dept.deptid And f.functionaldesignation=d.desigid And f.fdesigid=emp.fdesigid And emp.pcode='"+Request.QueryString[0].ToString()+"' And emp.isrequested=1 And emp.isactive=1 ";
			SqlCommand requestCom=new SqlCommand(requestDes,con);
			SqlDataReader requestRead=requestCom.ExecuteReader();
			while(requestRead.Read())
			{
				this.Label21.ToolTip=requestRead[0].ToString();
				this.Label21.Text=requestRead[1].ToString();
 
				this.Label23.ToolTip=requestRead[2].ToString();
				this.Label23.Text=requestRead[3].ToString();
 
				this.Label22.ToolTip=requestRead[4].ToString();
				this.Label22.Text=requestRead[5].ToString();
			}
			requestRead.Close();
			con.Close();
			InterfaceEmpInfo();
 
		}

		public void FamilyInfo()
		{
			this.DataGrid1.Visible=true;
			con.Open();
			//string getFamily="select name,relationship,maritialstatus,gender,occupation,pcode,fdid from t_familydetails where isactive='1'";
			string empId=Request.QueryString[0].ToString();
			//Session["user_id"].ToString();
			//Request.QueryString["id"].ToString();
////			string getFamily=
////				"select name,relationship = case relationship when '2' then 'Father' " + 
////				" when '3' then 'Mother' " + 
////				" when '4' then 'Son' " + 
////				" when '5' then 'Daughter' " + 
////				" END, " + 
////				" maritialstatus=case maritialstatus when '1' then 'Single' " + 
////				" when '2' then 'Married' " + 
////				" when '3' then 'Divorced' " + 
////				" END, " + 
////				" gender=case gender WHEN '1' THEN 'Male' " + 
////				" when '2' then 'Female' " + 
////				" END ,occupation,DOB,dependent=case dependent when '1' then 'Yes' when '2' then 'No' end,pcode,fdid " + 
////				" from t_familydetails where isactive='1' and pcode='" + empId + "'";
			string getFamily="SELECT fdid, pcode, name, " +
				" CASE relationship WHEN 2 THEN 'Husband' WHEN 3 THEN 'Wife' WHEN 4 THEN 'Son' WHEN 5 THEN 'Daughter' END AS Relationship, "+
				" CASE maritialstatus WHEN 1 THEN 'Single' WHEN 2 THEN 'Married' WHEN 3 THEN 'Divorced' END AS maritialstatus, "+
				" CASE gender WHEN 1 THEN 'Male' WHEN 2 THEN 'Female' END AS Gender, occupation, dob, Dependent " +
				" FROM dbo.t_FamilyDetails fd " +
				" WHERE (relationship IN (2, 3, 4, 5)) AND (isactive = 1) AND (pcode='"+empId.Trim()+"')";
			SqlDataAdapter rd=new SqlDataAdapter(getFamily,con);
			DataSet dsFamily=new DataSet();
 
			rd.Fill(dsFamily,"FamilyInfo");
			DataView dv=new DataView(dsFamily.Tables["FamilyInfo"]);
			dv.Sort=sort;
			this.DataGrid1.DataSource=dv;
			this.DataGrid1.DataBind();
			int getItems=this.DataGrid1.Items.Count;
			// this.DataGrid1.Columns[5].Visible=false;
			// this.DataGrid1.Columns[6].Visible=false;
			// this.DataGrid1.Columns[7].Visible=false;
			if(getItems<=0)
			{
				this.lblRecord.Visible=true;
			}
			else
			{
				this.lblRecord.Visible=false;
			}
			con.Close();
		}

		public void EducationalInfo()
		{
			if(con.State==ConnectionState.Open)
			{
				con.Close();
			}
			con.Open();
			string empId=Request.QueryString[0].ToString();
			//Session["user_id"].ToString();
			//Request.QueryString["id"].ToString();
			this.DataGrid3.Visible=true; 
			// string getInfo="select e.eduinfoid,degree=case e.degree when '0' then (select temp.otherdegree from t_educationinfo temp where temp.eduinfoid=e.eduinfoid ) else (select d.degree from t_degree d where d.id=e.degree) end , institute=case e.institute when '0' then (select temp.otherinstitute from t_educationinfo temp where temp.eduinfoid=e.eduinfoid) else (select temp.institute_name from t_institute temp where temp.id=e.institute)end ,e.yearpassing,e.result,e.duration,majors=case e.majors when '0' then (select temp.othermajors from t_educationinfo temp where temp.eduinfoid=e.eduinfoid) else (select major from t_majors where id=e.majors) end ,e.achievements,type from t_educationinfo e where e.isactive='1' And e.pcode='"+empId+"'";
			string getInfo="select e.eduinfoid,degree=case e.degree when '-1' then (select temp.otherdegree from t_educationinfo temp where temp.eduinfoid=e.eduinfoid ) else (select d.degree from t_degree d where d.id=e.degree) end , institute=case e.institute when '-1' then (select temp.otherinstitute from t_educationinfo temp where temp.eduinfoid=e.eduinfoid) else (select temp.institute_name from t_institute temp where temp.id=e.institute)end ,e.durationto,e.result,e.durationfrom,e.majors,e.achievements,type from t_educationinfo e where e.isactive='1' And e.pcode='"+empId+"'";
			SqlDataAdapter rd=new SqlDataAdapter(getInfo,con);
			DataSet rSet=new DataSet();
			rd.Fill(rSet,"EdutionalInfo");
			DataView dv=new DataView(rSet.Tables["EdutionalInfo"]);
			dv.Sort=sort3;
			this.DataGrid3.DataSource=dv;
			this.DataGrid3.DataBind();
			int getItems=this.DataGrid3.Items.Count;
			if(getItems<=0)
			{
				this.lblEdBoRecord.Visible=true;
			}
			else
			{
				this.lblEdBoRecord.Visible=false;
			}
			// this.DataGrid3.Columns[7].Visible=false; 
			// this.DataGrid3.Columns[8].Visible=false;
			getCurrentDegree();
			getCurrentInstitute();
 
			con.Close(); 
		}

		private void ddDepartment_SelectedIndexChanged(object sender, System.EventArgs e)
		{
 
 
		}

		private void DataGrid1_SelectedIndexChanged(object sender, System.EventArgs e)
		{
 
		}

		private void ImageButton4_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			this.pnlEducation.Visible=true;
			this.pnlEmployeeInfo.Visible=false;
			this.pnlFamilyInfo.Visible=false;
			this.pnlPersonal.Visible=false;
			this.btnUpdate.Visible=false;
		}

		private void DataGrid3_SelectedIndexChanged(object sender, System.EventArgs e)
		{
 

		}

		private void btnUpdate_Click(object sender, System.EventArgs e)
		{
			if(!_isRefresh)
			{
				string sqlQuery="";
				string sqlQuery2="update t_employee set ";
				string FunctionalDesignation="";
				if(this.pnlEmployeeInfo.Visible)
				{
					sqlQuery=Session["empRequest"].ToString();
				}
				if(this.pnlPersonal.Visible)
				{
					sqlQuery=Session["perRequest"].ToString();
				}
 
				string sql2="";
				string rejectFields="";
				string MessageToBulletin="The given request to update your profile have some fields rejected which are ";
				if(this.rdDOB.SelectedValue=="0")
				{
					sql2+=",dateofbirth='"+this.Label1.Text+"'";
				}
				else
				{
					if(this.rdDOB.Visible) 
						rejectFields+="DateOfBirth, ";
				}

				if(this.rdFname.SelectedValue=="0")
				{
					sql2+=",fathername='"+this.Label2.Text+"'";
				}
				else
				{
					if(this.rdFname.Visible)
						rejectFields+="FatherName, ";
				} 

				if(this.rdGender.SelectedValue=="0")
				{
					sql2+=",gender='"+this.Label3.ToolTip+"'";
				}
				else
				{
					if(this.rdGender.Visible)
						rejectFields+="Gender, ";
				}

				if(this.rdBloodGrp.SelectedValue=="0")
				{
					sql2+=",bloodgroup='"+this.Label4.ToolTip+"'";
				}
				else
				{
					if(this.rdBloodGrp.Visible)
						rejectFields+="Bloodgroup, ";
				}

				if(this.rdReligion.SelectedValue=="0")
				{
					sql2+=",religion='"+this.Label5.ToolTip+"'";
				}
				else
				{
					if(this.rdReligion.Visible)
						rejectFields+="Religion, ";
				}

				if(this.rdPassport.SelectedValue=="0")
				{
					sql2+=",passportno='"+this.Label6.Text+"'";
				}
				else
				{
					if(this.rdPassport.Visible)
						rejectFields+="Passport, ";
				} 

				if(this.rdAddress.SelectedValue=="0")
				{
					sql2+=",address='"+this.Label7.Text+"'";
				}
				else
				{
					if(this.rdAddress.Visible)
						rejectFields+="Address, ";
				}

				if(this.rdMaritialStat.SelectedValue=="0")
				{
					sql2+=",maritialstatus='"+this.Label8.ToolTip+"'";
				}
				else
				{
					if(this.rdMaritialStat.Visible)
						rejectFields+="MaritialStatus, ";
				}

				if(this.rdEmail.SelectedValue=="0")
				{
					sql2+=",email_personal='"+this.Label9.Text+"'";
				}
				else
				{
					if(this.rdEmail.Visible)
						rejectFields+="PersonalEmail, ";
				}

				if(this.rdNTN.SelectedValue=="0")
				{
					sql2+=",ntnno='"+this.Label10.Text+"'";
				}
				else
				{
					if(this.rdNTN.Visible)
						rejectFields+="NTNNO, ";
				} 

				if(this.rdContact.SelectedValue=="0")
				{
					sql2+=",telephone='"+this.Label11.Text+"'";
				}
				else
				{
					if(this.rdContact.Visible)
						rejectFields+="TelePhone, ";
				}

				if(this.rdMobile.SelectedValue=="0")
				{
					sql2+=",mobile='"+this.Label12.Text+"'";
				}
				else
				{
					if(this.rdMobile.Visible)
						rejectFields+="Mobile, ";
				}

				if(this.rdNICNew.SelectedValue=="0")
				{
					sql2+=",nic_new='"+this.Label13.Text+"'";
				}
				else
				{
					if(this.rdNICNew.Visible)
						rejectFields+="New NIC, ";
				}

				if(this.rdNICOld.SelectedValue=="0")
				{
					sql2+=",nic_old='"+this.Label14.Text+"'";
				}
				else
				{
					if(this.rdNICOld.Visible)
						rejectFields+="Old NIC, ";
				}
				if(this.rdKin.SelectedValue=="0")
				{
					sql2+=",kin='"+this.Label15.Text+"'";
				}
				else
				{
					if(this.rdKin.Visible)
						rejectFields+="NextOfKin, ";
				}

				if(this.rdBnkNo.SelectedValue=="0")
				{
					sql2+=",bankaccountno='"+this.Label16.Text+"'";
				}
				else
				{
					if(this.rdBnkNo.Visible)
						rejectFields+="BankAccountNo, ";
				}
				if(this.rdAcctDetails.SelectedValue=="0")
				{
					sql2+=",bankaccountdetails='"+this.Label17.Text+"'";
				}
				else
				{
					if(this.rdAcctDetails.Visible)
						rejectFields+="BankAccountDetails, ";
				}

				if(this.rdoNick.SelectedValue=="0")
				{
					sql2+=",nick='"+this.Label26.Text+"'";
				}
				else
				{
					if(this.rdoNick.Visible) 
						rejectFields+="Nick, ";
				}

				if(this.rdoNationality.SelectedValue=="0")
				{
					sql2+=",nationality='"+ this.Label28.ToolTip +"'";
				}
				else
				{
					if(this.rdoNationality.Visible)
						rejectFields+="Nationality, ";
				}
 
				//=========================// 
				if(this.rdoEmpCode.SelectedValue=="0")
				{
					sql2+=",pcode='"+this.Label18.Text+"'";
				}
 
				if(this.rdoEmpName.SelectedValue=="0")
				{
					sql2+=",name='"+this.Label19.Text+"'";
				}
				else
				{
					if(this.rdoEmpName.Visible)
						rejectFields+="Name, ";
				}

				if(this.rdoExtension.SelectedValue=="0")
				{
					sql2+=",extension='"+this.Label20.Text+"'";
				}
				else
				{
					if(this.rdoExtension.Visible)
					{
						rejectFields+="Extension, ";
					}
				}

				// if(this.rdoDepartment.SelectedValue=="0")
				// {
				// // sql2+=",deptid='"+this.Label21.ToolTip+"'";
				// }
				// else
				// {
				// if(this.rdoDepartment.Visible)
				// rejectFields+="Department, ";
				// }

				if(this.rdoDesignation.SelectedValue=="0")
				{
					sql2+=",designation='"+this.Label23.ToolTip+"'";
				}
				else
				{
					if(this.rdoDesignation.Visible)
						rejectFields+="Designation, ";
				}

				if(this.rdoDOJ.SelectedValue=="0")
				{
					sql2+=",dateofjoin='"+this.Label24.Text+"'";
				}
				else
				{
					if(this.rdoDOJ.Visible)
						rejectFields+="DateofJoin, ";
				}

				if(this.rdoFunctionalDesignation.SelectedValue=="0")
				{
					FunctionalDesignation=this.Label22.ToolTip.ToString();
					//sql2+=",dateofconfirmation='"+this.Label28.Text+"'";
				}
				else
				{
					if(this.rdoFunctionalDesignation.Visible)
						rejectFields+="Functional Designation, ";
				}

				if(this.rdoStation.SelectedValue=="0")
				{
					sql2+=",station='"+this.Label25.ToolTip+"'";
				}
				else
				{
					if(this.rdoStation.Visible)
						rejectFields+="Station, ";
				}

 
				// if(this.rdoInsuranceCode.SelectedValue=="0")
				// {
				// sql2+=",insurancecode='"+this.Label27.Text+"'";
				// }
				// else
				// {
				// if(this.rdoInsuranceCode.Visible)
				// rejectFields+="InsuranceCode, ";
				// }

				if(this.rdoEmailOffical.SelectedValue=="0")
				{
					sql2+=",email_official='"+this.Label32.Text+"'";
				}
				else
				{
					if(this.rdoEmailOffical.Visible)
						rejectFields+="Email_Official, ";
				}

 
				if(this.rdoTOE.SelectedValue=="0")
				{
					sql2+=",typeofemployment='"+this.Label30.ToolTip+"'";
				}
				else
				{
					if(this.rdoTOE.Visible)
						rejectFields+="TypeOfemployment, ";
				}

				if(this.rdoEOBINO.SelectedValue=="0")
				{
					sql2+=",eobino='"+this.Label33.Text+"'";
				}
				else
				{
					if(this.rdoEOBINO.Visible)
						rejectFields+="EOBINO, ";
				}

				if(this.rdoSessi.SelectedValue=="0")
				{
					sql2+=",sessino='"+this.Label34.Text+"'";
				}
				else
				{
					if(this.rdoSessi.Visible)
						rejectFields+="SESSINO, ";
				}
				if(this.rdoLocation.SelectedValue=="0")
				{
					sql2+=",operatingcity='"+ this.lblRempLocation.Text +"'";
				}
				else
				{
					if(this.rdoLocation.Visible)
						rejectFields+="Employee Location, "; 
				}

				// if(this.rdoOthers.SelectedValue=="0")
				// {
				// sql2+=",others='"+this.Label35.Text+"'";
				// }
				// else
				// {
				// if(this.rdoOthers.Visible)
				// rejectFields+="Others, ";
				// }
				if(this.rdoRequestPhoto.SelectedValue=="0")
				{
					sql2+=",pic='"+Request.QueryString[0].ToString().ToString().Trim()+".jpg"+"'";
					string path=Server.MapPath(@"..\employee\");
					string path2=Server.MapPath(@"..\tempEmployee\");
					if(File.Exists(@path+Request.QueryString[0].ToString().Trim()+".jpg"))
					{
						File.Delete(@path+Request.QueryString[0].ToString().Trim()+".jpg");
						File.Copy(@path2+Session["requestImg"].ToString(),@path+Request.QueryString[0].ToString().Trim()+".jpg");
 
						File.Delete(@path2+Session["requestImg"].ToString());
 
					}
					else
					{
						File.Copy(@path2+Session["requestImg"].ToString(),@path+Request.QueryString[0].ToString().Trim()+".jpg");
						File.Delete(@path2+Session["requestImg"].ToString());
 
					}

				}
				else
				{
					if(this.rdoRequestPhoto.Visible)
						rejectFields+=",requestImage ";
				}
				if(rejectFields.Length>0)
				{
					MessageToBulletin+=rejectFields;
					MessageToBulletin+="request date is "+sqlQuery;
					Bulletin.PostMessage("Rejected Fields",MessageToBulletin, Session["user_id"].ToString(), 10, 1,Session["user_id"].ToString(), Request.QueryString[0].ToString());
					// con.Open();
					// Guid guid=new Guid();
					// guid=Guid.NewGuid();
					// string msg="insert into bulletin values(N'"+"Rejected Fields"+"',N'"+MessageToBulletin+"','"+DateTime.Today.Date.ToString()+"','"+Session["user_id"].ToString()+"',dateadd(d,10,getdate()),'"+1+"','"+DateTime.Today.Date.ToString()+"','"+Session["user_id"].ToString()+"',NULL,NULL,'"+1+"','{"+ guid.ToString() +"}')"; 
					// SqlCommand cMsg=new SqlCommand(msg,con);
					// cMsg.ExecuteNonQuery(); 
					// con.Close(); 
					// 
					// con.Open();
					// // string getId="select max(s_no) from bulletin";
					// // SqlCommand cm=new SqlCommand(getId,con);
					// // SqlDataReader rd=cm.ExecuteReader();
					// // string Id=""; 
					// // while(rd.Read())
					// // {
					// // Id=rd[0].ToString();
					// // }
					// // rd.Close();
					// string var="NULL";
					// string sendMessage="insert into t_sendmessage values("+ var +",'"+Request.QueryString[0].ToString()+"','"+1+"','{"+ guid.ToString() +"}')";
					// SqlCommand c=new SqlCommand(sendMessage,con);
					// c.ExecuteNonQuery();
					// con.Close();
				}
				if(rejectFields.Length==0)
				{
					Bulletin.PostMessage("Request Updated","Your Request has been successfully updated, Request date is "+sqlQuery,Session["user_id"].ToString(),10,1,Session["user_id"].ToString(),Request.QueryString[0].ToString());
 
					// con.Open();
					// Guid guid=new Guid();
					// guid=Guid.NewGuid();
					// string msg="insert into bulletin values('"+"Request Updated"+"','"+"Your Request has been successfully updated, Request date is "+sqlQuery+"','"+DateTime.Today.Date.ToString()+"','"+Session["user_id"].ToString()+"',dateadd(d,10,getdate()),'"+1+"','"+DateTime.Today.Date.ToString()+"','"+Session["user_id"].ToString()+"',NULL,NULL,'"+1+"','{"+ guid.ToString() +"}')"; 
					// SqlCommand cMsg=new SqlCommand(msg,con);
					// cMsg.ExecuteNonQuery();
					// con.Close();
					//
					// con.Open();
					// // string getId="select max(s_no) from bulletin";
					// // SqlCommand cm=new SqlCommand(getId,con);
					// // SqlDataReader rd=cm.ExecuteReader();
					// // string Id=""; 
					// // while(rd.Read())
					// // {
					// // Id=rd[0].ToString();
					// // }
					// // rd.Close();
					// string var="NULL";
					// string sendMessage="insert into t_sendmessage values("+ var +",'"+Request.QueryString[0].ToString()+"','"+1+"','{"+ guid.ToString() +"}')";
					// SqlCommand c=new SqlCommand(sendMessage,con);
					// c.ExecuteNonQuery();
					// con.Close();
				}
				if(sql2.Length>0)
				{
					sqlQuery2+=sql2;
					int idx=sqlQuery2.IndexOf("set",0);
					sqlQuery2=sqlQuery2.Remove(idx+4,1);
					sqlQuery2+=" where pcode='"+Request.QueryString[0].ToString()+"'";
 
					con.Open();
					SqlCommand cmd=new SqlCommand(sqlQuery2,con);
					try
					{
						int i=cmd.ExecuteNonQuery();
					}
					catch(Exception ex){Response.Write(ex.Message); Response.End();}
					con.Close();
				}
				if(FunctionalDesignation.Length>0)
				{
					con.Open();
					string updateFunc="update t_designationhistory set isactive=0 where pcode='"+Request.QueryString[0].ToString()+"' And fdesigid='"+ this.lblEmpInfoFunctionalDesignation.ToolTip+"'";
					SqlCommand cUpdate=new SqlCommand(updateFunc,con);
					int k=cUpdate.ExecuteNonQuery();
 
					string InsertFunc="insert into t_designationhistory values('"+Request.QueryString[0].ToString()+"','"+FunctionalDesignation+"','"+DateTime.Today.Date.ToString()+"',"+" NULL "+",'"+1+"')";
					SqlCommand cInsert=new SqlCommand(InsertFunc,con);
					int j=cInsert.ExecuteNonQuery();
					con.Close();
				}
				//=================Query Required to Update Temporary Table======================//
 
				con.Open();
				string updateRequest="";
				string updateEmpInfo="";
				string updatePerInfo="";
				if(Session["getDesicion"].ToString()=="EmployeeInfo")
				{
					updateRequest="update t_empinfo set isrequested='"+0+"' where pcode='"+Request.QueryString[0].ToString()+"'";
				}
				if(Session["getDesicion"].ToString()=="PersonalInfo")
				{
					updateRequest="update t_personalinfo set isrequested='"+0+"' where pcode='"+Request.QueryString[0].ToString()+"'";
				}
				if(Session["getDesicion"].ToString().Length==0 && this.pnlEmployeeInfo.Visible)
				{
					updateEmpInfo="update t_empinfo set isrequested='"+0+"' where pcode='"+Request.QueryString[0].ToString()+"'";
					SqlCommand cmd=new SqlCommand(updateEmpInfo,con);
					cmd.ExecuteNonQuery();
 
				}
				if(Session["getDesicion"].ToString().Length==0 && this.pnlPersonal.Visible)
				{
					updatePerInfo="update t_personalinfo set isrequested='"+0+"' where pcode='"+Request.QueryString[0].ToString()+"'";
					SqlCommand cmd=new SqlCommand(updatePerInfo,con);
					cmd.ExecuteNonQuery(); 
				}
				if(updateRequest.Length>0)
				{
					SqlCommand cmd=new SqlCommand(updateRequest,con);
					cmd.ExecuteNonQuery();
				}
				this.lblMsg.Text="Request has been successfully updated";
				this.lblMsg.Visible=true;
				this.btnReject.Enabled=false;
				con.Close();
				Response.Write("<script>window.opener.location=window.opener.location; window.close();</script>");
				//Response.Write("<script language='javascript'>{ window.opener.location=window.opener.location; window.close(); }</script>") ;
				//Response.Write("<script>window.close();</script>");
			}
		}
		private void btnFupdate_Click(object sender, System.EventArgs e)
		{
			con.Open();
			if(!_isRefresh)
			{
				string empId=Request.QueryString[0].ToString();
				string updateFamily="";
				int state=-1;
				int k=0;
				SqlCommand cmdF=new SqlCommand();
				if(this.DataGrid2.Items[Int32.Parse(Session["famId"].ToString())].Cells[10].Text=="1")
				{
					//updateFamily="insert into t_familydetails values('"+empId+"','"+this.txtFamilyName.Text+"','"+Convert.ToInt32(this.rdRelation.SelectedValue.ToString())+"','"+Convert.ToInt32(this.rdStatus.SelectedValue.ToString())+"','"+Convert.ToInt32(this.rdFamilyGender.SelectedValue.ToString())+"','"+this.txtOccupation.Text+"','"+1+"')";
					///--updateFamily="insert into t_familydetails values('" + empId + "','" + 
					/// txtFamilyName.Text + "'," + 
					/// rdRelation.SelectedValue.ToString() + "," + 
					/// rdStatus.SelectedValue.ToString() + "," + 
					/// rdFamilyGender.SelectedValue.ToString() + ",'" + 
					/// txtOccupation.Text + "','"+ 
					/// this.CalendarPopup1.SelectedDate.ToString() +"','" + 
					/// 1 + "','"+ 
					/// this.ddDependent.SelectedValue.ToString() +"')";
					updateFamily="INSERT INTO dbo.t_FamilyDetails " +
						" (pcode, name, relationship, maritialstatus, gender, occupation, dob, isactive, Dependent) " +
						" VALUES (@p_pcode, @p_name, @p_relationship, @p_maritialstatus, @p_gender, @p_occupation, @p_dob, @p_isactive, @p_Dependent) ";

					SqlParameter p_pcode= new SqlParameter("@p_pcode",SqlDbType.Char, 10); 
					SqlParameter p_name= new SqlParameter("@p_name",SqlDbType.VarChar, 1000); 
					SqlParameter p_relationship= new SqlParameter("@p_relationship",SqlDbType.Int, 4); 
					SqlParameter p_maritialstatus= new SqlParameter("@p_maritialstatus",SqlDbType.TinyInt, 1); 
					SqlParameter p_gender= new SqlParameter("@p_gender",SqlDbType.TinyInt, 1); 
					SqlParameter p_occupation= new SqlParameter("@p_occupation",SqlDbType.VarChar, 1000); 
					SqlParameter p_dob= new SqlParameter("@p_dob",SqlDbType.DateTime, 8); 
					SqlParameter p_isactive= new SqlParameter("@p_isactive",SqlDbType.TinyInt, 1); 
					SqlParameter p_Dependent= new SqlParameter("@p_Dependent",SqlDbType.TinyInt, 1); 
					
					p_pcode.Value=empId;
					p_name.Value=txtFamilyName.Text.Trim();
					p_relationship.Value=rdRelation.SelectedValue.ToString();
					p_maritialstatus.Value=rdStatus.SelectedValue.ToString();
					p_gender.Value=rdFamilyGender.SelectedValue.ToString();
					p_occupation.Value=txtOccupation.Text.Trim();
					p_dob.Value=CalendarPopup1.SelectedDate.ToString();
					p_isactive.Value=1;
					p_Dependent.Value=ddDependent.SelectedValue.ToString();
 
					cmdF=new SqlCommand(updateFamily,con);
 
					cmdF.Parameters.Add(p_pcode);
					cmdF.Parameters.Add(p_name);
					cmdF.Parameters.Add(p_relationship);
					cmdF.Parameters.Add(p_maritialstatus);
					cmdF.Parameters.Add(p_gender);
					cmdF.Parameters.Add(p_occupation);
					cmdF.Parameters.Add(p_dob);
					cmdF.Parameters.Add(p_isactive);
					cmdF.Parameters.Add(p_Dependent);

					state=1;

					k=0;
					k=cmdF.ExecuteNonQuery();

				}
				if(this.DataGrid2.Items[Int32.Parse(Session["famId"].ToString())].Cells[10].Text=="0")
				{
					state=0;
					///--j--//updateFamily="update t_familydetails set 
					///name='" + txtFamilyName.Text + "',
					///relationship=" + rdRelation.SelectedValue.ToString() + ",
					///maritialstatus=" + rdStatus.SelectedValue.ToString() + ",
					///gender=" + rdFamilyGender.SelectedValue.ToString() + ",
					///dependent='"+  +"',
					///dob='"+ this.CalendarPopup1.SelectedDate.ToString() +"',
					///occupation='"+this.txtOccupation.Text+"' 
					///where 
					///fdid=" + Convert.ToInt32(this.DataGrid2.Items[Int32.Parse(Session["famId"].ToString())].Cells[8].Text) + " ";
					//updateFamily="update t_familydetails set name='"+this.txtFamilyName.Text+"',relationship='"+Convert.ToInt32(this.rdRelation.SelectedValue.ToString())+"',maritialstatus='"+Convert.ToInt32(this.rdStatus.SelectedValue.ToString())+"',gender='"+Convert.ToInt32(this.rdFamilyGender.SelectedValue.ToString())+"',occupation='"+this.txtOccupation.Text+"',isactive='"+1+"' where fdid='"+Convert.ToInt32(this.DataGrid2.Items[FamilyId].Cells[6].Text)+"'";
					updateFamily="UPDATE dbo.t_FamilyDetails " +
						" SET name = @p_name, relationship = @p_relationship, maritialstatus = @p_maritialstatus, gender = @p_gender, "+
						" occupation = @p_occupation, dob = @p_dob, Dependent = @p_Dependent " +
						" WHERE (fdid = @p_fdid)";


					SqlParameter p_fdid= new SqlParameter("@p_fdid",SqlDbType.Int, 4); 
					SqlParameter p_name= new SqlParameter("@p_name",SqlDbType.VarChar, 1000); 
					SqlParameter p_relationship= new SqlParameter("@p_relationship",SqlDbType.Int, 4); 
					SqlParameter p_maritialstatus= new SqlParameter("@p_maritialstatus",SqlDbType.TinyInt, 1);
					SqlParameter p_gender= new SqlParameter("@p_gender",SqlDbType.TinyInt, 1); 
					SqlParameter p_occupation= new SqlParameter("@p_occupation",SqlDbType.VarChar, 1000); 
					SqlParameter p_dob= new SqlParameter("@p_dob",SqlDbType.DateTime, 8); 
					SqlParameter p_Dependent= new SqlParameter("@p_Dependent",SqlDbType.TinyInt, 1); 
 
					p_fdid.Value=Convert.ToInt32(this.DataGrid2.Items[Int32.Parse(Session["famId"].ToString())].Cells[8].Text);
					p_name.Value=txtFamilyName.Text.Trim();
					p_relationship.Value=rdRelation.SelectedValue.ToString();
					p_maritialstatus.Value=rdStatus.SelectedValue.ToString();
					p_gender.Value=rdFamilyGender.SelectedValue.ToString();
					p_occupation.Value=this.txtOccupation.Text.Trim();
					p_dob.Value=CalendarPopup1.SelectedDate.ToString();
					p_Dependent.Value=this.ddDependent.SelectedValue.ToString();
					

					cmdF=new SqlCommand(updateFamily,con);
 
					cmdF.Parameters.Add(p_name);
					cmdF.Parameters.Add(p_relationship);
					cmdF.Parameters.Add(p_maritialstatus);
					cmdF.Parameters.Add(p_gender);
					cmdF.Parameters.Add(p_occupation);
					cmdF.Parameters.Add(p_dob);
					cmdF.Parameters.Add(p_Dependent);
					cmdF.Parameters.Add(p_fdid);

					k=0;
					k=cmdF.ExecuteNonQuery();


				}
				if(this.DataGrid2.Items[Int32.Parse(Session["famId"].ToString())].Cells[10].Text=="-1")
				{
					state=0;
					///updateFamily="update t_familydetails set isactive=0 where fdid=" + 
					///this.DataGrid2.Items[Int32.Parse(Session["famId"].ToString())].Cells[8].Text+ "";
					updateFamily="UPDATE dbo.t_FamilyDetails SET isactive=0 WHERE (fdid = @p_fdid)";
					cmdF=new SqlCommand(updateFamily,con);
					SqlParameter p_fdid= new SqlParameter("@p_fdid",SqlDbType.Int, 4); 
					p_fdid.Value=DataGrid2.Items[Int32.Parse(Session["famId"].ToString())].Cells[8].Text;
					cmdF.Parameters.Add(p_fdid);
					k=0;
					k=cmdF.ExecuteNonQuery();

				}
 
				con.Close();
				//FamilyRequest();
				FamilyInfo();
				this.btnFupdate.Enabled=false;
				this.btnNew.Enabled=true;
				this.txtFamilyName.Text="";
				this.txtOccupation.Text="";
				for(int i=0;i<this.rdFamilyGender.Items.Count;i++)
				{
					this.rdFamilyGender.Items[i].Selected=false;
				}
				for(int i=0;i<this.rdRelation.Items.Count;i++)
				{
					this.rdRelation.Items[i].Selected=false;
				}
				for(int i=0;i<this.rdStatus.Items.Count;i++)
				{
					this.rdStatus.Items[i].Selected=false;
				}
				if(k>0)
				{
					con.Open();
					string updateTemp="update t_familydetail_temp set isrequest='"+0+"' where tempfd_id='"+Convert.ToInt32(this.DataGrid2.Items[Int32.Parse(Session["famId"].ToString())].Cells[9].Text)+"'";
					SqlCommand cm=new SqlCommand(updateTemp,con);
					int y=cm.ExecuteNonQuery();
					con.Close();
					FamilyRequest();
				}
				string msgText="";
				if(state==0)
				{
					msgText="Your Family Detail Request has been successfully changed";
				}
				if(state==1)
				{
					msgText="Your New Family Detail Request has been successfully Added";
				}

				Bulletin.PostMessage("Request Accepted",msgText,Session["user_id"].ToString(),10,1,Session["user_id"].ToString(),empId);

				/// con.Open();
				/// Guid guid=new Guid();
				/// guid=Guid.NewGuid();
				/// string msg="insert into bulletin values('"+"Request Accepted"+"','"+ msgText +"','"+DateTime.Today.Date.ToString()+"','"+Session["user_id"].ToString()+"',dateadd(d,10,getdate()),'"+1+"','"+DateTime.Today.Date.ToString()+"','"+Session["user_id"].ToString()+"',NULL,NULL,'"+1+"','{"+ guid.ToString() +"}')"; 
				/// SqlCommand cMsg=new SqlCommand(msg,con);
				/// cMsg.ExecuteNonQuery();
				/// con.Close();
 
 
				/// con.Open();
				/// 
				/// string var="NULL";
				/// string sendMessage="insert into t_sendmessage values("+ var +",'"+empId+"','"+1+"','{"+ guid.ToString() +"}')";
				/// SqlCommand c=new SqlCommand(sendMessage,con);
				/// c.ExecuteNonQuery();
				if(this.DataGrid2.Items.Count<=0)
				{
					btnFupdate.Enabled=false;
				}
			}
			else
			{
				FamilyRequest();
				FamilyInfo();
				this.btnFupdate.Enabled=false;
				this.btnNew.Enabled=true;
				this.txtFamilyName.Text="";
				this.txtOccupation.Text="";
				for(int i=0;i<this.rdFamilyGender.Items.Count;i++)
				{
					this.rdFamilyGender.Items[i].Selected=false;
				}
				for(int i=0;i<this.rdRelation.Items.Count;i++)
				{
					this.rdRelation.Items[i].Selected=false;
				}
				for(int i=0;i<this.rdStatus.Items.Count;i++)
				{
					this.rdStatus.Items[i].Selected=false;
				}
			}
			con.Close();
		}

		private void btnNew_Click(object sender, System.EventArgs e)
		{
 

		}

		private void btnEdUpdate_Click(object sender, System.EventArgs e)
		{
			con.Open();
			if(!_isRefresh)
			{
				string empId=Request.QueryString[0].ToString();
				string updateInfo="";
				int state=-1;
				string vari="NULL";
				int i=0;

				if(Int32.Parse(Session["eduId"].ToString())==0)
				{
					state=1;
					///updateInfo="insert into t_educationinfo values('"+
					///empId.Trim()+"','"+
					///this.txtDegree.SelectedValue.ToString().Trim()+"','"+
					///this.txtInstitute.SelectedValue.ToString().Trim()+"','"+
					///this.DurationFrom.SelectedValue.ToString()+"','"+ 
					///this.DurationFrom.SelectedValue.ToString() +"','"+
					///this.txtResult.SelectedValue.ToString().Trim()+"','"+
					///this.txtOtherMajors.Text.Trim()+"','"+
					///this.txtAchievements.Text+"','"+
					///1+"','"+
					///this.txtOtherDegree.Text+"','"+
					///this.txtOtherInstitute.Text+"','"+
					///this.txtOtherMajors.Text+"',"+ 
					///vari +")";
 
					updateInfo="INSERT INTO dbo.t_educationInfo "+
						" (pcode, degree, institute, DurationFrom, DurationTo, result, majors, achievements, isactive, OtherDegree, OtherInstitute, OtherMajors) " +
						" VALUES(@p_pcode, @p_degree, @p_institute, @p_DurationFrom, @p_DurationTo, @p_result, @p_majors, @p_achievements, @p_isactive, @p_OtherDegree, @p_OtherInstitute, @p_OtherMajors)";

					SqlParameter p_pcode= new SqlParameter("@p_pcode",SqlDbType.Char, 10); p_pcode.Value= empId.Trim();
					SqlParameter p_degree= new SqlParameter("@p_degree",SqlDbType.Int, 4); p_degree.Value=txtDegree.SelectedValue;
					SqlParameter p_institute= new SqlParameter("@p_institute",SqlDbType.Int, 4); p_institute.Value=txtInstitute.SelectedValue;
					SqlParameter p_DurationFrom= new SqlParameter("@p_DurationFrom",SqlDbType.Int, 4); p_DurationFrom.Value=DurationFrom.SelectedValue;
					SqlParameter p_DurationTo= new SqlParameter("@p_DurationTo",SqlDbType.Int, 4); p_DurationTo.Value=DurationTo.SelectedValue;
					SqlParameter p_result= new SqlParameter("@p_result",SqlDbType.VarChar, 100); p_result.Value=txtResult.SelectedValue;
					SqlParameter p_majors= new SqlParameter("@p_majors",SqlDbType.VarChar, 50); p_majors.Value=txtOtherMajors.Text.Trim();
					SqlParameter p_achievements= new SqlParameter("@p_achievements",SqlDbType.Text, 16); p_achievements.Value=txtAchievements.Text.Trim();
					SqlParameter p_isactive= new SqlParameter("@p_isactive",SqlDbType.Int, 4); p_isactive.Value=1;
					SqlParameter p_OtherDegree= new SqlParameter("@p_OtherDegree",SqlDbType.VarChar, 50); p_OtherDegree.Value=txtOtherDegree.Text.Trim();
					SqlParameter p_OtherInstitute= new SqlParameter("@p_OtherInstitute",SqlDbType.VarChar, 50); p_OtherInstitute.Value=txtOtherInstitute.Text;
					SqlParameter p_OtherMajors= new SqlParameter("@p_OtherMajors",SqlDbType.VarChar, 50); p_OtherMajors.Value=txtOtherMajors.Text;

					SqlCommand cmd=new SqlCommand(updateInfo,con);
					cmd.Parameters.Add(p_pcode);
					cmd.Parameters.Add(p_degree);
					cmd.Parameters.Add(p_institute);
					cmd.Parameters.Add(p_DurationFrom);
					cmd.Parameters.Add(p_DurationTo);
					cmd.Parameters.Add(p_result);
					cmd.Parameters.Add(p_majors);
					cmd.Parameters.Add(p_achievements);
					cmd.Parameters.Add(p_isactive);
					cmd.Parameters.Add(p_OtherDegree);
					cmd.Parameters.Add(p_OtherInstitute);
					cmd.Parameters.Add(p_OtherMajors);
					i=cmd.ExecuteNonQuery();
					con.Close();

					//updateInfo="insert into t_educationinfo values('"+empId.Trim()+"','"+0+"','"+0+"','"+this.txtYear.Text+"','"+this.txtResult.SelectedValue.ToString().Trim()+"','"+this.txtDuration.Text+"','"+0+"','"+this.txtAchievements.Text+"','"+1+"','"+this.+"','"+0+"','"+0+"','"+2+"')"; 
				}
				if(Int32.Parse(Session["eduId"].ToString())>0)
				{
					state=0; 
					///updateInfo="update t_educationinfo set 
					///pcode='"+empId.Trim()+"',
					///degree='"+this.txtDegree.SelectedValue.ToString()+"',
					///institute='"+this.txtInstitute.SelectedValue.ToString()+"',
					///durationto='"+this.DurationTo.SelectedValue.ToString()+"',
					///result='"+this.txtResult.SelectedValue.ToString()+"',
					///durationfrom='"+this.DurationFrom.SelectedValue.ToString()+"',
					///majors='"+this.txtOtherMajors.Text+"',
					///achievements='"+this.txtAchievements.Text+"',
					///isactive='"+1+"',
					///otherdegree='"+this.txtOtherDegree.Text+"',
					///otherinstitute='"+this.txtOtherInstitute.Text+"',
					///othermajors='"+this.txtOtherMajors.Text+"' 
					///where 
					///eduinfoid='"+Convert.ToInt32(Session["eduId"].ToString())+"'";
 
					updateInfo="UPDATE dbo.t_educationInfo " +
						" SET pcode = @p_pcode, degree = @p_degree, institute = @p_institute, DurationFrom = @p_DurationFrom, DurationTo = @p_DurationTo, " +
						" result = @p_result, majors = @p_majors, achievements = @p_achievements, isactive = @p_isactive, OtherDegree = @p_OtherDegree, " +
						" OtherInstitute = @p_OtherInstitute, OtherMajors = @p_OtherMajors " +
						" WHERE (eduinfoid = @p_eduinfoid)";

					SqlParameter p_eduinfoid=new SqlParameter("@p_eduinfoid",SqlDbType.Int);
					p_eduinfoid.Value=Convert.ToInt32(Session["eduId"].ToString());
					SqlParameter p_pcode=new SqlParameter("@p_pcode",SqlDbType.Char, 10); 
					p_pcode.Value= empId.Trim();
					SqlParameter p_degree=new SqlParameter("@p_degree",SqlDbType.Int, 4); 
					p_degree.Value=txtDegree.SelectedValue;
					SqlParameter p_institute=new SqlParameter("@p_institute",SqlDbType.Int, 4); 
					p_institute.Value=txtInstitute.SelectedValue;
					SqlParameter p_DurationFrom=new SqlParameter("@p_DurationFrom",SqlDbType.Int, 4); 
					p_DurationFrom.Value=DurationFrom.SelectedValue;
					SqlParameter p_DurationTo=new SqlParameter("@p_DurationTo",SqlDbType.Int, 4); 
					p_DurationTo.Value=DurationTo.SelectedValue;
					SqlParameter p_result=new SqlParameter("@p_result",SqlDbType.VarChar, 100); 
					p_result.Value=txtResult.SelectedValue;
					SqlParameter p_majors=new SqlParameter("@p_majors",SqlDbType.VarChar, 50); 
					p_majors.Value=txtOtherMajors.Text.Trim();
					SqlParameter p_achievements=new SqlParameter("@p_achievements",SqlDbType.Text, 16); 
					p_achievements.Value=txtAchievements.Text.Trim();
					SqlParameter p_isactive=new SqlParameter("@p_isactive",SqlDbType.Int, 4); 
					p_isactive.Value=1;
					SqlParameter p_OtherDegree=new SqlParameter("@p_OtherDegree",SqlDbType.VarChar, 50); 
					p_OtherDegree.Value=txtOtherDegree.Text.Trim();
					SqlParameter p_OtherInstitute=new SqlParameter("@p_OtherInstitute",SqlDbType.VarChar, 50); 
					p_OtherInstitute.Value=txtOtherInstitute.Text;
					SqlParameter p_OtherMajors=new SqlParameter("@p_OtherMajors",SqlDbType.VarChar, 50); 
					p_OtherMajors.Value=txtOtherMajors.Text;

					SqlCommand cmd=new SqlCommand(updateInfo,con);
					cmd.Parameters.Add(p_eduinfoid);
					cmd.Parameters.Add(p_pcode);
					cmd.Parameters.Add(p_degree);
					cmd.Parameters.Add(p_institute);
					cmd.Parameters.Add(p_DurationFrom);
					cmd.Parameters.Add(p_DurationTo);
					cmd.Parameters.Add(p_result);
					cmd.Parameters.Add(p_majors);
					cmd.Parameters.Add(p_achievements);
					cmd.Parameters.Add(p_isactive);
					cmd.Parameters.Add(p_OtherDegree);
					cmd.Parameters.Add(p_OtherInstitute);
					cmd.Parameters.Add(p_OtherMajors);
					
					i=cmd.ExecuteNonQuery();

				}
				if(Int32.Parse(Session["eduId"].ToString())==-1)
				{
					//updateInfo="update t_educationinfo set isactive=0 where eduinfoid='"+Convert.ToInt32(Session["up"].ToString())+"'";
					updateInfo="UPDATE dbo.t_educationInfo SET isactive = 0 WHERE (eduinfoid = @p_eduinfoid) ";
					SqlCommand cmd=new SqlCommand(updateInfo,con);
					SqlParameter p_eduinfoid=new SqlParameter("@p_eduinfoid",SqlDbType.Int);
					p_eduinfoid.Value=Convert.ToInt32(Session["up"].ToString());
					cmd.Parameters.Add(p_eduinfoid);
					i=cmd.ExecuteNonQuery();
				}

 
 
				if(i>0)
				{
					if(con.State!=ConnectionState.Open)
						con.Open();
					string updateReq="update t_educationinfo_temp set isrequest='"+0+"' where eduinfoid='"+Convert.ToInt32(Session["updId"].ToString())+"'"; 
					SqlCommand c=new SqlCommand(updateReq,con);
					int y=c.ExecuteNonQuery();
					con.Close();
					EducationRequest();
					EducationalInfo();
					if(this.DataGrid4.Items.Count<=0)
					{
						btnEdUpdate.Enabled=false;
					}
				}
				this.btnEdUpdate.Enabled=false;
				this.btnEdNew.Enabled=true;
				this.txtDegree.SelectedValue="0";
				this.txtInstitute.SelectedValue="0";
				this.txtResult.SelectedIndex=0;
				this.DurationFrom.SelectedIndex=0;
				this.DurationTo.SelectedIndex=0;
				this.txtAchievements.Text="";

				this.txtOtherDegree.Text="";
				this.txtOtherInstitute.Text="";
				this.txtOtherMajors.Text="";

				string msgText="";
				if(state==0)
				{
					msgText="Your Educational Request has been successfully changed";
				}
				if(state==1)
				{
					msgText="Your New Educational Request has been successfully Added";
				}
				Bulletin.PostMessage("Request Accepted",msgText,Session["user_id"].ToString(),10,1,Session["user_id"].ToString(),empId);
///				con.Open();
///				Guid guid=new Guid();
///				guid=Guid.NewGuid();
///				string msg="insert into bulletin values('"+"Request Accepted"+"','"+ msgText +"','"+DateTime.Today.Date.ToString()+"','"+Session["user_id"].ToString()+"',dateadd(d,10,getdate()),'"+1+"','"+DateTime.Today.Date.ToString()+"','"+Session["user_id"].ToString()+"',NULL,NULL,'"+1+"','{"+ guid.ToString() +"}')"; 
///				SqlCommand cMsg=new SqlCommand(msg,con);
///				cMsg.ExecuteNonQuery();
///				con.Close();
///
///				con.Open();
///				string var="NULL";
///				string sendMessage="insert into t_sendmessage values("+ var +",'"+empId+"','"+1+"','{"+ guid.ToString() +"}')";
///				SqlCommand c2=new SqlCommand(sendMessage,con);
///				c2.ExecuteNonQuery();
///				//Response.Write("<script>window.close();</script>");
			}
			else
			{
				EducationRequest();
				EducationalInfo();
				if(this.DataGrid4.Items.Count<=0)
				{
					btnEdUpdate.Enabled=false;
				}
				this.btnEdUpdate.Enabled=false;
				this.btnEdNew.Enabled=true;
				this.txtDegree.SelectedValue="0";
				this.txtInstitute.SelectedValue="0";
				this.txtResult.SelectedIndex=0;
				this.DurationFrom.SelectedIndex=0;
				this.DurationTo.SelectedIndex=0;
				this.txtAchievements.Text="";

				this.txtOtherDegree.Text="";
				this.txtOtherInstitute.Text="";
				this.txtOtherMajors.Text="";

			}
			con.Close();
		}

		private void btnEdNew_Click(object sender, System.EventArgs e)
		{
 
		}

		public void FamilyRequest()
		{
			if(con.State==ConnectionState.Open)
			{
				con.Close();
			}
			con.Open();
			this.DataGrid2.Visible=true;
			string empId=Request.QueryString[0].ToString();
			//Session["user_id"].ToString();
			//Request.QueryString["id"].ToString();
			string getFamily=
				"select name,relationship = case relationship when '2' then 'Husband' " + 
				" when '3' then 'Wife' " + 
				" when '4' then 'Son' " + 
				" when '5' then 'Daughter' " + 
				" END, " + 
				" maritialstatus=case maritialstatus when '1' then 'Single' " + 
				" when '2' then 'Married' " + 
				" when '3' then 'Divorced' " + 
				" END, " + 
				" gender=case gender WHEN '1' THEN 'Male' " + 
				" when '2' then 'Female' " + 
				" END ,occupation,DOB,fdid,tempfd_id,isnew,dependent=case dependent when '1' then 'Yes' when '2' then 'No' end,pcode,fdid " + 
				" from t_familydetail_temp where isactive='1' and pcode='" + empId + "' And isrequest=1";
 

////			string getFamily= "SELECT name, CASE relationship WHEN 2 THEN 'Husband' WHEN 3 THEN 'Wife' WHEN 4 THEN 'Son' WHEN 5 THEN 'Daughter' END AS relationship, "+
////				" CASE maritialstatus WHEN '1' THEN 'Single' WHEN '2' THEN 'Married' WHEN '3' THEN 'Divorced' END AS maritialstatus, "+
////				" CASE gender WHEN '1' THEN 'Male' WHEN '2' THEN 'Female' END AS gender, occupation, dob, fdid, tempfd_id, isnew, "+
////				" CASE dependent WHEN '1' THEN 'Yes' WHEN '2' THEN 'No' END AS dependent, pcode, fdid "+
////				" FROM dbo.t_familydetail_temp "+
////				" WHERE (isactive = '1') AND (pcode = '"+empId.Trim()+"') AND (isrequest = 1)";

			SqlDataAdapter rd=new SqlDataAdapter(getFamily,con);
			DataSet dsFamily=new DataSet();
 
			rd.Fill(dsFamily,"FamilyInfo");
			DataView dv=new DataView(dsFamily.Tables["FamilyInfo"]);
			dv.Sort=sort2;
			this.DataGrid2.DataSource=dv;
			this.DataGrid2.DataBind();
			// this.DataGrid2.Columns[6].Visible=false;
			// this.DataGrid2.Columns[7].Visible=false;
			// this.DataGrid2.Columns[8].Visible=false;
			con.Close();
			for(int i=0;i<this.DataGrid2.Items.Count;i++)
			{
				if(this.DataGrid2.Items[i].Cells[10].Text=="-1")
				{
					this.DataGrid2.Items[i].BackColor=Color.Red;
				}
			}
		}

		public void EducationRequest()
		{
			if(con.State==ConnectionState.Open)
			{
				con.Close();
			}
			con.Open();
			this.DataGrid4.Visible=true;
			string empId=Request.QueryString[0].ToString();
 
			//string getInfo="select e.eduinfoid,degree=case e.degree when '0' then (select temp.otherdegree from t_educationinfo_temp temp where temp.eduinfoid=e.eduinfoid ) else (select d.degree from t_degree d where d.id=e.degree) end , institute=case e.institute when '0' then (select temp.otherinstitute from t_educationinfo_temp temp where temp.eduinfoid=e.eduinfoid) else (select temp.institute_name from t_institute temp where temp.id=e.institute)end ,e.durationto,e.result,e.durationfrom,majors=case e.majors when '0' then (select temp.othermajors from t_educationinfo_temp temp where temp.eduinfoid=e.eduinfoid) else (select major from t_majors where id=e.majors) end ,e.achievements,e.p_id,type from t_educationinfo_temp e where e.isactive='1' And e.pcode='"+empId+"' And isrequest='1'";
			string getInfo="select e.eduinfoid,degree=case e.degree when '-1' then (select temp.otherdegree from t_educationinfo_temp temp where temp.eduinfoid=e.eduinfoid ) else (select d.degree from t_degree d where d.id=e.degree) end , institute=case e.institute when '-1' then (select temp.otherinstitute from t_educationinfo_temp temp where temp.eduinfoid=e.eduinfoid) else (select temp.institute_name from t_institute temp where temp.id=e.institute)end ,e.durationto,e.result,e.durationfrom,e.majors,e.achievements,e.p_id,type from t_educationinfo_temp e where e.isactive='1' And e.pcode='"+empId+"' And isrequest=1";
			SqlDataAdapter rd=new SqlDataAdapter(getInfo,con);
			DataSet rSet=new DataSet();
			rd.Fill(rSet,"EdutionalInfo");
			DataView dv=new DataView(rSet.Tables["EdutionalInfo"]);
			dv.Sort=sort4;
			this.DataGrid4.DataSource=dv;
			this.DataGrid4.DataBind();
			this.DataGrid4.Columns[8].Visible=false;
			this.DataGrid4.Columns[9].Visible=false;
			this.DataGrid4.Columns[11].Visible=false;
			con.Close();
			for(int i=0;i<this.DataGrid4.Items.Count;i++)
			{
				if(this.DataGrid4.Items[i].Cells[9].Text=="-1")
				{
					this.DataGrid4.Items[i].BackColor=Color.Red;
				}
			}
		}

		private void DataGrid1_SelectedIndexChanged_1(object sender, System.EventArgs e)
		{
			this.btnFupdate.Enabled=true;
			this.btnNew.Enabled=false;

			this.txtFamilyName.Text=this.DataGrid1.Items[this.DataGrid1.SelectedIndex].Cells[0].Text;
			this.txtOccupation.Text=this.DataGrid1.Items[this.DataGrid1.SelectedIndex].Cells[4].Text;
			string getRelation=this.DataGrid1.Items[this.DataGrid1.SelectedIndex].Cells[1].Text.Trim();
			for(int i=0;i<this.rdRelation.Items.Count;i++)
			{
				ListItem itm=this.rdRelation.Items[i];
				if(itm.Text==getRelation)
				{
					this.rdRelation.Items[i].Selected=true;
				}
			}
			string getStatus=this.DataGrid1.Items[this.DataGrid1.SelectedIndex].Cells[2].Text.Trim();
			for(int i=0;i<this.rdStatus.Items.Count;i++)
			{
				ListItem itm=this.rdStatus.Items[i];
				if(itm.Text==getStatus)
				{
					this.rdStatus.Items[i].Selected=true;
				}
			}
			string getGender=this.DataGrid1.Items[this.DataGrid1.SelectedIndex].Cells[3].Text.Trim();
			for(int i=0;i<this.rdFamilyGender.Items.Count;i++)
			{
				ListItem itm=this.rdFamilyGender.Items[i];
				if(itm.Text==getGender)
				{
					this.rdFamilyGender.Items[i].Selected=true;
				}
			} 
		}

		private void DataGrid3_SelectedIndexChanged_1(object sender, System.EventArgs e)
		{
			this.btnEdUpdate.Enabled=true;
			this.btnEdNew.Enabled=false;

			string getDegree=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[0].Text;
			string getInstitute=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[1].Text;
			string getYear=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[2].Text;
			string getResult=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[3].Text;
			string getDuration=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[4].Text;
			string getMajors=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[5].Text;
			string getAchievements=this.DataGrid3.Items[this.DataGrid3.SelectedIndex].Cells[6].Text;

			try
			{
 
 
				for(int i=0;i<this.txtDegree.Items.Count;i++)
				{
					if(this.txtDegree.Items[i].Text==getDegree)
					{
						//this.txtDegree.SelectedValue=this.txtDegree.Items[i].Value;
						this.txtDegree.SelectedIndex=i;
						break;
					}
				}
				for(int i=0;i<this.txtInstitute.Items.Count;i++)
				{
					if(this.txtInstitute.Items[i].Text==getInstitute)
					{
						//this.txtDegree.SelectedValue=this.txtDegree.Items[i].Value;
						this.txtInstitute.SelectedIndex=i;
						break;
					}
				}
			}
			catch(Exception ex){string s=ex.Message;} 
			this.DurationTo.SelectedValue=getYear;
			//this.txtResult.Text=getResult;
			this.DurationFrom.SelectedValue=getDuration;
			this.txtOtherMajors.Text=getMajors;
			this.txtAchievements.Text=getAchievements; 
		}

		private void DataGrid2_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			//if(!_isRefresh)
		{
			Session["famId"]="0";
			this.btnFupdate.Enabled=true;
			this.btnNew.Enabled=false;
			Session["famId"]=this.DataGrid2.SelectedIndex; 
			this.txtFamilyName.Text=this.DataGrid2.Items[this.DataGrid2.SelectedIndex].Cells[0].Text;
			if(this.DataGrid2.Items[this.DataGrid2.SelectedIndex].Cells[4].Text!="&nbsp;")
			{
				this.txtOccupation.Text=this.DataGrid2.Items[this.DataGrid2.SelectedIndex].Cells[4].Text;
			}
			string getRelation=this.DataGrid2.Items[this.DataGrid2.SelectedIndex].Cells[1].Text.Trim();
			for(int i=0;i<this.rdRelation.Items.Count;i++)
			{
				ListItem itm=this.rdRelation.Items[i];
				if(itm.Text==getRelation.Trim())
				{
					this.rdRelation.SelectedIndex=i;
				}
			}
			string getStatus=this.DataGrid2.Items[this.DataGrid2.SelectedIndex].Cells[2].Text.Trim();
			for(int i=0;i<this.rdStatus.Items.Count;i++)
			{
				ListItem itm=this.rdStatus.Items[i];
				if(itm.Text==getStatus.Trim())
				{
					this.rdStatus.SelectedIndex=i;
				}
			}
			string getGender=this.DataGrid2.Items[this.DataGrid2.SelectedIndex].Cells[3].Text.Trim();
			for(int i=0;i<this.rdFamilyGender.Items.Count;i++)
			{
				ListItem itm=this.rdFamilyGender.Items[i];
				if(itm.Text==getGender.Trim())
				{
					this.rdFamilyGender.SelectedIndex=i;
				}
			}
			this.CalendarPopup1.SelectedDate=DateTime.Parse(DataGrid2.Items[DataGrid2.SelectedIndex].Cells[5].Text);
 
			if(DataGrid2.Items[DataGrid2.SelectedIndex].Cells[6].Text=="Yes")
			{
				this.ddDependent.SelectedIndex=1;
			}
			else if(DataGrid2.Items[DataGrid2.SelectedIndex].Cells[6].Text=="No")
			{
				this.ddDependent.SelectedIndex=2;
			}
			else
			{
				this.ddDependent.SelectedIndex=0;
			}
		}
		}

		private void DataGrid4_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			//if(!_isRefresh)
		{
			Session["eduId"]="0";
			Session["updId"]=0;
			Session["up"]="0";
			this.btnEdUpdate.Enabled=true;
			this.btnEdNew.Enabled=false;
			string temp=this.DataGrid4.Items[this.DataGrid4.SelectedIndex].Cells[9].Text; 
			if(this.DataGrid4.Items[this.DataGrid4.SelectedIndex].Cells[9].Text != "&nbsp;")
			{
				Session["eduId"]=Convert.ToInt32(this.DataGrid4.Items[this.DataGrid4.SelectedIndex].Cells[9].Text);
			}
			Session["updId"]=Convert.ToInt32(this.DataGrid4.Items[this.DataGrid4.SelectedIndex].Cells[8].Text);
 
			string getDegree=this.DataGrid4.Items[this.DataGrid4.SelectedIndex].Cells[0].Text.Trim();
			string getInstitute=this.DataGrid4.Items[this.DataGrid4.SelectedIndex].Cells[1].Text.Trim();
 
			string getYear=this.DataGrid4.Items[this.DataGrid4.SelectedIndex].Cells[3].Text;
			string getDuration=this.DataGrid4.Items[this.DataGrid4.SelectedIndex].Cells[2].Text;
 
			string getResult=this.DataGrid4.Items[this.DataGrid4.SelectedIndex].Cells[4].Text.Trim();
			string getMajors=this.DataGrid4.Items[this.DataGrid4.SelectedIndex].Cells[5].Text.Trim();
			string getAchievements="";
			if(this.DataGrid4.Items[this.DataGrid4.SelectedIndex].Cells[6].Text!="&nbsp;")
			{
				getAchievements=this.DataGrid4.Items[this.DataGrid4.SelectedIndex].Cells[6].Text;
			}
			string getType=this.DataGrid4.Items[this.DataGrid4.SelectedIndex].Cells[11].Text; 
			Session["up"]=getType;
			//this.ddType.SelectedIndex=Convert.ToInt32(getType);
 
			this.txtDegree.Visible=true;
			this.txtInstitute.Visible=true;
 
			this.txtOtherDegree.Visible=false;
			this.txtOtherInstitute.Visible=false;
			this.txtOtherMajors.Visible=true;
 
			this.txtOtherDegree.Text="";
			this.txtOtherInstitute.Text="";
			this.txtOtherMajors.Text="";
			try
			{
				int countDegree=0;
				for(int i=0;i<this.txtDegree.Items.Count;i++)
				{
					if(this.txtDegree.Items[i].Text==getDegree.Trim())
					{
						this.txtDegree.SelectedIndex=i;
						break;
					}
					else
					{
						countDegree++;
					}
				}
				if(countDegree==this.txtDegree.Items.Count)
				{
					this.txtOtherDegree.Visible=true;
					this.txtOtherDegree.Text=getDegree;
					this.txtDegree.SelectedIndex=countDegree-1;
				}
				int countInstitute=0;
				for(int i=0;i<this.txtInstitute.Items.Count;i++)
				{
					if(this.txtInstitute.Items[i].Text==getInstitute.Trim())
					{
						this.txtInstitute.SelectedIndex=i;
						break;
					}
					else
					{
						countInstitute++;
					}
				}
				if(countInstitute==this.txtInstitute.Items.Count)
				{
					this.txtOtherInstitute.Visible=true;
					this.txtOtherInstitute.Text=getInstitute;
					this.txtInstitute.SelectedIndex=countInstitute-1;
				}
				for(int i=0;i<this.txtResult.Items.Count;i++)
				{
					if(this.txtResult.Items[i].Text.Trim()==getResult.Trim())
					{
						//this.txtDegree.SelectedValue=this.txtDegree.Items[i].Value;
						this.txtResult.SelectedIndex=i;
						break;
					}
				}
			}
			catch(Exception ex){string s=ex.Message;}
 
			this.DurationTo.SelectedValue=getYear;
			this.DurationFrom.SelectedValue=getDuration;
			this.txtAchievements.Text=getAchievements;
			this.txtOtherMajors.Text=getMajors;
		}
		}
 
		public void InterfacePersonal()
		{
			if(this.Label1.Text.Equals(this.lblName.Text))
			{
				this.rdDOB.Visible=false;
			}
			if(this.Label2.Text.Equals(this.lblFName.Text))
			{
				this.rdFname.Visible=false;
			}
			if(this.Label3.Text.Equals(this.lblGender.Text))
			{
				this.rdGender.Visible=false;
			}
			if(this.Label4.Text.Equals(this.lblBloodGrp.Text))
			{
				this.rdBloodGrp.Visible=false;
			}
			if(this.Label5.Text.Equals(this.lblReligion.Text))
			{
				this.rdReligion.Visible=false;
			}
			if(this.Label6.Text.Equals(this.lblPassport.Text))
			{
				this.rdPassport.Visible=false;
			}
			if(this.Label7.Text.Equals(this.lblAddress.Text))
			{
				this.rdAddress.Visible=false;
			}
			if(this.Label8.Text.Equals(this.lblMaritialStat.Text))
			{
				this.rdMaritialStat.Visible=false;
			}
			if(this.Label9.Text.Equals(this.lblEmail.Text))
			{
				this.rdEmail.Visible=false;
			}
			if(this.Label10.Text.Equals(this.lblNTN.Text))
			{
				this.rdNTN.Visible=false;
			}
			if(this.Label11.Text.Equals(this.lblcontactNo.Text))
			{
				this.rdContact.Visible=false;
			}
			if(this.Label12.Text.Equals(this.lblMobileNo.Text))
			{
				this.rdMobile.Visible=false;
			}
			if(this.Label13.Text.Equals(this.lblNICNew.Text))
			{
				this.rdNICNew.Visible=false;
			}
			if(this.Label14.Text.Equals(this.lblNICOld.Text))
			{
				this.rdNICOld.Visible=false;
			}
			if(this.Label15.Text.Equals(this.lblKin.Text))
			{
				this.rdKin.Visible=false;
			}
			if(this.Label16.Text.Equals(this.lblBankAcctNo.Text))
			{
				this.rdBnkNo.Visible=false;
			}
			if(this.Label17.Text.Equals(this.lblAccountDetails.Text))
			{
				this.rdAcctDetails.Visible=false;
			}
			if(Session["requestImg"].ToString().Length<=0)
			{ 
				string path=Server.MapPath(@"..\employee\"+Session["requestImg"].ToString());
				bool IsFile=File.Exists(path);
				if(!IsFile)
				{
					this.btnRequestImg.Visible=false;
					this.rdoRequestPhoto.Visible=false;
				}
			} 
			if(this.Label26.Text.Equals(this.lblNick.Text))
			{
				this.rdoNick.Visible=false;
			}
			if(this.Label28.Text.Equals(this.lblNationality.Text))
			{
				this.rdoNationality.Visible=false;
			}
			if(this.Label29.Text.Equals(this.lblNationality2.Text))
			{
				rdoNationality2.Visible=false;
			}
		}

		public void InterfaceEmpInfo()
		{
			if(this.Label18.Text.Equals(this.lblEmpInfoCode.Text))
			{
				this.rdoEmpCode.Visible=false;
			}
			if(this.Label19.Text.Equals(lblEmpInfoName.Text))
			{
				this.rdoEmpName.Visible=false;
			}
			if(this.Label20.Text.Equals(lblEmpInfoExtension.Text))
			{
				this.rdoExtension.Visible=false;
			}
			if(this.Label21.Text.Equals(lblEmpInfoDept.Text))
			{
				this.rdoDepartment.Visible=false;
			}
			if(this.Label23.Text.Equals(lblEmpInfoDesignation.Text))
			{
				this.rdoDesignation.Visible=false;
			}
			if(this.Label24.Text.Equals(lblEmpInfoDOJ.Text))
			{
				this.rdoDOJ.Visible=false;
			}
			if(this.Label22.Text.Equals(lblEmpInfoFunctionalDesignation.Text))
			{
				this.rdoFunctionalDesignation.Visible=false;
			}
			if(this.Label25.Text.Equals(lblEmpInfoCity.Text))
			{
				this.rdoStation.Visible=false;
			}
 
			// if(this.Label27.Text.Equals(lblEmpInfoInsurance.Text))
			// {
			// this.rdoInsuranceCode.Visible=false;
			// }
 
			if(this.Label30.Text.Equals(lblEmpInfoTOE.Text))
			{
				this.rdoTOE.Visible=false;
			}
			if(this.Label32.Text.Equals(lblEmpInfoEmail.Text))
			{
				this.rdoEmailOffical.Visible=false;
			}
			if(this.Label33.Text.Equals(lblEmpInfoEObi.Text))
			{
				this.rdoEOBINO.Visible=false;
			}
			if(this.Label34.Text.Equals(lblEmpInfoSessi.Text))
			{
				this.rdoSessi.Visible=false;
			}
			// if(this.Label35.Text.Equals(TextBox1.Text))
			// {
			// this.rdoOthers.Visible=false;
			// }
			if(lblEmpLocation.Text.Equals(this.lblRempLocation.Text))
			{
				this.rdoLocation.Visible=false;
			}
 
		}

		public void eduChange(object sender, EventArgs e)
		{
			if(!_isRefresh)
			{
				con.Close(); 
				LinkButton btn=new LinkButton();
				btn=(LinkButton)sender;
				LinkButton dgButton=new LinkButton(); 
				for(int i=0;i<this.DataGrid4.Items.Count;i++)
				{
					dgButton=(LinkButton)this.DataGrid4.Items[i].FindControl("eduReject");
					if(dgButton==btn)
					{
						con.Open();
						string updateEdu="update t_educationinfo_temp set isrequest='"+2+"' where eduinfoid='"+Convert.ToInt32(this.DataGrid4.Items[i].Cells[8].Text)+"'";
						SqlCommand cmd=new SqlCommand(updateEdu,con);
						int j=cmd.ExecuteNonQuery();
						con.Close(); 
						if(j>0)
						{
							//this.DataGrid4.Visible=false;
							EducationRequest();
							if(this.DataGrid4.Items.Count<=0)
							{
								btnEdUpdate.Enabled=false;
							}
						}
					}
				}
				con.Close();
				Bulletin.PostMessage("Request Rejected","Your Educational request has been rejected "+DateTime.Now.ToString("d MMM, yyyy"),Session["user_id"].ToString(),10,1,Session["user_id"].ToString(),Request.QueryString[0].ToString());
////				con.Open();
////				Guid guid=new Guid();
////				guid=Guid.NewGuid();
////				string msg="insert into bulletin values('"+"Request Rejected"+"','"+ "Your Educational request has been rejected " +"','"+DateTime.Today.Date.ToString()+"','"+Session["user_id"].ToString()+"',dateadd(d,10,getdate()),'"+1+"','"+DateTime.Today.Date.ToString()+"','"+Session["user_id"].ToString()+"',NULL,NULL,'"+1+"','{"+guid.ToString() +"}')"; 
////				SqlCommand cMsg=new SqlCommand(msg,con);
////				cMsg.ExecuteNonQuery();
////				con.Close();
//// 
////				con.Open();
////				// string getId="select max(s_no) from bulletin";
////				// SqlCommand cm=new SqlCommand(getId,con);
////				// SqlDataReader rd=cm.ExecuteReader();
////				// string Id=""; 
////				// while(rd.Read())
////				// {
////				// Id=rd[0].ToString();
////				// }
////				// rd.Close();
////				string var="NULL";
////				string sendMessage="insert into t_sendmessage values("+ var +",'"+Request.QueryString[0].ToString()+"','"+1+"','{"+ guid.ToString() +"}')";
////				SqlCommand c=new SqlCommand(sendMessage,con);
////				c.ExecuteNonQuery();
////				con.Close(); 
			}
		}

		public void famChange(object sender, EventArgs e)
		{
			if(!_isRefresh)
			{
				con.Close(); 
				LinkButton btn=new LinkButton();
				btn=(LinkButton)sender;
				LinkButton dgButton=new LinkButton(); 
				for(int i=0;i<this.DataGrid2.Items.Count;i++)
				{
					dgButton=(LinkButton)this.DataGrid2.Items[i].FindControl("famReject");
					if(dgButton==btn)
					{
						con.Open();
						string updateEdu="update t_familydetail_temp set isrequest='"+2+"' where tempfd_id='"+Convert.ToInt32(this.DataGrid2.Items[i].Cells[9].Text)+"'";
						SqlCommand cmd=new SqlCommand(updateEdu,con);
						int j=cmd.ExecuteNonQuery();
						con.Close(); 
						if(j>0)
						{
							FamilyRequest();
							if(this.DataGrid2.Items.Count<=0)
							{
								btnFupdate.Enabled=false;
							}
						}
					}
				}

				con.Close();
				Bulletin.PostMessage("Request Rejected","Your Family Detail request has been rejected "+DateTime.Now.ToString("d MMM, yyyy"),Session["user_id"].ToString(), 10,1, Session["user_id"].ToString(),Request.QueryString[0].ToString());
////				con.Open();
////				Guid guid=new Guid();
////				guid=Guid.NewGuid();
////				string msg="insert into bulletin values('"+"Request Rejected"+"','"+ "Your Family Detail request has been rejected " +"','"+DateTime.Today.Date.ToString()+"','"+Session["user_id"].ToString()+"',dateadd(d,10,getdate()),'"+1+"','"+DateTime.Today.Date.ToString()+"','"+Session["user_id"].ToString()+"',NULL,NULL,'"+1+"','"+ guid.ToString() +"}')"; 
////				SqlCommand cMsg=new SqlCommand(msg,con);
////				cMsg.ExecuteNonQuery();
////				con.Close();
//// 
////				con.Open();
////				// string getId="select max(s_no) from bulletin";
////				// SqlCommand cm=new SqlCommand(getId,con);
////				// SqlDataReader rd=cm.ExecuteReader();
////				// string Id=""; 
////				// while(rd.Read())
////				// {
////				// Id=rd[0].ToString();
////				// }
////				// rd.Close();
////				string var="NULL";
////				string sendMessage="insert into t_sendmessage values("+ var +",'"+Request.QueryString[0].ToString()+"','"+1+"','{"+ guid.ToString() +"}')";
////				SqlCommand c=new SqlCommand(sendMessage,con);
////				c.ExecuteNonQuery();
////				con.Close();
			}
		}

		private void btnReject_Click(object sender, System.EventArgs e)
		{
			if(!_isRefresh)
			{
				string sqlQuery="";
				if(this.pnlEmployeeInfo.Visible)
				{
					sqlQuery=Session["empRequest"].ToString();
				}
				if(this.pnlPersonal.Visible)
				{
					sqlQuery=Session["perRequest"].ToString();
				}

				Bulletin.PostMessage("Request Rejected","Your Request has been Rejected, Request date is "+sqlQuery,Session["user_id"].ToString(),10,1,Session["user_id"].ToString(),Request.QueryString[0].ToString());
 
//				con.Open();
//				Guid guid=new Guid();
//				guid=Guid.NewGuid();
//				string msg="insert into bulletin values('"+"Request Rejected"+"','"+"Your Request has been Rejected, Request date is "+sqlQuery+"','"+DateTime.Today.Date.ToString()+"','"+Session["user_id"].ToString()+"',dateadd(d,10,getdate()),'"+1+"','"+DateTime.Today.Date.ToString()+"','"+Session["user_id"].ToString()+"',NULL,NULL,'"+1+"','{"+ guid.ToString() +"}' )"; 
//				SqlCommand cMsg=new SqlCommand(msg,con);
//				cMsg.ExecuteNonQuery();
//				con.Close();
// 
//				con.Open();
//
//				string var="NULL";
//				string sendMessage="insert into t_sendmessage values("+ var +",'"+Request.QueryString[0].ToString()+"','"+1+"','{"+ guid.ToString()+"}')";
//				SqlCommand c=new SqlCommand(sendMessage,con);
//				c.ExecuteNonQuery();
//				con.Close();

 
				con.Open();
				string updateRequest="";
				string updateEmpInfo="";
				string updatePerInfo="";
				if(Session["getDesicion"].ToString()=="EmployeeInfo")
				{
					updateRequest="update t_empinfo set isrequested='"+2+"' where pcode='"+Request.QueryString[0].ToString()+"'";
				}
				if(Session["getDesicion"].ToString()=="PersonalInfo")
				{
					updateRequest="update t_personalinfo set isrequested='"+2+"' where pcode='"+Request.QueryString[0].ToString()+"'";
				}
				if(Session["getDesicion"].ToString().Length==0 && this.pnlEmployeeInfo.Visible)
				{
					updateEmpInfo="update t_empinfo set isrequested='"+2+"' where pcode='"+Request.QueryString[0].ToString()+"'";
					SqlCommand cmd=new SqlCommand(updateEmpInfo,con);
					cmd.ExecuteNonQuery();
				}
				if(Session["getDesicion"].ToString().Length==0 && this.pnlPersonal.Visible)
				{
					updatePerInfo="update t_personalinfo set isrequested='"+2+"' where pcode='"+Request.QueryString[0].ToString()+"'";
					SqlCommand cmd=new SqlCommand(updatePerInfo,con);
					cmd.ExecuteNonQuery(); 
				}
				if(updateRequest.Length>0)
				{
					SqlCommand cmd=new SqlCommand(updateRequest,con);
					cmd.ExecuteNonQuery();
				}
				con.Close();
				this.lblMsg.Text="Request has been successfully rejected";
				this.lblMsg.Visible=true;
				this.btnUpdate.Enabled=false;
				Response.Write("<script>window.opener.location=window.opener.location; window.close();</script>");
			}
		}

		private void DataGrid1_SortCommand(object source, System.Web.UI.WebControls.DataGridSortCommandEventArgs e)
		{
			sort=e.SortExpression.ToString();
			FamilyInfo();
		}

		private void DataGrid2_SortCommand(object source, System.Web.UI.WebControls.DataGridSortCommandEventArgs e)
		{
			sort2=e.SortExpression.ToString();
			FamilyRequest();
		}

		private void DataGrid3_SortCommand(object source, System.Web.UI.WebControls.DataGridSortCommandEventArgs e)
		{
			sort3=e.SortExpression.ToString();
			EducationalInfo();
		}

		private void DataGrid4_SortCommand(object source, System.Web.UI.WebControls.DataGridSortCommandEventArgs e)
		{
			sort4=e.SortExpression.ToString();
			EducationRequest();
		}

		private void btnOriginalImg_Click(object sender, System.EventArgs e)
		{
			this.Image1.Visible=true;
			//this.Image1.ImageUrl="../employee/C10.jpg";
			this.Image1.ImageUrl=@"../employee/"+Session["realImg"].ToString();
		}

		private void btnRequestImg_Click(object sender, System.EventArgs e)
		{
			this.Image1.Visible=true;
			this.Image1.ImageUrl=@"..\tempEmployee\"+Session["requestImg"].ToString();
		}

		private void btnAdd_Click(object sender, System.EventArgs e)
		{
			
			con.Open();
			string var="NULL";
			//string insertData="insert into t_degree values('"+this.txtOtherDegree.Text+"','"+DateTime.Today.Date.ToString()+"',"+ var +",'"+Session["user_id"].ToString()+"',"+ var +",'"+1+"') "; 
			string insertData="INSERT INTO dbo.t_degree (degree, c_at, m_at, c_by, m_by, isactive) VALUES (@p_degree, GETDATE(), NULL, @p_c_by, NULL, 1)";
			SqlCommand cmd=new SqlCommand(insertData,con);
			SqlParameter p_degree= new SqlParameter("@p_degree",SqlDbType.VarChar, 100); p_degree.Value=txtOtherDegree.Text.Trim();
			SqlParameter p_c_by= new SqlParameter("@p_c_by",SqlDbType.Char, 10); p_c_by.Value=Session["user_id"].ToString();
			cmd.Parameters.Add(p_degree);
			cmd.Parameters.Add(p_c_by);

			int j=cmd.ExecuteNonQuery();
			this.txtOtherDegree.Visible=false;
			this.lblDegree.Visible=false;
			this.btnAdd.Visible=false;
			this.txtDegree.Visible=true;
			getCurrentDegree();
			for(int i=0;i<this.txtDegree.Items.Count;i++)
			{
				if(this.txtDegree.Items[i].Text.Equals(this.txtOtherDegree.Text))
				{
					this.txtDegree.SelectedIndex=i;
				}
			}
			this.txtOtherDegree.Text="";
			con.Close();
		}
		public void getCurrentDegree()
		{
			this.txtDegree.Items.Clear();
			string getDegree="select id,degree from t_degree where isactive='1'";
			SqlCommand cmDegree=new SqlCommand(getDegree,con);
			SqlDataReader rdDegree=cmDegree.ExecuteReader();
 
			ListItem it=new ListItem();
			it.Value="0";
			it.Text="Select--Degree";
			this.txtDegree.Items.Insert(0,it);
			int i=1;
			while(rdDegree.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rdDegree[0].ToString();
				itm.Text=rdDegree[1].ToString();
				this.txtDegree.Items.Insert(i,itm);
				i++;
			}
			rdDegree.Close();
			txtDegree.Items.Add(new ListItem("Other","-1"));
 
		}
		public void getCurrentInstitute()
		{
			this.txtInstitute.Items.Clear();
			string getInstitute="select id,institute_name from t_institute where isactive='1'";
			SqlCommand cmInstitute=new SqlCommand(getInstitute,con);
			SqlDataReader rdInstitute=cmInstitute.ExecuteReader();
 
			ListItem item2=new ListItem();
			item2.Value="0";
			item2.Text="Select--Institute";
			this.txtInstitute.Items.Insert(0,item2);
			int j=1;
			while(rdInstitute.Read())
			{
				ListItem itm=new ListItem();
				itm.Value=rdInstitute[0].ToString();
				itm.Text=rdInstitute[1].ToString();
				this.txtInstitute.Items.Insert(j,itm);
				j++;
			}
			rdInstitute.Close(); 
			txtInstitute.Items.Add(new ListItem("Other","-1"));
		}

		private void btnInstituteAdd_Click(object sender, System.EventArgs e)
		{
			con.Open();
			string var="NULL";
			//string insertData="insert into t_institute values('"+this.txtOtherInstitute.Text+"','"+DateTime.Today.Date.ToString()+"',"+ var +",'"+Session["user_id"].ToString()+"',"+ var +",'"+1+"') "; 
			string insertData="INSERT INTO dbo.t_institute (institute_name, c_at, m_at, c_by, m_by, isactive) VALUES (@p_institute_name, GETDATE(), NULL, @p_c_by, NULL, 1)";

			SqlParameter p_institute_name= new SqlParameter("@p_institute_name",SqlDbType.VarChar, 500); p_institute_name.Value=txtOtherInstitute.Text.Trim();
			SqlParameter p_c_by= new SqlParameter("@p_c_by",SqlDbType.Char, 10); p_c_by.Value=Session["user_id"].ToString();
	

			SqlCommand cmd=new SqlCommand(insertData,con);

			cmd.Parameters.Add(p_c_by);
			cmd.Parameters.Add(p_institute_name);

			int i=cmd.ExecuteNonQuery();
			getCurrentInstitute();
			this.txtOtherInstitute.Visible=false;
			this.btnInstituteAdd.Visible=false;
			this.txtInstitute.Visible=true;
			this.lblInstitute.Visible=false;
			for(int j=0;j<this.txtInstitute.Items.Count;j++)
			{
				if(this.txtInstitute.Items[j].Text.Equals(this.txtOtherInstitute.Text))
				{
					this.txtInstitute.SelectedIndex=j;
				}
			}
			this.txtOtherInstitute.Text="";
			con.Close();
		}

		public void getYears()
		{
			int startYear=1900;
			int endYear=DateTime.Today.Year;
			for(int i=endYear;i>=startYear;i--)
			{
				ListItem itm=new ListItem(i.ToString(),i.ToString());
				ListItem item=new ListItem(i.ToString(),i.ToString());
				this.DurationFrom.Items.Add(itm);
				this.DurationTo.Items.Add(item); 
			}
			ListItem itm2=new ListItem("In Progress","-1");
			this.DurationTo.Items.Insert(1,itm2);
		}

		private void txtDegree_SelectedIndexChanged(object sender, System.EventArgs e)
		{
 
		}

		private void lnkClose_Click(object sender, System.EventArgs e)
		{
			Response.Write("<script>window.opener.location=window.opener.location; window.close();</script>");
		}

		private void lnkEclose_Click(object sender, System.EventArgs e)
		{
			Response.Write("<script>window.opener.location=window.opener.location; window.close();</script>");
		}

		protected override void LoadViewState(object savedState)
		{
			object[] allStates = (object[]) savedState;
			base.LoadViewState(allStates[0]);
			_refreshState = (bool) allStates[1];
			try
			{
				_isRefresh = _refreshState == (bool) Session["__ISREFRESH"];
			}
			catch(Exception)
			{
				Response.Redirect("../Login.aspx");
			}

		}

		protected override object SaveViewState()
		{
			Session["__ISREFRESH"] = _refreshState;
			object[] allStates = new object[2];
			allStates[0] = base.SaveViewState();
			allStates[1] = !_refreshState;
			return allStates;
		}

	}
}
