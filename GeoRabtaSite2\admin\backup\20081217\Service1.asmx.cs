using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Web;
using System.Web.Services;
using System.Data.SqlClient;

namespace GeoRabtaSite.admin
{
	/// <summary>
	/// Summary description for Service1.
	/// </summary>
	public class Service1 : System.Web.Services.WebService
	{
		public Service1()
		{
			//CODEGEN: This call is required by the ASP.NET Web Services Designer
			InitializeComponent();
		}


		[WebMethod]
		public DataSet AllDeptRelImp(string station, string head, string user)
		{
			string temp = "";
			if (station != "All Stations")
				temp += " fb.EVStation='" + station + "' ";
			if (head != "All Evaluator")
				if (temp != "")
					temp += " AND fb.Head='" + head + "'";
				else
					temp = " fb.Head='" + head + "'";
			if (temp != "")
				temp += " AND ";

			string sqlC = "SELECT fb.Evaluator " +
				" FROM t_36feedback AS fb " +
				" WHERE " + temp + " fb.Attribute='Importance of service to your our business' " +
				" GROUP BY fb.Evaluator;";

			SqlConnection conn = new SqlConnection("Data Source=georaabta;Initial Catalog=36;User ID=sa;Password=*****");
			SqlDataAdapter daC = new SqlDataAdapter(sqlC, conn);
			DataSet dsC = new DataSet();
			daC.Fill(dsC);


			string sql = "SELECT fb.Department AS Attribute, Avg(fb.marks) AS AvgOfmarks, Count(fb.Evaluator) AS CountOfEvaluator, Sum([marks])/(" + dsC.Tables[0].Rows.Count.ToString() + "*10)*100 AS RS " +
				" FROM t_36feedback AS fb " +
				" WHERE " + temp + " fb.Attribute='Importance of service to your our business' " +
				" GROUP BY fb.Department " +
				" ORDER BY fb.Department ";


			//        SqlConnection conn = new SqlConnection("Data Source=georaabta;Initial Catalog=36;User ID=sa;Password=*****");
			SqlDataAdapter da = new SqlDataAdapter(sql, conn);

			DataSet ds = new DataSet("Attributes");
			da.Fill(ds, "data");
			DataView dv = ds.Tables[0].DefaultView;
			dv.Sort = "RS DESC";
			ds.Tables.Clear();
			//dv.Table;
			ds.Tables.Add(dv.Table);
			ds.Tables[0].Columns.Add("rank", typeof(string));

			int r = 1;
			double rv = double.Parse(ds.Tables[0].Rows[0]["RS"].ToString());
			ds.Tables[0].Rows[0]["rank"] = ranking(r) + " out of " + ds.Tables[0].Rows.Count.ToString(); //-->
			for (int j = 1; j < ds.Tables[0].Rows.Count; j++)
			{
				if (double.Parse(ds.Tables[0].Rows[j]["RS"].ToString()) != rv)
				{
					r = j + 1;
					rv = double.Parse(ds.Tables[0].Rows[j]["RS"].ToString());
				}
				ds.Tables[0].Rows[j]["rank"] = ranking(r) + " out of " + ds.Tables[0].Rows.Count.ToString();
			}

			DataSet ds2 = new DataSet("OverallAverages");
			ds2.Tables.Add("OverallAverages");
			ds2.Tables[0].Columns.Add("Attribute", typeof(string));
			ds2.Tables[0].Columns.Add("AvgOfmarks", typeof(double));
			ds2.Tables[0].Columns.Add("CountOfEvaluator", typeof(double));
			ds2.Tables[0].Columns.Add("Avg2", typeof(double));
			ds2.Tables[0].Columns.Add("rank", typeof(string));


			for (int j = 0; j < ds.Tables[0].Rows.Count; j++)
			{
				if (UserDept(user, ds.Tables[0].Rows[j]["Attribute"].ToString()) == true)
				{
					DataRow dr=ds2.Tables[0].NewRow();
					dr[0]=ds.Tables[0].Rows[j][0].ToString();
					dr[1]=ds.Tables[0].Rows[j][1].ToString();
					dr[2]=ds.Tables[0].Rows[j][2].ToString();
					dr[3]=ds.Tables[0].Rows[j][3].ToString();
					dr[4]=ds.Tables[0].Rows[j][4].ToString();
					
					
					ds2.Tables[0].Rows.Add(dr);
					/*ds2.Tables[0].Rows.Add(ds.Tables[0].Rows[j][0].ToString(),
						ds.Tables[0].Rows[j][1].ToString(),
						ds.Tables[0].Rows[j][2].ToString(),
						ds.Tables[0].Rows[j][3].ToString(),
						ds.Tables[0].Rows[j][4].ToString());*/
				}
			}
			DataView dv2 = ds2.Tables[0].DefaultView;
			dv2.Sort = "Attribute";
			ds2.Tables.Clear();
			ds2.Tables.Add(dv2.Table);
			return ds2;


		}

		private string ranking(int a)
		{
			int b = a;
			string temp = "";
			if (a == 1)
				temp = b.ToString() + "st";
			else if (a == 2)
				temp = b.ToString() + "nd";
			else if (a == 3)
				temp = b.ToString() + "rd";
			else if (a >= 4 && a <= 20)
				temp = b.ToString() + "th";
			else if (a > 20)
			{
				a %= 10;
				if (a == 1)
					temp = b.ToString() + "st";
				else if (a == 2)
					temp = b.ToString() + "nd";
				else if (a == 3)
					temp = b.ToString() + "rd";
				else if ((a >= 4 && a <= 20) || a == 0)
					temp = b.ToString() + "th";
			}
			return temp;
		}

		[WebMethod]
		public DataSet DeptRelImp(string station, string head, string department)
		{

			string temp = "";
			if (station != "All Stations")
				temp += " fb.EVStation='" + station + "' ";
			if (head != "All Evaluator")
				if (temp != "")
					temp += " AND fb.Head='" + head + "'";
				else
					temp = " fb.Head='" + head + "'";
			if (temp != "")
				temp += " AND ";

			string sqlC = "SELECT fb.Evaluator " +
				" FROM t_36feedback AS fb " +
				" WHERE " + temp + " fb.Attribute='Importance of service to your our business' " +
				" GROUP BY fb.Evaluator;";

			SqlConnection conn = new SqlConnection("Data Source=georaabta;Initial Catalog=36;User ID=sa;Password=*****");
			SqlDataAdapter daC = new SqlDataAdapter(sqlC, conn);
			DataSet dsC = new DataSet();
			daC.Fill(dsC);



			string sql = "SELECT fb.Department, Avg(fb.marks) AS AvgOfmarks, Count(fb.Evaluator) AS CountOfEvaluator, Sum([marks])/(" + dsC.Tables[0].Rows.Count.ToString() + "*10)*100 AS RS " +
				" FROM t_36feedback AS fb " +
				" WHERE " + temp + " fb.Attribute='Importance of service to your our business'" +
				" GROUP BY fb.Department " +
				" HAVING (((fb.Department)='" + department + "')) " +
				" ORDER BY fb.Department ";


			SqlDataAdapter da = new SqlDataAdapter(sql, conn);

			DataSet ds = new DataSet("DeptRelImp");
			da.Fill(ds, "data");
			return ds;
		}

		[WebMethod]
		public Double DeptAvg2(string department)
		{
			ArrayList al = new ArrayList();
			string sql = "SELECT t_36feedback.Department, Avg(t_36feedback.marks) AS AvgOfmarks " +
				" FROM t_36feedback " +
				" WHERE t_36feedback.Attribute<>'Importance of service to your our business' " +
				" GROUP BY t_36feedback.Department ";

			SqlConnection conn = new SqlConnection("Data Source=georaabta;Initial Catalog=36;User ID=sa;Password=*****");
			SqlDataAdapter da = new SqlDataAdapter(sql, conn);
			DataSet ds = new DataSet("Overall");
			da.Fill(ds, "Average");
			DataColumn dc = new DataColumn("Avg2", typeof(double));
			DataColumn dc2 = new DataColumn("OI", typeof(double));


			ds.Tables[0].Columns.Add(dc);
			ds.Tables[0].Columns.Add(dc2);


			double d = 0.0;
			for (int j = 0; j < ds.Tables[0].Rows.Count; j++)
			{
				d += double.Parse(ds.Tables[0].Rows[j]["AvgOfmarks"].ToString());
			}
			d = d / (double)ds.Tables[0].Rows.Count;
			for (int j = 0; j < ds.Tables[0].Rows.Count; j++)
			{
				ds.Tables[0].Rows[j]["Avg2"] = double.Parse(ds.Tables[0].Rows[j]["AvgOfmarks"].ToString()) / d * 100.0 - 100.0;
				ds.Tables[0].Rows[j]["OI"] = double.Parse(ds.Tables[0].Rows[j]["AvgOfmarks"].ToString()) / 7 * 100.0 - 100.0;
			}
			double dd = 0;
			for (int j = 0; j < ds.Tables[0].Rows.Count; j++)
			{
				string dept2 = ds.Tables[0].Rows[j]["Department"].ToString();
				if (dept2 == department)
				{

					dd = double.Parse(ds.Tables[0].Rows[j][1].ToString());
					break;
				}
			}
			return dd;
		}

		[WebMethod]
		public ArrayList AllUserDept(string userid)
		{
			ArrayList al = new ArrayList();
			SqlConnection conn = new SqlConnection("Data Source=georaabta;Initial Catalog=36;User ID=sa;Password=*****");

			string sql = "SELECT u.userid, d.deprtment " +
				" FROM (t_usersdept AS ud INNER JOIN t_users AS u ON ud.id = u.ID) INNER JOIN t_Deptartment AS d ON ud.deptid = d.deptid " +
				" WHERE (((u.userid)='" + userid + "')) " +
				" ORDER BY d.deprtment; ";


			SqlDataAdapter da = new SqlDataAdapter(sql, conn);
			SqlParameter _userid = new SqlParameter("@userid", userid);
			DataSet ds = new DataSet();
			da.Fill(ds);
			for (int j = 0; j < ds.Tables[0].Rows.Count; j++)
			{
				al.Add(ds.Tables[0].Rows[j]["deprtment"].ToString());
			}
			return al;

		}

		[WebMethod]
		public ArrayList Login(string userid, string password)
		{
			ArrayList al = new ArrayList();
			SqlConnection conn = new SqlConnection("Data Source=georaabta;Initial Catalog=36;User ID=sa;Password=*****");

			string sql = "SELECT t_users.password, t_users.fullname FROM t_users WHERE t_users.userid=@userid AND t_users.isactive=1";
			SqlDataAdapter da = new SqlDataAdapter(sql, conn);
			SqlParameter _userid = new SqlParameter("@userid", userid);
			da.SelectCommand.Parameters.Add(_userid);
        
			DataSet ds = new DataSet();
			da.Fill(ds);
			if (ds.Tables[0].Rows.Count > 0)
			{
				if (ds.Tables[0].Rows[0]["password"].ToString() == password)
				{
					al.Add("");
					al.Add(ds.Tables[0].Rows[0]["fullname"].ToString());
					al.Add(userid);
				}
				else
				{
					al.Add("Invalid User ID or Password");
				}
			}
			else
			{
				al.Add("Invalid User ID or Password");
			}
			return al;
		}

		[WebMethod]
		public ArrayList GetAllHeads(string station)
		{
			string temp = "";
			if (station != "All Stations")
				temp = " WHERE EVStation = '" + station + "' ";
			SqlConnection conn = new SqlConnection("Data Source=georaabta;Initial Catalog=36;User ID=sa;Password=*****");
			SqlDataAdapter da = new SqlDataAdapter("SELECT DISTINCT Head AS head FROM t_36feedback " + temp + " ORDER BY Head", conn);
			DataSet ds = new DataSet();
			da.Fill(ds);
			ArrayList al = new ArrayList();
			al.Add("All Evaluator");
			for (int j = 0; j < ds.Tables[0].Rows.Count; j++)
			{
				al.Add(ds.Tables[0].Rows[j]["head"].ToString());
			}

			return al;
		}

		[WebMethod]
		public double DeptAvg()
		{
			string sql = "SELECT t_36feedback.Department, Avg(t_36feedback.marks) AS AvgOfmarks " +
				" FROM t_36feedback " +
				" WHERE  t_36feedback.Attribute<>'Importance of service to your our business' " +
				" GROUP BY t_36feedback.Department ";

			SqlConnection conn = new SqlConnection("Data Source=georaabta;Initial Catalog=36;User ID=sa;Password=*****");
			SqlDataAdapter da = new SqlDataAdapter(sql, conn);
			DataSet ds = new DataSet("Overall");
			da.Fill(ds, "Average");
			DataColumn dc = new DataColumn("Avg2", typeof(double));
			DataColumn dc2 = new DataColumn("OI", typeof(double));
			ds.Tables[0].Columns.Add(dc);
			ds.Tables[0].Columns.Add(dc2);
			double d = 0.0;
			for (int j = 0; j < ds.Tables[0].Rows.Count; j++)
			{
				d += double.Parse(ds.Tables[0].Rows[j]["AvgOfmarks"].ToString());
			}
			d = d / (double)ds.Tables[0].Rows.Count;
			return d;

		}

		[WebMethod]
		public ArrayList GetAllStations()
		{
			SqlConnection conn = new SqlConnection("Data Source=georaabta;Initial Catalog=36;User ID=sa;Password=*****");
			SqlDataAdapter da = new SqlDataAdapter("SELECT DISTINCT EVStation AS station FROM         t_36feedback ORDER BY EVStation", conn);
			DataSet ds = new DataSet();
			da.Fill(ds);
			ArrayList al = new ArrayList();
			al.Add("All Stations");
			for (int j = 0; j < ds.Tables[0].Rows.Count; j++)
			{
				al.Add(ds.Tables[0].Rows[j]["station"].ToString());
			}

			return al;
		}

		[WebMethod]
		public DataSet OverallAverage(string station, string head, string user)
		{
			string temp = "";
			if (station != "All Stations")
				temp += " t_36feedback.EVStation='" + station + "' ";
			if (head != "All Evaluator")
				if (temp != "")
					temp += " AND t_36feedback.Head='" + head + "'";
				else
					temp = " t_36feedback.Head='" + head + "'";
			if (temp != "")
				temp += " AND";
			string sql = "SELECT t_36feedback.Department, Avg(t_36feedback.marks) AS AvgOfmarks " +
				" FROM t_36feedback " +
				" WHERE " + temp + " t_36feedback.Attribute<>'Importance of service to your our business' " +
				" GROUP BY t_36feedback.Department ";

			SqlConnection conn = new SqlConnection("Data Source=georaabta;Initial Catalog=36;User ID=sa;Password=*****");
			SqlDataAdapter da = new SqlDataAdapter(sql, conn);
			DataSet ds = new DataSet("Overall");
			da.Fill(ds, "Average");
			DataColumn dc = new DataColumn("Avg2", typeof(double));
			DataColumn dc2 = new DataColumn("OI", typeof(double));
			DataColumn rank = new DataColumn("rank", typeof(string));
			ds.Tables[0].Columns.Add(dc);
			ds.Tables[0].Columns.Add(dc2);

			ds.Tables[0].Columns.Add(rank);                 //-->

			DataView dv = ds.Tables[0].DefaultView;         //-->
			dv.Sort = "AvgOfmarks desc";                    //-->
			ds.Tables.Clear();                              //-->
			ds.Tables.Add(dv.Table);                    //-->

			double d = 0.0;
			for (int j = 0; j < ds.Tables[0].Rows.Count; j++)
			{
				d += double.Parse(ds.Tables[0].Rows[j]["AvgOfmarks"].ToString());
			}
			d = d / (double)ds.Tables[0].Rows.Count;
			for (int j = 0; j < ds.Tables[0].Rows.Count; j++)
			{
				ds.Tables[0].Rows[j]["Avg2"] = double.Parse(ds.Tables[0].Rows[j]["AvgOfmarks"].ToString()) / d * 100.0 - 100.0;
				ds.Tables[0].Rows[j]["OI"] = double.Parse(ds.Tables[0].Rows[j]["AvgOfmarks"].ToString()) / 7 * 100.0 - 100.0;
				ds.Tables[0].Rows[j]["rank"] = ranking(j + 1) + " out of " + ds.Tables[0].Rows.Count.ToString(); //-->
			}

			int r = 1;
			double rv = double.Parse(ds.Tables[0].Rows[0]["avgofmarks"].ToString());
			ds.Tables[0].Rows[0]["rank"] = ranking(r) + " out of " + ds.Tables[0].Rows.Count.ToString(); //-->
			for (int j = 1; j < ds.Tables[0].Rows.Count; j++)
			{
				if (double.Parse(ds.Tables[0].Rows[j]["avgofmarks"].ToString()) != rv)
				{
					r = j + 1;
					rv = double.Parse(ds.Tables[0].Rows[j]["avgofmarks"].ToString());
				}
				ds.Tables[0].Rows[j]["rank"] = ranking(r) + " out of " + ds.Tables[0].Rows.Count.ToString();
			}

			DataSet ds2 = new DataSet();
			ds2.Tables.Add("OverallAverages");
			ds2.Tables[0].Columns.Add("Department", typeof(string));
			ds2.Tables[0].Columns.Add("AvgOfmarks", typeof(double));
			ds2.Tables[0].Columns.Add("Avg2", typeof(double));
			ds2.Tables[0].Columns.Add("OI", typeof(double));
			ds2.Tables[0].Columns.Add("rank", typeof(string));         //-->
			for (int j = 0; j < ds.Tables[0].Rows.Count; j++)
			{
				if (UserDept(user, ds.Tables[0].Rows[j]["Department"].ToString()) == true)
				{
					DataRow dr=ds2.Tables[0].NewRow();
					dr[0]=ds.Tables[0].Rows[j][0].ToString();
					dr[1]=ds.Tables[0].Rows[j][1].ToString();
					dr[2]=ds.Tables[0].Rows[j][2].ToString();
					dr[3]=ds.Tables[0].Rows[j][3].ToString();
					dr[4]=ds.Tables[0].Rows[j][4].ToString();

					ds2.Tables[0].Rows.Add(dr);
				}
			}

			DataView dv2 = ds2.Tables[0].DefaultView;

			dv2.Sort = "Department";
			//ds2.Tables[0] = dv.Table;
			ds2.Tables.Clear();
			ds2.Tables.Add(dv2.Table);
			return ds2;
		}

		private bool UserDept(string userid, string dept)
		{
			string sql = "SELECT dept.deprtment" +
				" FROM (t_usersdept AS usrdept INNER JOIN t_users AS usr ON usrdept.id = usr.ID) INNER JOIN t_Deptartment AS dept ON usrdept.deptid = dept.deptid " +
				" WHERE (((dept.deprtment)='" + dept + "') AND ((usr.userid)='" + userid + "')); ";
			SqlConnection conn = new SqlConnection("Data Source=georaabta;Initial Catalog=36;User ID=sa;Password=*****");

			SqlDataAdapter da = new SqlDataAdapter(sql, conn);
			DataSet ds = new DataSet();
			da.Fill(ds);
			if (ds.Tables[0].Rows.Count > 0)
			{
				return true;
			}
			else
			{
				return false;
			}

		}

		[WebMethod]
		public DataSet GetAtributeAverges(string station, string head, string department,string user)
		{
			string temp = "";

			if (station != "All Stations")
				temp += " fb.EVStation='" + station + "' ";
			if (head != "All Evaluator")
				if (temp != "")
					temp += " AND fb.Head='" + head + "'";
				else
					temp = " fb.Head='" + head + "'";
			if (temp != "")
				temp += " AND";
			string sql = "SELECT fb.Attribute, Avg(fb.marks) AS AvgOfmarks " +
				" FROM t_36feedback AS fb " +
				" WHERE " + temp + " fb.Department='" + department + "' " +
				" GROUP BY fb.Attribute " +
				" HAVING fb.Attribute<>'Importance of service to your our business';";

			SqlConnection conn = new SqlConnection("Data Source=georaabta;Initial Catalog=36;User ID=sa;Password=*****");
			SqlDataAdapter da = new SqlDataAdapter(sql, conn);
			DataSet ds = new DataSet("Attributes");
			da.Fill(ds, "OverallAverages");

			DataColumn dc = new DataColumn("Avg2", typeof(double));
			DataColumn dc2 = new DataColumn("OI", typeof(double));
			DataColumn rank = new DataColumn("rank", typeof(string));
			ds.Tables[0].Columns.Add(dc);
			ds.Tables[0].Columns.Add(dc2);
			ds.Tables[0].Columns.Add(rank);
			//ds.Tables[0].Columns.Add(rank);
			double d = 0.0;
			for (int j = 0; j < ds.Tables[0].Rows.Count; j++)
			{
				d += double.Parse(ds.Tables[0].Rows[j]["AvgOfmarks"].ToString());
			}
			d = d / (double)ds.Tables[0].Rows.Count;
			for (int j = 0; j < ds.Tables[0].Rows.Count; j++)
			{
				ds.Tables[0].Rows[j]["Avg2"] = double.Parse(ds.Tables[0].Rows[j]["AvgOfmarks"].ToString()) / d * 100.0 - 100.0;
				ds.Tables[0].Rows[j]["OI"] = double.Parse(ds.Tables[0].Rows[j]["AvgOfmarks"].ToString()) / 7 * 100.0 - 100.0;
			}
        
			for (int j = 0; j < ds.Tables[0].Rows.Count; j++)
			{
				//ds.Tables[0].Rows[j]["rank"] = GetDeptAttributeRank(station, head, ds.Tables[0].Rows[j]["Attribute"].ToString(), User, department);
				ds.Tables[0].Rows[j]["rank"] = GetDeptAttributeRank(station, head, ds.Tables[0].Rows[j]["attribute"].ToString(), user, department);
			}
			return ds;

		}

		[WebMethod]
		public string HelloWorld()
		{
			return "Hello World";
		}

		[WebMethod]
		public DataSet GetDeptImportance(string station, string head, string department)
		{
			string temp = "";

			if (station != "All Stations")
				temp += " fb.EVStation='" + station + "' ";
			if (head != "All Evaluator")
				if (temp != "")
					temp += " AND fb.Head='" + head + "'";
				else
					temp = " fb.Head='" + head + "'";
			if (temp != "")
				temp += " AND";


			string sql = "SELECT fb.Department, Avg(fb.marks) AS AvgOfmarks " +
				" FROM t_36feedback AS fb " +
				" WHERE " + temp + " fb.Attribute='Importance of service to your our business' AND fb.Department='" + department + "' " +
				" GROUP BY fb.Department " +
				" ORDER BY fb.Department ";

			SqlConnection conn = new SqlConnection("Data Source=georaabta;Initial Catalog=36;User ID=sa;Password=*****");
			SqlDataAdapter da = new SqlDataAdapter(sql, conn);
			DataSet ds = new DataSet("DeptImportance");
			da.Fill(ds, "OverallAverages");

			DataColumn dc = new DataColumn("Avg2", typeof(double));
			DataColumn dc2 = new DataColumn("OI", typeof(double));
			ds.Tables[0].Columns.Add(dc);
			ds.Tables[0].Columns.Add(dc2);
			double d = 0.0;
			for (int j = 0; j < ds.Tables[0].Rows.Count; j++)
			{
				d += double.Parse(ds.Tables[0].Rows[j]["AvgOfmarks"].ToString());
			}
			d = d / (double)ds.Tables[0].Rows.Count;
			for (int j = 0; j < ds.Tables[0].Rows.Count; j++)
			{
				ds.Tables[0].Rows[j]["Avg2"] = double.Parse(ds.Tables[0].Rows[j]["AvgOfmarks"].ToString()) / d * 100.0 - 100.0;
				ds.Tables[0].Rows[j]["OI"] = double.Parse(ds.Tables[0].Rows[j]["AvgOfmarks"].ToString()) / 7 * 100.0 - 100.0;
			}

			return ds;

		}

		[WebMethod]
		public DataSet GetDeptAttribute(string station, string head, string attribute, string user)
		{
			string temp = "";

			if (station != "All Stations")
				temp += " fb.EVStation='" + station + "' ";
			if (head != "All Evaluator")
				if (temp != "")
					temp += " AND fb.Head='" + head + "'";
				else
					temp = " fb.Head='" + head + "'";
			if (temp != "")
				temp += " AND";


			string sql = "SELECT fb.Department as Attribute, Avg(fb.marks) AS AvgOfmarks " +
				" FROM t_36feedback AS fb " +
				" WHERE " + temp + " fb.Attribute='" + attribute + "' " +
				" GROUP BY fb.Department ";

			SqlConnection conn = new SqlConnection("Data Source=georaabta;Initial Catalog=36;User ID=sa;Password=*****");
			SqlDataAdapter da = new SqlDataAdapter(sql, conn);
			DataSet ds = new DataSet("DeptAttribute");
			da.Fill(ds, "Average");
			DataColumn dc = new DataColumn("Avg2", typeof(double));
			DataColumn dc2 = new DataColumn("OI", typeof(double));
			DataColumn rank = new DataColumn("rank", typeof(string));

			ds.Tables[0].Columns.Add(dc);
			ds.Tables[0].Columns.Add(dc2);
			ds.Tables[0].Columns.Add(rank);

			DataView dv = ds.Tables[0].DefaultView;
			dv.Sort = "AvgOfmarks DESC";
			ds.Tables.Clear();
			ds.Tables.Add(dv.Table);
			double d = 0.0;
			for (int j = 0; j < ds.Tables[0].Rows.Count; j++)
			{
				d += double.Parse(ds.Tables[0].Rows[j]["AvgOfmarks"].ToString());
			}
			d = d / (double)ds.Tables[0].Rows.Count;
			for (int j = 0; j < ds.Tables[0].Rows.Count; j++)
			{
				ds.Tables[0].Rows[j]["Avg2"] = double.Parse(ds.Tables[0].Rows[j]["AvgOfmarks"].ToString()) / d * 100.0 - 100.0;
				ds.Tables[0].Rows[j]["OI"] = double.Parse(ds.Tables[0].Rows[j]["AvgOfmarks"].ToString()) / 7 * 100.0 - 100.0;
				ds.Tables[0].Rows[j]["rank"] = ranking(j + 1) + " out of " + ds.Tables[0].Rows.Count.ToString();
			}

			int r = 1;
			double rv = double.Parse(ds.Tables[0].Rows[0]["avgofmarks"].ToString());
			ds.Tables[0].Rows[0]["rank"] = ranking(r) + " out of " + ds.Tables[0].Rows.Count.ToString(); //-->
			for (int j = 1; j < ds.Tables[0].Rows.Count; j++)
			{
				if (double.Parse(ds.Tables[0].Rows[j]["avgofmarks"].ToString()) != rv)
				{
					r = j + 1;
					rv = double.Parse(ds.Tables[0].Rows[j]["avgofmarks"].ToString());
				}
				ds.Tables[0].Rows[j]["rank"] = ranking(r) + " out of " + ds.Tables[0].Rows.Count.ToString();
			}

			DataSet ds2 = new DataSet();
			ds2.Tables.Add("OverallAverages");
			ds2.Tables[0].Columns.Add("Attribute", typeof(string));
			ds2.Tables[0].Columns.Add("AvgOfmarks", typeof(double));
			ds2.Tables[0].Columns.Add("Avg2", typeof(double));
			ds2.Tables[0].Columns.Add("OI", typeof(double));
			ds2.Tables[0].Columns.Add("rank", typeof(string));
			for (int j = 0; j < ds.Tables[0].Rows.Count; j++)
			{
				if (UserDept(user, ds.Tables[0].Rows[j]["Attribute"].ToString()) == true)
				{
					DataRow dr=ds2.Tables[0].NewRow();
					dr[0]=ds.Tables[0].Rows[j][0].ToString();
					dr[1]=ds.Tables[0].Rows[j][1].ToString();
					dr[2]=ds.Tables[0].Rows[j][2].ToString();
					dr[3]=ds.Tables[0].Rows[j][3].ToString();
					dr[4]=ds.Tables[0].Rows[j][4].ToString();

					ds2.Tables[0].Rows.Add(dr);
				}
			}
			DataView dv2 = ds2.Tables[0].DefaultView;
			dv2.Sort = "Attribute";
			ds2.Tables.Clear();
			ds2.Tables.Add(dv2.Table);
			return ds2;
		}

		public string GetDeptAttributeRank(string station, string head, string attribute, string user, string department)
		{
			string temp = "";

			if (station != "All Stations")
				temp += " fb.EVStation='" + station + "' ";
			if (head != "All Evaluator")
				if (temp != "")
					temp += " AND fb.Head='" + head + "'";
				else
					temp = " fb.Head='" + head + "'";
			if (temp != "")
				temp += " AND";


			string sql = "SELECT fb.Department as Attribute, Avg(fb.marks) AS AvgOfmarks " +
				" FROM t_36feedback AS fb " +
				" WHERE " + temp + " fb.Attribute='" + attribute + "' " +
				" GROUP BY fb.Department ";

			SqlConnection conn = new SqlConnection("Data Source=georaabta;Initial Catalog=36;User ID=sa;Password=*****");
			SqlDataAdapter da = new SqlDataAdapter(sql, conn);
			DataSet ds = new DataSet("DeptAttribute");
			da.Fill(ds, "Average");
			DataColumn dc = new DataColumn("Avg2", typeof(double));
			DataColumn dc2 = new DataColumn("OI", typeof(double));
			DataColumn rank = new DataColumn("rank", typeof(string));

			ds.Tables[0].Columns.Add(dc);
			ds.Tables[0].Columns.Add(dc2);
			ds.Tables[0].Columns.Add(rank);

			DataView dv = ds.Tables[0].DefaultView;
			dv.Sort = "AvgOfmarks DESC";
			ds.Tables.Clear();
			ds.Tables.Add(dv.Table);
			double d = 0.0;
			for (int j = 0; j < ds.Tables[0].Rows.Count; j++)
			{
				d += double.Parse(ds.Tables[0].Rows[j]["AvgOfmarks"].ToString());
			}
			d = d / (double)ds.Tables[0].Rows.Count;
			for (int j = 0; j < ds.Tables[0].Rows.Count; j++)
			{
				ds.Tables[0].Rows[j]["Avg2"] = double.Parse(ds.Tables[0].Rows[j]["AvgOfmarks"].ToString()) / d * 100.0 - 100.0;
				ds.Tables[0].Rows[j]["OI"] = double.Parse(ds.Tables[0].Rows[j]["AvgOfmarks"].ToString()) / 7 * 100.0 - 100.0;
				ds.Tables[0].Rows[j]["rank"] = ranking(j + 1) + " out of " + ds.Tables[0].Rows.Count.ToString();
			}

			int r = 1;
			double rv = double.Parse(ds.Tables[0].Rows[0]["avgofmarks"].ToString());
			ds.Tables[0].Rows[0]["rank"] = ranking(r) + " out of " + ds.Tables[0].Rows.Count.ToString(); //-->
			for (int j = 1; j < ds.Tables[0].Rows.Count; j++)
			{
				if (double.Parse(ds.Tables[0].Rows[j]["avgofmarks"].ToString()) != rv)
				{
					r = j + 1;
					rv = double.Parse(ds.Tables[0].Rows[j]["avgofmarks"].ToString());
				}
				ds.Tables[0].Rows[j]["rank"] = ranking(r) + " out of " + ds.Tables[0].Rows.Count.ToString();
			}

			DataSet ds2 = new DataSet();
			ds2.Tables.Add("OverallAverages");
			ds2.Tables[0].Columns.Add("Attribute", typeof(string));
			ds2.Tables[0].Columns.Add("AvgOfmarks", typeof(double));
			ds2.Tables[0].Columns.Add("Avg2", typeof(double));
			ds2.Tables[0].Columns.Add("OI", typeof(double));
			ds2.Tables[0].Columns.Add("rank", typeof(string));
			for (int j = 0; j < ds.Tables[0].Rows.Count; j++)
			{
				if (UserDept(user, ds.Tables[0].Rows[j]["Attribute"].ToString()) == true)
				{
					DataRow dr=ds2.Tables[0].NewRow();
					dr[0]=ds.Tables[0].Rows[j][0].ToString();
					dr[1]=ds.Tables[0].Rows[j][1].ToString();
					dr[2]=ds.Tables[0].Rows[j][2].ToString();
					dr[3]=ds.Tables[0].Rows[j][3].ToString();
					dr[4]=ds.Tables[0].Rows[j][4].ToString();

					ds2.Tables[0].Rows.Add(dr);
				}
			}
			DataView dv2 = ds2.Tables[0].DefaultView;
			dv2.Sort = "Attribute";
			ds2.Tables.Clear();
			ds2.Tables.Add(dv2.Table);
			//return ds2;
			string s = "";
			for (int j = 0; j < ds2.Tables[0].Rows.Count; j++)
			{
				if (ds2.Tables[0].Rows[j][0].ToString() == department)
				{
					s = ds2.Tables[0].Rows[j]["rank"].ToString();
				}
			}
			return s;
		}



		#region Component Designer generated code
		
		//Required by the Web Services Designer 
		private IContainer components = null;
				
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
		}

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		protected override void Dispose( bool disposing )
		{
			if(disposing && components != null)
			{
				components.Dispose();
			}
			base.Dispose(disposing);		
		}
		
		#endregion

		// WEB SERVICE EXAMPLE
		// The HelloWorld() example service returns the string Hello World
		// To build, uncomment the following lines then save and build the project
		// To test this web service, press F5

//		[WebMethod]
//		public string HelloWorld()
//		{
//			return "Hello World";
//		}
	}
}
