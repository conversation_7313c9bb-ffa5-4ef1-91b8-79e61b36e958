using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for EmpTraining.
	/// </summary>
	public class EmpTraining : System.Web.UI.Page
	{
		protected System.Web.UI.WebControls.Label lblTrainingMsg;
		protected System.Web.UI.WebControls.LinkButton Linkbutton3;
		protected System.Web.UI.WebControls.DataGrid dgRegTraining;
		protected System.Web.UI.WebControls.Panel pnlTraings;
		protected System.Web.UI.WebControls.TextBox txtTrainingTitle;
		protected System.Web.UI.WebControls.DropDownList ddlTrainingField;
		protected System.Web.UI.WebControls.Label lblOtherTrainingField;
		protected System.Web.UI.WebControls.TextBox txtOtherTrainingField;
		protected System.Web.UI.WebControls.DropDownList ddlDuration;
		protected System.Web.UI.WebControls.DropDownList ddlDurationUnit;
		protected System.Web.UI.WebControls.DropDownList ddlTrainingMonth;
		protected System.Web.UI.WebControls.DropDownList ddlTrainingYear;
		protected System.Web.UI.WebControls.CheckBox CheckBox1;
		protected System.Web.UI.WebControls.Label lblTrainingID;
		protected System.Web.UI.WebControls.TextBox txtTrainingInstitute;
		protected System.Web.UI.WebControls.DropDownList ddlCountry;
		protected System.Web.UI.WebControls.DropDownList ddlCity;
		protected System.Web.UI.WebControls.Label lblOtherCity;
		protected System.Web.UI.WebControls.TextBox txtOtherCity;
		protected System.Web.UI.WebControls.Button cmdAddTrainingReq;
		protected System.Web.UI.WebControls.Button btnCancelTrain;
		protected System.Web.UI.WebControls.Panel pnlTrainingDetails;
		protected System.Web.UI.WebControls.TextBox TextBox1;
		protected System.Web.UI.WebControls.TextBox txtX;
		protected System.Web.UI.WebControls.TextBox txtY;
		protected System.Web.UI.HtmlControls.HtmlTable Table11;
		protected System.Web.UI.HtmlControls.HtmlTable pnlTraining;
		protected System.Web.UI.WebControls.DataGrid dgTraining2;
		protected System.Web.UI.HtmlControls.HtmlInputFile fileExp;
		protected System.Web.UI.WebControls.Label lblPCode;
		protected System.Web.UI.WebControls.Label lblEmpInfo;
		protected System.Web.UI.WebControls.Label lblEduToolTip;
		protected System.Web.UI.WebControls.Panel Panel1;
		protected System.Web.UI.WebControls.HyperLink hlMyRequests;
		protected System.Web.UI.WebControls.ImageButton imgMyGrievance;
		protected System.Web.UI.WebControls.ImageButton ImgMyAttendance;
		protected System.Web.UI.WebControls.ImageButton imgMyLeave;
		protected System.Web.UI.WebControls.ImageButton ibMySelf;
		protected System.Web.UI.WebControls.ImageButton ibSalary;
		protected System.Web.UI.WebControls.ImageButton ImageButton5;
		protected System.Web.UI.WebControls.HyperLink hlMyExp;
		protected System.Web.UI.WebControls.ImageButton imgTraining;
		protected System.Web.UI.WebControls.ImageButton ImageButton4;
		protected System.Web.UI.WebControls.ImageButton ImageButton3;
		protected System.Web.UI.WebControls.ImageButton ImageButton2;
		protected System.Web.UI.WebControls.ImageButton ImageButton1;
		protected SqlConnection conn;

		public void DeleteTrainingReq(object sender, System.EventArgs e)
		{
			LinkButton rLink=(LinkButton)sender;
			for(int i=0;i<dgRegTraining.Items.Count;i++)
			{
				LinkButton dLink=(LinkButton)dgRegTraining.Items[i].FindControl("lbDelTrainingReq");
				if(dLink.Equals(rLink))
				{
					string id=dgRegTraining.Items[i].Cells[0].Text;
					UpdateRequest("cv_TrainingReq",id,"4");
				}
			}
			UpdateTrainingGrid();
		}


		private void UpdateRequest(string tablename,string reqid, string status)
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			SqlCommand cmd=new SqlCommand("update "+tablename+" set AppFlag="+status+",AppRejDate=Getdate(),AppRejBy='"+Session["user_id"].ToString()+"' where reqid="+reqid,conn);
			cmd.ExecuteNonQuery();
			conn.Close();
		}

		private bool isTrainingRequested(string trid)
		{
			if (trid!="")
			{
				string sql="SELECT reqid FROM dbo.cv_TrainingReq WHERE (AppFlag = 2) AND (trainingid= "+trid+")";
				SqlConnection conn=new SqlConnection(Connection.ConnectionString);
				SqlDataAdapter da=new SqlDataAdapter(sql,conn);
				DataSet ds=new DataSet();
				da.Fill(ds);
				if(ds.Tables[0].Rows.Count>0)
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				return false;
			}
		}
		public void DeleteTraining(object sender,EventArgs e)
		{
			string replyatrequest=GetReplyMsg("replyatrequest","39");

			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			SqlTransaction tran=conn.BeginTransaction();

			LinkButton rLink=(LinkButton)sender;
			for(int i=0;i<dgTraining2.Items.Count;i++)
			{
				LinkButton dLink=(LinkButton)dgTraining2.Items[i].FindControl("lbDeleteTraining");
				if(dLink.Equals(rLink))
				{
					string id=dgTraining2.Items[i].Cells[0].Text;

					if(isTrainingRequested(id))
					{
						string ssql="update cv_TrainingReq set AppFlag=4 where trainingid=" + id + " and AppFlag=2";
						SqlCommand cmd2=new SqlCommand(ssql,conn);
						cmd2.Transaction=tran;
						cmd2.ExecuteNonQuery();
					}

					string sql=" SELECT trainingid, PCode, TrainingTitle, TrainingField, TrainingFieldOther, TrainingMonth, TrainingYear, Duration, DurationUnit, InstituteName, Country, City, "+
						" CityOther, TrainingInGeo "+
						" FROM dbo.cv_Training "+
						" WHERE (trainingid = "+id+")";

					SqlDataAdapter da=new SqlDataAdapter(sql,conn);
					da.SelectCommand.Transaction=tran;

					DataSet ds=new DataSet();
					da.Fill(ds);
					SqlCommand cmd=new SqlCommand("insert into cv_trainingreq(trainingid,PCode,TrainingTitle,TrainingField,TrainingFieldOther,TrainingMonth,TrainingYear,Duration,DurationUnit,InstituteName,Country,City,CityOther,TrainingInGeo,c_at,Addflag,wfid,AppFlag) " +
						" values(@trainingid,@PCode,@TrainingTitle,@TrainingField,@TrainingFieldOther,@TrainingMonth,@TrainingYear,@Duration,@DurationUnit,@InstituteName,@Country,@City,@CityOther,@TrainingInGeo,getdate(),3,39,2) ",conn);
					cmd.Transaction=tran;

					SetParameterValue(cmd,ds,"trainingid");
					SetParameterValue(cmd,ds,"pcode");
					SetParameterValue(cmd,ds,"TrainingTitle");
					SetParameterValue(cmd,ds,"TrainingField");
					SetParameterValue(cmd,ds,"TrainingFieldOther");
					SetParameterValue(cmd,ds,"TrainingMonth");
					SetParameterValue(cmd,ds,"TrainingYear");
					SetParameterValue(cmd,ds,"Duration");
					SetParameterValue(cmd,ds,"DurationUnit");
					SetParameterValue(cmd,ds,"InstituteName");
					SetParameterValue(cmd,ds,"Country");
					SetParameterValue(cmd,ds,"City");
					SetParameterValue(cmd,ds,"TrainingInGeo");
					SetParameterValue(cmd,ds,"CityOther");
					cmd.ExecuteNonQuery();
					lblTrainingMsg.Visible=true;
					Bulletin.PostReqMessage(conn,tran,replyatrequest,"GEO Raabta",Session["user_id"].ToString(),1,"Training","cv_TrainingReq");
					tran.Commit();
					conn.Close();
					UpdateTrainingGrid();
					break;
				}
			}
			
		}

		private void SetParameterValue(SqlCommand cmd, DataSet ds, string field)
		{
			cmd.Parameters.Add("@"+field,ds.Tables[0].Rows[0][field].ToString());
		}

		public string GetTooltip(int ID)
		{
			SqlConnection con=new SqlConnection(Connection.ConnectionString);
			con.Open();
			SqlCommand cmd=new SqlCommand("select tooltip from t_fieldsmgt where f_id="+ID+"",con);
			string message="";
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				message=rd[0].ToString();	
			}
			message+="<br>";
			message=HttpUtility.HtmlEncode(message);
			message=message.Replace("\r\n","<br/>");
			rd.Close();
			cmd=new SqlCommand("select d.documentname from t_documentsrequired d,t_fieldmgtdocEmp f where f.documentbyemployee=d.d_id and f.f_id="+ID+"",con);
			rd=cmd.ExecuteReader();
			message+="<ul>";
			while(rd.Read())
			{
				//message+="<STRONG>/STRONG> "+rd[0].ToString()+"<br>";
				message+="<li>"+HttpUtility.HtmlEncode(rd[0].ToString()).Replace("\r\n","<br/>")+"</li>";
			}
			message+="</ul>";
			rd.Close();
			con.Close();
			//Response.Write(message);
			return message;
		}

		private string GetReplyMessage(string wfid)
		{
			string sql="select replyatrequest from t_fieldsmgt where f_id="+wfid;
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			conn.Close();
			conn.Dispose();
			if(ds.Tables[0].Rows.Count>0)
				return ds.Tables[0].Rows[0][0].ToString();
			else
				return "";

		}
		private void Page_Load(object sender, System.EventArgs e)
		{
			conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			lblTrainingMsg.Visible=false;
			if(!IsPostBack)
			{
				ibMySelf.Attributes.Add("onclick","window.open('myinfo.aspx','MyProfile','toolbar=no,statusbar=no,addressbar=no,scrollbars=yes,resizable=no,width=700,height=650');return false;");

				lblEduToolTip.Attributes.Add("onmouseover","Tip('<b><font size=2px COLOR=Black>Training</font></b><br/><br/>"+GetTooltip(38)+"', WIDTH, 268, SHADOW, true, FADEIN, 300, FADEOUT, 300,  BGCOLOR, '#F3B114', BGIMG, 'images/one.jpg', FONTCOLOR, '#ffffff',BORDERCOLOR,'#ffffff')");
				lblEduToolTip.Attributes.Add("onmouseout","UnTip()");

				ResetTrainingForm();
				UpdateTrainingGrid();
				FillDDL(ddlCountry,"cv_country","cname","countryid","","Select Country",true,conn);
				
				FillDDL(ddlTrainingField,"cv_trainingfields","trainingfield","trainingfieldid","","Select Training Field",true,conn);
				FillYears();
				ddlDuration.Items.Clear();
				for(int j=1;j<=100;j++)
				{
					ddlDuration.Items.Add(new ListItem(j.ToString(),j.ToString()));
				}
				ddlTrainingField.Attributes.Add("onchange","ShowOther('ddlTrainingField','-1','divOtherField','txtOtherTrainingField');");
				ddlCity.Attributes.Add("onchange","ShowOther('ddlCity','-1','divOtherCity','txtOtherCity');");
				ImageButton5.Attributes.Add("onclick","window.open('admin/organogram.htm','_blank','resizable=yes,menubar=no,scrollbars=yes,top=0,status=no');return false;");
				this.imgMyLeave.Attributes.Add("onclick","return OpenURL('MyLeaveBalance.aspx',650,300);");
				this.ImgMyAttendance.Attributes.Add("onclick","return OpenURL('MyAttendance.aspx',700,400);");
				//this.Image1.ImageUrl=@"employee\"+Session["user_id"].ToString()+".jpg";
				if(Request.QueryString.Count>0)
				{
					this.lblPCode.Text=Request.QueryString[0].ToString();
					this.lblEmpInfo.Text=Request.QueryString[1].ToString();
				}
			}
		}

		private void FillDDL(DropDownList ddl, string table, string val, string key, string condition, string title, bool fillother, SqlConnection con)
		{
			if (condition!="")
				condition="where "+condition;
			string sql="select "+key+","+val+" from "+table+" "+condition+" order by "+val;
			SqlDataAdapter da=new SqlDataAdapter(sql,con);
			DataSet ds=new DataSet();
			da.Fill(ds);
			ddl.Items.Clear();
			ddl.Items.Add("Select - "+title);
			for(int j=0;j<ds.Tables[0].Rows.Count;j++)
			{
				ddl.Items.Add(new ListItem(ds.Tables[0].Rows[j][val].ToString(),
					ds.Tables[0].Rows[j][key].ToString()));
			}
			if(fillother)
				ddl.Items.Add(new ListItem("Other","-1"));
			
		}

		public void FillYears()
		{
			int startYear=1900;
			int endYear=DateTime.Today.Year;
			for(int i=endYear;i>=startYear;i--)
			{
				ListItem item=new ListItem(i.ToString(),i.ToString());
			
			
				ddlTrainingYear.Items.Add(item);
				//ddlServedFromYear.Items.Add(item);
				//ddlServedTillYear.Items.Add(item);
			}
			ddlTrainingYear.Items.Insert(0,new ListItem("","-1"));
			//ddlServedFromYear.Items.Insert(0,new ListItem("",""));
			//ddlServedTillYear.Items.Insert(0,new ListItem("",""));

			ListItem itm2=new ListItem("In Progress","-1");
			
		}
		private bool TraReqExit(string id,SqlConnection conn)
		{
			SqlDataAdapter da=new SqlDataAdapter("SELECT reqid FROM dbo.cv_TrainingReq WHERE (AppFlag = 2) AND (AddFlag IN (1, 2, 3)) AND (trainingid = "+id+")",conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			if(ds.Tables[0].Rows.Count>0)
				return true;
			else
				return false;
		}

		private void UpdateTrainingGrid()
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			string sql="SELECT dbo.cv_Training.trainingid, dbo.cv_Training.PCode, dbo.cv_Training.TrainingTitle AS [Training Title], " +
				" CASE TrainingField WHEN - 1 THEN trainingfieldother ELSE" +
				" (SELECT TrainingField" +
				" FROM cv_TrainingFields" +
				" WHERE (TrainingFieldID = cv_Training.TrainingField)) END AS [Training Field], " +
				" dbo.cv_Months.Month + '-' + CAST(dbo.cv_Training.TrainingYear AS varchar) AS [Training (Month-Year)], CAST(dbo.cv_Training.Duration AS varchar) " +
				" + ' - ' + CASE dbo.cv_Training.DurationUnit WHEN 'D' THEN 'Day' WHEN 'Y' THEN 'Year' WHEN 'W' THEN 'Week' WHEN 'M' THEN 'Month' When 'H' then 'Hours' END AS [Training Duration]," +
				" dbo.cv_Training.InstituteName, dbo.cv_country.cname, CASE City WHEN - 1 THEN CityOther ELSE" +
				" (SELECT cityname" +
				" FROM cv_city" +
				" WHERE cityid = cv_training.city) END AS City" +
				" FROM dbo.cv_Training INNER JOIN" +
				" dbo.cv_Months ON dbo.cv_Training.TrainingMonth = dbo.cv_Months.MonthID INNER JOIN" +
				" dbo.cv_country ON dbo.cv_Training.Country = dbo.cv_country.countryid" +
				" WHERE (dbo.cv_Training.PCode = '" + Session["user_id"].ToString() + "') and (dbo.cv_Training.isactive=1)";

			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			dgTraining2.DataSource=ds.Tables[0].DefaultView;
			dgTraining2.DataBind();
			//AssignConfim(dgTraining,"lbDeleteTraining","Are you sure to delete this record?");
			for(int j=0;j<dgTraining2.Items.Count;j++)
			{
				if(TraReqExit(dgTraining2.Items[j].Cells[0].Text,conn))
				{
					LinkButton lb=(LinkButton)dgTraining2.Items[j].FindControl("lbDeleteTraining");
					lb.Attributes.Add("onclick","return confirm('A request already exist for this record, are you sure post this record for deletion?');");
				}
				else
				{
					LinkButton lb=(LinkButton)dgTraining2.Items[j].FindControl("lbDeleteTraining");
					lb.Attributes.Add("onclick","return confirm('Are you sure to delete this record?');");
				}
			}
			

			sql=" SELECT tr.trainingid, tr.PCode, tr.TrainingTitle AS [Training Title], CASE tr.TrainingField WHEN - 1 THEN tr.trainingfieldother ELSE " +
				" (SELECT TrainingField " +
				" FROM cv_TrainingFields " +
				" WHERE (TrainingFieldID = tr.TrainingField)) END AS [Training Field], mo.Month + '-' + CAST(tr.TrainingYear AS varchar) AS [Training (Month-Year)], " +
				" CAST(tr.Duration AS varchar) " +
				" + ' - ' + CASE tr.DurationUnit WHEN 'D' THEN 'Day' WHEN 'Y' THEN 'Year' WHEN 'W' THEN 'Week' WHEN 'M' THEN 'Month' When 'H' then 'Hours' END AS [Training Duration], " +
				" tr.InstituteName, co.cname, CASE City WHEN - 1 THEN CityOther ELSE " +
				" (SELECT cityname " +
				" FROM cv_city " +
				" WHERE cityid = tr.city) END AS City, tr.reqid " +
				" FROM dbo.cv_TrainingReq AS tr INNER JOIN " +
				" dbo.cv_Months AS mo ON tr.TrainingMonth = mo.MonthID INNER JOIN " +
				" dbo.cv_country AS co ON tr.Country = co.countryid " +
				" WHERE (tr.PCode = '"+Session["user_id"].ToString()+"') AND (tr.AppFlag = 2) ";
			da=new SqlDataAdapter(sql,conn);
			da.Fill(ds,"req");
			dgRegTraining.DataSource=ds.Tables[1].DefaultView;
			dgRegTraining.DataBind();
			AssignConfim(dgRegTraining,"lbDelTrainingReq","Are you sure to cancel this request?");

		
		}

		private void AssignConfim(DataGrid dg, string controlName, string msg)
		{
			for(int i=0;i<dg.Items.Count;i++)
			{
				LinkButton lnk=(LinkButton)dg.Items[i].FindControl(controlName);
				lnk.Attributes.Clear();
				lnk.Attributes.Add("onclick","return confirm('"+msg+"');"); 
			}
		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.ImageButton1.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton1_Click);
			this.ImageButton2.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton2_Click);
			this.ImageButton3.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton3_Click);
			this.ImageButton4.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton4_Click);
			this.imgTraining.Click += new System.Web.UI.ImageClickEventHandler(this.imgTraining_Click);
			this.ImageButton5.Click += new System.Web.UI.ImageClickEventHandler(this.ImageButton5_Click);
			this.imgMyGrievance.Click += new System.Web.UI.ImageClickEventHandler(this.imgMyGrievance_Click);
			this.dgTraining2.SelectedIndexChanged += new System.EventHandler(this.dgTraining2_SelectedIndexChanged);
			this.Linkbutton3.Click += new System.EventHandler(this.Linkbutton3_Click);
			this.dgRegTraining.SelectedIndexChanged += new System.EventHandler(this.dgRegTraining_SelectedIndexChanged);
			this.ddlCountry.SelectedIndexChanged += new System.EventHandler(this.ddlCountry_SelectedIndexChanged);
			this.ddlCity.SelectedIndexChanged += new System.EventHandler(this.ddlCity_SelectedIndexChanged);
			this.cmdAddTrainingReq.Click += new System.EventHandler(this.cmdAddTrainingReq_Click);
			this.btnCancelTrain.Click += new System.EventHandler(this.btnCancelTrain_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion

		private void dgTraining_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			lblTrainingID.Text=dgRegTraining.SelectedItem.Cells[0].Text;

			string sql=" SELECT trainingid, PCode, TrainingTitle, TrainingField, TrainingFieldOther, TrainingMonth, TrainingYear, Duration, DurationUnit, InstituteName, Country, City, " +
				" CityOther, TrainingInGeo, isActive " +
				" FROM dbo.cv_Training " +
				" WHERE (trainingid = 123) ";
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.Fill(ds,"tra");
			SetText(ds,txtTrainingTitle,"TrainingTitle");
			SetDDL(ds,ddlTrainingField,"TrainingField");
			SetText(ds,txtOtherTrainingField,"TrainingFieldOther");
			SetDDL(ds,ddlTrainingMonth, "TrainingMonth");
			SetDDL(ds,ddlTrainingYear,"TrainingYear");
			SetDDL(ds,ddlDuration,"Duration");
			SetDDL(ds, ddlDurationUnit, "DurationUnit");
			SetText(ds,txtTrainingInstitute,"InstituteName");
			SetDDL(ds,ddlCountry,"Country");
			SetDDL(ds,ddlCity,"City");
			SetText(ds,txtOtherCity,"CityOther");
			if(ds.Tables[0].Rows[0]["TrainingInGeo"].ToString()=="1")
				CheckBox1.Checked=true;
			else
				CheckBox1.Checked=false;

			pnlTraings.Visible=false;
			pnlTrainingDetails.Visible=true;

		}

		private void SetText(DataSet ds,TextBox tb,string field)
		{
			tb.Text=ds.Tables[0].Rows[0][field].ToString();
		}

		private void SetLabel(DataSet ds,Label lbl,string field)
		{
			lbl.Text=ds.Tables[0].Rows[0][field].ToString();
		}

		private void SetDDL(DataSet ds, DropDownList ddl, string field)
		{
			ddl.SelectedIndex=0;
			for(int j=0;j<ddl.Items.Count;j++)
			{
				if(ddl.Items[j].Value==ds.Tables[0].Rows[0][field].ToString())
				{
					ddl.SelectedIndex=j;
					break;
				}
			}
		}

		private void Linkbutton3_Click(object sender, System.EventArgs e)
		{
			//pnlTraining.Visible=true;
			//Linkbutton3.Visible=false;

			txtTrainingTitle.Text="";
			ddlTrainingField.SelectedIndex=0;
			txtOtherTrainingField.Text="";
			//txtOtherTrainingField.Visible=false;
			//lblOtherTrainingField.Visible=false;
			ddlTrainingMonth.SelectedIndex=0;
			ddlTrainingYear.SelectedIndex=0;
			ddlDuration.SelectedIndex=0;
			ddlDurationUnit.SelectedIndex=0;
			txtTrainingInstitute.Text="";
			ddlCity.SelectedIndex=0;
			ddlCountry.SelectedIndex=0;
			//pnlTraings.Visible=false;
			//pnlTrainingDetails.Visible=true;
			pnlTraings.Visible=false;
			pnlTrainingDetails.Visible=true;

		}

		private void ddlTrainingField_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			//txtOtherTrainingField.Text="";
			if (ddlTrainingField.SelectedValue=="-1")
			{
				//lblOtherTrainingField.Visible=true;
				//txtOtherTrainingField.Visible=true;
			}
			else
			{
				//lblOtherTrainingField.Visible=false;
				//txtOtherTrainingField.Visible=false;

			}
		}

		private void UpdateCity()
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			//lblOtherCity.Visible=false;
			//txtOtherCity.Visible=false;
			if (ddlCountry.SelectedIndex==0)
			{
				ddlCity.Items.Clear();
			}
			else if(ddlCountry.SelectedIndex>0)
			{
				ddlCity.Items.Clear();
				ddlCity.Items.Add("Select -- City");

				SqlDataAdapter da=new SqlDataAdapter("SELECT cityid, cityname FROM cv_City WHERE (countryid = " + ddlCountry.SelectedValue + ") AND (status = 1) Order by cityname",conn);
				DataSet ds=new DataSet();
				da.Fill(ds,"city");
				
				for(int j=0;j<ds.Tables[0].Rows.Count;j++)
				{
					ddlCity.Items.Add(new ListItem(ds.Tables[0].Rows[j][1].ToString(),
						ds.Tables[0].Rows[j][0].ToString()));
				}
				ddlCity.Items.Add(new ListItem("Other","-1"));
			}
			else
			{
				ddlCity.Items.Clear();
				ddlCity.Items.Add("Select -- City");
				ddlCity.Items.Add(new ListItem("Other","-1"));
			}
		}


		private void ddlCountry_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			UpdateCity();	
		}

//		private void SaveTrainingReq()
//		{
//			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
//			conn.Open();
//
//			if(cmdAddTrainingReq.Text=="Add" || cmdAddTrainingReq.Text=="Save")
//			{
//				if(lblTrainingID.Text!="")
//				{
//					if(isTrainingRequested(lblTrainingID.Text))
//					{
//						string ssql="update cv_TrainingReq set AppFlag=4 where trainingid=" + lblTrainingID.Text + " and AppFlag=2 and pcode='"+Session["user_id"].ToString()+"'";
//						SqlCommand cmd2=new SqlCommand(ssql,conn);
//						cmd2.ExecuteNonQuery();
//					}
//				}
//
//				string sql="insert into cv_TrainingReq(trainingid,PCode,TrainingTitle,TrainingField,TrainingFieldOther,TrainingMonth,TrainingYear,Duration,DurationUnit,InstituteName,Country,City,CityOther,TrainingInGeo,c_at,Addflag,wfid,AppFlag)"+
//					" values (@trainingid,@PCode,@TrainingTitle,@TrainingField,@TrainingFieldOther,@TrainingMonth,@TrainingYear,@Duration,@DurationUnit,@InstituteName,@Country,@City,@CityOther,@TrainingInGeo,getdate(),1,@wfid,2)";
//				SqlCommand cmd=new SqlCommand(sql,conn);
//				SqlParameter _trainingid=new SqlParameter("@trainingid",SqlDbType.Int);
//				_trainingid.Value=DBNull.Value;
//				cmd.Parameters.Add(_trainingid);
//				cmd.Parameters.Add(new SqlParameter("@PCode",Session["user_id"].ToString()));
//				//cmd.Parameters.Add(new SqlParameter());
//				cmd.Parameters.Add(new SqlParameter("@TrainingTitle",txtTrainingTitle.Text));
//				cmd.Parameters.Add(new SqlParameter("@TrainingField",ddlTrainingField.SelectedValue));
//				cmd.Parameters.Add(new SqlParameter("@TrainingFieldOther",txtOtherTrainingField.Text.Trim()));
//				cmd.Parameters.Add(new SqlParameter("@TrainingMonth",ddlTrainingMonth.SelectedIndex.ToString()));
//				cmd.Parameters.Add(new SqlParameter("@TrainingYear",ddlTrainingYear.SelectedValue));
//				cmd.Parameters.Add(new SqlParameter("@Duration",ddlDuration.SelectedValue));
//				cmd.Parameters.Add(new SqlParameter("@DurationUnit",ddlDurationUnit.SelectedValue));
//				cmd.Parameters.Add(new SqlParameter("@InstituteName",txtTrainingInstitute.Text.Trim()));
//				cmd.Parameters.Add(new SqlParameter("@Country",ddlCountry.SelectedValue));
//				cmd.Parameters.Add(new SqlParameter("@City",ddlCity.SelectedValue));
//				cmd.Parameters.Add(new SqlParameter("@CityOther",txtOtherCity.Text.Trim()));
//				SqlParameter _TrainingInGeo=new SqlParameter("@TrainingInGeo",SqlDbType.TinyInt);
//
//				if(CheckBox1.Checked)
//					_TrainingInGeo.Value=1;
//				else
//					_TrainingInGeo.Value=0;
//				cmd.Parameters.Add(_TrainingInGeo);
//				cmd.Parameters.Add(new SqlParameter("@wfid",39));
//				cmd.ExecuteNonQuery();
//				UpdateTrainingGrid();
//				conn.Close();
//
//			}
//			else if (cmdAddTrainingReq.Text=="Update")
//			{
//				SqlCommand cmd2=new SqlCommand("update cv_TrainingReq set appflag=4, AppRejDate=getdate(), AppRejBy='"+Session["user_id"].ToString()+"' where trainingid="+lblTrainingID.Text+" and AppFlag=2 and pcode='"+Session["user_id"].ToString()+"'",conn);
//				cmd2.ExecuteNonQuery();
//
//				string sql="insert into cv_TrainingReq(trainingid,PCode,TrainingTitle,TrainingField,TrainingFieldOther,TrainingMonth,TrainingYear,Duration,DurationUnit,InstituteName,Country,City,CityOther,TrainingInGeo,c_at,Addflag,wfid,AppFlag)"+
//					" values (@trainingid,@PCode,@TrainingTitle,@TrainingField,@TrainingFieldOther,@TrainingMonth,@TrainingYear,@Duration,@DurationUnit,@InstituteName,@Country,@City,@CityOther,@TrainingInGeo,getdate(),2,@wfid,2)";
//
//				SqlCommand cmd=new SqlCommand(sql,conn);
//				SqlParameter _trainingid=new SqlParameter("@trainingid",SqlDbType.Int);
//				_trainingid.Value=lblTrainingID.Text;
//				cmd.Parameters.Add(_trainingid);
//				cmd.Parameters.Add(new SqlParameter("@PCode",Session["user_id"].ToString()));
//				//cmd.Parameters.Add(new SqlParameter());
//				cmd.Parameters.Add(new SqlParameter("@TrainingTitle",txtTrainingTitle.Text));
//				cmd.Parameters.Add(new SqlParameter("@TrainingField",ddlTrainingField.SelectedValue));
//				cmd.Parameters.Add(new SqlParameter("@TrainingFieldOther",txtOtherTrainingField.Text.Trim()));
//				cmd.Parameters.Add(new SqlParameter("@TrainingMonth",ddlTrainingMonth.SelectedIndex.ToString()));
//				cmd.Parameters.Add(new SqlParameter("@TrainingYear",ddlTrainingYear.SelectedValue));
//				cmd.Parameters.Add(new SqlParameter("@Duration",ddlDuration.SelectedValue));
//				cmd.Parameters.Add(new SqlParameter("@DurationUnit",ddlDurationUnit.SelectedValue));
//				cmd.Parameters.Add(new SqlParameter("@InstituteName",txtTrainingInstitute.Text.Trim()));
//				cmd.Parameters.Add(new SqlParameter("@Country",ddlCountry.SelectedValue));
//				cmd.Parameters.Add(new SqlParameter("@City",ddlCity.SelectedValue));
//				cmd.Parameters.Add(new SqlParameter("@CityOther",txtOtherCity.Text.Trim()));
//				SqlParameter _TrainingInGeo=new SqlParameter("@TrainingInGeo",SqlDbType.TinyInt);
//
//				if(CheckBox1.Checked)
//					_TrainingInGeo.Value=1;
//				else
//					_TrainingInGeo.Value=0;
//				cmd.Parameters.Add(_TrainingInGeo);
//				cmd.Parameters.Add(new SqlParameter("@wfid",39));
//				cmd.ExecuteNonQuery();
//				UpdateTrainingGrid();
//				conn.Close();
//			}
//			ResetTrainingForm();
//			pnlTraining.Visible=false;
//			Linkbutton3.Visible=true;
//		}
//		


		private string GetTimeline(string wfid)
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			string sql=" SELECT timeline " +
				" FROM dbo.t_fieldsmgt " +
				" WHERE (f_id = "+wfid+") ";
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			return ds.Tables[0].Rows[0][0].ToString();
		}

		private string GetReplyMsg(string type, string wfid)
		{
			string sql="SELECT "+type+" FROM dbo.t_fieldsmgt WHERE (f_id = "+wfid+")";
			SqlConnection conn2=new SqlConnection(Connection.ConnectionString);
			conn2.Open();
			SqlDataAdapter da=new SqlDataAdapter(sql,conn2);
			DataSet ds=new DataSet();
			da.Fill(ds);
			
			conn2.Close();
			conn2.Dispose();
			return ds.Tables[0].Rows[0][type].ToString();
		}

		private void SaveTrainingReq()
		{
			SqlConnection conn=new SqlConnection(Connection.ConnectionString);
			conn.Open();
			SqlTransaction tran=conn.BeginTransaction();

			string replyatrequest=GetReplyMsg("replyatrequest","39");
			string fn="";
			
			if(fileExp.PostedFile.FileName!="")
			{
				fn=Guid.NewGuid().ToString()+".jpg";
			}
			else
			{
				fn="";
			}


			if(cmdAddTrainingReq.Text=="Add" || cmdAddTrainingReq.Text=="Save")
			{
				if(lblTrainingID.Text!="")
				{
					if(isTrainingRequested(lblTrainingID.Text))
					{
						string ssql="update cv_TrainingReq set AppFlag=4 where trainingid=" + lblTrainingID.Text + " and AppFlag=2 and pcode='"+Session["user_id"].ToString()+"'";
						SqlCommand cmd2=new SqlCommand(ssql,conn);
						cmd2.Transaction=tran;
						cmd2.ExecuteNonQuery();
					}
				}

				string sql="insert into cv_TrainingReq(trainingid,PCode,TrainingTitle,TrainingField,TrainingFieldOther,TrainingMonth,TrainingYear,Duration,DurationUnit,InstituteName,Country,City,CityOther,TrainingInGeo,c_at,Addflag,wfid,AppFlag,filename,requesttimeline)"+
					" values (@trainingid,@PCode,@TrainingTitle,@TrainingField,@TrainingFieldOther,@TrainingMonth,@TrainingYear,@Duration,@DurationUnit,@InstituteName,@Country,@City,@CityOther,@TrainingInGeo,getdate(),1,@wfid,2,@filename,@requesttimeline)";
				SqlCommand cmd=new SqlCommand(sql,conn);
				cmd.Transaction=tran;

				SqlParameter _trainingid=new SqlParameter("@trainingid",SqlDbType.Int);
				_trainingid.Value=DBNull.Value;
				cmd.Parameters.Add(_trainingid);
				cmd.Parameters.Add(new SqlParameter("@PCode",Session["user_id"].ToString()));
				//cmd.Parameters.Add(new SqlParameter());
				cmd.Parameters.Add(new SqlParameter("@TrainingTitle",txtTrainingTitle.Text));
				cmd.Parameters.Add(new SqlParameter("@TrainingField",ddlTrainingField.SelectedValue));
				cmd.Parameters.Add(new SqlParameter("@TrainingFieldOther",txtOtherTrainingField.Text.Trim()));
				cmd.Parameters.Add(new SqlParameter("@TrainingMonth",ddlTrainingMonth.SelectedIndex.ToString()));
				cmd.Parameters.Add(new SqlParameter("@TrainingYear",ddlTrainingYear.SelectedValue));
				cmd.Parameters.Add(new SqlParameter("@Duration",ddlDuration.SelectedValue));
				cmd.Parameters.Add(new SqlParameter("@DurationUnit",ddlDurationUnit.SelectedValue));
				cmd.Parameters.Add(new SqlParameter("@InstituteName",txtTrainingInstitute.Text.Trim()));
				cmd.Parameters.Add(new SqlParameter("@Country",ddlCountry.SelectedValue));
				cmd.Parameters.Add(new SqlParameter("@City",ddlCity.SelectedValue));
				cmd.Parameters.Add(new SqlParameter("@CityOther",txtOtherCity.Text.Trim()));
				SqlParameter _TrainingInGeo=new SqlParameter("@TrainingInGeo",SqlDbType.TinyInt);
				SqlParameter _requesttimeline=new SqlParameter("@requesttimeline",SqlDbType.Int);
				cmd.Parameters.Add(new SqlParameter("@FileName",fn));

				_requesttimeline.Value=GetTimeline("39");
				cmd.Parameters.Add(_requesttimeline);


				if(CheckBox1.Checked)
					_TrainingInGeo.Value=1;
				else
					_TrainingInGeo.Value=0;
				cmd.Parameters.Add(_TrainingInGeo);
				cmd.Parameters.Add(new SqlParameter("@wfid",39));
				//SqlTransaction tran=conn.BeginTransaction();
				//cmd.Transaction=tran;
				try
				{
					cmd.ExecuteNonQuery();
					if(fileExp.PostedFile.FileName!="")
						fileExp.PostedFile.SaveAs(Server.MapPath(@"tempEmployee\"+fn));
					Bulletin.PostReqMessage(conn,tran,replyatrequest,"GEO Raabta",Session["user_id"].ToString(),1,"Training","cv_TrainingReq");
					tran.Commit();
				}
				catch(Exception ex)
				{
					Response.Write(ex.Message);
					tran.Rollback();
				}
				tran.Dispose();
				UpdateTrainingGrid();
				conn.Close();
				lblTrainingMsg.Text=GetReplyMsg("replyatrequest","39");

			}
			else if (cmdAddTrainingReq.Text=="Update")
			{
				SqlCommand cmd2=new SqlCommand("update cv_TrainingReq set appflag=4, AppRejDate=getdate(), AppRejBy='"+Session["user_id"].ToString()+"' where trainingid="+lblTrainingID.Text+" and AppFlag=2 and pcode='"+Session["user_id"].ToString()+"'",conn);
				cmd2.Transaction=tran;
				cmd2.ExecuteNonQuery();

				string sql="insert into cv_TrainingReq(trainingid,PCode,TrainingTitle,TrainingField,TrainingFieldOther,TrainingMonth,TrainingYear,Duration,DurationUnit,InstituteName,Country,City,CityOther,TrainingInGeo,c_at,Addflag,wfid,AppFlag,filename,requesttimeline)"+
					" values (@trainingid,@PCode,@TrainingTitle,@TrainingField,@TrainingFieldOther,@TrainingMonth,@TrainingYear,@Duration,@DurationUnit,@InstituteName,@Country,@City,@CityOther,@TrainingInGeo,getdate(),2,@wfid,2,@filename,@requesttimeline)";

				SqlCommand cmd=new SqlCommand(sql,conn);
				cmd.Transaction=tran;

				SqlParameter _trainingid=new SqlParameter("@trainingid",SqlDbType.Int);
				_trainingid.Value=lblTrainingID.Text;
				cmd.Parameters.Add(_trainingid);
				cmd.Parameters.Add(new SqlParameter("@PCode",Session["user_id"].ToString()));
				//cmd.Parameters.Add(new SqlParameter());
				cmd.Parameters.Add(new SqlParameter("@TrainingTitle",txtTrainingTitle.Text));
				cmd.Parameters.Add(new SqlParameter("@TrainingField",ddlTrainingField.SelectedValue));
				cmd.Parameters.Add(new SqlParameter("@TrainingFieldOther",txtOtherTrainingField.Text.Trim()));
				cmd.Parameters.Add(new SqlParameter("@TrainingMonth",ddlTrainingMonth.SelectedIndex.ToString()));
				cmd.Parameters.Add(new SqlParameter("@TrainingYear",ddlTrainingYear.SelectedValue));
				cmd.Parameters.Add(new SqlParameter("@Duration",ddlDuration.SelectedValue));
				cmd.Parameters.Add(new SqlParameter("@DurationUnit",ddlDurationUnit.SelectedValue));
				cmd.Parameters.Add(new SqlParameter("@InstituteName",txtTrainingInstitute.Text.Trim()));
				cmd.Parameters.Add(new SqlParameter("@Country",ddlCountry.SelectedValue));
				cmd.Parameters.Add(new SqlParameter("@City",ddlCity.SelectedValue));
				cmd.Parameters.Add(new SqlParameter("@CityOther",txtOtherCity.Text.Trim()));
				SqlParameter _TrainingInGeo=new SqlParameter("@TrainingInGeo",SqlDbType.TinyInt);
				cmd.Parameters.Add(new SqlParameter("@FileName",fn));

				SqlParameter _requesttimeline=new SqlParameter("@requesttimeline",SqlDbType.Int);
				_requesttimeline.Value=GetTimeline("39");
				cmd.Parameters.Add(_requesttimeline);


				if(CheckBox1.Checked)
					_TrainingInGeo.Value=1;
				else
					_TrainingInGeo.Value=0;
				cmd.Parameters.Add(_TrainingInGeo);
				cmd.Parameters.Add(new SqlParameter("@wfid",39));
				//SqlTransaction tran=conn.BeginTransaction();
				//cmd.Transaction=tran;
				try
				{
					cmd.ExecuteNonQuery();
					if(fileExp.PostedFile.FileName!="")
						fileExp.PostedFile.SaveAs(Server.MapPath(@"tempEmployee\"+fn));
					
					Bulletin.PostReqMessage(conn,tran,replyatrequest,"GEO Raabta",Session["user_id"].ToString(),1,"Training","cv_TrainingReq");
					tran.Commit();
				}
				catch(Exception ex)
				{
					Response.Write(ex.Message);
					tran.Rollback();
				}
				tran.Dispose();
				UpdateTrainingGrid();
				conn.Close();

			}
			ResetTrainingForm();
			pnlTraining.Visible=false;
			Linkbutton3.Visible=true;
		}

		private void ResetTrainingForm()
		{
			txtTrainingTitle.Text="";
			ddlTrainingField.SelectedIndex=0;
			//lblOtherTrainingField.Visible=false;
			//txtOtherTrainingField.Visible=false;
			ddlTrainingMonth.SelectedIndex=0;
			ddlTrainingYear.SelectedIndex=0;
			ddlDuration.SelectedIndex=0;
			ddlDurationUnit.SelectedIndex=0;
			txtTrainingInstitute.Text="";
			ddlCountry.SelectedIndex=0;
			ddlCity.SelectedIndex=0;
			//txtOtherCity.Visible=false;
			//lblOtherCity.Visible=false;
			cmdAddTrainingReq.Text="Add";
			CheckBox1.Checked=false;
			Linkbutton3.Visible=true;
			pnlTraings.Visible=true;
			pnlTrainingDetails.Visible=false;
		}


		private void ddlCity_SelectedIndexChanged(object sender, System.EventArgs e)
		{
			//txtOtherDegree.Text="";
			if (ddlCity.SelectedValue=="-1")
			{
				//lblOtherCity.Visible=true;
				//txtOtherCity.Visible=true;
			}
			else
			{
				//lblOtherCity.Visible=false;
				//txtOtherCity.Visible=false;
			}	
		}

		private void cmdAddTrainingReq_Click(object sender, System.EventArgs e)
		{
			SaveTrainingReq();
			lblTrainingMsg.Visible=true;
			Linkbutton3.Visible=true;
		}

		private void btnCancelTrain_Click(object sender, System.EventArgs e)
		{
			//pnlTrainingDetails.Visible=false;
			//pnlTraings.Visible=true;
			//pnlTraining.Visible=true;
			pnlTraings.Visible=true;
			pnlTrainingDetails.Visible=false;
			Linkbutton3.Visible=true;
		}

		private void dgTraining_SelectedIndexChanged_1(object sender, System.EventArgs e)
		{
		
		}

		private void dgRegTraining_SelectedIndexChanged(object sender, System.EventArgs e)
		{
		
		}

		private void dgTraining2_SelectedIndexChanged(object sender, System.EventArgs e)
		{

			lblTrainingID.Text=dgTraining2.SelectedItem.Cells[0].Text;
			string sql=" SELECT trainingid, PCode, TrainingTitle, TrainingField, TrainingFieldOther, TrainingMonth, TrainingYear, Duration, DurationUnit, InstituteName, Country, City, " +
				" CityOther, TrainingInGeo, isActive " +
				" FROM dbo.cv_Training " +
				" WHERE (trainingid = "+lblTrainingID.Text+") ";
			SqlDataAdapter da=new SqlDataAdapter(sql,conn);
			DataSet ds=new DataSet();
			da.Fill(ds);
			SetText(ds,txtTrainingTitle,"TrainingTitle");
			SetDDL(ds,ddlTrainingField, "TrainingField");
			SetText(ds,txtOtherTrainingField,"TrainingFieldOther");
			SetDDL(ds,ddlDurationUnit,"DurationUnit");
			SetDDL(ds,ddlDuration,"Duration");
			SetDDL(ds,ddlTrainingMonth,"TrainingMonth");
			SetDDL(ds,ddlTrainingYear,"TrainingYear");
			if(ds.Tables[0].Rows[0]["TrainingInGeo"].ToString()=="1")
				CheckBox1.Checked=true;
			else
				CheckBox1.Checked=false;
			SetText(ds,txtTrainingInstitute,"InstituteName");
			SetDDL(ds,ddlCountry,"Country");
			UpdateCity();
			SetDDL(ds,ddlCity,"city");
			SetText(ds,txtOtherCity,"CityOther");
			if(ddlTrainingField.SelectedValue=="-1")
			{
				//lblOtherTrainingField.Visible=true;
				//txtOtherTrainingField.Visible=true;
			}
			else
			{
				//lblOtherTrainingField.Visible=false;
				//txtOtherTrainingField.Visible=false;
			}
			if(ddlCity.SelectedValue=="-1")
			{
				//lblOtherCity.Visible=true;
				//txtOtherCity.Visible=true;
			}
			else
			{
				//lblOtherCity.Visible=false;
				//txtOtherCity.Visible=false;
			}
			pnlTraings.Visible=false;
			pnlTrainingDetails.Visible=true;
			cmdAddTrainingReq.Text="Update";

		
		}

		private void imgEmp_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("UserProfile.aspx?id=1");
		}

		private void imgPersonal_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("UserProfile.aspx?id=2");
		}

		private void imgFamily_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("EmpFamily.aspx?pcode="+this.lblPCode.Text+"&name="+this.lblEmpInfo.Text);
		}

		private void imgEducation_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("EmpEducation.aspx?pcode="+this.lblPCode.Text+"&name="+this.lblEmpInfo.Text);
		}

		private void imgMyGrievance_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("MyGrievance.aspx?pcode="+lblPCode.Text+"&name="+lblEmpInfo.Text);
		}

		private void imgTraining_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("EmpTraining.aspx?pcode="+this.lblPCode.Text+"&name="+this.lblEmpInfo.Text);
		}

		private void ImageButton1_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("UserProfile.aspx?id=1");
		}

		private void ImageButton2_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("UserProfile.aspx?id=2");
		}
		private void ImageButton5_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			//pnlPaySlip.Visible=false;
			//Response.Write("<script language = Javascript >var win=window.showModalDialog('admin/textonimage1.aspx','','dialogHeight:900px;dialogWidth:800px;center:yes')</script"); 
			Response.Write("<script language = Javascript >win=window.open('admin/orgamogram.htm','_blank','resizable=yes,menubar=no,scrollbars=yes,top=0,status=0',true).focus();</script>");

		}

		private void ImageButton3_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("empfamily.aspx");
		}

		private void ImageButton4_Click(object sender, System.Web.UI.ImageClickEventArgs e)
		{
			Response.Redirect("empeducation.aspx");
		}

		

	}
}
