using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Web;
using System.Web.SessionState;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Data.SqlClient;

namespace GeoRabtaSite
{
	/// <summary>
	/// Summary description for EmployeeReport.
	/// </summary>
	public class EmployeeReport : System.Web.UI.Page
	{
		string PCode="";
		protected System.Web.UI.WebControls.Image empPhoto;
		protected System.Web.UI.WebControls.Label lblEmpInfoName2;
		protected System.Web.UI.WebControls.Label lblTpcode;
		protected System.Web.UI.WebControls.Label lblTname;
		SqlConnection con;
		protected System.Web.UI.WebControls.Label lblProbationPriod;
		protected System.Web.UI.WebControls.Label lblConfirmationDate;
		protected System.Web.UI.WebControls.Label lblConfirmationDueDate;
		protected System.Web.UI.WebControls.Label lblFuncDes;
		protected System.Web.UI.WebControls.DataGrid DataGrid1;
		protected System.Web.UI.WebControls.DataGrid DataGrid3;
		protected System.Web.UI.WebControls.Label lblUtility;
		protected System.Web.UI.WebControls.Label lblHouseRent;
		protected System.Web.UI.WebControls.Label lblBasicSal;
		protected System.Web.UI.WebControls.Label lblGrossSal;
		protected System.Web.UI.WebControls.Label lblCarDes;
		protected System.Web.UI.WebControls.Label lblCar;
		protected System.Web.UI.WebControls.Label lblPertrol;
		protected System.Web.UI.WebControls.Label lblMobile;
		//->protected System.Web.UI.HtmlControls.HtmlTable Table2;
		protected System.Web.UI.WebControls.Panel Panel1;
		protected System.Web.UI.WebControls.Label lblLocation;
		protected System.Web.UI.WebControls.Label lblEmpInfoEObi;
		protected System.Web.UI.WebControls.Label lblEmpInfoSessi;
		protected System.Web.UI.WebControls.Label lblEmpInfoExtension;
		protected System.Web.UI.WebControls.Label lblEmpInfoEmail;
		protected System.Web.UI.WebControls.Label lblEmpInfoDOJ;
		protected System.Web.UI.WebControls.Label lblEmpInfoCity;
		protected System.Web.UI.WebControls.Label lblEmpInfoDesignation;
		protected System.Web.UI.WebControls.Label lnlEmpInfoDept;
		protected System.Web.UI.WebControls.Label lblEmpInfoName;
		protected System.Web.UI.WebControls.Label lblEmpInfoCode;
		protected System.Web.UI.WebControls.Panel Panel2;
		protected System.Web.UI.WebControls.Label lblAccountDetails;
		protected System.Web.UI.WebControls.Label lblBankAcctNo;
		protected System.Web.UI.WebControls.Label lblNat;
		protected System.Web.UI.WebControls.Label lblNick;
		protected System.Web.UI.WebControls.Label lblKin;
		protected System.Web.UI.WebControls.Label lblNICOld;
		protected System.Web.UI.WebControls.Label lblNICNew;
		protected System.Web.UI.WebControls.Label lblMobileNo;
		protected System.Web.UI.WebControls.Label lblcontactNo;
		protected System.Web.UI.WebControls.Label lblNTN;
		protected System.Web.UI.WebControls.Label lblEmail;
		protected System.Web.UI.WebControls.RadioButtonList rblMaritialStat;
		protected System.Web.UI.WebControls.Label lblAddress;
		protected System.Web.UI.WebControls.Label lblPassport;
		protected System.Web.UI.WebControls.Label lblReligion;
		protected System.Web.UI.WebControls.Label lblBloodGrp;
		protected System.Web.UI.WebControls.Label lblGender;
		protected System.Web.UI.WebControls.Label lblFName;
		protected System.Web.UI.WebControls.Label lblDOB;
		protected System.Web.UI.WebControls.Panel Panel3;
		protected System.Web.UI.WebControls.Label lblCategory;
		protected System.Web.UI.WebControls.Label lblStatus;
		protected System.Web.UI.WebControls.LinkButton ViewComp;
		protected System.Web.UI.WebControls.TextBox txtX;
		protected System.Web.UI.WebControls.TextBox txtY;
		protected System.Web.UI.WebControls.DataGrid dgRelative;
		protected System.Web.UI.WebControls.Panel pnlRelative;
		protected System.Web.UI.WebControls.Label lblEmpInfoCompany;
		protected System.Web.UI.WebControls.DataGrid dgExp;
		protected System.Web.UI.WebControls.DataGrid dgTraining;
		protected System.Web.UI.WebControls.Label lblNetwork;
		protected System.Web.UI.WebControls.DataGrid dgContractHistory;
		protected System.Web.UI.WebControls.Label lblPositionType;
		protected System.Web.UI.WebControls.Label lblRefProfile;
		protected System.Web.UI.WebControls.RadioButtonList rdoSalType;
		protected System.Web.UI.WebControls.Label lblOther;
		protected System.Web.UI.WebControls.Label lblCurrency;
		string userId="";	
		private bool IsPageAccessAllowed()
		{
				
			try
			{
				userId=Session["user_id"].ToString();
			}
			catch(Exception ex)
			{
				string s=ex.Message;
				Response.Redirect("../notavailable.htm");
			}
		
			if(userId!="")
			{
				if(GeoSecurity.isControlVisible(2,33,userId,"ViewAll")==true ) 
				{
					return true;
				}
				else
				{
					return false;
				}
			}
			else
			{
				Response.Redirect("../notavailable.htm");
				return false;
			}
		}
		public void PersonalInfo()
		{
			con.Open();
			
			//string Id=Request.QueryString["id"].ToString(); 
			string Id=PCode;
			//string getData="select e.pcode,e.name,e.extension,dept.deptname,d.designation,e.dateofbirth,e.email_official,c.cityname,e.telephone,e.mobile,e.nic_new,e.nic_old,e.email_personal,e.dateofjoin,e.fathername from t_employee e,t_department dept,t_designation d,t_city c where d.deptid=dept.deptid And d.desigid=e.desigid And e.station=c.cityid And e.pcode='"+Id+"' And e.desigid=Any(select d2.desigid from t_designation d2,t_department dept2,t_employee e2 where d2.deptid=dept2.deptid And d2.desigid=e2.desigid And e2.pcode='"+Id+"')";
			string getData="select e.pcode,e.dateofbirth,e.gender,e.bloodgroup,e.fathername,e.passportno,e.address,e.maritialstatus,e.email_personal,e.religion,e.telephone,e.mobile,e.nic_new,e.nic_old,e.kin,e.bankaccountno,e.bankaccountdetails,e.ntnno,e.nick from t_employee e where e.pcode='"+Id+"'";
			SqlCommand cmd=new SqlCommand(getData,con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				//lblEmpCode.Text=rd[0].ToString();
				
 
				try
				{
					DateTime d=DateTime.Parse(rd[1].ToString());
					lblDOB.Text=d.ToString("d-MMM,yyyy");
				}
				catch
				{
					lblDOB.Text="";
				}
				
				

				if(rd[2].ToString()!="")
				{
					if(rd[2].ToString()=="1")
					{
					 
						lblGender.Text="Male";
					}
					if(rd[2].ToString()=="2")
					{
						lblGender.Text="Female";
					 
					}
					
				}

				if(rd[3].ToString()!="")
				{
					if(rd[3].ToString()=="0")
					{
						
					}
					if(rd[3].ToString()=="1")
					{
						
						lblBloodGrp.Text="A+";
					}
					if(rd[3].ToString()=="2")
					{
						
						lblBloodGrp.Text="A-";
					}
					if(rd[3].ToString()=="3")
					{
						
						lblBloodGrp.Text="B+";
					}
					if(rd[3].ToString()=="4")
					{
						
						lblBloodGrp.Text="B-";
					}
					if(rd[3].ToString()=="5")
					{
						
						lblBloodGrp.Text="AB+";
					}
					if(rd[3].ToString()=="6")
					{
						
						lblBloodGrp.Text="AB-";
					}
					if(rd[3].ToString()=="7")
					{
						
						lblBloodGrp.Text="O+";
					}
					if(rd[3].ToString()=="8")
					{
						
						lblBloodGrp.Text="O-";
					}
				}
				

				lblFName.Text=rd[4].ToString();
				lblEmail.Text=rd[8].ToString();
				lblPassport.Text=rd[5].ToString();
				lblcontactNo.Text=rd[10].ToString();
				lblMobileNo.Text=rd[11].ToString();
				lblNICNew.Text=rd[12].ToString();
				lblNICOld.Text=rd[13].ToString();
				lblAddress.Text=rd[6].ToString();
				lblNick.Text=rd["nick"].ToString().Trim();
							
				if (rd[7].ToString()!="" && rd[7].ToString()!="0")
				{
					rblMaritialStat.SelectedValue=rd[7].ToString();
				}
				if(rd[9].ToString()!="")
				{
					if(rd[9].ToString()=="0")
					{
					 
					}
					if(rd[9].ToString()=="1")
					{
						lblReligion.Text="Islam";
					}
					if(rd[9].ToString()=="2")
					{
						lblReligion.Text="Christianity";
					}
					if(rd[9].ToString()=="3")
					{
						lblReligion.Text="Buddhism";
					}
					if(rd[9].ToString()=="4")
					{
						lblReligion.Text="Zoroastrian";
					}
					if(rd[9].ToString()=="5")
					{
						lblReligion.Text="Jewish";
					}
					if(rd[9].ToString()=="6")
					{
						lblReligion.Text="Hinduism";
					}
					if(rd[9].ToString()=="7")
					{
						lblReligion.Text="Others";
					}
				}
				lblKin.Text=rd[14].ToString();
				lblBankAcctNo.Text=rd[15].ToString();
				lblAccountDetails.Text=rd[16].ToString();
				lblNTN.Text=rd[17].ToString();

			}
			rd.Close();



			con.Close(); 

		}


		public void EmployeeStatus()
		{

		}

		public void EmployeeInfo()
		{
			con.Open();
			string getEmpInfo="";
			//string empId=Request.QueryString["id"].ToString(); 
			string empId=PCode;
			string filename="";	

//			getEmpInfo="SELECT pcode, name, extension, dateofjoin, insurancecode, dateofconfirmation, typeofemployment, bondpaper, email_official, eobino, sessino, others,pic,desigid,compansatoryoff,operatingcity,probationperiod=case probabtionperiod when -2 then probabtionperiodother else probabtionperiod end,finalconfirmationdate,FunctionalDesignation=case t_employee.pcode when t_employee.pcode then (select f.functionaltitle from t_functionaldesignation f where fdesigid in(select fdesigid from t_designationhistory where pcode=t_employee.pcode and isactive=1))end,category=case t_employee.pcode when t_employee.pcode then (select c.cat_name from t_categorization c,t_designation d where t_employee.desigid=d.desigid and d.category=c.cat_id)end "+
//				"FROM dbo.t_Employee WHERE     (pcode = '"+empId+"')";
			getEmpInfo="SELECT pcode, name, extension, dateofjoin, insurancecode, dateofconfirmation, typeofemployment, bondpaper, email_official, eobino, sessino, others, pic, " +
				" desigid, compansatoryoff, operatingcity, CASE probabtionperiod WHEN - 2 THEN probabtionperiodother ELSE probabtionperiod END AS probationperiod, " +
				" finalconfirmationdate, CASE t_employee.pcode WHEN t_employee.pcode THEN " +
				" (SELECT f.functionaltitle FROM t_functionaldesignation f " +
				" WHERE fdesigid IN (SELECT fdesigid FROM t_designationhistory " +
				" WHERE pcode = t_employee.pcode AND isactive = 1)) END AS FunctionalDesignation, " +
				" CASE t_employee.pcode WHEN t_employee.pcode THEN " +
				" (SELECT c.cat_name " +
				" FROM t_categorization c, t_designation d " +
				" WHERE t_employee.desigid = d .desigid AND d .category = c.cat_id) END AS category, " +
				" CASE istobejoined WHEN 1 THEN 'Expected to join' WHEN 0 THEN (CASE del WHEN 1 THEN 'Active' WHEN 0 THEN 'Exited' WHEN 2 THEN 'Hold' END) END AS Status " +
				",CASE positiontype WHEN 1 THEN 'New' WHEN 2 THEN 'Replacement' ELSE 'Not Defined' END AS [positiontype], refprofile "+
				" FROM dbo.t_Employee " +
				" WHERE (pcode = '"+empId+"') ";

			SqlCommand cmd=new SqlCommand(getEmpInfo,con);
			SqlDataReader rd=cmd.ExecuteReader();
			string desigId="";
			while(rd.Read())
			{
				
				lblEmpInfoCode.Text=rd[0].ToString();
				lblEmpInfoName.Text=rd[1].ToString();  
				lblEmpInfoName2.Text=rd[1].ToString();
				this.lblTpcode.Text=rd[0].ToString();
				this.lblTname.Text=rd[1].ToString();
				this.lblFuncDes.Text=rd["FunctionalDesignation"].ToString();
				lblEmpInfoExtension.Text=rd[2].ToString();

				if(rd[3].ToString().Length>0)
				{
					DateTime DOJ = DateTime.Parse(rd[3].ToString());

					lblEmpInfoDOJ.Text=DOJ.ToString("d-MMM,yyyy");
				}

				if (rd["dateofconfirmation"].ToString().Length>0)
				{
					DateTime DOJ = DateTime.Parse(rd["dateofconfirmation"].ToString());
					lblConfirmationDueDate.Text=DOJ.ToString("d-MMM,yyyy");
				}
				if (rd["finalconfirmationdate"].ToString().Length>0)
				{
					DateTime DOJ = DateTime.Parse(rd["finalconfirmationdate"].ToString());
					lblConfirmationDate.Text=DOJ.ToString("d-MMM,yyyy");
				}
				lblProbationPriod.Text=rd["probationperiod"].ToString();
				//lblEmpInfoInsurance.Text=rd[4].ToString();

				if(rd[6].ToString()!="")
				{
					
					//					if(rd[6].ToString()=="1")
					//					{
					//						lblEmpInfoTOE.Text="Permanent";
					//					}
					//					if(rd[6].ToString()=="2")
					//					{
					//						lblEmpInfoTOE.Text="Contractual";
					//					}
					//					if(rd[6].ToString()=="3")
					//					{
					//						lblEmpInfoTOE.Text="Retainer";
					//					}
					ListItem itm=new ListItem();
					itm.Value=rd[6].ToString();
					//					itm.Text=lblEmpInfoTOE.Text;
					
				}
				lblEmpInfoEmail.Text=rd[8].ToString();
				lblEmpInfoEObi.Text=rd[9].ToString();
				lblEmpInfoSessi.Text=rd[10].ToString().Trim();
				lblLocation.Text=rd["operatingcity"].ToString();
				lblCategory.Text=rd["category"].ToString();
				lblStatus.Text=rd["status"].ToString();
				lblPositionType.Text=rd["positiontype"].ToString();
				lblRefProfile.Text=rd["refprofile"].ToString();
				//lblEmpInfoOthers.Text=rd[11].ToString();
				//lblEmpInfoOthers.Text=rd[11].ToString();

				
				//				if(rd[14].ToString()!="")
				//				{
				//					if(rd[14].ToString()=="1")
				//					{
				//						lblEmpInfoComp.Text="Yes";
				//						//rdCompansatory.Items[0].Selected=true;
				//					}
				//					if(rd[14].ToString()=="2")
				//					{
				//						lblEmpInfoComp.Text="No";
				//						//rdCompansatory.Items[1].Selected=true;
				//					}
				//				}
				
				if (System.IO.File.Exists(Server.MapPath(@"..\employee\"+rd[12].ToString())))
				{
					empPhoto.ImageUrl=@"..\employee\"+rd[12].ToString();
				}
				else
				{
					empPhoto.ImageUrl=@"..\employee\none.jpg";
				}

				filename=rd[12].ToString();
				desigId=rd[13].ToString();
			}
			rd.Close();
			
			
			string getStation2="select c.cityid,c.cityname from t_employee e,t_city c where e.station=c.cityid And e.pcode='"+empId+"' And e.del='"+1+"'";
			SqlCommand ccCity2=new SqlCommand(getStation2,con);
			SqlDataReader rrCity2=ccCity2.ExecuteReader();
			rrCity2.Read();
			if(rrCity2.HasRows)
			{
				lblEmpInfoCity.Text=rrCity2[1].ToString();
			}
			rrCity2.Close();

			string getds="select dept.deptname,d.designation,case dept.deptid when dept.deptid then (select n.Name from t_Network n,t_Sbu s where s.isactive=1 and n.del=1 and n.nId=s.networkID and s.sbuid=dept.sbu) End as Network from t_designation d,t_department dept where dept.status=1 And d.status=1 And d.deptid=dept.deptid and d.desigid='"+desigId+"' ";
			cmd=new SqlCommand(getds,con);
			rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				lnlEmpInfoDept.Text=rd[0].ToString();
				lblEmpInfoDesignation.Text=rd[1].ToString();
				if(rd[2].ToString().Length>0)
				lblNetwork.Text=rd[2].ToString();
			}
            rd.Close();
			/*string getCompany="SELECT dbo.t_Company.c_name FROM  dbo.t_Employee INNER JOIN dbo.t_Company ON dbo.t_Employee.empcompany = dbo.t_Company.c_id "+
            "WHERE (dbo.t_Company.isactive = 1) AND (dbo.t_Employee.pcode = '"+empId+"')";
			cmd=new SqlCommand(getCompany,con);
			rd=cmd.ExecuteReader();
			rd.Read();
			if(rd.HasRows)
			{
			  lblEmpInfoCompany.Text=rd[0].ToString();
			}
			else
			{
			 lblEmpInfoCompany.Text="Not defined";
			}
			rd.Close();*/
			con.Close();
            GetCompensation(empId);
		}

		public void GetCompensation(string Code)
		{
			/*con.Open();
			SqlCommand cmd=new SqlCommand("select grosssalary,basicsalary,houserent,utilities,mobileentitle,petrolentitle,carconveyance=case carconveyance when 1 then 'Company Maintained Car' when 2 then 'Conveyance Allowance' when 3 then 'Leased Car' when 4 then 'Pick & Drop' end,cardescription,others from t_employee where pcode='"+Code+"'",con);
			SqlDataReader rd=cmd.ExecuteReader();
			while(rd.Read())
			{
				if(rd["mobileentitle"].ToString().Length>0)
					this.lblMobile.Text=Convert.ToString(Math.Round(double.Parse(rd["mobileentitle"].ToString())));
			 
				this.lblPertrol.Text=rd["petrolentitle"].ToString();
             
				if(rd["basicsalary"].ToString().Length>0)
					this.lblBasicSal.Text=Convert.ToString(Math.Round(double.Parse(rd["basicsalary"].ToString())));
			 
				if(rd["grosssalary"].ToString().Length>0)
					this.lblGrossSal.Text=Convert.ToString(Math.Round(double.Parse(rd["grosssalary"].ToString())));
			 
				if(rd["houserent"].ToString().Length>0) 
					this.lblHouseRent.Text=Convert.ToString(Math.Round(double.Parse(rd["houserent"].ToString())));
			 
				if(rd["utilities"].ToString().Length>0)
					this.lblUtility.Text=Convert.ToString(Math.Round(double.Parse(rd["utilities"].ToString())));
			 
				this.lblCar.Text=rd["carconveyance"].ToString();
				this.lblCarDes.Text=rd["cardescription"].ToString();
				this.lblOther.Text=rd["others"].ToString();
			}
			rd.Close();
			con.Close();*/
			GeoRabtaSite.admin.Convertor c=new GeoRabtaSite.admin.Convertor();
			string sConvert=c.ConvertCom(Code);
			string []ArrCon=sConvert.Split('^');
			this.lblGrossSal.Text=ArrCon[0];
			this.lblBasicSal.Text=ArrCon[1];
			this.lblHouseRent.Text=ArrCon[2];
			this.lblUtility.Text=ArrCon[3];
			this.lblMobile.Text=ArrCon[4];
			this.lblPertrol.Text=ArrCon[5];
			this.lblCar.Text=ArrCon[6];
			this.lblCarDes.Text=ArrCon[7];
			this.lblOther.Text=ArrCon[8];
			if(ArrCon[9].ToString().Length>0)
			{
				rdoSalType.SelectedValue=ArrCon[9].ToString();
			}
			else
			{
				rdoSalType.SelectedIndex=-1;
			}
			lblCurrency.Text=ArrCon[11].ToString();
		}
		private string GetPCode()
		{
			return PCode;
		}

		public void FamilyInfo()
		{
			con.Open();
			//string getFamily="select name,relationship,maritialstatus,gender,occupation,pcode,fdid from t_familydetails where isactive='1'";
			//string empId=Request.QueryString["id"].ToString();
			string empId=PCode;
			string getFamily=
				"select name,relationship = case relationship when '2' then 'Husband' " +
				"  when '3' then 'Wife' " + 
				"  when '4' then 'Son' " +
				"  when '5' then 'Daughter' " +
				"  When '6' then 'Father' " +
				"  When '7' then 'Mother' " +
				"  END, " +
				" maritialstatus=case maritialstatus when '1' then 'Single' "+
				" when '2' then 'Married' "+
				" when '3' then 'Divorced' "+
				" when '4' then 'Widow' " +
				" when '5' then 'Separated' "+
				" END, "+
				" gender=case gender  WHEN '1' THEN 'Male' "+
				" when '2' then 'Female' "+
				" END ,occupation,pcode,fdid,CONVERT (CHAR(11), dob, 106) as dob,Dependent = case when Dependent=2 then 'No' when Dependent=1 then 'Yes' end "+
				" from t_familydetails where isactive='1' and pcode='"+empId+"' and relationship not in (6,7)";
			SqlDataAdapter rd=new SqlDataAdapter(getFamily,con);
			DataSet dsFamily=new DataSet();
			
			rd.Fill(dsFamily,"FamilyInfo");

			DataRow blankRow ;
			blankRow = dsFamily.Tables["FamilyInfo"].NewRow();
			dsFamily.Tables["FamilyInfo"].Rows.Add(blankRow);

			blankRow = dsFamily.Tables["FamilyInfo"].NewRow();
			dsFamily.Tables["FamilyInfo"].Rows.Add(blankRow);

			blankRow = dsFamily.Tables["FamilyInfo"].NewRow();
			dsFamily.Tables["FamilyInfo"].Rows.Add(blankRow);

			blankRow = dsFamily.Tables["FamilyInfo"].NewRow();
			dsFamily.Tables["FamilyInfo"].Rows.Add(blankRow);

			blankRow = dsFamily.Tables["FamilyInfo"].NewRow();
			dsFamily.Tables["FamilyInfo"].Rows.Add(blankRow);

			

			DataView dv=new DataView(dsFamily.Tables["FamilyInfo"]);
			
			DataGrid1.DataSource=dv;
			DataGrid1.DataBind();
			int getItems=DataGrid1.Items.Count;
			//DataGrid1.Columns[6].Visible=false;
			//DataGrid1.Columns[7].Visible=false;
			con.Close();
			DataGrid1.Visible=true;
			if(GeoSecurity.isControlVisible(2,33,Session["user_id"].ToString(),"View Family Info")==false) 
			{
				DataGrid1.Visible=false;
			}
			
		}

		public void EducationalInfo()
		{
			con.Open();
			//string empId=Request.QueryString["id"].ToString();
			string empId=PCode;
			DataGrid3.Visible=true; 
			//string getInfo="select eduinfoid,degree,institute,yearpassing,result,duration,majors,achievements from t_educationinfo where isactive='1' And pcode='"+empId+"'";
			//			string getInfo = " SELECT dbo.t_educationInfo.pcode, dbo.t_degree.degree, dbo.t_institute.institute_name, CONVERT(CHAR(11), dbo.t_educationInfo.DurationFrom, 106) " +
			//				" AS DurationFrom, CONVERT(CHAR(11), dbo.t_educationInfo.DurationTo, 106) AS DurationTo, dbo.t_educationInfo.result, " +
			//				" dbo.t_educationInfo.achievements, dbo.t_educationInfo.isactive, dbo.t_educationInfo.type, dbo.t_majors.major " +
			//				" FROM dbo.t_educationInfo INNER JOIN " +
			//				" dbo.t_degree ON dbo.t_educationInfo.degree = dbo.t_degree.id INNER JOIN " +
			//				" dbo.t_institute ON dbo.t_educationInfo.institute = dbo.t_institute.id INNER JOIN " +
			//				" dbo.t_majors ON dbo.t_educationInfo.majors = dbo.t_majors.id " +
			//				" WHERE (dbo.t_educationInfo.pcode = '" + PCode + "') AND (dbo.t_educationInfo.isactive=1)";


			string getInfo = "SELECT pcode, DurationFrom, CASE WHEN DurationTo = - 1 THEN 'In Progress' ELSE cast(DurationTo AS VarChar) END AS DurationTo, result, achievements, isactive, " +
				" type, majors as major, institute, CASE WHEN degree = - 1 THEN otherdegree ELSE " +
				" (SELECT eduSub.degree " +
				" FROM t_degree eduSub " +
				" WHERE eduSub.id = eduMain.degree) END AS Degree, CASE WHEN institute = - 1 THEN OtherInstitute ELSE " +
				" (SELECT eduSub2.institute_name " +
				" FROM t_institute eduSub2 " +
				" WHERE eduSub2.id = eduMain.institute) END AS institute_name " +
				" FROM dbo.t_educationInfo eduMain " +
				" WHERE (pcode = '" + PCode + "') AND isactive=1";

			SqlDataAdapter rd=new SqlDataAdapter(getInfo,con);
			DataSet rSet=new DataSet();
			rd.Fill(rSet,"EdutionalInfo");

			DataRow blankRow ;
			blankRow = rSet.Tables["EdutionalInfo"].NewRow();
			rSet.Tables["EdutionalInfo"].Rows.Add(blankRow);

			blankRow = rSet.Tables["EdutionalInfo"].NewRow();
			rSet.Tables["EdutionalInfo"].Rows.Add(blankRow);

			blankRow = rSet.Tables["EdutionalInfo"].NewRow();
			rSet.Tables["EdutionalInfo"].Rows.Add(blankRow);

			blankRow = rSet.Tables["EdutionalInfo"].NewRow();
			rSet.Tables["EdutionalInfo"].Rows.Add(blankRow);


			blankRow = rSet.Tables["EdutionalInfo"].NewRow();
			rSet.Tables["EdutionalInfo"].Rows.Add(blankRow);

			DataView dv=new DataView(rSet.Tables["EdutionalInfo"]);


			DataGrid3.DataSource=dv;
			DataGrid3.DataBind();
			int getItems=DataGrid3.Items.Count;
			//DataGrid3.Columns[8].Visible=false;
			con.Close(); 
			DataGrid3.Visible=true;
			if(GeoSecurity.isControlVisible(2,33,Session["user_id"].ToString(),"View Academic Info")==false) 
			{
				DataGrid3.Visible=false;
			}
			
		}


		private void Nationality()
		{
			//			SqlDataAdapter da = new SqlDataAdapter("SELECT dbo.t_country.Nationality, dbo.t_Employee.pcode " +
			//				" FROM dbo.t_country INNER JOIN " +
			//				" dbo.t_Employee ON dbo.t_country.countryid = dbo.t_Employee.Nationality " +
			//				" WHERE (dbo.t_Employee.pcode = '" + lblEmpInfoCode.Text.Trim() + "') ",con);

			if (con.State==ConnectionState.Closed)
				con.Open();
			
			string sql="SELECT     dbo.t_country.Nationality, dbo.t_Employee.pcode, dbo.t_Employee.name, dbo.t_country.countryid " +
				" FROM dbo.t_country INNER JOIN " +
				" dbo.t_Employee ON dbo.t_country.countryid = dbo.t_Employee.Nationality " +
				" WHERE (dbo.t_Employee.pcode = '" + lblEmpInfoCode.Text.Trim() + "') ";
			SqlDataAdapter da = new SqlDataAdapter(sql,con);
			//Response.Write(sql);
			
			DataSet ds = new DataSet();
			da.Fill(ds,"Nationality");
			if (ds.Tables[0].Rows.Count!=0)
			{
				lblNat.Text=ds.Tables[0].Rows[0][0].ToString();
			}
			else
			{
				lblNat.Text="";
			}
		}

		public bool ValidateAccount(string ID)
		{
			if (con.State==ConnectionState.Closed)
				con.Open();
		 string selectQry="select * from t_password where pcode='"+Session["user_id"].ToString()+"' and (state= 1 or state=0)";
	     SqlCommand cmd=new SqlCommand(selectQry,con);
		 SqlDataReader rd=cmd.ExecuteReader();
		 rd.Read();
			if(rd.HasRows)
			{
				rd.Close();
				con.Close();
				return true;
			}
			else
			{
			 rd.Close();
			 con.Close();
			 return false;
			}
		}
		public int ValidateUser(string ID)
		{
			if (con.State==ConnectionState.Closed)
				con.Open();
			string selectQry="select count(*) as TotalDeptRights from t_bumanagers where pcode='"+ID+"' and isactive=1";
			SqlCommand cmd=new SqlCommand(selectQry,con);
			SqlDataReader rd=cmd.ExecuteReader();
			rd.Read();
			int Val=-1;
			if(rd.HasRows)
			{
				
				Val= Int32.Parse(rd[0].ToString());
				rd.Close();
				return Val;
			}
			else
			{
				Val= Int32.Parse(rd[0].ToString());
				rd.Close();
				return Val;
			}
			
		}
		private void Page_Load(object sender, System.EventArgs e)
		{
			// Put user code to initialize the page here
			//////			Response.Cache.SetCacheability(HttpCacheability.NoCache);
			//////			Response.Cache.SetAllowResponseInBrowserHistory(false);
			//////			if(IsPageAccessAllowed())
			//////			{
			//////				
			//////			}
			//////			else
			//////			{
			//////				Response.Redirect("ErrorPage.aspx");
			//////			}
			//=========================Popup Parent================//
			if(!IsPostBack)
			{
				if(GeoSecurity.isAllAllow("Station",Session["user_id"].ToString()))
				{}
				else
				{
				 con=new SqlConnection(Connection.ConnectionString);
				 con.Open();
				 string Query="SELECT station FROM dbo.t_Employee WHERE (pcode = '"+Request.QueryString[0].ToString().Trim()+"') AND (station IN (SELECT dbo.t_RoleDemographic.rights "+
                 "FROM dbo.t_usersroles INNER JOIN dbo.t_RoleDemographic ON dbo.t_usersroles.roleID = dbo.t_RoleDemographic.roleid INNER JOIN "+
                 "dbo.t_demographic ON dbo.t_RoleDemographic.did = dbo.t_demographic.id "+
                 "WHERE (dbo.t_demographic.dname = 'Station') AND (dbo.t_usersroles.PCode = '"+Session["user_id"].ToString()+"')))";
				 SqlCommand rcm=new SqlCommand(Query,con);
				 SqlDataReader der=rcm.ExecuteReader();
				 der.Read();
					if(der.HasRows)
					{
					 der.Close();
					 con.Close();
					}
					else
					{
					 der.Close();
					 con.Close();
					 Response.Redirect("ErrorPage.aspx");
					}
				}
				if(GeoSecurity.isAllAllow("Department",Session["user_id"].ToString()))
				{}
				else
				{
					con=new SqlConnection(Connection.ConnectionString);
					con.Open();
					string Query="SELECT dbo.t_Department.deptid FROM dbo.t_Department INNER JOIN dbo.t_Designation ON dbo.t_Department.deptid = dbo.t_Designation.deptid INNER JOIN "+
                    "dbo.t_Employee ON dbo.t_Designation.desigid = dbo.t_Employee.desigid WHERE (dbo.t_Employee.pcode = '"+Request.QueryString[0].ToString().Trim()+"') AND (dbo.t_Department.deptid IN "+
                    "(SELECT dbo.t_RoleDemographic.rights FROM dbo.t_usersroles INNER JOIN "+
                    "dbo.t_RoleDemographic ON dbo.t_usersroles.roleID = dbo.t_RoleDemographic.roleid INNER JOIN "+
                    "dbo.t_demographic ON dbo.t_RoleDemographic.did = dbo.t_demographic.id "+
                    "WHERE (dbo.t_demographic.dname = 'Department') AND (dbo.t_usersroles.PCode = '"+Session["user_id"].ToString()+"')))";
					SqlCommand rcm=new SqlCommand(Query,con);
					SqlDataReader der=rcm.ExecuteReader();
					der.Read();
					if(der.HasRows)
					{
						der.Close();
						con.Close();
					}
					else
					{
						der.Close();
						con.Close();
						Response.Redirect("ErrorPage.aspx");
					}
				}
			}
			string scr = @"<script>
            function update(elemValue)
              {
              document.getElementById('txtFirstName').innerText=elemValue[0];
              document.getElementById('txtLastName').innerText=elemValue[1];
               }
             </script>";
			// register the javascript into the Page
			Page.RegisterClientScriptBlock("update", scr);
			//add our popup onclick attribute to the desired element on the page (Here, Hyperlink1)
			ViewComp.Attributes.Add("onclick", "return (window.showModalDialog('popup.aspx','yyy','dialogHeight:200px;dialogWidth:300px;center:yes;status:no;')==true)");

 
			//=====================================================//
			con=new SqlConnection(Connection.ConnectionString);
			if(GeoSecurity.isControlVisible(2,33,Session["user_id"].ToString(),"View Employement Info")==false) 
			{
				Panel1.Visible=false;
			}
			if(GeoSecurity.isControlVisible(2,33,Session["user_id"].ToString(),"View Personal Info")==false) 
			{
				Panel2.Visible=false;
			}
			if(GeoSecurity.isControlVisible(2,33,Session["user_id"].ToString(),"View Family Info")==false) 
			{
				DataGrid1.Visible=false;
			}
			if(GeoSecurity.isControlVisible(2,33,Session["user_id"].ToString(),"View Academic Info")==false) 
			{
				DataGrid3.Visible=false;
			}
			if(GeoSecurity.isControlVisible(2,33,Session["user_id"].ToString(),"View Relative Info")==false) 
			{
				this.pnlRelative.Visible=false;
			}
			if(GeoSecurity.isControlVisible(2,33,Session["user_id"].ToString(),"View Experience Info")==false) 
			{
				this.dgExp.Visible=false;
			}
			if(GeoSecurity.isControlVisible(2,33,Session["user_id"].ToString(),"View Training Info")==false) 
			{
				this.dgTraining.Visible=false;
			}
			if(GeoSecurity.isControlVisible(2,33,Session["user_id"].ToString(),"View Contract")==false) 
			{
				this.dgContractHistory.Visible=false;
			}
			/*if(GeoSecurity.isControlVisible(2,33,Session["user_id"].ToString(),"View Compensation & Benefits Info")==false) 
			{
				this.ViewComp.Visible=false;
				//Panel3.Visible=false;
				//==========================View Compensation Detail to Concerned BU========================//
				/*if(!Panel3.Visible)
				{
					if(Request.QueryString.Count>0)
					{
						con.Open();
						SqlCommand cmd =new SqlCommand("select b.deptid from t_bumanagers b where pcode='"+Session["user_id"].ToString()+"' and b.isactive=1 and b.deptid in (select dept.deptid from t_employee e,t_designation d,t_department dept where e.desigid=d.desigid and dept.deptid=d.deptid and e.pcode='"+Request.QueryString[0].ToString().Trim()+"')",con);
						SqlDataReader de=cmd.ExecuteReader();
						de.Read();
						if(de.HasRows)
						{
							Panel3.Visible=true;
						}
						else
						{
						 Panel3.Visible=false;
						}
						de.Close();
						con.Close();
					}
				}*/
			//============================================================================================//
			//}
			//=====================================Authenticate Compensation Rights========================//
			con.Open();
			if(GeoSecurity.isControlVisible(2,32,Session["user_id"].ToString(),"Compensation")==true)
			{
				//if(GeoSecurity.isControlVisible(2,33,Session["user_id"].ToString(),"View Compensation & Benefits Info")==true && ValidateAccount(Session["user_id"].ToString().Trim()))
				//{
				if(GeoSecurity.GetLoginUserDeptRights(Session["user_id"].ToString(),Request.QueryString[0].ToString().Trim()))
				{
					this.ViewComp.Visible=true;
				}
				else
				{
					this.ViewComp.Visible=false;
				}
				//}
				//else
				//{
				//	this.ViewComp.Visible=false;
				//}
			}
			else
			{
			 this.ViewComp.Visible=false;
			}
			/*else
			{ 
				SqlCommand cmd =new SqlCommand("select b.deptid from t_bumanagers b where pcode='"+Session["user_id"].ToString()+"' and b.isactive=1 and b.deptid in (select dept.deptid from t_employee e,t_designation d,t_department dept where e.desigid=d.desigid and dept.deptid=d.deptid and e.pcode='"+Request.QueryString[0].ToString().Trim()+"')",con);
				SqlDataReader de=cmd.ExecuteReader();
				de.Read();
				if(de.HasRows)
				{
					this.ViewComp.Visible=true;
					de.Close();
					con.Close();
				}
				else
				{
					this.ViewComp.Visible=false;
					de.Close();
					con.Close();
				}
			}*/
			con.Close();
			//=============================================================================================//
			
			try
			{
				PCode=Request.QueryString[0].ToString();
				if(!IsPostBack)
				{
					EmployeeInfo();
					PersonalInfo();
					FamilyInfo();
					EducationalInfo();
					Nationality();
                    RelativeInfo(PCode);
					ExperienceInfo(PCode);
					TrainingInfo(PCode);
					GetContractDetail(PCode);
					foreach(Control c in this.Controls[1].Controls)
					{
						if (c is Label)
						{
							Label l = (Label)c;
							if (l.Text=="" || l.Text=="&nbsp;")
							{
								l.Text=" ";
								l.BackColor=Color.Wheat;
							}
							else
							{
								l.BackColor=Color.White;
							}


						}
					}
				}
			}
			catch (Exception ex)
			{
				string s=ex.Message;
				Response.Write(s);
			}

		}

		#region Web Form Designer generated code
		override protected void OnInit(EventArgs e)
		{
			//
			// CODEGEN: This call is required by the ASP.NET Web Form Designer.
			//
			InitializeComponent();
			base.OnInit(e);
		}
		
		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{    
			this.ViewComp.Click += new System.EventHandler(this.ViewComp_Click);
			this.Load += new System.EventHandler(this.Page_Load);

		}
		#endregion
		public void GetContractDetail(string pcode)
		{
			ContractController c=ContractController.getInstance();
			DataSet ds=c.GetContractDetail(pcode);
			dgContractHistory.DataSource=ds;
			dgContractHistory.DataBind();
			
		}
		public void ExperienceInfo(string pcode)
		{
			SqlDataAdapter dr=new SqlDataAdapter("sp_GetExpInfo",con);
			dr.SelectCommand.CommandType=CommandType.StoredProcedure;
			dr.SelectCommand.Parameters.Add(new SqlParameter("@pcode",pcode));
			DataSet ds=new DataSet();
			dr.Fill(ds,"Relative");
			this.dgExp.DataSource=ds;
			this.dgExp.DataBind();
			this.dgExp.Visible=true;
			if(GeoSecurity.isControlVisible(2,33,Session["user_id"].ToString(),"View Experience Info")==false) 
			{
				this.dgExp.Visible=false;
			}
		}
		public void TrainingInfo(string pcode)
		{
			SqlDataAdapter dr=new SqlDataAdapter("sp_GetTrainingInfo",con);
			dr.SelectCommand.CommandType=CommandType.StoredProcedure;
			dr.SelectCommand.Parameters.Add(new SqlParameter("@pcode",pcode));
			DataSet ds=new DataSet();
			dr.Fill(ds,"Relative");
			this.dgTraining.DataSource=ds;
			this.dgTraining.DataBind();
			this.dgTraining.Visible=true;
			if(GeoSecurity.isControlVisible(2,33,Session["user_id"].ToString(),"View Training Info")==false) 
			{
				this.dgTraining.Visible=false;
			}
		}
		public void RelativeInfo(string pcode)
		{
		 	
			SqlDataAdapter dr=new SqlDataAdapter("select r.relcode,r.name,dept.deptname as department,d.designation,r.relation from t_relation r,t_designation d,t_department dept where r.empcode='"+pcode+"' and r.designation=d.desigid and r.department=dept.deptid and r.isactive=1",con);
			DataSet ds=new DataSet();
			dr.Fill(ds,"Relative");
			//dr=new SqlDataAdapter("select r.empcode as relcode,e.name,dept.deptname as department,d.designation,r.relation from t_relation r,t_designation d,t_department dept,t_employee e where r.relcode='"+pcode+"' and e.desigid=d.desigid and d.deptid=dept.deptid and r.isactive=1 and e.pcode=r.empcode",con);
			//dr=new SqlDataAdapter("select r.relcode as pcode,r.name,dept.deptname,dept.deptid,d.designation,d.desigid,r.relation,relationdeclared=case r.reldeclared when 1 then 'Yes' when 0 then 'No' end,r.rid from t_relation r,t_department dept,t_designation d where dept.deptid=r.department and d.desigid=r.designation and r.isactive=1 and r.empcode='"+pcode+"'",con);
			//dr.Fill(ds,"Relative");
			this.dgRelative.DataSource=ds;
			this.dgRelative.DataBind();
			this.pnlRelative.Visible=true;
			this.dgRelative.Visible=true;
			if(GeoSecurity.isControlVisible(2,33,Session["user_id"].ToString(),"View Relative Info")==false) 
			{
				this.pnlRelative.Visible=false;
			}
			
		}
		private void ViewComp_Click(object sender, System.EventArgs e)
		{
			this.Panel3.Visible=true;
			rdoSalType.Visible=true;
			this.ViewComp.Visible=false;
			con.Open();
			SqlCommand cmd=new SqlCommand("insert into t_userlog(userid,action,description,accessdate) values(@userid,@action,@description,@accessdate)",con);
			SqlParameter _userid=new SqlParameter("@userid",SqlDbType.Char);
			SqlParameter _action=new SqlParameter("@action",SqlDbType.VarChar);
			SqlParameter _description=new SqlParameter("@description",SqlDbType.VarChar);
			SqlParameter _accessdate=new SqlParameter("@accessdate",SqlDbType.DateTime);
			_userid.Value=Session["user_id"].ToString().Trim();
			_action.Value="View Compensation";
			_description.Value="Viewed Profile Details:Pcode="+this.lblEmpInfoCode.Text.Trim()+",Name="+this.lblEmpInfoName.Text;
			_accessdate.Value=DateTime.Now;
			cmd.Parameters.Add(_userid);
			cmd.Parameters.Add(_action);
			cmd.Parameters.Add(_description);
			cmd.Parameters.Add(_accessdate);
			cmd.ExecuteNonQuery();
			con.Close();
			con.Dispose();
		}
	}
}