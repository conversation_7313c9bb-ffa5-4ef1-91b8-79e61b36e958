<%@ Register TagPrefix="ajax" Namespace="MagicAjax.UI.Controls" Assembly="MagicAjax" %>
<%@ Page language="c#" Codebehind="GeoTasaweer.aspx.cs" AutoEventWireup="false" Inherits="GeoRabtaSite.admin.GeoTasaweer" smartNavigation="True"%>
<%@ Register TagPrefix="uc1" TagName="myMenus" Src="myMenus.ascx" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Raabta Admin :: Geo <PERSON>er</title>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="RaabtaAdmin.css" type="text/css" rel="stylesheet">
		<LINK href="../Styles4.css" type="text/css" rel="stylesheet">
	</HEAD>
	<body dir="ltr" bottomMargin="0" bgProperties="fixed" leftMargin="0" topMargin="0" rightMargin="0">
		<form id="myForm" name="MyForm" runat="server">
			<table height="100%" cellSpacing="0" cellPadding="0" width="780" align="center" bgColor="#ffffff"
				border="0">
				<tr>
					<td vAlign="middle" align="left" height="69">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="69" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="1826">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top1.swf">
							<PARAM NAME="Src" VALUE="flash/Top1.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top1.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="69"> </embed>
						</OBJECT>
					</td>
				</tr>
				<tr>
					<td height="10">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="10" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="265">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top2.swf">
							<PARAM NAME="Src" VALUE="flash/Top2.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top2.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="10"> </embed>
						</OBJECT>
					</td>
				</tr>
				<TR>
					<TD class="PageTitle" height="20">Geo Raabta Admin :: Geo Tasaweer</TD>
				</TR>
				<tr>
					<td background="../images/menu-off-bg.gif" height="20"><uc1:mymenus id="MyMenus1" runat="server"></uc1:mymenus>
						<asp:Label id="albumSort" runat="server" Visible="False"></asp:Label>
						<asp:Label id="photoSort" runat="server" Visible="False"></asp:Label></td>
				</tr>
				<tr>
					<td class="MainBG" vAlign="top">
						<!--<ajax:AjaxPanel id="AjaxPanel1" runat="server" Width="100%">--><BR>
						<TABLE class="MainFormColor" id="pnlAlbum" cellSpacing="0" cellPadding="3" width="750"
							align="center" border="0" runat="server">
							<TR>
								<TD class="OrangeFormTitle" width="320" colSpan="3">Photo Albums
									<asp:label id="lblAlbumID" runat="server" Visible="False"></asp:label></TD>
							</TR>
							<TR>
								<TD colSpan="3">
									<asp:datagrid id="dgAlbums" runat="server" Width="100%" AllowSorting="True" BorderStyle="Solid"
										AutoGenerateColumns="False" BorderColor="Navy" BorderWidth="1px" CellPadding="3" GridLines="Horizontal">
										<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
										<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
										<AlternatingItemStyle HorizontalAlign="Left" CssClass="alternetitem" VerticalAlign="Bottom"></AlternatingItemStyle>
										<ItemStyle HorizontalAlign="Left" ForeColor="Navy" CssClass="item" VerticalAlign="Bottom"></ItemStyle>
										<HeaderStyle Font-Bold="True" ForeColor="Black" CssClass="header"></HeaderStyle>
										<Columns>
											<asp:ButtonColumn DataTextField="Album Name" SortExpression="Album Name" HeaderText="Album Name" CommandName="Select"></asp:ButtonColumn>
											<asp:BoundColumn DataField="Employee Name" SortExpression="Employee Name" HeaderText="Created By"></asp:BoundColumn>
											<asp:BoundColumn DataField="Designation" SortExpression="Designation" HeaderText="Designation"></asp:BoundColumn>
											<asp:BoundColumn DataField="Department Name" SortExpression="Department Name" HeaderText="Department"></asp:BoundColumn>
											<asp:BoundColumn Visible="False" DataField="albumid" HeaderText="albumid"></asp:BoundColumn>
											<asp:BoundColumn Visible="False" DataField="pcode" HeaderText="pcode"></asp:BoundColumn>
										</Columns>
										<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
									</asp:datagrid></TD>
							</TR>
						</TABLE>
						<TABLE class="MainFormColor" id="pnlPhotos" cellSpacing="0" cellPadding="3" width="750"
							align="center" border="0" runat="server">
							<TR>
								<TD class="OrangeFormTitle" colSpan="3">Album:
									<asp:label id="lblAlbumName" runat="server"></asp:label></TD>
							</TR>
							<TR>
								<TD class="MenuBar" width="320"><STRONG>Created By:</STRONG></TD>
								<TD class="MenuBar" width="90"><STRONG></STRONG></TD>
								<TD class="MenuBar" width="320"><STRONG>Designation:</STRONG></TD>
							</TR>
							<TR>
								<TD width="320">
									<asp:label id="lblCreatedBy" runat="server"></asp:label></TD>
								<TD width="90"></TD>
								<TD width="320">
									<asp:label id="lblDesignation" runat="server"></asp:label></TD>
							</TR>
							<TR>
								<TD class="MenuBar" width="320"><STRONG>Department:</STRONG></TD>
								<TD class="MenuBar" width="90"><STRONG></STRONG></TD>
								<TD class="MenuBar" width="320"><STRONG></STRONG></TD>
							</TR>
							<TR>
								<TD width="320">
									<asp:label id="lblDepartment" runat="server"></asp:label></TD>
								<TD width="90"></TD>
								<TD width="320">
									<asp:Label id="lblPCode" runat="server" Visible="False"></asp:Label></TD>
							</TR>
							<TR>
								<TD class="OrangeTabtleTile " colSpan="3">Photos</TD>
							</TR>
							<TR>
								<TD colSpan="3">
									<asp:datagrid id="DataGrid1" runat="server" Width="100%" AllowSorting="True" BorderStyle="Solid"
										AutoGenerateColumns="False" BorderColor="Navy" BorderWidth="1px" CellPadding="3" GridLines="Horizontal"
										HorizontalAlign="Left">
										<FooterStyle ForeColor="#4A3C8C" BackColor="#B5C7DE"></FooterStyle>
										<SelectedItemStyle Font-Bold="True" ForeColor="#F7F7F7" BackColor="#738A9C"></SelectedItemStyle>
										<AlternatingItemStyle HorizontalAlign="Left" CssClass="alternetitem" VerticalAlign="Bottom"></AlternatingItemStyle>
										<ItemStyle HorizontalAlign="Left" ForeColor="Navy" CssClass="item" VerticalAlign="Bottom"></ItemStyle>
										<HeaderStyle Font-Bold="True" ForeColor="Black" CssClass="header"></HeaderStyle>
										<Columns>
											<asp:BoundColumn Visible="False" DataField="picid" HeaderText="picid"></asp:BoundColumn>
											<asp:BoundColumn DataField="Photo" HeaderText="Photo">
												<ItemStyle Width="90px"></ItemStyle>
											</asp:BoundColumn>
											<asp:BoundColumn DataField="Title" SortExpression="Title" HeaderText="Title"></asp:BoundColumn>
											<asp:BoundColumn DataField="picturedate" SortExpression="picturedate" HeaderText="Picture Date" DataFormatString="{0:d}">
												<ItemStyle Width="70px"></ItemStyle>
											</asp:BoundColumn>
											<asp:TemplateColumn HeaderText="Accept">
												<ItemStyle HorizontalAlign="Center" Width="30px" VerticalAlign="Middle"></ItemStyle>
												<ItemTemplate>
													<asp:CheckBox id="chkFlag" runat="server" OnCheckedChanged="CheckAccept" AutoPostBack="True"></asp:CheckBox>
												</ItemTemplate>
											</asp:TemplateColumn>
											<asp:TemplateColumn HeaderText="Reject">
												<ItemStyle HorizontalAlign="Center" Width="30px" VerticalAlign="Middle"></ItemStyle>
												<ItemTemplate>
													<asp:CheckBox id="cbReject" runat="server" OnCheckedChanged="CheckReject" AutoPostBack="True"></asp:CheckBox>
												</ItemTemplate>
											</asp:TemplateColumn>
											<asp:TemplateColumn HeaderText="Reason">
												<ItemStyle Width="150"></ItemStyle>
												<ItemTemplate>
													<asp:TextBox id="txtRemarks" runat="server" Visible="False" Width="100%" Rows="5" TextMode="MultiLine"
														CssClass="TextBox"></asp:TextBox>
													<asp:RequiredFieldValidator id="RequiredFieldValidator1" runat="server" Display="Dynamic" ControlToValidate="txtRemarks"
														ErrorMessage="* Reason is required"></asp:RequiredFieldValidator>
												</ItemTemplate>
											</asp:TemplateColumn>
										</Columns>
										<PagerStyle HorizontalAlign="Right" ForeColor="#4A3C8C" BackColor="#E7E7FF" Mode="NumericPages"></PagerStyle>
									</asp:datagrid></TD>
							</TR>
							<TR>
								<TD colSpan="3">
									<asp:button id="cmdUpdate" runat="server" Width="72px" Text="Update"></asp:button>
									<asp:DropDownList id="ddlDepartment" runat="server" Visible="False"></asp:DropDownList></TD>
							</TR>
							<TR>
								<TD colSpan="3">
									<asp:linkbutton id="cmdBack" runat="server" CausesValidation="False">Back to album</asp:linkbutton></TD>
							</TR>
						</TABLE>
						<!--</ajax:AjaxPanel>-->
					</td>
				</tr>
				<tr>
					<td vAlign="middle" align="center" height="20">Copyright © 2005 Independent Media 
						Corporation <A href="http://www.geo.tv">www.geo.tv</A></td>
				</tr>
			</table>
		</form>
	</body>
</HTML>
