<%@ Register TagPrefix="uc1" TagName="myMenus" Src="myMenus.ascx" %>
<%@ Page CodeBehind="PulseQueuelist.aspx.cs" Language="c#" AutoEventWireup="false" Inherits="GeoRabtaSite.PulseQueueList" %>
<%@ Register TagPrefix="ew" Namespace="eWorld.UI" Assembly="eWorld.UI" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Raabta Admin :: Pulse QueueList</title>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<LINK href="RaabtaAdmin.css" type="text/css" rel="stylesheet">
		<LINK href="../Styles4.css" type="text/css" rel="stylesheet">
		<style type="text/css">.style1 { FONT-WEIGHT: bold; FONT-SIZE: 8px }
	.style2 { FONT-WEIGHT: bold; FONT-SIZE: 9pt }
		</style>
		<script language="javascript">
		function ValidatePage()
		{
		if(document.getElementById('ddActionCode').selectedIndex==0)
		{
		 if(document.getElementById('txtBlog').value=='')
		 {
		  alert('Please enter your comments');
		  return false;
		 }
		}
		else if(document.getElementById('r2')!=null)
		 {
		 var aControl=document.getElementById('chkReportingList');
		 var arrayOfCheckBoxes= aControl.getElementsByTagName("input");
		 var count=0;
		 for(var a=0;a<arrayOfCheckBoxes.length;a++)
		  {
		    if(arrayOfCheckBoxes[a].checked)
		    count++;
		  }
		   if(count==0)
		   {
		   window.alert('Please Select Team/Line To Send Pulse'); 
		   return false;
		   }
		 }
		 else
		 {
		  return true;
		 }
		}
		</script>
		<script language="javascript" id="clientEventHandlersJS">
<!--

function window_onscroll() {
document.getElementById("txtX").value=document.body.scrollTop;
document.getElementById("txtY").value=document.body.scrollLeft;
}

function window_onload() {
document.body.scrollTop=document.getElementById("txtX").value;
document.body.scrollLeft=document.getElementById("txtY").value;
}

//-->
		</script>
	</HEAD>
	<body language="javascript" dir="ltr" onscroll="return window_onscroll()" bottomMargin="0"
		bgProperties="fixed" leftMargin="0" topMargin="0" onload="return window_onload()"
		rightMargin="0">
		<form id="ideas" runat="server">
			<table height="100%" cellSpacing="0" cellPadding="0" width="780" align="center" bgColor="#ffffff"
				border="0">
				<tr>
					<td vAlign="middle" align="left" height="69">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="69" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="1826">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top1.swf">
							<PARAM NAME="Src" VALUE="flash/Top1.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top1.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="69"> </embed>
						</OBJECT>
					</td>
				</tr>
				<tr>
					<td height="10">
						<OBJECT codeBase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,19,0"
							height="10" width="780" classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">
							<PARAM NAME="_cx" VALUE="20638">
							<PARAM NAME="_cy" VALUE="265">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="flash/Top2.swf">
							<PARAM NAME="Src" VALUE="flash/Top2.swf">
							<PARAM NAME="WMode" VALUE="Window">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="">
							<PARAM NAME="Scale" VALUE="ShowAll">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="flash/Top2.swf" quality="high" pluginspage="http://www.macromedia.com/go/getflashplayer"
								type="application/x-shockwave-flash" width="780" height="10"> </embed>
						</OBJECT>
					</td>
				</tr>
				<tr>
					<td class="PageTitle" height="20">Geo Raabta Admin :: Pulse QueueList</td>
				</tr>
				<TR>
					<TD class="MenuBar" height="20"><uc1:mymenus id="MyMenus1" runat="server"></uc1:mymenus></TD>
				</TR>
				<tr>
					<td class="MainBG" vAlign="top" align="left"><BR>
						<table class="MainFormColor" id="tabForm" cellSpacing="0" cellPadding="3" width="750" align="center"
							border="0" runat="server">
							<tr>
								<td class="OrangeFormTitle" colSpan="3">My Queue List::
									<asp:Label id="Label2" runat="server"></asp:Label>
									Pulse</td>
							</tr>
							<TR>
								<TD colSpan="3"><STRONG><asp:imagebutton id="ImageButton1" runat="server" ImageUrl="..\images\CurrentPulse.gif"></asp:imagebutton><asp:imagebutton id="ImageButton2" runat="server" ImageUrl="..\images\rfcpulse.gif"></asp:imagebutton>
										<asp:ImageButton id="imgNew" runat="server" ImageUrl="..\images\NewPulse.gif"></asp:ImageButton>
										<asp:ImageButton id="imgBack" runat="server" ImageUrl="..\images\back.gif"></asp:ImageButton></STRONG></TD>
							</TR>
							<TR>
								<TD id="rmain1" style="HEIGHT: 118px" width="100%" colSpan="3" runat="server"><asp:panel id="pnlMain1" runat="server">
										<asp:DataGrid id="dgLivePulse" runat="server" Width="100%" AutoGenerateColumns="False">
											<Columns>
												<asp:ButtonColumn Text="View" CommandName="Select"></asp:ButtonColumn>
												<asp:BoundColumn DataField="Pulse ID" HeaderText="Pulse ID"></asp:BoundColumn>
												<asp:BoundColumn DataField="pic" HeaderText="Initiator Picture"></asp:BoundColumn>
												<asp:BoundColumn DataField="pulse initiate by" HeaderText="Initiator Name"></asp:BoundColumn>
												<asp:BoundColumn DataField="title" HeaderText="Title"></asp:BoundColumn>
												<asp:BoundColumn DataField="pulsetype" HeaderText="Pulse Type"></asp:BoundColumn>
												<asp:BoundColumn Visible="False" DataField="description"></asp:BoundColumn>
												<asp:BoundColumn DataField="pulsecreateon" HeaderText="Create Date" DataFormatString="{0:dd MMM,yyyy h:mm tt}"></asp:BoundColumn>
												<asp:BoundColumn DataField="current status" HeaderText="Current Status"></asp:BoundColumn>
												<asp:BoundColumn DataField="tasksheet" HeaderText="Is Add To Issue &amp; Task Sheet?"></asp:BoundColumn>
												<asp:BoundColumn DataField="Last Update by" HeaderText="Last Updated by"></asp:BoundColumn>
												<asp:BoundColumn Visible="False" DataField="pcode"></asp:BoundColumn>
												<asp:BoundColumn Visible="False" DataField="ides"></asp:BoundColumn>
												<asp:BoundColumn Visible="False" DataField="idept"></asp:BoundColumn>
												<asp:BoundColumn Visible="False" DataField="pdept"></asp:BoundColumn>
												<asp:BoundColumn Visible="False" DataField="pcity"></asp:BoundColumn>
											</Columns>
										</asp:DataGrid>
									</asp:panel></TD>
							</TR>
							<TR>
								<TD id="rmain" style="HEIGHT: 118px" width="100%" colSpan="3" runat="server"><asp:panel id="pnlMain2" runat="server" Visible="False">
										<asp:DataGrid id="dgDeferred" runat="server" Width="737px" AutoGenerateColumns="False" Visible="False">
											<Columns>
												<asp:ButtonColumn Text="View" CommandName="Select"></asp:ButtonColumn>
												<asp:BoundColumn DataField="Pulse ID" HeaderText="Pulse ID"></asp:BoundColumn>
												<asp:BoundColumn DataField="pic" HeaderText="Initiator Picture"></asp:BoundColumn>
												<asp:BoundColumn DataField="pulse initiate by" HeaderText="Initiator Name"></asp:BoundColumn>
												<asp:BoundColumn DataField="title" HeaderText="Title"></asp:BoundColumn>
												<asp:BoundColumn DataField="pulsetype" HeaderText="Pulse Type"></asp:BoundColumn>
												<asp:BoundColumn Visible="False" DataField="description"></asp:BoundColumn>
												<asp:BoundColumn DataField="pulsecreateon" HeaderText="Create Date" DataFormatString="{0:dd MMM,yyyy h:mm tt}"></asp:BoundColumn>
												<asp:BoundColumn DataField="current status" HeaderText="Current Status"></asp:BoundColumn>
												<asp:BoundColumn DataField="tasksheet" HeaderText="Is Add To Issue &amp; Task Sheet?"></asp:BoundColumn>
												<asp:BoundColumn DataField="blog" HeaderText="Total Blogs"></asp:BoundColumn>
												<asp:BoundColumn Visible="False" DataField="pcode"></asp:BoundColumn>
												<asp:BoundColumn Visible="False" DataField="ides"></asp:BoundColumn>
												<asp:BoundColumn Visible="False" DataField="idept"></asp:BoundColumn>
												<asp:BoundColumn Visible="False" DataField="pdept"></asp:BoundColumn>
												<asp:BoundColumn Visible="False" DataField="pcity"></asp:BoundColumn>
												<asp:TemplateColumn HeaderText="Log">
													<ItemTemplate>
														<asp:Button id="btnSel" runat="server" Text="+" onclick="getMe"></asp:Button>
													</ItemTemplate>
												</asp:TemplateColumn>
												<asp:TemplateColumn>
													<ItemTemplate>
														<asp:DataGrid id="dgStatus" runat="server" AutoGenerateColumns="False" Visible="False" BorderColor="Fuchsia">
															<Columns>
																<asp:BoundColumn DataField="status" HeaderText="Status"></asp:BoundColumn>
																<asp:BoundColumn DataField="name" HeaderText="Change by"></asp:BoundColumn>
																<asp:BoundColumn DataField="schangeddate" HeaderText="Date"></asp:BoundColumn>
															</Columns>
														</asp:DataGrid>
													</ItemTemplate>
												</asp:TemplateColumn>
											</Columns>
										</asp:DataGrid>
									</asp:panel></TD>
							</TR>
							<TR>
								<TD width="100%" colSpan="3"></TD>
							</TR>
							<TR>
								<TD width="100%" colSpan="3"><asp:panel id="pnlSubMain1" runat="server" Visible="False">
										<TABLE id="Table2" cellSpacing="0" cellPadding="1" width="100%" border="0">
											<TR>
												<TD class="field" style="HEIGHT: 26px" width="340"><STRONG>Pulse ID:<BR>
													</STRONG>
													<asp:Label id="lblPulseID" runat="server"></asp:Label></TD>
												<TD style="HEIGHT: 26px" width="50">&nbsp;</TD>
												<TD style="HEIGHT: 26px" width="340"><STRONG>&nbsp;</STRONG></TD>
											</TR>
											<TR>
												<TD class="field" width="340"><STRONG>Create Date:</STRONG><BR>
													<asp:Label id="lblCreateon" runat="server"></asp:Label></TD>
												<TD>&nbsp;</TD>
												<TD class="field" width="340"><STRONG>Type:</STRONG><BR>
													<asp:Label id="lblType" runat="server"></asp:Label></TD>
											</TR>
											<TR>
												<TD class="field" colSpan="3"><STRONG>Title:</STRONG><BR>
													<asp:Label id="lblTitle" runat="server"></asp:Label></TD>
											</TR>
											<TR>
												<TD class="field"><STRONG>Initiator PCode:</STRONG><BR>
													<asp:Label id="lblPcode" runat="server"></asp:Label></TD>
												<TD>&nbsp;</TD>
												<TD class="field"><STRONG>Initiator Name:<BR>
													</STRONG>
													<asp:Label id="lblName" runat="server"></asp:Label></TD>
											</TR>
											<TR>
												<TD class="field"><STRONG>Initiator Department:</STRONG><BR>
													<asp:Label id="lblDepartment" runat="server"></asp:Label></TD>
												<TD>&nbsp;</TD>
												<TD class="field"><STRONG>Initiator Designation:</STRONG><BR>
													<asp:Label id="lblDesignation" runat="server"></asp:Label></TD>
											</TR>
											<TR>
												<TD class="field" style="HEIGHT: 27px"><STRONG>Department(for which pulse is 
														generated):</STRONG><BR>
													<asp:Label id="lblpDepartment" runat="server"></asp:Label></TD>
												<TD style="HEIGHT: 27px">&nbsp;</TD>
												<TD class="field" style="HEIGHT: 27px"><STRONG>Station(for which pulse is generated):</STRONG><BR>
													<asp:Label id="lblpStation" runat="server"></asp:Label></TD>
											</TR>
											<TR>
												<TD class="field" id="rdes" colSpan="3" runat="server"><STRONG>Description:</STRONG><BR>
													<asp:Label id="lblDescription" runat="server"></asp:Label></TD>
											</TR>
										</TABLE>
									</asp:panel></TD>
							</TR>
							<TR>
								<TD width="100%" colSpan="3"><asp:panel id="pnlSubMain2" runat="server" Visible="False"><FONT color="#000099"><FONT size="4"><FONT color="#6633cc"><STRONG><U>Pulse 
															Progress Blog:: Pulse ID:
															<asp:Label id="lblhPulseID" runat="server"></asp:Label></U></STRONG></FONT><BR>
											</FONT></FONT>
										<asp:DataGrid id="dgPulseBlog" runat="server" Width="100%" AutoGenerateColumns="False" Visible="False"
											CellPadding="3" BorderWidth="1px" GridLines="Vertical" BorderStyle="None">
											<FooterStyle ForeColor="Black" BackColor="#CCCCCC"></FooterStyle>
											<SelectedItemStyle Font-Bold="True" ForeColor="White" BackColor="#008A8C"></SelectedItemStyle>
											<AlternatingItemStyle CssClass="baitem"></AlternatingItemStyle>
											<ItemStyle CssClass="bitem"></ItemStyle>
											<HeaderStyle Font-Bold="True" ForeColor="White" BackColor="#000084"></HeaderStyle>
											<Columns>
												<asp:BoundColumn Visible="False" HeaderText="Sender Picture"></asp:BoundColumn>
												<asp:BoundColumn DataField="send by" HeaderText="Sender Name"></asp:BoundColumn>
												<asp:BoundColumn DataField="progressblog" HeaderText="Blog"></asp:BoundColumn>
												<asp:BoundColumn DataField="pdate" HeaderText="Date"></asp:BoundColumn>
												<asp:BoundColumn DataField="Action Code" HeaderText="Action Code"></asp:BoundColumn>
												<asp:TemplateColumn Visible="False">
													<ItemTemplate>
														<asp:Button id="btnSelect" onclick="getRecords" runat="server" Text="+"></asp:Button>
													</ItemTemplate>
												</asp:TemplateColumn>
												<asp:TemplateColumn Visible="False">
													<ItemTemplate>
														<asp:DataGrid id="dgRecord1" runat="server" AutoGenerateColumns="False" Width="100%" Visible="False"
															BorderColor="Fuchsia">
															<HeaderStyle Font-Bold="True"></HeaderStyle>
															<Columns>
																<asp:BoundColumn DataField="name"></asp:BoundColumn>
															</Columns>
														</asp:DataGrid>
													</ItemTemplate>
												</asp:TemplateColumn>
											</Columns>
											<PagerStyle HorizontalAlign="Center" ForeColor="Black" BackColor="#999999" Mode="NumericPages"></PagerStyle>
										</asp:DataGrid>
									</asp:panel></TD>
							</TR>
							<TR>
								<TD colSpan="3"><asp:panel id="pnlSubMain3" runat="server" Visible="False"><FONT color="#3333cc"><FONT color="#6633cc" size="4"><STRONG>New 
													Blog
													<asp:Image id="Image1" runat="server" ImageUrl="..\images\reply.gif"></asp:Image></STRONG></FONT><BR>
										</FONT>
										<TABLE id="Table3" cellSpacing="1" cellPadding="1" width="100%" border="1">
											<TR>
												<TD><STRONG>Comments:</STRONG><BR>
													<asp:TextBox id="txtBlog" runat="server" Width="100%" TextMode="MultiLine" CssClass="textbox"
														Height="158px"></asp:TextBox></TD>
											</TR>
											<TR>
												<TD><STRONG>Action Code:<BR>
														<asp:DropDownList id="ddActionCode" runat="server" Width="560px" CssClass="textbox" AutoPostBack="True">
															<asp:ListItem Value="0">Select--Action Code</asp:ListItem>
														</asp:DropDownList></STRONG></TD>
											</TR>
											<TR>
												<TD id="r1" runat="server"><STRONG>Interim Closed Date:<BR>
														<ew:CalendarPopup id="calInterimDate" runat="server">
															<WeekdayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="White"></WeekdayStyle>
															<MonthHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Yellow"></MonthHeaderStyle>
															<OffMonthStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Gray"
																BackColor="AntiqueWhite"></OffMonthStyle>
															<GoToTodayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="White"></GoToTodayStyle>
															<TodayDayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="LightGoldenrodYellow"></TodayDayStyle>
															<DayHeaderStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Orange"></DayHeaderStyle>
															<WeekendStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="LightGray"></WeekendStyle>
															<SelectedDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="Yellow"></SelectedDateStyle>
															<ClearDateStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="White"></ClearDateStyle>
															<HolidayStyle Font-Size="XX-Small" Font-Names="Verdana,Helvetica,Tahoma,Arial" ForeColor="Black"
																BackColor="White"></HolidayStyle>
														</ew:CalendarPopup></STRONG></TD>
											</TR>
											<TR>
												<TD id="r2" runat="server">
													<DIV style="OVERFLOW: auto; WIDTH: 712px; HEIGHT: 126px" ms_positioning="FlowLayout">
														<asp:label id="Label1" runat="server">Label</asp:label>&nbsp;<STRONG>(</STRONG>
														<asp:Label id="lbltag" runat="server"></asp:Label><STRONG>) </STRONG>
														<asp:checkboxlist id="chkReportingList" runat="server"></asp:checkboxlist></DIV>
													<asp:Label id="lblsendmsg" runat="server"></asp:Label></TD>
											</TR>
											<TR>
												<TD>
													<asp:Button id="btnSend" runat="server" CssClass="button" Text="Send"></asp:Button></TD>
											</TR>
										</TABLE>
									</asp:panel><asp:panel id="Panel1" runat="server"></asp:panel></TD>
							</TR>
							<TR>
								<TD colSpan="3"></TD>
							</TR>
							<TR>
								<TD colSpan="3"></TD>
							</TR>
						</table>
						<BR>
						<TABLE id="Table1" cellSpacing="0" cellPadding="3" width="750" align="center" border="0">
							<TR>
								<TD></TD>
							</TR>
							<TR>
								<TD></TD>
							</TR>
						</TABLE>
						<BR>
						<TABLE id="Table4" cellSpacing="0" cellPadding="3" width="100%" border="0">
							<TR>
								<TD colSpan="3"><asp:textbox id="txtX" style="VISIBILITY: hidden" runat="server" Width="16px"></asp:textbox><asp:textbox id="txtY" style="VISIBILITY: hidden" runat="server" Width="21px"></asp:textbox></TD>
							</TR>
						</TABLE>
					</td>
				</tr>
				<TR>
					<TD vAlign="middle" align="center" height="20">Copyright © 2005 Independent Media 
						Corporation <A href="http://www.geo.tv">www.geo.tv</A></TD>
				</TR>
			</table>
		</form>
	</body>
</HTML>
