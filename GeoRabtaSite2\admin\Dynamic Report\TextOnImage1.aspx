<%@ Page language="c#" Codebehind="TextOnImage1.aspx.cs" AutoEventWireup="false" Inherits="GeoRabtaSite.TextOnImage1" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" >
<HTML>
	<HEAD>
		<title>My OrganoGram</title>
		<base target="_self">
		<meta content="Microsoft Visual Studio .NET 7.1" name="GENERATOR">
		<meta content="C#" name="CODE_LANGUAGE">
		<meta content="JavaScript" name="vs_defaultClientScript">
		<meta content="http://schemas.microsoft.com/intellisense/ie5" name="vs_targetSchema">
		<script language="javascript" id="clientEventHandlersJS">
		function imageInfo(info)
        {
         parent.frmDetail.location.href="orgdetail.aspx?id="+info;
         }
		
		
<!--

function document_onclick() {
	if (event.button==2)
		return false;
}

function document_oncontextmenu() {
	return false;
}

function document_ondragstart() {
	return false;
}

function document_onkeydown() {
	return false;
}

function document_onkeypress() {
	return false;
}

function document_onkeyup() {
	return false;
}

function document_onmouseup() {
	return false;
}

function document_onselectionchange() {
	return false;
}

function document_onselectstart() {
	return false;
}

//-->
		</script>
		<script language="javascript" event="onclick" for="document">
<!--

return document_onclick()
//-->
		</script>
		<script language="javascript" event="oncontextmenu" for="document">
<!--

return document_oncontextmenu()
//-->
		</script>
		<script language="javascript" event="ondragstart" for="document">
<!--

return document_ondragstart()
//-->
		</script>
		<script language="javascript" event="onkeydown" for="document">
<!--

return document_onkeydown()
//-->
		</script>
		<script language="javascript" event="onkeypress" for="document">
<!--

return document_onkeypress()
//-->
		</script>
		<script language="javascript" event="onkeyup" for="document">
<!--

return document_onkeyup()
//-->
		</script>
		<script language="javascript" event="onmouseup" for="document">
<!--

return document_onmouseup()
//-->
		</script>
		<script language="javascript" event="onselectionchange" for="document">
<!--

return document_onselectionchange()
//-->
		</script>
		<script language="javascript" event="onselectstart" for="document">
<!--

return document_onselectstart()
//-->


		</script>
	</HEAD>
	<body language="javascript" onunload="return window_onunload()" MS_POSITIONING="GridLayout">
		<form id="TextOnImage1" method="post" runat="server">
			<asp:LinkButton id="LkB1" style="Z-INDEX: 101; LEFT: 8px; POSITION: absolute; TOP: 0px" runat="server">Back to Start</asp:LinkButton>
		</form>
	</body>
</HTML>
