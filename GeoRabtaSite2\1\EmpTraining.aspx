<%@ Register TagPrefix="ew" Namespace="eWorld.UI" Assembly="eWorld.UI" %>
<%@ Register TagPrefix="uc1" TagName="Organimeter" Src="Organimeter.ascx" %>
<%@ Register TagPrefix="uc1" TagName="MenuControl" Src="MenuControl.ascx" %>
<%@ Page language="c#" Codebehind="EmpTraining.aspx.cs" AutoEventWireup="false" Inherits="GeoRabtaSite.EmpTraining" %>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
	<HEAD>
		<title>Geo Rabta :: User Profile</title>
		<meta content="text/html; charset=utf-8" http-equiv="Content-Type">
		<meta name="GENERATOR" content="Microsoft Visual Studio .NET 7.1">
		<meta name="vs_targetSchema" content="http://schemas.microsoft.com/intellisense/ie5">
		<LINK rel="stylesheet" type="text/css" href="StyleSheet1.css">
		<style type="text/css">.style2 { FONT-WEIGHT: bold; FONT-SIZE: 16pt; COLOR: #ffffff }
	A:link { COLOR: white }
		</style>
		<script src="jquery-1.2.6.js" type="text/javascript"></script>
		<script language="javascript" src="jquery.MetaData.js" type="text/javascript"></script>
		<script src="documentation.js" type="text/javascript"></script>
		<script language="javascript" src="jquery.MultiFile.js" type="text/javascript"></script>
		<script language="javascript" src="jquery.blockUI.js" type="text/javascript"></script>
		<script language="javascript">
String.prototype.trim = function() {
	return this.replace(/^\s+|\s+$/g,"");
}


function showMe (it, status) {
//alert(it);
//alert(status);
var vis = (status) ? "block" : "none";
document.getElementById(it).style.display = vis;
}

function SetOther(ddl,val,div)
{
	var ddl=document.getElementById(ddl);
	if(ddl)
	{
		if(ddl.selectedIndex>=0)
			showMe(div,ddl[ddl.selectedIndex].value==val)
	}

}

function SetOtherControl(ddl, val, oc)
{
	var ddl=document.getElementById(ddl);
	var oc=document.getElementById(oc);
	if(ddl && oc)
	{
	if(ddl[ddl.selectedIndex].value==val)
		{
		oc.disabled=true;
		oc.selectedIndex=0;
		}
	else
		oc.disabled=false;
	}
}

function ValidateTraining()
{
	return true;
}
	
function ShowOther(ddl,val,div,txtOther)
{
	var ddl=document.getElementById(ddl);
	showMe(div,ddl[ddl.selectedIndex].value==val)
	var txtO=document.getElementById(txtOther);
	if(ddl.selectedIndex>=0)
	{
	if(ddl[ddl.selectedIndex].value==val)
		{
		//txtO.value='';
		txtO.focus();
		}
	}

}
function PopUpSlip()
{
	var w = 600, h = 350;
	if (document.all) 
	{
		w = document.body.clientWidth;
		h = document.body.clientHeight;
	}
	else if (document.layers) 
	{
		w = window.innerWidth;
		h = window.innerHeight;
    }
	var popW = 800, popH = 375;
	var leftPos = (w-popW)/2, topPos = (h-popH)/2;
	window.open('Report.aspx','MyPaySlip','width=' + popW + ',height='+popH+',top='+topPos+ ',left='+leftPos)
	//window.open('Report.aspx','MyPaySlip','status:no,toolbar:no,scrollbars:no,width=800');
	return false; 
}
	
function OpenURL(file,docw,doch)
{
	var w=screen.width;
	var h=screen.height;
	var l=(w-docw)/2;
	var t=(h-doch)/2;
	var viewimageWin=window.open(file,"codewindow","toolbar=0,scrollbars=1,location=0,statusbar=0,menubar=0,resizable=0,width="+docw+",height="+doch+",left=" + l + ",top="+t);
	return false;
}
		</script>
		<script id="clientEventHandlersJS" language="javascript" type="text/javascript">
<!--

function window_onload() 
{
	//d=document.getElementById('info');
	//d.style.visibility='hidden';
	document.body.scrollTop =document.getElementById("txtX").value;
	document.body.scrollLeft=document.getElementById("txtY").value;
	SetOther('ddlTrainingField','-1','divOtherField');
	SetOther('ddlCity','-1','divOtherCity');
}
function displayDiv()
{
	d=document.getElementById('info');
	d.style.visibility='visible';
	d.style.left=window.event.x+document.body.scrollLeft+10;
	d.style.top=window.event.y+document.body.scrollTop+15;
	var val=document.getElementById('TextBox1');
	d.innerHTML=val.value;
}

function disableDiv()
{
	d=document.getElementById('info');
	d.style.visibility='hidden';
}

function window_onscroll() 
{
	document.getElementById("txtX").value=document.body.scrollTop;
	document.getElementById("txtY").value=document.body.scrollLeft;
}

function showMe (it, status) {
var vis = (status) ? "block" : "none";
document.getElementById(it).style.display = vis;
}
//-->
		</script>
	</HEAD>
	<body onscroll="return window_onscroll()" oncontextmenu="return false" language="javascript"
		onselectstart="return false" ondrag="return false" onload="return window_onload()"
		bottomMargin="0" background="images\bg.jpg" leftMargin="0" rightMargin="0" bgProperties="fixed"
		topMargin="0" dir="ltr">
		<script type="text/javascript" src="wz_tooltip.js"></script>
		<form id="myForm" runat="server">
			<table id="Table1" border="0" cellSpacing="0" cellPadding="0" width="1004" height="100%">
				<tr>
					<td style="HEIGHT: 200px">
						<OBJECT id="Shockwaveflash1" codeBase="http://fpdownload.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=7,0,0,0"
							height="200" width="1004" align="middle" classid="clsid:d27cdb6e-ae6d-11cf-96b8-444553540000"
							VIEWASTEXT>
							<PARAM NAME="_cx" VALUE="26564">
							<PARAM NAME="_cy" VALUE="5292">
							<PARAM NAME="FlashVars" VALUE="">
							<PARAM NAME="Movie" VALUE="top.swf">
							<PARAM NAME="Src" VALUE="top.swf">
							<PARAM NAME="WMode" VALUE="Transparent">
							<PARAM NAME="Play" VALUE="-1">
							<PARAM NAME="Loop" VALUE="-1">
							<PARAM NAME="Quality" VALUE="High">
							<PARAM NAME="SAlign" VALUE="">
							<PARAM NAME="Menu" VALUE="-1">
							<PARAM NAME="Base" VALUE="">
							<PARAM NAME="AllowScriptAccess" VALUE="sameDomain">
							<PARAM NAME="Scale" VALUE="ExactFit">
							<PARAM NAME="DeviceFont" VALUE="0">
							<PARAM NAME="EmbedMovie" VALUE="0">
							<PARAM NAME="BGColor" VALUE="0066FF">
							<PARAM NAME="SWRemote" VALUE="">
							<PARAM NAME="MovieData" VALUE="">
							<PARAM NAME="SeamlessTabbing" VALUE="1">
							<PARAM NAME="Profile" VALUE="0">
							<PARAM NAME="ProfileAddress" VALUE="">
							<PARAM NAME="ProfilePort" VALUE="0">
							<PARAM NAME="AllowNetworking" VALUE="all">
							<PARAM NAME="AllowFullScreen" VALUE="false">
							<embed src="top.swf" width="1004" height="200" align="middle" quality="high" wmode="transparent"
								bgcolor="#0066ff" scale="exactfit" allowscriptaccess="sameDomain" type="application/x-shockwave-flash"
								pluginspage="http://www.macromedia.com/go/getflashplayer" />
						</OBJECT>
					</td>
				</tr>
				<tr>
					<td vAlign="top" align="center">
						<table id="Table2" border="0" cellSpacing="0" cellPadding="0" width="100%">
							<tr>
								<td vAlign="top" align="center" width="176"><uc1:menucontrol id="MenuControl1" runat="server"></uc1:menucontrol></td>
								<td style="WIDTH: 650px" vAlign="top" align="left" bgColor="#ffffff">
									<table style="BACKGROUND-COLOR: white" border="0" cellSpacing="0" cellPadding="2" width="100%"
										align="center">
										<tr>
											<td bgColor="#004477" height="65" background="images/PanelTop.jpg">
												<table border="0" cellSpacing="0" cellPadding="4" width="650">
													<tr>
														<td style="WIDTH: 40px"></td>
														<td><span class="style2">User Profile</span>
														</td>
													</tr>
												</table>
											</td>
										</tr>
										<tr>
											<td>
												<asp:imagebutton id="ImageButton1" runat="server" ImageUrl="images\employee.gif" CausesValidation="False"></asp:imagebutton>
												<asp:imagebutton id="ImageButton2" runat="server" ImageUrl="images\personal.gif" CausesValidation="False"></asp:imagebutton>
												<asp:imagebutton id="ImageButton3" runat="server" ImageUrl="images\family.gif" CausesValidation="False"></asp:imagebutton>
												<asp:imagebutton id="ImageButton4" runat="server" ImageUrl="images\education.gif" CausesValidation="False"></asp:imagebutton>
												<asp:imagebutton id="imgTraining" runat="server" ImageUrl="images\training.gif"></asp:imagebutton>
												<asp:HyperLink id="hlMyExp" runat="server" BorderWidth="0px" NavigateUrl="EmpExperience.aspx" BorderStyle="None"
													ImageUrl="images/MyExperience.gif">HyperLink</asp:HyperLink>
												<asp:imagebutton id="ImageButton5" runat="server" ImageUrl="buttons/myorganogram.gif" CausesValidation="False"></asp:imagebutton>
												<asp:imagebutton id="ibSalary" runat="server" ImageUrl="images/payslip.gif" CausesValidation="False"></asp:imagebutton>
												<asp:imagebutton id="ibMySelf" runat="server" ImageUrl="Images\MeMyself.gif" CausesValidation="False"
													Visible="False"></asp:imagebutton>
												<asp:imagebutton id="imgMyLeave" runat="server" ImageUrl="images\MyLeaveBalance.gif" CausesValidation="False"></asp:imagebutton>
												<asp:imagebutton id="ImgMyAttendance" runat="server" ImageUrl="images\MyAttendance.gif" CausesValidation="False"></asp:imagebutton>
												<asp:imagebutton id="imgMyGrievance" runat="server" ImageUrl="images\mygrievance.gif"></asp:imagebutton>
												<asp:HyperLink id="hlMyRequests" runat="server" BorderWidth="0px" NavigateUrl="MyRequest.aspx"
													BorderStyle="None" ImageUrl="images/myrequests.gif">My Requests</asp:HyperLink>
												<asp:panel id="Panel1" runat="server" BackImageUrl="images\tabstrip.jpg" Height="10px" Width="100%"></asp:panel>
												<uc1:Organimeter id="Organimeter1" runat="server"></uc1:Organimeter>
												<asp:Label id="lblPCode" runat="server" Visible="False"></asp:Label>
												<asp:Label id="lblEmpInfo" runat="server" Visible="False"></asp:Label><br>
												<asp:panel id="pnlTraings" runat="server" Visible="False">
													<TABLE class="FormColor2" id="Table11" cellSpacing="0" cellPadding="3" width="100%" align="center"
														bgColor="#ffffff" border="0" runat="server">
														<TR>
															<TD class="PanelTitle" colSpan="3">Training
															</TD>
														</TR>
														<TR>
															<TD colSpan="3">
																<asp:DataGrid id="dgTraining2" runat="server" BorderWidth="1px" Width="100%" ForeColor="Black"
																	BackColor="LightGoldenrodYellow" CellPadding="2" BorderColor="Tan" AutoGenerateColumns="False">
																	<FooterStyle BackColor="Tan"></FooterStyle>
																	<SelectedItemStyle ForeColor="GhostWhite" BackColor="DarkSlateBlue"></SelectedItemStyle>
																	<AlternatingItemStyle BackColor="PaleGoldenrod"></AlternatingItemStyle>
																	<HeaderStyle Font-Bold="True" BackColor="Tan"></HeaderStyle>
																	<Columns>
																		<asp:BoundColumn Visible="False" DataField="trainingid"></asp:BoundColumn>
																		<asp:BoundColumn DataField="Training Title" HeaderText="Title"></asp:BoundColumn>
																		<asp:BoundColumn DataField="Training Field" HeaderText="Field"></asp:BoundColumn>
																		<asp:BoundColumn DataField="Training Duration" HeaderText="Duration"></asp:BoundColumn>
																		<asp:BoundColumn DataField="Training (Month-Year)" HeaderText="Completion&lt;br&gt;(Month-Year)"></asp:BoundColumn>
																		<asp:BoundColumn DataField="InstituteName" HeaderText="Institute Name"></asp:BoundColumn>
																		<asp:BoundColumn DataField="cname" HeaderText="Country"></asp:BoundColumn>
																		<asp:BoundColumn DataField="City" HeaderText="City"></asp:BoundColumn>
																		<asp:ButtonColumn Text="Select" CommandName="Select"></asp:ButtonColumn>
																		<asp:TemplateColumn>
																			<HeaderStyle Width="20px"></HeaderStyle>
																			<ItemTemplate>
																				<asp:LinkButton id="lbDeleteTraining" onclick="DeleteTraining" runat="server">
																					<img src="images/deletex.gif" border="0"></asp:LinkButton>
																			</ItemTemplate>
																		</asp:TemplateColumn>
																	</Columns>
																	<PagerStyle HorizontalAlign="Center" ForeColor="DarkSlateBlue" BackColor="PaleGoldenrod"></PagerStyle>
																</asp:DataGrid>
																<asp:Label id="lblTrainingMsg" runat="server" Visible="False" ForeColor="White" Font-Size="Small"
																	Font-Bold="True">Your Request has been successfully posted...</asp:Label></TD>
														</TR>
														<TR>
															<TD colSpan="3">
																<asp:LinkButton id="Linkbutton3" runat="server">Add New Training</asp:LinkButton></TD>
														</TR>
														<TR>
															<TD colSpan="3">
																<asp:DataGrid id="dgRegTraining" runat="server" BorderWidth="1px" Visible="False" Width="100%"
																	ForeColor="Black" BackColor="LightGoldenrodYellow" CellPadding="2" BorderColor="Tan" AutoGenerateColumns="False">
																	<FooterStyle BackColor="Tan"></FooterStyle>
																	<SelectedItemStyle ForeColor="GhostWhite" BackColor="DarkSlateBlue"></SelectedItemStyle>
																	<AlternatingItemStyle BackColor="PaleGoldenrod"></AlternatingItemStyle>
																	<HeaderStyle Font-Bold="True" BackColor="Tan"></HeaderStyle>
																	<Columns>
																		<asp:BoundColumn DataField="reqid" HeaderText="Req. No."></asp:BoundColumn>
																		<asp:BoundColumn Visible="False" DataField="trainingid"></asp:BoundColumn>
																		<asp:BoundColumn DataField="Training Title" HeaderText="Title"></asp:BoundColumn>
																		<asp:BoundColumn DataField="Training Field" HeaderText="Field"></asp:BoundColumn>
																		<asp:BoundColumn DataField="Training Duration" HeaderText="Duration"></asp:BoundColumn>
																		<asp:BoundColumn DataField="Training (Month-Year)" HeaderText="Completion&lt;br&gt;(Month-Year)"></asp:BoundColumn>
																		<asp:BoundColumn DataField="InstituteName" HeaderText="Institute Name"></asp:BoundColumn>
																		<asp:BoundColumn DataField="cname" HeaderText="Country"></asp:BoundColumn>
																		<asp:BoundColumn DataField="City" HeaderText="City"></asp:BoundColumn>
																		<asp:TemplateColumn>
																			<HeaderStyle Width="20px"></HeaderStyle>
																			<ItemTemplate>
																				<asp:LinkButton id="lbDelTrainingReq" onclick="DeleteTrainingReq" runat="server">
																					<img src="images/deletex.gif" border="0"></asp:LinkButton>
																			</ItemTemplate>
																		</asp:TemplateColumn>
																	</Columns>
																	<PagerStyle HorizontalAlign="Center" ForeColor="DarkSlateBlue" BackColor="PaleGoldenrod"></PagerStyle>
																</asp:DataGrid></TD>
														</TR>
													</TABLE>
												</asp:panel><asp:panel id="pnlTrainingDetails" runat="server">
													<TABLE class="tab" id="pnlTraining" cellSpacing="0" cellPadding="3" width="100%" align="center"
														bgColor="#f5fffa" border="0" runat="server">
														<TR>
															<TD class="PanelTitle" vAlign="top" width="340" colSpan="3">Training Detail
																<asp:Label id="lblEduToolTip" style="CURSOR: hand" runat="server" ForeColor="Yellow" Font-Size="Small"
																	Font-Bold="True" Font-Underline="True">?</asp:Label></TD>
														</TR>
														<TR>
															<TD class="FormColor1" vAlign="top" width="300"><STRONG>Title</STRONG>
																<BR>
																<asp:TextBox id="txtTrainingTitle" runat="server" Width="100%" CssClass="textbox"></asp:TextBox></TD>
															<TD class="FormColor1"></TD>
															<TD class="FormColor1" vAlign="top" width="300"><STRONG>Field </STRONG>
																<asp:DropDownList id="ddlTrainingField" runat="server" Width="100%" CssClass="textbox"></asp:DropDownList><BR>
																<DIV id="divOtherField" style="DISPLAY: none">
																	<asp:Label id="lblOtherTrainingField" runat="server" Font-Bold="True">Other Training Field</asp:Label>
																	<asp:TextBox id="txtOtherTrainingField" runat="server" Width="100%" CssClass="textbox"></asp:TextBox></DIV>
															</TD>
														</TR>
														<TR>
															<TD class="FormColor2" vAlign="top" width="300"><STRONG>Duration<BR>
																</STRONG>
																<asp:DropDownList id="ddlDuration" runat="server" CssClass="textbox"></asp:DropDownList>-
																<asp:DropDownList id="ddlDurationUnit" runat="server" CssClass="textbox">
																	<asp:ListItem></asp:ListItem>
																	<asp:ListItem Value="H">Hour</asp:ListItem>
																	<asp:ListItem Value="D">Day</asp:ListItem>
																	<asp:ListItem Value="M">Month</asp:ListItem>
																	<asp:ListItem Value="Y">Year</asp:ListItem>
																</asp:DropDownList></TD>
															<TD class="FormColor2"></TD>
															<TD class="FormColor2" vAlign="top" width="300"><STRONG>Completion&nbsp; </STRONG>(Month-Year)<BR>
																<asp:DropDownList id="ddlTrainingMonth" runat="server" CssClass="textbox">
																	<asp:ListItem></asp:ListItem>
																	<asp:ListItem Value="1">Jan</asp:ListItem>
																	<asp:ListItem Value="2">Feb</asp:ListItem>
																	<asp:ListItem Value="3">Mar</asp:ListItem>
																	<asp:ListItem Value="4">Apr</asp:ListItem>
																	<asp:ListItem Value="5">May</asp:ListItem>
																	<asp:ListItem Value="6">Jun</asp:ListItem>
																	<asp:ListItem Value="7">Jul</asp:ListItem>
																	<asp:ListItem Value="8">Aug</asp:ListItem>
																	<asp:ListItem Value="9">Sep</asp:ListItem>
																	<asp:ListItem Value="10">Oct</asp:ListItem>
																	<asp:ListItem Value="11">Nov</asp:ListItem>
																	<asp:ListItem Value="12">Dec</asp:ListItem>
																</asp:DropDownList>-
																<asp:DropDownList id="ddlTrainingYear" runat="server" CssClass="textbox"></asp:DropDownList></TD>
														</TR>
														<TR>
															<TD class="FormColor1" width="300">
																<asp:CheckBox id="CheckBox1" runat="server" Text="Company Sponsored Training"></asp:CheckBox></TD>
															<TD class="FormColor1"></TD>
															<TD class="FormColor1" width="300">
																<asp:Label id="lblTrainingID" runat="server" Visible="False"></asp:Label></TD>
														</TR>
														<TR>
															<TD class="FormColor1" vAlign="top" colSpan="3"><STRONG>Name of Institute</STRONG><BR>
																<asp:TextBox id="txtTrainingInstitute" runat="server" Width="100%" CssClass="textbox"></asp:TextBox></TD>
														</TR>
														<TR>
															<TD class="FormColor2" vAlign="top" width="300"><STRONG>Country:
																	<asp:DropDownList id="ddlCountry" runat="server" Width="100%" CssClass="textbox" AutoPostBack="True"></asp:DropDownList></STRONG></TD>
															<TD class="FormColor2" style="HEIGHT: 23px"></TD>
															<TD class="FormColor2" style="HEIGHT: 23px" width="300"><STRONG>City:</STRONG><BR>
																<asp:DropDownList id="ddlCity" runat="server" Width="100%" CssClass="textbox"></asp:DropDownList><SPAN id="divOtherCity" style="DISPLAY: none">
																	<asp:Label id="lblOtherCity" runat="server" Font-Bold="True">Other City</asp:Label>
																	<asp:TextBox id="txtOtherCity" runat="server" Width="100%" CssClass="textbox"></asp:TextBox></SPAN></TD>
														</TR>
														<TR>
															<TD class="FormColor1" style="HEIGHT: 23px" width="300" colSpan="3"><INPUT id="fileExp" style="BORDER-RIGHT: dimgray 1px solid; BORDER-TOP: dimgray 1px solid; FONT-SIZE: 12px; BORDER-LEFT: dimgray 1px solid; WIDTH: 384px; BORDER-BOTTOM: dimgray 1px solid; FONT-FAMILY: Verdana; HEIGHT: 22px; BACKGROUND-COLOR: aliceblue"
																	type="file" size="44" name="File1" Runat="server"></TD>
														</TR>
														<TR>
															<TD class="FormColor1" style="HEIGHT: 23px" width="300">
																<asp:Button id="cmdAddTrainingReq" runat="server" Width="88px" Text="Save"></asp:Button>&nbsp;
																<asp:Button id="btnCancelTrain" runat="server" Width="88px" Text="Cancel"></asp:Button></TD>
															<TD class="FormColor1" style="HEIGHT: 23px"></TD>
															<TD class="FormColor1" style="HEIGHT: 23px" width="300"></TD>
														</TR>
													</TABLE>
												</asp:panel><asp:textbox style="VISIBILITY: hidden" id="TextBox1" runat="server"></asp:textbox><asp:textbox style="VISIBILITY: hidden" id="txtX" runat="server" Width="24px"></asp:textbox><asp:textbox style="VISIBILITY: hidden" id="txtY" runat="server" Width="24px"></asp:textbox></td>
										</tr>
									</table>
								</td>
								<td vAlign="top" align="center" width="200">
								</td>
							</tr>
						</table>
					</td>
				</tr>
				<tr>
					<td class="Fotter" height="20" align="center">Copyright © 2005 Independent Media 
						Corporation www.geo.tv<br>
					</td>
				</tr>
			</table>
		</form>
	</body>
</HTML>
